CREATE OR REPLACE FUNCTION hash_index_qurey_test(iterations INT DEFAULT 5) 
RETURNS TABLE (
    operation TEXT,
    average_time_ms NUMERIC
) AS $$
DECLARE
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    total_time NUMERIC;

    iter_start_time TIMESTAMP;
    iter_end_time TIMESTAMP;
    iter_total_time numeric;
    
    -- 存储每次迭代的结果
    idx_query10_times NUMERIC[];
    idx_query3000_times NUMERIC[];
    cnt int;
    i INT;
BEGIN
    -- 确保测试表存在且没有索引
    RAISE INFO '准备测试环境...';
    
    
    -- 开始测试循环
    FOR i IN 1..iterations LOOP
        RAISE INFO '执行第 % 次迭代...', i;

        -- 开始循环计时
        iter_start_time := clock_timestamp();

        -- 1. 查询  int_value = 10（有索引均匀查询）
        start_time := clock_timestamp();
        select  count(*) into cnt FROM combined_table WHERE int_value = 10;
        end_time := clock_timestamp();
        idx_query10_times[i] := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;
        
        -- 2. 查询  int_value = 3000（有索引非均匀查询）
        start_time := clock_timestamp();
        select  count(*) into cnt FROM combined_table WHERE  int_value = 3000;
        end_time := clock_timestamp();
        idx_query3000_times[i] := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000;        

        -- 该次循环结束计时
        iter_end_time := clock_timestamp();
        iter_total_time := EXTRACT(EPOCH FROM (iter_end_time - iter_start_time));
        RAISE INFO '第 % 次迭代持续时间：% s', i, iter_total_time;
    END LOOP;
    
    -- 计算平均值并返回结果
	RETURN QUERY
	SELECT 
	    op.operation,
	    AVG(t.time) AS average_time_ms
	FROM (
	    VALUES
      (1, '均匀查询', idx_query10_times),
	    (2, '非均匀查询', idx_query3000_times)
	) AS op(order_key, operation, time_array)
	LEFT JOIN LATERAL unnest(op.time_array) AS t(time) ON true
	GROUP BY op.order_key, op.operation
	ORDER BY op.order_key;       
	    
END;
$$ LANGUAGE plpgsql;

-- 执行测试并查看结果，指定迭代次数为10
SELECT * FROM hash_index_qurey_test(1);    