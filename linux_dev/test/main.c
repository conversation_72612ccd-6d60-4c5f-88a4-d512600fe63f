/************************************************
 版    权: 
 文 件 名: main.c
 创建日期: 2025-08-27 16:28:57
 编 写 者: zql
 内容描述: 
 关系文件: 
 更改历史: 
************************************************/

#include <stdio.h>
#include "gncdb.h"
#include "gncdbconstant.h"
#include "hash.h"

#include "concurrence.h"
#include "singletest.h"
#include "testdependlib/sqlitelib/sqlite3.h"
#include "dbsqlitetest.h"
// #include <gperftools/profiler.h>
#include <stdio.h>
#include <string.h>
#include <string.h>
#include <time.h>
// #include <sqlite3.h>
#include <stdio.h>
#include <stdlib.h>


int main(int argc, char *argv[])
{
    int rc = 0;
    // rc     = correctnessTest();
    // Sql_DemoFun();

    rc = dbtest();

    // rc = concurrencetest();

    // muticoncurrence();

    // rc = perTestMain(argc, argv);
    // rc = sqlite_test();
    // rc = gncdb_prepare();
    // rc = gncdb_test();
    // rc = join_test();
    // sqlquerytest_main();

    return rc;
}
