#include "tpcc.h"
#include "gncdb.h"
#include "load.h"
#include "sql_event.h"
#include "start.h"
#include <stdio.h>
#include <time.h>
#include "transaction.h"
#include <unistd.h>
#include "tpc.h"
#include "trans_if.h"
#include <time.h>

unsigned long long GetOneTupleAvgCost = 0;
unsigned long long RowTupleConstructCost = 0;
unsigned long long ScanFilterCost = 0;
int count1 = 0;
int count2 = 0;
// static int num_ware = 4;             // 仓库数量
/*
 * 生成除 home_ware 外的有效仓库编号
 * （假设除当前仓库外还存在其他仓库）
 */
int other_ware(int home_ware) {
  int tmp;
  int num_ware = WAREHOUSE;
  if (num_ware == 1)
    return home_ware;
  // 如果仓库数量为1，则返回当前仓库编号
  while ((tmp = RandomNumber(1, num_ware)) == home_ware)
    ;
  // 随机选择一个不是当前仓库的仓库编号
  return tmp;
}
int ExecuteSQLUseFile1(GNCDB* db, char* fileName){
    FILE *file;
    char *line = NULL;
    size_t len = 0;
    ssize_t read;
    char *current_sql = NULL;
    size_t current_sql_len = 0;
    char *temp = NULL;

    file = fopen(fileName, "r");
    if (file == NULL) {
        perror("打开文件时出错");
        return 1;
    }

    while ((read = getline(&line, &len, file)) != -1) {
        // 忽略以"--"开头的行
        if (strncmp(line, "--", 2) == 0)
            continue;

        // 移除行尾的换行符
        if (line[read - 1] == '\n') {
            line[read - 1] = '\0';
            --read;
        }

        // 为 current_sql 分配或重新分配内存
        temp = my_realloc(current_sql, current_sql_len + read + 2);
        if (!temp) {
            perror("内存分配失败");
            my_free(line);
            my_free(current_sql);
            fclose(file);
            return GNCDB_MEM;
        }
        current_sql = temp;

        // 将行添加到 current_sql
        memmove(current_sql+current_sql_len, line, strlen(line));
        memmove(current_sql+current_sql_len+strlen(line), " ", 1);
        current_sql_len += read + 1;
        current_sql[current_sql_len] = '\0';
        // 检查行是否以分号结尾
        if (line[read - 1] == ';') {
            GNCDB_exec(db, current_sql,  NULL, NULL, NULL); // 执行 SQL 语句
            my_free(current_sql); // 释放当前 SQL 语句的内存
            current_sql = NULL;
            current_sql_len = 0;
        }
    }
    my_free(line);
    fclose(file);
    return GNCDB_SUCCESS;
}

int tpccFlag = 0;

void initTpccFlag()
{
	tpccFlag = 0;
}

int tpccCallBack(void *NotUsed,int columnNum, char** fieldName, char** fieldValue)
{
    // 暂时忽略data参数
   if (tpccFlag == 0)
	{
		//printf("查询出的记录：\n");
		printf("\n");
		printf("|");
		for (int i = 0; i < columnNum; i++)
		{
			printf("%16s|", fieldName[i]);
		}
		printf("\n");
		for (int i = 0; i <= columnNum * 17; ++i)
		{
			printf("-");
		}
		printf("\n");
	}

	printf("|");
	for (int i = 0; i < columnNum; i++)
	{
		printf("%16s|", fieldValue[i]);
	}
	printf("\n");

	tpccFlag = 1;
	return 0;
}

int tpccTest(){
    int rc = GNCDB_SUCCESS;
    GNCDB*db = NULL;

    remove("tpcc.db");
    remove("log_tpcc.db");

    // if(resFp != NULL){
	// 	fwrite("TPCC 测试\n", 1, strlen("TPCC 测试\n"), resFp);
	// }

    rc = GNCDB_open(&db, "tpcc.db", 0, 20000);
    if(rc != GNCDB_SUCCESS){
        return rc;
    }

    rc = executeSQLFile(db,"./test/tpc-c/tpcc.sql");
    if( rc != GNCDB_SUCCESS)
    {
        printf("executeSQLFile failed\n");
    }
    GNCDB_exec(db, "select * from master;", tpccCallBack, NULL, NULL);

    rc = load(db, NULL);
    if(rc != GNCDB_SUCCESS){
        printf("load error : rc %d \n", rc);
        return rc;
    }

    

    rc = GNCDB_close(&db);
    if(rc != GNCDB_SUCCESS){
        printf("GNCDB_close error : rc %d \n", rc);
        return rc;
    }
    rc = start(NULL);

    return rc;
}
