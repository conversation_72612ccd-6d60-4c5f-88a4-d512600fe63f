-- PRAGMA foreign_keys = ON;

drop table if exists warehouse;

create table warehouse (
w_id int PRIMARY KEY not null,
w_name char(10), 
w_street_1 char(20), 
w_street_2 char(20), 
w_city char(20), 
w_state char(2), 
w_zip char(9), 
w_tax float, 
w_ytd float);

drop table if exists district;

create table district (
d_id int PRIMARY KEY not null, 
d_w_id int PRIMARY KEY not null, 
d_name char(10), 
d_street_1 char(20), 
d_street_2 char(20), 
d_city char(20), 
d_state char(2), 
d_zip char(9), 
d_tax float, 
d_ytd float, 
d_next_o_id int);

drop table if exists customer;

create table customer (
c_id int PRIMARY KEY not null,
c_d_id int PRIMARY KEY not null,
c_w_id int PRIMARY KEY not null,
c_first char(16), 
c_middle char(2), 
c_last char(16), 
c_street_1 char(20), 
c_street_2 char(20), 
c_city char(20), 
c_state char(2), 
c_zip char(9), 
c_phone char(16), 
c_since char(81), 
c_credit char(2), 
c_credit_lim int, 
c_discount float, 
c_balance float, 
c_ytd_payment float, 
c_payment_cnt int, 
c_delivery_cnt int, 
c_data char(500));

drop table if exists history;

create table history (
h_c_id int, 
h_c_d_id int, 
h_c_w_id int,
h_d_id int,
h_w_id int,
h_date char(81),
h_amount float, 
h_data char(24));

drop table if exists orders;

create table orders (
o_id int PRIMARY KEY not null, 
o_d_id int PRIMARY KEY not null, 
o_w_id int PRIMARY KEY not null,
o_c_id int,
o_entry_d char(81),
o_carrier_id int,
o_ol_cnt int, 
o_all_local int);

drop table if exists new_orders;

create table new_orders (
no_o_id int PRIMARY KEY not null,
no_d_id int PRIMARY KEY not null,
no_w_id int PRIMARY KEY not null);

drop table if exists order_line;

create table order_line ( 
ol_o_id int PRIMARY KEY not null, 
ol_d_id int PRIMARY KEY not null,
ol_w_id int PRIMARY KEY not null,
ol_number int PRIMARY KEY not null,
ol_i_id int, 
ol_supply_w_id int,
ol_delivery_d char(81), 
ol_quantity int, 
ol_amount float, 
ol_dist_info char(24));

drop table if exists item;

create table item (
i_id int PRIMARY KEY not null, 
i_im_id int, 
i_name char(24), 
i_price float, 
i_data char(50));

drop table if exists stock;

create table stock (
s_i_id int PRIMARY KEY not null, 
s_w_id int PRIMARY KEY not null, 
s_quantity int, 
s_dist_01 char(24), 
s_dist_02 char(24),
s_dist_03 char(24),
s_dist_04 char(24), 
s_dist_05 char(24), 
s_dist_06 char(24), 
s_dist_07 char(24), 
s_dist_08 char(24), 
s_dist_09 char(24), 
s_dist_10 char(24), 
s_ytd float, 
s_order_cnt int, 
s_remote_cnt int,
s_data char(50));

-- CREATE INDEX idx_customer ON customer (c_w_id, c_d_id, c_last, c_first);
-- CREATE INDEX idx_orders ON orders (o_w_id, o_d_id, o_c_id, o_id);

-- PRAGMA foreign_keys = ON;
