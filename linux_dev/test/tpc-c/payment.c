
#include <string.h>
#include <stdio.h>
#include <time.h>

// #include "common.h"
#include "tpc.h"
#include "sql_event.h"
#include "spt_proc.h"
#include "transaction.h"

extern GNCDB **ctx;
extern char ***stmt;

#define NNULL ((void *)0)

/*
 * the payment transaction
 */

#ifdef PRINT_TRXS_COST_TIME
int payment( int t_num,
	     int w_id_arg,		/* 仓库 ID */
	     int d_id_arg,		/* 区域 ID */
	     int byname,		/* 按 c_id 还是 c_last 进行选择？ */
	     int c_w_id_arg,
	     int c_d_id_arg,
	     int c_id_arg,		/* 客户 ID */
	     char c_last_arg[],	         /* 客户姓氏 */
	     float h_amount_arg,	         /* 付款金额 */
		 SQLStageEvent *sql_event
)
{
	
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            c_id = c_id_arg;
	char            w_name[11];
	char            w_street_1[21];
	char            w_street_2[21];
	char            w_city[21];
	char            w_state[3];
	char            w_zip[10];
	int            c_d_id = c_d_id_arg;
	int            c_w_id = c_w_id_arg;
	char            c_first[17];
	char            c_middle[3];
	char            c_last[17];
	char            c_street_1[21];
	char            c_street_2[21];
	char            c_city[21];
	char            c_state[3];
	char            c_zip[10];
	char            c_phone[17];
	char            c_since[20];
	char            c_credit[4];
	int            c_credit_lim;
	float           c_discount;
	float           c_balance;
	char            c_data[502];
	char            c_new_data[502];
	float           h_amount = h_amount_arg;
	char            h_data[26];
	char            d_name[11];
	char            d_street_1[21];
	char            d_street_2[21];
	char            d_city[21];
	char            d_state[3];
	char            d_zip[10];
	int            namecnt;
	char            datetime[81];

	int             n;
	int             proceed = 0;
	int bytes;
	
	FILE* fp2;
	fp2 = fopen("avgPayment", "a");

	struct timespec start, end; 
	unsigned long long timeCost = 0;
	char sql_stmt[1000];
	memset(sql_stmt, 0, sizeof(sql_stmt));
	int num_cols;
	int rc = GNCDB_SUCCESS;
	/* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr; */
	/* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */

	// 获取时间戳
	gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);

	SQLStageEventReset(sql_event);
	proceed = 1;
	// 更新仓库的年度销售额
	/*EXEC_SQL UPDATE warehouse SET w_ytd = w_ytd + :h_amount
	  WHERE w_id =:w_id;*/
	
	
	clock_gettime(CLOCK_MONOTONIC, &start);
	sprintf(sql_stmt, "UPDATE warehouse SET w_ytd = w_ytd + %f WHERE w_id = %d;", h_amount, w_id);
	rc = txnExecuteSQL(sql_event, sql_stmt);
	clock_gettime(CLOCK_MONOTONIC, &end);
	timeCost = (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
	fprintf(fp2, "%llu,", timeCost/1000);
	if(rc != GNCDB_SUCCESS){
		printf("#payment1 Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	
	proceed = 2;
	// 查询仓库信息
	/*EXEC_SQL SELECT w_street_1, w_street_2, w_city, w_state, w_zip,
	                w_name
	                INTO :w_street_1, :w_street_2, :w_city, :w_state,
				:w_zip, :w_name
	                FROM warehouse
	                WHERE w_id = :w_id;*/
	clock_gettime(CLOCK_MONOTONIC, &start);
	sprintf(sql_stmt, "SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = %d;", w_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	clock_gettime(CLOCK_MONOTONIC, &end);
	timeCost = (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
	fprintf(fp2, "%llu,", timeCost/1000);
	if(rc != GNCDB_SUCCESS){
		if(sql_event->res->field_names == NULL){
			printf("#payment2 payment:Error in executing sql\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->field_count;
		if(num_cols != 6){
			printf("#payment2 payment:Error in executing sql\n");
			goto sqlerr;
		}
		strcpy(w_street_1, sql_event->res->fieldValues[0]);
		strcpy(w_street_2, sql_event->res->fieldValues[1]);
		strcpy(w_city, sql_event->res->fieldValues[2]);
		strcpy(w_state, sql_event->res->fieldValues[3]);
		strcpy(w_zip, sql_event->res->fieldValues[4]);
		strcpy(w_name, sql_event->res->fieldValues[5]);
	}
	SQLStageEventReset(sql_event);
	
	proceed = 3;
	// 更新区域的年度销售额
	/*EXEC_SQL UPDATE district SET d_ytd = d_ytd + :h_amount
			WHERE d_w_id = :w_id 
			AND d_id = :d_id;*/

	memset(sql_stmt, 0, sizeof(sql_stmt));
	clock_gettime(CLOCK_MONOTONIC, &start);
	sprintf(sql_stmt, "UPDATE district SET d_ytd = d_ytd + %f WHERE d_w_id = %d AND d_id = %d;", h_amount, w_id, d_id);
	rc = txnExecuteSQL(sql_event, sql_stmt);
	clock_gettime(CLOCK_MONOTONIC, &end);
	timeCost = (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
	fprintf(fp2, "%llu,", timeCost/1000);
	if(rc != GNCDB_SUCCESS){
		printf("#payment3 Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	proceed = 4;
	// 查询区域信息
	/*EXEC_SQL SELECT d_street_1, d_street_2, d_city, d_state, d_zip,
	                d_name
	                INTO :d_street_1, :d_street_2, :d_city, :d_state,
				:d_zip, :d_name
	                FROM district
	                WHERE d_w_id = :w_id 
			AND d_id = :d_id;*/

	clock_gettime(CLOCK_MONOTONIC, &start);
	sprintf(sql_stmt, "SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = %d AND d_id = %d;", w_id, d_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	clock_gettime(CLOCK_MONOTONIC, &end);
	timeCost = (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
	fprintf(fp2, "%llu,", timeCost/1000);
	if(rc != GNCDB_SUCCESS){
		if(sql_event->res->field_names == NULL){
			printf("#payment3 payment:Error in executing sql\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->field_count;
		if(num_cols != 6){
			printf("#payment3 payment:Error in executing sql\n");
			goto sqlerr;
		}
		strcpy(d_street_1, sql_event->res->fieldValues[0]);
		strcpy(d_street_2, sql_event->res->fieldValues[1]);
		strcpy(d_city, sql_event->res->fieldValues[2]);
		strcpy(d_state, sql_event->res->fieldValues[3]);
		strcpy(d_zip, sql_event->res->fieldValues[4]);
		strcpy(d_name, sql_event->res->fieldValues[5]);
	}

	SQLStageEventReset(sql_event);

	unsigned long long byNameTime1 = 0;
	unsigned long long byNameTime2 = 0;
	struct timespec byNameStart1, byNameEnd1;
	struct timespec byNameStart2, byNameEnd2;
	int cnt = 0;
	if (byname) {
		strcpy(c_last, c_last_arg);

		proceed = 5;
		// 查询姓氏相同的客户数量
		/*EXEC_SQL SELECT count(c_id) 
			INTO :namecnt
		        FROM customer
			WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
		        AND c_last = :c_last;*/
		memset(sql_stmt, 0, sizeof(sql_stmt));
		
		// sprintf(sql_stmt, "SELECT c_id,c_w_id,c_d_id,c_last  FROM customer WHERE c_w_id = %d ;", c_w_id);
		// rc = txnExecuteSQL(sql_event, sql_stmt);
		// SQLStageEventReset(sql_event);
		clock_gettime(CLOCK_MONOTONIC, &byNameStart1);
		sprintf(sql_stmt, "SELECT count(c_id) FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s';", c_w_id, c_d_id, c_last);
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		clock_gettime(CLOCK_MONOTONIC, &byNameEnd1);
		byNameTime1 += (byNameEnd1.tv_sec - byNameStart1.tv_sec) * 1000000000 + (byNameEnd1.tv_nsec - byNameStart1.tv_nsec);
		if(rc != GNCDB_SUCCESS){
			if(sql_event->res->field_names == NULL){
				printf("#payment4 payment:Error in executing sql\n");
				
				goto sqlerr;
			}
			num_cols = sql_event->res->field_count;
			if(num_cols != 1){
				printf("#payment4 payment:Error in executing sql\n");
				goto sqlerr;
			}
			namecnt = atoi(sql_event->res->fieldValues[0]);
		}
		else{
			namecnt = 0;
		}
		SQLStageEventReset(sql_event);

		/*EXEC_SQL DECLARE c_byname_p CURSOR FOR
		        SELECT c_id
		        FROM customer
		        WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_last = :c_last
			ORDER BY c_first;

			EXEC_SQL OPEN c_byname_p;*/
		memset(sql_stmt, 0, sizeof(sql_stmt));
		sprintf(sql_stmt, "SELECT c_id FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s' ORDER BY c_first;", c_w_id, c_d_id, c_last);
		if (namecnt % 2)
			namecnt++;
		cnt = namecnt / 2;
		for (n = 0; n < namecnt / 2; n++) {
			clock_gettime(CLOCK_MONOTONIC, &byNameStart2);
			rc = txnExecuteSQLStep(sql_event, sql_stmt);
			clock_gettime(CLOCK_MONOTONIC, &byNameEnd2);
			byNameTime2 += (byNameEnd2.tv_sec - byNameStart2.tv_sec) * 1000000000 + (byNameEnd2.tv_nsec - byNameStart2.tv_nsec);
			if(rc != GNCDB_SUCCESS){
				if(sql_event->res->field_names == NULL){
					printf("#payment5 payment:Error in executing sql\n");
					goto sqlerr;
				}
				num_cols = sql_event->res->field_count;
				if(num_cols != 1){
					printf("#payment5 payment:Error in executing sql\n");
					goto sqlerr;
				}
				c_id = atoi(sql_event->res->fieldValues[0]);
			}
		}
		SQLStageEventReset(sql_event);

		// sqlite3_reset(sqlite_stmt);

	}
	else{
		byNameTime1 = 0;
		byNameTime2 = 0;
	}
	fprintf(fp2, "%llu,", byNameTime1);
	fprintf(fp2, "%llu,", cnt == 0 ? 0 : byNameTime2 / cnt);

	proceed = 6;
	// 查询客户信息
	/*EXEC_SQL SELECT c_first, c_middle, c_last, c_street_1,
		        c_street_2, c_city, c_state, c_zip, c_phone,
		        c_credit, c_credit_lim, c_discount, c_balance,
		        c_since
		INTO :c_first, :c_middle, :c_last, :c_street_1,
		     :c_street_2, :c_city, :c_state, :c_zip, :c_phone,
		     :c_credit, :c_credit_lim, :c_discount, :c_balance,
		     :c_since
		FROM customer
	        WHERE c_w_id = :c_w_id 
	        AND c_d_id = :c_d_id 
		AND c_id = :c_id
		FOR UPDATE;*/
	memset(sql_stmt, 0, sizeof(sql_stmt));
	clock_gettime(CLOCK_MONOTONIC, &start);
	sprintf(sql_stmt, "SELECT c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	clock_gettime(CLOCK_MONOTONIC, &end);
	timeCost = (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
	fprintf(fp2, "%llu,", timeCost/1000);
	if(rc != GNCDB_SUCCESS){
		if(sql_event->res->field_names == NULL){
			printf("#payment6 payment:Error in executing sql\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->field_count;
		if(num_cols != 14){
			printf("#payment6 payment:Error in executing sql\n");
			goto sqlerr;
		}
		strcpy(c_first, sql_event->res->fieldValues[0]);
		strcpy(c_middle, sql_event->res->fieldValues[1]);
		strcpy(c_last, sql_event->res->fieldValues[2]);
		strcpy(c_street_1, sql_event->res->fieldValues[3]);
		strcpy(c_street_2, sql_event->res->fieldValues[4]);
		strcpy(c_city, sql_event->res->fieldValues[5]);
		strcpy(c_state, sql_event->res->fieldValues[6]);
		strcpy(c_zip, sql_event->res->fieldValues[7]);
		strcpy(c_phone, sql_event->res->fieldValues[8]);
		strcpy(c_credit, sql_event->res->fieldValues[9]);
		c_credit_lim = atoi(sql_event->res->fieldValues[10]);
		c_discount = atof(sql_event->res->fieldValues[11]);
		c_balance = atof(sql_event->res->fieldValues[12]);
		strcpy(c_since, sql_event->res->fieldValues[13]);
	}
	SQLStageEventReset(sql_event);

	// 更新客户信息
	c_balance = c_balance - h_amount;
	c_credit[2] = '\0';
	clock_gettime(CLOCK_MONOTONIC, &start);
	if (strstr(c_credit, "BC")) {
		proceed = 7;
		// 查询客户信息
		/*EXEC_SQL SELECT c_data 
			INTO :c_data
		        FROM customer
		        WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id; */

		memset(sql_stmt, 0, sizeof(sql_stmt));
		sprintf(sql_stmt, "SELECT c_data FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		if(rc != GNCDB_SUCCESS){
			if(sql_event->res->field_names == NULL){
				printf("#payment7 payment:Error in executing sql\n");
				goto sqlerr;
			}
			num_cols = sql_event->res->field_count;
			if(num_cols != 1){
				printf("#payment7 payment:Error in executing sql\n");
				goto sqlerr;
			}
			strcpy(c_data, sql_event->res->fieldValues[0]);
		}
		SQLStageEventReset(sql_event);

		sprintf(c_new_data, 
			"| %4d %2d %4d %2d %4d $%7.2f %12c %24c",
			c_id, c_d_id, c_w_id, d_id,
			w_id, h_amount,
			datetime, c_data);

		strncat(c_new_data, c_data, 
			500 - strlen(c_new_data));

		c_new_data[500] = '\0';

		proceed = 8;
		/*EXEC_SQL UPDATE customer
			SET c_balance = :c_balance, c_data = :c_new_data
			WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id;*/
		memset(sql_stmt, 0, sizeof(sql_stmt));
		sprintf(sql_stmt, "UPDATE customer SET c_balance = %f, c_data = '%s' WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_balance, c_data, c_w_id, c_d_id, c_id);
		rc = txnExecuteSQL(sql_event, sql_stmt);
		if(rc != GNCDB_SUCCESS){
			printf("#payment8 Error in executing sql\n");
			goto sqlerr;
		}
		SQLStageEventReset(sql_event);

	}else{
		proceed = 9;
		// 更新客户余额
		/*EXEC_SQL UPDATE customer 
			SET c_balance = :c_balance
			WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id;*/

		sprintf(sql_stmt, "UPDATE customer SET c_balance = %f WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_balance, c_w_id, c_d_id, c_id);
		rc = txnExecuteSQL(sql_event, sql_stmt);
		if(rc != GNCDB_SUCCESS){
			printf("#payment9 Error in executing sql\n");
			goto sqlerr;
		}
		SQLStageEventReset(sql_event);
	}

	clock_gettime(CLOCK_MONOTONIC, &end);
	timeCost = (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
	fprintf(fp2, "%llu,", timeCost/1000);


	strncpy(h_data, w_name, 10);
	h_data[10] = '\0';
	strncat(h_data, d_name, 10);
	h_data[20] = ' ';
	h_data[21] = ' ';
	h_data[22] = ' ';
	h_data[23] = ' ';
	h_data[24] = '\0';

	proceed = 10;
	/*EXEC_SQL INSERT INTO history(h_c_d_id, h_c_w_id, h_c_id, h_d_id,
			                   h_w_id, h_date, h_amount, h_data)
	                VALUES(:c_d_id, :c_w_id, :c_id, :d_id,
		               :w_id, 
			       :datetime,
			       :h_amount, :h_data);*/
	memset(sql_stmt, 0, sizeof(sql_stmt));
	clock_gettime(CLOCK_MONOTONIC, &start);
	sprintf(sql_stmt, "INSERT INTO history(h_c_id, h_c_d_id, h_c_w_id, h_d_id, h_w_id, h_date, h_amount, h_data) VALUES(%d, %d, %d, %d, %d, '%s', %f, '%s');", c_id, c_d_id, c_w_id, d_id, w_id, datetime, h_amount, h_data);

	rc = txnExecuteSQL(sql_event, sql_stmt);
	clock_gettime(CLOCK_MONOTONIC, &end);
	timeCost = (end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec);
	fprintf(fp2, "%llu\n", timeCost/1000);
	if(rc != GNCDB_SUCCESS){
		printf("#payment10 Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	fclose(fp2);
	return (1);

sqlerr:
	fprintf(stderr,"payment %d:%d\n",t_num,proceed);
	printf("%s: error: rc = %d\n", __func__, rc);
	exit(-1);
      	//error(ctx[t_num],mysql_stmt);
	/*EXEC SQL WHENEVER SQLERROR GOTO sqlerrerr;*/
	/*EXEC_SQL ROLLBACK WORK;*/
	transactionRollback(sql_event->txn, sql_event->db);
	
}



#else

int payment( int t_num,
	     int w_id_arg,		/* 仓库 ID */
	     int d_id_arg,		/* 区域 ID */
	     int byname,		/* 按 c_id 还是 c_last 进行选择？ */
	     int c_w_id_arg,
	     int c_d_id_arg,
	     int c_id_arg,		/* 客户 ID */
	     char c_last_arg[],	         /* 客户姓氏 */
	     float h_amount_arg,	         /* 付款金额 */
		 SQLStageEvent *sql_event
)
{
	
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            c_id = c_id_arg;
	char            w_name[11];
	char            w_street_1[21];
	char            w_street_2[21];
	char            w_city[21];
	char            w_state[3];
	char            w_zip[10];
	int            c_d_id = c_d_id_arg;
	int            c_w_id = c_w_id_arg;
	char            c_first[17];
	char            c_middle[3];
	char            c_last[17];
	char            c_street_1[21];
	char            c_street_2[21];
	char            c_city[21];
	char            c_state[3];
	char            c_zip[10];
	char            c_phone[17];
	char            c_since[20];
	char            c_credit[4];
	int            c_credit_lim;
	float           c_discount;
	float           c_balance = 0.0;
	char            c_data[502];
	char            c_new_data[1024];
	float           h_amount = h_amount_arg;
	char            h_data[26];
	char            d_name[11];
	char            d_street_1[21];
	char            d_street_2[21];
	char            d_city[21];
	char            d_state[3];
	char            d_zip[10];
	int            namecnt;
	char            datetime[81];

	int             n;
	int             proceed = 0;
	// int bytes;
	
	int num_cols;
	int rc = GNCDB_SUCCESS;
	char sql_stmt[1000];
	memset(sql_stmt, 0, sizeof(sql_stmt));
	/* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr; */
	/* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */

	// 获取时间戳
	gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);

	SQLStageEventReset(sql_event);
	proceed = 1;
	// 更新仓库的年度销售额
	/*EXEC_SQL UPDATE warehouse SET w_ytd = w_ytd + :h_amount
	  WHERE w_id =:w_id;*/
	
	
	sprintf(sql_stmt, "UPDATE warehouse SET w_ytd = w_ytd + %f WHERE w_id = %d;", h_amount, w_id);
	rc = txnExecuteSQL(sql_event, sql_stmt);
	if(rc != GNCDB_SUCCESS){
		printf("#payment1 Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	
	proceed = 2;
	// 查询仓库信息
	/*EXEC_SQL SELECT w_street_1, w_street_2, w_city, w_state, w_zip,
	                w_name
	                INTO :w_street_1, :w_street_2, :w_city, :w_state,
				:w_zip, :w_name
	                FROM warehouse
	                WHERE w_id = :w_id;*/
	sprintf(sql_stmt, "SELECT w_street_1, w_street_2, w_city, w_state, w_zip, w_name FROM warehouse WHERE w_id = %d;", w_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	if(rc == GNCDB_SUCCESS){
		if(sql_event->res->fieldNames == NULL){
			printf("#payment2 payment:Error in executing sql\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->fieldCount;
		if(num_cols != 6){
			printf("#payment2 payment:Error in executing sql\n");
			goto sqlerr;
		}
		strcpy(w_street_1, sql_event->res->fieldValues[0]);
		strcpy(w_street_2, sql_event->res->fieldValues[1]);
		strcpy(w_city, sql_event->res->fieldValues[2]);
		strcpy(w_state, sql_event->res->fieldValues[3]);
		strcpy(w_zip, sql_event->res->fieldValues[4]);
		strcpy(w_name, sql_event->res->fieldValues[5]);
	}
	else{
		printf("#payment2 payment:Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);
	
	proceed = 3;
	// 更新区域的年度销售额
	/*EXEC_SQL UPDATE district SET d_ytd = d_ytd + :h_amount
			WHERE d_w_id = :w_id 
			AND d_id = :d_id;*/

	memset(sql_stmt, 0, sizeof(sql_stmt));
	sprintf(sql_stmt, "UPDATE district SET d_ytd = d_ytd + %f WHERE d_w_id = %d AND d_id = %d;", h_amount, w_id, d_id);
	rc = txnExecuteSQL(sql_event, sql_stmt);
	if(rc != GNCDB_SUCCESS){
		printf("#payment3 Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	proceed = 4;
	// 查询区域信息
	/*EXEC_SQL SELECT d_street_1, d_street_2, d_city, d_state, d_zip,
	                d_name
	                INTO :d_street_1, :d_street_2, :d_city, :d_state,
				:d_zip, :d_name
	                FROM district
	                WHERE d_w_id = :w_id 
			AND d_id = :d_id;*/

	sprintf(sql_stmt, "SELECT d_street_1, d_street_2, d_city, d_state, d_zip, d_name FROM district WHERE d_w_id = %d AND d_id = %d;", w_id, d_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	if(rc == GNCDB_SUCCESS){
		if(sql_event->res->fieldNames == NULL){
			printf("#payment3 payment:Error in executing sql\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->fieldCount;
		if(num_cols != 6){
			printf("#payment3 payment:Error in executing sql\n");
			goto sqlerr;
		}
		strcpy(d_street_1, sql_event->res->fieldValues[0]);
		strcpy(d_street_2, sql_event->res->fieldValues[1]);
		strcpy(d_city, sql_event->res->fieldValues[2]);
		strcpy(d_state, sql_event->res->fieldValues[3]);
		strcpy(d_zip, sql_event->res->fieldValues[4]);
		strcpy(d_name, sql_event->res->fieldValues[5]);
	}
	else{
		printf("#payment3 payment:Error in executing sql\n");
		goto sqlerr;
	}

	SQLStageEventReset(sql_event);

	if (byname) {
		strcpy(c_last, c_last_arg);

		proceed = 5;
		// 查询姓氏相同的客户数量
		/*EXEC_SQL SELECT count(c_id) 
			INTO :namecnt
		        FROM customer
			WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
		        AND c_last = :c_last;*/
		memset(sql_stmt, 0, sizeof(sql_stmt));
		
		// sprintf(sql_stmt, "SELECT c_id,c_w_id,c_d_id,c_last  FROM customer WHERE c_w_id = %d ;", c_w_id);
		// rc = txnExecuteSQL(sql_event, sql_stmt);
		// SQLStageEventReset(sql_event);
		sprintf(sql_stmt, "SELECT count(c_id) FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s';", c_w_id, c_d_id, c_last);
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		if(rc == GNCDB_SUCCESS){
			if(sql_event->res->fieldNames == NULL){
				printf("#payment4 payment:Error in executing sql\n");
				
				goto sqlerr;
			}
			num_cols = sql_event->res->fieldCount;
			if(num_cols != 1){
				printf("#payment4 payment:Error in executing sql\n");
				goto sqlerr;
			}
			namecnt = atoi(sql_event->res->fieldValues[0]);
		}
		else{
			namecnt = 0;
			printf("#payment4 payment:Error in executing sql\n");
			goto sqlerr;
		}
		SQLStageEventReset(sql_event);

		/*EXEC_SQL DECLARE c_byname_p CURSOR FOR
		        SELECT c_id
		        FROM customer
		        WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_last = :c_last
			ORDER BY c_first;

			EXEC_SQL OPEN c_byname_p;*/
		memset(sql_stmt, 0, sizeof(sql_stmt));
		sprintf(sql_stmt, "SELECT c_id FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s' ORDER BY c_first;", c_w_id, c_d_id, c_last);
		if (namecnt % 2)
			namecnt++;
		for (n = 0; n < namecnt / 2; n++) {
			rc = txnExecuteSQLStep(sql_event, sql_stmt);
			if(rc == GNCDB_SUCCESS){
				if(sql_event->res->fieldNames == NULL){
					printf("#payment5 payment:Error in executing sql\n");
					goto sqlerr;
				}
				num_cols = sql_event->res->fieldCount;
				if(num_cols != 1){
					printf("#payment5 payment:Error in executing sql\n");
					goto sqlerr;
				}
				c_id = atoi(sql_event->res->fieldValues[0]);
			}
			else{
				printf("#payment5 payment:Error in executing sql\n");
				goto sqlerr;
			}
		}
		SQLStageEventReset(sql_event);

		// sqlite3_reset(sqlite_stmt);

	}


	proceed = 6;
	// 查询客户信息
	/*EXEC_SQL SELECT c_first, c_middle, c_last, c_street_1,
		        c_street_2, c_city, c_state, c_zip, c_phone,
		        c_credit, c_credit_lim, c_discount, c_balance,
		        c_since
		INTO :c_first, :c_middle, :c_last, :c_street_1,
		     :c_street_2, :c_city, :c_state, :c_zip, :c_phone,
		     :c_credit, :c_credit_lim, :c_discount, :c_balance,
		     :c_since
		FROM customer
	        WHERE c_w_id = :c_w_id 
	        AND c_d_id = :c_d_id 
		AND c_id = :c_id
		FOR UPDATE;*/
	memset(sql_stmt, 0, sizeof(sql_stmt));
	sprintf(sql_stmt, "SELECT c_first, c_middle, c_last, c_street_1, c_street_2, c_city, c_state, c_zip, c_phone, c_credit, c_credit_lim, c_discount, c_balance, c_since FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);
	
	{    
        // char con[128] = {0};
        // sprintf(con, "select * from customer where c_id = %d;", c_id);
        // printf("%s\n", sql_stmt);
        // initTpccFlag();
        // GNCDB_exec(sql_event->db, con, tpccCallBack, NULL, NULL);
		
    }
	
	
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	if(rc == GNCDB_SUCCESS){
		if(sql_event->res->fieldNames == NULL){
			printf("#payment6 payment:Error in executing sql\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->fieldCount;
		if(num_cols != 14){
			printf("#payment6 payment:Error in executing sql\n");
			goto sqlerr;
		}
		strcpy(c_first, sql_event->res->fieldValues[0]);
		strcpy(c_middle, sql_event->res->fieldValues[1]);
		strcpy(c_last, sql_event->res->fieldValues[2]);
		strcpy(c_street_1, sql_event->res->fieldValues[3]);
		strcpy(c_street_2, sql_event->res->fieldValues[4]);
		strcpy(c_city, sql_event->res->fieldValues[5]);
		strcpy(c_state, sql_event->res->fieldValues[6]);
		strcpy(c_zip, sql_event->res->fieldValues[7]);
		strcpy(c_phone, sql_event->res->fieldValues[8]);
		strcpy(c_credit, sql_event->res->fieldValues[9]);
		c_credit_lim = atoi(sql_event->res->fieldValues[10]);
		c_discount = atof(sql_event->res->fieldValues[11]);
		c_balance = atof(sql_event->res->fieldValues[12]);

		(void)c_credit_lim;
		(void)c_discount;
		// (void)c_balance;

		strcpy(c_since, sql_event->res->fieldValues[13]);
	}
	else{
		printf("#payment6 payment:Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	// 更新客户信息
	c_balance = c_balance - h_amount;
	c_credit[2] = '\0';
	if (strstr(c_credit, "BC")) {
		proceed = 7;
		// 查询客户信息
		/*EXEC_SQL SELECT c_data 
			INTO :c_data
		        FROM customer
		        WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id; */

		memset(sql_stmt, 0, sizeof(sql_stmt));
		sprintf(sql_stmt, "SELECT c_data FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		if(rc == GNCDB_SUCCESS){
			if(sql_event->res->fieldNames == NULL){
				printf("#payment7 payment:Error in executing sql\n");
				goto sqlerr;
			}
			num_cols = sql_event->res->fieldCount;
			if(num_cols != 1){
				printf("#payment7 payment:Error in executing sql\n");
				goto sqlerr;
			}
			strcpy(c_data, sql_event->res->fieldValues[0]);
		}
		else{
			printf("#payment7 payment:Error in executing sql\n");
			goto sqlerr;
		}
		SQLStageEventReset(sql_event);

		sprintf(c_new_data, 
			"| %4d %2d %4d %2d %4d $%7.2f %12s %s",
			c_id, c_d_id, c_w_id, d_id,
			w_id, h_amount,
			datetime, c_data);

		strncat(c_new_data, c_data, 
			1024 - strlen(c_new_data));

		c_new_data[1023] = '\0';

		proceed = 8;
		/*EXEC_SQL UPDATE customer
			SET c_balance = :c_balance, c_data = :c_new_data
			WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id;*/
		memset(sql_stmt, 0, sizeof(sql_stmt));
		sprintf(sql_stmt, "UPDATE customer SET c_balance = %f, c_data = '%s' WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_balance, c_data, c_w_id, c_d_id, c_id);
		rc = txnExecuteSQL(sql_event, sql_stmt);
		if(rc != GNCDB_SUCCESS){
			printf("#payment8 Error in executing sql\n");
			goto sqlerr;
		}
		SQLStageEventReset(sql_event);

	}else{
		proceed = 9;
		// 更新客户余额
		/*EXEC_SQL UPDATE customer 
			SET c_balance = :c_balance
			WHERE c_w_id = :c_w_id 
			AND c_d_id = :c_d_id 
			AND c_id = :c_id;*/

		sprintf(sql_stmt, "UPDATE customer SET c_balance = %f WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_balance, c_w_id, c_d_id, c_id);
		rc = txnExecuteSQL(sql_event, sql_stmt);
		if(rc != GNCDB_SUCCESS){
			printf("#payment9 Error in executing sql\n");
			goto sqlerr;
		}
		SQLStageEventReset(sql_event);
	}



	strncpy(h_data, w_name, 10);
	h_data[10] = '\0';
	strncat(h_data, d_name, 10);
	h_data[20] = ' ';
	h_data[21] = ' ';
	h_data[22] = ' ';
	h_data[23] = ' ';
	h_data[24] = '\0';

	proceed = 10;
	/*EXEC_SQL INSERT INTO history(h_c_d_id, h_c_w_id, h_c_id, h_d_id,
			                   h_w_id, h_date, h_amount, h_data)
	                VALUES(:c_d_id, :c_w_id, :c_id, :d_id,
		               :w_id, 
			       :datetime,
			       :h_amount, :h_data);*/
	memset(sql_stmt, 0, sizeof(sql_stmt));
	sprintf(sql_stmt, "INSERT INTO history(h_c_id, h_c_d_id, h_c_w_id, h_d_id, h_w_id, h_date, h_amount, h_data) VALUES(%d, %d, %d, %d, %d, '%s', %f, '%s');", c_id, c_d_id, c_w_id, d_id, w_id, datetime, h_amount, h_data);

	rc = txnExecuteSQL(sql_event, sql_stmt);
	if(rc != GNCDB_SUCCESS){
		printf("#payment10 Error in executing sql\n");
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	return (1);

sqlerr:
	fprintf(stderr,"payment %d:%d\n",t_num,proceed);
	printf("%s: error: rc = %d\n", __func__, rc);
      	//error(ctx[t_num],mysql_stmt);
	/*EXEC SQL WHENEVER SQLERROR GOTO sqlerrerr;*/
	/*EXEC_SQL ROLLBACK WORK;*/
	SQLStageEventReset(sql_event);
	transactionRollback(sql_event->txn, sql_event->db);
	sql_event->txn = transcationConstrcut(sql_event->db);
	return 0;
	
}


#endif