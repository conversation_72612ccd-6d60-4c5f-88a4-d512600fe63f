/*
 * delivery.pc
 * 对应附录 A 中的 A.4
 */

#include <stdio.h>
#include <string.h>
#include <time.h>

// #include <sqlite3.h>

// #include "common.h"
#include "spt_proc.h"
#include "tpc.h"
#include "sql_event.h"
#include "transaction.h"

extern GNCDB **ctx;  // 外部全局数据库连接指针数组
extern char ***stmt; // 外部全局 SQL 语句数组

#define NNULL ((void *)0) // 空指针定义

// delivery 函数定义


#ifdef PRINT_TRXS_COST_TIME
int delivery(int t_num,
             int w_id_arg,
             int o_carrier_id_arg,
             SQLStageEvent *sql_event)
{
    int rc = GNCDB_SUCCESS; // 返回码初始化
    int w_id = w_id_arg;    // 仓库 ID
    int o_carrier_id = o_carrier_id_arg; // 承运人 ID
    int d_id;   // 分区 ID
    int c_id;   // 客户 ID
    int no_o_id;    // 新订单 ID
    float ol_total; // 订单总额
    char datetime[81]; // 日期时间字符串

    int proceed = 0; // 进行到的步骤

    char sql_stmt[520]; // SQL 语句字符串
    int num_cols; // 结果列数

    FILE* fp4;
    fp4 = fopen("avgDelivery", "a");

    struct timespec start, end;
    // 获取当前时间戳
    gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);

    // 对每个仓库中的分区进行操作
    for (d_id = 1; d_id <= DIST_PER_WARE; d_id++)
    {
        proceed = 1; // 设置进度标志

        // 查询该分区中最小的新订单号
        clock_gettime(CLOCK_MONOTONIC, &start);
        sprintf(sql_stmt, "SELECT MIN(no_o_id) FROM new_orders WHERE no_d_id = %d AND no_w_id = %d;", d_id, w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        clock_gettime(CLOCK_MONOTONIC, &end);
        fprintf(fp4, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + end.tv_nsec - start.tv_nsec)/1000);
        if (rc != GNCDB_SUCCESS)
        {
            if (sql_event->sql_result->field_names == NULL)
            {
                goto sqlerr;
            }
            num_cols = sql_event->sql_result->field_count;
            if (num_cols != 1)
            {
                goto sqlerr;
            }
            no_o_id = sql_event->sql_result->field_values[0] == NULL ? 0 : atoi(sql_event->sql_result->field_values[0]);
            if(no_o_id < 0){
                printf("no_o_id < 0\n");
            }
        }

        SQLStageEventReset(sql_event);

        // 如果不存在新订单，则跳过
        if (no_o_id == 0)
            continue;
        proceed = 2; // 更新进度标志

        // 删除对应的新订单
        clock_gettime(CLOCK_MONOTONIC, &start);
        sprintf(sql_stmt, "DELETE FROM new_orders WHERE no_o_id = %d AND no_d_id = %d AND no_w_id = %d;", no_o_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        clock_gettime(CLOCK_MONOTONIC, &end);
        fprintf(fp4, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + end.tv_nsec - start.tv_nsec)/1000);
        if (rc != GNCDB_SUCCESS)
        {
            goto sqlerr;
        }

        SQLStageEventReset(sql_event);

        proceed = 3;

        // 获取订单对应的客户 ID
        clock_gettime(CLOCK_MONOTONIC, &start);
        sprintf(sql_stmt, "SELECT o_c_id FROM orders WHERE o_id = %d AND o_d_id = %d AND o_w_id = %d;", no_o_id, d_id, w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        clock_gettime(CLOCK_MONOTONIC, &end);
        fprintf(fp4, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + end.tv_nsec - start.tv_nsec)/1000);
        if (rc != GNCDB_SUCCESS)
        {
            if (sql_event->sql_result->field_names == NULL)
            {
                goto sqlerr;
            }
            num_cols = sql_event->sql_result->field_count;
            if (num_cols != 1)
            {
                goto sqlerr;
            }
            c_id = atoi(sql_event->sql_result->field_values[0]);
        }

        SQLStageEventReset(sql_event);

        proceed = 4;

        // 更新订单中的承运人 ID
        clock_gettime(CLOCK_MONOTONIC, &start);
        sprintf(sql_stmt, "UPDATE orders SET o_carrier_id = %d WHERE o_id = %d AND o_d_id = %d AND o_w_id = %d;", o_carrier_id, no_o_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        clock_gettime(CLOCK_MONOTONIC, &end);
        fprintf(fp4, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + end.tv_nsec - start.tv_nsec)/1000);
        if (rc != GNCDB_SUCCESS)
        {   
            // sprintf(sql_stmt, "select * from orders WHERE o_id = %d AND o_d_id = %d AND o_w_id = %d;", no_o_id, d_id, w_id);
            // SQLStageEventReset(sql_event);
            // rc = txnExecuteSQL(sql_event, sql_stmt);
            goto sqlerr;
        }

        SQLStageEventReset(sql_event);

        proceed = 5;

        // 更新订单行的交付日期
        clock_gettime(CLOCK_MONOTONIC, &start);
        sprintf(sql_stmt, "UPDATE order_line SET ol_delivery_d = '%s' WHERE ol_o_id = %d AND ol_d_id = %d AND ol_w_id = %d;", datetime, no_o_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        clock_gettime(CLOCK_MONOTONIC, &end);
        fprintf(fp4, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + end.tv_nsec - start.tv_nsec)/1000);
        if (rc != GNCDB_SUCCESS)
        {
            goto sqlerr;
        }

        SQLStageEventReset(sql_event);

        proceed = 6;

        // 查询订单行总额
        clock_gettime(CLOCK_MONOTONIC, &start);
        sprintf(sql_stmt, "SELECT SUM(ol_amount) FROM order_line WHERE ol_o_id = %d AND ol_d_id = %d AND ol_w_id = %d;", no_o_id, d_id, w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        clock_gettime(CLOCK_MONOTONIC, &end);
        fprintf(fp4, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + end.tv_nsec - start.tv_nsec)/1000);
        if (rc != GNCDB_SUCCESS)
        {
            if (sql_event->sql_result->field_names == NULL)
            {
                goto sqlerr;
            }
            num_cols = sql_event->sql_result->field_count;
            if (num_cols != 1)
            {
                goto sqlerr;
            }
            ol_total = atof(sql_event->sql_result->field_values[0]);
        }
        else{
            SQLStageEventReset(sql_event);
            rc = txnExecuteSQLStep(sql_event, sql_stmt);
        }

        SQLStageEventReset(sql_event);

        proceed = 7;

        // 更新客户余额和交付计数
        clock_gettime(CLOCK_MONOTONIC, &start);
        sprintf(sql_stmt, "UPDATE customer SET c_balance = c_balance + %f, c_delivery_cnt = c_delivery_cnt + 1 WHERE c_id = %d AND c_d_id = %d AND c_w_id = %d;", ol_total, c_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        clock_gettime(CLOCK_MONOTONIC, &end);
        fprintf(fp4, "%lld\n", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + end.tv_nsec - start.tv_nsec)/1000);
        if (rc != GNCDB_SUCCESS)
        {
            goto sqlerr;
        }

        SQLStageEventReset(sql_event);
    }


    fclose(fp4);
    return 1; // 成功返回

sqlerr:
    fprintf(stderr, "delivery %d:%d\n", t_num, proceed); // 输出错误信息
    printf("%s: error: rc = %d\n", __func__, rc); // 输出错误信息
    transactionRollback(sql_event->txn, sql_event->db); // 事务回滚
sqlerrerr:
    return (0); // 失败返回
}


#else

/**
 * @brief 处理订单交付事务。
 * 
 * @param t_num 事务编号
 * @param w_id_arg 仓库ID
 * @param o_carrier_id_arg 承运人ID
 * @param sql_event SQL事件结构体
 * @return 成功返回1，失败返回0
 */
int delivery(int t_num,
             int w_id_arg,
             int o_carrier_id_arg,
             SQLStageEvent *sql_event)
{
    int rc = GNCDB_SUCCESS; // 初始化返回码为成功
    int w_id = w_id_arg;    // 将传入的仓库ID赋值给局部变量
    int o_carrier_id = o_carrier_id_arg; // 将传入的承运人ID赋值给局部变量
    int d_id;   // 分区ID
    int c_id;   // 客户ID
    int no_o_id;    // 新订单ID
    float ol_total; // 订单行总金额
    char datetime[81]; // 存储当前时间戳的字符串

    int proceed = 0; // 标记事务处理的步骤

    char sql_stmt[520]; // SQL语句的缓冲区
    int num_cols; // 结果集中的列数

    // 获取当前时间戳
    gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);

    // 遍历仓库中的每个分区
    for (d_id = 1; d_id <= DIST_PER_WARE; d_id++)
    {
        proceed = 1; // 设置当前步骤为1

        // 查询该分区中最小的新订单号
        sprintf(sql_stmt, "SELECT MIN(no_o_id) FROM new_orders WHERE no_d_id = %d AND no_w_id = %d;", d_id, w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if (rc == GNCDB_SUCCESS)
        {
            if (sql_event->res->fieldNames == NULL)
            {
                goto sqlerr; // 如果字段名为空，跳转到错误处理
            }
            num_cols = sql_event->res->fieldCount;
            if (num_cols != 1)
            {
                goto sqlerr; // 如果列数不为1，跳转到错误处理
            }
            no_o_id = sql_event->res->fieldValues[0] == NULL ? 0 : atoi(sql_event->res->fieldValues[0]);
            if(no_o_id < 0){
                printf("no_o_id < 0\n"); // 如果新订单ID小于0，输出错误信息
            }
        }

        SQLStageEventReset(sql_event); // 重置SQL事件结构体

        // 如果不存在新订单，则跳过当前分区
        if (no_o_id == 0)
            continue;
        proceed = 2; // 设置当前步骤为2

        // 删除对应的新订单
        sprintf(sql_stmt, "DELETE FROM new_orders WHERE no_o_id = %d AND no_d_id = %d AND no_w_id = %d;", no_o_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if (rc != GNCDB_SUCCESS)
        {
            goto sqlerr; // 如果执行失败，跳转到错误处理
        }

        SQLStageEventReset(sql_event); // 重置SQL事件结构体

        proceed = 3; // 设置当前步骤为3

        // 获取订单对应的客户ID
        sprintf(sql_stmt, "SELECT o_c_id FROM orders WHERE o_id = %d AND o_d_id = %d AND o_w_id = %d;", no_o_id, d_id, w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if (rc == GNCDB_SUCCESS)
        {
            if (sql_event->res->fieldNames == NULL)
            {
                goto sqlerr; // 如果字段名为空，跳转到错误处理
            }
            num_cols = sql_event->res->fieldCount;
            if (num_cols != 1)
            {
                goto sqlerr; // 如果列数不为1，跳转到错误处理
            }
            c_id = atoi(sql_event->res->fieldValues[0]);
        }

        SQLStageEventReset(sql_event); // 重置SQL事件结构体

        proceed = 4; // 设置当前步骤为4

        // 更新订单中的承运人ID
        sprintf(sql_stmt, "UPDATE orders SET o_carrier_id = %d WHERE o_id = %d AND o_d_id = %d AND o_w_id = %d;", o_carrier_id, no_o_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if (rc != GNCDB_SUCCESS)
        {   
            goto sqlerr; // 如果执行失败，跳转到错误处理
        }

        SQLStageEventReset(sql_event); // 重置SQL事件结构体

        proceed = 5; // 设置当前步骤为5

        // 更新订单行的交付日期
        sprintf(sql_stmt, "UPDATE order_line SET ol_delivery_d = '%s' WHERE ol_o_id = %d AND ol_d_id = %d AND ol_w_id = %d;", datetime, no_o_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if (rc != GNCDB_SUCCESS)
        {
            goto sqlerr; // 如果执行失败，跳转到错误处理
        }

        SQLStageEventReset(sql_event); // 重置SQL事件结构体

        proceed = 6; // 设置当前步骤为6

        // 查询订单行总额
        sprintf(sql_stmt, "SELECT SUM(ol_amount) FROM order_line WHERE ol_o_id = %d AND ol_d_id = %d AND ol_w_id = %d;", no_o_id, d_id, w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if (rc == GNCDB_SUCCESS)
        {
            if (sql_event->res->fieldNames == NULL)
            {
                goto sqlerr; // 如果字段名为空，跳转到错误处理
            }
            num_cols = sql_event->res->fieldCount;
            if (num_cols != 1)
            {
                goto sqlerr; // 如果列数不为1，跳转到错误处理
            }
            ol_total = atof(sql_event->res->fieldValues[0]);
        }
        else{
            SQLStageEventReset(sql_event);
            rc = txnExecuteSQLStep(sql_event, sql_stmt); // 重新执行SQL语句
        }

        SQLStageEventReset(sql_event); // 重置SQL事件结构体

        proceed = 7; // 设置当前步骤为7

        // 更新客户余额和交付计数
        sprintf(sql_stmt, "UPDATE customer SET c_balance = c_balance + %f, c_delivery_cnt = c_delivery_cnt + 1 WHERE c_id = %d AND c_d_id = %d AND c_w_id = %d;", ol_total, c_id, d_id, w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if (rc != GNCDB_SUCCESS)
        {
            goto sqlerr; // 如果执行失败，跳转到错误处理
        }

        SQLStageEventReset(sql_event); // 重置SQL事件结构体
    }

    return 1; // 事务处理成功，返回1

sqlerr:
    fprintf(stderr, "delivery %d:%d\n", t_num, proceed); // 输出错误信息
    printf("%s: error: rc = %d\n", __func__, rc); // 输出错误码
    SQLStageEventReset(sql_event); // 重置SQL事件结构体
    transactionRollback(sql_event->txn, sql_event->db); // 回滚事务

    sql_event->txn = transcationConstrcut(sql_event->db); // 重新构造事务上下文
// sqlerrerr:
    return (0); // 事务处理失败，返回0
}


#endif