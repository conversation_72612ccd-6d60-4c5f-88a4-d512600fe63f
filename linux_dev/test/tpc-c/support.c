/*
 * support.c
 * routines needed for the tpcc loading and transaction programs
 */

#include <stdlib.h>  // 包含标准库头文件，用于随机数生成和内存分配
#include <stdio.h>   // 包含标准输入输出头文件，用于打印错误信息
#include <string.h>  // 包含字符串操作头文件，用于字符串复制和连接
#include <time.h>    // 包含时间处理头文件，用于获取当前时间
#include <ctype.h>   // 包含字符类型检查头文件

#include "tpc.h"     // 包含TPC-C相关的头文件

static int nums[CUST_PER_DIST];  // 定义一个静态数组，用于存储客户ID的排列
static int perm_count;           // 定义一个静态变量，用于跟踪排列的当前位置

// 设置随机数种子
void SetSeed (int seed)
{
	srand(seed);  // 使用给定的种子初始化随机数生成器
}

/*
 * return number uniformly distributed b/w min and max, inclusive
 */
// 定义一个函数 RandomNumber，用于生成一个在指定范围内的随机数
int RandomNumber (int min, int max)
{
    // 使用 rand() 函数生成一个0到RAND_MAX之间的随机数
    // RAND_MAX 是一个常量，表示 rand() 函数能生成的最大值
    // (rand() % ((max - min) + 1)) 计算出一个在0到(max - min)范围内的随机数
    // 加上 min 后，结果即在 min 到 max 范围内
	return min + (rand() % ((max - min) + 1));
}

/*
 * non uniform random -- see p. 15
 *
 * the constant C depends on which value of A is passed, but the same
 * value of C should be used for all calls with the same value of
 * A.  however, we know in advance which values of A will be used.
 */
int NURand (unsigned A, unsigned x, unsigned y)
{
    static int first = 1;  // 静态变量，用于标记是否是第一次调用函数
    static unsigned C_255, C_1023, C_8191;  // 设置静态变量来存储生成的随机常数
    unsigned C;

    if (first) {
        // 如果是第一次调用函数，生成三个随机常数并存储在静态变量中
        C_255 = RandomNumber(0, 255);
        C_1023 = RandomNumber(0, 1023);
        C_8191 = RandomNumber(0, 8191);
        first = 0;  // 确保常数只生成一次
    }

    // 根据A的值，选择对应的C常量
    switch (A) {
    case 255: C = C_255; break;
    case 1023: C = C_1023; break;
    case 8191: C = C_8191; break;
    default:
        // 如果A的值不是预期的255、1023或8191，输出错误信息并终止程序
        fprintf(stderr,
                "NURand: unexpected value (%d) of A used\n",
                A);
        abort();  // 错误处理：终止程序
    }

    // 返回NURand的结果
    // 生成两个随机数，进行按位或操作，加上C常量，然后取模(y - x + 1)，再加上x
    return (int) (((RandomNumber(0, A) | RandomNumber(x, y)) + C) % (y - x + 1)) + x;
}

/*
 * p. 54
 *
 * make a ``random a-string'': a string of random alphanumeric
 * characters of a random length of minimum x, maximum y, and
 * mean (y+x)/2
 */
int MakeAlphaString (int x, int y, char str[]) // 函数声明，MakeAlphaString用于生成一个随机长度的字母数字字符串
{
	static char *alphanum = "0123456789" // 定义一个静态字符串，包含数字和大小写字母
			        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
			        "abcdefghijklmnopqrstuvwxyz";
	int arrmax = 61;  /* index of last array element */
	register int i, len;

	len = RandomNumber(x, y); // 调用RandomNumber函数生成一个在x和y之间的随机数，作为字符串的长度

	for (i = 0; i < len; i++) // 循环生成随机字符，直到达到指定长度
		str[i] = alphanum[RandomNumber(0, arrmax)]; // 调用RandomNumber函数生成一个在0和arrmax之间的随机数，作为alphanum字符串的索引，并将对应的字符赋值给str数组

	return len; // 返回生成的字符串长度
}

/*
 * like MakeAlphaString, only numeric characters only
 */
int MakeNumberString (int x, int y, char str[])
{
    // 定义一个静态字符串，包含数字字符'0'到'9'
	static char *numeric = "0123456789";
    // 定义一个整数变量arrmax，表示数字字符数组的最大索引值
	int arrmax = 9;
    // 定义两个寄存器变量i和len，用于循环计数和存储字符串长度
	register int i, len;

    // 调用RandomNumber函数生成一个范围在x到y之间的随机数，作为字符串的长度
	len = RandomNumber(x, y);

    // 使用for循环生成随机数字字符串
	for (i = 0; i < len; i++)
        // 在每次循环中，调用RandomNumber函数生成一个范围在0到arrmax之间的随机数
        // 作为索引值，从numeric字符串中选择一个字符，并将其赋值给str数组的当前索引位置
		str[i] = numeric[RandomNumber(0, arrmax)];

    // 返回生成的字符串长度
	return len;
}

/*
 * turn system time into database format
 * the format argument should be a strftime() format string that produces
 *   a datetime string acceptable to the database
 */
// 函数声明：gettimestamp
// 参数：
//   str[]: 用于存储时间戳字符串的字符数组
//   format: 时间格式字符串，用于指定时间戳的格式
//   len: 字符数组str的长度，确保不会发生缓冲区溢出
void gettimestamp (char str[], char *format, size_t len)
{
	// 声明变量t，用于存储当前时间的时间戳
	time_t t;
	// 声明变量datetime，用于存储转换后的时间结构体
	struct tm *datetime;

	// 获取当前时间的时间戳
	t = time(NULL);
	// 将时间戳转换为本地时间，并存储在datetime中
	datetime = localtime(&t);

	// 使用strftime函数将时间结构体转换为指定格式的字符串
	// 如果转换失败（返回值为0），则输出错误信息并终止程序
	if ( !strftime(str, len, format, datetime) ) {
		// 输出错误信息到标准错误输出
		fprintf(stderr, "error writing timestamp to string\n");
		// 终止程序
		abort();
	}
}

/*
 * permute the list of customer ids for the order table
 */
void InitPermutation (void)
{
	int *cur;  // 定义一个指向整数的指针cur，用于遍历数组
	int i,j;   // 定义两个整型变量i和j，用于循环计数和随机交换

	perm_count = 0;  // 初始化perm_count为0，可能用于记录某种计数

	/* initialize with consecutive values [1..ORD_PER_DIST] */
	for (i = 0, cur = nums; i < ORD_PER_DIST; i++, cur++) {
		*cur = i + 1;
	}

	/* now, shuffle */
	for (i = 0; i < ORD_PER_DIST-1; i++) {
		j = (int)RandomNumber(i+1, ORD_PER_DIST-1);
		swap_int(nums[i], nums[j]);
	}
}

int GetPermutation (void) // 定义一个函数GetPermutation，用于获取排列中的下一个元素
{
    // 检查当前排列计数是否已经达到或超过每个排列的最大数量
	if ( perm_count >= ORD_PER_DIST ) {
        // 如果是，则向标准错误输出打印错误信息
		fprintf(stderr, "GetPermutation: past end of list!\n");
        // 终止程序执行
		abort();
	}
    // 返回当前排列计数对应的数组元素，并将排列计数加1
	return nums[perm_count++];
}

/*==================================================================+
 | ROUTINE NAME
 |      Lastname
 | DESCRIPTION
 |      TPC-C Lastname Function.
 | ARGUMENTS 
 |      num  - non-uniform random number
 |      name - last name string
 +==================================================================*/
void Lastname(num, name)
  int num;           // 整数参数num，用于确定姓氏的后缀
  char *name;        // 字符串指针name，用于存储生成的姓氏
{
  static char *n[] =  // 定义一个静态字符指针数组n，用于存储姓氏的后缀
    {"BAR", "OUGHT", "ABLE", "PRI", "PRES", 
     "ESE", "ANTI", "CALLY", "ATION", "EING"};
 
  strcpy(name,n[num/100]); // 将num除以100的商对应的后缀复制到name中
  strcat(name,n[(num/10)%10]); // 将num除以10的商再取模10的结果对应的后缀追加到name中
  strcat(name,n[num%10]); // 将num取模10的结果对应的后缀追加到name中
 
 return; // 函数结束，返回void类型，不需要返回值
}

