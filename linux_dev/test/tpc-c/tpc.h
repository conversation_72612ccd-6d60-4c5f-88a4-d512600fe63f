#ifndef TPC_H
#define TPC_H
#include "sql_event.h"
#include "typedefine.h"
#include <stddef.h>
#include "gncdb.h"
/*
 * tpc.h
 * definitions for tpcc loading program && transactions
 */

#ifdef __cplusplus
extern "C" {
#endif

#define SAMLL_VALUES 1

#if SAMLL_VALUES

    /* 
    * small values
    */
    #define WAREHOUSE 	4  // 仓库数
    #define MAXITEMS 	10000    // 商品数
    #define CUST_PER_DIST 	300  // 每个district的顾客数
    #define DIST_PER_WARE	3   // 每个仓库的district数
    #define ORD_PER_DIST	300  // 每个district的订单数

#else
    /*
    * correct values
    */
    #define WAREHOUSE 	4  // 仓库数
    #define MAXITEMS      100000 
    #define CUST_PER_DIST 3000 
    #define DIST_PER_WARE 10 
    #define ORD_PER_DIST  3000
    /*
    */
#endif


/* definitions for new order transaction */
#define MAX_NUM_ITEMS 15
#define MAX_ITEM_LEN  24

#define swap_int(a,b) {int tmp; tmp=a; a=b; b=tmp;}

/*
 * hack MakeAddress() into a macro so that we can pass Oracle
 * VARCHARs instead of char *s
 */
#define MakeAddressMacro(str1,str2,city,state,zip) \
{int tmp; \
 tmp = MakeAlphaString(10,20,str1.arr); \
 str1.len = tmp; \
 tmp = MakeAlphaString(10,20,str2.arr); \
 str2.len = tmp; \
 tmp = MakeAlphaString(10,20,city.arr); \
 city.len = tmp; \
 tmp = MakeAlphaString(2,2,state.arr); \
 state.len = tmp; \
 tmp = MakeNumberString(9,9,zip.arr); \
 zip.len = tmp;}

/*
 * while we're at it, wrap MakeAlphaString() and MakeNumberString()
 * in a similar way
 */
#define MakeAlphaStringMacro(x,y,str) \
{int tmp; tmp = MakeAlphaString(x,y,str.arr); str.len = tmp;}
#define MakeNumberStringMacro(x,y,str) \
{int tmp; tmp = MakeNumberString(x,y,str.arr); str.len = tmp;}

/*
 * likewise, for Lastname()
 * counts on Lastname() producing null-terminated strings
 */
#define LastnameMacro(num,str) \
{Lastname(num, str.arr); str.len = strlen(str.arr);}

extern long count_ware;
  
/* Functions */
    
int          LoadItems(GNCDB*db ,CallBack2 callback);
int          LoadWare(GNCDB*db ,CallBack2 callback);   
int          LoadCust(GNCDB*db ,CallBack2 callback);
int          LoadOrd(GNCDB*db ,CallBack2 callback);
void         LoadNewOrd();   
int          Stock(int w_id, SQLStageEvent* sql_event);
int          District(SQLStageEvent* sql_event, int w_id);
int          Customer(SQLStageEvent* sql_event, int d_id, int w_id);
int          Orders(SQLStageEvent*sql_event, int d_id, int w_id);
void         New_Orders();
void         MakeAddress(char* str1, char* str2, char* city, char* state, char* zip);
void         Error(SQLStageEvent* sql_event, char* sql_stmt);

#ifdef __STDC__
void SetSeed (int seed);
int RandomNumber (int min, int max);
int NURand (unsigned A, unsigned x, unsigned y);
int MakeAlphaString (int x, int y, char str[]);
int MakeNumberString (int x, int y, char str[]);
void gettimestamp (char str[], char *format, size_t n);
void InitPermutation (void);
int GetPermutation (void);
void Lastname(int num, char* name);


void initTpccFlag();
int tpccCallBack(void *NotUsed,int columnNum, char** fieldName, char** fieldValue);

#endif /* __STDC__ */
    
#ifdef __cplusplus
}
#endif

#endif /* TPC_H */
