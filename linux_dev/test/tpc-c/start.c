/*
 * main.pc
 * 用于驱动 tpcc 事务的主程序
 */

#include <fcntl.h>
#include <pthread.h>
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>
#include <time.h>
#include <unistd.h>


#include "rthist.h"
#include "sb_percentile.h"
#include "sequence.h"
#include "spt_proc.h"
#include "start.h"
#include "sql_event.h"
#include "tpc.h"
#include "trans_if.h"
#include "transaction.h"
#include "timers.h"
#include "typedefine.h"
#include <bits/types/timer_t.h>

#include "tpch.h"
// #include "xlsxwriter.h"

CallBack2 global_callback;

atomic_uint_least64_t Instrustats[INSTRUMENT_NUM];

/* 全局 SQL 变量 */
GNCDB **ctx;
char ***stmt;


const char *Instruprint[INSTRUMENT_NUM] =
{
	"open",
	"close",
	"pread",
	"pwrite",
	"read",
	"write",
	"seek",
	"fsync",
	"unlink",
	"bg_thread",
	"memcpy_to_pmem",
	"fsync_noop",
	"neword",
	"payment",
	"ordstat",
	"delivery",
	"slev",
};



#define DB_STRING_MAX 128
#define MAX_CLUSTER_SIZE 128

int num_ware;     // 仓库数量
int num_conn;     // 连接数
int lampup_time;  // 上升时间
int measure_time; // 测量时间

int num_node; /* 组成集群的服务器数量 i.e. RAC（0:正常模式）*/
#define NUM_NODE_MAX 8
char node_string[NUM_NODE_MAX][DB_STRING_MAX];

int time_count;
int PRINT_INTERVAL = 10;
int multi_schema = 0;
int multi_schema_offset = 0;

int success[5]; // 成功次数
int late[5];    // 迟到次数
int retry[5];   // 重试次数
int failure[5]; // 失败次数

int *success2[5]; // 成功次数的数组
int *late2[5];    // 迟到次数的数组
int *retry2[5];   // 重试次数的数组
int *failure2[5]; // 失败次数的数组

int success2_sum[5]; // 成功次数总和
int late2_sum[5];    // 迟到次数总和
int retry2_sum[5];   // 重试次数总和
int failure2_sum[5]; // 失败次数总和

int prev_s[5]; // 上一次的成功次数
int prev_l[5]; // 上一次的迟到次数

double max_rt[5];     // 最大响应时间
double total_rt[5];   // 总响应时间
double cur_max_rt[5]; // 当前最大响应时间

double prev_total_rt[5]; // 上一次的总响应时间

#define RTIME_NEWORD 5
#define RTIME_NEWORD 5
#define RTIME_PAYMENT 5
#define RTIME_ORDSTAT 5
#define RTIME_DELIVERY 80
#define RTIME_SLEV 20


// #define RTIME_NEWORD 25
// #define RTIME_NEWORD 25
// #define RTIME_PAYMENT 25
// #define RTIME_ORDSTAT 25
// #define RTIME_DELIVERY 400
// #define RTIME_SLEV 100

// 定义一个整型数组rt_limit，包含5个元素，用于存储不同事务的时间限制
int rt_limit[5] = {RTIME_NEWORD, RTIME_PAYMENT, RTIME_ORDSTAT, RTIME_DELIVERY,
                   RTIME_SLEV};

// 定义一个sb_percentile_t类型的变量local_percentile，用于存储百分位数信息
sb_percentile_t local_percentile;

// 定义一个整型变量activate_transaction，用于标记是否激活事务
int activate_transaction;
// 定义一个双精度浮点型变量time_taken，用于存储事务执行所花费的时间
double time_taken;
// 定义两个clock_t类型的变量time_start和time_end，用于记录事务开始和结束的时间
clock_t time_start;
clock_t time_end;
// 定义一个整型变量counting_on，用于标记是否正在进行计数
int counting_on;
// 定义一个整型变量num_trans，用于存储事务的数量
int num_trans;

// 定义一个长整型变量clk_tck，用于存储每秒钟的时钟滴答数
long clk_tck;

// int is_local = 0;     /* "1" 表示本地 */
int valuable_flg = 0; /* "1" 表示有价值比率 */

// const char *db_path = "tpcc.db"; // 数据库路径

// 定义一个结构体类型thread_arg，用于传递线程参数
typedef struct {
  int number; // 结构体成员number，用于存储整型数据
} thread_arg;
// 声明一个函数thread_main，该函数接收一个指向thread_arg结构体的指针作为参数
// 该函数通常用于线程的入口点，处理线程的主要逻辑
int thread_main(thread_arg *);

// 声明一个函数alarm_handler，该函数用于处理信号，接收一个整型参数signum表示信号编号
// 信号处理函数通常用于响应特定的系统信号，如定时器到期、中断等
void alarm_handler(int signum);
// 声明一个函数alarm_dummy，该函数没有参数
// 该函数的具体用途未在代码中体现，可能用于占位、测试或其他用途
void alarm_dummy();

int start(CallBack2 callback) {
	int i, k;
	long j;
	float f;
	pthread_t *t;
	thread_arg *thd_arg;
	// timer_t timer;
	struct itimerval itval;
	struct sigaction sigact;
	int fd, seed;

	int xx, xy;
	lxw_format *title_format;
	lxw_format *title_format1;
	char message[24];

  	global_callback = callback;


  	title_format  = workbook_add_format(workbook);
	format_set_border(title_format, LXW_BORDER_THIN);
	// format_set_bold(title_format);
	format_set_font_size(title_format, 16);
	format_set_align(title_format, LXW_ALIGN_CENTER);
	format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);

	title_format1  = workbook_add_format(workbook);
	format_set_border(title_format1, LXW_BORDER_THIN);
	format_set_font_size(title_format1, 16);
	format_set_align(title_format1, LXW_ALIGN_RIGHT);
	format_set_align(title_format1, LXW_ALIGN_VERTICAL_CENTER);


	// 打印程序标题
	printf("***************************************\n");
	printf("*** ###easy### TPC-C Load Generator ***\n");
	printf("***************************************\n");

	/* 初始化 */
	hist_init(); // 初始化历史记录
	activate_transaction = 1; // 激活事务
	counting_on = 1; // 开始计数

	// 初始化统计变量
	for (i = 0; i < 5; i++) {
		success[i] = 0;
		late[i] = 0;
		retry[i] = 0;
		failure[i] = 0;

		prev_s[i] = 0;
		prev_l[i] = 0;

		prev_total_rt[i] = 0.0;
		max_rt[i] = 0.0;
		total_rt[i] = 0.0;
	}

	/* 初始化虚拟参数*/
	num_ware = 1;
	num_conn = 10;
	lampup_time = 10;
	measure_time = 20;

	/* 节点数量（默认为0） */
	num_node = 0;
	// arg_offset = 0;

	clk_tck = sysconf(_SC_CLK_TCK);

	num_ware = WAREHOUSE;
	num_conn = 1;
	num_trans = 450;
	// num_trans = 80;
	// db_path = "./test/tpc-c/tpcc.db";

	// 检查 num_node 是否大于 0
	if (num_node > 0) {
		// 检查 num_ware 是否能被 num_node 整除
		if (num_ware % num_node != 0) {
		// 如果不能整除，向标准错误输出提示信息
		fprintf(stderr, "\n [warehouse] 必须被 [num_node] 整除.\n");
		// 终止程序，返回状态码 1
		exit(1);
		}
		// 检查 num_conn 是否能被 num_node 整除
		if (num_conn % num_node != 0) {
		// 如果不能整除，向标准错误输出提示信息
		fprintf(stderr, "\n [connection] 必须被 [num_node] 整除.\n");
		// 终止程序，返回状态码 1
		exit(1);
		}
	}

	printf("<参数>\n");
	printf("  [仓库数量]: %d\n", num_ware);
	printf(" [连接数]: %d\n", num_conn);
	printf("     [上升时间]: %d (秒)\n", lampup_time);
	printf("    [测量时间]: %d (秒)\n", measure_time);


	xx = 1;
	xy = 6;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "1. 测试环境说明", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 35, NULL);
	xx++;
	xy = 2;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "项目", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, "说明", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);
	xx++;
	xy = 2;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "数据库名称", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, "GNCDB", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);
	xx++;
	xy = 2;

	// 将message清空为0
	memset(message, 0, sizeof(message));
	sprintf(message, "%d", num_ware);
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "仓库数量", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, message, title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);
	xx++;
	xy = 2;

	// 将message清空为0
	memset(message, 0, sizeof(message));
	sprintf(message, "%d", num_conn);
  	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "并发连接数", title_format);
  	worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, message, title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);
	xx++;
	xy = 2;

	// 将message清空为0
	memset(message, 0, sizeof(message));
	sprintf(message, "%d(秒)", lampup_time);
  	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "上升时间", title_format);
  	worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, message, title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);
	xx++;
	xy = 2;

	// 将message清空为0
	memset(message, 0, sizeof(message));
	sprintf(message, "%d(秒)", measure_time);
  	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "测量时间", title_format);
  	worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, message, title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);


	/* 初始化定时器 */
	time_count = 0;
	itval.it_interval.tv_sec = PRINT_INTERVAL;
	itval.it_interval.tv_usec = 0;
	itval.it_value.tv_sec = PRINT_INTERVAL;
	itval.it_value.tv_usec = 0;
	sigact.sa_handler = alarm_handler;
	sigact.sa_flags = 0;
	sigemptyset(&sigact.sa_mask);

	/* 设置处理程序和定时器 */
	if (sigaction(SIGALRM, &sigact, NULL) == -1) {
		fprintf(stderr, "sigaction() 出错\n");
		exit(1);
	}

	fd = open("/dev/urandom", O_RDONLY);
	if (fd == -1) {
		fd = open("/dev/random", O_RDONLY);
		if (fd == -1) {
		struct timeval tv;
		gettimeofday(&tv, NULL);
		seed = (tv.tv_sec ^ tv.tv_usec) * tv.tv_sec * tv.tv_usec ^ tv.tv_sec;
		} else {
		read(fd, &seed, sizeof(seed));
		close(fd);
		}
	} else {
		read(fd, &seed, sizeof(seed));
		close(fd);
	}
	SetSeed(seed);

	if (valuable_flg == 0) {
		seq_init(10,

				10, 1, 1, 1); /* 普通比率 */
	} else {
	}

	/* 设置每个计数器 */
	for (i = 0; i < 5; i++) {
		success2[i] = my_malloc(sizeof(int) * num_conn);
		late2[i] = my_malloc(sizeof(int) * num_conn);
		retry2[i] = my_malloc(sizeof(int) * num_conn);
		failure2[i] = my_malloc(sizeof(int) * num_conn);
		for (k = 0; k < num_conn; k++) {
		success2[i][k] = 0;
		late2[i][k] = 0;
		retry2[i][k] = 0;
		failure2[i][k] = 0;
		}
	}

	if (sb_percentile_init(&local_percentile, 100000, 1.0, 1e13))
		return 0;

	/* 设置线程 */

	t = my_malloc(sizeof(pthread_t) * num_conn);
	if (t == NULL) {
		fprintf(stderr, "my_malloc(pthread_t) 出错\n");
		exit(1);
	}
	thd_arg = my_malloc(sizeof(thread_arg) * num_conn);
	if (thd_arg == NULL) {
		fprintf(stderr, "my_malloc(thread_arg) 出错\n");
		exit(1);
	}
	memset(thd_arg, 0, sizeof(thread_arg) * num_conn);

	ctx = my_malloc(sizeof(GNCDB *) * num_conn);
	stmt = my_malloc(sizeof(char **) * num_conn);
	for (i = 0; i < num_conn; i++) {
		stmt[i] = my_malloc(sizeof(char *) * 40);
	}

	if (ctx == NULL) {
		fprintf(stderr, "my_malloc(sql_context) 出错\n");
		exit(1);
	}

	/* EXEC SQL WHENEVER SQLERROR GOTO sqlerr; */

	// for (t_num = 0; t_num < num_conn; t_num++) {
	//   thd_arg[t_num].number = t_num;
	//   pthread_create(&t[t_num], NULL, (void *)thread_main,
	//                  (void *)&(thd_arg[t_num]));
	// }
	thd_arg[0].number = 0;
	thread_main(&thd_arg[0]);



	xx++;
	xy = 6;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "2. 事务响应时间预热阶段", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 35, NULL);
	xx++;
	xy = 2;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "事务类型", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, "平均耗时(纳秒)", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);

	{								
		int i;										
		for(i=0; i<INSTRUMENT_NUM; i++)				
		{
			if (Instrustats[i] > 0)				
			{
				xx++;
				xy = 2;
				worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, Instruprint[i], title_format);
				memset(message, 0, sizeof(message));
				sprintf(message, "%lu ns", Instrustats[i]);
				worksheet_merge_range(tpcc_worksheet, xx, xy + 1, xx, 6, message, title_format);
				worksheet_set_row(tpcc_worksheet, xx, 30, NULL);
			}
		}
	}
	
	printf("\n上升时间(%d 秒).\n", lampup_time);
	fflush(stdout);
	sleep(lampup_time);
	printf("\n开始测量.\n\n");
	fflush(stdout);

	#ifndef _SLEEP_ONLY_
	if (setitimer(ITIMER_REAL, &itval, NULL) == -1) {
		fprintf(stderr, "setitimer() 出错\n");
	}
	#endif

	counting_on = 1;

	counting_on = 1;

	#ifndef _SLEEP_ONLY_
	/* 停止定时器 */
	itval.it_interval.tv_sec = 0;
	itval.it_interval.tv_usec = 0;
	itval.it_value.tv_sec = 0;
	itval.it_value.tv_usec = 0;
	if (setitimer(ITIMER_REAL, &itval, NULL) == -1) {
		fprintf(stderr, "setitimer() 出错\n");
	}
	#endif

	// printf("\n停止线程");
	activate_transaction = 0;

	// /* 等待线程结束并关闭连接*/
	// for (i = 0; i < num_conn; i++) {
	//   pthread_join(t[i], NULL);
	// }

	// printf("线程结束并关闭连接\n");

	my_free(ctx);
	for (i = 0; i < num_conn; i++) {
		my_free(stmt[i]);
	}
	my_free(stmt);

	my_free(t);
	my_free(thd_arg);


	xx++;
	xy = 6;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "3. 原始事务执行统计", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 35, NULL);
	xx++;
	xy = 0;
	worksheet_write_string(tpcc_worksheet, xx, xy++, "事物类型ID", title_format);
	worksheet_write_string(tpcc_worksheet, xx, xy++, "成功数", title_format);
	worksheet_write_string(tpcc_worksheet, xx, xy++, "迟到数", title_format);
	worksheet_write_string(tpcc_worksheet, xx, xy++, "重试数", title_format);
	worksheet_write_string(tpcc_worksheet, xx, xy++, "失败数", title_format);
	worksheet_write_string(tpcc_worksheet, xx, xy++, "平均响应时间(ms)", title_format);
	worksheet_write_string(tpcc_worksheet, xx, xy++, "SLA(ms)", title_format);
	
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);



	printf("\n<原始结果>\n");
	for (i = 0; i < 5; i++) {
		printf("  [%d] 成功:%d 迟到:%d 重试:%d 失败:%d 平均响应时间: %.1f (%d)\n",
			i, success[i], late[i], retry[i], failure[i],
			total_rt[i] / (success[i] + late[i]), rt_limit[i]);
		xx++;
		xy = 0;
		worksheet_write_number(tpcc_worksheet, xx, xy++, i, title_format);
		worksheet_write_number(tpcc_worksheet, xx, xy++, success[i], title_format);
		worksheet_write_number(tpcc_worksheet, xx, xy++, late[i], title_format1);
		worksheet_write_number(tpcc_worksheet, xx, xy++, retry[i], title_format1);
		worksheet_write_number(tpcc_worksheet, xx, xy++, failure[i], title_format1);
		worksheet_write_number(tpcc_worksheet, xx, xy++, total_rt[i] / (success[i] + late[i]), title_format1);
		worksheet_write_number(tpcc_worksheet, xx, xy++, rt_limit[i], title_format);
		worksheet_set_row(tpcc_worksheet, xx, 30, NULL);
	}
	printf(" in %d 秒.\n", (measure_time / PRINT_INTERVAL) * PRINT_INTERVAL);

	printf("\n<原始结果2(总和版本)>\n");
	for (i = 0; i < 5; i++) {
		success2_sum[i] = 0;
		late2_sum[i] = 0;
		retry2_sum[i] = 0;
		failure2_sum[i] = 0;
		for (k = 0; k < num_conn; k++) {
		success2_sum[i] += success2[i][k];
		late2_sum[i] += late2[i][k];
		retry2_sum[i] += retry2[i][k];
		failure2_sum[i] += failure2[i][k];
		}
	}
	for (i = 0; i < 5; i++) {
		printf("  [%d] 成功:%d  迟到:%d  重试:%d  失败:%d \n", i, success2_sum[i],
			late2_sum[i], retry2_sum[i], failure2_sum[i]);
	}

	xx++;
	xy = 6;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "4. 事务类型比例符合性检查", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 35, NULL);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 1, "事务类型", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, 2, xx, 3, "实际比例", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, 4, xx, 5, "最低要求", title_format);
	worksheet_write_string(tpcc_worksheet, xx, 6, "是否达标", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);


	printf("\n<约束检查>（所有值必须是 [OK])\n [事务百分比]\n");
	for (i = 0, j = 0; i < 5; i++) {
		j += (success[i] + late[i]);
	}

	f = 100.0 * (float)(success[1] + late[1]) / (float)j;
	printf("        Payment: %3.2f%% (>=43.0%%)", f);

	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 1, "Payment", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 2, xx, 3, message, title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "43.0%%");
	worksheet_merge_range(tpcc_worksheet, xx, 4, xx, 5, message, title_format);
	if (f >= 43.0) {
		printf(" [OK]\n");
		worksheet_write_string(tpcc_worksheet, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_write_string(tpcc_worksheet, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);


	f = 100.0 * (float)(success[2] + late[2]) / (float)j;
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 1, "Order-Status", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 2, xx, 3, message, title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "4.0%%");
	worksheet_merge_range(tpcc_worksheet, xx, 4, xx, 5, message, title_format);
	printf("   Order-Status: %3.2f%% (>= 4.0%%)", f);
	if (f >= 4.0) {
		printf(" [OK]\n");
		worksheet_write_string(tpcc_worksheet, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_write_string(tpcc_worksheet, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);


	f = 100.0 * (float)(success[3] + late[3]) / (float)j;
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 1, "Delivery", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 2, xx, 3, message, title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "4.0%%");
	worksheet_merge_range(tpcc_worksheet, xx, 4, xx, 5, message, title_format);
	
	printf("       Delivery: %3.2f%% (>= 4.0%%)", f);
	if (f >= 4.0) {
		worksheet_write_string(tpcc_worksheet, xx, 6, "是", title_format);
		printf(" [OK]\n");
	} else {
		printf(" [NG] *\n");
		worksheet_write_string(tpcc_worksheet, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);

	f = 100.0 * (float)(success[4] + late[4]) / (float)j;
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 1, "Stock-Level", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 2, xx, 3, message, title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "4.0%%");
	worksheet_merge_range(tpcc_worksheet, xx, 4, xx, 5, message, title_format);

	printf("    Stock-Level: %3.2f%% (>= 4.0%%)", f);
	if (f >= 4.0) {
		printf(" [OK]\n");
		worksheet_write_string(tpcc_worksheet, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_write_string(tpcc_worksheet, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);

	printf(" [响应时间(至少90%%通过）]\n");

	xx++;
	xy = 6;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "5. 响应时间达标率检查(至少90%%通过)", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 35, NULL);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 2, "事务类型", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, 3, xx, 4, "达标率", title_format);
	worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "是否达标", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);


	f = 100.0 * (float)success[0] / (float)(success[0] + late[0]);
	printf("      New-Order: %3.2f%% ", f

	);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 2, "New-Order", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 3, xx, 4, message, title_format);
	if (f >= 90.0) {
		printf(" [OK]\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);

	f = 100.0 * (float)success[1] / (float)(success[1] + late[1]);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 2, "Payment", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 3, xx, 4, message, title_format);
	
	printf("        Payment: %3.2f%% ", f);
	if (f >= 90.0) {
		printf(" [OK]\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);

	f = 100.0 * (float)success[2] / (float)(success[2] + late[2]);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 2, "Order-Status", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 3, xx, 4, message, title_format);
	printf("   Order-Status: %3.2f%% ", f);
	if (f >= 90.0) {
		printf(" [OK]\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);

	f = 100.0 * (float)success[3] / (float)(success[3] + late[3]);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 2, "Delivery", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 3, xx, 4, message, title_format);
	printf("       Delivery: %3.2f%% ", f);
	if (f >= 90.0) {
		printf(" [OK]\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "否", title_format);
	}
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);


	f = 100.0 * (float)success[4] / (float)(success[4] + late[4]);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 2, "Stock-Level", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%3.2f%%", f);
	worksheet_merge_range(tpcc_worksheet, xx, 3, xx, 4, message, title_format);
	printf("    Stock-Level: %3.2f%% ", f);
	if (f >= 90.0) {
		printf(" [OK]\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "是", title_format);
	} else {
		printf(" [NG] *\n");
		worksheet_merge_range(tpcc_worksheet, xx, 5, xx, 6, "否", title_format);
	}

	printf("\n<TpmC>\n");
	f = (float)(success[0] + late[0]) * 60.0 /
		(float)((measure_time / PRINT_INTERVAL) * PRINT_INTERVAL);
	printf("                 %.3f TpmC\n", f);

	printf("\nTime taken\n");
	time_taken = ((double)(time_end - time_start)) / 1000000;
	printf("                 %.3f seconds\n", time_taken);

	xx++;
	xy = 6;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, xy, "6. TpmC性能指标", title_format);
	worksheet_set_row(tpcc_worksheet, xx, 35, NULL);
	xx++;
	worksheet_merge_range(tpcc_worksheet, xx, 0, xx, 3, "TpmC值", title_format);
	memset(message, 0, sizeof(message));
	sprintf(message, "%.3f TpmC", f);
	worksheet_merge_range(tpcc_worksheet, xx, 4, xx, 6, message, title_format1);
	worksheet_set_row(tpcc_worksheet, xx, 30, NULL);

	// exit(0);
	return GNCDB_SUCCESS;

// sqlerr:
//   fprintf(stdout, "main 中出错\n");
//   exit(1);
}

// 定义报警信号处理函数
void alarm_handler(int signum) {
  int i;
  int s[5], l[5];
  // double rt90[5];
  double trt[5];
  double percentile_val;
  double percentile_val99;

  // 复制数组以防止并发访问
  // 遍历数组，将全局变量success、late、total_rt的值复制到局部变量s、l、trt中
  for (i = 0; i < 5; i++) {
    s[i] = success[i];
    l[i] = late[i];
    trt[i] = total_rt[i];
    // rt90[i] = hist_ckp(i); // 注释掉的代码，可能是用于获取某个百分位数的响应时间
  }

  // 更新计时器
  // 将全局变量time_count增加PRINT_INTERVAL，表示时间间隔
  time_count += PRINT_INTERVAL;

  // 计算百分位数值
  // 计算并获取95%和99%的百分位数响应时间
  percentile_val = sb_percentile_calculate(&local_percentile, 95);
  percentile_val99 = sb_percentile_calculate(&local_percentile, 99);
  // 重置百分位数计算器
  sb_percentile_reset(&local_percentile);

  // 打印报警信息
  // 打印当前时间、事务数、95%和99%的响应时间、最大响应时间等信息
  printf("%4d, trx: %d, 95%%: %.3f, 99%%: %.3f, max_rt: %.3f, %d|%.3f, %d|%.3f, "
         "%d|%.3f, %d|%.3f\n",
         time_count, (s[0] + l[0] - prev_s[0] - prev_l[0]), percentile_val,
         percentile_val99, (double)cur_max_rt[0],
         (s[1] + l[1] - prev_s[1] - prev_l[1]), (double)cur_max_rt[1],
         (s[2] + l[2] - prev_s[2] - prev_l[2]), (double)cur_max_rt[2],
         (s[3] + l[3] - prev_s[3] - prev_l[3]), (double)cur_max_rt[3],
         (s[4] + l[4] - prev_s[4] - prev_l[4]), (double)cur_max_rt[4]);
  fflush(stdout);

  // 更新前一次成功、迟到的事务数和总响应时间
  for (i = 0; i < 5; i++) {
    prev_s[i] = s[i];
    prev_l[i] = l[i];
    prev_total_rt[i] = trt[i];
    cur_max_rt[i] = 0.0;
  }
}

// 定义报警响应函数
void alarm_dummy() {
  int i;
  int s[5], l[5];
  float rt90[5];

  // 复制数组以防止并发访问
  for (i = 0; i < 5; i++) {
    s[i] = success[i];
    l[i] = late[i];
    rt90[i] = hist_ckp(i);
  }

  // 更新计时器
  time_count += PRINT_INTERVAL;

  // 打印响应信息
  printf(
      "%4d, %d(%d):%.2f, %d(%d):%.2f, %d(%d):%.2f, %d(%d):%.2f, %d(%d):%.2f\n",
      time_count, (s[0] + l[0] - prev_s[0] - prev_l[0]), (l[0] - prev_l[0]),
      rt90[0], (s[1] + l[1] - prev_s[1] - prev_l[1]), (l[1] - prev_l[1]),
      rt90[1], (s[2] + l[2] - prev_s[2] - prev_l[2]), (l[2] - prev_l[2]),
      rt90[2], (s[3] + l[3] - prev_s[3] - prev_l[3]), (l[3] - prev_l[3]),
      rt90[3], (s[4] + l[4] - prev_s[4] - prev_l[4]), (l[4] - prev_l[4]),
      rt90[4]);
  fflush(stdout);

  // 更新前一次成功、迟到的事务数
  for (i = 0; i < 5; i++) {
    prev_s[i] = s[i];
    prev_l[i] = l[i];
  }
}

// 线程主函数
int thread_main(thread_arg *arg) {
  int rc = GNCDB_SUCCESS;
  int t_num = arg->number;
  int r, i;
  GNCDB *db;
  SQLStageEvent sql_event;

  // 打印正在打开数据库的信息
  // printf("%s: 正在打开数据库，线程 ID = %lu\n", __func__, pthread_self());
  printf("%s: 正在打开数据库\n", __func__);

  // 打开数据库连接
  GNCDB_open(&db, "tpcc.db", 0, 0);

  // 打印已打开数据库的信息
  printf("%s: 数据库已打开\n", __func__);

  ctx[t_num] = db;

  // 准备所有 SQL 语句

  // 初始化计时器
  INITIALIZE_TIMERS();

  time_start = clock();
  SQLStageEventInit(&sql_event);
  // sql_event.affectedRows = 0;
  // sql_event.sqlNode = NULL;
  // sql_event.plan = NULL;
  // sql_event.stmt = NULL;
  sql_event.logicalPlan = NULL;
  sql_event.callback = global_callback;
  sql_event.db = db;
  // sql_event.txn = transcationConstrcut(sql_event.db);
  // sql_event.affectedRows = 0;
  // sql_event.subQueryLevel = 0;
  // sql_event.isOpen = false;
  // sql_event.res = NULL;
  // GNCDB_select(db, global_callback, NULL, 1, 0, 0, "schema");
  // db_exec(sql_event.db, "select * from schema;", global_callback);
  // 执行事务
  for (i = 0; i < num_trans; i++) {
    // 构建事务
    sql_event.txn = transcationConstrcut(sql_event.db);
    if (sql_event.txn == NULL) {
      return GNCDB_MEM;
    }
    if(sql_event.txn->status == ABORTED){
      printf("sql_event.txn->status == ABORTED\n");
    }
    // 执行事务
    r = driver(t_num, &sql_event);

    // 提交事务
    rc = transactionCommit(sql_event.txn, sql_event.db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  // 打印时间信息
  PRINT_TIME();

  time_end =             clock();

  // 关闭数据库连接
  GNCDB_close(&ctx[t_num]);

  printf(".");
  fflush(stdout);

  return (r);

  return rc;
}

