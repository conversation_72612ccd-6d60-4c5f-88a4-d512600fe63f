cmake_minimum_required(VERSION 3.10)

# 设置可执行文件输出目录
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/build)


aux_source_directory(. TPCC_SRC)
# 创建一个包含所有源文件的库
add_library(gncdb_tpcctest ${TPCC_SRC})

target_link_libraries(gncdb_tpcctest m pthread)

# 如果有特定的编译选项需要添加
# target_compile_options(tpcc_load PRIVATE -你的编译选项)
# target_compile_definitions(tpcc_load PRIVATE -你的预处理器定义)
