/*
 * -*-C-*- 
 * ordstat.pc 
 * corresponds to A.3 in appendix A
 */

#include <string.h>
#include <stdio.h>

// #include <sqlite3.h>

// #include "common.h"
#include "spt_proc.h"
#include "tpc.h"
#include "transaction.h"

extern GNCDB **ctx;
extern char ***stmt;

/*
 * the order status transaction
 */

#ifdef PRINT_TRXS_COST_TIME
int ordstat( int t_num,
	     int w_id_arg,		/* warehouse id */
	     int d_id_arg,		/* district id */
	     int byname,		/* select by c_id or c_last? */
	     int c_id_arg,		/* customer id */
	     char c_last_arg[],	        /* customer last name, format? */
		 SQLStageEvent *sql_event
)
{
	// int ret;
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            c_id = c_id_arg;
	int            c_d_id = d_id;
	int            c_w_id = w_id;
	char            c_first[17];
	char            c_middle[3];
	char            c_last[17];
	float           c_balance;
	int            o_id;
	char            o_entry_d[25];
	int            o_carrier_id;
	int            ol_i_id;
	int            ol_supply_w_id;
	int            ol_quantity;
	float           ol_amount;
	char            ol_delivery_d[25];
	int            namecnt = 0;

	int             n;
	int             proceed = 0;

	char            sql_stmt[520];
	int rc = GNCDB_SUCCESS;
	int num_cols;
	int bytes;
	FILE* fp3;
    fp3 = fopen("avgOrdStat", "a");
	
	/*EXEC SQL WHENEVER NOT FOUND GOTO sqlerr;*/
	/*EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
	struct timespec start, end; // 计时器
	
	unsigned long long part2CostTime = 0;
	if (byname) {
		strcpy(c_last, c_last_arg);
		proceed = 1;
		/*EXEC_SQL SELECT count(c_id)
			INTO :namecnt
		        FROM customer
			WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
		        AND c_last = :c_last;*/
		clock_gettime(CLOCK_REALTIME, &start);
		sprintf(sql_stmt, "SELECT count(c_id) FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s';", c_w_id, c_d_id, c_last);
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		clock_gettime(CLOCK_REALTIME, &end);
		part2CostTime += ((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec));
		if(rc != GNCDB_SUCCESS){
			if(sql_event->sql_result->field_values == NULL){
				printf("#1, sql_event->sql_result->field_values is NULL\n");
				goto sqlerr;
			}
			num_cols = sql_event->sql_result->field_count;
			if(num_cols != 1){
				printf("#2, num_cols is not 1\n");
				goto sqlerr;
			}
			namecnt = atoi(sql_event->sql_result->field_values[0]);
		}
		SQLStageEventReset(sql_event);

		proceed = 2;
		/*EXEC_SQL DECLARE c_byname_o CURSOR FOR
		        SELECT c_balance, c_first, c_middle, c_last
		        FROM customer
		        WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
			AND c_last = :c_last
			ORDER BY c_first;
		proceed = 3;
		EXEC_SQL OPEN c_byname_o;*/
		sprintf(sql_stmt, "SELECT c_balance, c_first, c_middle, c_last FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s' ORDER BY c_first;", c_w_id, c_d_id, c_last);

		if (namecnt % 2)
			namecnt++;	/* Locate midpoint customer; */
		unsigned long long costTime = 0;
		int count = namecnt / 2;
		for (n = 0; n < namecnt / 2; n++) {
			clock_gettime(CLOCK_REALTIME, &start);
			rc = txnExecuteSQLStep(sql_event, sql_stmt);
			clock_gettime(CLOCK_REALTIME, &end);
			costTime += ((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec));
			if(rc != GNCDB_SUCCESS){
				if(sql_event->sql_result->field_values == NULL){
					printf("#3, sql_event->sql_result->field_values is NULL\n");
					goto sqlerr;
				}
				num_cols = sql_event->sql_result->field_count;
				if(num_cols != 4){
					printf("#4, num_cols is not 4\n");
					goto sqlerr;
				}
				c_balance = atof(sql_event->sql_result->field_values[0]);
				strcpy(c_first, sql_event->sql_result->field_values[1]);
				strcpy(c_middle, sql_event->sql_result->field_values[2]);
				strcpy(c_last, sql_event->sql_result->field_values[3]);
			}
		}
		part2CostTime += count == 0 ? 0 :costTime / count;

		SQLStageEventReset(sql_event);

		proceed = 5;
		/*EXEC_SQL CLOSE  c_byname_o;*/

	} else {		/* by number */
		proceed = 6;
		/*EXEC_SQL SELECT c_balance, c_first, c_middle, c_last
			INTO :c_balance, :c_first, :c_middle, :c_last
		        FROM customer
		        WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
			AND c_id = :c_id;*/
		clock_gettime(CLOCK_REALTIME, &start);
		sprintf(sql_stmt, "SELECT c_balance, c_first, c_middle, c_last FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		clock_gettime(CLOCK_REALTIME, &end);
		part2CostTime += ((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec));
		if(rc != GNCDB_SUCCESS){
			if(sql_event->sql_result->field_values == NULL){
				printf("#5, sql_event->sql_result->field_values is NULL\n");
				goto sqlerr;
			}
			num_cols = sql_event->sql_result->field_count;
			if(num_cols != 4){
				printf("#6, num_cols is not 4\n");
				goto sqlerr;
			}
			c_balance = atof(sql_event->sql_result->field_values[0]);
			strcpy(c_first, sql_event->sql_result->field_values[1]);
			strcpy(c_middle, sql_event->sql_result->field_values[2]);
			strcpy(c_last, sql_event->sql_result->field_values[3]);
		}

		SQLStageEventReset(sql_event);
	}

	fprintf(fp3, "%lld,", part2CostTime / 1000);
	/* find the most recent order for this customer */

	proceed = 7;
	/*EXEC_SQL SELECT o_id, o_entry_d, COALESCE(o_carrier_id,0)
		INTO :o_id, :o_entry_d, :o_carrier_id
	        FROM orders
	        WHERE o_w_id = :c_w_id
		AND o_d_id = :c_d_id
		AND o_c_id = :c_id
		AND o_id = (SELECT MAX(o_id)
		    	    FROM orders
		    	    WHERE o_w_id = :c_w_id
		  	    AND o_d_id = :c_d_id
		    	    AND o_c_id = :c_id);*/
	clock_gettime(CLOCK_REALTIME, &start);
	sprintf(sql_stmt, "SELECT o_id, o_entry_d, o_carrier_id FROM orders WHERE o_w_id = %d AND o_d_id = %d AND o_c_id = %d AND o_id = (SELECT MAX(o_id) FROM orders WHERE o_w_id = %d AND o_d_id = %d AND o_c_id = %d);", c_w_id, c_d_id, c_id, c_w_id, c_d_id, c_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	clock_gettime(CLOCK_REALTIME, &end);
	fprintf(fp3, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec)) / 1000);
	if(rc != GNCDB_SUCCESS){
		if(sql_event->sql_result->field_values == NULL){
			printf("#7, sql_event->sql_result->field_values is NULL\n");
			goto sqlerr;
		}
		num_cols = sql_event->sql_result->field_count;
		if(num_cols != 3){
			printf("#8, num_cols is not 3\n");
			goto sqlerr;
		}
		o_id = atoi(sql_event->sql_result->field_values[0]);
		strcpy(o_entry_d, sql_event->sql_result->field_values[1]);
		o_carrier_id = sql_event->sql_result->field_values[2] == NULL ? 0 : atoi(sql_event->sql_result->field_values[2]);
	}
	SQLStageEventReset(sql_event);

	proceed = 8;
	/*EXEC_SQL DECLARE c_items CURSOR FOR
		SELECT ol_i_id, ol_supply_w_id, ol_quantity, ol_amount,
                       ol_delivery_d
		FROM order_line
	        WHERE ol_w_id = :c_w_id
		AND ol_d_id = :c_d_id
		AND ol_o_id = :o_id;*/

	sprintf(sql_stmt, "SELECT ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_delivery_d FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d AND ol_o_id = %d;", c_w_id, c_d_id, o_id);
	int cnt = 0;
	unsigned long long part3CostTime = 0;
	for(;;) {
		// cnt++;
		// printf("cnt = %d\n", cnt);
		clock_gettime(CLOCK_REALTIME, &start);
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		clock_gettime(CLOCK_REALTIME, &end);
		part3CostTime+= ((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec));
		if(rc == GNCDB_SUCCESS){
			break;
		}
		if(sql_event->sql_result->field_values != NULL){
			proceed = 10;
			num_cols = sql_event->sql_result->field_count;
			if(num_cols != 5){
				printf("#9, num_cols is not 5\n");
				goto sqlerr;
			}
			ol_i_id = atoi(sql_event->sql_result->field_values[0]);
			ol_supply_w_id = atoi(sql_event->sql_result->field_values[1]);
			ol_quantity = atoi(sql_event->sql_result->field_values[2]);
			ol_amount = atof(sql_event->sql_result->field_values[3]);
			bytes = sql_event->sql_result->field_values[4] == NULL ? 0 : strlen(sql_event->sql_result->field_values[4]);
			if (bytes)
				strcpy(ol_delivery_d, sql_event->sql_result->field_values[4]);

		}
		else{
			printf("#10, sql_event->sql_result->field_values is NULL\n");
			goto sqlerr;
		}
	}
	fprintf(fp3, "%lld\n", cnt == 0 ? 0 : part3CostTime / cnt / 1000);
	SQLStageEventReset(sql_event);

	// sqlite3_reset(sqlite_stmt);

	/*proceed = 9;
	EXEC_SQL OPEN c_items;

	EXEC SQL WHENEVER NOT FOUND GOTO done;*/
	fclose(fp3);
	return 1;
sqlerr:
	fprintf(stderr, "ordstat %d:%d\n",t_num,proceed);
	printf("%s: error: rc = %d\n", __func__, rc);
	//error(ctx[t_num],mysql_stmt);
        /*EXEC SQL WHENEVER SQLERROR GOTO sqlerrerr;*/
	/*EXEC_SQL ROLLBACK WORK;*/
	transactionRollback(sql_event->txn, sql_event->db);

sqlerrerr:
	return (0);
}


#else

int ordstat( int t_num,
	     int w_id_arg,		/* warehouse id */
	     int d_id_arg,		/* district id */
	     int byname,		/* select by c_id or c_last? */
	     int c_id_arg,		/* customer id */
	     char c_last_arg[],	        /* customer last name, format? */
		 SQLStageEvent *sql_event
)
{
	// int ret;
	int            w_id = w_id_arg;
	int            d_id = d_id_arg;
	int            c_id = c_id_arg;
	int            c_d_id = d_id;
	int            c_w_id = w_id;
	char            c_first[17];
	char            c_middle[3];
	char            c_last[17];
	float           c_balance;
	int            o_id = 0;
	int max_o_id = 0;
	char            o_entry_d[25];
	int            o_carrier_id;
	int            ol_i_id;
	int            ol_supply_w_id;
	int            ol_quantity;
	float           ol_amount;
	char            ol_delivery_d[25];
	int            namecnt = 0;

	int             n;
	int             proceed = 0;

	char            sql_stmt[520];
	int rc = GNCDB_SUCCESS;
	int num_cols;
	int bytes;
	
	/*EXEC SQL WHENEVER NOT FOUND GOTO sqlerr;*/
	/*EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
	
	if (byname) { // 如果byname为真，表示需要按名字进行查询
		strcpy(c_last, c_last_arg); // 将传入的c_last_arg复制到c_last变量中
		proceed = 1; // 设置proceed为1，表示可以继续执行后续操作
		/*EXEC_SQL SELECT count(c_id) // 注释掉的SQL语句，用于统计符合条件的客户数量
			INTO :namecnt // 将查询结果存储到namecnt变量中
		        FROM customer // 从customer表中查询
			WHERE c_w_id = :c_w_id // 条件：仓库ID等于c_w_id
			AND c_d_id = :c_d_id // 条件：地区ID等于c_d_id
		        AND c_last = :c_last;*/
		// 构造SQL查询语句，统计符合条件的客户数量
		sprintf(sql_stmt, "SELECT count(c_id) FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s';", c_w_id, c_d_id, c_last);
		// 执行SQL查询
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		if(rc == GNCDB_SUCCESS){
			// 检查查询结果是否为空
			if(sql_event->res->fieldValues == NULL){
				printf("#1, sql_event->sql_result->field_values is NULL\n");
				goto sqlerr; // 跳转到错误处理
			}
			// 获取查询结果的列数
			num_cols = sql_event->res->fieldCount;
			// 检查列数是否为1
			if(num_cols != 1){
				printf("#2, num_cols is not 1\n");
				goto sqlerr; // 跳转到错误处理
			}
			// 将查询结果转换为整数并存储到namecnt变量中
			namecnt = atoi(sql_event->res->fieldValues[0]);
		}
		else{
			printf("#2, executeSQLStep failed, rc = %d\n", rc);
			goto sqlerr; // 跳转到错误处理
		}
		// 重置SQL事件，准备下一次查询
		SQLStageEventReset(sql_event);

		proceed = 2;
		/*EXEC_SQL DECLARE c_byname_o CURSOR FOR
		        SELECT c_balance, c_first, c_middle, c_last
		        FROM customer
		        WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
			AND c_last = :c_last
			ORDER BY c_first;
		proceed = 3;
		EXEC_SQL OPEN c_byname_o;*/
		// 使用sprintf函数构建SQL查询语句，查询customer表中符合条件的记录
		// c_balance, c_first, c_middle, c_last为查询的字段
		// c_w_id, c_d_id, c_last为查询条件
		// 查询结果按c_first字段排序
		sprintf(sql_stmt, "SELECT c_balance, c_first, c_middle, c_last FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_last = '%s' ORDER BY c_first;", c_w_id, c_d_id, c_last);

		// 如果namecnt是奇数，则将其加1，以便找到中间的顾客
		if (namecnt % 2)
			namecnt++;	/* Locate midpoint customer; */
		// 循环执行SQL查询，直到找到中间的顾客
		for (n = 0; n < namecnt / 2; n++) {
			// 执行SQL查询
			rc = txnExecuteSQLStep(sql_event, sql_stmt);
			// 如果查询成功
			if(rc == GNCDB_SUCCESS){
				// 检查查询结果是否为空
				if(sql_event->res->fieldValues == NULL){
					printf("#3, sql_event->sql_result->field_values is NULL\n");
					goto sqlerr; // 跳转到错误处理
				}
				// 获取查询结果的列数
				num_cols = sql_event->res->fieldCount;
				// 检查列数是否为4
				if(num_cols != 4){
					printf("#4, num_cols is not 4\n");
					goto sqlerr; // 跳转到错误处理
				}
				// 将查询结果中的字段值赋给相应的变量
				c_balance = atof(sql_event->res->fieldValues[0]);
				strcpy(c_first, sql_event->res->fieldValues[1]);
				strcpy(c_middle, sql_event->res->fieldValues[2]);
				strcpy(c_last, sql_event->res->fieldValues[3]);
			}
			else{
				printf("#4, executeSQLStep failed, rc = %d\n", rc);
				goto sqlerr; // 跳转到错误处理
			}
		}

		// 重置SQL事件，以便下次使用
		SQLStageEventReset(sql_event);

		proceed = 5;
		/*EXEC_SQL CLOSE  c_byname_o;*/

	} else {		/* by number */
		proceed = 6;
		/*EXEC_SQL SELECT c_balance, c_first, c_middle, c_last
			INTO :c_balance, :c_first, :c_middle, :c_last
		        FROM customer
		        WHERE c_w_id = :c_w_id
			AND c_d_id = :c_d_id
			AND c_id = :c_id;*/
    // 使用sprintf函数构建SQL查询语句，查询customer表中符合条件的记录
    // c_balance, c_first, c_middle, c_last分别表示客户的余额、名、中间名和姓
    // c_w_id, c_d_id, c_id分别表示客户的工作仓库ID、地区ID和客户ID
		sprintf(sql_stmt, "SELECT c_balance, c_first, c_middle, c_last FROM customer WHERE c_w_id = %d AND c_d_id = %d AND c_id = %d;", c_w_id, c_d_id, c_id);
    // 执行SQL查询语句，并获取返回码rc
		rc = txnExecuteSQLStep(sql_event, sql_stmt);
    // 检查返回码是否为GNCDB_SUCCESS，表示查询成功
		if(rc == GNCDB_SUCCESS){
        // 检查查询结果中的字段值是否为空
			if(sql_event->res->fieldValues == NULL){
            // 如果字段值为空，打印错误信息
				printf("#5, sql_event->sql_result->field_values is NULL\n");
            // 跳转到sqlerr标签，处理错误
				goto sqlerr;
			}
        // 获取查询结果中的字段数量
			num_cols = sql_event->res->fieldCount;
        // 检查字段数量是否为4，如果不是则打印错误信息并跳转到sqlerr标签
			if(num_cols != 4){
				printf("#6, num_cols is not 4\n");
				goto sqlerr;
			}
        // 将查询结果中的第一个字段值转换为浮点数，并赋值给c_balance
			c_balance = atof(sql_event->res->fieldValues[0]);
			(void)c_balance;
        // 将查询结果中的第二个字段值复制到c_first
			strcpy(c_first, sql_event->res->fieldValues[1]);
        // 将查询结果中的第三个字段值复制到c_middle
			strcpy(c_middle, sql_event->res->fieldValues[2]);
        // 将查询结果中的第四个字段值复制到c_last
			strcpy(c_last, sql_event->res->fieldValues[3]);
		}
		// 如果查询失败，打印错误信息并跳转到sqlerr标签
		else{
			printf("#6, executeSQLStep failed, rc = %d\n", rc);
			goto sqlerr;
		}

    // 重置SQL事件，准备下一次查询
		SQLStageEventReset(sql_event);
	}

	/* find the most recent order for this customer */

	proceed = 7;
	/*EXEC_SQL SELECT o_id, o_entry_d, COALESCE(o_carrier_id,0)
		INTO :o_id, :o_entry_d, :o_carrier_id
	        FROM orders
	        WHERE o_w_id = :c_w_id
		AND o_d_id = :c_d_id
		AND o_c_id = :c_id
		AND o_id = (SELECT MAX(o_id)
		    	    FROM orders
		    	    WHERE o_w_id = :c_w_id
		  	    AND o_d_id = :c_d_id
		    	    AND o_c_id = :c_id);*/


	// 1. 先查出(SELECT MAX(o_id) FROM orders WHERE o_w_id = %d AND o_d_id = %d AND o_c_id = %d);
	// 2. 再查出SELECT o_id, o_entry_d, COALESCE(o_carrier_id,0) FROM orders WHERE o_w_id = %d AND o_d_id = %d AND o_c_id = %d AND o_id = (上面查出的结果);
	// 3. 最后将结果赋值给o_id, o_entry_d, o_carrier_id
	sprintf(sql_stmt, "SELECT MAX(o_id) FROM orders WHERE o_w_id = %d AND o_d_id = %d AND o_c_id = %d;",  c_w_id, c_d_id, c_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	if(rc == GNCDB_SUCCESS){
		if(sql_event->res->fieldValues == NULL){
			printf("#6, sql_event->sql_result->field_values is NULL\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->fieldCount;
		if(num_cols != 1){
			printf("#7, num_cols is not 1\n");
			goto sqlerr;
		}
		max_o_id = atoi(sql_event->res->fieldValues[0]);
	}
	else{
		printf("#7, executeSQLStep failed, rc = %d\n", rc);
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	sprintf(sql_stmt, "SELECT o_id, o_entry_d, o_carrier_id FROM orders WHERE o_w_id = %d AND o_d_id = %d AND o_c_id = %d AND o_id = %d;", c_w_id, c_d_id, c_id, max_o_id);
	rc = txnExecuteSQLStep(sql_event, sql_stmt);
	if(rc == GNCDB_SUCCESS){
		if(sql_event->res->fieldValues == NULL){
			printf("#7, sql_event->sql_result->field_values is NULL\n");
			goto sqlerr;
		}
		num_cols = sql_event->res->fieldCount;
		if(num_cols != 3){
			printf("#8, num_cols is not 3\n");
			goto sqlerr;
		}
		o_id = atoi(sql_event->res->fieldValues[0]);
		// printf("o_entry_d = %s\n", sql_event->res->fieldValues[1]);
		strcpy(o_entry_d, sql_event->res->fieldValues[1]);
		o_carrier_id = sql_event->res->fieldValues[2] == NULL ? 0 : atoi(sql_event->res->fieldValues[2]);
		(void)o_carrier_id;
	}
	else{
		printf("#8, executeSQLStep failed, rc = %d\n", rc);
		goto sqlerr;
	}
	SQLStageEventReset(sql_event);

	proceed = 8;
	/*EXEC_SQL DECLARE c_items CURSOR FOR
		SELECT ol_i_id, ol_supply_w_id, ol_quantity, ol_amount,
                       ol_delivery_d
		FROM order_line
	        WHERE ol_w_id = :c_w_id
		AND ol_d_id = :c_d_id
		AND ol_o_id = :o_id;*/

	sprintf(sql_stmt, "SELECT ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_delivery_d FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d AND ol_o_id = %d;", c_w_id, c_d_id, o_id);
	for(;;) {
		// cnt++;
		// printf("cnt = %d\n", cnt);

		// {
		// 	char sql[128] = {0};
		// 	sprintf(sql, "select * from order_line where ol_o_id = %d;", o_id);
		// 	printf("sql_stmt = %s\n", sql_stmt);
		// 	initTpccFlag();
		// 	GNCDB_exec(sql_event->db, sql, tpccCallBack, NULL, NULL);
		// }


		rc = txnExecuteSQLStep(sql_event, sql_stmt);
		if(rc == GNCDB_SUCCESS && sql_event->affectedRows == 0){
			break;
		}
		if(sql_event->res->fieldValues != NULL){
			proceed = 10;
			num_cols = sql_event->res->fieldCount;
			if(num_cols != 5){
				printf("#9, num_cols is not 5\n");
				goto sqlerr;
			}
			ol_i_id = atoi(sql_event->res->fieldValues[0]);
			ol_supply_w_id = atoi(sql_event->res->fieldValues[1]);
			ol_quantity = atoi(sql_event->res->fieldValues[2]);
			ol_amount = atof(sql_event->res->fieldValues[3]);

			(void)ol_i_id;
			(void)ol_supply_w_id;
			(void)ol_quantity;
			(void)ol_amount;

			bytes = sql_event->res->fieldValues[4] == NULL ? 0 : strlen(sql_event->res->fieldValues[4]);
			if (bytes)
				strcpy(ol_delivery_d, sql_event->res->fieldValues[4]);

		}
		else{
			printf("#10, sql_event->sql_result->field_values is NULL\n");
			goto sqlerr;
		}
	}
	SQLStageEventReset(sql_event);

	// sqlite3_reset(sqlite_stmt);

	/*proceed = 9;
	EXEC_SQL OPEN c_items;

	EXEC SQL WHENEVER NOT FOUND GOTO done;*/
	return 1;
sqlerr:
	fprintf(stderr, "ordstat %d:%d\n",t_num,proceed);
	printf("%s: error: rc = %d\n", __func__, rc);
	//error(ctx[t_num],mysql_stmt);
        /*EXEC SQL WHENEVER SQLERROR GOTO sqlerrerr;*/
	/*EXEC_SQL ROLLBACK WORK;*/
	SQLStageEventReset(sql_event);
	transactionRollback(sql_event->txn, sql_event->db);
	sql_event->txn = transcationConstrcut(sql_event->db);

// sqlerrerr:
	return (0);
}

#endif