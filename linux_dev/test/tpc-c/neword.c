/*
 * neword.pc 
 * 对应附录 A 中的 A.1
 */

#include <stdio.h>
#include <string.h>
#include <time.h>

// #include <sqlite3.h>

#include "spt_proc.h"
#include "sql_event.h"
#include "tpc.h"
#include "transaction.h"
#include "trans_if.h"

#define pick_dist_info(ol_dist_info, ol_supply_w_id) \
switch(ol_supply_w_id) { \
case 1: strncpy(ol_dist_info, s_dist_01, 25); break; \
case 2: strncpy(ol_dist_info, s_dist_02, 25); break; \
case 3: strncpy(ol_dist_info, s_dist_03, 25); break; \
case 4: strncpy(ol_dist_info, s_dist_04, 25); break; \
case 5: strncpy(ol_dist_info, s_dist_05, 25); break; \
case 6: strncpy(ol_dist_info, s_dist_06, 25); break; \
case 7: strncpy(ol_dist_info, s_dist_07, 25); break; \
case 8: strncpy(ol_dist_info, s_dist_08, 25); break; \
case 9: strncpy(ol_dist_info, s_dist_09, 25); break; \
case 10: strncpy(ol_dist_info, s_dist_10, 25); break; \
}

extern GNCDB **ctx;
extern char ***stmt;

#define NNULL ((void *)0)
// extern char* tpccTimeCost;
// extern FILE *fp1;
/*
 * 新订单事务
 */
#ifdef PRINT_TRXS_COST_TIME

int neword(int t_num,
           int w_id_arg,       /* 仓库编号 */
           int d_id_arg,       /* 区域编号 */
           int c_id_arg,       /* 客户编号 */
           int o_ol_cnt_arg,   /* 商品数量 */
           int o_all_local_arg,/* 是否全部为本地订单 */
           int itemid[],       /* 订单商品编号 */
           int supware[],      /* 供应商品的仓库 */
           int qty[],          /* 每个商品的数量 */
           SQLStageEvent* sql_event)
{   
    FILE* fp1;
    fp1 = fopen("avgNewOrd", "a+");
    int w_id = w_id_arg;
    int d_id = d_id_arg;
    int c_id = c_id_arg;
    int o_ol_cnt = o_ol_cnt_arg;
    int o_all_local = o_all_local_arg;
    float c_discount;
    char c_last[17];
    char c_credit[3];
    float w_tax;
    int d_next_o_id;
    float d_tax;
    char datetime[81];
    int o_id;
    char i_name[25];
    float i_price;
    char i_data[51];
    int ol_i_id;
    int s_quantity;
    char s_data[51];
    char s_dist_01[25];
    char s_dist_02[25];
    char s_dist_03[25];
    char s_dist_04[25];
    char s_dist_05[25];
    char s_dist_06[25];
    char s_dist_07[25];
    char s_dist_08[25];
    char s_dist_09[25];
    char s_dist_10[25];
    char ol_dist_info[25];
    int ol_supply_w_id;
    float ol_amount;
    int ol_number;
    int ol_quantity;

    char iname[MAX_NUM_ITEMS][MAX_ITEM_LEN];
    char bg[MAX_NUM_ITEMS];
    float amt[MAX_NUM_ITEMS];
    float price[MAX_NUM_ITEMS];
    int stock[MAX_NUM_ITEMS];
    float total = 0.0;

    int min_num;
    int i, j, tmp, swp;
    int ol_num_seq[MAX_NUM_ITEMS];

    int proceed = 0;
    struct timespec tbuf1, tbuf_start;
    clock_t clk1, clk_start;

    int rc = GNCDB_SUCCESS;
    char sql_stmt[520];
    int num_cols;
    
    /* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr;*/
    /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/

    /*EXEC_SQL CONTEXT USE :ctx[t_num];*/

    gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);
    clk_start = clock_gettime(CLOCK_REALTIME, &tbuf_start );


    struct timespec st1, ed1;
    struct timespec st2, ed2;
    struct timespec st3, ed3;
    struct timespec st4, ed4;
    struct timespec st5, ed5;
    struct timespec st6, ed6;
    struct timespec st7, ed7;
    struct timespec st8, ed8;
    struct timespec st9, ed9;
    unsigned long long timeCost1 = 0;
    unsigned long long timeCost2 = 0;
    unsigned long long timeCost3 = 0;
    unsigned long long timeCost4 = 0;
    unsigned long long timeCost5 = 0;
    unsigned long long timeCost6 = 0;
    unsigned long long timeCost7 = 0;
    unsigned long long timeCost8 = 0;
    unsigned long long timeCost9 = 0;
    proceed = 1;
    /*EXEC_SQL SELECT c_discount, c_last, c_credit, w_tax
        INTO :c_discount, :c_last, :c_credit, :w_tax
            FROM customer, warehouse
            WHERE w_id = :w_id 
        AND c_w_id = w_id 
        AND c_d_id = :d_id 
        AND c_id = :c_id;*/
    SQLStageEventReset(sql_event);
    clock_gettime(CLOCK_REALTIME, &st1);
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "SELECT c_discount, c_last, c_credit, w_tax FROM customer INNER JOIN warehouse ON c_w_id = w_id WHERE w_id = %d AND c_d_id = %d AND c_id = %d;", w_id, d_id, c_id);

    rc = txnExecuteSQLStep(sql_event, sql_stmt);
    clock_gettime(CLOCK_REALTIME, &ed1);
    timeCost1 = ((ed1.tv_sec - st1.tv_sec) * 1000000000 +  (ed1.tv_nsec - st1.tv_nsec)) / 1000 ;
    fprintf(fp1, "%lld,", timeCost1);
    if(rc != GNCDB_SUCCESS){
        if(rc != GNCDB_ROW_DONE){
            printf("#proceed1, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        if(sql_event->res == NULL){
            printf("#proceed1, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        num_cols = sql_event->res->field_count;
        if(num_cols != 4){
            printf("#proceed1, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        c_discount = atof(sql_event->res->fieldValues[0]);
        strcpy(c_last, sql_event->res->fieldValues[1]);
        strcpy(c_credit, sql_event->res->fieldValues[2]);
        w_tax = atof(sql_event->res->fieldValues[3]);
    }
    SQLStageEventReset(sql_event);
    // clock_gettime(CLOCK_REALTIME, &ed1);
    // timeCost1 = ((ed1.tv_sec - st1.tv_sec) * 1000000000 +  (ed1.tv_nsec - st1.tv_nsec)) / 1000 ;
    // fprintf(fp1, "sum1 =  %lld\n", timeCost1);
    // fprintf(fp2, "%lld,", timeCost1);
#ifdef DEBUG
    // printf("n %d\n",proceed);
#endif

    proceed = 2;
    /*EXEC_SQL SELECT d_next_o_id, d_tax INTO :d_next_o_id, :d_tax
            FROM district
            WHERE d_id = :d_id
        AND d_w_id = :w_id
        FOR UPDATE;*/
    clock_gettime(CLOCK_REALTIME, &st2);
    // fprintf(fp1, "#neword2 SELECT district\n");
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "SELECT d_next_o_id, d_tax FROM district WHERE d_id = %d AND d_w_id = %d;", d_id, w_id);

    rc = txnExecuteSQLStep(sql_event, sql_stmt);
    clock_gettime(CLOCK_REALTIME, &ed2);
    timeCost2 = ((ed2.tv_sec - st2.tv_sec) * 1000000000 +  (ed2.tv_nsec - st2.tv_nsec)) / 1000;
    fprintf(fp1, "%lld,", timeCost2);
    if(rc != GNCDB_SUCCESS){
        if(rc != GNCDB_ROW_DONE){
            printf("#proceed2, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        if(sql_event->res == NULL){
            printf("#proceed2, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        num_cols = sql_event->res->field_count;
        if(num_cols != 2){
            printf("#proceed2, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        d_next_o_id = atoi(sql_event->res->fieldValues[0]);
        d_tax = atof(sql_event->res->fieldValues[1]);
    }
    SQLStageEventReset(sql_event);
    // clock_gettime(CLOCK_REALTIME, &ed2);
    // 取us值
    // timeCost2 =         ((ed2.tv_sec - st2.tv_sec) * 1000000000 +  (ed2.tv_nsec - st2.tv_nsec)) / 1000;
    // fprintf(fp1, "sum2 = %lld\n", timeCost2);
    // fprintf(fp2, "%lld,", timeCost2);

    proceed = 3;
    /*EXEC_SQL UPDATE district SET d_next_o_id = :d_next_o_id + 1
            WHERE d_id = :d_id 
        AND d_w_id = :w_id;*/
    // fprintf(fp1, "#neword3 UPDATE district\n");
    clock_gettime(CLOCK_REALTIME, &st3);
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "UPDATE district SET d_next_o_id = %d + 1 WHERE d_id = %d AND d_w_id = %d;", d_next_o_id, d_id, w_id);
    rc = txnExecuteSQL(sql_event, sql_stmt);
    clock_gettime(CLOCK_REALTIME, &ed3);
    timeCost3 = ((ed3.tv_sec - st3.tv_sec) * 1000000000 +  (ed3.tv_nsec - st3.tv_nsec)) / 1000;
    fprintf(fp1, "%lld,", timeCost3);

    if(rc != GNCDB_SUCCESS){
        printf("#proceed3, neword: error in txnExecuteSQL\n");
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);
    // clock_gettime(CLOCK_REALTIME, &ed3);
    // 取us值
    // timeCost3 =        ((ed3.tv_sec - st3.tv_sec) * 1000000000 +  (ed3.tv_nsec - st3.tv_nsec)) / 1000;
    // fprintf(fp1, "sum3 = %lld\n", timeCost3);
    // fprintf(fp2, "%lld,", timeCost3);

    o_id = d_next_o_id;

#ifdef DEBUG
    // printf("n %d\n",proceed);
#endif

    proceed = 4;
    /*EXEC_SQL INSERT INTO orders (o_id, o_d_id, o_w_id, o_c_id,
                             o_entry_d, o_ol_cnt, o_all_local)
        VALUES(:o_id, :d_id, :w_id, :c_id, 
               :datetime,
                       :o_ol_cnt, :o_all_local);*/
    // fprintf(fp1, "#neword4 INSERT orders\n");
    clock_gettime(CLOCK_REALTIME, &st4);
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "INSERT INTO orders (o_id, o_d_id, o_w_id, o_c_id, o_entry_d, o_ol_cnt, o_all_local) VALUES(%d, %d, %d, %d, '%s', %d, %d);", o_id, d_id, w_id, c_id, datetime, o_ol_cnt, o_all_local);
    rc = txnExecuteSQL(sql_event, sql_stmt);
    clock_gettime(CLOCK_REALTIME, &ed4);
    timeCost4 = ((ed4.tv_sec - st4.tv_sec) * 1000000000 +  (ed4.tv_nsec - st4.tv_nsec)) / 1000;
    fprintf(fp1, "%lld,", timeCost4);
    if(rc != GNCDB_SUCCESS){
        printf("#proceed4, neword: error in txnExecuteSQL\n");
        // SQLStageEventReset(sql_event);
        // sprintf(sql_stmt, "select * from orders where o_id = %d and o_d_id = %d and o_w_id = %d and o_c_id = %d and o_entry_d = '%s' and o_ol_cnt = %d and o_all_local = %d;", o_id, d_id, w_id, c_id, datetime, o_ol_cnt, o_all_local);
        // rc = txnExecuteSQL(sql_event, sql_stmt);
        // SQLStageEventReset(sql_event);
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);
    // clock_gettime(CLOCK_REALTIME, &ed4);
    // 取us值
    // timeCost4 =       ((ed4.tv_sec - st4.tv_sec) * 1000000000 +  (ed4.tv_nsec - st4.tv_nsec)) / 1000;
    // fprintf(fp1, "sum4 = %lld\n", timeCost4);
    // fprintf(fp2, "%lld,", timeCost4);
#ifdef DEBUG
    // printf("n %d\n",proceed);
#endif
    proceed = 5;
    /* EXEC_SQL INSERT INTO new_orders (no_o_id, no_d_id, no_w_id)
       VALUES (:o_id,:d_id,:w_id); */
    // fprintf(fp1, "#neword5 INSERT new_orders\n");
    clock_gettime(CLOCK_REALTIME, &st5);
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "INSERT INTO new_orders (no_o_id, no_d_id, no_w_id) VALUES (%d, %d, %d);", o_id, d_id, w_id);
    rc = txnExecuteSQL(sql_event, sql_stmt);
    clock_gettime(CLOCK_REALTIME, &ed5);
    timeCost5 = ((ed5.tv_sec - st5.tv_sec) * 1000000000 +  (ed5.tv_nsec - st5.tv_nsec)) / 1000;
    fprintf(fp1, "%lld,", timeCost5);
    if(rc != GNCDB_SUCCESS){
        printf("#proceed5, neword: error in txnExecuteSQL\n");
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);
    // clock_gettime(CLOCK_REALTIME, &ed5);
    //  取us值
    // timeCost5 =     ((ed5.tv_sec - st5.tv_sec) * 1000000000 +  (ed5.tv_nsec - st5.tv_nsec)) / 1000;
    // fprintf(fp1, "sum5 = %lld\n",timeCost5 );
    // fprintf(fp2, "%lld,", timeCost5);
    /* 对订单进行排序以避免死锁 */
    for (i = 0; i < o_ol_cnt; i++) {
        ol_num_seq[i]=i;
    }
    for (i = 0; i < (o_ol_cnt - 1); i++) {
        tmp = (MAXITEMS + 1) * supware[ol_num_seq[i]] + itemid[ol_num_seq[i]];
        min_num = i;
        for ( j = i+1; j < o_ol_cnt; j++) {
          if ( (MAXITEMS + 1) * supware[ol_num_seq[j]] + itemid[ol_num_seq[j]] < tmp ){
            tmp = (MAXITEMS + 1) * supware[ol_num_seq[j]] + itemid[ol_num_seq[j]];
            min_num = j;
          }
        }
        if ( min_num != i ){
          swp = ol_num_seq[min_num];
          ol_num_seq[min_num] = ol_num_seq[i];
          ol_num_seq[i] = swp;
        }
    }
    unsigned long long timeCostloop6 = 0;
    unsigned long long timeCostloop7 = 0;
    unsigned long long timeCostloop8 = 0;
    unsigned long long timeCostloop9 = 0;
    
    // fprintf(fp1, "\n循环次数%d\n", o_ol_cnt);
    int cnt = o_ol_cnt;
    for (ol_number = 1; ol_number <= o_ol_cnt; ol_number++) {
        ol_supply_w_id = supware[ol_num_seq[ol_number - 1]];
        ol_i_id = itemid[ol_num_seq[ol_number - 1]];
        ol_quantity = qty[ol_num_seq[ol_number - 1]];

        /* EXEC SQL WHENEVER NOT FOUND GOTO invaliditem; */
        proceed = 6;
        /*EXEC_SQL SELECT i_price, i_name, i_data
            INTO :i_price, :i_name, :i_data
                FROM item
                WHERE i_id = :ol_i_id;*/
        // fprintf(fp1, "#neword6 SELECT item\n");
        clock_gettime(CLOCK_REALTIME, &st6);
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "SELECT i_price, i_name, i_data FROM item WHERE i_id = %d;", ol_i_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if(rc != GNCDB_SUCCESS){
            if(rc != GNCDB_ROW_DONE){
                printf("#proceed6, neword: error in txnExecuteSQLStep\n");
                // SQLStageEventReset(sql_event);
                // rc = txnExecuteSQLStep(sql_event, sql_stmt);
                goto sqlerr;
            }
            if(sql_event->res == NULL){
                printf("#proceed6, neword: error in txnExecuteSQLStep\n");
                // SQLStageEventReset(sql_event);
                // rc = txnExecuteSQLStep(sql_event, sql_stmt);
                goto sqlerr;
            }
            num_cols = sql_event->res->field_count;
            if(num_cols != 3){
                printf("#proceed6, neword: error in txnExecuteSQLStep\n");
                // SQLStageEventReset(sql_event);
                // rc = txnExecuteSQLStep(sql_event, sql_stmt);
                goto sqlerr;
            }
            i_price = atof(sql_event->res->fieldValues[0]);
            strcpy(i_name, sql_event->res->fieldValues[1]);
            strcpy(i_data, sql_event->res->fieldValues[2]);
        }
        SQLStageEventReset(sql_event);
        clock_gettime(CLOCK_REALTIME, &ed6);
        //  取us值
        // fprintf(fp1, "sum6 = %ld\n", ((ed6.tv_sec - st6.tv_sec) * 1000000000 +  (ed6.tv_nsec - st6.tv_nsec)) / 1000);
        timeCostloop6 +=((ed6.tv_sec - st6.tv_sec) * 1000000000 +  (ed6.tv_nsec - st6.tv_nsec)) / 1000;


        price[ol_num_seq[ol_number - 1]] = i_price;
        strncpy(iname[ol_num_seq[ol_number - 1]], i_name, 25);

        /* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr; */

#ifdef DEBUG
        // printf("n %d\n",proceed);
#endif
        proceed = 7;

        /*EXEC_SQL SELECT s_quantity, s_data, s_dist_01, s_dist_02,
                        s_dist_03, s_dist_04, s_dist_05, s_dist_06,
                        s_dist_07, s_dist_08, s_dist_09, s_dist_10
            INTO :s_quantity, :s_data, :s_dist_01, :s_dist_02,
                     :s_dist_03, :s_dist_04, :s_dist_05, :s_dist_06,
                     :s_dist_07, :s_dist_08, :s_dist_09, :s_dist_10
                FROM stock
                WHERE s_i_id = :ol_i_id 
            AND s_w_id = :ol_supply_w_id
            FOR UPDATE;*/
        // fprintf(fp1, "#neword7 SELECT stock\n");
        clock_gettime(CLOCK_REALTIME, &st7);
        SQLStageEventReset(sql_event);
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = %d AND s_w_id = %d;", ol_i_id, ol_supply_w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if(rc != GNCDB_SUCCESS){
            if(sql_event->res == NULL){
                printf("#proceed7, neword: error in txnExecuteSQLStep\n");
                goto sqlerr;
            }
            if(sql_event->res == NULL){
                printf("#proceed7, neword: error in txnExecuteSQLStep\n");
                goto sqlerr;
            }
            num_cols = sql_event->res->field_count;
            if(num_cols != 12){
                printf("#proceed7, neword: error in txnExecuteSQLStep\n");
                goto sqlerr;
            }
            s_quantity = atoi(sql_event->res->fieldValues[0]);
            strcpy(s_data, sql_event->res->fieldValues[1]);
            strcpy(s_dist_01, sql_event->res->fieldValues[2]);
            strcpy(s_dist_02, sql_event->res->fieldValues[3]);
            strcpy(s_dist_03, sql_event->res->fieldValues[4]);
            strcpy(s_dist_04, sql_event->res->fieldValues[5]);
            strcpy(s_dist_05, sql_event->res->fieldValues[6]);
            strcpy(s_dist_06, sql_event->res->fieldValues[7]);
            strcpy(s_dist_07, sql_event->res->fieldValues[8]);
            strcpy(s_dist_08, sql_event->res->fieldValues[9]);
            strcpy(s_dist_09, sql_event->res->fieldValues[10]);
            strcpy(s_dist_10, sql_event->res->fieldValues[11]);
        }
        SQLStageEventReset(sql_event);
        clock_gettime(CLOCK_REALTIME, &ed7);
        // 取us值
        // fprintf(fp1, "sum7 = %ld\n", ((ed7.tv_sec - st7.tv_sec) * 1000000000 +  (ed7.tv_nsec - st7.tv_nsec)) / 1000);
        timeCostloop7 += ((ed7.tv_sec - st7.tv_sec) * 1000000000 +  (ed7.tv_nsec - st7.tv_nsec)) / 1000;


        pick_dist_info(ol_dist_info, d_id);  /* 选择正确的 s_dist_xx */

        stock[ol_num_seq[ol_number - 1]] = s_quantity;

        if ((strstr(i_data, "original") != NULL) &&
            (strstr(s_data, "original") != NULL))
            bg[ol_num_seq[ol_number - 1]] = 'B';
        else
            bg[ol_num_seq[ol_number - 1]] = 'G';

        if (s_quantity > ol_quantity)
            s_quantity = s_quantity - ol_quantity;
        else
            s_quantity = s_quantity - ol_quantity + 91;

#ifdef DEBUG
        // printf("n %d\n",proceed);
#endif

        proceed = 8;
        /*EXEC_SQL UPDATE stock SET s_quantity = :s_quantity
                WHERE s_i_id = :ol_i_id 
            AND s_w_id = :ol_supply_w_id;*/
        // fprintf(fp1, "#neword8 UPDATE stock\n");
        clock_gettime(CLOCK_REALTIME, &st8);
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "UPDATE stock SET s_quantity = %d WHERE s_i_id = %d AND s_w_id = %d;", s_quantity, ol_i_id, ol_supply_w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if(rc != GNCDB_SUCCESS){
            printf("#proceed8, neword: error in txnExecuteSQL\n");
            goto sqlerr;
        }
        SQLStageEventReset(sql_event);
        clock_gettime(CLOCK_REALTIME, &ed8);
        //      取us值
        // fprintf(fp1, "sum8 = %ld\n", ((ed8.tv_sec - st8.tv_sec) * 1000000000 +  (ed8.tv_nsec - st8.tv_nsec)) / 1000);
        timeCostloop8 += ((ed8.tv_sec - st8.tv_sec) * 1000000000 +  (ed8.tv_nsec - st8.tv_nsec)) / 1000;
        ol_amount = ol_quantity * i_price * (1 + w_tax + d_tax) * (1 - c_discount);
        amt[ol_num_seq[ol_number - 1]] = ol_amount;
        total += ol_amount;

#ifdef DEBUG
        // printf("n %d\n",proceed);
#endif

        proceed = 9;
        /*EXEC_SQL INSERT INTO order_line (ol_o_id, ol_d_id, ol_w_id, 
                         ol_number, ol_i_id, 
                         ol_supply_w_id, ol_quantity, 
                         ol_amount, ol_dist_info)
            VALUES (:o_id, :d_id, :w_id, :ol_number, :ol_i_id,
                :ol_supply_w_id, :ol_quantity, :ol_amount,
                :ol_dist_info);*/
        // fprintf(fp1, "#neword9 INSERT order_line\n");
        clock_gettime(CLOCK_REALTIME, &st9);
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "INSERT INTO order_line (ol_o_id, ol_d_id, ol_w_id, ol_number, ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_dist_info) VALUES (%d, %d, %d, %d, %d, %d, %d, %f, '%s');", o_id, d_id, w_id, ol_number, ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_dist_info);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if(rc != GNCDB_SUCCESS){
            printf("#proceed9, neword: error in txnExecuteSQL\n");
            goto sqlerr;
        }
        SQLStageEventReset(sql_event);
        clock_gettime(CLOCK_REALTIME, &ed9);
        // 取us值
        // fprintf(fp1, "sum9 = %ld\n", ((ed9.tv_sec - st9.tv_sec) * 1000000000 +  (ed9.tv_nsec - st9.tv_nsec)) / 1000);
        timeCostloop9 += ((ed9.tv_sec - st9.tv_sec) * 1000000000 +  (ed9.tv_nsec - st9.tv_nsec)) / 1000;
    }            /* 结束订单行 */

#ifdef DEBUG
    printf("insert 3\n");
    fflush(stdout);
#endif

    /*EXEC_SQL COMMIT WORK;*/
    //if( sqlite3_exec(ctx[t_num], "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;

    // 计算平均响应时间
    // fprintf(fp1, "%lld,%lld,%lld,%lld\n", timeCostloop6/o_ol_cnt, timeCostloop7/o_ol_cnt, timeCostloop8/o_ol_cnt, timeCostloop9/o_ol_cnt);
    fprintf(fp1, "%lld,%lld,%lld,%lld\n", timeCostloop6/cnt, timeCostloop7/cnt, timeCostloop8/cnt, timeCostloop9/cnt);
    clk1 = clock_gettime(CLOCK_REALTIME, &tbuf1 );

    fclose(fp1);
    return 1;

sqlerr:
    fprintf(stderr,"neword %d:%d\n",t_num,proceed);
    printf("%s: error: rc = %d\n", __func__, rc);
          //error(ctx[t_num],mysql_stmt);
    /*EXEC SQL WHENEVER SQLERROR GOTO sqlerrerr;*/
    /*EXEC_SQL ROLLBACK WORK;*/
    transactionRollback(sql_event->txn, sql_event->db);
sqlerrerr:
    return (0);

}


#else

int neword(int t_num,
           int w_id_arg,       /* 仓库编号 */
           int d_id_arg,       /* 区域编号 */
           int c_id_arg,       /* 客户编号 */
           int o_ol_cnt_arg,   /* 商品数量 */
           int o_all_local_arg,/* 是否全部为本地订单 */
           int itemid[],       /* 订单商品编号 */
           int supware[],      /* 供应商品的仓库 */
           int qty[],          /* 每个商品的数量 */
           SQLStageEvent* sql_event)
{   

    // 定义变量并初始化
    int w_id = w_id_arg; // 仓库编号
    int d_id = d_id_arg; // 区域编号
    int c_id = c_id_arg; // 客户编号
    int o_ol_cnt = o_ol_cnt_arg; // 商品数量
    int o_all_local = o_all_local_arg; // 是否全部为本地订单
    float c_discount; // 客户折扣
    char c_last[17]; // 客户姓氏
    char c_credit[3]; // 客户信用
    float w_tax; // 仓库税率
    int d_next_o_id = 0; // 区域下一个订单ID
    float d_tax; // 区域税率
    char datetime[81];
    int o_id; // 订单ID
    char i_name[25]; // 商品名称
    float i_price; // 商品价格
    char i_data[51]; // 商品数据
    int ol_i_id; // 订单商品ID
    int s_quantity; // 库存数量
    char s_data[51]; // 库存数据
    char s_dist_01[25]; // 库存分区1
    char s_dist_02[25]; // 库存分区2
    char s_dist_03[25]; // 库存分区3
    char s_dist_04[25]; // 库存分区4
    char s_dist_05[25]; // 库存分区5
    char s_dist_06[25]; // 库存分区6
    char s_dist_07[25]; // 库存分区7
    char s_dist_08[25]; // 库存分区8
    char s_dist_09[25]; // 库存分区9
    char s_dist_10[25]; // 库存分区10
    char ol_dist_info[25]; // 订单商品分区信息
    int ol_supply_w_id; // 订单商品供应仓库
    float ol_amount;
    int ol_number; // 订单商品编号
    int ol_quantity; // 订单商品数量

    // 定义数组用于存储商品信息
    // 定义一个二维字符数组，用于存储商品名称，每个商品名称的最大长度为MAX_ITEM_LEN，最多存储MAX_NUM_ITEMS个商品
    char iname[MAX_NUM_ITEMS][MAX_ITEM_LEN];
    // 定义一个字符数组，用于存储每个商品的背景信息，最多存储MAX_NUM_ITEMS个商品
    char bg[MAX_NUM_ITEMS];
    // 定义一个浮点数组，用于存储每个商品的数量，最多存储MAX_NUM_ITEMS个商品
    float amt[MAX_NUM_ITEMS];
    // 定义一个浮点数组，用于存储每个商品的价格，最多存储MAX_NUM_ITEMS个商品
    float price[MAX_NUM_ITEMS];
    // 定义一个整型数组，用于存储每个商品的库存数量，最多存储MAX_NUM_ITEMS个商品
    int stock[MAX_NUM_ITEMS];
    
    // 定义一个浮点变量，用于存储总金额，初始值为0.0
    float total = 0.0;

    // 定义一个整型变量，用于存储最小数量
    int min_num;
    // 定义两个整型变量i和j，用于循环计数，初始值均为0
    int i = 0, j = 0, tmp, swp;
    // 定义一个整型数组，用于存储订单号序列，最多存储MAX_NUM_ITEMS个订单号
    int ol_num_seq[MAX_NUM_ITEMS];

    // 定义一个整型变量，用于存储数据库操作返回码，初始值为GNCDB_SUCCESS
    int rc = GNCDB_SUCCESS;
    // 定义一个字符数组，用于存储SQL语句，最大长度为520
    char sql_stmt[520];
    // 定义一个整型变量，用于存储列数
    int num_cols;

    // 定义一个整型变量，用于标记是否继续执行，初始值为0
    int proceed = 0;
    // 定义两个timespec结构体变量，用于存储时间信息
    struct timespec tbuf1, tbuf_start;
    
    // 定义两个clock_t类型变量，用于存储时钟信息
    clock_t clk1, clk_start;
    // 定义一个文件指针变量，用于文件操作
    // FILE* fp1;
    // 定义一个整型变量，用于存储最大数量
    int max_num;
    
    (void)tbuf1;
    (void)stock;
    (void)max_num;
    (void)clk1;
    (void)clk_start;

    /* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr;*/
    /* EXEC SQL WHENEVER SQLERROR GOTO sqlerr;*/
    // 获取当前时间戳并存储在datetime变量中，格式由STRFTIME_FORMAT指定，长度不超过TIMESTAMP_LEN
    gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);
    // 获取当前实时时钟的时间，并存储在tbuf_start结构体中
    clk_start = clock_gettime(CLOCK_REALTIME, &tbuf_start );
    // 注释掉的SQL上下文使用语句，可能用于数据库操作的上下文设置
    /*EXEC_SQL CONTEXT USE :ctx[t_num];*/

    // 再次获取当前时间戳并存储在datetime变量中
    gettimestamp(datetime, STRFTIME_FORMAT, TIMESTAMP_LEN);

    // 设置proceed变量为1，表示可以继续进行后续操作
    proceed = 1;
    // 注释掉的SQL查询语句，用于从customer和warehouse表中获取特定客户的折扣、姓氏、信用和仓库税
    /*EXEC_SQL SELECT c_discount, c_last, c_credit, w_tax
        INTO :c_discount, :c_last, :c_credit, :w_tax
            FROM customer, warehouse
            WHERE w_id = :w_id 
        AND c_w_id = w_id 
        AND c_d_id = :d_id 
        AND c_id = :c_id;*/
    // 重置SQL事件，准备执行新的SQL语句
    SQLStageEventReset(sql_event);
    // 将sql_stmt数组清零，准备存储新的SQL语句
    memset(sql_stmt, 0, sizeof(sql_stmt));
    // 构造SQL查询语句，用于从warehouse和customer表中获取特定客户的折扣、姓氏、信用和仓库税
    sprintf(sql_stmt, "SELECT c_discount, c_last, c_credit, w_tax FROM warehouse, customer where w_id = c_w_id and w_id = %d AND c_d_id = %d AND c_id = %d;", w_id, d_id, c_id);

    {    
        // char con[128] = {0};
        // sprintf(con, "select * from customer where c_id = %d;", c_id);
        // printf("%s\n", sql_stmt);
        // initTpccFlag();
        // GNCDB_exec(sql_event->db, "select * from master;", tpccCallBack, NULL, NULL);
        // initTpccFlag();
        // GNCDB_exec(sql_event->db, "select * from warehouse;", tpccCallBack, NULL, NULL);
        // initTpccFlag();
        // GNCDB_exec(sql_event->db, con, tpccCallBack, NULL, NULL);
        // initTpccFlag();
        // GNCDB_exec(sql_event->db, "select * from customer;", tpccCallBack, NULL, NULL);
    }

    // 执行SQL查询语句，并获取返回码
    rc = txnExecuteSQLStep(sql_event, sql_stmt);
    // 检查返回码是否为GNCDB_SUCCESS，表示SQL执行成功
    if(rc == GNCDB_SUCCESS){
        // 注释掉的代码，用于检查是否还有更多行数据
        // if(rc != GNCDB_ROW_DONE){
        //     printf("#proceed1, neword: error in txnExecuteSQLStep\n");
        //     goto sqlerr;
        // }
        // 检查SQL事件的结果是否为空，如果为空则表示执行出错
        if(sql_event->res == NULL){
            printf("#proceed1, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        // 获取查询结果的列数，如果不为4则表示查询结果不符合预期
        num_cols = sql_event->res->fieldCount;
        if(num_cols != 4){
            printf("#proceed1, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        // 从查询结果中获取各字段的值，并存储在相应的变量中
        c_discount = atof(sql_event->res->fieldValues[0]);
        strcpy(c_last, sql_event->res->fieldValues[1]);
        strcpy(c_credit, sql_event->res->fieldValues[2]);
        w_tax = atof(sql_event->res->fieldValues[3]);
    }
    else{
        printf("#proceed1, neword: error in txnExecuteSQLStep\n");
        goto sqlerr;
    }
    // 重置SQL事件，准备执行新的SQL语句
    SQLStageEventReset(sql_event);
// #ifdef DEBUG
//     // printf("n %d\n",proceed);
// #endif

    proceed = 2;
    /*EXEC_SQL SELECT d_next_o_id, d_tax INTO :d_next_o_id, :d_tax
            FROM district
            WHERE d_id = :d_id
        AND d_w_id = :w_id
        FOR UPDATE;*/
    // fprintf(fp1, "#neword2 SELECT district\n");
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "SELECT d_next_o_id, d_tax FROM district WHERE d_id = %d AND d_w_id = %d;", d_id, w_id);

    rc = txnExecuteSQLStep(sql_event, sql_stmt);
    if(rc == GNCDB_SUCCESS){
        // if(rc != GNCDB_ROW_DONE){
        //     printf("#proceed2, neword: error in txnExecuteSQLStep\n");
        //     goto sqlerr;
        // }
        if(sql_event->res == NULL){
            printf("#proceed2, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        num_cols = sql_event->res->fieldCount;
        if(num_cols != 2){
            printf("#proceed2, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        d_next_o_id = atoi(sql_event->res->fieldValues[0]);
        d_tax = atof(sql_event->res->fieldValues[1]);
    }
    else{
        printf("#proceed2, neword: error in txnExecuteSQLStep\n");
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);

    proceed = 3;
    /*EXEC_SQL UPDATE district SET d_next_o_id = :d_next_o_id + 1
            WHERE d_id = :d_id 
        AND d_w_id = :w_id;*/
    // fprintf(fp1, "#neword3 UPDATE district\n");
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "UPDATE district SET d_next_o_id = %d + 1 WHERE d_id = %d AND d_w_id = %d;", d_next_o_id, d_id, w_id);
    rc = txnExecuteSQL(sql_event, sql_stmt);

    if(rc != GNCDB_SUCCESS){
        printf("#proceed3, neword: error in txnExecuteSQL\n");
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);
    o_id = d_next_o_id;

// #ifdef DEBUG
//     // printf("n %d\n",proceed);
// #endif

    proceed = 4;
    /*EXEC_SQL INSERT INTO orders (o_id, o_d_id, o_w_id, o_c_id,
                             o_entry_d, o_ol_cnt, o_all_local)
        VALUES(:o_id, :d_id, :w_id, :c_id, 
               :datetime,
                       :o_ol_cnt, :o_all_local);*/
    // fprintf(fp1, "#neword4 INSERT orders\n");
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "INSERT INTO orders (o_id, o_d_id, o_w_id, o_c_id, o_entry_d, o_ol_cnt, o_all_local) VALUES(%d, %d, %d, %d, '%s', %d, %d);", o_id, d_id, w_id, c_id, datetime, o_ol_cnt, o_all_local);
    rc = txnExecuteSQL(sql_event, sql_stmt);
    if(rc != GNCDB_SUCCESS){
        printf("#proceed4, neword: error in txnExecuteSQL\n");
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);
#ifdef DEBUG
    // printf("n %d\n",proceed);
#endif
    proceed = 5;
    /* EXEC_SQL INSERT INTO new_orders (no_o_id, no_d_id, no_w_id)
       VALUES (:o_id,:d_id,:w_id); */
    // fprintf(fp1, "#neword5 INSERT new_orders\n");
    memset(sql_stmt, 0, sizeof(sql_stmt));
    sprintf(sql_stmt, "INSERT INTO new_orders (no_o_id, no_d_id, no_w_id) VALUES (%d, %d, %d);", o_id, d_id, w_id);
    rc = txnExecuteSQL(sql_event, sql_stmt);
    if(rc != GNCDB_SUCCESS){
        printf("#proceed5, neword: error in txnExecuteSQL\n");
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);
    /* 对订单进行排序以避免死锁 */
    for (i = 0; i < MAX_NUM_ITEMS; i++) {
        ol_num_seq[i]=i;
    }
    for (i = 0; i < (o_ol_cnt - 1); i++) {
        tmp = (MAXITEMS + 1) * supware[ol_num_seq[i]] + itemid[ol_num_seq[i]];
        min_num = i;
        for ( j = i+1; j < o_ol_cnt; j++) {
          if ( (MAXITEMS + 1) * supware[ol_num_seq[j]] + itemid[ol_num_seq[j]] < tmp ){
            tmp = (MAXITEMS + 1) * supware[ol_num_seq[j]] + itemid[ol_num_seq[j]];
            min_num = j;
          }
        }
        if ( min_num != i ){
          swp = ol_num_seq[min_num];
          ol_num_seq[min_num] = ol_num_seq[i];
          ol_num_seq[i] = swp;
        }
    }
    
    for (ol_number = 1; ol_number <= o_ol_cnt; ol_number++) {
        ol_supply_w_id = supware[ol_num_seq[ol_number - 1]];
        ol_i_id = itemid[ol_num_seq[ol_number - 1]];
        ol_quantity = qty[ol_num_seq[ol_number - 1]];

        /* EXEC SQL WHENEVER NOT FOUND GOTO invaliditem; */
        proceed = 6;
        /*EXEC_SQL SELECT i_price, i_name, i_data
            INTO :i_price, :i_name, :i_data
                FROM item
                WHERE i_id = :ol_i_id;*/
        // fprintf(fp1, "#neword6 SELECT item\n");
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "SELECT i_price, i_name, i_data FROM item WHERE i_id = %d;", ol_i_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if(rc == GNCDB_SUCCESS){
            // if(rc != GNCDB_ROW_DONE){
            //     printf("#proceed6, neword: error in txnExecuteSQLStep\n");
            //     goto sqlerr;
            // }
            if(sql_event->res == NULL){
                goto sqlerr;
            }
            num_cols = sql_event->res->fieldCount;
            if(num_cols != 3){
                printf("#proceed6, neword: error in txnExecuteSQLStep\n");
                goto sqlerr;
            }
            i_price = atof(sql_event->res->fieldValues[0]);
            strcpy(i_name, sql_event->res->fieldValues[1]);
            strcpy(i_data, sql_event->res->fieldValues[2]);
        }
        else{
            printf("#proceed6, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        SQLStageEventReset(sql_event);
        price[ol_num_seq[ol_number - 1]] = i_price;
        (void)(price);
        strncpy(iname[ol_num_seq[ol_number - 1]], i_name, 25);

        /* EXEC SQL WHENEVER NOT FOUND GOTO sqlerr; */

#ifdef DEBUG
        // printf("n %d\n",proceed);
#endif
        proceed = 7;

        /*EXEC_SQL SELECT s_quantity, s_data, s_dist_01, s_dist_02,
                        s_dist_03, s_dist_04, s_dist_05, s_dist_06,
                        s_dist_07, s_dist_08, s_dist_09, s_dist_10
            INTO :s_quantity, :s_data, :s_dist_01, :s_dist_02,
                     :s_dist_03, :s_dist_04, :s_dist_05, :s_dist_06,
                     :s_dist_07, :s_dist_08, :s_dist_09, :s_dist_10
                FROM stock
                WHERE s_i_id = :ol_i_id 
            AND s_w_id = :ol_supply_w_id
            FOR UPDATE;*/
        // fprintf(fp1, "#neword7 SELECT stock\n");
        SQLStageEventReset(sql_event);
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "SELECT s_quantity, s_data, s_dist_01, s_dist_02, s_dist_03, s_dist_04, s_dist_05, s_dist_06, s_dist_07, s_dist_08, s_dist_09, s_dist_10 FROM stock WHERE s_i_id = %d AND s_w_id = %d;", ol_i_id, ol_supply_w_id);
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if(rc == GNCDB_SUCCESS){
            if(sql_event->res == NULL){
                printf("#proceed7, neword: error in txnExecuteSQLStep\n");
                goto sqlerr;
            }
            num_cols = sql_event->res->fieldCount;
            if(num_cols != 12){
                printf("#proceed7, neword: error in txnExecuteSQLStep\n");
                goto sqlerr;
            }
            s_quantity = atoi(sql_event->res->fieldValues[0]);
            strcpy(s_data, sql_event->res->fieldValues[1]);
            strcpy(s_dist_01, sql_event->res->fieldValues[2]);
            strcpy(s_dist_02, sql_event->res->fieldValues[3]);
            strcpy(s_dist_03, sql_event->res->fieldValues[4]);
            strcpy(s_dist_04, sql_event->res->fieldValues[5]);
            strcpy(s_dist_05, sql_event->res->fieldValues[6]);
            strcpy(s_dist_06, sql_event->res->fieldValues[7]);
            strcpy(s_dist_07, sql_event->res->fieldValues[8]);
            strcpy(s_dist_08, sql_event->res->fieldValues[9]);
            strcpy(s_dist_09, sql_event->res->fieldValues[10]);
            strcpy(s_dist_10, sql_event->res->fieldValues[11]);
        }
        else{
            printf("#proceed7, neword: error in txnExecuteSQLStep\n");
            goto sqlerr;
        }
        SQLStageEventReset(sql_event);

        pick_dist_info(ol_dist_info, d_id);  /* 选择正确的 s_dist_xx */

        stock[ol_num_seq[ol_number - 1]] = s_quantity;

        if ((strstr(i_data, "original") != NULL) &&
            (strstr(s_data, "original") != NULL))
            bg[ol_num_seq[ol_number - 1]] = 'B';
        else
            bg[ol_num_seq[ol_number - 1]] = 'G';

        (void)bg;
        if (s_quantity > ol_quantity)
            s_quantity = s_quantity - ol_quantity;
        else
            s_quantity = s_quantity - ol_quantity + 91;

#ifdef DEBUG
        // printf("n %d\n",proceed);
#endif
        
        proceed = 8;
        /*EXEC_SQL UPDATE stock SET s_quantity = :s_quantity
                WHERE s_i_id = :ol_i_id 
            AND s_w_id = :ol_supply_w_id;*/
        // fprintf(fp1, "#neword8 UPDATE stock\n");
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "UPDATE stock SET s_quantity = %d WHERE s_i_id = %d AND s_w_id = %d;", s_quantity, ol_i_id, ol_supply_w_id);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if(rc != GNCDB_SUCCESS){
            printf("#proceed8, neword: error in txnExecuteSQL\n");
            goto sqlerr;
        }
        SQLStageEventReset(sql_event);
        ol_amount = ol_quantity * i_price * (1 + w_tax + d_tax) * (1 - c_discount);
        amt[ol_num_seq[ol_number - 1]] = ol_amount;
        (void)amt;
        total += ol_amount;

#ifdef DEBUG
        // printf("n %d\n",proceed);
#endif

        proceed = 9;
        /*EXEC_SQL INSERT INTO order_line (ol_o_id, ol_d_id, ol_w_id, 
                         ol_number, ol_i_id, 
                         ol_supply_w_id, ol_quantity, 
                         ol_amount, ol_dist_info)
            VALUES (:o_id, :d_id, :w_id, :ol_number, :ol_i_id,
                :ol_supply_w_id, :ol_quantity, :ol_amount,
                :ol_dist_info);*/
        // fprintf(fp1, "#neword9 INSERT order_line\n");
        memset(sql_stmt, 0, sizeof(sql_stmt));
        sprintf(sql_stmt, "INSERT INTO order_line (ol_o_id, ol_d_id, ol_w_id, ol_number, ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_dist_info) VALUES (%d, %d, %d, %d, %d, %d, %d, %f, '%s');", o_id, d_id, w_id, ol_number, ol_i_id, ol_supply_w_id, ol_quantity, ol_amount, ol_dist_info);
        rc = txnExecuteSQL(sql_event, sql_stmt);
        if(rc != GNCDB_SUCCESS){
            printf("#proceed9, neword: error in txnExecuteSQL\n");
            goto sqlerr;
        }
        SQLStageEventReset(sql_event);
    }            /* 结束订单行 */

#ifdef DEBUG
    // printf("insert 3\n");
    fflush(stdout);
#endif

//     /*EXEC_SQL COMMIT WORK;*/
//     //if( sqlite3_exec(ctx[t_num], "COMMIT;", NULL, NULL, NULL) != SQLITE_OK) goto sqlerr;

//     clk1 = clock_gettime(CLOCK_REALTIME, &tbuf1 );

    return 1;

sqlerr:
    fprintf(stderr,"neword %d:%d\n",t_num,proceed);
    printf("%s: error: rc = %d\n", __func__, rc);
          //error(ctx[t_num],mysql_stmt);
    /*EXEC SQL WHENEVER SQLERROR GOTO sqlerrerr;*/
    /*EXEC_SQL ROLLBACK WORK;*/
    SQLStageEventReset(sql_event);
    transactionRollback(sql_event->txn, sql_event->db);
    sql_event->txn = transcationConstrcut(sql_event->db);
// sqlerrerr:
    return (0);

}

#endif