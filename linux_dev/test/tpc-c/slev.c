/*
 * slev.pc 
 * 对应附录 A 中的 A.5
 */

#include <string.h>
#include <stdio.h>

// #include <sqlite3.h>

// #include "common.h"
#include "gncdb.h"
#include "physical_operator.h"
#include "spt_proc.h"
#include "sql_event.h"
#include "tpc.h"
#include "transaction.h"

extern GNCDB **ctx;  // 外部全局数据库连接指针数组
extern char ***stmt; // 外部全局 SQL 语句数组

/*
 * 库存水平事务
 */
#ifdef PRINT_TRXS_COST_TIME

int slev(int t_num,
         int w_id_arg,      /* 仓库 ID */
         int d_id_arg,      /* 分区 ID */
         int level_arg,     /* 库存水平 */
         SQLStageEvent *sql_event)
{
    int rc = GNCDB_SUCCESS; // 返回码初始化
    int w_id = w_id_arg;    // 仓库 ID
    int d_id = d_id_arg;    // 分区 ID
    int level = level_arg;  // 库存水平
    int d_next_o_id;        // 下一个订单 ID
    int i_count;            // 计数器
    int ol_i_id;            // 订单行项目 ID

    char sql_stmt[520]; // SQL 语句字符串
    char sql_stmt2[520]; // 第二条 SQL 语句字符串
    int num_cols; // 结果列数
    FILE* fp5;
    fp5 = fopen("avgSlev", "a");

    struct timespec start, end; // 计时器
    SQLStageEventReset(sql_event);

    clock_gettime(CLOCK_MONOTONIC, &start); // 计时开始
    // 找到下一个订单号
    sprintf(sql_stmt, "SELECT d_next_o_id FROM district WHERE d_id = %d AND d_w_id = %d;", d_id, w_id);
    rc = txnExecuteSQLStep(sql_event, sql_stmt);
    if (rc != GNCDB_SUCCESS)
    {
        if (sql_event->sql_result->field_values == NULL)
        {
            goto sqlerr;
        }
        num_cols = sql_event->sql_result->field_count;
        if (num_cols != 1)
        {
            goto sqlerr;
        }
        d_next_o_id = atoi(sql_event->sql_result->field_values[0]);
    }
    clock_gettime(CLOCK_MONOTONIC, &end); // 计时结束
    fprintf(fp5, "%lld,", (long long)((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec)) /1000);
    SQLStageEventReset(sql_event);

    SQLStageEvent* sql_event2 = (SQLStageEvent*)my_malloc(sizeof(SQLStageEvent));
    sql_event2->affected_rows = 0;
    sql_event2->tuple = NULL;
    sql_event2->sql_node = NULL;
    sql_event2->physical_operator = NULL;
    sql_event2->stmt = NULL;
    sql_event2->callback = sql_event->callback;
    sql_event2->db = sql_event->db;
    sql_event2->is_step = false;
    sql_event2->txn = sql_event->txn;
    sql_event2->affected_rows = 0;
    sql_event2->sub_query_level = 0;
    sql_event2->sql_result = NULL;
    sql_event2->is_open = false;
    SQLStageEventReset(sql_event2);
    unsigned long long costTime1 = 0;
    unsigned long long costTime2 = 0;
    int cnt = 0;
    // 查找该分区最近的 20 个订单
    sprintf(sql_stmt, "SELECT DISTINCT ol_i_id FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d  AND ol_o_id >= %d AND ol_o_id < %d;", w_id, d_id, d_next_o_id - 20, d_next_o_id);
    while (true)
    {   
        clock_gettime(CLOCK_MONOTONIC, &start); // 计时开始
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if(rc == GNCDB_SUCCESS){
            break;
        }
        clock_gettime(CLOCK_MONOTONIC, &end); // 计时结束
        costTime1 += ((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec)) /1000.0;
        // printf("slev#1, rc = %d\n", rc);
        num_cols = sql_event->sql_result->field_count;
        if (num_cols != 1)
        {
            goto sqlerr;
        }
        ol_i_id = atoi(sql_event->sql_result->field_values[0]);

        SQLStageEventReset(sql_event2);
        memset(sql_stmt2, 0, 520);
        clock_gettime(CLOCK_MONOTONIC, &start); // 计时开始
        // sprintf(sql_stmt2, "SELECT * FROM stock WHERE s_w_id = %d AND s_i_id = %d AND s_quantity < %d;", w_id, ol_i_id, level);
        sprintf(sql_stmt2, "SELECT count(*) FROM stock WHERE s_w_id = %d AND s_i_id = %d AND s_quantity < %d;", w_id, ol_i_id, level);
        rc = txnExecuteSQLStep(sql_event2, sql_stmt2);
        clock_gettime(CLOCK_MONOTONIC, &end); // 计时结束
        costTime2 +=  ((end.tv_sec - start.tv_sec) * 1000000000 + (end.tv_nsec - start.tv_nsec)) /1000.0;
        if (rc != GNCDB_SUCCESS)
        {   
            if (sql_event2->sql_result->field_values == NULL)
            {
                goto sqlerr;
            }
            num_cols = sql_event2->sql_result->field_count;
            if (num_cols != 1)
            {
                goto sqlerr;
            }
            i_count = atoi(sql_event2->sql_result->field_values[0]);
        }
        else{
            i_count = 0;
        }
        cnt++;
        SQLStageEventReset(sql_event2);
    }

    // printf("rc = %d\n", rc);
    SQLStageEventReset(sql_event);
    fprintf(fp5, "%lld,", costTime1 /(cnt+1) );
    fprintf(fp5, "%lld\n", costTime2 /(cnt) );
    fclose(fp5);

    return (1);

sqlerr:
    fprintf(stderr, "slev\n"); // 输出错误信息
    printf("slev: rc = %d\n", rc); // 输出错误信息
    transactionRollback(sql_event->txn, sql_event->db); // 事务回滚
    return (0);
}

#else

int slev(int t_num,
         int w_id_arg,      /* 仓库 ID */
         int d_id_arg,      /* 分区 ID */
         int level_arg,     /* 库存水平 */
         SQLStageEvent *sql_event)
{
    int rc = GNCDB_SUCCESS; // 返回码初始化
    int w_id = w_id_arg;    // 仓库 ID
    int d_id = d_id_arg;    // 分区 ID
    int level = level_arg;  // 库存水平
    int d_next_o_id = 0;        // 下一个订单 ID
    // int i_count;            // 计数器
    int ol_i_id;            // 订单行项目 ID

    char sql_stmt[520]; // SQL 语句字符串
    char sql_stmt2[520]; // 第二条 SQL 语句字符串
    int num_cols; // 结果列数
    SQLStageEvent* sql_event2 = NULL;
    int cnt = 0;


    SQLStageEventReset(sql_event);

    // 找到下一个订单号
    sprintf(sql_stmt, "SELECT d_next_o_id FROM district WHERE d_id = %d AND d_w_id = %d;", d_id, w_id);
    rc = txnExecuteSQLStep(sql_event, sql_stmt);
    if (rc == GNCDB_SUCCESS)
    {
        if (sql_event->res->fieldValues == NULL)
        {
            goto sqlerr;
        }
        num_cols = sql_event->res->fieldCount;
        if (num_cols != 1)
        {
            goto sqlerr;
        }
        d_next_o_id = atoi(sql_event->res->fieldValues[0]);
    }
    else{
        printf("slev#1, rc = %d\n", rc);
        goto sqlerr;
    }
    SQLStageEventReset(sql_event);

    sql_event2 = (SQLStageEvent*)my_malloc0(sizeof(SQLStageEvent));
    SQLStageEventInit(sql_event2);
    sql_event2->affectedRows = 0;
    // sql_event2->sqlNode = NULL;
    // sql_event2->plan = NULL;
    // sql_event2->stmt = NULL;
    sql_event2->callback = sql_event->callback;
    sql_event2->db = sql_event->db;
    // sql_event2->isStep = false;
    sql_event2->txn = sql_event->txn;
    // sql_event2->affectedRows = 0;
    // sql_event2->subQueryLevel = 0;
    // sql_event2->res = NULL;
    // sql_event2->isOpen = false;
    SQLStageEventReset(sql_event2);
    // 查找该分区最近的 20 个订单
    // sprintf(sql_stmt, "SELECT ol_d_id FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d;", w_id, d_id);
    // printf("slev: %s\n", sql_stmt);

    // rc = txnExecuteSQL(sql_event2, sql_stmt);
    // if (rc != GNCDB_SUCCESS)
    // {
    //     printf("slev#1, rc = %d\n", rc);
    //     goto sqlerr;
    // }
    // SQLStageEventReset(sql_event2);

    // sprintf(sql_stmt, "SELECT ol_d_id FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d AND ol_o_id >= %d AND ol_o_id < %d;", w_id, d_id, d_next_o_id - 20, d_next_o_id);
    sprintf(sql_stmt, "SELECT  ol_i_id FROM order_line WHERE ol_w_id = %d AND ol_d_id = %d  AND ol_o_id >= %d AND ol_o_id < %d;", w_id, d_id, d_next_o_id - 20, d_next_o_id);
    while (true)
    {   
        rc = txnExecuteSQLStep(sql_event, sql_stmt);
        if(rc == GNCDB_SUCCESS && sql_event->affectedRows == 0){
            break;
        }
        num_cols = sql_event->res->fieldCount;
        if (num_cols != 1)
        {
            goto sqlerr;
        }
        ol_i_id = atoi(sql_event->res->fieldValues[0]);

        SQLStageEventReset(sql_event2);
        memset(sql_stmt2, 0, 520);
        sprintf(sql_stmt2, "SELECT count(*) FROM stock WHERE s_w_id = %d AND s_i_id = %d AND s_quantity < %d;", w_id, ol_i_id, level);
        rc = txnExecuteSQLStep(sql_event2, sql_stmt2);
        if (rc == GNCDB_SUCCESS)
        {   
            if (sql_event2->res->fieldValues == NULL)
            {
                goto sqlerr;
            }
            num_cols = sql_event2->res->fieldCount;
            if (num_cols != 1)
            {
                goto sqlerr;
            }
        }
        else{
            goto sqlerr;
        }
        cnt++;
        SQLStageEventReset(sql_event2);
    }

    // printf("rc = %d\n", rc);
    SQLStageEventReset(sql_event);

    return (1);

sqlerr:
    fprintf(stderr, "slev\n"); // 输出错误信息
    printf("slev: rc = %d\n", rc); // 输出错误信息
    SQLStageEventReset(sql_event);
    transactionRollback(sql_event->txn, sql_event->db); // 事务回滚
    sql_event->txn = transcationConstrcut(sql_event->db);
    return (0);
}
#endif
