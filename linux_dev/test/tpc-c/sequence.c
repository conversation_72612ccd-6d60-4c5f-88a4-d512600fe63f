/*
 * sequence.c
 * manage sequence shared by threads
 */

#include <stdio.h>
#include <stdlib.h>
#include <pthread.h>

#include "gncdbconstant.h"

/* weight */
static int no;
static int py;
static int os;
static int dl;
static int sl;
static int total;

static pthread_mutex_t mutex;
static int *seq;
static int next_num;

static void shuffle()
{
  int i,j,rnd,tmp;

  for( i=0, j=0; i < no ; i++, j++ ){
    seq[j]=0;
  }
  for( i=0; i < py ; i++, j++){
    seq[j]=1;
  }
  for( i=0; i < os ; i++, j++){
    seq[j]=2;
  }
  for( i=0; i < dl ; i++, j++){
    seq[j]=3;
  }
  for( i=0; i < sl ; i++, j++){
    seq[j]=4;
  }
  for( i=0, j = total - 1; j>0; i++, j--){
    rnd = rand()%(j+1);
    tmp = seq[rnd+i];
    seq[rnd+i] = seq[i];
    seq[i] = tmp;
  }
}

void seq_init( int n, int p, int o, int d, int s )
{
  pthread_mutex_init( &mutex, NULL );
  no = n;
  py = p;
  os = o;
  dl = d;
  sl = s;
  total = n + p + o + d + s;
  seq = my_malloc(sizeof(int) * total );
  shuffle();
  next_num = 0;
}


/**
 * @brief 多线程环境中用于获取序列号的函数
 * 
 * @return int 
 */
int seq_get()
{
  int retval; // 用于存储返回值的变量

  pthread_mutex_lock( &mutex ); // 锁定互斥锁以保护共享资源

  // 如果下一个序列号等于或超过总数，重新洗牌并重置序列号
  if(next_num >= total){
    shuffle(); // 洗牌函数，重新生成序列号
    next_num = 0; // 重置序列号计数器
  }

  retval = seq[next_num]; // 从序列数组中获取当前序列号
  ++next_num; // 序列号计数器递增

  pthread_mutex_unlock( &mutex ); // 解锁互斥锁

  return(retval); // 返回序列号
}

