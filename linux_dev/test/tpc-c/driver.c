/*
 * driver.c
 * 用于 tpcc 事务的驱动程序
 */

#include "rthist.h"
#include "sb_percentile.h"
#include "sequence.h"
#include "sql_event.h"
#include "tpc.h"      /* 杂项函数的原型 */
#include "trans_if.h" /* 事务接口调用的原型 */
// #include <sqlite3.h>
#include <stdio.h>
#include <sys/times.h>
#include <time.h>
#include "timers.h"

// 声明其他文件中定义的全局变量和函数
static int other_ware(int home_ware);
static int do_neword(int t_num, SQLStageEvent *sql_event);
static int do_payment(int t_num, SQLStageEvent *sql_event);
static int do_ordstat(int t_num, SQLStageEvent *sql_event);
static int do_delivery(int t_num, SQLStageEvent *sql_event);
static int do_slev(int t_num, SQLStageEvent *sql_event);

extern GNCDB **ctx; // 数据库连接上下文数组
extern int num_ware; // 仓库数量
extern int num_conn; // 连接数量
extern int activate_transaction; // 事务激活标志
extern int counting_on; // 计数开关
extern int time_start; // 计时器开始时间
extern int time_end; // 计时器结束时间
extern int num_trans; // 事务数量

extern int num_node; // 节点数量
extern int time_count; // 时间计数器
extern FILE *freport_file; // 报告文件

extern int success[]; // 成功事务计数数组
extern int late[]; // 迟到事务计数数组
extern int retry[]; // 重试事务计数数组
extern int failure[]; // 失败事务计数数组

extern int *success2[]; // 成功事务计数二维数组
extern int *late2[]; // 迟到事务计数二维数组
extern int *retry2[]; // 重试事务计数二维数组
extern int *failure2[]; // 失败事务计数二维数组

extern double max_rt[]; // 最大响应时间数组
extern double total_rt[]; // 总响应时间数组

extern int rt_limit[]; // 响应时间限制数组

extern long clk_tck; // 时钟滴答数
extern sb_percentile_t local_percentile; // 本地百分位结构

#define MAX_RETRY 10 // 最大重试次数

// 事务驱动函数
int driver(int t_num, SQLStageEvent *sql_event) {
  // int i, j;
  instrumentation_type neword_time, payment_time, ordstat_time, delivery_time,
      slev_time;

  switch (seq_get()) {
  case 0:
    // 计时开始
    START_TIMING(neword_t, neword_time);
    // 执行新订单事务
    do_neword(t_num, sql_event);
    // 计时结束
    END_TIMING(neword_t, neword_time);
    break;
  case 1:
    // 计时开始
    START_TIMING(payment_t, payment_time);
    // 执行支付事务
    do_payment(t_num, sql_event);
    // 计时结束
    END_TIMING(payment_t, payment_time);
    break;
  case 2:
    // 计时开始
    START_TIMING(ordstat_t, ordstat_time);
    // 执行订单状态事务
    do_ordstat(t_num, sql_event);
    // 计时结束
    END_TIMING(ordstat_t, ordstat_time);
    break;
  case 3:
    // 计时开始
    START_TIMING(delivery_t, delivery_time);
    // 执行交付事务
    do_delivery(t_num, sql_event);
    // 计时结束
    END_TIMING(delivery_t, delivery_time);
    break;
  case 4:
    // 计时开始
    START_TIMING(slev_t, slev_time);
    // 执行库存水平事务
    do_slev(t_num, sql_event);
    // 计时结束
    END_TIMING(slev_t, slev_time);
    break;
  default:
    printf("Error - 未知序列。\n");
  }

  return (0);
}


/*
 * 准备数据并执行一个新订单事务
 * 官方上，这应该是模拟终端I/O
 */
static int do_neword(int t_num, SQLStageEvent *sql_event) {
  // 定义变量
  int c_num;                    // 客户编号
  int i, ret;                   // 循环计数器和返回值
  clock_t clk1, clk2;           // 时钟变量
  double rt;                    // 响应时间
  struct timespec tbuf1;        // 时间结构体
  struct timespec tbuf2;        // 时间结构体
  int w_id, d_id, c_id, ol_cnt; // 仓库ID，地区ID，客户ID，订单行数
  int all_local = 1;            // 所有本地标志
  int notfound = MAXITEMS + 1;  /* 有效的商品ID连续编号
                                    [1..MAXITEMS] */
  int rbk;                      // 回滚标志
  int itemid[MAX_NUM_ITEMS];    // 商品ID数组
  int supware[MAX_NUM_ITEMS];   // 供应商仓库数组
  int qty[MAX_NUM_ITEMS];       // 数量数组
  // init itemid、supware
  for (i = 0; i < MAX_NUM_ITEMS; i++) {
    itemid[i] = 0;
    supware[i] = 0;
    qty[i] = 0;
  }

  // 根据节点数决定仓库ID
  if (num_node == 0) {
    w_id = RandomNumber(1, num_ware);
  } else {
    c_num = ((num_node * t_num) / num_conn); // 计算客户编号
    w_id = RandomNumber(1 + (num_ware * c_num) / num_node,
                        (num_ware * (c_num + 1)) / num_node);
  }
  // 随机生成地区ID和客户ID
  d_id = RandomNumber(1, DIST_PER_WARE);
  c_id = NURand(1023, 1, CUST_PER_DIST);

  // 随机生成订单行数和回滚标志
  ol_cnt = RandomNumber(5, 15);
  rbk = RandomNumber(1, 100);

  // 生成订单详情
  for (i = 0; i < ol_cnt; i++) {
    itemid[i] = NURand(8191, 1, MAXITEMS);
    if ((i == ol_cnt - 1) && (rbk == 101)) {
      itemid[i] = notfound; // 最后一项可能是未找到的商品
    }
    if (RandomNumber(1, 100) != 1) {
      supware[i] = w_id; // 通常情况下供应商仓库与订单仓库相同
    } else {
      supware[i] = other_ware(w_id); // 有时供应商仓库不同
      all_local = 0;                 // 设置非本地标志
    }
    qty[i] = RandomNumber(1, 10); // 随机生成数量
  }

  // for (i = 0; i < MAX_NUM_ITEMS; i++) {
  //   printf("itemid[%d] = %d\n", i, itemid[i]);
  // }
  // for (i = 0; i < MAX_NUM_ITEMS; i++) {
  //   printf("supware[%d] = %d\n", i, supware[i]);
  // }

  // 获取开始时间
  clk1 = clock_gettime(CLOCK_MONOTONIC, &tbuf1);
  // 尝试执行订单直到成功或达到最大重试次数
  for (i = 0; i < 1; i++) {
    ret = neword(t_num, w_id, d_id, c_id, ol_cnt, all_local, itemid, supware,
                 qty, sql_event);
    // 获取结束时间
    clk2 = clock_gettime(CLOCK_MONOTONIC, &tbuf2);

    // 如果成功执行订单
    if (ret) {

      rt = (double)(tbuf2.tv_sec * 1000.0 + tbuf2.tv_nsec / 1000000.0 -
                    tbuf1.tv_sec * 1000.0 - tbuf1.tv_nsec / 1000000.0);
      // 计算响应时间并更新统计数据
      if (rt > max_rt[0])
        max_rt[0] = rt;
      total_rt[0] += rt;
      sb_percentile_update(&local_percentile, rt);
      hist_inc(0, rt);
      if (counting_on) {
        if (rt < rt_limit[0]) {
          success[0]++;
          success2[0][t_num]++;
        } else {
          late[0]++;
          late2[0][t_num]++;
        }
      }

      return (1); // 结束函数
    } else {
      // 如果执行失败，更新重试统计
      if (counting_on) {
        retry[0]++;
        retry2[0][t_num]++;
      }
    }
  }

  // 更新失败统计
  if (counting_on) {
    retry[0]--;
    retry2[0][t_num]--;
    failure[0]++;
    failure2[0][t_num]++;
  }

  (void)clk1;
  (void)clk2;
  return (0); // 返回失败
}

/*
 * 生成除 home_ware 外的有效仓库编号
 * （假设除当前仓库外还存在其他仓库）
 */
static int other_ware(int home_ware) {
  int tmp;

  if (num_ware == 1)
    return home_ware;
  // 如果仓库数量为1，则返回当前仓库编号
  while ((tmp = RandomNumber(1, num_ware)) == home_ware)
    ;
  // 随机选择一个不是当前仓库的仓库编号
  return tmp;
}

/*
 * 准备数据并执行支付事务
 */
static int do_payment(int t_num, SQLStageEvent *sql_event) {
  int c_num;
  int byname, i, ret;
  clock_t clk1, clk2;
  double rt;
  struct timespec tbuf1;
  struct timespec tbuf2;
  int w_id, d_id, c_w_id, c_d_id, c_id, h_amount;
  char c_last[17];

  // 随机选择一个仓库编号
  if (num_node == 0) {
    w_id = RandomNumber(1, num_ware);
  } else {
    c_num = ((num_node * t_num) / num_conn); /* 将余数去掉 */
    w_id = RandomNumber(1 + (num_ware * c_num) / num_node,
                        (num_ware * (c_num + 1)) / num_node);
  }
  // 随机选择一个地区编号
  d_id = RandomNumber(1, DIST_PER_WARE);
  // 随机选择一个客户编号
  c_id = NURand(1023, 1, CUST_PER_DIST);
  // 生成客户姓氏
  Lastname(NURand(255, 0, 999), c_last);
  // 随机生成支付金额
  h_amount = RandomNumber(1, 5000);
  // 根据规定比例决定是否通过姓氏选择客户
  if (RandomNumber(1, 100) <= 60) {
    byname = 1; /* 通过姓氏选择 */
  } else {
    byname = 0; /* 通过客户编号选择 */
  }
  // 根据规定比例决定是否在同一仓库内选择客户
  if (RandomNumber(1, 100) <= 85) {
    c_w_id = w_id;
    c_d_id = d_id;
  } else {
    c_w_id = other_ware(w_id);  // 选择另一个仓库
    c_d_id = RandomNumber(1, DIST_PER_WARE); // 选择该仓库的一个地区
  }

  clk1 = clock_gettime(CLOCK_MONOTONIC, &tbuf1);
  for (i = 0; i < MAX_RETRY; i++) {
    ret = payment(t_num, w_id, d_id, byname, c_w_id, c_d_id, c_id, c_last,
                  h_amount, sql_event);  // 执行支付事务
    clk2 = clock_gettime(CLOCK_MONOTONIC, &tbuf2);

    if (ret) {

      rt = (double)(tbuf2.tv_sec * 1000.0 + tbuf2.tv_nsec / 1000000.0 -
                    tbuf1.tv_sec * 1000.0 - tbuf1.tv_nsec / 1000000.0);
      if (rt > max_rt[1])
        max_rt[1] = rt;
      total_rt[1] += rt;
      hist_inc(1, rt);
      if (counting_on) {
        if (rt < rt_limit[1]) {
          success[1]++;
          success2[1][t_num]++;
        } else {
          late[1]++;
          late2[1][t_num]++;
        }
      }

      return (1); /* 结束 */
    } else {

      if (counting_on) {
        retry[1]++;
        retry2[1][t_num]++;
      }
    }
  }

  if (counting_on) {
    retry[1]--;
    retry2[1][t_num]--;
    failure[1]++;
    failure2[1][t_num]++;
  }

  (void)clk1;
  (void)clk2;
  return (0);
}

/*
 * 准备数据并执行订单状态事务
 */
static int do_ordstat(int t_num, SQLStageEvent *sql_event) {
  int c_num;
  int byname, i, ret;
  clock_t clk1, clk2;
  double rt;
  struct timespec tbuf1;
  struct timespec tbuf2;
  int w_id, d_id, c_id;
  char c_last[16];

  // 随机选择一个仓库编号
  if (num_node == 0) {
    w_id = RandomNumber(1, num_ware);
  } else {
    c_num = ((num_node * t_num) / num_conn); /* 将余数去掉 */
    w_id = RandomNumber(1 + (num_ware * c_num) / num_node,
                        (num_ware * (c_num + 1)) / num_node);
  }
  // 随机选择一个地区编号
  d_id = RandomNumber(1, DIST_PER_WARE);
  // 随机选择一个客户编号
  c_id = NURand(1023, 1, CUST_PER_DIST);
  // 生成客户姓氏
  Lastname(NURand(255, 0, 999), c_last);
  // 根据规定比例决定是否通过姓氏选择客户
  if (RandomNumber(1, 100) <= 60) {
    byname = 1; /* 通过姓氏选择 */
  } else {
    byname = 0; /* 通过客户编号选择 */
  }

  clk1 = clock_gettime(CLOCK_MONOTONIC, &tbuf1);
  for (i = 0; i < MAX_RETRY; i++) {
    ret = ordstat(t_num, w_id, d_id, byname, c_id, c_last, sql_event); // 执行订单状态事务
    clk2 = clock_gettime(CLOCK_MONOTONIC, &tbuf2);

    if (ret) {

      rt = (double)(tbuf2.tv_sec * 1000.0 + tbuf2.tv_nsec / 1000000.0 -
                    tbuf1.tv_sec * 1000.0 - tbuf1.tv_nsec / 1000000.0);
      if (rt > max_rt[2])
        max_rt[2] = rt;
      total_rt[2] += rt;
      hist_inc(2, rt);
      if (counting_on) {
        if (rt < rt_limit[2]) {
          success[2]++;
          success2[2][t_num]++;
        } else {
          late[2]++;
          late2[2][t_num]++;
        }
      }

      return (1); /* 结束 */
    } else {

      if (counting_on) {
        retry[2]++;
        retry2[2][t_num]++;
      }
    }
  }

  if (counting_on) {
    retry[2]--;
    retry2[2][t_num]--;
    failure[2]++;
    failure2[2][t_num]++;
  }

  (void)clk1;
  (void)clk2;
  return (0);
}

/*
 * 执行交付事务
 */
static int do_delivery(int t_num, SQLStageEvent *sql_event) {
  int c_num;
  int i, ret;
  clock_t clk1, clk2;
  double rt;
  struct timespec tbuf1;
  struct timespec tbuf2;
  int w_id, o_carrier_id;

  // 随机选择一个仓库编号
  if (num_node == 0) {
    w_id = RandomNumber(1, num_ware);
  } else {
    c_num = ((num_node * t_num) / num_conn); /* 将余数去掉 */
    w_id = RandomNumber(1 + (num_ware * c_num) / num_node,
                        (num_ware * (c_num + 1)) / num_node);
  }
  // 随机选择一个承运人编号
  o_carrier_id = RandomNumber(1, 10);

  clk1 = clock_gettime(CLOCK_MONOTONIC, &tbuf1);
  for (i = 0; i < MAX_RETRY; i++) {
    ret = delivery(t_num, w_id, o_carrier_id, sql_event); // 执行交付事务
    clk2 = clock_gettime(CLOCK_MONOTONIC, &tbuf2);

    if (ret) {

      rt = (double)(tbuf2.tv_sec * 1000.0 + tbuf2.tv_nsec / 1000000.0 -
                    tbuf1.tv_sec * 1000.0 - tbuf1.tv_nsec / 1000000.0);
      if (rt > max_rt[3])
        max_rt[3] = rt;
      total_rt[3] += rt;
      hist_inc(3, rt);
      if (counting_on) {
        if (rt < rt_limit[3]) {
          success[3]++;
          success2[3][t_num]++;
        } else {
          late[3]++;
          late2[3][t_num]++;
        }
      }

      return (1); /* 结束 */
    } else {

      if (counting_on) {
        retry[3]++;
        retry2[3][t_num]++;
      }
    }
  }

  if (counting_on) {
    retry[3]--;
    retry2[3][t_num]--;
    failure[3]++;
    failure2[3][t_num]++;
  }

  (void)clk1;
  (void)clk2;
  return (0);
}

/*
 * 准备数据并执行库存水平事务
 */
static int do_slev(int t_num, SQLStageEvent *sql_event) {
  int c_num;
  int i, ret;
  clock_t clk1, clk2;
  double rt;
  struct timespec tbuf1;
  struct timespec tbuf2;
  int w_id, d_id, level;

  // 随机选择一个仓库编号
  if (num_node == 0) {
    w_id = RandomNumber(1, num_ware);
  } else {
    c_num = ((num_node * t_num) / num_conn); /* 将余数去掉 */
    w_id = RandomNumber(1 + (num_ware * c_num) / num_node,
                        (num_ware * (c_num + 1)) / num_node);
  }
  // 随机选择一个地区编号
  d_id = RandomNumber(1, DIST_PER_WARE);
  // 随机生成水平值
  level = RandomNumber(10, 20);

  clk1 = clock_gettime(CLOCK_MONOTONIC, &tbuf1);
  for (i = 0; i < MAX_RETRY; i++) {
    ret = slev(t_num, w_id, d_id, level, sql_event); // 执行库存水平事务
    clk2 = clock_gettime(CLOCK_MONOTONIC, &tbuf2);

    if (ret) {

      rt = (double)(tbuf2.tv_sec * 1000.0 + tbuf2.tv_nsec / 1000000.0 -
                    tbuf1.tv_sec * 1000.0 - tbuf1.tv_nsec / 1000000.0);
      if (rt > max_rt[4])
        max_rt[4] = rt;
      total_rt[4] += rt;
      hist_inc(4, rt);
      if (counting_on) {
        if (rt < rt_limit[4]) {
          success[4]++;
          success2[4][t_num]++;
        } else {
          late[4]++;
          late2[4][t_num]++;
        }
      }

      return (1); /* 结束 */
    } else {

      if (counting_on) {
        retry[4]++;
        retry2[4][t_num]++;
      }
    }
  }

  if (counting_on) {
    retry[4]--;
    retry2[4][t_num]--;
    failure[4]++;
    failure2[4][t_num]++;
  }

  (void)clk1;
  (void)clk2;
  
  return (0);
}

