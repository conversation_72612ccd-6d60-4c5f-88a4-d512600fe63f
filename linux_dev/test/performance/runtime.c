#include "performance.h"

FILE* fp = NULL;
char path[] = "./testfile/datafile/ARPT.txt";
GNCDB* db = NULL;
double min = -2147483647.0;
double max = 2147483647.0;
/* 执行次数 */
int count = 1000;
int unit = 3;           // 计时单位（1: 秒，2: 毫秒，3: 微秒，4: 纳秒）
int createTable();
int insertTableTime();
int selectTableTime();
int updateTableTime();
int deleteTableTime();

double timeFonversionFun(long long executionTime)
{
    double resultTime;

    switch (unit) {
        case 1:  // 秒
            resultTime = (double)executionTime / 1000000000.0;
            break;
        case 2:  // 毫秒
            resultTime = (double)executionTime / 1000000.0;
            break;
        case 3:  // 微秒
            resultTime = (double)executionTime / 1000.0;
            break;
        case 4:  // 纳秒
            resultTime = (double)executionTime;
            break;
        default:
            resultTime = 0;
            break;
    }
    return resultTime;
}

int runtime()
{
    int rc = 0;
    rc = createTable();
    if(rc != 0)
    {
        return rc;
    }
    fp = fopen(path, "r");
    rc = insertTableTime();
    if(rc != 0)
    {
        return rc;
    }
    rc = selectTableTime();
    if(rc != 0)
    {
        return rc;
    }
    rc = updateTableTime();
    if(rc != 0)
    {
        return rc;
    }
    rc = deleteTableTime();

    fclose(fp);
    return rc;
}


int createTable()
{
    int rc = 0;

    remove("runtime.dat");
    remove("log_runtime.dat");
    rc = GNCDB_open(&db, "runtime.dat", 0, 0);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = GNCDB_createTable(db, "ARPT", 6,
                           "ARPT_ident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0,
                           "ARPT_lon", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_lat", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_elev", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_length", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_mag_var", FIELDTYPE_REAL, 0, 0, min, max,
                           10000);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    return rc;
}

int insertTableTime()
{
    int rc = 0;

    int i = 0;
    int rows = 7000;
    ST_ARPT_ROW ct_arpt;

    /* 定义时间相关变量 */
    struct timespec startTime, endTime;
    long long totalTime = 0;
    long long executionTime = 0;
    double argRunTime = 0;
    double putTime = 0;


    if(fp == NULL)
    {
        return -1;
    }
    for (i = 0; i < rows; ++i)
    {
        fscanf(fp, "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
               ct_arpt.sc8_arpt_ident,
               &ct_arpt.f64_lon,
               &ct_arpt.f64_lat,
               &ct_arpt.f64_elev,
               &ct_arpt.f64_longest_rwy_length,
               &ct_arpt.f64_mag_var);

        /* 获取开始时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_insert(db, NULL, "ARPT",
                          ct_arpt.sc8_arpt_ident,
                          ct_arpt.f64_lon,
                          ct_arpt.f64_lat,
                          ct_arpt.f64_elev,
                          ct_arpt.f64_longest_rwy_length,
                          ct_arpt.f64_mag_var);

        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                                 (endTime.tv_nsec - startTime.tv_nsec);
        totalTime += executionTime;

        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }

    }
    argRunTime = (double)totalTime / (double)rows;
    putTime = timeFonversionFun(argRunTime);
    printf("insert: 插入 %d 条数据 平均时间:%.6lf μs\n", rows, putTime);
    return rc;
}

int selectTableTime()
{
    int rc = 0;
    int rows = 0;
    int i = 0;


    /* 定义时间相关变量 */
    double average_Alltime[5] = {0};
    struct timespec startTime, endTime;
    long long totalTime = 0;
    long long executionTime = 0;
    double argRunTime = 0;
    double putTime = 0;


    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>FRGFX");
    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lon>25.5");
    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat<5.5");
    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_ident>GYCQL", "ARPT_ident<MSFPM");
    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_ident>HIE", "ARPT_lat<5.5");
    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_lon>=0.000000", "ARPT_lon<=45.000000");
    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lon=-90.0");
    GNCDB_select(db, NULL, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_lat>-90.0", "ARPT_lon>25.5");
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident=FRGFX");

    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_select(db, NULL, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident=FRGFX");
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                                 (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;

        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("select: 主键单条查询 执行%d次平均时间: %.6lf μs\n", count, putTime);
    average_Alltime[0] = putTime;

    totalTime = 0;
    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_select(db, NULL, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_ident>GYCQL", "ARPT_ident<MSFPM");
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;

        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("select: 主键范围查询条数%d 执行%d次平均时间: %.6lf μs\n", rows, count, putTime);
    average_Alltime[1] = putTime;

    totalTime = 0;
    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_select(db, NULL, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_lon>0.000000", "ARPT_lon<45.000000");
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;

        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("select: 非主键范围查询条数%d 执行%d次平均时间: %.6lf μs\n", rows, count, putTime);
    average_Alltime[2] = putTime;

    putTime = (average_Alltime[0] + average_Alltime[1] + average_Alltime[2]) / 3;

    printf("select: 查询执行%d次平均时间: %.6lf μs\n", 3*count, putTime);

    return rc;
}

int updateTableTime()
{
    int rc = 0;
    int rows = 0;
    int allRows = 0;
    int i = 0;

    /* 定义时间相关变量 */
    double average_Alltime[5] = {0};
    struct timespec startTime, endTime;
    long long totalTime = 0;
    long long executionTime = 0;
    double argRunTime = 0;
    double putTime = 0;

    char* condition1[3] = {0};
    char* condition2[3] = {0};
    char* condition3[3] = {0};
    char* condition4[3] = {0};
    char* condition5[3] = {0};

    condition1[0] = "ARPT_ident=SVLMB";
    condition1[1] = "ARPT_ident=KUUAX";
    condition1[2] = "ARPT_ident=RZMTD";

    condition2[0] = "ARPT_ident>FKELG";
    condition2[1] = "ARPT_ident>AUESR";
    condition2[2] = "ARPT_ident>MSFPM";

    condition3[0] = "ARPT_ident<HILDY";
    condition3[1] = "ARPT_ident<CIRKL";
    condition3[2] = "ARPT_ident<OGXQE";

    condition4[0] = "ARPT_lon>-90.0";
    condition4[1] = "ARPT_lon>-25.0";
    condition4[2] = "ARPT_lon>22.5";

    condition5[0] = "ARPT_lon<-45.0";
    condition5[1] = "ARPT_lon<0.0";
    condition5[2] = "ARPT_lon<50.0";

    count = 3;
    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_update(db, &rows, "ARPT", 1, 1, "ARPT_elev", 25.3, condition1[i]);
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;

        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("update: 主键单条更新 执行%d次平均时间: %.6lf μs\n", count, putTime);
    average_Alltime[0] = putTime;

    totalTime = 0;
    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_update(db, &rows, "ARPT", 1, 2, "ARPT_length", 1.1, condition2[i], condition3[i]);
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;

        allRows += rows;
        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("update: 主键范围更新总条数%d 执行%d次平均时间: %.6lf μs\n", allRows, count, putTime);
    average_Alltime[1] = putTime;

    totalTime = 0;
    allRows = 0;
    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_update(db, &rows, "ARPT", 1, 2, "ARPT_lat", 30.5, condition4[i], condition5[i]);
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;
        allRows += rows;
        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("update: 非主键范围更新总条数%d 执行%d次平均时间: %.6lf μs\n", allRows, count, putTime);
    average_Alltime[2] = putTime;

    putTime = (average_Alltime[0] + average_Alltime[1] + average_Alltime[2]) / 3;

    printf("update: 删除执行%d次平均时间: %.6lf μs\n", 3*count, putTime);

    return rc;
}

int deleteTableTime()
{
    int rc = 0;
    int rows = 0;
    int allRows = 0;
    int i = 0;

    /* 定义时间相关变量 */
    double average_Alltime[5] = {0};
    struct timespec startTime, endTime;
    long long totalTime = 0;
    long long executionTime = 0;
    double argRunTime = 0;
    double putTime = 0;

    char* condition1[3] = {0};
    char* condition2[3] = {0};
    char* condition3[3] = {0};
    char* condition4[3] = {0};
    char* condition5[3] = {0};

    condition1[0] = "ARPT_ident=UBKNE";
    condition1[1] = "ARPT_ident=ERHPG";
    condition1[2] = "ARPT_ident=IIFAP";

    condition2[0] = "ARPT_ident>WJARB";
    condition2[1] = "ARPT_ident>HMSAN";
    condition2[2] = "ARPT_ident>BGZIL";

    condition3[0] = "ARPT_ident<YXGCP";
    condition3[1] = "ARPT_ident<LMUJQ";
    condition3[2] = "ARPT_ident<EYVLK";

    condition4[0] = "ARPT_lon>-45.0";
    condition4[1] = "ARPT_lon>25.0";
    condition4[2] = "ARPT_lon>0.0";

    condition5[0] = "ARPT_lon<-25.0";
    condition5[1] = "ARPT_lon<60.0";
    condition5[2] = "ARPT_lon<22.5";

    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_delete(db, &rows, "ARPT", 1, condition1[i]);
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;

        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("delete: 主键单条删除 执行%d次平均时间: %.6lf μs\n", count, putTime);
    average_Alltime[0] = putTime;

    totalTime = 0;
    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_delete(db, &rows, "ARPT", 2, condition2[i], condition3[i]);
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;

        allRows += rows;
        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("delete: 主键范围删除总条数%d 执行%d次平均时间: %.6lf μs\n", allRows, count, putTime);
    average_Alltime[1] = putTime;

    totalTime = 0;
    allRows = 0;
    for (i = 0; i < count; i++) {
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &startTime);

        rc = GNCDB_delete(db, &rows, "ARPT", 2, condition4[i], condition5[i]);
        /* 获取结束时间 */
        clock_gettime(CLOCK_MONOTONIC, &endTime);
        /* 计算执行时间 */
        executionTime = (endTime.tv_sec - startTime.tv_sec) * 1000000000LL +
                       (endTime.tv_nsec - startTime.tv_nsec);

        totalTime += executionTime;
        allRows += rows;
        if(rc != 0)
        {
            return rc;
        }
    }
    argRunTime = (double)totalTime / (double)count;
    putTime = timeFonversionFun(argRunTime);
    printf("delete: 非主键范围删除总条数%d 执行%d次平均时间: %.6lf μs\n", allRows, count, putTime);
    average_Alltime[2] = putTime;

    putTime = (average_Alltime[0] + average_Alltime[1] + average_Alltime[2]) / 3;

    printf("delete: 删除执行%d次平均时间: %.6lf μs\n", 3*count, putTime);

    return rc;

}