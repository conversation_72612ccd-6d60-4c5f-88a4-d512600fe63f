#include "performance.h"

pthread_mutex_t mutex;
GNCDB* db = NULL;
double min = -2147483647.0;
double max = 2147483647.0;

// 定义并发用户数
#define CONCURRENT_USERS 16
// 定义测试持续时间（秒）
#define TEST_DURATION 60

// 定义计数器变量
int request_count = 0;

// 查询数据库函数
void queryDatabase() {
    // 在此处编写连接数据库和发送查询请求的代码
    // 根据您使用的MySQL C API进行连接和查询操作
    // 请确保适当地处理数据库连接和查询结果
    // 可以使用mysql_real_query()函数执行查询语句，并使用mysql_store_result()获取结果集
    // 根据需要对结果集进行处理和释放资源
    // 请注意使用互斥锁确保计数器(request_count)的安全访问
    // 示例代码仅作为演示，具体实现需要根据您的数据库和API进行调整
    int randomNumber = 0;
    srand(time(0));
    randomNumber = rand() % 5;
    if(randomNumber == 0)
    {
        GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>GYCQL");
    }
    else if(randomNumber == 1)
    {
        GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident=GYCQL");
    }
    else if(randomNumber == 2)
    {
        GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lon>10.0");
    }
    else if(randomNumber == 3)
    {
        GNCDB_select(db, NULL, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_lon>10.0", "ARPT_lat<22.5");
    }
    else if(randomNumber == 4)
    {
        GNCDB_select(db, NULL, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");
    }

}

// 并发用户线程函数
void *userThread(void *arg) {
    // 获取线程编号
    int *p = (int *) arg;
    // 计算结束时间
    time_t end_time = time(NULL) + TEST_DURATION;
    // 设置随机种子
    srand(time(NULL));
    // 模拟并发请求
    while (time(NULL) < end_time) {
        // 发送查询请求
        queryDatabase();

        // 增加请求数
        //pthread_mutex_lock(&mutex);
        (*p)++;
        //pthread_mutex_unlock(&mutex);

        // 随机等待一段时间
//        sleep_time = rand() % 1000;
//        usleep(sleep_time);
    }

    pthread_exit(NULL);
    return NULL;
}

int qps_test() {
    int rc = 0;
    int i = 0;
    double qps = 0;

    FILE* fp = NULL;
    char path[] = "./testfile/datafile/ARPT.txt";

    pthread_t threads[CONCURRENT_USERS];
    int thread_ids[CONCURRENT_USERS];

    ST_ARPT_ROW ct_arpt;

    // 初始化互斥锁
    pthread_mutex_init(&mutex, NULL);

    // 初始化数据库连接
    remove("qps.dat");
    remove("log_qps.dat");
    rc = GNCDB_open(&db, "qps.dat", 0, 0);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = GNCDB_createTable(db, "ARPT", 6,
                           "ARPT_ident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0,
                           "ARPT_lon", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_lat", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_elev", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_length", FIELDTYPE_REAL, 0, 0, min, max,
                           "ARPT_mag_var", FIELDTYPE_REAL, 0, 0, min, max,
                           10000);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    fp = fopen(path, "r");
    if(fp == NULL)
    {
        return -1;
    }
    for (i = 0; i < 2000; ++i)
    {
        fscanf(fp, "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
               ct_arpt.sc8_arpt_ident,
               &ct_arpt.f64_lon,
               &ct_arpt.f64_lat,
               &ct_arpt.f64_elev,
               &ct_arpt.f64_longest_rwy_length,
               &ct_arpt.f64_mag_var);
        rc = GNCDB_insert(db, NULL, "ARPT",
                          ct_arpt.sc8_arpt_ident,
                          ct_arpt.f64_lon,
                          ct_arpt.f64_lat,
                          ct_arpt.f64_elev,
                          ct_arpt.f64_longest_rwy_length,
                          ct_arpt.f64_mag_var);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }

    }
    // 创建并发用户线程
    for (i = 0; i < CONCURRENT_USERS; i++) {
        thread_ids[i] = 0;
        pthread_create(&threads[i], NULL, userThread, (void *) &thread_ids[i]);
    }

    // 等待所有线程完成
    for (i = 0; i < CONCURRENT_USERS; i++) {
        pthread_join(threads[i], NULL);
    }
    for (i = 0; i < CONCURRENT_USERS; i++) {
        request_count += thread_ids[i];
    }

    // 输出结果
    printf("Total requests: %d\n", request_count);
    qps = (double) request_count / TEST_DURATION;
    printf("QPS: %.2f\n", qps);

    // 清理资源
    GNCDB_close(&db);
    pthread_mutex_destroy(&mutex);

    return 0;
}