#include "performance.h"
#include <fcntl.h>

int createTestFile();
void* pthreadFun1(void* arg);
void* pthreadFun2(void* arg);
char testFileName[60] = "./testfile/datafile/testFile.txt";

int createTestFile()
{
    FILE* fp = NULL;
    pthread_t thread1 = 0, thread2 = 0;
    int result = 0;
    char str[1024] = "this is test message\nhello world!\n";

    fp = fopen(testFileName, "w+");
    if(fp == NULL)
    {
        perror("create File failed\n");
        return -1;
    }
    printf("create File success\n");
    fwrite(str, 1, strlen(str), fp);

    fclose(fp);

    result = pthread_create(&thread1, NULL, pthreadFun1, (void*) testFileName);
    if(result != 0)
    {
        perror("Failed to create thread 1");
        return -1;
    }
    result = pthread_create(&thread2, NULL, pthreadFun2, (void*) testFileName);
    if(result != 0)
    {
        perror("Failed to create thread 1");
        return -1;
    }


    pthread_join(thread1, NULL);
    pthread_join(thread2, NULL);

    //remove(filename);
    return 0;
}

/* "r"：只读模式（read-only）。使用该权限打开文件后，可以读取文件内容，但不能写入或修改文件。如果文件不存在，打开操作将失败。
"w"：写入模式（write-only）。使用该权限打开文件后，可以写入文件内容，但不能读取文件。如果文件不存在，将创建新文件；如果文件已存在，将截断文件内容（即清空文件），然后重新写入。
"a"：追加模式（append）。使用该权限打开文件后，可以在文件末尾追加内容，不能修改文件中已有的内容。如果文件不存在，将创建新文件。
"r+"：读写模式（read-write）。使用该权限打开文件后，既可以读取文件内容，也可以写入或修改文件。如果文件不存在，打开操作将失败。
"w+"：读写模式，文件存在时截断（read-write, truncate）。使用该权限打开文件后，可以读取、写入或修改文件。如果文件不存在，将创建新文件；如果文件已存在，将截断文件内容，然后重新写入。
"a+"：读写模式，文件存在时追加（read-write, append）。使用该权限打开文件后，可以读取、写入或修改文件。如果文件不存在，将创建新文件；如果文件已存在，将在文件末尾追加内容。
除了上述基本权限之外，还可以与以下附加权限参数进行组合使用：

"b"：以二进制模式打开文件。使用该参数可以以二进制格式进行读写操作，适用于处理二进制文件。
"t"：以文本模式打开文件（默认）。使用该参数可以以文本格式进行读写操作，适用于处理文本文件。
"+"：允许读写模式（读写模式的组合）。使用该参数可以在打开文件后既能读取文件内容，又能写入或修改文件。 */

void* pthreadFun1(void* arg)
{
    FILE* fp = NULL;
    const char *filename = (const char *) arg;
    //char str[64] = {0};
    char* mess = "thread 1 write a message\n";
    int size = 0, count = 0;
    fp = fopen(filename, "w");
    if (fp == NULL) {
        perror("Failed to open file");
    }
    else
    {
//       printf("Thread 1 opened file with SUCCESS\n");
//       fread(str, 1, 20, fp);
//       printf("%s", str);
        count = strlen(mess);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        sleep(1);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        //sleep(3);
        printf("Thread 1 close\n");
        fclose(fp);
    }


    return NULL;
}

void* pthreadFun2(void* arg)
{
    FILE* fp = NULL;
    const char* filename = (const char*) arg;
    //char str[64] = {0};
    int size = 0, count = 0;
    char* mess = "thread 2 write a message\n";
    fp = fopen(filename, "w");
    if (fp == NULL) {
        perror("Failed to open file");
    } else {
//         printf("Thread 2 opened file with SUCCESS\n");
//         fread(str, 1, 20, fp);
//         printf("%s", str);
        count = strlen(mess);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);
        size = fwrite(mess, 1, count, fp);
        if (size != count) {
            perror("Failed to write data");
        }
        fflush(fp);

        printf("Thread 2 close\n");
        fclose(fp);
    }

    return NULL;
}

/*  */
void process_fun()
{
    // struct flock
}


