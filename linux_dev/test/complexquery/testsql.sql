-- test 1 查询客户名称和余额大于 500 的客户
SELECT C_NAME,
       C_ACCTBAL
FROM   CUSTOMER
WHERE  C_ACCTBAL > 500;

-- test 2 查询库存大于平均库存的商品编号
SELECT PS_PARTKEY,
       PS_AVAILQTY
FROM   PARTSUPP
WHERE  PS_AVAILQTY > (SELECT AVG(PS_AVAILQTY) FROM PARTSUPP);

-- test 3 查询每个供应商的商品供应数量
SELECT S_NAME,
       SUM(PS_AVAILQTY)
FROM   SUPPLIER,
       PARTSUPP
WHERE  S_SUPPKEY = PS_SUPPKEY
GROUP BY S_NAME;

-- test 4 查询每个客户的总消费金额
SELECT C_NAME,
       SUM(O_TOTALPRICE)
FROM   CUSTOMER,
       ORDERS
WHERE  C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 5 查询商品类型及其最大零售价
SELECT P_TYPE,
       MAX(P_RETAILPRICE)
FROM   PART
GROUP BY P_TYPE;

-- test 6 查询每个订单的总金额
SELECT L_ORDERKEY,
       SUM(L_EXTENDEDPRICE * L_QUANTITY) AS TOTAL
FROM   LINEITEM
GROUP BY L_ORDERKEY;

-- test 7 查询库存大于平均库存的商品编号
SELECT PS_PARTKEY,
       PS_AVAILQTY
FROM   PARTSUPP
WHERE  PS_AVAILQTY > (SELECT AVG(PS_AVAILQTY) FROM PARTSUPP);

-- test 8 查询每个供应商的商品供应数量
SELECT S_NAME,
       SUM(PS_AVAILQTY)
FROM   SUPPLIER,
       PARTSUPP
WHERE  S_SUPPKEY = PS_SUPPKEY
GROUP  BY S_NAME;

-- test 9 查询每个客户的总消费金额
SELECT C_NAME,
       SUM(O_TOTALPRICE)
FROM   CUSTOMER,
       ORDERS
WHERE  C_CUSTKEY = O_CUSTKEY
GROUP  BY C_NAME;

-- test 10 查询商品类型及其最大零售价
SELECT P_TYPE,
       MAX(P_RETAILPRICE)
FROM   PART
GROUP  BY P_TYPE;

-- test 11：查询每位客户的订单数量和订单总金额
SELECT C_NAME,
       COUNT(O_ORDERKEY),
       SUM(O_TOTALPRICE)
FROM CUSTOMER, ORDERS
WHERE C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 12：查询每位供应商所提供的不同商品的数量
SELECT S_NAME,
       COUNT(PS_PARTKEY)
FROM SUPPLIER, PARTSUPP
WHERE S_SUPPKEY = PS_SUPPKEY
GROUP BY S_NAME;

-- test 13：查询每个订单中涉及的不同商品数量
SELECT L_ORDERKEY,
       COUNT(L_PARTKEY)
FROM LINEITEM
GROUP BY L_ORDERKEY;

-- test 14：查询每个客户的订单总数（统计客户下订单数量）
SELECT C_NAME,
       COUNT(O_ORDERKEY)
FROM CUSTOMER, ORDERS
WHERE C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 15：查询每个供应商供应的商品种类数（统计供应商供应不同商品的数量）
SELECT S_NAME,
       COUNT( PS_PARTKEY)
FROM SUPPLIER, PARTSUPP
WHERE S_SUPPKEY = PS_SUPPKEY
GROUP BY S_NAME;


-- test 16：查询每个商品在订单中被购买的次数和总数量
SELECT L_PARTKEY,
       COUNT(L_ORDERKEY),
       SUM(L_QUANTITY)
FROM LINEITEM
GROUP BY L_PARTKEY;

-- test 17：查询每位供应商的总库存量
SELECT PS_SUPPKEY,
       SUM(PS_AVAILQTY)
FROM PARTSUPP
GROUP BY PS_SUPPKEY;

-- test 18：查询客户中有至少一个订单总金额大于其本客户平均订单金额的客户名和订单金额
SELECT CUSTOMER.C_NAME, ORDERS.O_TOTALPRICE
FROM CUSTOMER, ORDERS
WHERE CUSTOMER.C_CUSTKEY = ORDERS.O_CUSTKEY
  AND ORDERS.O_TOTALPRICE > (
      SELECT AVG(ORDERS.O_TOTALPRICE)
      FROM ORDERS
      WHERE ORDERS.O_CUSTKEY = CUSTOMER.C_CUSTKEY
  );
-- ORDER BY CUSTOMER.C_NAME;

-- test 19：查询每位供应商供应的商品总数量和其名称
SELECT SUPPLIER.S_NAME,
       SUM(PARTSUPP.PS_AVAILQTY)
FROM SUPPLIER, PARTSUPP
WHERE SUPPLIER.S_SUPPKEY = PARTSUPP.PS_SUPPKEY
GROUP BY SUPPLIER.S_NAME;


-- test 20：查询供应商提供的商品中，有供应成本高于其自身平均供应成本的商品编号和成本
SELECT SUPPLIER.S_NAME, PARTSUPP.PS_PARTKEY, PARTSUPP.PS_SUPPLYCOST
FROM SUPPLIER, PARTSUPP
WHERE SUPPLIER.S_SUPPKEY = PARTSUPP.PS_SUPPKEY
  AND PARTSUPP.PS_SUPPLYCOST > (
      SELECT AVG(PARTSUPP.PS_SUPPLYCOST)
      FROM PARTSUPP
      WHERE PARTSUPP.PS_SUPPKEY = SUPPLIER.S_SUPPKEY
  );

-- test 21：查询每位客户下过的订单总数
SELECT C_NAME,
       COUNT( O_ORDERKEY)
FROM CUSTOMER, ORDERS
WHERE C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 22：查询每个订单的最晚发货日期
SELECT L_ORDERKEY,
       MAX(L_SHIPDATE)
FROM LINEITEM
GROUP BY L_ORDERKEY;

-- test 23：查询每位供应商的总供货成本（单价 × 数量）
SELECT S_NAME,
       SUM(PS_SUPPLYCOST * PS_AVAILQTY)
FROM SUPPLIER, PARTSUPP
WHERE S_SUPPKEY = PS_SUPPKEY
GROUP BY S_NAME;

-- test 24：查询每位客户下单的不同日期数量
SELECT C_NAME,
       COUNT( O_ORDERDATE)
FROM CUSTOMER, ORDERS
WHERE C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 25：统计每种商品类型的商品数量
SELECT P_TYPE,
       COUNT(P_PARTKEY)
FROM PART
GROUP BY P_TYPE;

-- test 26：查询每天的订单总金额
SELECT O_ORDERDATE,
       SUM(O_TOTALPRICE)
FROM ORDERS
GROUP BY O_ORDERDATE;

-- test 27：查询每个订单中最早的发货日期
SELECT L_ORDERKEY,
       MIN(L_SHIPDATE)
FROM LINEITEM
GROUP BY L_ORDERKEY;

-- test 28：查询每位供应商提供的不同商品的数量
SELECT PS_SUPPKEY,
       COUNT( PS_PARTKEY)
FROM PARTSUPP
GROUP BY PS_SUPPKEY;

-- test 29：查询每位供应商提供商品的总数
-- SELECT S_NAME,
--        (SELECT COUNT(*)
--         FROM PARTSUPP
--         WHERE PARTSUPP.PS_SUPPKEY = SUPPLIER.S_SUPPKEY)
-- FROM SUPPLIER;

-- test 30：查询每位客户的订单总数
-- SELECT C_NAME,
--        (SELECT COUNT(*)
--         FROM ORDERS
--         WHERE ORDERS.O_CUSTKEY = CUSTOMER.C_CUSTKEY)
-- FROM CUSTOMER;

-- test 31 查询每位客户的订单总金额
SELECT C_NAME,
       SUM(O_TOTALPRICE)
FROM CUSTOMER, ORDERS
WHERE C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 32 查询每个订单的总扩展价格
SELECT L_ORDERKEY,
       SUM(L_EXTENDEDPRICE)
FROM LINEITEM
GROUP BY L_ORDERKEY;

-- test 33 查询每种商品类型的平均零售价
SELECT P_TYPE,
       AVG(P_RETAILPRICE)
FROM PART
GROUP BY P_TYPE;

-- test 34 查询每位供应商供应的商品数量
SELECT S_NAME,
       COUNT(PS_PARTKEY)
FROM SUPPLIER, PARTSUPP
WHERE S_SUPPKEY = PS_SUPPKEY
GROUP BY S_NAME;

-- test 35 查询每位客户的最后一次下单日期
SELECT C_NAME,
       MAX(O_ORDERDATE)
FROM CUSTOMER, ORDERS
WHERE C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 36 查询每个订单包含的订单项数量
SELECT O_ORDERKEY,
       COUNT(L_LINENUMBER)
FROM ORDERS, LINEITEM
WHERE O_ORDERKEY = L_ORDERKEY
GROUP BY O_ORDERKEY;

-- test 37 查询每个订单的实际支付金额（考虑折扣）
SELECT L_ORDERKEY,
       SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT))
FROM LINEITEM
GROUP BY L_ORDERKEY;

-- test 38 查询每种商品尺寸的商品数量
SELECT P_SIZE,
       COUNT(P_PARTKEY)
FROM PART
GROUP BY P_SIZE;

-- test 39 查询每位供应商提供的商品的最高供货成本
-- SELECT S_NAME,
--        (SELECT MAX(PS_SUPPLYCOST)
--         FROM PARTSUPP
--         WHERE PARTSUPP.PS_SUPPKEY = SUPPLIER.S_SUPPKEY)
-- FROM SUPPLIER;

-- test 40 查询每位客户的订单数量
-- SELECT C_NAME,
--        (SELECT COUNT(O_ORDERKEY)
--         FROM ORDERS
--         WHERE ORDERS.O_CUSTKEY = CUSTOMER.C_CUSTKEY)
-- FROM CUSTOMER;

-- test 41 查询不同订单状态下的总金额
SELECT O_ORDERSTATUS,
       SUM(O_TOTALPRICE)
FROM ORDERS
GROUP BY O_ORDERSTATUS;

-- test 42 查询每种退货标志对应的订单数量
SELECT L_RETURNFLAG,
       COUNT(L_ORDERKEY)
FROM LINEITEM
GROUP BY L_RETURNFLAG;

-- test 43 查询每个国家的供应商总供货成本
SELECT S_NATIONKEY,
       SUM(PS_SUPPLYCOST)
FROM SUPPLIER, PARTSUPP
WHERE S_SUPPKEY = PS_SUPPKEY
GROUP BY S_NATIONKEY;

-- test 44 查询每种商品类型对应的最大尺寸
SELECT P_TYPE,
       MAX(P_SIZE)
FROM PART
GROUP BY P_TYPE;

-- test 45 查询每个订单中最大的折扣值
SELECT L_ORDERKEY,
       MAX(L_DISCOUNT)
FROM LINEITEM
GROUP BY L_ORDERKEY;

-- test 46 查询每位客户的第一次下单日期
SELECT C_NAME,
       MIN(O_ORDERDATE)
FROM CUSTOMER, ORDERS
WHERE C_CUSTKEY = O_CUSTKEY
GROUP BY C_NAME;

-- test 47 查询每个商品被多少供应商供应
SELECT PS_PARTKEY,
       COUNT(PS_SUPPKEY)
FROM PARTSUPP
GROUP BY PS_PARTKEY;

-- test 48 查询订单总金额高于其所属客户平均订单金额的订单编号和客户名
SELECT ORDERS.O_ORDERKEY,
       CUSTOMER.C_NAME
FROM ORDERS, CUSTOMER
WHERE ORDERS.O_CUSTKEY = CUSTOMER.C_CUSTKEY
  AND ORDERS.O_TOTALPRICE > (
      SELECT AVG(ORDERS.O_TOTALPRICE)
      FROM ORDERS
      WHERE ORDERS.O_CUSTKEY = CUSTOMER.C_CUSTKEY
  );


-- test 49 查询每种商品容器类型的总零售价
SELECT P_CONTAINER,
       SUM(P_RETAILPRICE)
FROM PART
GROUP BY P_CONTAINER;

-- test 50 查询每个订单中的最小商品数量
SELECT L_ORDERKEY,
       MIN(L_QUANTITY)
FROM LINEITEM
GROUP BY L_ORDERKEY;
