#include "gncdbconstant.h"
#include "sqlquerytest.h"


int sqlTestInitCreateTable(GNCDB* db, sqlite3* sqdb, char* fileName){
    // int cnt = 0;
    FILE *file;
    char *line = NULL;
    size_t len = 0;
    ssize_t read;
    char *current_sql = NULL;
    char *temp = NULL;
	// int jumpFlag = 0;

	int rc = 0;

	char *queryNum = NULL;
    size_t current_sql_len = 0;

    file = fopen(fileName, "r");
    if (file == NULL) {
        perror("打开文件时出错");
        return 1;
    }

    while ((read = getline(&line, &len, file)) != -1) {
        // 忽略以"--"开头的行
        if (strncmp(line, "--", 2) == 0){
			queryNum = strstr(line, "Query");
			if(queryNum != NULL){
				printf("Query %s\n", queryNum);
			}
            continue;
		}
        // 移除行尾的换行符
        if (line[read - 1] == '\n') {
            line[read - 1] = '\0';
            --read;
        }

        // 为 current_sql 分配或重新分配内存
        temp = my_realloc(current_sql, current_sql_len + read + 2);
        if (!temp) {
            perror("内存分配失败");
            my_free(line);
            my_free(current_sql);
            fclose(file);
            return GNCDB_MEM;
        }
        current_sql = temp;

        // 将行添加到 current_sql
        memmove(current_sql+current_sql_len, line, strlen(line));
        memmove(current_sql+current_sql_len+strlen(line), " ", 1);
        current_sql_len += read + 1;
        current_sql[current_sql_len] = '\0';
        // 检查行是否以分号结尾
        if (line[read - 1] == ';') {
            // printf("%s\n\n", current_sql); // 打印 SQL 语句并换行
			remove_extra_spaces(current_sql);
            rc = GNCDB_exec(db, current_sql, NULL, NULL, NULL); // 执行 SQL 语句
			if(rc != 0){
				printf("GNCDB_exec error\n");
			}
            rc = sqlite3_exec(sqdb, current_sql, NULL, NULL, NULL);
            if(rc != SQLITE_OK){
                printf("sqlite3_exec error\n");
            }

            my_free(current_sql); // 释放当前 SQL 语句的内存
            current_sql = NULL;
            current_sql_len = 0;
        }
    }
    my_free(line);
    fclose(file);

    return GNCDB_SUCCESS;
}

int sqlTestInitInsertTable(GNCDB* db, sqlite3 * sqlite_db) 
{
	int rc = 0; // 用于存储函数返回值
	int i = 0; // 循环计数器
	int count = 0; // 用于存储fscanf读取的字段数
	FILE* fp = NULL; // 文件指针
    char regionPath[60] = { 0 };
    char nationPath[60] = { 0 };
    char supplierPath[60] = { 0 };
    char partPath[60] = { 0 };
    char partsuppPath[60] = { 0 };
    char customerPath[60] = { 0 };
    char ordersPath[60] = { 0 };
    char lineitemPath[60] = { 0 };

	/* region表 */
	sprintf(regionPath, "%s%s", tpchpath, regionFileName);
	fp = fopen(regionPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < REGIONROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|\n",
			&regiontbl.regionkey,
			regiontbl.name,
			regiontbl.comment);
		if (count != 3)
		{

		}
        rc = GNCDB_insert(db, NULL, "REGION",
            regiontbl.regionkey,
            regiontbl.name,
            regiontbl.comment);
        {
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO REGION VALUES(%d,'%s','%s');", 
				regiontbl.regionkey, 
				regiontbl.name, 
				regiontbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	/* nation表 */
	sprintf(nationPath, "%s%s", tpchpath, nationFileName);
	fp = fopen(nationPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < NATIONROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%d|%[^|]|\n",
			&nationtbl.nationkey,
			nationtbl.name,
			&nationtbl.regionkey,
			nationtbl.comment);
		if (count != 4)
		{

        }
        rc = GNCDB_insert(db, NULL, "NATION",
            nationtbl.nationkey,
            nationtbl.name,
            nationtbl.regionkey,
            nationtbl.comment);
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
        {
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO NATION VALUES(%d,'%s',%d,'%s');", 
				nationtbl.nationkey, 
				nationtbl.name, 
				nationtbl.regionkey, 
				nationtbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
	}
	fclose(fp);

	/* supplier表 */
	sprintf(supplierPath, "%s%s", tpchpath, supplierFileName);
	fp = fopen(supplierPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < SUPPLIERROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&suppliertbl.suppkey,
			suppliertbl.name,
			suppliertbl.address,
			&suppliertbl.nationkey,
			suppliertbl.phone,
			&suppliertbl.acctbal,
			suppliertbl.comment);
		if (count != 7)
		{

		}

        rc = GNCDB_insert(db, NULL, "SUPPLIER",
            suppliertbl.suppkey,
            suppliertbl.name,
            suppliertbl.address,
            suppliertbl.nationkey,
            suppliertbl.phone,
            suppliertbl.acctbal,
            suppliertbl.comment);
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
        {
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO supplier VALUES(%d,'%s','%s',%d,'%s',%lf,'%s');", 
				suppliertbl.suppkey, 
				suppliertbl.name, 
				suppliertbl.address, 
				suppliertbl.nationkey, 
				suppliertbl.phone, 
				suppliertbl.acctbal, 
				suppliertbl.comment
			);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}

		}
	}
	fclose(fp);

	/* part表 */
	sprintf(partPath, "%s%s", tpchpath, partFileName);
	fp = fopen(partPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < PARTROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&parttbl.partkey,
			parttbl.name,
			parttbl.mfgr,
			parttbl.brand,
			parttbl.type,
			&parttbl.size,
			parttbl.container,
			&parttbl.retailprice,
			parttbl.comment);
		if (count != 9)
		{

		}
        rc = GNCDB_insert(db, NULL, "PART",
            parttbl.partkey,
            parttbl.name,
            parttbl.mfgr,
            parttbl.brand,
            parttbl.type,
            parttbl.size,
            parttbl.container,
            parttbl.retailprice,
            parttbl.comment);
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
        {
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO part VALUES (%d,'%s','%s','%s','%s',%d,'%s',%lf,'%s');", 
				parttbl.partkey, 
				parttbl.name, 
				parttbl.mfgr, 
				parttbl.brand, 
				parttbl.type, 
				parttbl.size, 
				parttbl.container, 
				parttbl.retailprice, 
				parttbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
	}
	fclose(fp);

	/* partsupp表 */
	sprintf(partsuppPath, "%s%s", tpchpath, partsuppFileName);
	fp = fopen(partsuppPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < PARTSUPPROWS; ++i)
	{
		count = fscanf(fp, "%d|%d|%d|%lf|%[^|]|\n",
			&partsupptbl.partkey,
			&partsupptbl.suppkey,
			&partsupptbl.availqty,
			&partsupptbl.supplycost,
			partsupptbl.comment);
		if (count != 5)
		{

		}
        rc = GNCDB_insert(db, NULL, "PARTSUPP",
            partsupptbl.partkey,
            partsupptbl.suppkey,
            partsupptbl.availqty,
            partsupptbl.supplycost,
            partsupptbl.comment);
		
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
        {
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO partsupp VALUES(%d,%d,%d,%lf,'%s');", 
				partsupptbl.partkey, 
				partsupptbl.suppkey, 
				partsupptbl.availqty, 
				partsupptbl.supplycost, 
				partsupptbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
	}
	fclose(fp);

	/* customer表 */
	sprintf(customerPath, "%s%s", tpchpath, customerFileName);
	fp = fopen(customerPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < CUSTOMERROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|%[^|]|\n",
			&customertbl.custkey,
			customertbl.name,
			customertbl.address,
			&customertbl.nationkey,
			customertbl.phone,
			&customertbl.acctbal,
			customertbl.mktsegment,
			customertbl.comment);
		if (count != 8)
		{

		}
        rc = GNCDB_insert(db, NULL, "CUSTOMER",
            customertbl.custkey,
            customertbl.name,
            customertbl.address,
            customertbl.nationkey,
            customertbl.phone,
            customertbl.acctbal,
            customertbl.mktsegment,
            customertbl.comment);

		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
        {
			char sql[1024] = { 0 };
			char *errMsg = NULL;
			sprintf(sql, "INSERT INTO customer VALUES(%d,'%s','%s',%d,'%s',%lf,'%s','%s');", 
				customertbl.custkey, 
				customertbl.name, 
				customertbl.address, 
				customertbl.nationkey, 
				customertbl.phone, 
				customertbl.acctbal, 
				customertbl.mktsegment, 
				customertbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, &errMsg);
			if(rc != SQLITE_OK){
				printf("errMsg:%s\n", errMsg);
				sqlite3_free(errMsg);
				printf("sqlite3_exec failed\n");
				return rc;
			}

		}
	}
	fclose(fp);

	/* orders表 */
	sprintf(ordersPath, "%s%s", tpchpath, ordersFileName);
	fp = fopen(ordersPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < ORDERSROWS; ++i)
	{
		count = fscanf(fp, "%d|%d|%[^|]|%lf|%[^|]|%[^|]|%[^|]|%d|%[^|]|\n",
			&orderstbl.orderkey,
			&orderstbl.custkey,
			orderstbl.orderstatus,
			&orderstbl.totalprice,
			orderstbl.orderdate,
			orderstbl.orderpriority,
			orderstbl.clerk,
			&orderstbl.shippriority,
			orderstbl.comment);
		if (count != 9)
		{

		}
        rc = GNCDB_insert(db, NULL, "ORDERS",
            orderstbl.orderkey,
            orderstbl.custkey,
            orderstbl.orderstatus,
            orderstbl.totalprice,
            orderstbl.orderdate,
            orderstbl.orderpriority,
            orderstbl.clerk,
            orderstbl.shippriority,
            orderstbl.comment);

		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
        {
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO orders VALUES(%d,%d,'%s',%lf,'%s','%s','%s',%d,'%s');", 
				orderstbl.orderkey, 
				orderstbl.custkey, 
				orderstbl.orderstatus, 
				orderstbl.totalprice, 
				orderstbl.orderdate, 
				orderstbl.orderpriority, 
				orderstbl.clerk, 
				orderstbl.shippriority,
				orderstbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
        
	}
	fclose(fp);

	/* lineitem表 */
	sprintf(lineitemPath, "%s%s", tpchpath, lineitemFileName);
	fp = fopen(lineitemPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < LINEITEMROWS; ++i)
	{
		count = fscanf(fp, "%d|%d|%d|%d|%lf|%lf|%lf|%lf|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|\n",
			&lineitemtbl.orderkey,
			&lineitemtbl.partkey,
			&lineitemtbl.suppkey,
			&lineitemtbl.linenumber,
			&lineitemtbl.quantity,
			&lineitemtbl.extendedpeice,
			&lineitemtbl.discount,
			&lineitemtbl.tax,
			lineitemtbl.returnflag,
			lineitemtbl.linestatus,
			lineitemtbl.shipdate,
			lineitemtbl.commitdate,
			lineitemtbl.receiptdate,
			lineitemtbl.shipinstruct,
			lineitemtbl.shipmode,
			lineitemtbl.comment);
		if (count != 16)
		{

		}
        rc = GNCDB_insert(db, NULL, "LINEITEM",
            lineitemtbl.orderkey,
            lineitemtbl.partkey,
            lineitemtbl.suppkey,
            lineitemtbl.linenumber,
            lineitemtbl.quantity,
            lineitemtbl.extendedpeice,
            lineitemtbl.discount,
            lineitemtbl.tax,
            lineitemtbl.returnflag,
            lineitemtbl.linestatus,
            lineitemtbl.shipdate,
            lineitemtbl.commitdate,
            lineitemtbl.receiptdate,
            lineitemtbl.shipinstruct,
            lineitemtbl.shipmode,
            lineitemtbl.comment);

		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
        {
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO lineitem VALUES (%d,%d,%d,%d,%lf,%lf,%lf,%lf,'%s','%s','%s','%s','%s','%s','%s','%s');", 
				lineitemtbl.orderkey, 
				lineitemtbl.partkey, 
				lineitemtbl.suppkey, 
				lineitemtbl.linenumber, 
				lineitemtbl.quantity, 
				lineitemtbl.extendedpeice,
				lineitemtbl.discount,
				lineitemtbl.tax,
				lineitemtbl.returnflag,
				lineitemtbl.linestatus,
				lineitemtbl.shipdate,
				lineitemtbl.commitdate,
				lineitemtbl.receiptdate,
				lineitemtbl.shipinstruct,
				lineitemtbl.shipmode,
				lineitemtbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
	}
	fclose(fp);

	return GNCDB_SUCCESS;
}


void initTestCallBack(TESTCB_* testcb)
{ 
    testcb->count = 0;
    testcb->file = fopen("testcmp.txt", "w");
    if(testcb->file == NULL){
        printf("open testcmp.txt failed\n");
    }
    testcb->sqfile = fopen("sqtestcmp.txt", "w");
    if(testcb->sqfile == NULL){
        printf("open sqtestcmp.txt failed\n");
    }
}

int fileCmp(TESTCB_* testcb){
    FILE* f1 = NULL;
    FILE* f2 = NULL;
    int ch1, ch2;
	long size1, size2;
	long pos1, pos2;
    fclose(testcb->file);
    fclose(testcb->sqfile);

    // 判断两个文件内容是否一致
    f1 = fopen("testcmp.txt", "rb");
    if(f1 == NULL){
        printf("open testcmp.txt failed\n");
        return -1;
    }
    f2 = fopen("sqtestcmp.txt", "rb");
    if(f2 == NULL){
        printf("open sqtestcmp.txt failed\n");
        return -1;
    }
	// 如果两个文件内容为空，则认为它们不相同

	pos1 = ftell(f1);
    pos2 = ftell(f2);
	fseek(f1, 0, SEEK_END);
    size1 = ftell(f1);
	fseek(f2, 0, SEEK_END);
    size2 = ftell(f2);

	fseek(f1, pos1, SEEK_SET);
    fseek(f2, pos2, SEEK_SET);

	// 如果两个文件大小都为 0，返回 -1
    if (size1 == 0 && size2 == 0) {
        return -1;
    }

    while (1) {
		if((ch1 = fgetc(f1)) == EOF)
		{
			ch2 = fgetc(f2);
			break;
		}
		else if((ch2 = fgetc(f2)) == EOF)
		{
			ch1 = fgetc(f1);
			break;
		}
		// printf("%c %c\n", ch1, ch2);
        if (ch1 != ch2) {
            fclose(f1);
            fclose(f2);
            return 0; // 文件内容不同
        }
    }

    // 如果一个文件结束了而另一个文件还没结束，说明文件大小不同
    if ((ch1 != EOF) || (ch2 != EOF)) {
        fclose(f1);
        fclose(f2);
        return 0; // 文件大小不同
    }

    fclose(f1);
    fclose(f2);
    return 1; // 文件相同

}