#include "sqlquerytest.h"


int test1CallBack(void *NotUsed, int columnNum, char** fieldName, char** fieldValue){
    TESTCB_* cb = (TESTCB_*)NotUsed;
    if(columnNum == 1 && strcmp(fieldName[0], "S_NAME") == 0){
        cb->count++;
    }
    return 0;
}

int test2CallBack(void *NotUsed, int columnNum, char** fieldName, char** fieldValue){
    TESTCB_* cb = (TESTCB_*)NotUsed;
    if(columnNum == 2 && strcmp(fieldName[0], "C_NAME") == 0 && strcmp(fieldName[1], "C_ACCTBAL") == 0){
        cb->count++;
    }
    return 0;
}

int writerFileDbCallBack(void *NotUsed, int columnNum, char** fieldName, char** fieldValue){
    int i = 0;
    int j = 0;
    int count = 0;
    char* pstr = NULL;
    TESTCB_* cb = (TESTCB_*)NotUsed;
    for(; i < columnNum; i++){
        char line[1024] = {0};
        // 如果feildName[i]中存在.，只打印.后边的部分
        if(strchr(fieldName[i], '.') != NULL){
            pstr = strchr(fieldName[i], '.') + 1;
        } else {
            pstr = fieldName[i];
        }
        sprintf(line, "%s : %s\n", pstr,  fieldValue[i]);
        count = strlen(line);

        for(j = 0; j < count; j++){
            fwrite(&line[j], 1, 1, cb->file);
        }
        // printf("%d\n", strlen(line));
        // printf("%s", line);
        // fwrite(line, strlen(line), 1, cb->file);
    }
    fflush(cb->file);
    return 0;
}

int writerFilesqDbCallBack(void *NotUsed, int columnNum, char** fieldValue, char** fieldName){
    int i = 0;
    int j = 0;
    TESTCB_* cb = (TESTCB_*)NotUsed;
    int count = 0;
    char* pstr = NULL;
    for(; i < columnNum; i++){
        char line[1024] = {0};
        // 如果feildName[i]中存在.，只打印.后边的部分
        if(strchr(fieldName[i], '.') != NULL){
            pstr = strchr(fieldName[i], '.') + 1;
        } else {
            pstr = fieldName[i];
        }
        sprintf(line, "%s : %s\n", pstr,  fieldValue[i]);
        
        count = strlen(line);
        for(j = 0; j < count; j++){
            fwrite(&line[j], 1, 1, cb->sqfile);
        }
        // fwrite(line, 1, strlen(line), cb->sqfile);
        fflush(cb->sqfile);
    }
    return 0;
}


int  sqlitecomCallBack(void *data, int argc, char **argv, char **azColName)
{
  int i;
//   cnt++;
  // 如果printHeader为1，则打印列名 {
    for (i = 0; i < argc; i++) {
      printf("%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
    }// 设置为0，下次调用时不再打印列名 // 初始化循环变量i为0
  // 初始化返回码rc为0
  for (i = 0; i < argc; i++) {
    printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");  // 初始化全局变量SAMETABLE为0
  }  // 从10开始循环，直到i小于MULTI_TESTNUM
  printf("\n");
  return 0;  // 如果i等于5
}