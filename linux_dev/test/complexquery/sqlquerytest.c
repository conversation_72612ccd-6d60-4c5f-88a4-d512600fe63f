#include "sqlquerytest.h"



GNCDB* sqc_db = NULL;
sqlite3 * sqc_sqlitedb = NULL;


int sqlquerytest_main()
{
	int rc = 0;

	TESTCB_ cb = {0};

	FILE *file;
    char *line = NULL;
    size_t len = 0;
    ssize_t read;
    char *current_sql = NULL;
    char *temp = NULL;

	char *queryNum = NULL;
    size_t current_sql_len = 0;

	int index = 0;

	// double y = 2916.0;
	// char strt[25] = {0};

	// format_double_dtoa(y, strt, 20);
	// printf("%s\n", strt);

	// remove("sqc_test.dat");
	// remove("log_sqc_test.dat");
	// remove("sqc_sqlite.db");

	// rc = GNCDB_open(&sqc_db, "sqc_test.dat", 0, 0);
	// if (rc != GNCDB_SUCCESS){
	// 	printf("open sqc_test.dat error\n");
	// 	return rc;
	// }
	// rc = sqlite3_open("sqc_sqlite.db", &sqc_sqlitedb);
	// if (rc != SQLITE_OK){
	// 	printf("open sqc_sqlite.db error\n");
	// 	return rc;
	// }

	// rc = sqlTestInitCreateTable(sqc_db, sqc_sqlitedb, "./test/tpc-h/tpch.ddl");
	// if (rc != GNCDB_SUCCESS){
	// 	printf("create table error\n");
	// 	return rc;
	// }

	// rc = sqlTestInitInsertTable(sqc_db, sqc_sqlitedb);
	// if (rc != GNCDB_SUCCESS){
	// 	printf("insert table error\n");
	// 	return rc;
	// }


	// GNCDB_close(&sqc_db);
	// sqlite3_close(sqc_sqlitedb);

	rc = GNCDB_open(&sqc_db, "sqc_test.dat", 0, 0);
	if (rc != GNCDB_SUCCESS){
		printf("open sqc_test.dat error\n");
		return rc;
	}
	rc = sqlite3_open("sqc_sqlite.db", &sqc_sqlitedb);
	if (rc != SQLITE_OK){
		printf("open sqc_sqlite.db error\n");
		return rc;
	}

	file = fopen("./test/complexquery/testsql.sql", "r");
    if (file == NULL) {
        perror("打开文件时出错");
        return 1;
    }

    while ((read = getline(&line, &len, file)) != -1) {
        // 忽略以"--"开头的行
        if (strncmp(line, "--", 2) == 0){
			queryNum = strstr(line, "test");
			if(queryNum != NULL){
				if (line[read - 1] == '\n') {
					line[read - 1] = '\0';
					--read;
				}
				printf("\n");
				printf("%s : ", line + 3);
			}
            continue;
		}
        // 移除行尾的换行符
        if (line[read - 1] == '\n') {
            line[read - 1] = '\0';
            --read;
        }

        // 为 current_sql 分配或重新分配内存
        temp = my_realloc(current_sql, current_sql_len + read + 2);
        if (!temp) {
            perror("内存分配失败");
            my_free(line);
            my_free(current_sql);
            fclose(file);
			my_free(line);
            return GNCDB_MEM;
        }
        current_sql = temp;

        // 将行添加到 current_sql
        memmove(current_sql+current_sql_len, line, strlen(line));
        memmove(current_sql+current_sql_len+strlen(line), " ", 1);
        current_sql_len += read + 1;
        current_sql[current_sql_len] = '\0';
        // 检查行是否以分号结尾
        if (line[read - 1] == ';') {
			remove_extra_spaces(current_sql);
			initTestCallBack(&cb);
			index ++;
			rc = sqlite3_exec(sqc_sqlitedb, current_sql, writerFilesqDbCallBack, &cb, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite error\n");
			}
			rc = sqlite3_exec(sqc_sqlitedb, current_sql, sqlitecomCallBack, NULL, NULL);

            rc = GNCDB_exec(sqc_db, current_sql, writerFileDbCallBack, &cb, NULL); // 执行 SQL 语句
			if(rc != 0){
				printf(" error\n");
			}

            my_free(current_sql); // 释放当前 SQL 语句的内存
            current_sql = NULL;
            current_sql_len = 0;


			rc = fileCmp(&cb);
			if(rc == 1){
				printf("测试通过");
			}
			else{
				printf("测试未通过");
			}


        }
    }
    my_free(line);
    fclose(file);


	GNCDB_close(&sqc_db);
	sqlite3_close(sqc_sqlitedb);


	return 0;
}