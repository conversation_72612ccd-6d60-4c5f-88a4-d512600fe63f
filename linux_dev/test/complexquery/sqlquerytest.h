#ifndef _SQLQUERYTEST_H_
#define _SQLQUERYTEST_H_

#include "../testdependlib/sqlitelib/sqlite3.h"
#include "gncdb.h"
#include "tpch.h"


typedef struct {
    int count;
    FILE* file;
    FILE* sqfile;
} TESTCB_;

// 测试入口
int sqlquerytest_main();



int sqlTestInitCreateTable(GNCDB* db, sqlite3* sqdb, char* fileName);
int sqlTestInitInsertTable(GNCDB* db, sqlite3* sqdb);
void initTestCallBack(TESTCB_* testcb);
int fileCmp(TESTCB_* testcb);
int test1CallBack(void *NotUsed, int columnNum, char** fieldName, char** fieldValue);

int writerFileDbCallBack(void *NotUsed, int columnNum, char** fieldValue, char** fieldName);
int writerFilesqDbCallBack(void *NotUsed, int columnNum, char** fieldValue, char** fieldName);

int  sqlitecomCallBack(void *data, int argc, char **argv, char **azColName);

#endif