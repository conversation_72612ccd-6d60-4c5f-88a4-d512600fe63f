-- TPC-H/TPC-R Pricing Summary Report Query (Q1)
SELECT
    L_RETURNFLAG,
    L_LINESTATUS,
    SUM(L_QUANTITY) AS SUM_QTY,
    SUM(L_EXTENDEDPRICE) AS SUM_BASE_PRICE,
    SUM(<PERSON>_EXTENDEDPRICE * (1 - L_DISCOUNT)) AS SUM_DISC_PRICE,
    SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT) * (1 + L_TAX)) AS SUM_CHARGE,
    AVG(L_QUANTITY) AS AVG_QTY,
    AVG(L_EXTENDEDPRICE) AS AVG_PRICE,
    AVG(L_DISCOUNT) AS AVG_DISC,
    COUNT(*) AS COUNT_ORDER
FROM
    LINEITEM
WHERE
    L_SHIPDATE <= '1998-09-02'
GROUP BY
    L_RETURNFLAG,
    L_LINESTATUS
ORDER BY
    L_RETURNFLAG,
    L_LINESTATUS;



-- TPC-H/TPC-R Minimum Cost Supplier Query (Q2)
-- SELECT
--     S_ACCTBAL,
--     S_NAME,
--     N_NAME,
--     P_PARTKEY,
--     P_MFGR,
--     S_ADDRESS,
--     S_PHONE,
--     S_COMMENT
-- FROM
--     PART
-- INNER JOIN
--     PARTSUPP ON P_PARTKEY = PS_PARTKEY
-- INNER JOIN
--     SUPPLIER ON S_SUPPKEY = PS_SUPPKEY
-- INNER JOIN
--     NATION ON S_NATIONKEY = N_NATIONKEY
-- INNER JOIN
--     REGION ON N_REGIONKEY = R_REGIONKEY
-- WHERE
--     P_SIZE = 15
--     AND P_TYPE LIKE '%BRASS'
--     AND R_NAME = 'EUROPE'
--     AND PS_SUPPLYCOST = (
--         SELECT
--             MIN(PS_SUPPLYCOST)
--         FROM
--             PARTSUPP
--         INNER JOIN
--             SUPPLIER ON S_SUPPKEY = PS_SUPPKEY
--         INNER JOIN
--             NATION ON S_NATIONKEY = N_NATIONKEY
--         INNER JOIN
--             REGION ON N_REGIONKEY = R_REGIONKEY
--         WHERE
--             R_NAME = 'EUROPE'
--     )
-- ORDER BY
--     S_ACCTBAL DESC,
--     N_NAME,
--     S_NAME,
--     P_PARTKEY;


-- TPC-H/TPC-R Shipping Priority Query (Q3)
-- SELECT
-- 	L_ORDERKEY,
-- 	SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) AS REVENUE,
-- 	O_ORDERDATE,
-- 	O_SHIPPRIORITY
-- FROM
-- 	CUSTOMER
-- INNER JOIN ORDERS ON C_CUSTKEY = O_CUSTKEY
-- INNER JOIN LINEITEM ON L_ORDERKEY = O_ORDERKEY
-- WHERE
-- 	C_MKTSEGMENT = 'BUILDING'
-- 	AND O_ORDERDATE <  '1995-03-15'
-- 	AND L_SHIPDATE >  '1995-03-15'
-- GROUP BY
-- 	L_ORDERKEY,
-- 	O_ORDERDATE,
-- 	O_SHIPPRIORITY
-- ORDER BY
-- 	REVENUE DESC,
-- 	O_ORDERDATE
-- LIMIT 10;


-- TPC-H/TPC-R Order Priority Checking Query (Q4)
-- SELECT
-- 	O_ORDERPRIORITY,
-- 	COUNT(*) AS ORDER_COUNT
-- FROM
-- 	ORDERS
-- WHERE
-- 	O_ORDERDATE >=  '1993-07-01'
-- 	AND O_ORDERDATE <  '1993-10-01'
-- 	AND EXISTS (
-- 		SELECT
-- 			*
-- 		FROM
-- 			LINEITEM
-- 		WHERE
-- 			L_ORDERKEY = O_ORDERKEY
-- 			AND L_COMMITDATE < L_RECEIPTDATE
-- 	)
-- GROUP BY
-- 	O_ORDERPRIORITY
-- ORDER BY
-- 	O_ORDERPRIORITY;


-- TPC-H/TPC-R Local Supplier Volume Query (Q5)
-- SELECT
--     N_NAME,
--     SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) AS REVENUE
-- FROM
--     CUSTOMER
--     INNER JOIN ORDERS ON C_CUSTKEY = O_CUSTKEY
--     INNER JOIN LINEITEM ON L_ORDERKEY = O_ORDERKEY
--     INNER JOIN SUPPLIER ON L_SUPPKEY = S_SUPPKEY
--     INNER JOIN NATION ON N_NATIONKEY = S_NATIONKEY
--     INNER JOIN REGION ON N_REGIONKEY = R_REGIONKEY
-- WHERE
--     R_NAME = 'ASIA'
--     AND C_NATIONKEY = S_NATIONKEY
--     AND O_ORDERDATE >=  '1994-01-01'
--     AND O_ORDERDATE <  '1995-01-01' 
-- GROUP BY
--     N_NAME
-- ORDER BY
--     REVENUE DESC;


-- TPC-H/TPC-R Forecasting Revenue Change Query (Q6)
SELECT
    SUM(L_EXTENDEDPRICE * L_DISCOUNT) AS REVENUE
FROM
    LINEITEM
WHERE
    L_SHIPDATE >=  '1994-01-01'
    AND L_SHIPDATE <  '1995-01-01'
    AND L_DISCOUNT >= 0.05
    AND L_DISCOUNT <= 0.07
    AND L_QUANTITY < 24;

-- 批量出货查询
-- TPC-H/TPC-R Volume Per Shipping Priority Query (Q7)
-- SELECT
--     SUPP_NATION,
--     CUST_NATION,
--     L_YEAR,
--     SUM(VOLUME) AS REVENUE
-- FROM
--     (
--         SELECT
--             N1.N_NAME AS SUPP_NATION,
--             N2.N_NAME AS CUST_NATION,
--             YEAR(L_SHIPDATE) AS L_YEAR,
--             L_EXTENDEDPRICE * (1 - L_DISCOUNT) AS VOLUME
--         FROM
--             SUPPLIER
--             INNER JOIN LINEITEM ON S_SUPPKEY = L_SUPPKEY,
--             INNER JOIN ORDERS ON O_ORDERKEY = L_ORDERKEY,
--             INNER JOIN CUSTOMER ON O_CUSTKEY = C_CUSTKEY,
--             INNER JOIN NATION N1 ON S_NATIONKEY = N1.N_NATIONKEY,
--             INNER JOIN NATION N2 ON C_NATIONKEY = N2.N_NATIONKEY
--         WHERE
--             (N1.N_NAME = '[NATION1]' AND N2.N_NAME = '[NATION2]')
--             OR (N1.N_NAME = '[NATION2]' AND N2.N_NAME = '[NATION1]')
--             AND L_SHIPDATE BETWEEN '1995-01-01' AND '1996-12-31'
--     ) AS SHIPPING
-- GROUP BY
--     SUPP_NATION,
--     CUST_NATION,
--     L_YEAR
-- ORDER BY
--     SUPP_NATION,
--     CUST_NATION,
--     L_YEAR;
            
--全国市场份额查询
-- TPC-H/TPC-R National Market Share Query (Q8)
-- SELECT
--     O_YEAR,
--     SUM(CASE
--         WHEN NATION = '[NATION]' THEN VOLUME
--         ELSE 0
--     END) / SUM(VOLUME) AS MKT_SHARE
-- FROM
--     (
--         SELECT
--             YEAR(O_ORDERDATE) AS O_YEAR,
--             L_EXTENDEDPRICE * (1 - L_DISCOUNT) AS VOLUME,
--             N2.N_NAME AS NATION
--         FROM
--             PART,
--             INNER JOIN SUPPLIER ON S_SUPPKEY = L_SUPPKEY,
--             INNER JOIN LINEITEM ON L_PARTKEY = P_PARTKEY,
--             INNER JOIN ORDERS ON O_ORDERKEY = L_ORDERKEY,
--             INNER JOIN CUSTOMER ON O_CUSTKEY = C_CUSTKEY,
--             INNER JOIN NATION N1 ON C_NATIONKEY = N1.N_NATIONKEY,
--             INNER JOIN NATION N2 ON S_NATIONKEY = N2.N_NATIONKEY,
--             INNER JOIN REGION ON N1.N_REGIONKEY = R_REGIONKEY
--         WHERE
--             R_NAME = '[REGION]'
--             AND P_TYPE = '[TYPE]'
--             AND O_ORDERDATE BETWEEN '1995-01-01' AND '1996-12-31'
--     ) AS ALL_NATIONS
-- GROUP BY
--     O_YEAR
-- ORDER BY
--     O_YEAR;

-- 产品类型利润度量查询
-- TPC-H/TPC-R Product Type Profit Measure Query (Q9)
-- SELECT
--     NATION,
--     O_YEAR,
--     SUM(AMOUNT) AS SUM_PROFIT
-- FROM
--     (
--         SELECT
--             N_NAME AS NATION,
--             EXTRACT(YEAR FROM O_ORDERDATE) AS O_YEAR,
--             L_EXTENDEDPRICE * (1 - L_DISCOUNT) - PS_SUPPLYCOST * L_QUANTITY AS AMOUNT
--         FROM
--             PART,
--             INNER JOIN SUPPLIER ON S_SUPPKEY = L_SUPPKEY,
--             INNER JOIN LINEITEM ON PS_PARTKEY = L_PARTKEY,
--             INNER JOIN PARTSUPP ON PS_SUPPKEY = S_SUPPKEY,
--             INNER JOIN ORDERS ON O_ORDERKEY = L_ORDERKEY,
--             INNER JOIN NATION ON S_NATIONKEY = N_NATIONKEY
--         WHERE
--             P_NAME LIKE '%[COLOR]% [FEATURE]'
--     ) AS PROFIT
-- GROUP BY
--     NATION,
--     O_YEAR
-- ORDER BY
--     NATION,
--     O_YEAR DESC;


-- 退货报告查询
-- TPC-H/TPC-R Returned Item Reporting Query (Q10)
-- SELECT
--     C_CUSTKEY,
--     C_NAME,
--     SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) AS REVENUE,
--     C_ACCTBAL,
--     N_NAME,
--     C_ADDRESS,
--     C_PHONE,
--     C_COMMENT
-- FROM
--     CUSTOMER,
--     INNER JOIN ORDERS ON C_CUSTKEY = O_CUSTKEY,
--    INNER JOIN LINEITEM ON O_ORDERKEY = L_ORDERKEY,
--     INNER JOIN NATION ON C_NATIONKEY = N_NATIONKEY
-- WHERE
--     O_ORDERDATE >=  '1993-10-01'
--     AND O_ORDERDATE <  '1994-01-01'
--     AND L_RETURNFLAG = 'R'
-- GROUP BY
--     C_CUSTKEY,
--     C_NAME,
--     C_ACCTBAL,
--     C_PHONE,
--     N_NAME,
--     C_ADDRESS,
--     C_COMMENT
-- ORDER BY
--     REVENUE DESC;


-- 库存价值查询
-- TPC-H/TPC-R Inventory Value Query (Q11)
-- SELECT
--     PS_PARTKEY,
--     SUM(PS_SUPPLYCOST * PS_AVAILQTY) AS VALUE
-- FROM
--     PARTSUPP,
--     INNER JOIN SUPPLIER ON S_SUPPKEY = PS_SUPPKEY,
--     INNER JOIN NATION ON S_NATIONKEY = N_NATIONKEY
-- WHERE
--     N_NAME = '[NATION]'
-- GROUP BY
--     PS_PARTKEY HAVING
--         SUM(PS_SUPPLYCOST * PS_AVAILQTY) > (
--             SELECT
--                 SUM(PS_SUPPLYCOST * PS_AVAILQTY) * [FRACTION]
--             FROM
--                 PARTSUPP,
--                INNER JOIN SUPPLIER ON S_SUPPKEY = PS_SUPPKEY,
--                INNER JOIN NATION ON S_NATIONKEY = N_NATIONKEY
--             WHERE
--                 N_NAME = '[NATION]'
--         )
-- ORDER BY
--     VALUE DESC;

-- 运送方式和订单优先级查询
-- TPC-H/TPC-R Shipping Modes and Order Priority Query (Q12)
-- SELECT
--     L_SHIPMODE,
--     SUM(CASE WHEN O_ORDERPRIORITY = '1-URGENT'
--             OR O_ORDERPRIORITY = '2-HIGH'
--             THEN 1
--         ELSE 0
--     END) AS HIGH_LINE_COUNT,
--     SUM(CASE WHEN O_ORDERPRIORITY <> '1-URGENT'
--             AND O_ORDERPRIORITY <> '2-HIGH'
--             THEN 1
--         ELSE 0
--     END) AS LOW_LINE_COUNT
-- FROM
--     ORDERS,
--     INNER JOIN LINEITEM ON O_ORDERKEY = L_ORDERKEY
-- WHERE
--     L_SHIPMODE IN ('[SHIPMODE1]', '[SHIPMODE2]')
--     AND L_COMMITDATE < L_RECEIPTDATE
--     ANDL_SHIPDATE < L_COMMITDATE
--     AND L_RECEIPTDATE >= '1994-01-01'
--     AND L_RECEIPTDATE < '1995-01-01'
-- GROUP BY
--     L_SHIPMODE
-- ORDER BY
--     L_SHIPMODE; 

-- 客户分布查询
-- TPC-H/TPC-R Customer Distribution Query (Q13)
-- SELECT
--     C_COUNT,
--     COUNT(*) AS CUSTDIST
-- FROM
--     (
--         SELECT
--             C_CUSTKEY,
--             COUNT(O_ORDERKEY)
--         FROM
--             CUSTOMER LEFT OUTER JOIN ORDERS ON C_CUSTKEY = O_CUSTKEY
--              AND O_COMMENT NOT LIKE '%[WORD1]%[WORD2]%'
                            -- WORD1 为以下四个可能值中任意一个：special、pending、unusual、express
			                -- WORD2 为以下四个可能值中任意一个：packages、requests、accounts、deposits
--         GROUP BY
--             C_CUSTKEY
--     ) AS C_ORDERS (C_CUSTKEY, C_COUNT)
-- GROUP BY
--     C_COUNT
-- ORDER BY
--     CUSTDIST DESC,
--     C_COUNT DESC;

-- 促销效果查询
-- TPC-H/TPC-R Promotion Effect Query (Q14)
-- SELECT
--     100.00 * SUM(CASE
--         WHEN P_TYPE LIKE 'PROMO%'
--             THEN L_EXTENDEDPRICE * (1 - L_DISCOUNT)
--         ELSE 0
--     END) / SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) AS PROMO_REVENUE
-- FROM
--     LINEITEM,
--     INNER JOIN PART ON L_PARTKEY = P_PARTKEY
-- WHERE
--     L_SHIPDATE >= '1995-09-01'
--     AND L_SHIPDATE < '1995-10-01';

-- 顶级供应商查询
-- TPC-H/TPC-R Top Supplier Query (Q15)
-- CREATE VIEW REVENUE[STREAM_ID](SUPPLIER_NO, TOTAL_REVENUE) AS
-- SELECT
--     L_SUPPKEY,
--     SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT))
-- FROM
--     LINEITEM
-- WHERE
--     L_SHIPDATE >= '1996-01-01'
--     AND L_SHIPDATE < '1996-04-01'
-- GROUP BY
--     L_SUPPKEY
-- 查询语句
-- SELECT
--     S_SUPPKEY,
--     S_NAME,
--     S_ADDRESS,
--     S_PHONE,
--     TOTAL_REVENUE
-- FROM
--     SUPPLIER,
--     REVENUE[STREAM_ID]
-- WHERE
--     S_SUPPKEY = SUPPLIER_NO
--     AND TOTAL_REVENUE = (
--         SELECT
--             MAX(TOTAL_REVENUE)
--         FROM
--             REVENUE[STREAM_ID]
--     )
-- ORDER BY
--     S_SUPPKEY;

-- 零部件/供货商关系查询
-- TPC-H/TPC-R Parts/Supplier Relationship Query (Q16)
-- SELECT
--     P_BRAND,
--     P_TYPE,
--     P_SIZE,
--     COUNT(DISTINCT PS_SUPPKEY) AS SUPPLIER_CNT
-- FROM
--     PARTSUPP,
--     INNER JOIN PART ON P_PARTKEY = PS_PARTKEY
-- WHERE
--     P_BRAND <> '[BRAND]'
--                          BRAND＝Brand＃MN ，M和N是两个字母，代表两个数值，相互独立，取值在1到5之间
--     AND P_TYPE NOT LIKE '[TYPE]%' 
--                          消费者不感兴趣的类型和尺寸
--     AND P_SIZE IN ([SIZE1], [SIZE2], [SIZE3], [SIZE4], [SIZE5], [SIZE6], [SIZE7], [SIZE8])  TYPEX是在1到50之间任意选择的一组八个不同的值
--     AND PS_SUPPKEY NOT IN (
--         SELECT
--             S_SUPPKEY
--         FROM
--            SUPPLIER
--         WHERE
--             S_COMMENT LIKE '%Customer%Complaints%'
--     )
-- GROUP BY
--     P_BRAND,
--     P_TYPE,
--     P_SIZE
-- ORDER BY
--     SUPPLIER_CNT DESC,
--     P_BRAND,
--     P_TYPE,
--     P_SIZE;


-- 小额订单收入查询
-- TPC-H/TPC-R Small-Quantity-Order Revenue Query (Q17)
-- SELECT
--     SUM(L_EXTENDEDPRICE) / 7.0 AS AVG_YEARLY
-- FROM
--     LINEITEM,
--     INNER JOIN PART ON P_PARTKEY = L_PARTKEY
-- WHERE
--       P_BRAND = '[BRAND]'  '[BRAND]' /*指定品牌。 BRAND＝’Brand#MN’ ，M和N是两个字母，代表两个数值，相互独立，取值在1到5之间 */
--     AND P_CONTAINER = '[CONTAINER]'  //指定包装类型。在TPC-H标准指定的范围内随机选择
--     AND L_QUANTITY < (
--         SELECT
--             0.2 * AVG(L_QUANTITY)
--         FROM
--             LINEITEM
--         WHERE
--             L_PARTKEY = P_PARTKEY
-- );

-- 大批量客户查询
-- TPC-H/TPC-R Large Volume Customer Query (Q18)
-- SELECT
--     C_NAME,
--     C_CUSTKEY,
--     O_ORDERKEY,
--     O_ORDERDATE,
--     O_TOTALPRICE,
--     SUM(L_QUANTITY)
-- FROM
--     CUSTOMER,
--     INNER JOIN ORDERS ON C_CUSTKEY = O_CUSTKEY
--     INNER JOIN LINEITEM ON O_ORDERKEY= L_ORDERKEY
-- WHERE
--     O_ORDERKEY IN (
--         SELECT
--             L_ORDERKEY
--         FROM
--             LINEITEM
--         GROUP BY
--             L_ORDERKEY
--         HAVING
--             SUM(L_QUANTITY) > [QUANTITY]
--     )
-- GROUP BY
--     C_NAME,
--     C_CUSTKEY,
--     O_ORDERKEY,
--     O_ORDERDATE,
--     O_TOTALPRICE
-- ORDER BY
--     O_TOTALPRICE DESC,
--     O_ORDERDATE;

-- 折扣收入查询
-- TPC-H/TPC-R Discounted Revenue Query (Q19)
-- SELECT
--     SUM(L_EXTENDEDPRICE * (1 - L_DISCOUNT)) AS REVENUE
-- FROM
--     LINEITEM,
--     INNER JOIN PART ON P_PARTKEY = L_PARTKEY
-- WHERE (
--     P_BRAND = '[BRAND]'  //指定品牌。 BRAND＝’Brand#MN’ ，M和N是两个字母，代表两个数值，相互独立，取值在1到5之间
--     AND P_CONTAINER IN ('SM CASE', 'SM BOX', 'SM PACK', 'SM PKG')  //指定包装类型。在TPC-H标准指定的范围内随机选择
--     AND L_QUANTITY >= [QUANTITY1] AND L_QUANTITY <= [QUANTITY2]  //指定数量范围。在TPC-H标准指定的范围内随机选择
--     AND P_SIZE BETWEEN 1 AND 5  //指定尺寸范围。在TPC-H标准指定的范围内随机选择
--     AND L_SHIPMODE IN ('AIR', 'AIR REG')  //指定运输方式。在TPC-H标准指定的范围内随机选择
--     AND L_SHIPINSTRUCT = 'DELIVER IN PERSON'  //指定运输指令。在TPC-H标准指定的范围内随机选择
--     ) OR (
--     P_BRAND = '[BRAND]'  //指定品牌。 BRAND＝’Brand#MN’ ，M和N是两个字母，代表两个数值，相互独立，取值在1到5之间
--     AND P_CONTAINER IN ('MED BAG', 'MED BOX', 'MED PKG', 'MED PACK')  //指定包装类型。在TPC-H标准指定的范围内随机选择
--     AND L_QUANTITY >= [QUANTITY2] AND L_QUANTITY <= [QUANTITY2] + 10 //指定数量范围。在TPC-H标准指定的范围内随机选择
--     AND P_SIZE BETWEEN 1 AND 10  //指定尺寸范围。在TPC-H标准指定的范围内随机选择
--     AND L_SHIPMODE IN ('AIR', 'AIR REG')  //指定运输方式。在TPC-H标准指定的范围内随机选择
--     AND L_SHIPINSTRUCT = 'DELIVER IN PERSON'  //指定运输指令。在TPC-H标准指定的范围内随机选择
--     ) OR (
--     P_BRAND = '[BRAND]'  //指定品牌。 BRAND＝’Brand#MN’ ，M和N是两个字母，代表两个数值，
--     P_CONTAINER IN ('LG CASE', 'LG BOX', 'LG PACK', 'LG PKG')  //指定包装类型。在TPC-H标准指定的范围内随机选择
--     AND L_QUANTITY >= [QUANTITY3] AND L_QUANTITY <= [QUANTITY3] + 10 //指定数量范围。在TPC-H标准指定的范围内随机选择
--     AND P_SIZE BETWEEN 1 AND 15  //指定尺寸范围。在TPC-H标准指定的范围内随机选择
--     AND L_SHIPMODE IN ('AIR', 'AIR REG')  //指定运输方式。在TPC-H标准指定的范围内随机选择
--     AND L_SHIPINSTRUCT = 'DELIVER IN PERSON'  //指定运输指令。在TPC-H标准指定的范围内随机选择
-- )

-- 潜在零部件促销查询
-- TPC-H/TPC-R Potential Part Promotion Query (Q20)
-- SELECT
--     S_NAME,
--     S_ADDRESS
-- FROM
--     SUPPLIER,
--     INNER JOIN NATION ON S_NATIONKEY = N_NATIONKEY
-- WHERE
--     S_SUPPKEY IN (
--         SELECT
--             PS_SUPPKEY
--         FROM
--             PARTSUPP
--         WHERE
--             PS_PARTKEY IN (
--                 SELECT
--                     P_PARTKEY
--                 FROM
--                     PART
--                 WHERE
--                     P_NAME LIKE '[PATTERN]'  //指定零件名称。在TPC-H标准指定的范围内随机选择
--             )
--             AND PS_AVAILQTY > (
--                 SELECT
--                     0.5 * SUM(L_QUANTITY)
--                 FROM
--                     LINEITEM
--                 WHERE
--                     L_PARTKEY = PS_PARTKEY
--                     AND L_SUPPKEY = PS_SUPPKEY
--                     AND L_SHIPDATE >= '[DATE]' //指定日期。在TPC-H标准指定的范围内随机选择
--                     AND L_SHIPDATE < '[DATE]'  + INTERVAL '1' YEAR
--             )
--     )
--     AND N_NAME = '[NATION]'  //指定国家。在TPC-H标准指定的范围内随机选择
-- ORDER BY
--     S_NAME;

-- 供应商留单等待查询
-- TPC-H/TPC-R Supplier Waiting to Deliver Orders Query (Q21)
-- SELECT
--     S_NAME,
--     COUNT(*) AS NUMWAIT
-- FROM
--     SUPPLIER,
--     INNER JOIN LINEITEM L1 ON S_SUPPKEY = L1.L_SUPPKEY
--     INNER JOIN ORDERS ON L1.L_ORDERKEY = O_ORDERKEY
--     INNER JOIN NATION ON S_NATIONKEY = N_NATIONKEY
-- WHERE
--     O_ORDERSTDATE = 'F'
--     AND L1.L_RECEIPTDATE > L1.L_COMMITDATE
--     AND EXISTS (
--         SELECT
--             *
--         FROM
--             LINEITEM L2
--         WHERE
--             L2.L_ORDERKEY = L1.L_ORDERKEY
--             AND L2.L_SUPPKEY <> L1.L_SUPPKEY
--     )
--     AND NOT EXISTS (
--         SELECT
--             *
--         FROM
--             LINEITEM L3
--        WHERE
--             L3.L_ORDERKEY = L1.L_ORDERKEY
--             AND L3.L_SUPPKEY <> L1.L_SUPPKEY
--             AND L3.L_RECEIPTDATE > L3.L_COMMITDATE
--     )
--     AND N_NAME = '[NATION]'  //指定国家。在TPC-H标准指定的范围内随机选择
-- GROUP BY
--     S_NAME
-- ORDER BY
--     NUMWAIT DESC,
--     S_NAME;

-- 全球销售机会查询
-- TPC-H/TPC-R Global Sales Opportunity Query (Q22)
-- SELECT
--     CNTRYCODE,
--     COUNT(*) AS NUMCUST,
--     SUM(C_ACCTBAL) AS TOTACCTBAL
-- FROM
--     (
--         SELECT
--             SUBSTRING(C_PHONE, 1, 2) AS CNTRYCODE,
--             C_ACCTBAL,
--         FROM
--             CUSTOMER
--         WHERE
----           I1…I7是在TPC-H中定义国家代码的可能值中不重复的任意值
--             SUBSTRING(C_PHONE, 1, 2) IN ('[COUNTRY1]', '[COUNTRY2]', '[COUNTRY3]', '[COUNTRY4]', '[COUNTRY5]', '[COUNTRY6]', '[COUNTRY7]')
--            AND C_ACCTBAL > (
--                 SELECT
--                     AVG(C_ACCTBAL)
--                 FROM
--                     CUSTOMER
--                 WHERE
--                     C_ACCTBAL > 0.00
--                     AND SUBSTRING(C_PHONE, 1, 2) IN ('[COUNTRY1]', '[COUNTRY2]', '[COUNTRY3]', '[COUNTRY4]', '[COUNTRY5]', '[COUNTRY6]', '[COUNTRY7]')
--             )
--             AND NOT EXISTS (
--                 SELECT
--                     *
--                 FROM
--                     ORDERS
--                 WHERE
--                     O_CUSTKEY = C_CUSTKEY
--             )
--     ) AS CUSTSALES
-- GROUP BY
--     CNTRYCODE
-- ORDER BY
--     CNTRYCODE;
