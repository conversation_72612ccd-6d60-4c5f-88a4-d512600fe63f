#include "tpch.h"
#include "dbsqlitetest.h"
// #include <bits/time.h> // 如果需要，可以尝试包含这个头文件
// #include "common.h"


// FILE* resFp;
lxw_workbook *workbook;
lxw_worksheet *main_worksheet;
lxw_worksheet *tpch_worksheet;
lxw_worksheet *tpcc_worksheet;
lxw_worksheet *pertest_worksheet;
lxw_worksheet *conpertest_worksheet;


int WriteToCSVFlag = 0;

char tpchpath[] = "./testfile/datafile/tpcc/";
char regionFileName[] = "region.tbl";
char nationFileName[] = "nation.tbl";
char supplierFileName[] = "supplier.tbl";
char partFileName[] = "part.tbl";
char partsuppFileName[] = "partsupp.tbl";
char customerFileName[] = "customer.tbl";
char ordersFileName[] = "orders.tbl";
char lineitemFileName[] = "lineitem.tbl";

unsigned long timeCost = 0;
unsigned long timeCostProject = 0;
extern int myCallBack(void *NotUsed, int argc, char** azColName, char** argv);

int sqliteprintHeader = 1;

void initTpchFlagSqlite(){
	sqliteprintHeader = 1;
}

int  sqlitemyCallBack(void *data, int argc, char **argv, char **azColName)
{
  int i;
//   cnt++;
  // 如果printHeader为1，则打印列名
  if (sqliteprintHeader) {
    for (i = 0; i < argc; i++) {
      printf("%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
    }
    printf("\n");
    sqliteprintHeader = 0;  // 设置为0，下次调用时不再打印列名 // 初始化循环变量i为0
  }  // 初始化返回码rc为0
  for (i = 0; i < argc; i++) {
    printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");  // 初始化全局变量SAMETABLE为0
  }  // 从10开始循环，直到i小于MULTI_TESTNUM
  printf("\n");
  return 0;  // 如果i等于5
}

void remove_extra_spaces(char *str) {
    int i = 0, j = 0;
    int len = strlen(str);

    // 跳过字符串开头的空格和制表符
    while (i < len && (str[i] == ' ' || str[i] == '\t')) {
        i++;
    }

    // 遍历字符串，处理中间的空格
    while (i < len) {
		if (str[i] == '\t') {
            // 如果当前字符是制表符，替换为一个空格
            str[j++] = ' ';
        } else if (str[i] != ' ' || (i > 0 && str[i - 1] != ' ')) {
            str[j++] = str[i];
        }
        i++;
    }

    // 处理字符串末尾的空格
    if (j > 0 && str[j - 1] == ' ') {
        j--;
    }

    // 添加字符串结束符
    str[j] = '\0';
}


int ExecuteSQLUseFile(GNCDB* db, sqlite3 * sqlite_db, char* fileName){
    // int cnt = 0;
    FILE *file;
    char *line = NULL;
    size_t len = 0;
    ssize_t read;
    char *current_sql = NULL;
    struct timespec st, ed;
    long cost_nano;
    double total_time = 0;
	long cost_nano_sqlite;
    double total_time_sqlite = 0;
    char *temp = NULL;
	// int jumpFlag = 0;

	int xEx = 2;
	int yEx = 0;
	int rc = 0;
	lxw_format *title_format;
	lxw_format *title_format1;

	char csvMessage[64] = {0};

	char *queryNum = NULL;
    // memset(current_sql, 0, 1);
    size_t current_sql_len = 0;

    file = fopen(fileName, "r");
    if (file == NULL) {
        perror("打开文件时出错");
        return 1;
    }

    while ((read = getline(&line, &len, file)) != -1) {
        // 忽略以"--"开头的行
        if (strncmp(line, "--", 2) == 0){
			queryNum = strstr(line, "Query");
			if(queryNum != NULL){
				printf("Query %s\n", queryNum);
				if(WriteToCSVFlag){
					// if(jumpFlag){
					// 	fwrite("\n", 1, 1, resFp);
					// }
					// fwrite(queryNum, 1, strlen(queryNum) - 1, resFp);

					xEx++;
					yEx = 0;
					
					title_format  = workbook_add_format(workbook);
					format_set_border(title_format, LXW_BORDER_THIN);
					title_format1  = workbook_add_format(workbook);
					format_set_border(title_format1, LXW_BORDER_THIN);
					format_set_font_size(title_format, 16);
					format_set_align(title_format, LXW_ALIGN_CENTER);
					format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
					format_set_font_size(title_format1, 16);
					format_set_align(title_format1, LXW_ALIGN_RIGHT);
					format_set_align(title_format1, LXW_ALIGN_VERTICAL_CENTER);
					format_set_num_format(title_format1, "#,##0.00");
					worksheet_write_string(tpch_worksheet, xEx, yEx++, queryNum, title_format);
					worksheet_set_row(tpch_worksheet, xEx, 30, NULL);

					
					// jumpFlag = 1;
				}
			}
            continue;
		}
        // 移除行尾的换行符
        if (line[read - 1] == '\n') {
            line[read - 1] = '\0';
            --read;
        }

        // 为 current_sql 分配或重新分配内存
        temp = my_realloc(current_sql, current_sql_len + read + 2);
        if (!temp) {
            perror("内存分配失败");
            my_free(line);
            my_free(current_sql);
            fclose(file);
            return GNCDB_MEM;
        }
        current_sql = temp;

        // 将行添加到 current_sql
        memmove(current_sql+current_sql_len, line, strlen(line));
        memmove(current_sql+current_sql_len+strlen(line), " ", 1);
        current_sql_len += read + 1;
        current_sql[current_sql_len] = '\0';
        // 检查行是否以分号结尾
        if (line[read - 1] == ';') {
            // printf("%s\n\n", current_sql); // 打印 SQL 语句并换行
			remove_extra_spaces(current_sql);

			initTpchFlag();
            clock_gettime(CLOCK_MONOTONIC, &st);
            GNCDB_exec(db, current_sql,  tpchCallBack, NULL, NULL); // 执行 SQL 语句
            clock_gettime(CLOCK_MONOTONIC, &ed);
            cost_nano = (ed.tv_sec - st.tv_sec) * 1000000000L + (ed.tv_nsec - st.tv_nsec);


			initTpchFlagSqlite();
			clock_gettime(CLOCK_MONOTONIC, &st);
			rc = sqlite3_exec(sqlite_db, current_sql, sqlitemyCallBack, NULL, NULL);
			clock_gettime(CLOCK_MONOTONIC, &ed);
			cost_nano_sqlite = (ed.tv_sec - st.tv_sec) * 1000000000L + (ed.tv_nsec - st.tv_nsec);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec error: %s\n", sqlite3_errmsg(sqlite_db));
			}

            printf("NO.%s,\n cost %f s\n",current_sql,  cost_nano / 1000000000.0);

			if(WriteToCSVFlag){
				double num = (double)cost_nano/cost_nano_sqlite;
				// jumpFlag = 0;
				sprintf(csvMessage, "%fs", cost_nano / 1000000000.0);
				// fwrite(csvMessage, 1, strlen(csvMessage), resFp);
				worksheet_write_string(tpch_worksheet, xEx, yEx++, current_sql, title_format);
				worksheet_write_string(tpch_worksheet, xEx, yEx++, csvMessage, title_format);
				sprintf(csvMessage, "%fs", cost_nano_sqlite / 1000000000.0);
				worksheet_write_string(tpch_worksheet, xEx, yEx++, csvMessage, title_format);

				worksheet_write_number(tpch_worksheet, xEx, yEx, num, title_format1);
				// fflush(resFp);
			}



            total_time += (cost_nano / 1000000000.0);
			total_time_sqlite += (cost_nano_sqlite / 1000000000.0);
            my_free(current_sql); // 释放当前 SQL 语句的内存
            current_sql = NULL;
            current_sql_len = 0;
        }
    }
    my_free(line);
    fclose(file);
    printf("total time %f s\n", total_time);

	if(WriteToCSVFlag){
		sprintf(csvMessage, "TPCH query total time:");
		++xEx;
		yEx = 0;
		worksheet_write_string(tpch_worksheet, xEx, yEx, csvMessage, title_format);
		yEx++;
		worksheet_write_string(tpch_worksheet, xEx, yEx, "  ", title_format);
		yEx++;
		sprintf(csvMessage, "%fs", total_time);
		worksheet_write_string(tpch_worksheet, xEx, yEx, csvMessage, title_format);
		yEx++;
		sprintf(csvMessage, "%fs", total_time_sqlite);
		worksheet_write_string(tpch_worksheet, xEx, yEx, csvMessage, title_format);
		yEx++;
		worksheet_write_number(tpch_worksheet, xEx, yEx, total_time/total_time_sqlite, title_format1);
		worksheet_set_row(tpch_worksheet, xEx, 30, NULL);
		// fwrite(csvMessage, 1, strlen(csvMessage), resFp);
	}

    return GNCDB_SUCCESS;
}

int tpcH1Test(){
    int rc = GNCDB_SUCCESS;
    GNCDB* db;
	sqlite3* sqlite_db;
	char pageSizeQuery[24];
	// char tmp_sql[1000];
	// int num = 150000; // Number of keys to generate
	// int keys[num];
	// struct timespec start, end;
	// long timeCost;
    int rows = 0;
    remove("tpch.dat");
    remove("log_tpch.dat");
	remove("sqlite_tpch.db");
	// if(resFp != NULL){
	// 	fwrite("TPCH 测试\n", 1, strlen("TPCH 测试\n"), resFp);
	// }
	rc = GNCDB_open(&db, "tpch.dat", 0, 0);
	if (rc != GNCDB_SUCCESS) {
		printf("Failed to open database rc = %d\n", rc);
		return 1;
	}

	rc = sqlite3_open("sqlite_tpch.db", &sqlite_db);
	if (rc != SQLITE_OK) {
		fprintf(stderr, "Can't open database: %s\n", sqlite3_errmsg(sqlite_db));
		return 1;
	}
	sprintf(pageSizeQuery, "PRAGMA page_size = %d", db->pagePoolCount);
	rc = sqlite3_exec(sqlite_db, pageSizeQuery, 0, 0, NULL);
	if (rc != SQLITE_OK) {
		printf("Error setting page size\n");
		return 1;
	}

    rc = ExecuteSQLUseFile(db, sqlite_db, "./test/tpc-h/tpch.ddl");

    GNCDB_exec(db, "select * from master;", tpchCallBack, NULL, NULL);
	sqlite3_exec(sqlite_db, "select * from sqlite_master;", sqlitemyCallBack, NULL, NULL);

    rc = tpchInsertTable(db, sqlite_db);
    if( rc != GNCDB_SUCCESS){
        printf("tpchInsertTable failed\n");
        return 1;
    }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/customer.tbl' INTO TABLE CUSTOMER;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data CUSTOMER failed\n");
	// }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/orders.tbl' INTO TABLE ORDERS;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data ORDERS failed\n");
	// }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/lineitem.tbl' INTO TABLE LINEITEM;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data LINEITEM failed\n");
	// }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/nation.tbl' INTO TABLE NATION;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data NATION failed\n");
	// }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/region.tbl' INTO TABLE REGION;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data REGION failed\n");
	// }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/part.tbl' INTO TABLE PART;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data PART failed\n");
	// }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/partsupp.tbl' INTO TABLE PARTSUPP;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data PARTSUPP failed\n");
	// }
	// rc = GNCDB_exec(db, "load data INFILE './testfile/datafile/tpcc/supplier.tbl' INTO TABLE SUPPLIER;", NULL, NULL, NULL);
	// if( rc != GNCDB_SUCCESS){
	// 	printf("load data SUPPLIER failed\n");
	// }

    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "CUSTOMER");
    printf("CUSTOMER rows = %d\n", rows);
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "ORDERS");
    printf("ORDERS rows = %d\n", rows);
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "LINEITEM");
    printf("LINEITEM rows = %d\n", rows);
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "NATION");
    printf("NATION rows = %d\n", rows);
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "REGION");
    printf("REGION rows = %d\n", rows);
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "PART");
    printf("PART rows = %d\n", rows);
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "PARTSUPP");
    printf("PARTSUPP rows = %d\n", rows);
    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "SUPPLIER");
    printf("SUPPLIER rows = %d\n", rows);

	// 查询sqlite中所有表的数行数
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM CUSTOMER;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM LINEITEM;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM NATION;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM ORDERS;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM PART;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM PARTSUPP;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM REGION;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	sqlite3_exec(sqlite_db, "SELECT count(*) FROM SUPPLIER;", sqlitemyCallBack, NULL, NULL);



	WriteToCSVFlag = 1;
    rc = ExecuteSQLUseFile(db, sqlite_db, "./test/tpc-h/query.sql");
	WriteToCSVFlag = 0;
    if( rc != GNCDB_SUCCESS){
        printf("executeSQLFile failed\n");
    }
    
    
    GNCDB_close(&db);
	sqlite3_close(sqlite_db);
    return rc;
}


int tpchInsertTable(GNCDB* db, sqlite3 * sqlite_db) 
{
	int rc = 0; // 用于存储函数返回值
	int i = 0; // 循环计数器
	int count = 0; // 用于存储fscanf读取的字段数
	FILE* fp = NULL; // 文件指针
    char regionPath[60] = { 0 };
    char nationPath[60] = { 0 };
    char supplierPath[60] = { 0 };
    char partPath[60] = { 0 };
    char partsuppPath[60] = { 0 };
    char customerPath[60] = { 0 };
    char ordersPath[60] = { 0 };
    char lineitemPath[60] = { 0 };

	// 查询所有表
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM region;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM nation;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM supplier;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM part;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM partsupp;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM customer;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM orders;", sqlitemyCallBack, NULL, NULL);
	initTpchFlagSqlite();
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM lineitem;", sqlitemyCallBack, NULL, NULL);






	/* region表 */
	sprintf(regionPath, "%s%s", tpchpath, regionFileName);
	fp = fopen(regionPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < REGIONROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|\n",
			&regiontbl.regionkey,
			regiontbl.name,
			regiontbl.comment);
		if (count != 3)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO region VALUES(%d,'%s','%s');", 
				regiontbl.regionkey, 
				regiontbl.name, 
				regiontbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else {
			rc = GNCDB_insert(db, NULL, "REGION",
				regiontbl.regionkey,
				regiontbl.name,
				regiontbl.comment);
		}

		{
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO region VALUES(%d,'%s','%s');", 
				regiontbl.regionkey, 
				regiontbl.name, 
				regiontbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}

		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	initTpchFlag();
	GNCDB_select(db, tpchCallBack, NULL, NULL, 1, 0, 0, "region");

	/* nation表 */
	sprintf(nationPath, "%s%s", tpchpath, nationFileName);
	fp = fopen(nationPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < NATIONROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%d|%[^|]|\n",
			&nationtbl.nationkey,
			nationtbl.name,
			&nationtbl.regionkey,
			nationtbl.comment);
		if (count != 4)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO nation VALUES(%d,'%s',%d,'%s');", 
				nationtbl.nationkey, 
				nationtbl.name, 
				nationtbl.regionkey, 
				nationtbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else { 
			rc = GNCDB_insert(db, NULL, "NATION",
				nationtbl.nationkey,
				nationtbl.name,
				nationtbl.regionkey,
				nationtbl.comment);
		}
		{
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO nation VALUES(%d,'%s',%d,'%s');", 
				nationtbl.nationkey, 
				nationtbl.name, 
				nationtbl.regionkey, 
				nationtbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	/* supplier表 */
	sprintf(supplierPath, "%s%s", tpchpath, supplierFileName);
	fp = fopen(supplierPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < SUPPLIERROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&suppliertbl.suppkey,
			suppliertbl.name,
			suppliertbl.address,
			&suppliertbl.nationkey,
			suppliertbl.phone,
			&suppliertbl.acctbal,
			suppliertbl.comment);
		if (count != 7)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO supplier VALUES(%d,'%s','%s',%d,'%s',%lf,'%s');", 
				suppliertbl.suppkey, 
				suppliertbl.name, 
				suppliertbl.address, 
				suppliertbl.nationkey, 
				suppliertbl.phone, 
				suppliertbl.acctbal, 
				suppliertbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else {
			rc = GNCDB_insert(db, NULL, "SUPPLIER",
				suppliertbl.suppkey,
				suppliertbl.name,
				suppliertbl.address,
				suppliertbl.nationkey,
				suppliertbl.phone,
				suppliertbl.acctbal,
				suppliertbl.comment);
		}
		{
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO supplier VALUES(%d,'%s','%s',%d,'%s',%lf,'%s');", 
				suppliertbl.suppkey, 
				suppliertbl.name, 
				suppliertbl.address, 
				suppliertbl.nationkey, 
				suppliertbl.phone, 
				suppliertbl.acctbal, 
				suppliertbl.comment
			);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}

		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	/* part表 */
	sprintf(partPath, "%s%s", tpchpath, partFileName);
	fp = fopen(partPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < PARTROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&parttbl.partkey,
			parttbl.name,
			parttbl.mfgr,
			parttbl.brand,
			parttbl.type,
			&parttbl.size,
			parttbl.container,
			&parttbl.retailprice,
			parttbl.comment);
		if (count != 9)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO part VALUES(%d,'%s','%s','%s','%s',%d,'%s',%lf,'%s');", 
				parttbl.partkey, 
				parttbl.name, 
				parttbl.mfgr, 
				parttbl.brand, 
				parttbl.type, 
				parttbl.size, 
				parttbl.container, 
				parttbl.retailprice, 
				parttbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else {
			rc = GNCDB_insert(db, NULL, "PART",
				parttbl.partkey,
				parttbl.name,
				parttbl.mfgr,
				parttbl.brand,
				parttbl.type,
				parttbl.size,
				parttbl.container,
				parttbl.retailprice,
				parttbl.comment);
		}
		{
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO part VALUES (%d,'%s','%s','%s','%s',%d,'%s',%lf,'%s');", 
				parttbl.partkey, 
				parttbl.name, 
				parttbl.mfgr, 
				parttbl.brand, 
				parttbl.type, 
				parttbl.size, 
				parttbl.container, 
				parttbl.retailprice, 
				parttbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	/* partsupp表 */
	sprintf(partsuppPath, "%s%s", tpchpath, partsuppFileName);
	fp = fopen(partsuppPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < PARTSUPPROWS; ++i)
	{
		count = fscanf(fp, "%d|%d|%d|%lf|%[^|]|\n",
			&partsupptbl.partkey,
			&partsupptbl.suppkey,
			&partsupptbl.availqty,
			&partsupptbl.supplycost,
			partsupptbl.comment);
		if (count != 5)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO partsupp VALUES(%d,%d,%d,%lf,'%s');", 
				partsupptbl.partkey, 
				partsupptbl.suppkey, 
				partsupptbl.availqty, 
				partsupptbl.supplycost, 
				partsupptbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else {
			rc = GNCDB_insert(db, NULL, "PARTSUPP",
				partsupptbl.partkey,
				partsupptbl.suppkey,
				partsupptbl.availqty,
				partsupptbl.supplycost,
				partsupptbl.comment);
		}
		{
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO partsupp VALUES(%d,%d,%d,%lf,'%s');", 
				partsupptbl.partkey, 
				partsupptbl.suppkey, 
				partsupptbl.availqty, 
				partsupptbl.supplycost, 
				partsupptbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	/* customer表 */
	sprintf(customerPath, "%s%s", tpchpath, customerFileName);
	// 查询sqlite的customer表
	rc = sqlite3_exec(sqlite_db, "SELECT * FROM customer;", sqlitemyCallBack, NULL, NULL);
	fp = fopen(customerPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < CUSTOMERROWS; ++i)
	{
		count = fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|%[^|]|\n",
			&customertbl.custkey,
			customertbl.name,
			customertbl.address,
			&customertbl.nationkey,
			customertbl.phone,
			&customertbl.acctbal,
			customertbl.mktsegment,
			customertbl.comment);
		if (count != 8)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO customer VALUES(%d,'%s','%s',%d,'%s',%lf,'%s','%s');", 
				customertbl.custkey, 
				customertbl.name, 
				customertbl.address, 
				customertbl.nationkey, 
				customertbl.phone, 
				customertbl.acctbal, 
				customertbl.mktsegment, 
				customertbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else {
			rc = GNCDB_insert(db, NULL, "CUSTOMER",
				customertbl.custkey,
				customertbl.name,
				customertbl.address,
				customertbl.nationkey,
				customertbl.phone,
				customertbl.acctbal,
				customertbl.mktsegment,
				customertbl.comment);
		}
		{
			char sql[1024] = { 0 };
			char *errMsg = NULL;
			sprintf(sql, "INSERT INTO customer VALUES(%d,'%s','%s',%d,'%s',%lf,'%s','%s');", 
				customertbl.custkey, 
				customertbl.name, 
				customertbl.address, 
				customertbl.nationkey, 
				customertbl.phone, 
				customertbl.acctbal, 
				customertbl.mktsegment, 
				customertbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, &errMsg);
			if(rc != SQLITE_OK){
				printf("errMsg:%s\n", errMsg);
				sqlite3_free(errMsg);
				printf("sqlite3_exec failed\n");
				return rc;
			}

		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	/* orders表 */
	sprintf(ordersPath, "%s%s", tpchpath, ordersFileName);
	fp = fopen(ordersPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < ORDERSROWS; ++i)
	{
		count = fscanf(fp, "%d|%d|%[^|]|%lf|%[^|]|%[^|]|%[^|]|%d|%[^|]|\n",
			&orderstbl.orderkey,
			&orderstbl.custkey,
			orderstbl.orderstatus,
			&orderstbl.totalprice,
			orderstbl.orderdate,
			orderstbl.orderpriority,
			orderstbl.clerk,
			&orderstbl.shippriority,
			orderstbl.comment);
		if (count != 9)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO orders VALUES(%d,%d,'%s',%lf,'%s','%s','%s',%d,'%s');", 
				orderstbl.orderkey, 
				orderstbl.custkey, 
				orderstbl.orderstatus, 
				orderstbl.totalprice, 
				orderstbl.orderdate, 
				orderstbl.orderpriority, 
				orderstbl.clerk, 
				orderstbl.shippriority,
				orderstbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else {
			rc = GNCDB_insert(db, NULL, "ORDERS",
				orderstbl.orderkey,
				orderstbl.custkey,
				orderstbl.orderstatus,
				orderstbl.totalprice,
				orderstbl.orderdate,
				orderstbl.orderpriority,
				orderstbl.clerk,
				orderstbl.shippriority,
				orderstbl.comment);
		}
		{
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO orders VALUES(%d,%d,'%s',%lf,'%s','%s','%s',%d,'%s');", 
				orderstbl.orderkey, 
				orderstbl.custkey, 
				orderstbl.orderstatus, 
				orderstbl.totalprice, 
				orderstbl.orderdate, 
				orderstbl.orderpriority, 
				orderstbl.clerk, 
				orderstbl.shippriority,
				orderstbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	/* lineitem表 */
	sprintf(lineitemPath, "%s%s", tpchpath, lineitemFileName);
	fp = fopen(lineitemPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < LINEITEMROWS; ++i)
	{
		count = fscanf(fp, "%d|%d|%d|%d|%lf|%lf|%lf|%lf|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|%[^|]|\n",
			&lineitemtbl.orderkey,
			&lineitemtbl.partkey,
			&lineitemtbl.suppkey,
			&lineitemtbl.linenumber,
			&lineitemtbl.quantity,
			&lineitemtbl.extendedpeice,
			&lineitemtbl.discount,
			&lineitemtbl.tax,
			lineitemtbl.returnflag,
			lineitemtbl.linestatus,
			lineitemtbl.shipdate,
			lineitemtbl.commitdate,
			lineitemtbl.receiptdate,
			lineitemtbl.shipinstruct,
			lineitemtbl.shipmode,
			lineitemtbl.comment);
		if (count != 16)
		{

		}
		if(PERSQLTEST){
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO lineitem VALUES(%d,%d,%d,%d,%lf,%lf,%lf,%lf,'%s','%s','%s','%s','%s','%s','%s','%s');", 
				lineitemtbl.orderkey, 
				lineitemtbl.partkey, 
				lineitemtbl.suppkey, 
				lineitemtbl.linenumber, 
				lineitemtbl.quantity, 
				lineitemtbl.extendedpeice,
				lineitemtbl.discount,
				lineitemtbl.tax,
				lineitemtbl.returnflag,
				lineitemtbl.linestatus,
				lineitemtbl.shipdate,
				lineitemtbl.commitdate,
				lineitemtbl.receiptdate,
				lineitemtbl.shipinstruct,
				lineitemtbl.shipmode,
				lineitemtbl.comment);
			rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
		} else {
			rc = GNCDB_insert(db, NULL, "LINEITEM",
				lineitemtbl.orderkey,
				lineitemtbl.partkey,
				lineitemtbl.suppkey,
				lineitemtbl.linenumber,
				lineitemtbl.quantity,
				lineitemtbl.extendedpeice,
				lineitemtbl.discount,
				lineitemtbl.tax,
				lineitemtbl.returnflag,
				lineitemtbl.linestatus,
				lineitemtbl.shipdate,
				lineitemtbl.commitdate,
				lineitemtbl.receiptdate,
				lineitemtbl.shipinstruct,
				lineitemtbl.shipmode,
				lineitemtbl.comment);
		}
		{
			char sql[1024] = { 0 };
			sprintf(sql, "INSERT INTO lineitem VALUES (%d,%d,%d,%d,%lf,%lf,%lf,%lf,'%s','%s','%s','%s','%s','%s','%s','%s');", 
				lineitemtbl.orderkey, 
				lineitemtbl.partkey, 
				lineitemtbl.suppkey, 
				lineitemtbl.linenumber, 
				lineitemtbl.quantity, 
				lineitemtbl.extendedpeice,
				lineitemtbl.discount,
				lineitemtbl.tax,
				lineitemtbl.returnflag,
				lineitemtbl.linestatus,
				lineitemtbl.shipdate,
				lineitemtbl.commitdate,
				lineitemtbl.receiptdate,
				lineitemtbl.shipinstruct,
				lineitemtbl.shipmode,
				lineitemtbl.comment);
			rc = sqlite3_exec(sqlite_db, sql, NULL, NULL, NULL);
			if(rc != SQLITE_OK){
				printf("sqlite3_exec failed\n");
				return rc;
			}
		}
		if (rc != GNCDB_SUCCESS)
		{
			fclose(fp);
			return rc;
		}
	}
	fclose(fp);

	return GNCDB_SUCCESS;
}

int tpchFlag = 0;

void initTpchFlag()
{
	tpchFlag = 0;
}

int tpchCallBack(void *NotUsed, int columnNum, char** fieldName, char** fieldValue)
{
	if (tpchFlag == 0)
	{
		//printf("查询出的记录：\n");
		printf("\n");
		printf("|");
		for (int i = 0; i < columnNum; i++)
		{
			printf("%16s|", fieldName[i]);
		}
		printf("\n");
		for (int i = 0; i <= columnNum * 17; ++i)
		{
			printf("-");
		}
		printf("\n");
	}

	printf("|");
	for (int i = 0; i < columnNum; i++)
	{
		printf("%16s|", fieldValue[i]);
	}
	printf("\n");

	tpchFlag = 1;

	return 0;
}