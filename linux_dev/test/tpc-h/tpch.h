#ifndef TPCH1_H
#define TPCH1_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <time.h> // 确保包含 <time.h> 头文件

#include "../testdependlib/sqlitelib/sqlite3.h"
#include "gncdb.h"
#include "gncdbconstant.h"
#include "xlsxwriter.h"
// #include "dbsqlitetest.h"


#define PERSQLTEST  0

#define SF					0.01
#define REGIONROWS			20					/* 5 */
#define NATIONROWS			25					/* 25 */
#define SUPPLIERROWS		10000*SF			/* 10000 */
#define PARTROWS			200000*SF			/* 200000 */
#define PARTSUPPROWS		800000*SF			/* 800000 */
#define CUSTOMERROWS		150000*SF			/* 150000 */
#define ORDERSROWS			1500000*SF			/* 1500000 */
#define LINEITEMROWS		6000000*SF			/* 6000000 */

// extern FILE* resFp;
extern lxw_workbook *workbook;

extern lxw_worksheet *main_worksheet;
extern lxw_worksheet *tpch_worksheet;
extern lxw_worksheet *tpcc_worksheet;
extern lxw_worksheet *pertest_worksheet;
extern lxw_worksheet *conpertest_worksheet;


extern char tpchpath[];
extern char regionFileName[];
extern char nationFileName[];
extern char supplierFileName[];
extern char partFileName[];
extern char partsuppFileName[];
extern char customerFileName[];
extern char ordersFileName[];
extern char lineitemFileName[];





int tpcH1Test();

int tpchInsertTable(GNCDB* db, sqlite3 * sqlite_db);


void initTpchFlag();
int tpchCallBack(void *NotUsed,int columnNum, char** fieldName, char** fieldValue);

int ExecuteSQLUseFile(GNCDB* db, sqlite3 * sqlite_db, char* fileName);

void remove_extra_spaces(char *str);

/* ================================region表================================== */
typedef struct {
	int regionkey;
	char name[25];
	char comment[152];
	/*double supplycost;
	char pscomment[199];*/
}REGION;

/* ================================nation表================================== */
typedef struct {
	int nationkey;
	char name[25];
	int regionkey;
	char comment[152];
}NATION;

/* ================================supplier表================================== */
typedef struct {
	int suppkey;
	char name[25];
	char address[40];
	int nationkey;
	char phone[15];
	double acctbal;
	char comment[101];
}SUPPLIER;

/* ================================part表================================== */
typedef struct {
	int partkey;
	char name[55];
	char mfgr[25];
	char brand[10];
	char type[25];
	int size;
	char container[10];
	double retailprice;
	char comment[23];
}PART;

/* ================================partsupp表================================== */
typedef struct {
	int partkey;
	int suppkey;
	int availqty;
	double supplycost;
	char comment[199];
}PARTSUPP;

/* ================================customer表================================== */
typedef struct {
	int custkey;
	char name[25];
	char address[40];
	int nationkey;
	char phone[15];
	double acctbal;
	char mktsegment[12];
	char comment[117];
}CUSTOMER;

/* ================================orders表================================== */
typedef struct {
	int orderkey;
	int custkey;
	char orderstatus[2];
	double totalprice;
	char orderdate[12];
	char orderpriority[16];
	char clerk[15];
	int shippriority;
	char comment[79];
}ORDERS;

/* ================================lineitem表================================== */
typedef struct {
	int orderkey;
	int partkey;
	int suppkey;
	int linenumber;
	double quantity;
	double extendedpeice;
	double discount;
	double tax;
	char returnflag[2];
	char linestatus[2];
	char shipdate[12];
	char commitdate[12];
	char receiptdate[12];
	char shipinstruct[25];
	char shipmode[10];
	char comment[44];
}LINEITEM;

extern REGION regiontbl;
extern NATION nationtbl;
extern SUPPLIER suppliertbl;
extern PART parttbl;
extern PARTSUPP partsupptbl;
extern CUSTOMER customertbl;
extern ORDERS orderstbl;
extern LINEITEM lineitemtbl;


#endif // TPCH1_H