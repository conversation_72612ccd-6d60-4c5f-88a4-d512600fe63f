-- Sccsid:     @(#)dss.ddl	2.1.8.1
CREATE TABLE NATION  ( N_NATIONKEY  INT NOT NULL,
                            N_NAME       CHAR(25) NOT NULL,
                            N_REGIONKEY  INT NOT NULL,
                            N_COMMENT    CHAR(152));

CREATE TABLE REGION  ( <PERSON>_<PERSON><PERSON>IONKEY  INT NOT NULL,
                            R_NAME       CHAR(25) NOT NULL,
                            R_COMMENT    CHAR(152));

CREATE TABLE PART  ( P_PARTKEY     INT NOT NULL,
                          P_NAME        CHAR(55) NOT NULL,
                          P_MFGR        CHAR(25) NOT NULL,
                          P_<PERSON>AND       CHAR(10) NOT NULL,
                          P_TYPE        CHAR(25) NOT NULL,
                          P_SIZE        INT NOT NULL,
                          P_CONTAINER   CHAR(10) NOT NULL,
                          P_RETAILPRICE float NOT NULL,
                          P_COMMENT     CHAR(23) NOT NULL );

CREATE TABLE SUPPLIER ( S_SUPPKEY     INT NOT NULL,
                             S_<PERSON>AME        CHAR(25) NOT NULL,
                             S_ADDRESS     CHAR(40) NOT NULL,
                             S_NATIONKEY   INT NOT NULL,
                             S_<PERSON>H<PERSON>E       CHAR(15) NOT NULL,
                             S_ACCTBAL     float NOT NULL,
                             S_COMMENT     CHAR(101) NOT NULL);

CREATE TABLE PARTSUPP ( PS_PARTKEY     INT NOT NULL,
                             PS_SUPPKEY     INT NOT NULL,
                             PS_AVAILQTY    INT NOT NULL,
                             PS_SUPPLYCOST  float  NOT NULL,
                             PS_COMMENT     CHAR(199) NOT NULL );

CREATE TABLE CUSTOMER ( C_CUSTKEY     INT  PRIMARY KEY NOT NULL ,
                             C_NAME        CHAR(25) NOT NULL,
                             C_ADDRESS     CHAR(40) NOT NULL,
                             C_NATIONKEY   INT NOT NULL,
                             C_PHONE       CHAR(15) NOT NULL,
                             C_ACCTBAL     float   NOT NULL,
                             C_MKTSEGMENT  CHAR(10) NOT NULL,
                             C_COMMENT     CHAR(117) NOT NULL);

CREATE TABLE ORDERS  ( O_ORDERKEY       INT NOT NULL,
                           O_CUSTKEY        INT NOT NULL,
                           O_ORDERSTATUS    CHAR(1) NOT NULL,
                           O_TOTALPRICE     float NOT NULL,
                           O_ORDERDATE      CHAR(11) NOT NULL,
                           O_ORDERPRIORITY  CHAR(15) NOT NULL,  
                           O_CLERK          CHAR(15) NOT NULL, 
                           O_SHIPPRIORITY   INT NOT NULL,
                           O_COMMENT        CHAR(79) NOT NULL);

CREATE TABLE LINEITEM ( L_ORDERKEY    INT NOT NULL,
                             L_PARTKEY     INT NOT NULL,
                             L_SUPPKEY     INT NOT NULL,
                             L_LINENUMBER  INT NOT NULL,
                             L_QUANTITY    float NOT NULL,
                             L_EXTENDEDPRICE  float NOT NULL,
                             L_DISCOUNT    float NOT NULL,
                             L_TAX         float NOT NULL,
                             L_RETURNFLAG  CHAR(1) NOT NULL,
                             L_LINESTATUS  CHAR(1) NOT NULL,
                             L_SHIPDATE    CHAR(11) NOT NULL,
                             L_COMMITDATE  CHAR(11) NOT NULL,
                             L_RECEIPTDATE CHAR(11) NOT NULL,
                             L_SHIPINSTRUCT CHAR(25) NOT NULL,
                             L_SHIPMODE     CHAR(10) NOT NULL,
                             L_COMMENT      CHAR(44) NOT NULL);

