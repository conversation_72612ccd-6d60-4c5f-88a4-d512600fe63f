/*
 * @Author: zql <EMAIL>
 * @Date: 2025-05-27 15:29:11
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-19 09:31:48
 * @FilePath: /gncdbflr/linux_dev/test/concurrence/index_concu_test.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "index_concu_test.h"
#include "catalog.h"
#include "concurrence.h"
#include "gncdb.h"
#include "gncdbconstant.h"
#include "hash.h"
#include <string.h>

#define reset_CT_tool()                          \
  {                                              \
    for (int i = 0; i < PTHREAD_MAXCOUNT; i++) { \
      CT_tool.rowcount[i]  = 0;                  \
      CT_tool.rowcounts[i] = 0;                  \
    }                                            \
  }

int printCallback(void *data, int argc, char **azColName, char **argv)
{
  int i;
  for (i = 0; i < argc; i++) {
    printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");  // 初始化全局变量SAMETABLE为0
  }  // 从10开始循环，直到i小于MULTI_TESTNUM
  printf("\n");
  return 0;  // 如果i等于5
}

int getCntCallback(void *data, int argc, char **azColName, char **argv)
{
  int *count = (int *)data;
  *count     = atoi(argv[0]);
  return 0;
}

int ct_cbIndexSelectDouble(void *NotUsed, int argc, char **azColName, char **argv)
{
  double value1   = 10.5;
  double valueTab = atof(argv[0]);
  if (valueTab == value1) {
    if (NotUsed != NULL) {
      int *num = (int *)NotUsed;
      (*num)++;
    }
    CT_tool.rowcount[1]++;
  }
  return 0;
}

int ct_cbIndexSelectInt(void *NotUsed, int argc, char **azColName, char **argv)
{
  int value1   = 10.5;
  int valueTab = atoi(argv[0]);
  if (valueTab == value1) {
    if (NotUsed != NULL) {
      int *num = (int *)NotUsed;
      (*num)++;
    }
    CT_tool.rowcount[0]++;
  }
  return 0;
}

int ct_cbIndexSelectInt1(void *NotUsed, int argc, char **azColName, char **argv)
{
  int value1   = 10.5;
  int valueTab = atoi(argv[0]);
  if (valueTab == value1) {
    if (NotUsed != NULL) {
      int *num = (int *)NotUsed;
      (*num)++;
    }
    CT_tool.rowcount[1]++;
  }
  return 0;
}

int createHashIdxInt()
{
  int rc = 0;
#ifdef SQL_CTTESTMODE
  rc = GNCDB_exec(ct_Global.db, "CREATE INDEX idx_int ON combined_table(int_value) USING HASH;", NULL, NULL, NULL);
#else
  rc = GNCDB_createHashIndex(ct_Global.db, "combined_table", "idx_int", 1, "int_value");
#endif
  return rc;
}

int createHashIdxDouble()
{
  int rc = 0;
#ifdef SQL_CTTESTMODE
  rc =
      GNCDB_exec(ct_Global.db, "CREATE INDEX idx_double ON combined_table(double_value) USING HASH;", NULL, NULL, NULL);
#else
  rc = GNCDB_createHashIndex(ct_Global.db, "combined_table", "idx_double", 1, "double_value");
#endif
  return rc;
}

int insertTest(void *arg)
{
  int         rc   = 0;
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  // rc     = executeSQLFile(ct_Global.db, "./test/hash_index_test/test_sql_file/insert.sql");
  rc = GNCDB_exec(ct_Global.db, "INSERT INTO combined_table VALUES (200000, 10, 10.5, 'value_10');", NULL, NULL, NULL);
  ct_p->rcArray[0] = rc;
  if (rc != GNCDB_SUCCESS) {
    return -1;
  }
  return 0;
}

int insertTest1(void *arg)
{
  int         rc   = 0;
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  // rc     = executeSQLFile(ct_Global.db, "./test/hash_index_test/test_sql_file/insert.sql");
  rc = GNCDB_exec(ct_Global.db, "INSERT INTO combined_table VALUES (200001, 10, 10.5, 'value_10');", NULL, NULL, NULL);
  ct_p->rcArray[0] = rc;
  if (rc != GNCDB_SUCCESS) {
    return -1;
  }
  return 0;
}

int deleteTest(void *arg)
{
  int         rc   = 0;
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  // rc     = executeSQLFile(ct_Global.db, "./test/hash_index_test/test_sql_file/delete.sql");
  rc               = GNCDB_exec(ct_Global.db, "DELETE FROM combined_table WHERE id = 2009;", NULL, NULL, NULL);
  ct_p->rcArray[0] = rc;
  return 0;
}

int deleteTest1(void *arg)
{
  int         rc   = 0;
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  // rc     = executeSQLFile(ct_Global.db, "./test/hash_index_test/test_sql_file/delete.sql");
  rc               = GNCDB_exec(ct_Global.db, "DELETE FROM combined_table WHERE id = 4009;", NULL, NULL, NULL);
  ct_p->rcArray[1] = rc;
  return 0;
}

int updateTest(void *arg)
{
  int         rc   = 0;
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  // 10 -> 300
  rc = GNCDB_exec(ct_Global.db, "update combined_table set int_value = 300 where id = 3009;", NULL, NULL, NULL);
  ct_p->rcArray[0] = rc;
  return 0;
}

int updateTest1(void *arg)
{
  int         rc   = 0;
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  // 10 -> 300
  rc = GNCDB_exec(ct_Global.db, "update combined_table set int_value = 300 where id = 5009;", NULL, NULL, NULL);
  ct_p->rcArray[1] = rc;
  return 0;
}

int selectIntTest(void *arg)
{
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  int         rc   = 0;
  rc               = GNCDB_exec(ct_Global.db,
      "SELECT int_value FROM combined_table WHERE int_value = 10;",
      ct_cbIndexSelectInt,
      &(ct_p->selectCount[0]),
      NULL);
  if (rc != GNCDB_SUCCESS) {
    return -1;
  }
  return 0;
}

int selectIntTest1(void *arg)
{
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  int         rc   = 0;
  rc               = GNCDB_exec(ct_Global.db,
      "SELECT int_value FROM combined_table WHERE int_value = 10;",
      ct_cbIndexSelectInt1,
      &(ct_p->selectCount[1]),
      NULL);
  ct_p->rcArray[1] = rc;
  return 0;
}

int selectDoubleTest(void *arg)
{
  CT_Pthread *ct_p = (CT_Pthread *)arg;
  int         rc   = 0;
  rc               = GNCDB_exec(ct_Global.db,
      "SELECT double_value FROM combined_table WHERE double_value = 10.5;",
      ct_cbIndexSelectDouble,
      &(ct_p->selectCount[1]),
      NULL);
  ct_p->rcArray[1] = rc;
  if (rc != GNCDB_SUCCESS) {
    return -1;
  }
  return 0;
}

int createAndSelectIndexTest()
{
  CT_Pthread item;
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  item.project          = "创建索引";
  item.operation        = "使用两个线程在同一个表同时创建两个索引";
  item.message          = "CREATE INDEX idx_int ON combined_table(int_value) USING HASH;"
                          "CREATE INDEX idx_double ON combined_table(double_value) USING HASH;";
  item.ptCount          = 2;
  item.threadFuncs[0]   = createHashIdxInt;
  item.threadFuncs[1]   = createHashIdxDouble;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }

  create_pthreadInit(&item);
  if (item.rc != 0) {
    CT_updatamessage(&item);
    return -1;
  }

  CT_tool.tableName = "idx_int";
  rc = GNCDB_exec(ct_Global.db, "select * from master where tableName='idx_int';", ct_callBack1, NULL, NULL);
  if (CT_tool.testFlag == false) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }
  CT_tool.tableName = "idx_double";
  rc = GNCDB_exec(ct_Global.db, "select * from master where tableName='idx_double';", ct_callBack1, NULL, NULL);
  if (CT_tool.testFlag == false) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  rc = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex1, "combined_table.idx_int");
  rc = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex2, "combined_table.idx_double");
  if (getHashIndexKVCnt(hashIndex1, ct_Global.db) != 100000) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }
  if (getHashIndexKVCnt(hashIndex2, ct_Global.db) != 100000) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  CT_updatamessage(&item);

  return rc;
}

int selectTest1()
{
  CT_Pthread item;
  item.project   = "索引查询";
  item.operation = "使用两个线程索引查询同一个表不同索引";
  item.message   = "SELECT * FROM combined_table WHERE int_value = 10;"
                   "SELECT * FROM combined_table WHERE double_value = 10.0";

  item.ptCount        = 2;
  item.threadFuncs[0] = selectIntTest;
  item.threadFuncs[1] = selectDoubleTest;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }

  create_pthreadInit(&item);
  if (item.rc != 0) {
    CT_updatamessage(&item);
    return -1;
  }
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }
  CT_updatamessage(&item);
  return 0;
}

int selectTest2()
{
  CT_Pthread item;
  item.project   = "表查询";
  item.operation = "使用两个线程索引查询同一个表同一索引";
  item.message   = "SELECT * FROM combined_table WHERE int_value = 10;"
                   "SELECT * FROM combined_table WHERE int_value = 10";

  item.ptCount        = 2;
  item.threadFuncs[0] = selectIntTest;
  item.threadFuncs[1] = selectIntTest1;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }

  create_pthreadInit(&item);
  if (item.rc != 0) {
    CT_updatamessage(&item);
    return -1;
  }
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }
  CT_updatamessage(&item);
  return 0;
}

int insert_selectTest()
{
  CT_Pthread item;
  int        rc         = 0;
  int        prevCnt1   = 0;
  int        prevCnt2   = 0;
  int        postCnt1   = 0;
  int        postCnt2   = 0;
  int        cnt1       = 0;
  int        cnt2       = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  item.project          = "索引读写冲突测试";
  item.operation        = "使用一个线程插入数据，一个线程读取数据";
  item.message          = "insert into combined_table values(?, ?, ?, ?);"
                          "SELECT * FROM combined_table WHERE int_value = 100";
  item.ptCount          = 2;
  item.threadFuncs[0]   = insertTest;
  item.threadFuncs[1]   = selectDoubleTest;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }

  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex1, "combined_table.idx_int");
  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex2, "combined_table.idx_double");
  prevCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  prevCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &cnt1, NULL);

  create_pthreadInit(&item);
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  postCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  postCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &cnt2, NULL);

  if (item.rcArray[0] == GNCDB_SUCCESS) {
    if (prevCnt1 != postCnt1 - 1 || prevCnt2 != postCnt2 - 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (cnt1 != cnt2 - 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  } else if (item.rcArray[0] != GNCDB_SUCCESS) {
    if (item.rcArray[1] != GNCDB_SUCCESS) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (prevCnt1 != postCnt1 || prevCnt2 != postCnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (cnt1 != cnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  }

  // rc = GNCDB_exec(
  //     ct_Global.db, "select count(*) from combined_table where double_value = 10.5;", printCallback, NULL, NULL);
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }
  CT_updatamessage(&item);
  return rc;
}

int delete_selectTest()
{
  CT_Pthread item;
  int        rc         = 0;
  int        prevCnt1   = 0;
  int        prevCnt2   = 0;
  int        postCnt1   = 0;
  int        postCnt2   = 0;
  int        cnt1       = 0;
  int        cnt2       = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  item.project          = "索引删除测试";
  item.operation        = "使用一个线程删除数据，一个线程去读取数据";
  item.message          = "delete from combined_table where int_value = 100;"
                          "SELECT * FROM combined_table WHERE int_value = 100";
  item.ptCount          = 2;
  item.threadFuncs[0]   = deleteTest;
  item.threadFuncs[1]   = selectIntTest1;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }
  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex1, "combined_table.idx_int");
  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex2, "combined_table.idx_double");
  prevCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  prevCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &cnt1, NULL);

  create_pthreadInit(&item);
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  postCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  postCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &cnt2, NULL);

  if (item.rcArray[0] == GNCDB_SUCCESS) {
    if (prevCnt1 != postCnt1 + 1 || prevCnt2 != postCnt2 + 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (cnt1 != cnt2 + 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  } else if (item.rcArray[0] != GNCDB_SUCCESS) {
    if (item.rcArray[1] != GNCDB_SUCCESS) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (prevCnt1 != postCnt1 || prevCnt2 != postCnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (cnt1 != cnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  }
  CT_updatamessage(&item);
  return rc;
}

int update_selectTest()
{
  CT_Pthread item;
  int        rc         = 0;
  int        prevCnt1   = 0;
  int        prevCnt2   = 0;
  int        postCnt1   = 0;
  int        postCnt2   = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  int        prevCnt10  = 0;
  int        prevCnt300 = 0;
  int        postCnt10  = 0;
  int        postCnt300 = 0;

  item.project        = "索引更新测试";
  item.operation      = "使用一个线程更新数据，一个线程去读取数据";
  item.message        = "update combined_table set int_value = 300 where id = 3009;"
                        "SELECT * FROM combined_table WHERE int_value = 200";
  item.ptCount        = 2;
  item.threadFuncs[0] = updateTest;
  item.threadFuncs[1] = selectIntTest1;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }

  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex1, "combined_table.idx_int");
  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex2, "combined_table.idx_double");
  prevCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  prevCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &prevCnt10, NULL);
  rc = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 300;", getCntCallback, &prevCnt300, NULL);

  create_pthreadInit(&item);
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  postCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  postCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &postCnt10, NULL);
  rc = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 300;", getCntCallback, &postCnt300, NULL);

  if (prevCnt1 != postCnt1 || prevCnt2 != postCnt2) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  if (item.rcArray[0] == GNCDB_SUCCESS) {
    // 更新成功，10: 99, 300: 101
    if (prevCnt10 != postCnt10 + 1 || prevCnt300 != postCnt300 - 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  } else if (item.rcArray[0] != GNCDB_SUCCESS) {
    // 更新不成功，查询必须成功， 10: 100, 300: 100
    if (item.rcArray[1] != GNCDB_SUCCESS) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (prevCnt10 != postCnt10 || prevCnt300 != postCnt300) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  }
  CT_updatamessage(&item);
  return rc;
}

int insert_deleteTest()
{
  CT_Pthread item;
  int        prevCnt1   = 0;
  int        prevCnt2   = 0;
  int        postCnt1   = 0;
  int        postCnt2   = 0;
  int        cnt1       = 0;
  int        cnt2       = 0;
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  item.project          = "索引更新测试";
  item.operation        = "使用一个线程插入数据，一个线程删除数据";
  item.message          = "INSERT INTO combined_table VALUES (200001, 10, 10.5, 'value_10');"
                          "DELETE FROM combined_table WHERE id = 4009;";

  item.ptCount        = 2;
  item.threadFuncs[0] = insertTest1;
  item.threadFuncs[1] = deleteTest1;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }

  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex1, "combined_table.idx_int");
  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex2, "combined_table.idx_double");
  prevCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  prevCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &cnt1, NULL);

  create_pthreadInit(&item);
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  postCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  postCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &cnt2, NULL);

  if (item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS) {
    // 插入和删除都成功
    if (prevCnt1 != postCnt1 || prevCnt2 != postCnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (cnt1 != cnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  } else if (item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS) {
    // 插入失败，删除成功
    if (prevCnt1 != postCnt1 + 1 || prevCnt2 != postCnt2 + 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (cnt1 != cnt2 + 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  } else if (item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS) {
    // 插入成功，删除失败
    if (prevCnt1 + 1 != postCnt1 || prevCnt2 + 1 != postCnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (cnt1 + 1 != cnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
  } else {
    // 插入和删除都失败
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }
  CT_updatamessage(&item);
  return rc;
}

int insert_updateTest()
{
  CT_Pthread item;
  int        prevCnt1   = 0;
  int        prevCnt2   = 0;
  int        postCnt1   = 0;
  int        postCnt2   = 0;
  int        prevCnt10  = 0;
  int        prevCnt300 = 0;
  int        postCnt10  = 0;
  int        postCnt300 = 0;
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  item.project          = "索引更新测试";
  item.operation        = "使用一个线程插入数据，一个线程更新数据";
  item.message          = "INSERT INTO combined_table VALUES (200001, 10, 10.5, 'value_10');;"
                          "update combined_table set int_value = 300 where id = 5009;";

  item.ptCount        = 2;
  item.threadFuncs[0] = insertTest1;
  item.threadFuncs[1] = updateTest1;
  for (int i = 0; i < PTHREAD_MAXCOUNT; i++) {
    item.selectCount[i]  = 0;
    item.selectCounts[i] = 0;
  }

  rc = GNCDB_exec(ct_Global.db, "delete from  combined_table where id = 200001;", NULL, NULL, NULL);
  if (rc != GNCDB_SUCCESS) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex1, "combined_table.idx_int");
  rc       = catalogGetHashIndex(ct_Global.db->catalog, &hashIndex2, "combined_table.idx_double");
  prevCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  prevCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &prevCnt10, NULL);
  rc = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 300;", getCntCallback, &prevCnt300, NULL);

  create_pthreadInit(&item);
  if (CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]) {
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }

  postCnt1 = getHashIndexKVCnt(hashIndex1, ct_Global.db);
  postCnt2 = getHashIndexKVCnt(hashIndex2, ct_Global.db);
  rc       = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 10;", getCntCallback, &postCnt10, NULL);
  rc = GNCDB_exec(
      ct_Global.db, "select count(*) from combined_table where int_value = 300;", getCntCallback, &postCnt300, NULL);

  if (item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS) {
    // 插入和更新都成功
    if (prevCnt1 + 1 != postCnt1 || prevCnt2 + 1 != postCnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (prevCnt10 != postCnt10 || prevCnt300 + 1 != postCnt300) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }

  } else if (item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS) {
    // 插入失败，更新成功
    if (prevCnt1 != postCnt1 || prevCnt2 != postCnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (prevCnt10 != postCnt10 + 1 || prevCnt300 != postCnt300 - 1) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }

  } else if (item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS) {
    // 插入成功，更新失败
    if (prevCnt1 + 1 != postCnt1 || prevCnt2 + 1 != postCnt2) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }
    if (prevCnt10 != postCnt10 - 1 || prevCnt300 != postCnt300) {
      item.rc = -1;
      CT_updatamessage(&item);
      return -1;
    }

  } else {
    // 插入和删除都失败
    item.rc = -1;
    CT_updatamessage(&item);
    return -1;
  }
  CT_updatamessage(&item);
  return rc;
}

int hashIndexConcurTest()
{
  int rc = 0;
  rc     = initConcurrenceTest();
  if (rc != 0) {
    return rc;
  }

  rc = executeSQLFile(ct_Global.db, "./test/hash_index_test/test_sql_file/init.sql");
  if (rc != 0) {
    return rc;
  }
  // 执行索引创建测试
  reset_CT_tool();
  rc = createAndSelectIndexTest();

  // 读读测试：执行查询测试
  // 1.俩个线程查询不同索引
  reset_CT_tool();
  rc = selectTest1();
  // 2.俩个线程查询同一索引
  reset_CT_tool();
  rc = selectTest2();

  // 读写测试：
  // 1.一个线程插入数据，一个线程查询数据
  reset_CT_tool();
  rc = insert_selectTest();
  // 2.一个线程更新数据，一个线程查询数据
  reset_CT_tool();
  rc = update_selectTest();
  // 3.一个线程删除数据，一个线程查询数据
  reset_CT_tool();
  rc = delete_selectTest();

  // 写写测试：
  // 1.一个线程插入，一个线程删除
  reset_CT_tool();
  insert_deleteTest();
  // 2.一个线程插入，一个线程更新
  reset_CT_tool();
  insert_updateTest();

  if (rc != 0) {
    return rc;
  }
  return rc;
}