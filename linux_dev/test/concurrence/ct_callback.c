#include "concurrence.h"
#include <math.h>
int ct_flag = 0;
void ct_initFlag()
{
    ct_flag = 0;
}



int ct_compare_float(float a, float b, int decimal_places) {
    // 计算比较的精度
    float precision = pow(10, -decimal_places);
    
    // 计算两个数的差值
    float diff = fabs(a - b);
    
    // 如果差值小于精度，则认为两个数相等
    if (diff < precision) {
        return 0; // 相等
    } else if (a < b) {
        return -1; // a 小于 b
    } else {
        return 1; // a 大于 b
    }
}

int ct_CallBack(void *NotUsed, int argc, char** azColName, char** argv)
{
    if (ct_flag == 0)
    {
        //// printf("查询出的记录：\n");
        printf("\n");
        printf("|");
        for (int i = 0; i < argc; i++)
        {
            printf("%16s|", azColName[i]);
        }
        printf("\n");
        for (int i = 0; i <= argc * 17; ++i)
        {
            printf("-");
        }
        printf("\n");
    }

    printf("|");
    for (int i = 0; i < argc; i++)
    {
        printf("%16s|", argv[i]);
    }
    printf("\n");

    ct_flag = 1;

    return 0;
}

int ct_callBack1(void *NotUsed, int argc, char** azColName, char** argv)
{
    if (strcmp(argv[1], CT_tool.tableName) == 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.testFlag = true;
    }
    else
    {
        CT_tool.testFlag = false;
    }
    return 0;
}

int ct_callBackinsert1(void *NotUsed, int argc, char** azColName, char** argv)
{
    CT_tool.rowcount[0]++;
    if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }

    return 0;
}

int ct_callBackSelectARPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = atof(argv[1]);
    double num2 = 10.0;
    if(num1 > num2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }

    return 0;
}

int ct_callBackSelectARPTtable_1C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* data2 = "RSMAM";
    if(strcmp(argv[0], data2) > 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }

    return 0;
}

int ct_callBackSelectARPTtable_2C(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = atof(argv[2]);
    double num2 = 10.0;
    double num3 = 50.0;
    if(num1 > num2 && num1 < num3)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }

    return 0;
}

int ct_callBackUpdateARPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 25.0;
    double valueTab = atof(argv[1]);
    if(value == valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateARPTtable_1C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 25.0;
    double valueTab = atof(argv[4]);
    if(value == valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    else{
        //printf("ERROR");
    }
    return 0;
}

int ct_callBackUpdateARPTtable_1C3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 25.0;
    double valueTab = atof(argv[3]);
    if(value == valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }
    return 0;
}

int ct_callBackUpdateARPTtable_1C4(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 125.0;
    double valueTab = atof(argv[1]);
    if(value == valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateARPTtable_2C(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 10.0;
    double valueTab = atof(argv[1]);
    if(value == valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    return 0;
}

int ct_callBackSelectWPTtable_2C(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = -25.0;
    char* str = "VCOED";
    double valueTab = atof(argv[2]);
    if(strcmp(argv[0], str) >= 0 && value > valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackSelectWPTtable_2C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = -25.0;
    char* str = "VCOED";
    double valueTab = atof(argv[2]);
    if(strcmp(argv[0], str) >= 0 && value > valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    return 0;
}

int ct_callBackSelectWPTtable_2C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = -25.0;
    char* str = "VCOED";
    double valueTab = atof(argv[2]);
    if(strcmp(argv[0], str) >= 0 && value < valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }
    return 0;
}

int ct_callBackUpdateWPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 25.25;
    double valueTab = atof(argv[2]);
    if(value == valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }

    return 0;
}

int ct_callBackUpdateWPTtable_1C3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 25.25;
    double valueTab = atof(argv[2]);
    if(value == valueTab)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }

    return 0;
}

int ct_callBackUpdateWPTtable_1C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value1 = 25.25;
    double value2 = -25.25;
    double valueTab1 = atof(argv[2]);
    double valueTab2 = atof(argv[1]);
    if(value1 == valueTab1)
    {
        CT_tool.rowcount[0]++;
    }
    if(value2 == valueTab2)
    {
        CT_tool.rowcount[1]++;
    }

    return 0;
}

int ct_callBackUpdateWPTtable_1C5(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value2 = -25.25;
    double valueTab2 = atof(argv[1]);
    if(value2 == valueTab2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }

    return 0;
}

int ct_callBackUpdateWPTtable_2C0(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value2 = 25.25;
    double valueTab2 = atof(argv[2]);
    if(value2 == valueTab2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateWPTtable_2C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value2 = 25.25;
    double valueTab2 = atof(argv[2]);
    if(value2 == valueTab2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    return 0;
}

int ct_callBackUpdateWPTtable_2C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value2 = 0.0;
    double valueTab2 = atof(argv[1]);
    if(value2 == valueTab2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateWPTtable_2C3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value2 = 125.25;
    double valueTab2 = atof(argv[2]);
    if(value2 == valueTab2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    return 0;
}

int ct_callBackUpdateWPTtable_2C4(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value2 = 10.0;
    double valueTab2 = atof(argv[2]);
    if(value2 == valueTab2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateWPTtable_2C5(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value2 = 12.0;
    double valueTab2 = atof(argv[1]);
    if(value2 == valueTab2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackSelectNAVtable_2C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value1 = -22.5;
    double value2 = 22.5;
    double valueTab = atof(argv[2]);
    if(valueTab > value1 && valueTab < value2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackSelectNAVtable_2C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "LKWHG";
    double value = 132.483993;
    double valueTab = atof(argv[6]);
    if(strcmp(str, argv[0]) >= 0 && valueTab < value)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    return 0;
}

int ct_callBackUpdateNAVtable_1C(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = -45.000000;
    double value2 = 0.0;
    double valueTab = atof(argv[3]);
    double valueTab2 = atof(argv[4]);
    if(valueTab == value)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        if(valueTab2 == value2)
        {
            CT_tool.rowcount[1]++;
        }
    }
    return 0;
}
int ct_callBackupdateNAVtable_1C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value = 102.0101;
    double valueTab = atof(argv[4]);
    if(!ct_compare_float(value, valueTab, 2))
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    return 0;
}

int ct_callBackSelectSIDtable_1C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "HYUBW";
    if(strcmp(str, argv[1]) >= 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    return 0;
}

int ct_callBackUpdateSIDtable_1C(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "BDHKC";
    double value = 52.000;
    double valueTab = atof(argv[4]);
    if(strcmp(str, argv[0]) >= 0)
    {
        if(value == valueTab)
        {
            if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
            CT_tool.rowcount[0]++;
        }
    }
    return 0;
}

int ct_callBackSelectAPCHtable_1C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "XZDH";
    if(strcmp(str, argv[1]) == 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateAPCHtable_1C(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str1 = "1";
    char* str2 = "4";
    if(strcmp(str1, argv[3]) == 0)
    {
        if(strcmp(str2, argv[6]) == 0)
        {
            if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
            CT_tool.rowcount[1]++;
        }
    }
    return 0;
}

int ct_callBackUpdateAPCHtable_1C2(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str1 = "1";
    char* str2 = "4";
    if(strcmp(str1, argv[3]) == 0)
    {
        if(strcmp(str2, argv[6]) == 0)
        {
            if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
            CT_tool.rowcount[0]++;
        }
    }
    return 0;
}

int ct_callBackSelectDEFINE_WPTtable_2C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double value1 = 45.0;
    double value2 = -45.0;
    double valueTab1 = atof(argv[1]);
    double valueTab2 = atof(argv[2]);
    if(valueTab1 >= value1 && valueTab2 < value2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateDEFINE_WPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "KFUKRA";
    double value = 10.000;
    double valueTab = atof(argv[1]);
    if(strcmp(str, argv[0]) == 0)
    {
        if(!ct_compare_float(value, valueTab, 2))
        {
            if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
            CT_tool.rowcount[0]++;
        }
    }
    return 0;
}


int ct_callBackSelectFPLNtable_1C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "2";
    if(strcmp(str, argv[6]) == 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackUpdateFPLNtable_2C(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "1";
    double value1 = 0.00;
    double value2 = 25.000;
    double valueTab = atof(argv[9]);
    if(valueTab >= value1 && valueTab <= value2)
    {
        if(strcmp(str, argv[7]) == 0 && strcmp(str, argv[8]) == 0)
        {
            if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
            CT_tool.rowcount[0]++;
        }
    }
    return 0;
}

int ct_callBackUpdateFPLNtable_2C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    char* str = "1";
    // double value1 = 0.00;
    // double value2 = 25.000;
    // double valueTab = atof(argv[9]);
    //if(valueTab >= value1 && valueTab <= value2)
    {
        if(strcmp(str, argv[7]) == 0 && strcmp(str, argv[8]) == 0)
        {
            if(NotUsed != NULL)
            {
                int* num = (int*)NotUsed;
                (*num)++;
            }
            CT_tool.rowcount[2]++;
        }
    }
    return 0;
}

int ct_callBackSameTableTestCreate(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(strcmp(argv[1], "ARPT") == 0 || strcmp(argv[1], "WPT") == 0 || strcmp(argv[1], "NAV") == 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    return 0;
}

int ct_callBackJoinTestInsert2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[5]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[6]);
    if(valueLon1 < valueLon2 && valueLat1 == valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }

    return 0;
}

int ct_callBackJoinTestMulti116(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[5]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[6]);
    if(valueLon1 < valueLon2 && valueLat1 == valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }
    else
    {
        valueLat1 = 0;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT0(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[5]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[6]);
    if(valueLon1 < valueLon2 && valueLat1 == valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    else
    {
        valueLon1 = 0;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[5]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[6]);
    if(valueLon1 == valueLon2 && valueLat1 < valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    else
    {
        valueLon1 = 0;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[5]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[6]);
    if(valueLon1 < valueLon2 && valueLat1 == valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT2C1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[5]);
    if(valueLon1 > valueLon2 && strcmp(argv[0], argv[4]) < 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT0EX(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[7]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[8]);
    if(valueLon1 < valueLon2 && valueLat1 == valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT1EX(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[7]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[8]);
    if(valueLon1 == valueLon2 && valueLat1 < valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT2EX(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    double valueLat1 = 0, valueLat2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[7]);
    valueLat1 = atof(argv[2]);
    valueLat2 = atof(argv[8]);
    if(valueLon1 < valueLon2 && valueLat1 == valueLat2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }

    return 0;
}

int ct_callBackSelectTableWPTjoinARPT2C1EX(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    valueLon1 = atof(argv[1]);
    valueLon2 = atof(argv[7]);
    if(valueLon1 > valueLon2 && strcmp(argv[0], argv[6]) < 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[2]++;
    }

    return 0;
}

int ct_callBackJoinTestSelect(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(strcmp(argv[0], argv[4]) == 0)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }

    return 0;
}


int ct_callBackSelectTableARPTjoinSID(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    valueLon1 = atof(argv[9]);
    valueLon2 = atof(argv[15]);
    if(valueLon1 < valueLon2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[1]++;
    }
    else
    {
        valueLon1 = 0;
    }

    return 0;
}

int ct_callBackSelectTableFPLNjoinNAV(void *NotUsed, int argc, char** azColName, char** argv)
{
    double valueLon1 = 0, valueLon2 = 0;
    valueLon1 = atof(argv[9]);
    valueLon2 = atof(argv[15]);
    if(valueLon1 == valueLon2)
    {
        if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
        CT_tool.rowcount[0]++;
    }
    else
    {
        valueLon1 = 0;
    }

    return 0;
}

int testCallBackBlobSize0(void *NotUsed, int argc, char** azColName, char** argv)
{
    CT_tool.rowcount[0] = atoi(argv[3]);

    return 0;
}

int testCallBackBlobSize1(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
    CT_tool.rowcount[1] = atoi(argv[3]);

    return 0;
}

int testCallBackBlobSize2(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
    CT_tool.rowcount[2] = atoi(argv[3]);

    return 0;
}

int testCallBackBlobSize3(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(NotUsed != NULL)
        {
            int* num = (int*)NotUsed;
            (*num)++;
        }
    CT_tool.rowcount[3] = atoi(argv[3]);

    return 0;
}

int ct_callBackSQLGetRows(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(NotUsed != NULL)
    {
        int* num = (int*)NotUsed;
        (*num) = atoi(argv[0]);
    }

    return 0;
}

int ct_testCallBackSqlAggregate1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum;
    }
    return 0;
}

int ct_testCallBackAggregate1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) += sum;
    }
    return 0;
}

int ct_testCallBackAggregate4(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum > (*p) ? sum : (*p);
    }
    return 0;
}

int ct_testCallBackAggregate7(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[2]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum > (*p) ? sum : (*p);
    }
    return 0;
}


float ctstring_to_float(char* str) {
    float result = 0.0f;
    float frac = 0.1f;
    int sign = 1;
    int decimal_seen = 0;

    if (*str == '-') {
        sign = -1;
        str++;
    }
    else if (*str == '+') {
        str++;
    }

    while (*str != '\0') {
        if (*str == '.') {
            decimal_seen = 1;
            str++;
            continue;
        }
        if (*str >= '0' && *str <= '9') {
            if (decimal_seen) {
                result = result + frac * (*str - '0');
                frac /= 10.0f;
            }
            else {
                result = result * 10.0f + (*str - '0');
            }
        }
        else {
            return sign * result;
        }
        str++;
    }
    return sign * result;
}



int ct_testCallBackAggregate3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum < (*p) ? sum : (*p);
    }
    return 0;
}

int ct_testCallBackAggregate8(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[2]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum < (*p) ? sum : (*p);
    }
    return 0;
}

int ct_testCallBackAggregate5(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[4]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum < (*p) ? sum : (*p);
    }
    return 0;
}

int ct_testCallBackSqlSubquery1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        if(sum == 0.000000)
        {
            (*p) += 1.0;
        }
        else
        {
            (*p) -= 1000;
        }
    }
    
    return 0;
}

double value1121 = -200.0;
int ct_testCallBackSqlOrderBy1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p = NULL;
    sum = ctstring_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        if(sum >= value1121)
        {
            value1121 = sum;
        }
        else{
            *p = *p + 1;
        }  
    }
    
    return 0;
}

int ct_testCallBackAggregate2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) += sum;
    }
    return 0;
}

int ct_testCallBackAggregate6(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[4]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) += sum;
    }
    return 0;
}

int ct_testCallBackSqlAggregate5(void *NotUsed, int argc, char** azColName, char** argv)
{
    // todo
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum;
    }
    return 0;
}

int ct_testCallBackSqlOrderBy2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        if(sum <= value1121)
        {
            value1121 = sum;
        }
        else{
            *p = *p + 1;
        }  
    }
    
    return 0;
}

int ct_testCallBackSqlOrderBy3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = ctstring_to_float(argv[2]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        if(sum <= value1121)
        {
            value1121 = sum;
        }
        else{
            *p = *p + 1;
        }  
    }
    
    return 0;
}

int ct_testCallBackSqlLimit1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double* p= NULL;
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p)++;

    }
    return 0;
}
