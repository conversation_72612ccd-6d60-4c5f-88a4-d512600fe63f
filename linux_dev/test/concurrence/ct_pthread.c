#include "concurrence.h"

CT_CREATETABLE CT_tool;

int concurrencetest()
{
	int rc = 0;
	rc = initConcurrenceTest();
	if (rc != 0)
	{
		return rc;
	}
    /* 双线程不同表测试 */
    ct_doubleThreadTest();
    /* 双线程同表 */
    sameTableTest();
    /* 三线程不同表测试 */
    multiThreadTest();
    /* 三线程同表测试 */
    sameTablemulti();
    /* 不同表join测试 */
    multiJoinTable();
    /* 同表join测试 */
    sameTablemultiJoin();
    /* 不同表BLOB测试 */
    // multiTableBlobTest();
    // /* 同表BLOB测试 */
    // sameTablemultiBlob();

    // 聚集函数测试
#ifdef SQL_CTTESTMODE
    multiSpecialTest();
#endif
	closeConcurrenceTest();

	return rc;
}

void ct_doubleThreadTest()
{
    ct_createTableTest();
    ct_insertTableTest();
    ct_selectTableTest();
    ct_updateTableTest();
    ct_deleteTableTest();
}

void ct_createTableTest()
{
	createTableTest_1();
	createTableTest_2();
    createTableTest_3();
    createTableTest_4();
    createTableTest_5();
    // createTableTest_6();
}

void ct_insertTableTest()
{
    insertTableTest_1();
    insertTableTest_2();
    insertTableTest_3();
    insertTableTest_4();
    //insertTableTest_5();
}

void ct_selectTableTest()
{
    selectTableTest_1();
    selectTableTest_2();
    selectTableTest_3();
    //selectTableTest_4();
}

void ct_updateTableTest()
{
    updateTableTest_1();
    updateTableTest_2();
    //updateTableTest_3();
}

void ct_deleteTableTest()
{
    deleteTableTest_1();
    //deleteTableTest_2();
}

void ct_dropTableTest()
{
    //dropTableTest_1();
}


void sameTableTest()
{
    int rc = 0;
    rc = closeTest_1();
    if(rc )
    {
        return;
    }
    rc = sameTableTestinit();
    if(rc )
    {
        return ;
    }
    sameTableTestCreate();
    sameTableTestInsert1();
    sameTableTestInsert2();
    sameTableTestInsertSelect();
    sameTableTestInsertUpdate();
    sameTableTestInsertDelete();
    sameTableTestSelect();
    sameTableTestSelectUpdete();
    sameTableTestSelectDelete();
    sameTableTestUpdate();
    sameTableTestUpdateDelete();
    sameTableTestDelete();
    //sameTableTestDropSelect();
    //sameTableTestDrop();
}

/* multi-thread 多线程并发测试项 */
void multiThreadTest()
{
    int rc = 0;
    rc = closeTest_1();
    if(rc )
    {
        return;
    }
    rc = multiThreadInit();
    if(rc)
    {
        return ;
    }
    multiCreateTable();
    multiInsertTable();
    multiSelectTable();
    multiUpdateDeleteTable();

}

int multiCreateTable()
{
    int rc = 0;
    createFlag = 0;
    multiCreateTable111();
    multiCreateTable112();
    multiCreateTable113();
    multiCreateTable114();
    multiCreateTable115();
    multiCreateTable122();
    multiCreateTable123();
    multiCreateTable124();
    multiCreateTable125();
    multiCreateTable133();
    multiCreateTable134();
    multiCreateTable135();
    multiCreateTable144();
    multiCreateTable145();
    multiCreateTable155();
    return rc;
}

int multiInsertTable()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    multiInsertTable222();
    multiInsertTable223();
    multiInsertTable224();
    multiInsertTable225();
    multiInsertTable233();
    multiInsertTable234();
    multiInsertTable235();
    multiInsertTable244();
    multiInsertTable245();
    multiInsertTable255();

    return rc;
}

int multiSelectTable()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiSelectTable333();
    multiSelectTable334();
    multiSelectTable335();
    multiSelectTable344();
    multiSelectTable345();
    multiSelectTable355();

    return rc;
}

int multiUpdateDeleteTable()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiUptateTable444();
    multiUptateTable445();
    multiUptateTable455();
    multiDeleteTable555();

    return rc;
}

int multiJoinTable()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    multiJoinTable116();
    multiJoinTable126();
    multiJoinTable136();
    multiJoinTable146();
    multiJoinTable156();
    multiJoinTable166();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = joinTestinsert1();
    if (rc) {
        return rc;
    }
    multiJoinTable226();
    multiJoinTable236();
    multiJoinTable246();
    multiJoinTable256();
    multiJoinTable266();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiJoinTable336();
    multiJoinTable346();
    multiJoinTable356();
    multiJoinTable366();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiJoinTable446();
    multiJoinTable456();
    multiJoinTable466();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }

    multiJoinTable556();
    multiJoinTable566();
    multiJoinTable666();

    return rc;
}

int sameTablemulti()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest222();
    sameTablemultiTest223();
    sameTablemultiTest224();
    sameTablemultiTest225();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest233();
    sameTablemultiTest234();
    sameTablemultiTest235();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest244();
    sameTablemultiTest245();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest255();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest333();
    sameTablemultiTest334();
    sameTablemultiTest335();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest344();
    sameTablemultiTest345();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest355();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest444();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest445();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest455();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiTest555();

    return rc;
}

int sameTablemultiJoin()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }

    sameTablemultiJoin226T2();
    sameTablemultiJoin236T2();
    sameTablemultiJoin246T2();
    sameTablemultiJoin256T2();
    sameTablemultiJoin266T2();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }

    sameTablemultiJoin336T2();
    sameTablemultiJoin346T2();
    sameTablemultiJoin356T2();
    sameTablemultiJoin366T2();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin446T2();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin456T2();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin466T2();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin556T2();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin566T2();
    sameTablemultiJoin666T2();

    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }

    sameTablemultiJoin226T1();
    sameTablemultiJoin236T1();
    sameTablemultiJoin246T1();
    sameTablemultiJoin256T1();
    sameTablemultiJoin266T1();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin336T1();
    sameTablemultiJoin346T1();
    sameTablemultiJoin356T1();
    sameTablemultiJoin366T1();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin446T1();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin456T1();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin466T1();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin556T1();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiJoin566T1();
    sameTablemultiJoin666T1();


    return 0;
}

int multiTableBlobTest()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    multiBlobTable117set();
    multiBlobTable117get();
    multiBlobTable117delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    multiBlobTable127set();
    multiBlobTable127get();
    multiBlobTable127delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    multiBlobTable137set();
    multiBlobTable137get();
    multiBlobTable137delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    multiBlobTable147set();
    multiBlobTable147get();
    multiBlobTable147delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    multiBlobTable157set();
    multiBlobTable157get();
    multiBlobTable157delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    multiBlobTable167set();
    multiBlobTable167get();
    multiBlobTable167delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = joinTestinsert1();
    if (rc) {
        return rc;
    }
    multiBlobTable227set();
    multiBlobTable227get();
    multiBlobTable227delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = joinTestinsert1();
    if (rc) {
        return rc;
    }
    multiBlobTable237set();
    multiBlobTable237get();
    multiBlobTable237delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = joinTestinsert1();
    if (rc) {
        return rc;
    }
    multiBlobTable247set();
    multiBlobTable247get();
    multiBlobTable247delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = joinTestinsert1();
    if (rc) {
        return rc;
    }
    multiBlobTable257set();
    multiBlobTable257get();
    multiBlobTable257delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = joinTestinsert1();
    if (rc) {
        return rc;
    }
    multiBlobTable267set();
    multiBlobTable267get();
    multiBlobTable267delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable337set();
    multiBlobTable337get();
    multiBlobTable337delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable347set();
    multiBlobTable347get();
    multiBlobTable347delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable357set();
    multiBlobTable357get();
    multiBlobTable357delete();
    multiBlobTable367set();
    multiBlobTable367get();
    multiBlobTable367delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable447set();
    multiBlobTable447get();
    multiBlobTable447delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable457set();
    multiBlobTable457get();
    multiBlobTable457delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable467set();
    multiBlobTable467get();
    multiBlobTable467delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable557set();
    multiBlobTable557get();
    multiBlobTable557delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable567set();
    multiBlobTable567get();
    multiBlobTable567delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    multiBlobTable667set();
    multiBlobTable667get();
    multiBlobTable667delete();

    return rc;
}

int sameTablemultiBlob()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob227set();
    sameTablemultiBlob227get();
    sameTablemultiBlob227delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob237set();
    sameTablemultiBlob237get();
    sameTablemultiBlob237delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob247set();
    sameTablemultiBlob247get();
    sameTablemultiBlob247delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob257set();
    sameTablemultiBlob257get();
    sameTablemultiBlob257delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob267set();
    sameTablemultiBlob267get();
    sameTablemultiBlob267delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob337set();
    sameTablemultiBlob337get();
    sameTablemultiBlob337delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob347set();
    sameTablemultiBlob347get();
    sameTablemultiBlob347delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob357set();
    sameTablemultiBlob357get();
    sameTablemultiBlob357delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob367set();
    sameTablemultiBlob367get();
    sameTablemultiBlob367delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob447set();
    sameTablemultiBlob447get();
    sameTablemultiBlob447delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob457set();
    sameTablemultiBlob457get();
    sameTablemultiBlob457delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob467set();
    sameTablemultiBlob467get();
    sameTablemultiBlob467delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob557set();
    sameTablemultiBlob557get();
    sameTablemultiBlob557delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob567set();
    sameTablemultiBlob567get();
    sameTablemultiBlob567delete();
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc =  setJoinOrBlobTest();
    if (rc) {
        return rc;
    }
    sameTablemultiBlob667set();
    sameTablemultiBlob667get();
    sameTablemultiBlob667delete();

    return rc;
}


int multiSpecialTest()
{
    int rc = 0;
    rc = multiThreadReset();
    if (rc) {
        return rc;
    }
    rc = createAllTable();
    if (rc) {
        return rc;
    }
    rc = insertAllTable();
    if (rc) {
        return rc;
    }
    sql_multiAggregateFunTestOtherTable1();
    sql_multiAggregateFunTestOtherTable2();
    sql_multiAggregateFunTestOtherTable3();
    // sql_multiAggregateFunTesOtherTable4();
    sql_multiAggregateFunTestSameTable1();
    sql_multiAggregateFunTestSameTable2();
    sql_multiAggregateFunTestSameTable3();
    sql_multiAggregateFunTesSameTable4();

    return rc;
}
