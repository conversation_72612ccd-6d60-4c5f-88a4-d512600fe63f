#ifndef THREAD_POOL_H
#define THREAD_POOL_H

#include <pthread.h>
#include "gncdb.h"

#define PTHREAD_MAXCOUNT        10          /* 线程最大数量 */

/* 任务结构体 */
typedef struct {
    int (*taskFunction)(void* arg); /* 任务函数指针 */
    void* arg; /* 任务参数 */
} Task;

/* 线程池结构体 */
typedef struct {
    pthread_t* threads; /* 线程数组指针 */
    int numThreads; /* 线程数量 */
    Task taskQueue[PTHREAD_MAXCOUNT]; /* 任务队列 */
    int queueFront; /* 队列首索引 */
    int queueRear; /* 队列尾索引 */
    int queueSize; /* 队列大小 */
    Semaphore queueMutex;
//    pthread_mutex_t queueMutex; /* 队列互斥锁 */
//    pthread_cond_t queueNotEmpty; /* 队列非空条件变量 */
    int taskCounter; /* 任务计数器 */
    Semaphore counterMutex;
//    pthread_mutex_t counterMutex; /* 计数器互斥锁 */
//    pthread_cond_t counterEmpty; /* 计数器为空条件变量 */
    int destroyFlag;  /* 是否销毁的标志 */

    int ct_count;   /* 一次运行的线程数量 */
    int rcindex; /* 在测试中记录返回值 */
    int pthrc[PTHREAD_MAXCOUNT];/* 函数返回值 */
} ThreadPool;

/* 创建线程池 */
ThreadPool* threadPoolCreate(int numThreads);

/* 线程任务 */
void* threadPoolWorker(void* arg);

/* 销毁线程池 */
void threadPoolDestroy(ThreadPool* pool);

/* 添加任务到线程池 */
void threadPoolAddTask(ThreadPool* pool, int (*taskFunction)(void* arg), void* arg);

/* 等待所有任务完成 */
void threadPoolWaitAllTasks(ThreadPool* pool);

#endif /* THREAD_POOL_H */