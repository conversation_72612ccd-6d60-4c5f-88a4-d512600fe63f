
#include "concurrence.h"
#include "btreetable.h"



/*创建线程并运行函数*/
int create_pthreadInit(CT_Pthread* item)
{
    int i = 0;
    ct_Global.pthreadPool->rcindex = 0;
    ct_Global.pthreadPool->ct_count = item->ptCount;
    ct_Global.pthreadPool->taskCounter = 0;
    for(i = 0; i < item->ptCount; ++i)
    {
        threadPoolAddTask(ct_Global.pthreadPool, item->threadFuncs[i], (void*)item);
    }

    threadPoolWaitAllTasks(ct_Global.pthreadPool);

    for(i = 0; i < item->ptCount; ++i)
    {
        item->rc = ct_Global.pthreadPool->pthrc[i];
        if(item->rc != 0)
        {
            break;
        }
    }
	return 0;
}

/* 天脉系统 */

//void CREATE_PROCESS(
//        PROCESS_ATTRIBUTE_TYPE *ATTRIBUTES,
//        PROCESS_ID_TYPE *ID,
//        RETURN_CODE_TYPE *RETURN_CODE
//)
//typedef struct
//{
//PROCESS_NAME_TYPE NAME; /* 进程名 */
//SYSTEM_ADDRESS_TYPE ENTRY_POINT; /* 进程入口点 */
//STACK_SIZE_TYPE STACK_SIZE; /* 进程栈大小 */
//PRIORITY_TYPE BASE_PRIORITY; /* 进程基础优先级 */
//SYSTEM_TIME_TYPE PERIOD; /* 进程周期 */
//SYSTEM_TIME_TYPE TIME_CAPACITY; /* 进程时间容量 */
//DEADLINE_TYPE DEADLINE; /* 进程截止期类型 */
//} PROCESS_ATTRIBUTE_TYPE
//void GET_MY_ID(
//        PROCESS_ID_TYPE *PROCESS_ID,
//        RETURN_CODE_TYPE *RETURN_CODE
//)
//void GET_TIME (
//        SYSTEM_TIME_TYPE *SYSTEM_TIME,
//        RETURN_CODE_TYPE *RETURN_CODE
//)

#if 0
int reurnFlag = 0;
int pthreadPool_pthreadFun1(void* arg)
{
    int rc = reurnFlag++;
    int count = 0;
    PROCESS_ID_TYPE PROCESS_ID;
    RETURN_CODE_TYPE RETURN_CODE;
    SYSTEM_TIME_TYPE SYSTEM_TIME;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    GET_MY_ID(&PROCESS_ID, &RETURN_CODE);
    if(RETURN_CODE != NO_ERROR)
    {
        ct_p->rcArray[0] = -1;
        return -1;
    }
    for(count = 0; count < 10; count++)
    {
        GET_TIME(&SYSTEM_TIME, &RETURN_CODE);
        if(RETURN_CODE != NO_ERROR)
        {
            ct_p->rcArray[0] = -1;
            return -1;
        }
        printf("current pthread ID : %d", PROCESS_ID);
    }
    ct_p->rcArray[0] = rc;
    return rc;
}

int pthreadPoolTest_1()
{
    CT_Pthread item;
    int i = 0;
    printf("天脉系统线程池并发测试:\n");

    item.ptCount = 2;
    item.threadFuncs[0] = pthreadPool_pthreadFun1;
    item.threadFuncs[1] = pthreadPool_pthreadFun1;

    create_pthreadInit(&item);
    for(; i < item.ptCount; ++i)
    {
        printf("item.rc: %d, pthread.rc: %d\n", item.rcArray[i], ct_Global.pthreadPool->pthrc[i]);
    }

    return 0;
}
#endif


int createTableTest_1()
{
	CT_Pthread item;
	item.project = "创建表";
	item.operation = "使用两个线程同时创建ARPT和WPT表";
	item.message = "CREATE TABLE ARPT("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL);"
                   ",CREATE TABLE WPT("
                   "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL"
                   "WPT_lon REAL  NOT NULL "
                   "WPT_lat  REAL NOT NULL"
                   "WPT_blob  BLOB);";

	item.ptCount = 2;
	item.threadFuncs[0] = createARPTtable;
	item.threadFuncs[1] = createWPTtable;

	create_pthreadInit(&item);
	if (item.rc != 0)
	{
		CT_updatamessage(&item);
		return -1;
	}

	CT_tool.tableName = "ARPT";
	GNCDB_select(ct_Global.db, ct_callBack1, NULL, NULL, 1, 0, 1, "master", "tableName=ARPT");
	if (CT_tool.testFlag == false)
	{
		item.rc = -1;
		CT_updatamessage(&item);
		return -1;
	}
	CT_tool.tableName = "WPT";
	GNCDB_select(ct_Global.db, ct_callBack1, NULL, NULL, 1, 0, 1, "master", "tableName=WPT");
	if (CT_tool.testFlag == false)
	{
		item.rc = -1;
		CT_updatamessage(&item);
		return -1;
	}

//	ct_initFlag();
//	GNCDB_select(ct_Global.db, ct_CallBack, NULL, NULL, 1, 0, 0, "master");
//	ct_initFlag();
//	GNCDB_select(ct_Global.db, ct_CallBack, NULL, NULL, 1, 0, 0, "schema");

	CT_updatamessage(&item);

	return 0;
}

int createTableTest_2()
{
	CT_Pthread item;
	item.project = NULL;
	item.operation = "使用两个线程创建NAV表和向ARPT表插入数据";
	item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL)"
                   ",INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var)";

	item.ptCount = 2;
	item.threadFuncs[0] = createNAVtable;
	item.threadFuncs[1] = insertARPTtable;

	create_pthreadInit(&item);
	if (item.rc != 0)
	{
		CT_updatamessage(&item);
		return -1;
	}

	CT_tool.tableName = "NAV";
	GNCDB_select(ct_Global.db, ct_callBack1, NULL, NULL, 1, 0, 1, "master", "tableName=NAV");
	if (CT_tool.testFlag == false)
	{
		item.rc = -1;
		CT_updatamessage(&item);
		return -1;
	}
	CT_tool.rowcount[0] = 0;
	GNCDB_select(ct_Global.db, ct_callBackinsert1, NULL, NULL, 1, 0, 0, "ARPT");
	if (CT_tool.rowcount[0] != CT_ARPTROWS)
	{
		item.rc = -1;
		CT_updatamessage(&item);
		return -1;
	}

	CT_updatamessage(&item);

	return 0;
}

int createTableTest_3()
{
	CT_Pthread item;
	item.project = NULL;
	item.operation = "使用两个线程创建SID表和查询ARPT表数据";
	item.message = "CREATE TABLE SID ("
                   "SID_arptident CHAR(8) NOT NULL"
                   "SID_sidident  CHAR(8) PRIMARY KEY NOT NULL"
                   "SID_no CHAR(2) NOT NULL"
                   "SID_type CHAR(2) NOT NULL"
                   "SID_track REAL NOT NULL"
                   "SID_fix CHAR(8) NOT NULL"
                   "SID_cat CHAR(2) NOT NULL"
                   "SID_lon REAL NOT NULL"
                   "SID_lat REAL NOT NULL"
                   "SID_rnp REAL NOT NULL"
                   "SID_alt REAL NOT NULL"
                   "),"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0";

	item.ptCount = 2;
	item.threadFuncs[0] = createSIDtable;
	item.threadFuncs[1] = selectARPTtable_1C;

	create_pthreadInit(&item);
	if (item.rc != 0)
	{
		CT_updatamessage(&item);
		return -1;
	}
    CT_tool.tableName = "SID";
    GNCDB_select(ct_Global.db, ct_callBack1, NULL, NULL, 1, 0, 1, "master", "tableName=SID");
    if(CT_tool.testFlag == false || item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
	CT_updatamessage(&item);

	return 0;
}

int createTableTest_4()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程创建APCH表和更新ARPT表";
    item.message = "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "UPDATE ARPT SET　ARPT_lon=25.00　WHERE ARPT_ident=VLSTG";

    item.ptCount = 2;
    item.threadFuncs[0] = createAPCHtable;
    item.threadFuncs[1] = updateARPTtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.tableName = "APCH";
    GNCDB_select(ct_Global.db, ct_callBack1, NULL, NULL, 1, 0, 1, "master", "tableName=APCH");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident=VLSTG");
    if(CT_tool.testFlag == false || item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    CT_updatamessage(&item);

    return 0;
}

int createTableTest_5()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程创建DEFINE_WPT表和删除ARPT表";
    item.message = "CREATE TABLE DEFINE_WPT("
                   "DEFINE_WPT_ident CHAR(8) PRIMARY KEY NOT NULL"
                   "DEFINE_WPT_lon REAL NOT NULL"
                   "DEFINE_WPT_lat REAL NOT NULL"
                   "),"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 2;
    item.threadFuncs[0] = createDEFINEWPTtable;
    item.threadFuncs[1] = deleteARPTtable_1C;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.tableName = "DEFINE_WPT";
    GNCDB_select(ct_Global.db, ct_callBack1, NULL, NULL, 1, 0, 1, "master", "tableName=DEFINE_WPT");
    if(CT_tool.testFlag == false || item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    CT_updatamessage(&item);

    return 0;
}

int createTableTest_6()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程创建FPLN表和销毁ARPT表";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "DROP TABLE ARPT";

    item.ptCount = 2;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = dropARPTtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.tableName = "FPLN";
    GNCDB_select(ct_Global.db, ct_callBack1, NULL, NULL, 1, 0, 1, "master", "tableName=FPLN");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=ARPT");
    if(CT_tool.testFlag == false || CT_tool.rowcount[0] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    CT_updatamessage(&item);

    return 0;
}

int insertTableTest_1()
{
    CT_Pthread item;
    int rc = 0;
    item.project = "表插入";
    item.operation = "使用两个线程插入WPT表和NAV表";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),";

    item.ptCount = 2;
    item.threadFuncs[0] = insertWPTtable;
    item.threadFuncs[1] = insertNAVtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackinsert1, NULL, NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, NULL, &(CT_tool.rowcount[1]), NULL, 1, 0, 0, "NAV");
    if (CT_tool.rowcount[0] != CT_WPTROWS || CT_tool.rowcount[1] != CT_NAVROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(rc){
        
    }
    CT_updatamessage(&item);
    return 0;
}

int insertTableTest_2()
{
	CT_Pthread item;
	item.project = NULL;
	item.operation = "使用两个线程插入SID表和查询WPT表";
	item.message = "INSERT INTO SID(SID_arptident SID_sidident SID_no SID_type SID_track SID_fix SID_cat SID_lon "
                   "SID_lat SID_rnp SID_alt)"
                   "VALUES(ct_sid.sc8_arpt_ident ct_sid.sc8_sid_ident ct_sid.sc8_seq_no ct_sid.sc8_flight_seg_type "
                   "ct_sid.f64_track ct_sid.sc8_fix_ident ct_sid.sc8_fix_cat ct_sid.f64_lon ct_sid.f64_lat "
                   "ct_sid.f64_rnp ct_sid.f64_alt),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0";

    item.ptCount = 2;
    item.threadFuncs[0] = insertSIDtable;
    item.threadFuncs[1] = selectWPTtable_2C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
	CT_tool.rowcount[0] = 0;
	GNCDB_select(ct_Global.db, ct_callBackinsert1, NULL, NULL, 1, 0, 0, "SID");
	if (CT_tool.rowcount[0] != CT_SIDROWS || CT_tool.rowcount[1] != item.selectCount[1])
	{
		item.rc = -1;
		CT_updatamessage(&item);
		return -1;
	}
	CT_updatamessage(&item);
	return 0;
}

int insertTableTest_3()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程插入APCH表时更新WPT表";
    item.message = "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "UPDATE WPT SET WPT_lat=25.25"
                   "WHERE WPT_ident>=YQTGU";

    item.ptCount = 2;
    item.threadFuncs[0] = insertAPCHtable;
    item.threadFuncs[1] = updateWPTtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackinsert1, NULL, NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    if (CT_tool.rowcount[0] != CT_APCHROWS || CT_tool.rowcount[1] != item.selectCount[0] || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int insertTableTest_4()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程插入DEFINE_WPT表时删除WPT表";
    item.message = "INSERT INTO DEFINE_WPT(DEFINE_WPT_ident DEFINE_WPT_lon DEFINE_WPT_lat)"
                   "VALUES(ct_definewpt.sc8_wpt_ident ct_definewpt.f64_lon ct_definewpt.f64_lat),"
                   "DELETE FROM WPT WHERE WPT_lon<-22.5";

    item.ptCount = 2;
    item.threadFuncs[0] = insertDEFINEWPTtable;
    item.threadFuncs[1] = deleteWPTtable_1C1;
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "WPT", "WPT_lon<-22.5");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackinsert1, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    if(CT_tool.rowcount[0] != CT_DEFINEWPTROWS || CT_tool.rowcount[1] != item.selectCount[0] || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int insertTableTest_5()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程插入FPLN表时销毁WPT表";
    item.message = "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "DROP TABLE WPT";

    item.ptCount = 2;
    item.threadFuncs[0] = insertFPLNtable;
    item.threadFuncs[1] = dropWPTtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackinsert1, NULL, NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &(CT_tool.rowcount[1]), NULL, 1, 0, 1, "master", "tableName=WPT");
    if(CT_tool.rowcount[0] != CT_FPLNROWS || CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

/* 在单线程测试时已将多个条件的数据多种情况进行测试，目前基础功能只考虑2个条件 */
int selectTableTest_1()
{
    CT_Pthread item;
    item.project = "表查询";
    item.operation = "使用两个线程查询NAV表和SID表";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM SID WHERE SID_sidident<=HYUBW";

    item.ptCount = 2;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectSIDtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int selectTableTest_2()
{
    CT_Pthread item;
    int rows = 0;
    item.project = NULL;
    item.operation = "使用两个线程查询APCH表和更新NAV表";
    item.message = "SELECT * FROM APCH WHERE APCH_apchident=XZDH,"
                   "UPDATE NAV SET NAV_vor=0.000 WHERE NAV_lat=-45.000000";

    item.ptCount = 2;
    item.threadFuncs[0] = selectAPCHtable_1C1;
    item.threadFuncs[1] = updateNAVtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateNAVtable_1C, &rows, NULL, 1, 0, 0, "NAV");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || rows != CT_NAVROWS || item.selectCount[1] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int selectTableTest_3()
{
    CT_Pthread item;
    int rows = 0;
    item.project = NULL;
    item.operation = "使用两个线程查询DEFINE_WPT表和删除NAV表";
    item.message = "SELECT * FROM DEFINE_WPT WHERE DEFINE_WPT_lon>=45.0 DEFINE_WPT_lat<-45.0 , "
                   "DELETE FROM NAV WHERE NAV_ident<XFQTZ";

    item.ptCount = 2;
    item.threadFuncs[0] = selectDEFINE_WPTtable_2C1;
    item.threadFuncs[1] = deleteNAVtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "NAV");
    rows = CT_tool.rowcount[1] + item.selectCount[1];
    if(CT_tool.rowcount[0] != item.selectCount[0] || rows != CT_NAVROWS || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int selectTableTest_4()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程查询FPLN表和销毁NAV表";
    item.message = "SELECT * FROM FPLN WHERE FPLN_fixcat=2, "
                   "DROP TABLE NAV";

    item.ptCount = 2;
    item.threadFuncs[0] = selectFPLNtable_1C1;
    item.threadFuncs[1] = dropNAVtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=NAV");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int updateTableTest_1()
{
    CT_Pthread item;
    item.project = "表更新";
    item.operation = "使用两个线程更新SID表和APCH表";
    item.message = "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1";

    item.ptCount = 2;
    item.threadFuncs[0] = updateSIDtable_1C;
    item.threadFuncs[1] = updateAPCHtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || item.selectCount[0] == 0 || item.selectCount[1] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int updateTableTest_2()
{
    CT_Pthread item;
    int rows = 0;
    item.project = NULL;
    item.operation = "使用两个线程更新DEFINE_WPT表和删除SID表";
    item.message = "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA, "
                   "DELETE FROM SID WHERE SID_type=2";

    item.ptCount = 2;
    item.threadFuncs[0] = updateDEFINE_WPTtable_1C;
    item.threadFuncs[1] = deleteSIDtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    rows = CT_tool.rowcount[1] + item.selectCount[1];
    if(CT_tool.rowcount[0] != item.selectCount[0] || rows != CT_NAVROWS || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int updateTableTest_3()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程更新FPLN表和销毁SID表";
    item.message = "UPDATE FPLN SET FPLN_stage1=1 FPLN_stage2=1 WHERE FPLN_lon>=0.00 FPLN_lon<=25.00, "
                   "DROP TABLE SID";

    item.ptCount = 2;
    item.threadFuncs[0] = updateFPLNtable_2C;
    item.threadFuncs[1] = dropSIDtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateFPLNtable_2C, NULL, NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=SID");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int deleteTableTest_1()
{
    CT_Pthread item;
    int rows = 0;
    int testRow1 = 0;
    int testRow2 = 0;
    item.project = "表删除";
    item.operation = "使用两个线程删除APCH表和DEFINE_WPT表";
    item.message = "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0";

    item.ptCount = 2;
    item.threadFuncs[0] = deleteAPCHtable_2C;
    item.threadFuncs[1] = deleteDEFINE_WPTtable_2C;

    GNCDB_select(ct_Global.db, NULL, &rows, NULL, 1, 0, 0, "APCH");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    testRow1 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    testRow2 = CT_tool.rowcount[1] + item.selectCount[1];
    if(testRow1 != rows || testRow2 != CT_DEFINEWPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int deleteTableTest_2()
{
    CT_Pthread item;
    int rows = 0;
    item.project = NULL;
    item.operation = "使用两个线程删除FPLN表和销毁APCH表";
    item.message = "DELETE FROM FPLN WHERE FPLN_ident>SLMWREZVQG, "
                   "DROP TABLE APCH";

    item.ptCount = 2;
    item.threadFuncs[0] = deleteFPLNtable_1C;
    item.threadFuncs[1] = dropAPCHtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "FPLN");
    rows = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=APCH");
    if(rows != CT_FPLNROWS || CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int dropTableTest_1()
{
    CT_Pthread item;
    item.project = "销毁表";
    item.operation = "使用两个线程销毁DEFINE_WPT表和FPLN表";
    item.message = "DROP TABLE DEFINE_WPT, "
                   "DROP TABLE FPLN";

    item.ptCount = 2;
    item.threadFuncs[0] = dropDEFINEWPTtable;
    item.threadFuncs[1] = dropFPLNtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=DEFINE_WPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=FPLN");
    if(CT_tool.rowcount[0] != 0 || CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int closeTest_1()
{
    CT_Pthread item;
    item.project = "关闭数据库";
    item.operation = "关闭当前数据库";
    item.message = "QUIT";

    item.rc = GNCDB_close(&(ct_Global.db));

    CT_updatamessage(&item);
    return item.rc;
}

/* 针对单表测试的测试项 */

int sameTableTestinit()
{
    CT_Pthread item;
    item.project = "同表测试";
    item.operation = "创建数据库";
    item.message = "CREATE DB ct_same.dat";

#if defined _WIN32
	    remove("..\\ct_same.dat");
    remove("..\\log_ct_same.dat");

    item.rc = GNCDB_open(&(ct_Global.db), "..\\ct_same.dat", 0, 0);
#else
	remove("ct_same.dat");
    remove("log_ct_same.dat");
    item.rc = GNCDB_open(&(ct_Global.db), "ct_same.dat", 0, 0);
#endif



    CT_updatamessage(&item);
    return item.rc;
}

int sameTableTestCreate()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建ARPT表、WPT表、NAV表";
    item.message = "CREATE TABLE ARPT("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL);"
                   ",CREATE TABLE WPT("
                   "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL"
                   "WPT_lon REAL  NOT NULL "
                   "WPT_lat  REAL NOT NULL"
                   "WPT_blob  BLOB);,"
                   "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL)";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = createWPTtable;
    item.threadFuncs[2] = createARPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackSameTableTestCreate, NULL, NULL, 1, 0, 0, "master");
    if(CT_tool.rowcount[0] != 3)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestInsert1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT表、WPT表、NAV表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var)"
                   ",INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb)";

    item.ptCount = 3;
    item.threadFuncs[0] = insertNAVtable;
    item.threadFuncs[1] = insertWPTtable;
    item.threadFuncs[2] = insertARPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "NAV");
    if(CT_tool.rowcount[0] != CT_ARPTROWS || CT_tool.rowcount[1] != CT_WPTROWS || CT_tool.rowcount[2] != CT_NAVROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestInsert2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程同时对NAV表插入50条数据";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb)";
    item.ptCount = 2;
    item.threadFuncs[0] = insertNAVtable_SameTab;
    item.threadFuncs[1] = insertNAVtable_SameTab;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_NAVROWS;
    CT_releTest.reuse = true;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    if(CT_tool.rowcount[0] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestInsertSelect()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对NAV表插入数据并查询";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5";

    item.ptCount = 2;
    item.threadFuncs[0] = insertNAVtable_SameTab;
    item.threadFuncs[1] = selectNAVtable_2C1;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "NAV");
    if(CT_tool.rowcount[1] != CT_releTest.allRows || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestInsertUpdate()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对NAV表插入50条数据并更新";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "UPDATE NAV SET NAV_vor=102.0101 WHERE NAV_ident=LDUTL";
    item.ptCount = 2;
    item.threadFuncs[0] = insertNAVtable_SameTab;
    item.threadFuncs[1] = updateNAVtable_1C2;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackupdateNAVtable_1C2, NULL, NULL, 1, 0, 1, "NAV", "NAV_ident=LDUTL");
    if(CT_tool.rowcount[0] != CT_releTest.allRows || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestInsertDelete()
{
    BtreeTable* table =  NULL;

    CT_Pthread item;
    int rc = 0;
    int row = 0;
    item.project = NULL;
    item.operation = "使用线程对NAV表插入50条数据并删除";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "DELETE FROM NAV WHERE NAV_ident<XFQTZ";
    item.ptCount = 2;
    item.threadFuncs[0] = insertNAVtable_SameTab;
    item.threadFuncs[1] = deleteNAVtable_1C1;

    rc = GNCDB_select(ct_Global.db, NULL, &row, NULL, 1, 0, 0, "NAV");

    create_pthreadInit(&item);
    if (item.rcArray[1] != 0)
    {
        catalogGetTable(ct_Global.db->catalog, &table, "NAV");
//        printBtreeToFile(table, ct_Global.db);
        //rc = GNCDB_select(ct_Global.db, ct_CallBack, NULL, NULL, 1, 0, 0, "NAV");
//        // printf("rollbackrow = %d\n", rollbackrow);
        CT_updatamessage(&item);
        return rc;
    }
    catalogGetTable(ct_Global.db->catalog, &table, "NAV");
    printBtreeToFile(table, ct_Global.db);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "NAV", "NAV_ident<XFQTZ");
    if(CT_tool.rowcount[0] + item.selectCount[1] != row + item.selectCount[3])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTableTestSelect()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对NAV表同时进行查询";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993";
    item.ptCount = 2;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectNAVtable_2C2;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestSelectUpdete()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对NAV表进行查询和更新";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "UPDATE NAV SET NAV_vor=0.000 WHERE NAV_lat=-45.000000";
    item.ptCount = 2;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = updateNAVtable_1C1;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateNAVtable_1C, NULL, NULL, 1, 0, 0, "NAV");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestSelectDelete()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对NAV表进行查询和删除";
    item.message = "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993,"
                   "DELETE FROM NAV WHERE NAV_lat>125.0";
    item.ptCount = 2;
    item.threadFuncs[0] = selectNAVtable_2C2;
    item.threadFuncs[1] = deleteNAVtable_1C2;
    create_pthreadInit(&item);
    if (item.rc != 0) {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "NAV", "NAV_lat>125.0");
    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestUpdate()
{
    int rc = 0;
    int latRow = 0, lonRow = 0;
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对WPT表同时进行更新";
    item.message = "UPDATE WPT SET WPT_lat=25.25"
                   "WHERE WPT_ident>=YQTGU,"
                   "UPDATE WPT SET WPT_lon=-25.25"
                   "WHERE WPT_ident>=YQTGU";
    item.ptCount = 2;
    item.threadFuncs[0] = updateWPTtable_1C1;
    item.threadFuncs[1] = updateWPTtable_1C2;

    CT_tool.rowcount[0] = 0;
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    latRow = CT_tool.rowcount[0];
    lonRow = CT_tool.rowcount[1];

    create_pthreadInit(&item);
//    if (item.rc != 0) {
//        CT_updatamessage(&item);
//        return -1;
//    }
    CT_tool.rowcount[0] = 0;
    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    if(ct_Global.pthreadPool->pthrc[0] == 0 && ct_Global.pthreadPool->pthrc[1] == 0)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }

        CT_updatamessage(&item);
    }
    else if(ct_Global.pthreadPool->pthrc[0] == 0 && ct_Global.pthreadPool->pthrc[1] != 0)
    {
        if(rc != 0 || CT_tool.rowcount[1] != lonRow || CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    else if(ct_Global.pthreadPool->pthrc[0] != 0 && ct_Global.pthreadPool->pthrc[1] == 0)
    {
        if(rc != 0 || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != latRow)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    else
    {
        if(rc != 0 || CT_tool.rowcount[1] != lonRow || CT_tool.rowcount[0] != latRow)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    return 0;
}

int sameTableTestUpdateDelete()
{
    CT_Pthread item;
    int rowsUp = 0, rowsDe = 0;
    int rc = 0;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新和删除操作";
    item.message = "UPDATE ARPT SET　ARPT_length=25.00"
                   "WHERE ARPT_ident>=VLSTG,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";
    item.ptCount = 2;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C;

    GNCDB_select(ct_Global.db, NULL, &rowsDe, NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    rowsUp = CT_tool.rowcount[1];

    create_pthreadInit(&item);

    CT_tool.rowcount[0] = 0;
    rc = GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");

    if(ct_Global.pthreadPool->pthrc[0] == 0 && ct_Global.pthreadPool->pthrc[1] == 0)
    {
        if(rc != 0 || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != 0)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }

        CT_updatamessage(&item);
    }
    else if(ct_Global.pthreadPool->pthrc[0] == 0 && ct_Global.pthreadPool->pthrc[1] != 0)
    {
        if(rc != 0 || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != rowsDe)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    else if(ct_Global.pthreadPool->pthrc[0] != 0 && ct_Global.pthreadPool->pthrc[1] == 0)
    {
        if(rc != 0 || CT_tool.rowcount[1] != rowsUp || CT_tool.rowcount[0] != 0)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    else
    {
        if(rc != 0 || CT_tool.rowcount[1] != rowsUp || CT_tool.rowcount[0] != rowsDe)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    return 0;
}

int sameTableTestDelete()
{
    CT_Pthread item;
    int row0 = 0, row1 = 0;
    int rc = 0;
    item.project = NULL;
    item.operation = "使用线程对WPT表删除操作";
    item.message = "DELETE FROM WPT WHERE WPT_lon<-22.5,"
                   "DELETE FROM WPT WHERE WPT_lat<-22.5";
    item.ptCount = 2;
    item.threadFuncs[0] = deleteWPTtable_1C1;
    item.threadFuncs[1] = deleteWPTtable_1C2;

    GNCDB_select(ct_Global.db, NULL, &row0, NULL, 1, 0, 1, "WPT", "WPT_lon<-22.5");
    GNCDB_select(ct_Global.db, NULL, &row1, NULL, 1, 0, 1, "WPT", "WPT_lat<-22.5");

    create_pthreadInit(&item);

    CT_tool.rowcount[0] = 0;
    rc = GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "WPT", "WPT_lon<-22.5");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "WPT", "WPT_lat<-22.5");

    if(ct_Global.pthreadPool->pthrc[0] == 0 && ct_Global.pthreadPool->pthrc[1] == 0)
    {
        if(rc != 0 || CT_tool.rowcount[0] != 0 || CT_tool.rowcount[1] != 0)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }

        CT_updatamessage(&item);
    }
    else if(ct_Global.pthreadPool->pthrc[0] == 0 && ct_Global.pthreadPool->pthrc[1] != 0)
    {
        if(rc != 0 || CT_tool.rowcount[0] != 0 || CT_tool.rowcount[1] != row1)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    else if(ct_Global.pthreadPool->pthrc[0] != 0 && ct_Global.pthreadPool->pthrc[1] == 0)
    {
        if(rc != 0 || CT_tool.rowcount[0] != row0 || CT_tool.rowcount[1] != 0)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    else
    {
        if(rc != 0 || CT_tool.rowcount[0] != row0 || CT_tool.rowcount[1] != row1)
        {
            item.rc = rc;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
        CT_updatamessage(&item);
    }
    return 0;
}

int sameTableTestDropSelect()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程销毁NAV表并进行查询";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "DROP TABLE NAV";
    item.ptCount = 2;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = dropNAVtable;
    create_pthreadInit(&item);
    if (item.rc != 0) {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=NAV");
    if(CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTableTestDrop()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程销毁ARPT表和WPT表";
    item.message = "DROP TABLE ARPT,"
                   "DROP TABLE WPT";
    item.ptCount = 2;
    item.threadFuncs[0] = dropARPTtable;
    item.threadFuncs[1] = dropWPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0) {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "master");
    if(CT_tool.rowcount[0] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

/* 表间连接测试项 */
int joinTestInit()
{
    CT_Pthread item;
    item.project = "连接测试";
    item.operation = "创建数据库";
    item.message = "CREATE DB ct_join.dat";

    remove("ct_join.dat");
    remove("log_ct_join.dat");

    item.rc = GNCDB_open(&(ct_Global.db), "ct_join.dat", 0, 0);

    CT_updatamessage(&item);
    return item.rc;
}

int joinTestCreate()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建ARPT WPT NAV表";
    item.message = "CREATE TABLE ARPT("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL);"
                   ",CREATE TABLE WPT("
                   "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL"
                   "WPT_lon REAL  NOT NULL "
                   "WPT_lat  REAL NOT NULL"
                   "WPT_blob  BLOB),"
                   "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL)";

    item.ptCount = 3;
    item.threadFuncs[0] = createARPTtable;
    item.threadFuncs[1] = createWPTtable;
    item.threadFuncs[2] = createNAVtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackSameTableTestCreate, NULL, NULL, 1, 0, 0, "master");
    if(CT_tool.rowcount[0] != 3)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int joinTestinsert1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT WPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var)"
                   ",INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL)";

    item.ptCount = 2;
    item.threadFuncs[0] = insertARPTtable;
    item.threadFuncs[1] = insertWPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }


    CT_updatamessage(&item);
    return 0;
}

int joinTestInsert2()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程连接ARPT和WPT表时对ARPT表进行插入";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                    "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var)";

    item.ptCount = 2;
    item.threadFuncs[0] = selectJoinTable;
    item.threadFuncs[1] = insertARPTtable_SameTab;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[0] = 0;
    jointool.callBack = ct_callBackJoinTestInsert2;
    item.jointool = &jointool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(item.selectCount[0] != CT_tool.rowcount[0] || CT_tool.rowcount[1] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int joinTestSelect()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程连接ARPT和WPT表时查询ARPT表";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_ident=ARPT_ident,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0";

    item.ptCount = 2;
    item.threadFuncs[0] = selectJoinTable;
    item.threadFuncs[1] = selectARPTtable_2C;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_ident=ARPT_ident";
    CT_tool.rowcount[0] = 0;
    jointool.callBack = ct_callBackJoinTestSelect;
    item.jointool = &jointool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    if(item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int joinTestUpdate()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程连接ARPT和WPT表时更新ARPT表";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_ident=ARPT_ident,"
                   "UPDATE ARPT SET　ARPT_length=25.00"
                   "WHERE ARPT_ident>=VLSTG";

    item.ptCount = 2;
    item.threadFuncs[0] = selectJoinTable;
    item.threadFuncs[1] = updateARPTtable_1C2;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_ident=ARPT_ident";
    CT_tool.rowcount[0] = 0;
    jointool.callBack = ct_callBackJoinTestSelect;
    item.jointool = &jointool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if(item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int joinTestDelete()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程连接ARPT和WPT表时对WPT表进行删除";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "DELETE FROM WPT WHERE WPT_lat<-22.5";

    item.ptCount = 2;
    item.threadFuncs[0] = selectJoinTable;
    item.threadFuncs[1] = deleteWPTtable_1C2;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[0] = 0;
    jointool.callBack = ct_callBackJoinTestInsert2;
    item.jointool = &jointool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    if(item.selectCount[0] != CT_tool.rowcount[0] || CT_tool.rowcount[1] + item.selectCount[1] != CT_WPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

/* multi-thread 多线程并发测试项 */

int multiThreadInit()
{
    CT_Pthread item;
    item.project = "多线程并发测试";
    item.operation = "创建数据库";
    item.message = "CREATE DB ct_multi.dat";

#if defined _WIN32
    remove(".\\ct_multi.dat");
    remove(".\\log_ct_multi.dat");

    item.rc = GNCDB_open(&(ct_Global.db), "..\\ct_multi.dat", 0, 0);
#else
	remove("ct_multi.dat");
    remove("log_ct_multi.dat");

    item.rc = GNCDB_open(&(ct_Global.db), "ct_multi.dat", 0, 0);
#endif


    CT_updatamessage(&item);
    return item.rc;
}

int multiThreadReset()
{

    int rc = 0;
    rc = GNCDB_close(&(ct_Global.db));
    if( rc!= 0)
    {
        return rc;
    }
	
#if defined _WIN32
    remove("..\\log\\DBlog_ct_multi.log");
    remove("..\\ct_multi.dat");
    remove("..\\log_ct_multi.dat");

    rc = GNCDB_open(&(ct_Global.db), "..\\ct_multi.dat", 0, 0);
#else
	remove("./log/DBlog_ct_multi.log");
    remove("ct_multi.dat");
    remove("log_ct_multi.dat");
    rc = GNCDB_open(&(ct_Global.db), "ct_multi.dat", 0, 0);
#endif
    

    return rc;
}

int createAllTable()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建所有表";
    item.message = "、、、";

    item.ptCount = 7;
    item.threadFuncs[0] = createARPTtable;
    item.threadFuncs[1] = createWPTtable;
    item.threadFuncs[2] = createNAVtable;
    item.threadFuncs[3] = createSIDtable;
    item.threadFuncs[4] = createAPCHtable;
    item.threadFuncs[5] = createDEFINEWPTtable;
    item.threadFuncs[6] = createFPLNtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "master");
    if(CT_tool.rowcount[0] != 7)
    {
        item.rc = -1;
        return -1;
    }

    return 0;
}

int insertAllTable()
{
    CT_Pthread item = {0};
    int rc = 0;
    item.project = NULL;
    item.operation = "插入所有表";
    item.message = "、、、";

    rc = insertARPTtable(&item);
    if (rc != 0)
    {
        return -1;
    }
    rc = insertWPTtable(&item);
    if (rc != 0)
    {
        return -1;
    }
    rc = insertNAVtable(&item);
    if (rc != 0)
    {
        return -1;
    }
    rc = insertSIDtable(&item);
    if (rc != 0)
    {
        return -1;
    }
    rc = insertAPCHtable(&item);
    if (rc != 0)
    {
        return -1;
    }
    rc = insertDEFINEWPTtable(&item);
    create_pthreadInit(&item);
    if (rc != 0)
    {
        return -1;
    }
    rc = insertFPLNtable(&item);
    if (rc != 0)
    {
        return -1;
    }

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[4] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[4], NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[5] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[5], NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[6] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[6], NULL, 1, 0, 0, "FPLN");
    if(CT_tool.rowcount[0] != CT_ARPTROWS || CT_tool.rowcount[1] != CT_WPTROWS || CT_tool.rowcount[2] != CT_NAVROWS
       || CT_tool.rowcount[3] != CT_SIDROWS || CT_tool.rowcount[4] != CT_APCHROWS || CT_tool.rowcount[5] != CT_DEFINEWPTROWS
       || CT_tool.rowcount[6] != CT_FPLNROWS)
    {
        item.rc = -1;
        return -1;
    }
    return 0;
}

/* *
 * 1: 创建表
 * 2: 插入表
 * 3: 查询表
 * 4: 更新表
 * 5: 删除表
 * 6: 连接操作
 * 7: 大文件
 * */

/* 111 112 113 114 115 122 123 124 125 133 134 135 144 145 155
    222 223 224 225 233 234 235 244 245 255
    333 334 335 344 345 355
    444 445 455
    555 */

int multiCreateTable111()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建ARPT表、WPT表、NAV表";
    item.message = "CREATE TABLE ARPT("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL);"
                   ",CREATE TABLE WPT("
                   "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL"
                   "WPT_lon REAL  NOT NULL "
                   "WPT_lat  REAL NOT NULL"
                   "WPT_blob  BLOB);,"
                   "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL)";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = createWPTtable;
    item.threadFuncs[2] = createARPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackSameTableTestCreate, NULL, NULL, 1, 0, 0, "master");
    if(CT_tool.rowcount[0] != 3)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable112()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建SID表、APCH表并插入ARPT表";
    item.message = "CREATE TABLE SID ("
                   "SID_arptident CHAR(8) NOT NULL"
                   "SID_sidident  CHAR(8) PRIMARY KEY NOT NULL"
                   "SID_no CHAR(2) NOT NULL"
                   "SID_type CHAR(2) NOT NULL"
                   "SID_track REAL NOT NULL"
                   "SID_fix CHAR(8) NOT NULL"
                   "SID_cat CHAR(2) NOT NULL"
                   "SID_lon REAL NOT NULL"
                   "SID_lat REAL NOT NULL"
                   "SID_rnp REAL NOT NULL"
                   "SID_alt REAL NOT NULL"
                   "),"
                   "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var)";

    item.ptCount = 3;
    item.threadFuncs[0] = createSIDtable;
    item.threadFuncs[1] = createAPCHtable;
    item.threadFuncs[2] = insertARPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=SID");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=APCH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[2] != 1 || CT_tool.rowcount[1] != CT_ARPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable113()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建DEFINEWPT表、FPLN表并查询ARPT表";
    item.message = "CREATE TABLE DEFINE_WPT("
                   "DEFINE_WPT_ident CHAR(8) PRIMARY KEY NOT NULL"
                   "DEFINE_WPT_lon REAL NOT NULL"
                   "DEFINE_WPT_lat REAL NOT NULL"
                   "),"
                   "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0";

    item.ptCount = 3;
    item.threadFuncs[0] = createDEFINEWPTtable;
    item.threadFuncs[1] = createFPLNtable;
    item.threadFuncs[2] = selectARPTtable_1C;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=DEFINE_WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=FPLN");

    if(CT_tool.rowcount[1] != 1 || CT_tool.rowcount[2] != 1 || item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[0] == 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable114()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable0表、testTable1表并更新ARPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "UPDATE ARPT SET　ARPT_length=25.00"
                   "WHERE ARPT_ident>=VLSTG";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = createTesttable;
    item.threadFuncs[2] = updateARPTtable_1C2;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=testTable0");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable1");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[2] != 1 || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable115()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable2表、testTable3表并删除ARPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = createTesttable;
    item.threadFuncs[2] = deleteARPTtable_1C;
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=testTable2");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable3");

    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[2] != 1 || item.selectCount[0] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable122()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable4表、插入WPT表、插入NAV表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL)"
                   ",INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb)";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = insertWPTtable;
    item.threadFuncs[2] = insertNAVtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=testTable4");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "NAV");

    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != CT_WPTROWS || CT_tool.rowcount[2] != CT_NAVROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable123()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable5表、插入SID表、查询WPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "INSERT INTO SID(SID_arptident SID_sidident SID_no SID_type SID_track SID_fix SID_cat SID_lon "
                   "SID_lat SID_rnp SID_alt)"
                   "VALUES(ct_sid.sc8_arpt_ident ct_sid.sc8_sid_ident ct_sid.sc8_seq_no ct_sid.sc8_flight_seg_type "
                   "ct_sid.f64_track ct_sid.sc8_fix_ident ct_sid.sc8_fix_cat ct_sid.f64_lon ct_sid.f64_lat "
                   "ct_sid.f64_rnp ct_sid.f64_alt),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = insertSIDtable;
    item.threadFuncs[2] = selectWPTtable_2C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=testTable5");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "SID");

    if(CT_tool.rowcount[1] != 1 || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != CT_SIDROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable124()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable6表、插入APCH表、更新WPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "UPDATE WPT SET WPT_lat=25.25"
                   "WHERE WPT_ident>=YQTGU";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = insertAPCHtable;
    item.threadFuncs[2] = updateWPTtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=testTable6");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != item.selectCount[0] || CT_tool.rowcount[2] != CT_APCHROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable125()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable6表、插入DEFINE_WPT表、删除WPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "INSERT INTO DEFINE_WPT(DEFINE_WPT_ident DEFINE_WPT_lon DEFINE_WPT_lat)"
                   "VALUES(ct_definewpt.sc8_wpt_ident ct_definewpt.f64_lon ct_definewpt.f64_lat),"
                   "DELETE FROM WPT WHERE WPT_lon<-22.5";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = insertDEFINEWPTtable;
    item.threadFuncs[2] = deleteWPTtable_1C1;

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "WPT", "WPT_lon<-22.5");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=testTable6");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "DEFINE_WPT");

    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != item.selectCount[0] || CT_tool.rowcount[2] != CT_DEFINEWPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable133()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable7表、查询NAV和SID表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM SID WHERE SID_sidident<=HYUBW";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = selectNAVtable_2C1;
    item.threadFuncs[2] = selectSIDtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable7");

    if(CT_tool.rowcount[2] != 1 || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable134()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable8表、查询NAV表、更新SID表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993,"
                   "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = selectNAVtable_2C2;
    item.threadFuncs[2] = updateSIDtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable8");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    if(CT_tool.rowcount[2] != 1 || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable135()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable9表、查询NAV表、删除SID表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "DELETE FROM SID WHERE SID_type=2";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = selectNAVtable_2C1;
    item.threadFuncs[2] = deleteSIDtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable9");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    if(CT_tool.rowcount[2] != 1 || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] + item.selectCount[1] != CT_SIDROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable144()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable10表、更新NAV和DEFINE_WPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "UPDATE NAV SET NAV_vor=0.000 WHERE NAV_lat=-45.000000,"
                   "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = updateNAVtable_1C1;
    item.threadFuncs[2] = updateDEFINE_WPTtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable10");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateNAVtable_1C, NULL, NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    if(CT_tool.rowcount[2] != 1 || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable145()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable11表、更新APCH表、删除NAV表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "DELETE FROM NAV WHERE NAV_lat>125.0,"
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = deleteNAVtable_1C2;
    item.threadFuncs[2] = updateAPCHtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable11");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "NAV", "NAV_lat>125.0");
    if(CT_tool.rowcount[2] != 1 || CT_tool.rowcount[0] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiCreateTable155()
{
    CT_Pthread item;
    int rows = 0;
    int testRow1 = 0;
    int testRow2 = 0;
    item.project = NULL;
    item.operation = "使用线程创建testTable10表、删除APCH表、删除DEFINE_WPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = deleteAPCHtable_2C;
    item.threadFuncs[2] = deleteDEFINE_WPTtable_2C;
    GNCDB_select(ct_Global.db, NULL, &rows, NULL, 1, 0, 0, "APCH");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable10");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    testRow1 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    testRow2 = CT_tool.rowcount[1] + item.selectCount[1];
    if(CT_tool.rowcount[2] != 1 || testRow1 != rows || testRow2 != CT_DEFINEWPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable222()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT、WPT、NAV表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL)"
                   ",INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb)";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable;
    item.threadFuncs[1] = insertWPTtable;
    item.threadFuncs[2] = insertNAVtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "NAV");

    if(CT_tool.rowcount[0] != CT_ARPTROWS || CT_tool.rowcount[1] != CT_WPTROWS || CT_tool.rowcount[2] != CT_NAVROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable223()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入SID、APCH表并查询ARPT表";
    item.message = "INSERT INTO SID(SID_arptident SID_sidident SID_no SID_type SID_track SID_fix SID_cat SID_lon "
                   "SID_lat SID_rnp SID_alt)"
                   "VALUES(ct_sid.sc8_arpt_ident ct_sid.sc8_sid_ident ct_sid.sc8_seq_no ct_sid.sc8_flight_seg_type "
                   "ct_sid.f64_track ct_sid.sc8_fix_ident ct_sid.sc8_fix_cat ct_sid.f64_lon ct_sid.f64_lat "
                   "ct_sid.f64_rnp ct_sid.f64_alt),"
                   "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0";

    item.ptCount = 3;
    item.threadFuncs[0] = insertSIDtable;
    item.threadFuncs[1] = insertAPCHtable;
    item.threadFuncs[2] = selectARPTtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "APCH");

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != CT_SIDROWS || CT_tool.rowcount[2] != CT_APCHROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable224()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入DEFINEWPT、ARPT表并更新WPT表";
    item.message = "INSERT INTO DEFINE_WPT(DEFINE_WPT_ident DEFINE_WPT_lon DEFINE_WPT_lat)"
                   "VALUES(ct_definewpt.sc8_wpt_ident ct_definewpt.f64_lon ct_definewpt.f64_lat),"
                   "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE WPT SET WPT_lon=-25.25"
                   "WHERE WPT_ident>=YQTGU";

    item.ptCount = 3;
    item.threadFuncs[0] = insertDEFINEWPTtable;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = updateWPTtable_1C2;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");

    if(CT_tool.rowcount[0] != CT_DEFINEWPTROWS || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != 1050)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable225()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT、FPLN表并删除WPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "DELETE FROM WPT WHERE WPT_lat<-22.5";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = insertFPLNtable;
    item.threadFuncs[2] = deleteWPTtable_1C2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "FPLN");

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");

    if(CT_tool.rowcount[0] != 1100 || CT_tool.rowcount[1] + item.selectCount[1] != CT_WPTROWS || CT_tool.rowcount[2] != CT_FPLNROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable233()
{
    CT_Pthread item;
    int row = 0;
    item.project = NULL;
    item.operation = "使用线程插入ARPT表并查询WPT和NAV表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat>-25.0"
                   ",SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectWPTtable_2C2;
    item.threadFuncs[2] = selectNAVtable_2C2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    GNCDB_select(ct_Global.db, NULL, &row, NULL, 1, 0, 0, "WPT");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable234()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT表并查询WPT和更新NAV表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat>-25.0,"
                   "UPDATE NAV SET NAV_vor=102.0101 WHERE NAV_ident=LDUTL";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectWPTtable_2C2;
    item.threadFuncs[2] = updateNAVtable_1C2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackupdateNAVtable_1C2, NULL, NULL, 1, 0, 1, "NAV", "NAV_ident=LDUTL");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable235()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT表并查询WPT和删除NAV表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat>-25.0,"
                   "DELETE FROM NAV WHERE NAV_ident<XFQTZ";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectWPTtable_2C2;
    item.threadFuncs[2] = deleteNAVtable_1C1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "NAV");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] + item.selectCount[1] != CT_NAVROWS || CT_tool.rowcount[0] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable244()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT表并更新SID和APCH表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = updateSIDtable_1C;
    item.threadFuncs[2] = updateAPCHtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
    || item.selectCount[0] == 0 || item.selectCount[1] == 0
    || CT_tool.rowcount[2] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable245()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入ARPT表、更新DEFINE_WPT、删除SID表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA, "
                   "DELETE FROM SID WHERE SID_type=2";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = updateDEFINE_WPTtable_1C;
    item.threadFuncs[2] = deleteSIDtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] + item.selectCount[1] != CT_SIDROWS
       || item.selectCount[0] == 0 || item.selectCount[1] == 0
       || CT_tool.rowcount[2] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiInsertTable255()
{
    CT_Pthread item;
    int rows = 0;
    int testRow1 = 0;
    int testRow2 = 0;
    item.project = NULL;
    item.operation = "使用线程插入ARPT表并删除APCH和DEFINE_WPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = deleteAPCHtable_2C;
    item.threadFuncs[2] = deleteDEFINE_WPTtable_2C;
    GNCDB_select(ct_Global.db, NULL, &rows, NULL, 1, 0, 0, "APCH");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    testRow1 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    testRow2 = CT_tool.rowcount[1] + item.selectCount[1];
    if(CT_tool.rowcount[2] != CT_releTest.allRows || testRow1 != rows || testRow2 != CT_DEFINEWPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiSelectTable333()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程查询ARPT、WPT和NAV表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lon>10.0"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat>-25.0,"
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_1C;
    item.threadFuncs[1] = selectWPTtable_2C2;
    item.threadFuncs[2] = selectNAVtable_2C2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiSelectTable334()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程更新ARPT表、查询WPT和NAV表";
    item.message = "UPDATE ARPT SET　ARPT_lon=25.00　WHERE ARPT_ident=VLSTG,"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C1;
    item.threadFuncs[1] = selectWPTtable_2C2;
    item.threadFuncs[2] = selectNAVtable_2C2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident=VLSTG");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiSelectTable335()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程删除ARPT、查询WPT和NAV表";
    item.message = "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                    "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteARPTtable_1C;
    item.threadFuncs[1] = selectWPTtable_2C2;
    item.threadFuncs[2] = selectNAVtable_2C2;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
 if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiSelectTable344()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程查询WPT表并更新SID和APCH表";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = updateSIDtable_1C;
    item.threadFuncs[2] = updateAPCHtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
       || item.selectCount[0] == 0 || item.selectCount[1] == 0
       || CT_tool.rowcount[2] != item.selectCount[2] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiSelectTable345()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程查询WPT表、更新DEFINE_WPT、删除SID表";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA, "
                   "DELETE FROM SID WHERE SID_type=2";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = updateDEFINE_WPTtable_1C;
    item.threadFuncs[2] = deleteSIDtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] + item.selectCount[1] != CT_SIDROWS
       || item.selectCount[0] == 0 || item.selectCount[1] == 0
       || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiSelectTable355()
{
    CT_Pthread item;
    int rows = 0;
    int testRow1 = 0;
    int testRow2 = 0;
    item.project = NULL;
    item.operation = "使用线程查询WPT表并删除APCH和DEFINE_WPT表";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = deleteAPCHtable_2C;
    item.threadFuncs[2] = deleteDEFINE_WPTtable_2C;
    GNCDB_select(ct_Global.db, NULL, &rows, NULL, 1, 0, 0, "APCH");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    testRow1 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    testRow2 = CT_tool.rowcount[1] + item.selectCount[1];
    if(testRow2 != CT_FPLNROWS  || testRow1 != rows || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiUptateTable444()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程更新WPT表、SID和APCH表";
    item.message = "UPDATE WPT SET WPT_lon=-25.25 WHERE WPT_ident>=YQTGU,"
                   "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_1C3;
    item.threadFuncs[1] = updateSIDtable_1C;
    item.threadFuncs[2] = updateAPCHtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
       || item.selectCount[0] == 0 || item.selectCount[1] == 0
       || CT_tool.rowcount[2] != item.selectCount[2] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiUptateTable445()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程更新ARPT、DEFINE_WPT表并删除SID表";
    item.message = "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG,"
                   "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA, "
                   "DELETE FROM SID WHERE SID_type=2";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C3;
    item.threadFuncs[1] = updateDEFINE_WPTtable_1C;
    item.threadFuncs[2] = deleteSIDtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] + item.selectCount[1] != CT_SIDROWS
       || item.selectCount[0] == 0 || item.selectCount[1] == 0
       || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiUptateTable455()
{
    CT_Pthread item;
    int rows = 0;
    int testRow1 = 0;
    int testRow2 = 0;
    item.project = NULL;
    item.operation = "使用线程更新FPLN表并删除APCH和DEFINE_WPT表";
    item.message = "UPDATE FPLN SET FPLN_stage1=1 FPLN_stage2=1 WHERE FPLN_lon>=0.00 FPLN_lon<=25.00, "
                   "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0";

    item.ptCount = 3;
    item.threadFuncs[0] = updateFPLNtable_2C1;
    item.threadFuncs[1] = deleteAPCHtable_2C;
    item.threadFuncs[2] = deleteDEFINE_WPTtable_2C;
    GNCDB_select(ct_Global.db, NULL, &rows, NULL, 1, 0, 0, "APCH");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateFPLNtable_2C1, NULL, NULL, 1, 0, 2, "FPLN", "FPLN_lon>=0.00", "FPLN_lon<=25.00");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    testRow1 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    testRow2 = CT_tool.rowcount[1] + item.selectCount[1];
    if(testRow2 != CT_DEFINEWPTROWS  || testRow1 != rows || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiDeleteTable555()
{
    CT_Pthread item;
    int testRow1 = 0;
    int testRow2 = 0;
    int testRow0 = 0;
    item.project = NULL;
    item.operation = "使用线程删除FPLN、WPT和ARPT表";
    item.message = "DELETE FROM FPLN WHERE FPLN_ident>SLMWREZVQG, "
                   "DELETE FROM WPT WHERE WPT_lat<-22.5,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteFPLNtable_1C;
    item.threadFuncs[1] = deleteWPTtable_1C2;
    item.threadFuncs[2] = deleteARPTtable_1C1;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    testRow2 = CT_tool.rowcount[2] + item.selectCount[2];
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "FPLN");
    testRow0 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    testRow1 = CT_tool.rowcount[1] + item.selectCount[1];
    if(testRow2 != CT_ARPTROWS  || testRow1 != CT_WPTROWS || testRow0 != CT_FPLNROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

/*
116 126 136 146 156 166
226 236 246 256 266
336 346 356 366
446 456 466
556 566
666
*/

int setJoinOrBlobTest()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建并插入ARPT和WPT表";
    item.message = "、、、";

    item.ptCount = 2;
    item.threadFuncs[0] = createARPTtable;
    item.threadFuncs[1] = createWPTtable;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "master");
    if(CT_tool.rowcount[0] != 2)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    // CT_updatamessage(&item);

    item.ptCount = 1;
    item.threadFuncs[0] = insertARPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    item.threadFuncs[0] = insertWPTtable;
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    if(CT_tool.rowcount[0] != CT_ARPTROWS || CT_tool.rowcount[1] != CT_WPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable116()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程创建SID表、APCH表并连接ARPT和WPT表";
    item.message = "CREATE TABLE SID ("
                   "SID_arptident CHAR(8) NOT NULL"
                   "SID_sidident  CHAR(8) PRIMARY KEY NOT NULL"
                   "SID_no CHAR(2) NOT NULL"
                   "SID_type CHAR(2) NOT NULL"
                   "SID_track REAL NOT NULL"
                   "SID_fix CHAR(8) NOT NULL"
                   "SID_cat CHAR(2) NOT NULL"
                   "SID_lon REAL NOT NULL"
                   "SID_lat REAL NOT NULL"
                   "SID_rnp REAL NOT NULL"
                   "SID_alt REAL NOT NULL"
                   "),"
                   "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = createSIDtable;
    item.threadFuncs[1] = createAPCHtable;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=APCH");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != 1 || CT_tool.rowcount[2] != jointool.select)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable126()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程创建NAV表、插入SID并连接ARPT和WPT表";
    item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL),"
                    "INSERT INTO SID(SID_arptident SID_sidident SID_no SID_type SID_track SID_fix SID_cat SID_lon "
                   "SID_lat SID_rnp SID_alt)"
                   "VALUES(ct_sid.sc8_arpt_ident ct_sid.sc8_sid_ident ct_sid.sc8_seq_no ct_sid.sc8_flight_seg_type "
                   "ct_sid.f64_track ct_sid.sc8_fix_ident ct_sid.sc8_fix_cat ct_sid.f64_lon ct_sid.f64_lat "
                   "ct_sid.f64_rnp ct_sid.f64_alt),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = insertSIDtable;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=NAV");

    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != CT_SIDROWS || CT_tool.rowcount[2] != jointool.select)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable136()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程创建DEFINEWPT表、查询NAV表并连接ARPT和WPT表";
    item.message = "CREATE TABLE DEFINE_WPT("
                   "DEFINE_WPT_ident CHAR(8) PRIMARY KEY NOT NULL"
                   "DEFINE_WPT_lon REAL NOT NULL"
                   "DEFINE_WPT_lat REAL NOT NULL"
                   "),"
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = createDEFINEWPTtable;
    item.threadFuncs[1] = selectNAVtable_2C2;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=DEFINE_WPT");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != jointool.select)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable146()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程创建FPLN表、更新SID表并连接ARPT和WPT表";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = updateSIDtable_1C;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=SID");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != 1
       || item.selectCount[0] == 0 || CT_tool.rowcount[2] != jointool.select )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable156()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程创建testTable0、删除SID表并连接ARPT和WPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "DELETE FROM SID WHERE SID_type=2,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = deleteSIDtable_1C;
    item.threadFuncs[2] = multiSelectJoinTable;

    createFlag = 0;
    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=testTable0");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] + item.selectCount[1] != CT_SIDROWS
    || item.selectCount[1] == 0 || CT_tool.rowcount[2] != jointool.select)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable166()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程创建testTable1并连接ARPT和WPT表";
    item.message = "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = createTesttable;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = selectTableWPTjoinARPT1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "master", "tableName=testTable0");

    if(CT_tool.rowcount[2] != 1 || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable226()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程插入SID、APCH表并连接ARPT和WPT表";
    item.message = "INSERT INTO SID(SID_arptident SID_sidident SID_no SID_type SID_track SID_fix SID_cat SID_lon "
                   "SID_lat SID_rnp SID_alt)"
                   "VALUES(ct_sid.sc8_arpt_ident ct_sid.sc8_sid_ident ct_sid.sc8_seq_no ct_sid.sc8_flight_seg_type "
                   "ct_sid.f64_track ct_sid.sc8_fix_ident ct_sid.sc8_fix_cat ct_sid.f64_lon ct_sid.f64_lat "
                   "ct_sid.f64_rnp ct_sid.f64_alt),"
                   "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertSIDtable;
    item.threadFuncs[1] = insertAPCHtable;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    if(CT_tool.rowcount[2] != jointool.select || CT_tool.rowcount[1] != CT_SIDROWS || CT_tool.rowcount[0] != CT_APCHROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable236()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程插入NAV表、查询SID表并连接ARPT和WPT表";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertNAVtable;
    item.threadFuncs[1] = selectSIDtable_1C1;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");

    if(CT_tool.rowcount[2] != jointool.select || CT_tool.rowcount[0] != CT_NAVROWS || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable246()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程插入DEFINEWPT表、更新SID表并连接ARPT和WPT表";
    item.message = "INSERT INTO DEFINE_WPT(DEFINE_WPT_ident DEFINE_WPT_lon DEFINE_WPT_lat)"
                   "VALUES(ct_definewpt.sc8_wpt_ident ct_definewpt.f64_lon ct_definewpt.f64_lat),"
                   "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertDEFINEWPTtable;
    item.threadFuncs[1] = updateSIDtable_1C;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != CT_DEFINEWPTROWS
       || item.selectCount[0] == 0 || CT_tool.rowcount[2] != jointool.select )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable256()
{
    CT_Pthread item;
    int rows = 0;
    int testRow1 = 0;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程插入FPLN表、删除APCH表并连接ARPT和WPT表";
    item.message = "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertFPLNtable;
    item.threadFuncs[1] = deleteAPCHtable_2C;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    GNCDB_select(ct_Global.db, NULL, &rows, NULL, 1, 0, 0, "APCH");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    testRow1 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "FPLN");
    if(CT_tool.rowcount[2] != jointool.select || testRow1 != rows || CT_tool.rowcount[1] != CT_FPLNROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable266()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程插入NAV表并连接ARPT和WPT表";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertNAVtable_SameTab;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = selectTableWPTjoinARPT1;

    createFlag = 0;
    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_NAVROWS;
    CT_releTest.reuse = true;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "NAV");

    if(CT_tool.rowcount[2] !=  CT_releTest.allRows || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable336()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程查询APCH和NAV表并连接ARPT和WPT表";
    item.message = "SELECT * FROM APCH WHERE APCH_apchident=XZDH,"
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectAPCHtable_1C1;
    item.threadFuncs[1] = selectNAVtable_2C2;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != jointool.select || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable346()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程查询NAV表、更新SID表并连接ARPT和WPT表";
    item.message = "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "SELECT * FROM NAV WHERE NAV_ident<=LKWHG NAV_tacan<132.483993,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = updateSIDtable_1C;
    item.threadFuncs[1] = selectNAVtable_2C2;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");

    if(CT_tool.rowcount[2] != jointool.select || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable356()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程查询NAV表、删除SID表并连接ARPT和WPT表";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "DELETE FROM SID WHERE SID_type=2,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = deleteSIDtable_1C;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");

    if(CT_tool.rowcount[2] != jointool.select || CT_tool.rowcount[0] != item.selectCount[0]
    || CT_tool.rowcount[1] + item.selectCount[1] != CT_SIDROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable366()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程查询NAV表并连接ARPT和WPT表";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectTableWPTjoinARPT2;
    item.threadFuncs[2] = selectTableWPTjoinARPT1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable446()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程更新FPLN和APCH表并连接ARPT和WPT表";
    item.message = "UPDATE FPLN SET FPLN_stage1=1 FPLN_stage2=1 WHERE FPLN_lon>=0.00 FPLN_lon<=25.00, "
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = updateFPLNtable_2C;
    item.threadFuncs[1] = updateAPCHtable_1C;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateFPLNtable_2C, NULL, NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
       || item.selectCount[0] == 0 || item.selectCount[1] == 0
       || CT_tool.rowcount[2] != jointool.select)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable456()
{
    CT_Pthread item;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程更新DEFINEWPT表、删除NAV表并连接ARPT和WPT表";
    item.message = "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA, "
                   "DELETE FROM NAV WHERE NAV_ident<XFQTZ,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = updateDEFINE_WPTtable_1C;
    item.threadFuncs[1] = deleteNAVtable_1C1;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "NAV", "NAV_ident<XFQTZ");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != 0
       || item.selectCount[0] == 0 || item.selectCount[1] == 0
       || CT_tool.rowcount[2] != jointool.select)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable466()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程更新SID表并连接ARPT和WPT表";
    item.message = "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = updateSIDtable_1C;
    item.threadFuncs[1] = selectTableWPTjoinARPT2;
    item.threadFuncs[2] = selectTableWPTjoinARPT1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable556()
{
    CT_Pthread item;
    int rows = 0;
    int testRow1 = 0;
    int testRow2 = 0;
    JOINTOOL jointool = {0};
    item.project = NULL;
    item.operation = "使用线程删除APCH和DEFINE_WPT表并连接ARPT和WPT表";
    item.message = "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteAPCHtable_2C;
    item.threadFuncs[1] = deleteDEFINE_WPTtable_2C;
    item.threadFuncs[2] = multiSelectJoinTable;

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackJoinTestMulti116;
    item.jointool = &jointool;

    GNCDB_select(ct_Global.db, NULL, &rows, NULL, 1, 0, 0, "APCH");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    testRow1 = CT_tool.rowcount[0] + item.selectCount[0];
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    testRow2 = CT_tool.rowcount[1] + item.selectCount[1];
    if(testRow2 != CT_FPLNROWS  || testRow1 != rows || CT_tool.rowcount[2] != jointool.select)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable566()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程删除NAV表并使用两个线程连接ARPT和WPT表";
    item.message = "DELETE FROM NAV WHERE NAV_lat>125.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteNAVtable_1C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT2;
    item.threadFuncs[2] = selectTableWPTjoinARPT1;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "NAV", "NAV_lat>125.0");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiJoinTable666()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程连接ARPT和WPT表";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableWPTjoinARPT2C1;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = selectTableWPTjoinARPT1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

/*
117 127 137 147 157 167 177
227 237 247 257 267 277
337 347 357 367 377
447 457 467 477
557 567 577
667 677
777
 177 277 377 477 577 677 777 最后编写
 */

/* 21*3=63 + 21*3=63 + ** 约 126 + join的双表6 = 132 */

/* BLOB文件路径 */

#if defined _WIN32
	char blobPath[] = "..\\testfile\\blob\\blobdata\\";
	char savePath[] = "..\\testfile\\blob\\blobresult\\concurrence\\";
#else
	char blobPath[] = "./testfile/blob/blobdata/";
	char savePath[] = "./testfile/blob/blobresult/concurrence/";

#endif

char blobfile1[] = "blob.txt";
char blobfile2[] = "blob.png";
char blobfile3[] = "blob.mp3";
char blobfile4[] = "blob.mp4";

int multiBlobTable117set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = "三线程不同表BLOB测试";
    item.operation = "创建NAV和SID表并在WPT表中插入BLOB";
    item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL),"
                   "CREATE TABLE SID ("
                   "SID_arptident CHAR(8) NOT NULL"
                   "SID_sidident  CHAR(8) PRIMARY KEY NOT NULL"
                   "SID_no CHAR(2) NOT NULL"
                   "SID_type CHAR(2) NOT NULL"
                   "SID_track REAL NOT NULL"
                   "SID_fix CHAR(8) NOT NULL"
                   "SID_cat CHAR(2) NOT NULL"
                   "SID_lon REAL NOT NULL"
                   "SID_lat REAL NOT NULL"
                   "SID_rnp REAL NOT NULL"
                   "SID_alt REAL NOT NULL"
                   "),"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=DZDGO";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = createSIDtable;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile1);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "DZDGO";

    create_pthreadInit(&item);

    fclose(fp);

    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=NAV");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZDGO");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != 1 || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable117get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob117.txt";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile1);

    item.project = NULL;
    item.operation = "创建APCH和DEFINE_WPT表并在WPT表中获取BLOB";
    item.message = "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "CREATE TABLE DEFINE_WPT("
                   "DEFINE_WPT_ident CHAR(8) PRIMARY KEY NOT NULL"
                   "DEFINE_WPT_lon REAL NOT NULL"
                   "DEFINE_WPT_lat REAL NOT NULL"
                   "),"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=DZDGO";

    item.ptCount = 3;
    item.threadFuncs[2] = createAPCHtable;
    item.threadFuncs[1] = createDEFINEWPTtable;
    item.threadFuncs[0] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZDGO");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    memset(blobTool.buf, 0, blobTool.size);
    blobTool.fieldValue = "DZDGO";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    rc = fwrite(blobTool.buf, 1, blobTool.size, fp);
    fclose(fp);

    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=APCH");
    //GNCDB_select(ct_Global.db, ct_CallBack, NULL, NULL, 1, 0, 0, "master");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=DEFINE_WPT");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != 1)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable117delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "创建FPLN和testTable表并在WPT表中删除BLOB";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "CREATE TABLE testTable("
                   "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                   "ARPT_lon REAL  NOT NULL "
                   "ARPT_lat REAL NOT NULL"
                   "ARPT_elev REAL NOT NULL"
                   "ARPT_length REAL NOT NULL"
                   "ARPT_mag_var REAL  NOT NULL"
                   "),"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=DZDGO";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = createTesttable;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "DZDGO";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=testTable0");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZDGO");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != 1)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable127set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "创建NAV表、插入ARPT表、在WPT表中插入BLOB";
    item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL),"
                   "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=NAV");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != CT_releTest.allRows || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable127get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob127.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "创建APCH表、插入NAV表、在WPT表中获取BLOB";
    item.message = "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = createAPCHtable;
    item.threadFuncs[1] = insertNAVtable;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=APCH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "NAV");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != CT_NAVROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable127delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "创建FPLN表、插入APCH表、在WPT表中删除BLOB";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=GIERW";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = insertAPCHtable;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != 1 || CT_tool.rowcount[1] != CT_APCHROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable137set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "创建NAV表、查询ARPT表、在WPT表中插入BLOB";
    item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable137get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob137.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "创建APCH表、查询ARPT表、在WPT表中获取BLOB";
    item.message = "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = createAPCHtable;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable137delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "创建FPLN表、查询ARPT表、在WPT表中删除BLOB";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=GIERW";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable147set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "创建NAV表、更新ARPT表、在WPT表中插入BLOB";
    item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL),"
                   "UPDATE ARPT SET　ARPT_length=25.00"
                   "WHERE ARPT_ident>=VLSTG,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=QWIDP";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = updateARPTtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "QWIDP";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=NAV");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QWIDP");
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable147get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob147.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "创建APCH表、更新NAV表、在WPT表中获取BLOB";
    item.message = "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "UPDATE NAV SET NAV_vor=0.000 WHERE NAV_lat=-45.000000,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=QWIDP";

    item.ptCount = 3;
    item.threadFuncs[0] = createAPCHtable;
    item.threadFuncs[1] = updateNAVtable_1C1;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    rc = insertNAVtable(&item);

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QWIDP");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "QWIDP";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=APCH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateNAVtable_1C, NULL, NULL, 1, 0, 0, "NAV");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable147delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "创建FPLN表、更新APCH表、在WPT表中删除BLOB";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=QWIDP";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = updateAPCHtable_1C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "QWIDP";

    insertAPCHtable(&item);

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QWIDP");
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable157set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "创建NAV表、删除ARPT表、在WPT表中插入BLOB";
    item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL),"
                   "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=TDOQE";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile3);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "TDOQE";

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "ARPT", "ARPT_lon<-45");
    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=TDOQE");
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable157get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob157.mp3";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile3);

    item.project = NULL;
    item.operation = "创建APCH表、删除NAV表、在WPT表中获取BLOB";
    item.message = "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "DELETE FROM NAV WHERE NAV_ident<XFQTZ,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=TDOQE";

    item.ptCount = 3;
    item.threadFuncs[0] = createAPCHtable;
    item.threadFuncs[1] = deleteNAVtable_1C1;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    rc = insertNAVtable(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "NAV", "NAV_ident<XFQTZ");
    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=TDOQE");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "TDOQE";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable157delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "创建FPLN表、删除APCH表、在WPT表中删除BLOB";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=TDOQE";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = deleteAPCHtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "TDOQE";

    insertAPCHtable(&item);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "APCH", "APCH_lon<-25.0", "APCH_lat<-25.0");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "master", "tableName=FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=TDOQE");
    if(CT_tool.rowcount[1] != 1 || item.selectCount[0] != CT_tool.rowcount[0] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable167set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "创建NAV表、连接ARPT和SID表、在WPT表中插入BLOB";
    item.message = "CREATE TABLE NAV("
                   "NAV_ident CHAR(10) PRIMARY KEY NOT NULL"
                   "NAV_type CHAR(2)  NOT NULL"
                   "NAV_lon REAL NOT NULL"
                   "NAV_lat REAL NOT NULL"
                   "NAV_vor REAL NOT NULL"
                   "NAV_dme REAL NOT NULL"
                   "NAV_tacan REAL NOT NULL"
                   "NAV_ndb REAL NOT NULL),"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = createNAVtable;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "GIERW";

    createSIDtable(&item);
    insertSIDtable(&item);

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable167get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob167.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "创建APCH表、连接ARPT和SID表、在WPT表中获取BLOB";
    item.message = "CREATE TABLE　APCH("
                   "APCH_arptident CHAR(8) NOT NULL"
                   "APCH_apchident CHAR(8) PRIMARY KEY NOT NULL"
                   "APCH_no CHAR(2) NOT NULL"
                   "APCH_type CHAR(2) NOT NULL"
                   "APCH_track REAL NOT NULL"
                   "APCH_fixident CHAR(8) NOT NULL"
                   "APCH_fixcat CHAR(2) NOT NULL"
                   "APCH_stage CHAR(2) NOT NULL"
                   "APCH_lon REAL NOT NULL"
                   "APCH_lat REAL NOT NULL"
                   "APCH_rnp REAL NOT NULL"
                   "APCH_alt REAL NOT NULL"
                   "),"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = createAPCHtable;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable167delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "创建FPLN表、连接ARPT和SID表、在WPT表中删除BLOB";
    item.message = "CREATE TABLE FPLN ("
                   "FPLN_eng CHAR(2) NOT NULL"
                   "FPLN_ident CHAR(16) PRIMARY KEY NOT NULL"
                   "FPLN_no CHAR(3) NOT NULL"
                   "FPLN_type CHAR(2) NOT NULL"
                   "FPLN_track REAL NOT NULL"
                   "FPLN_fixident CHAR(8) NOT NULL"
                   "FPLN_fixcat CHAR(2) NOT NULL"
                   "FPLN_stage1 CHAR(2) NOT NULL"
                   "FPLN_stage2 CHAR(2) NOT NULL"
                   "FPLN_lon REAL NOT NULL"
                   "FPLN_lat REAL NOT NULL"
                   "FPLN_rnp REAL NOT NULL"
                   "FPLN_alt REAL NOT NULL"
                   "),"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=GIERW";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = createFPLNtable;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "master", "tableName=FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != 1 || item.selectCount[1] != CT_tool.rowcount[1] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable227set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "插入NAV表、插入SID表、在WPT表中插入BLOB";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "INSERT INTO SID(SID_arptident SID_sidident SID_no SID_type SID_track SID_fix SID_cat SID_lon "
                   "SID_lat SID_rnp SID_alt)"
                   "VALUES(ct_sid.sc8_arpt_ident ct_sid.sc8_sid_ident ct_sid.sc8_seq_no ct_sid.sc8_flight_seg_type "
                   "ct_sid.f64_track ct_sid.sc8_fix_ident ct_sid.sc8_fix_cat ct_sid.f64_lon ct_sid.f64_lat "
                   "ct_sid.f64_rnp ct_sid.f64_alt),"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = insertNAVtable;
    item.threadFuncs[1] = insertSIDtable;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != CT_NAVROWS || CT_tool.rowcount[1] != CT_SIDROWS || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable227get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob227.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "插入APCH表、插入DEFINEWPT表、在WPT表中获取BLOB";
    item.message = "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "INSERT INTO DEFINE_WPT(DEFINE_WPT_ident DEFINE_WPT_lon DEFINE_WPT_lat)"
                   "VALUES(ct_definewpt.sc8_wpt_ident ct_definewpt.f64_lon ct_definewpt.f64_lat),"
                   "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = insertAPCHtable;
    item.threadFuncs[1] = insertDEFINEWPTtable;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "DEFINE_WPT");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != CT_APCHROWS || CT_tool.rowcount[1] != CT_DEFINEWPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable227delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "插入FPLN表、插入ARPT表、在WPT表中删除BLOB";
    item.message = "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat "
                   "arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=GIERW";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = insertFPLNtable;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "GIERW";

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != CT_FPLNROWS || CT_tool.rowcount[1] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable237set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "插入NAV表、查询ARPT表、在WPT表中插入BLOB";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = insertNAVtable;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile4);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != CT_NAVROWS || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable237get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob237.mp4";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile4);

    item.project = NULL;
    item.operation = "插入APCH表、查询ARPT表、在WPT表中获取BLOB";
    item.message = "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = insertAPCHtable;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != CT_APCHROWS || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable237delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "插入FPLN表、查询ARPT表、在WPT表中删除BLOB";
    item.message = "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=GIERW";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[1] = insertFPLNtable;
    item.threadFuncs[2] = selectARPTtable_2C;
    item.threadFuncs[0] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "GIERW";

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != CT_FPLNROWS || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable247set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "插入NAV表、更新ARPT表、在WPT表中插入BLOB";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "UPDATE ARPT SET　ARPT_length=25.00"
                   "WHERE ARPT_ident>=VLSTG,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=NLCXR";

    item.ptCount = 3;
    item.threadFuncs[0] = insertNAVtable;
    item.threadFuncs[1] = updateARPTtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "NLCXR";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=NLCXR");
    if(CT_tool.rowcount[0] != CT_NAVROWS || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable247get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob247.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "插入APCH表、更新NAV表、在WPT表中获取BLOB";
    item.message = "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "UPDATE NAV SET NAV_vor=0.000 WHERE NAV_lat=-45.000000,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=NLCXR";

    item.ptCount = 3;
    item.threadFuncs[0] = insertAPCHtable;
    item.threadFuncs[1] = updateNAVtable_1C1;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=NLCXR");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "NLCXR";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateNAVtable_1C, NULL, NULL, 1, 0, 0, "NAV");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != CT_APCHROWS || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable247delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "插入FPLN表、更新APCH表、在WPT表中删除BLOB";
    item.message = "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=NLCXR";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = insertFPLNtable;
    item.threadFuncs[1] = updateAPCHtable_1C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "NLCXR";

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=NLCXR");
    if(CT_tool.rowcount[0] != CT_FPLNROWS || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable257set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "插入NAV表、删除ARPT表、在WPT表中插入BLOB";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=TDOQE";

    item.ptCount = 3;
    item.threadFuncs[0] = insertNAVtable;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile3);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "TDOQE";

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "ARPT", "ARPT_lon<-45");
    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=TDOQE");
    if(CT_tool.rowcount[0] != CT_NAVROWS || item.selectCount[1] != CT_tool.rowcount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable257get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob257.mp3";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile3);

    item.project = NULL;
    item.operation = "插入APCH表、删除NAV表、在WPT表中获取BLOB";
    item.message = "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "DELETE FROM NAV WHERE NAV_ident<XFQTZ,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=TDOQE";

    item.ptCount = 3;
    item.threadFuncs[0] = insertAPCHtable;
    item.threadFuncs[1] = deleteNAVtable_1C1;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "NAV", "NAV_ident<XFQTZ");
    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=TDOQE");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "TDOQE";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != CT_APCHROWS || item.selectCount[1] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable257delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "插入FPLN表、删除APCH表、在WPT表中删除BLOB";
    item.message = "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=TDOQE";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = insertFPLNtable;
    item.threadFuncs[1] = deleteAPCHtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "TDOQE";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "APCH", "APCH_lon<-25.0", "APCH_lat<-25.0");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=TDOQE");
    if(CT_tool.rowcount[1] != CT_FPLNROWS || item.selectCount[0] != CT_tool.rowcount[0] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable267set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "插入NAV表、连接ARPT和SID表、在WPT表中插入BLOB";
    item.message = "INSERT INTO NAV(NAV_ident NAV_type NAV_lon NAV_lat NAV_vor NAV_dme NAV_tacan NAV_ndb)"
                   "VALUES(ct_nav.sc8_nav_ident ct_nav.sc8_nav_type ct_nav.f64_lon ct_nav.f64_lat "
                   "ct_nav.f64_vor ct_nav.f64_dme ct_nav.f64_tacan ct_nav.f64_ndb),"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[1] = insertNAVtable;
    item.threadFuncs[2] = selectTableARPTjoinSID;
    item.threadFuncs[0] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile4);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "GIERW";

    insertSIDtable(&item);

    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        item.rc = -111;
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != CT_NAVROWS || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable267get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob267.mp4";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile4);

    item.project = NULL;
    item.operation = "插入APCH表、连接ARPT和SID表、在WPT表中获取BLOB";
    item.message = "INSERT INTO APCH(APCH_arptident APCH_apchident APCH_no APCH_type APCH_track APCH_fixident "
                   "APCH_fixcat APCH_stage APCH_lon APCH_lat APCH_rnp APCH_alt)"
                   "VALUES(ct_apch.sc8_arpt_ident ct_apch.sc8_apch_ident ct_apch.sc8_seq_no ct_apch.sc8_flight_seg_type "
                   "ct_apch.f64_track ct_apch.sc8_fix_ident ct_apch.sc8_fix_cat ct_apch.sc8_flight_stage ct_apch.f64_lon "
                   "ct_apch.f64_lat ct_apch.f64_rnp ct_apch.f64_alt),"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=GIERW";

    item.ptCount = 3;
    item.threadFuncs[0] = insertAPCHtable;
    item.threadFuncs[2] = selectTableARPTjoinSID;
    item.threadFuncs[1] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    memset(blobTool.buf, 0, blobTool.size);
    blobTool.fieldValue = "GIERW";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != CT_APCHROWS || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable267delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "插入FPLN表、连接ARPT和SID表、在WPT表中删除BLOB";
    item.message = "INSERT INTO FPLN(FPLN_eng FPLN_ident FPLN_no FPLN_type FPLN_track FPLN_fixident FPLN_fixcat "
                   "FPLN_stage1 FPLN_stage2 FPLN_lon FPLN_lat FPLN_rnp FPLN_alt)"
                   "VALUES(ct_fpln.sc8_chs_eng ct_fpln.sc8_fpln_ident ct_fpln.sc8_seq_no ct_fpln.sc8_flight_seq_type "
                   "ct_fpln.f64_track ct_fpln.sc8_fix_ident ct_fpln.sc8_fix_cat ct_fpln.sc8_fight_stage1 ct_fpln.sc8_fight_stage2 "
                   "ct_fpln.f64_lon ct_fpln.f64_lat ct_fpln.f64_rnp ct_fpln.f64_alt),"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=GIERW";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[1] = insertFPLNtable;
    item.threadFuncs[2] = selectTableARPTjoinSID;
    item.threadFuncs[0] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "GIERW";

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
    if(CT_tool.rowcount[0] != CT_FPLNROWS || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable337set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "查询NAV表、查询ARPT表、在WPT表中插入BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=LKWTB";

    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile3);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "LKWTB";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=LKWTB");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable337get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob337.mp3";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile3);

    item.project = NULL;
    item.operation = "查询NAV表、查询ARPT表、在WPT表中获取BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=LKWTB";

    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=LKWTB");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "LKWTB";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable337delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "查询NAV表、查询ARPT表、在WPT表中删除BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=LKWTB";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "LKWTB";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=LKWTB");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable347set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "查询NAV表、更新ARPT表、在WPT表中插入BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "UPDATE ARPT SET　ARPT_length=25.00"
                   "WHERE ARPT_ident>=VLSTG,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AHNDQ";

    item.ptCount = 2;
    item.threadFuncs[1] = selectNAVtable_2C1;
    item.threadFuncs[2] = updateARPTtable_1C2;
    item.threadFuncs[0] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AHNDQ";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AHNDQ");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable347get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob347.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "查询NAV表、更新APCH表、在WPT表中获取BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AHNDQ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = updateAPCHtable_1C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AHNDQ");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AHNDQ";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable347delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "更新FPLN表、查询ARPT表、在WPT表中删除BLOB";
    item.message = "UPDATE FPLN SET FPLN_stage1=1 FPLN_stage2=1 WHERE FPLN_lon>=0.00 FPLN_lon<=25.00, "
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AHNDQ";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = updateFPLNtable_2C;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AHNDQ";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateFPLNtable_2C, NULL, NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AHNDQ");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable357set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "删除NAV表、查询ARPT表、在WPT表中插入BLOB";
    item.message = "DELETE FROM NAV WHERE NAV_lat>125.0,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=LKWTB";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteNAVtable_1C2;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile3);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "LKWTB";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "NAV", "NAV_lat>125.0");

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=LKWTB");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable357get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob357.mp3";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile3);

    item.project = NULL;
    item.operation = "删除APCH表、查询ARPT表、在WPT表中获取BLOB";
    item.message = "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=LKWTB";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteAPCHtable_2C;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=LKWTB");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "LKWTB";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "APCH", "APCH_lon<-25.0", "APCH_lat<-25.0");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable357delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "删除FPLN表、查询ARPT表、在WPT表中删除BLOB";
    item.message = "DELETE FROM FPLN WHERE FPLN_ident>SLMWREZVQG, "
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=LKWTB";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = deleteFPLNtable_1C;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "LKWTB";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "FPLN", "FPLN_ident>SLMWREZVQG");

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=LKWTB");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable367set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "查询NAV表、连接ARPT和SID表、在WPT表中插入BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=SRORR";

    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile3);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "SRORR";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=SRORR");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable367get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob367.mp3";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile3);

    item.project = NULL;
    item.operation = "查询NAV表、连接ARPT和SID表、在WPT表中获取BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=SRORR";

    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=SRORR");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "SRORR";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable367delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "查询NAV表、连接ARPT和SID表、在WPT表中删除BLOB";
    item.message = "SELECT * FROM NAV WHERE NAV_lon>-22.5 NAV_lon<22.5,"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=SRORR";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = selectNAVtable_2C1;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "SRORR";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=SRORR");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable447set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "更新SID、更新ARPT表、在WPT表中插入BLOB";
    item.message = "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "UPDATE ARPT SET　ARPT_length=25.00"
                   "WHERE ARPT_ident>=VLSTG,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=BHYLY";

    item.ptCount = 3;
    item.threadFuncs[0] = updateSIDtable_1C;
    item.threadFuncs[1] = updateARPTtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "BHYLY";

    //setWPTTableBlob(&item);
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    fclose(fp);
    my_free(blobTool.buf);
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable447get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob447.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "更新DEFINE_WPT表、更新APCH表、在WPT表中获取BLOB";
    item.message = "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA, "
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=BHYLY";

    item.ptCount = 3;
    item.threadFuncs[0] = updateDEFINE_WPTtable_1C;
    item.threadFuncs[1] = updateAPCHtable_1C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "BHYLY";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable447delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "更新FPLN表、更新NAV表、在WPT表中删除BLOB";
    item.message = "UPDATE FPLN SET FPLN_stage1=1 FPLN_stage2=1 WHERE FPLN_lon>=0.00 FPLN_lon<=25.00, "
                   "UPDATE NAV SET NAV_vor=0.000 WHERE NAV_lat=-45.000000,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=BHYLY";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = updateFPLNtable_2C;
    item.threadFuncs[1] = updateNAVtable_1C1;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "BHYLY";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateFPLNtable_2C, NULL, NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateNAVtable_1C, NULL, NULL, 1, 0, 0, "NAV");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable457set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "更新SID、删除ARPT表、在WPT表中插入BLOB";
    item.message = "UPDATE SID SET SID_track=52.000 WHERE SID_arptident<=BDHKC, "
                   "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=BHYLY";

    item.ptCount = 3;
    item.threadFuncs[0] = updateSIDtable_1C;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "BHYLY";
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "ARPT", "ARPT_lon<-45");

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateSIDtable_1C, NULL, NULL, 1, 0, 0, "SID");
    //GNCDB_select(ct_Global.db, ct_CallBack, NULL, NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable457get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob457.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "更新APCH表、删除NAV表、在WPT表中获取BLOB";
    item.message = "DELETE FROM NAV WHERE NAV_lat>125.0,"
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=BHYLY";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteNAVtable_1C2;
    item.threadFuncs[1] = updateAPCHtable_1C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "BHYLY";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "NAV", "NAV_lat>125.0");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C, NULL, NULL, 1, 0, 0, "APCH");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable457delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "更新FPLN表、更新NAV表、在WPT表中删除BLOB";
    item.message = "UPDATE FPLN SET FPLN_stage1=1 FPLN_stage2=1 WHERE FPLN_lon>=0.00 FPLN_lon<=25.00, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=BHYLY";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = updateFPLNtable_2C;
    item.threadFuncs[1] = deleteDEFINE_WPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "BHYLY";

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 2, "DEFINE_WPT", "DEFINE_WPT_lon<-25.0", "DEFINE_WPT_lat<-25.0");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateFPLNtable_2C, NULL, NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable467set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "连接ARPT和SID表、更新APCH表、在WPT表中插入BLOB";
    item.message = "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE APCH SET APCH_fixcat=4 WHERE APCH_type=1,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=BHYLY";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableARPTjoinSID;
    item.threadFuncs[1] = updateAPCHtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "BHYLY";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateAPCHtable_1C2, NULL, NULL, 1, 0, 0, "APCH");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable467get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob467.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "更新DEFINE_WPT表、连接ARPT和SID表、在WPT表中获取BLOB";
    item.message = "UPDATE DEFINE_WPT SET DEFINE_WPT_lon=10.000 WHERE DEFINE_WPT_ident=KFUKRA, "
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=BHYLY";

    item.ptCount = 3;
    item.threadFuncs[0] = updateDEFINE_WPTtable_1C;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "BHYLY";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateDEFINE_WPTtable_1C, NULL, NULL, 1, 0, 0, "DEFINE_WPT");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable467delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "更新FPLN表、连接ARPT和SID表、在WPT表中删除BLOB";
    item.message = "UPDATE FPLN SET FPLN_stage1=1 FPLN_stage2=1 WHERE FPLN_lon>=0.00 FPLN_lon<=25.00, "
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=BHYLY";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = updateFPLNtable_2C;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "BHYLY";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateFPLNtable_2C, NULL, NULL, 1, 0, 0, "FPLN");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable557set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "删除APCH表、删除ARPT表、在WPT表中插入BLOB";
    item.message = "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=DZECO";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteAPCHtable_2C;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "DZECO";
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "ARPT", "ARPT_lon<-45");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "APCH", "APCH_lon<-25.0", "APCH_lat<-25.0");

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable557get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob557.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "删除SID表、删除NAV表、在WPT表中获取BLOB";
    item.message = "DELETE FROM NAV WHERE NAV_lat>125.0,"
                   "DELETE FROM SID WHERE SID_type=2,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=DZECO";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteNAVtable_1C2;
    item.threadFuncs[1] = deleteSIDtable_1C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "DZECO";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "NAV", "NAV_lat>125.0");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "SID", "SID_type=2");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable557delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "删除FPLN表、删除DEFINEWPT表、在WPT表中删除BLOB";
    item.message = "DELETE FROM FPLN WHERE FPLN_ident>SLMWREZVQG, "
                   "DELETE FROM DEFINE_WPT WHERE DEFINE_WPT_lon<-25.0 DEFINE_WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=DZECO";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = deleteFPLNtable_1C;
    item.threadFuncs[1] = deleteDEFINE_WPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "DZECO";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "FPLN", "FPLN_ident>SLMWREZVQG");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 2, "DEFINE_WPT", "DEFINE_WPT_lon<-25.0", "DEFINE_WPT_lat<-25.0");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable567set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "删除APCH表、连接ARPT和SID表、在WPT表中插入BLOB";
    item.message = "DELETE FROM APCH WHERE APCH_lon<-25.0 APCH_lat<-25.0, "
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=DZECO";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteAPCHtable_2C;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile4);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "DZECO";
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "APCH", "APCH_lon<-25.0", "APCH_lat<-25.0");

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable567get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob567.mp4";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile4);

    item.project = NULL;
    item.operation = "连接ARPT和SID表、删除NAV表、在WPT表中获取BLOB";
    item.message = "DELETE FROM NAV WHERE NAV_lat>125.0,"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=DZECO";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteNAVtable_1C2;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "DZECO";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "NAV", "NAV_lat>125.0");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable567delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "删除FPLN表、连接ARPT和SID表、在WPT表中删除BLOB";
    item.message = "DELETE FROM FPLN WHERE FPLN_ident>SLMWREZVQG, "
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=DZECO";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = deleteFPLNtable_1C;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "DZECO";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "FPLN", "FPLN_ident>SLMWREZVQG");
    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable667set()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "连接FPLN和NAV表、连接ARPT和SID表、在WPT表中插入BLOB";
    item.message = "SELECT * FROM FPLN JOIN NAV ON FPLN_lon=NAV_lon,"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=DZECO";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableFPLNjoinNAV;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile3);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "DZECO";

    create_pthreadInit(&item);

    fclose(fp);
    my_free(blobTool.buf);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    /* 判断表是否存在以及blob文件的大小 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable667get()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blob667.mp3";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile3);

    item.project = NULL;
    item.operation = "连接FPLN和NAV表、连接ARPT和SID表、在WPT表中获取BLOB";
    item.message = "SELECT * FROM FPLN JOIN NAV ON FPLN_lon=NAV_lon,"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=DZECO";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableFPLNjoinNAV;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "DZECO";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    /* 判断表是否存在以及blob文件是否一致 */
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int multiBlobTable667delete()
{
    CT_Pthread item = {0};
    BLOBTOOL blobTool = {0};
    item.project = NULL;
    item.operation = "连接FPLN和NAV表、连接ARPT和SID表、在WPT表中删除BLOB";
    item.message = "SELECT * FROM FPLN JOIN NAV ON FPLN_lon=NAV_lon,"
                   "SELECT * FROM ARPT JOIN SID ON ARPT_lon=SID_lon,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=DZECO";

    createFlag = 0;
    item.ptCount = 3;
    item.threadFuncs[0] = selectTableFPLNjoinNAV;
    item.threadFuncs[1] = selectTableARPTjoinSID;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "DZECO";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    /* 判断表是否存在以及blob文件是否一致 */
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}
/* 177 277 377 477 577 677 777 最后编写 */


/* 同表多线程测试 */
/*
222 223 224 225 233 234 235 244 245 255
333 334 335 344 345 355
444 445 455
555
*/

int sameTablemultiTest222()
{
    CT_Pthread item;
    item.project = "三线程同表测试";
    item.operation = "使用线程同时对ARPT表进行插入";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var)";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = insertARPTtable_SameTab;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;

    item.selectCount[9] = 0;
    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_ARPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest223()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程同时对ARPT表进行插入并进行查询";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = selectARPTtable_1C;

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_ARPTROWS)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest224()
{
    CT_Pthread item;
    int updateRow = 0;
    item.project = NULL;
    item.operation = "使用两个线程同时对ARPT表进行插入并进行更新";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = updateARPTtable_1C3;

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    updateRow = CT_tool.rowcount[2];

    create_pthreadInit(&item);
    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
        CT_tool.rowcount[2] = 0;
        GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
        if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[9] + CT_ARPTROWS)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
        CT_tool.rowcount[2] = 0;
        GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
        if(CT_tool.rowcount[2] != updateRow || CT_tool.rowcount[1] != item.selectCount[9] + CT_ARPTROWS)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest225()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程同时对ARPT表进行插入并进行删除";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = deleteARPTtable_1C;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
        if(CT_tool.rowcount[0] + item.selectCount[9] - item.selectCount[0] != CT_tool.rowcount[1] )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
        if(item.selectCount[0] != 0 || CT_tool.rowcount[0] + item.selectCount[9] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest233()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入并使用两个线程进行查询";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = selectARPTtable_1C;
    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    item.selectCount[9] = 0;

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != CT_releTest.allRows)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest234()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入、查询和更新";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0,"
                   "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectARPTtable_1C;
    item.threadFuncs[2] = updateARPTtable_1C3;

    CT_tool.rowcount[9] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[9], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if(1000 + item.selectCount[9] != CT_tool.rowcount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != item.selectCount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest235()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入、查询和删除";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = deleteARPTtable_1C;

    item.selectCount[9] = 0;
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);
    if (item.rcArray[2] == GNCDB_SUCCESS)
    {
        CT_tool.rowcount[2] = 0;
        GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
        if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != CT_tool.rowcount[0] + item.selectCount[9] - item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        CT_tool.rowcount[2] = 0;
        GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
        if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[2] != CT_tool.rowcount[0] + item.selectCount[9] || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest244()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入并使用两个线程进行更新";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE ARPT SET　ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                   "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = updateARPTtable_1C3;
    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    item.selectCount[9] = 0;
    create_pthreadInit(&item);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if (item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] == 0 || CT_tool.rowcount[2] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != 0 || CT_tool.rowcount[0] == 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] == 0 || item.selectCount[0] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(CT_tool.rowcount[1] != CT_ARPTROWS + item.selectCount[9])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest245()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程同时对ARPT表进行插入、更新和删除";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE ARPT SET　ARPT_lon=10.00 WHERE ARPT_ident<FGHN  ARPT_length>1,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = updateARPTtable_2C;
    item.threadFuncs[2] = deleteARPTtable_1C;

    item.selectCount[9] = 0;
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_2C, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_ident<FGHN", "ARPT_length>1");
    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[9] - item.selectCount[0] != CT_tool.rowcount[2] || item.selectCount[1] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[9] != CT_tool.rowcount[2] || item.selectCount[0] != 0 || item.selectCount[1] == 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[9] - item.selectCount[0] != CT_tool.rowcount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[0] + item.selectCount[9] != CT_tool.rowcount[2] || item.selectCount[0] != 0 || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest255()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程同时对ARPT表进行插入并使用两个线程删除";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                   "DELETE FROM ARPT WHERE ARPT_lon<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = deleteARPTtable_1C;

    item.selectCount[9] = 0;
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[9] - item.selectCount[0] - item.selectCount[1] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[9]  - item.selectCount[1] != CT_tool.rowcount[2] || item.selectCount[0] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[9]  - item.selectCount[0] != CT_tool.rowcount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[0] + item.selectCount[9] != CT_tool.rowcount[2] || item.selectCount[0] != 0 || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest333()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询";
    item.message = "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_1C2;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = selectARPTtable_1C;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
    || CT_tool.rowcount[2] != item.selectCount[2] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest334()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询并更新";
    item.message = "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT * FROM ARPT WHERE ARPT_lon>10.0";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C3;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = selectARPTtable_1C;

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
       || CT_tool.rowcount[2] != item.selectCount[2] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest335()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询并删除";
    item.message = "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_1C2;
    item.threadFuncs[1] = selectARPTtable_2C;
    item.threadFuncs[2] = deleteARPTtable_1C;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
    if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
           || CT_tool.rowcount[2] != item.selectCount[2] )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[1] != item.selectCount[1]
           || CT_tool.rowcount[2] != item.selectCount[2] )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != item.selectCount[0] || item.selectCount[1] != 0
           || CT_tool.rowcount[2] != item.selectCount[2] )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != 0
           || CT_tool.rowcount[2] != item.selectCount[2] )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1]
           || item.selectCount[2] != 0 )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[1] != 0
           || item.selectCount[2] != 0 )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[1] != item.selectCount[1]
           || item.selectCount[2] != 0 )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != 0
           || item.selectCount[2] != 0 )
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest344()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询并使用两个线程进行更新";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE ARPT SET　ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                   "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = updateARPTtable_1C3;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if (item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != 0 || CT_tool.rowcount[0] != item.selectCount[0])
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != item.selectCount[2] || item.selectCount[0] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest345()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程同时对ARPT表进行查询、更新和删除";
    item.message = "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "UPDATE ARPT SET　ARPT_lon=10.00 WHERE ARPT_ident<FGHN  ARPT_length>1,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_1C2;
    item.threadFuncs[1] = updateARPTtable_2C;
    item.threadFuncs[2] = deleteARPTtable_1C;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_2C, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_ident<FGHN", "ARPT_length>1");
    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] == 0 || CT_tool.rowcount[1] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest355()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程同时对ARPT表进行查询并使用两个线程删除";
    item.message = "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = deleteARPTtable_1C;

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "ARPT", "ARPT_lon<-45");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");
    create_pthreadInit(&item);
    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest444()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00"
                    "WHERE ARPT_ident>=VLSTG,"
                   "UPDATE ARPT SET　ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                   "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = updateARPTtable_1C3;

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");

    if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] != item.selectCount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest445()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程对ARPT表进行更新并删除";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00 WHERE ARPT_ident>=VLSTG,"
                    "UPDATE ARPT SET　ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                    "DELETE FROM ARPT WHERE ARPT_lat<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = deleteARPTtable_1C1;

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] == 0 || CT_tool.rowcount[2] + item.selectCount[2] != CT_ARPTROWS || item.selectCount[1] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] + item.selectCount[2] != CT_WPTROWS || item.selectCount[1] == 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] == 0 || CT_tool.rowcount[2] + item.selectCount[2] != CT_WPTROWS || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] + item.selectCount[2] != CT_WPTROWS || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest455()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新并使用两个线程删除";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00 WHERE ARPT_ident>=VLSTG,"
                    "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                    "DELETE FROM ARPT WHERE ARPT_ident<BEADE";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C3;
    item.threadFuncs[2] = deleteARPTtable_1C1;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_ident<BEADE");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
    if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] != item.selectCount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        CT_tool.rowcount[1] = 0;
        GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {

            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiTest555()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行删除";
    item.message =  "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                    "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                    "DELETE FROM ARPT WHERE ARPT_ident<BEADE";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C3;
    item.threadFuncs[2] = deleteARPTtable_1C1;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_ident<BEADE");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "ARPT", "ARPT_lon<-45");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
    if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || CT_tool.rowcount[2] != item.selectCount[2] || item.selectCount[1] != 0)
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] != GNCDB_SUCCESS && item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || CT_tool.rowcount[1] != item.selectCount[1])
        {
            CT_updatamessage(&item);
            return -1;
        }
    }
    else if(item.rcArray[0] == GNCDB_SUCCESS && item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[2] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0] || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[2] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

/*
226 236 246 256 266
336 346 356 366
446 456 466
556 566
666
*/

int sameTablemultiJoin226T2()
{
    CT_Pthread item;
    item.project = "join WPT表在前";
    item.operation = "使用线程对ARPT表进行插入并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    item.selectCount[9] = 0;
    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0] || CT_tool.rowcount[1] != CT_ARPTROWS + item.selectCount[9])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin236T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入、查询并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectARPTtable_1C2;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2] || item.selectCount[0] != CT_tool.rowcount[0] || CT_tool.rowcount[1] != CT_tool.rowcount[3] + item.selectCount[9])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin246T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入、更新并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE ARPT SET ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != CT_tool.rowcount[3] + item.selectCount[9])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin256T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程同时对ARPT表进行插入、删除并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "DELETE FROM ARPT WHERE ARPT_lon<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");

    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] - item.selectCount[1] != CT_tool.rowcount[2] || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] - item.selectCount[1] != CT_tool.rowcount[2] || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] != CT_tool.rowcount[2] || item.selectCount[1] != 0 || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] != CT_tool.rowcount[2] || item.selectCount[1] != 0 || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin266T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectTableWPTjoinARPT2C1;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if (item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != CT_tool.rowcount[0]|| item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != 0|| item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != CT_tool.rowcount[0]|| item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != 0|| item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin336T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = selectARPTtable_1C2;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0]|| CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin346T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询 更新并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = updateARPTtable_1C3;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != item.selectCount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin356T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询 删除并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = deleteARPTtable_1C1;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");
    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    if (item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] - item.selectCount[2] != CT_tool.rowcount[2] || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] - item.selectCount[2] != CT_tool.rowcount[2] || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] != CT_tool.rowcount[2] || item.selectCount[2] != 0 || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[3] != CT_tool.rowcount[2] || item.selectCount[2] != 0 || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin366T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = selectTableWPTjoinARPT2C1;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0]|| CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin446T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新并连接WPT和ARPT表";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00"
                    "WHERE ARPT_ident>=VLSTG,"
                    "UPDATE ARPT SET　ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1;

   create_pthreadInit(&item);
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");

    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != item.selectCount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin456T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新、删除并连接WPT和ARPT表";
    item.message =  "UPDATE ARPT SET ARPT_length=25.00"
                    "WHERE ARPT_ident>=VLSTG,"
                    "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[1] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin466T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新并连接WPT和ARPT表";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00"
                    "WHERE ARPT_ident>=VLSTG,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1;

    create_pthreadInit(&item);
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != item.selectCount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    item.rc = 0;

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin556T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行删除并连接WPT和ARPT表";
    item.message =  "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                    "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1;

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");

    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[0] + item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[0] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[0] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin566T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行删除并连接WPT和ARPT表";
    item.message =  "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteARPTtable_1C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1;

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");

    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(item.selectCount[1] + CT_tool.rowcount[1] != CT_ARPTROWS)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(CT_tool.rowcount[1] != CT_ARPTROWS)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin666T2()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用三个线程连接WPT和ARPT表";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableWPTjoinARPT1;
    item.threadFuncs[1] = selectTableWPTjoinARPT2C1;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0]|| CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

/*
226 236 246 256 266
336 346 356 366
446 456 466
556 566
666
*/

int sameTablemultiJoin226T1()
{
    CT_Pthread item;
    item.project = "join WPT表在前";
    item.operation = "使用线程对ARPT表进行插入并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = insertARPTtable_SameTab;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_ARPTROWS;
    CT_releTest.reuse = true;
    item.selectCount[9] = 0;
    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0] || CT_tool.rowcount[1] != CT_ARPTROWS + item.selectCount[9])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin236T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入、查询并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectARPTtable_1C2;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2] || item.selectCount[0] != CT_tool.rowcount[0] || CT_tool.rowcount[1] != CT_tool.rowcount[3] + item.selectCount[9])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin246T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入、更新并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "UPDATE ARPT SET　ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1EX;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != CT_tool.rowcount[3] + item.selectCount[9])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin256T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用两个线程同时对ARPT表进行插入、删除并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "DELETE FROM ARPT WHERE ARPT_lon<-45";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = deleteARPTtable_1C2;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");

    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] - item.selectCount[1] != CT_tool.rowcount[2] || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] - item.selectCount[1] != CT_tool.rowcount[2] || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] != CT_tool.rowcount[2] || item.selectCount[1] != 0 || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[3] + item.selectCount[9] != CT_tool.rowcount[2] || item.selectCount[1] != 0 || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin266T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行插入并连接WPT和ARPT表";
    item.message = "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var)"
                   "VALUES(arpt.sc8_arpt_ident arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = insertARPTtable_SameTab;
    item.threadFuncs[1] = selectTableWPTjoinARPT2C1EX;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");
    if (item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != CT_tool.rowcount[0]|| item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != 0|| item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != CT_tool.rowcount[0]|| item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[3] + item.selectCount[9]  != CT_tool.rowcount[1] || item.selectCount[0] != 0|| item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin336T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT * FROM ARPT WHERE ARPT_ident>RSMAM,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = selectARPTtable_1C2;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0]|| CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin346T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询 更新并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "UPDATE ARPT SET　ARPT_elev=25.00 WHERE ARPT_ident>=VLSTG,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = updateARPTtable_1C3;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C3, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0]|| CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin356T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询 删除并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = deleteARPTtable_1C1;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "ARPT");
    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[2], NULL, 1, 0, 0, "ARPT");
    if (item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] - item.selectCount[2] != CT_tool.rowcount[2] || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] - item.selectCount[2] != CT_tool.rowcount[2] || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[2] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[3] != CT_tool.rowcount[2] || item.selectCount[2] != 0 || item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[3] != CT_tool.rowcount[2] || item.selectCount[2] != 0 || item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin366T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行查询并连接WPT和ARPT表";
    item.message = "SELECT * FROM ARPT WHERE ARPT_lat>10.0 ARPT_lat<50.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectARPTtable_2C;
    item.threadFuncs[1] = selectTableWPTjoinARPT2C1EX;
    item.threadFuncs[2] = selectTableWPTjoinARPT0EX;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0]|| CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin446T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新并连接WPT和ARPT表";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00"
                    "WHERE ARPT_ident>=VLSTG,"
                    "UPDATE ARPT SET　ARPT_lon=125.00 WHERE ARPT_lat=22.5,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = updateARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1EX;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C4, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lat=22.5");

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin456T1()
{
    CT_Pthread item;
    int rows = 0;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新、删除并连接WPT和ARPT表";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00"
                    "WHERE ARPT_ident>=VLSTG,"
                    "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1EX;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "ARPT", "ARPT_lat<-45");

    create_pthreadInit(&item);
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");
    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[1] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0] || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(item.selectCount[0] != 0 || item.selectCount[1] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin466T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行更新并连接WPT和ARPT表";
    item.message =  "UPDATE ARPT SET　ARPT_length=25.00"
                    "WHERE ARPT_ident>=VLSTG,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = updateARPTtable_1C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0EX;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1EX;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateARPTtable_1C2, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");

    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin556T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行删除并连接WPT和ARPT表";
    item.message =  "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                    "DELETE FROM ARPT WHERE ARPT_lat<-45,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteARPTtable_1C2;
    item.threadFuncs[1] = deleteARPTtable_1C4;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1EX;

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");

    create_pthreadInit(&item);
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "ARPT");

    if (item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[0] + item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] == GNCDB_SUCCESS && item.rcArray[0] != GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[1] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else if(item.rcArray[1] != GNCDB_SUCCESS && item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] + item.selectCount[0] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }
    else
    {
        if(CT_tool.rowcount[0] != CT_tool.rowcount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
        item.rc = 0;
    }

    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin566T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对ARPT表进行删除并连接WPT和ARPT表";
    item.message =  "DELETE FROM ARPT WHERE ARPT_lon<-45,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                    "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteARPTtable_1C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0EX;
    item.threadFuncs[2] = selectTableWPTjoinARPT2C1EX;

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "ARPT");

    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(item.selectCount[1] + CT_tool.rowcount[1] != CT_ARPTROWS)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(CT_tool.rowcount[1] != CT_ARPTROWS)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(item.selectCount[0] != CT_tool.rowcount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[0] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    if(item.rcArray[2] == GNCDB_SUCCESS)
    {
        if(item.selectCount[2] != CT_tool.rowcount[2])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else
    {
        if(item.selectCount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiJoin666T1()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用三个线程连接WPT和ARPT表";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_ident<ARPT_ident AND WPT_lon>ARPT_lon,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableWPTjoinARPT1;
    item.threadFuncs[1] = selectTableWPTjoinARPT2C1;
    item.threadFuncs[2] = selectTableWPTjoinARPT0;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0]|| CT_tool.rowcount[2] != item.selectCount[2])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}


/*
227 237 247 257 267 277
337 347 357 367 377
447 457 467 477
557 567 577
667 677
777
 */
/* todo */
int sameTablemultiBlob227set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = "三线程同表BLOB测试";
    item.operation = "使用线程对WPT表进行插入并在WPT表中插入BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = insertWPTtable_SameTab;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_WPTROWS;
    CT_releTest.reuse = true;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob227get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT227.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入并在WPT表中拿取BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = insertWPTtable_SameTab;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    item.selectCount[9] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob227delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入并在WPT表中删除BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = insertWPTtable_SameTab;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if (item.rcArray[9] != 0)
    {
        if(CT_tool.rowcount[2] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob237set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、查询、并插入BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = selectWPTtable_2C;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_WPTROWS;
    CT_releTest.reuse = true;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob237get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT237.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、查询、并拿取BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = selectWPTtable_2C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";
    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[3])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob237delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、查询、并删除BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = selectWPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";
    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[3])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if (item.rcArray[9] != 0)
    {
        if(CT_tool.rowcount[2] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob247set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、更新、并插入BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "UPDATE WPT SET WPT_lat=25.25 WHERE WPT_ident>=YQTGU,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = updateWPTtable_1C1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_WPTROWS;
    CT_releTest.reuse = true;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C3, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob247get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT247.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、更新、并拿取BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "UPDATE WPT SET WPT_lon=-25.25 WHERE WPT_ident>=YQTGU,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = updateWPTtable_1C2;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";
    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C5, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[9] + CT_tool.rowcount[3])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] < item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob247delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、更新、并删除BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "UPDATE WPT SET WPT_lon=-25.25 WHERE WPT_lon>=90.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = updateWPTtable_1C4;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";
    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C3, NULL, NULL, 1, 0, 1, "WPT", "WPT_lon>=90.0");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[3])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if (item.rcArray[9] != 0)
    {
        if(CT_tool.rowcount[2] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else{
        if(CT_tool.rowcount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob257set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、删除、并插入BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "DELETE FROM WPT WHERE WPT_lon<-22.5,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=ATLVQ";

    item.ptCount = 3;
    item.threadFuncs[2] = insertWPTtable_SameTab;
    item.threadFuncs[0] = deleteWPTtable_1C1;
    item.threadFuncs[1] = setWPTTableBlob;
    item.blobtool = &blobTool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_WPTROWS;
    CT_releTest.reuse = true;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "ATLVQ";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "WPT", "WPT_lon<-22.5");

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    //GNCDB_select(ct_Global.db, ct_CallBack, NULL, NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=ATLVQ");
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != blobTool.size)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            my_free(blobTool.buf);
            return -1;
        }
    }
    else{
        GNCDB_setBlob(ct_Global.db, "WPT", 3, blobTool.buf, blobTool.size, 1, blobTool.fieldValue);
    }
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS - item.selectCount[0] )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        my_free(blobTool.buf);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    my_free(blobTool.buf);
    return 0;
}

int sameTablemultiBlob257get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT257.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、删除、并拿取BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "DELETE FROM WPT WHERE WPT_lat<-22.5,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=ATLVQ";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = deleteWPTtable_1C2;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=ATLVQ");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "ATLVQ";

    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C5, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[9] + CT_tool.rowcount[3] - item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob257delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、删除、并删除BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "DELETE FROM WPT WHERE WPT_lon<22.5 WPT_lon>0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=ATLVQ";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = deleteWPTtable_2C0;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "ATLVQ";
    item.selectCount[9] = 0;
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=ATLVQ");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[3] - item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if (item.rcArray[9] == 0)
    {
        if(CT_tool.rowcount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob267set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、连接查询、并插入BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    CT_releTest.insertRows = 50;
    CT_releTest.allRows = CT_WPTROWS;
    CT_releTest.reuse = true;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob267get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT267.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、连接查询、并拿取BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[3] = 0;
    item.selectCount[9] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[3] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob267delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行插入、连接查询、并删除BLOB";
    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = insertWPTtable_SameTab;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";
    CT_tool.rowcount[3] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[3], NULL, 1, 0, 0, "WPT");
    item.selectCount[9] = 0;
    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_tool.rowcount[3] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

//int sameTablemultiBlob277set()
//{
//    CT_Pthread item;
//    BLOBTOOL blobTool = {0};
//    FILE* fp = NULL;
//    char path[128] = { 0 };
//    item.project = NULL;
//    item.operation = "使用线程对WPT表进行插入、查询、并插入BLOB";
//    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
//                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
//                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
//                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";
//
//    item.ptCount = 3;
//    item.threadFuncs[0] = insertWPTtable_SameTab;
//    item.threadFuncs[1] = selectTableWPTjoinARPT0;
//    item.threadFuncs[2] = setWPTTableBlob;
//    item.blobtool = &blobTool;
//
//    CT_releTest.insertRows = 50;
//    CT_releTest.allRows = CT_WPTROWS;
//    CT_releTest.reuse = true;
//
//    sprintf(path, "%s%s", blobPath, blobfile2);
//    fp = fopen(path, "rb");
//    fseek(fp, 0, SEEK_END);
//    blobTool.size = ftell(fp);
//    rewind(fp);
//    blobTool.buf = my_malloc(blobTool.size);
//    if(blobTool.buf == NULL)
//    {
//        return -1;
//    }
//    fread(blobTool.buf, blobTool.size, 1, fp);
//    blobTool.fieldValue = "AFTTM";
//
//    item.selectCount[9] = 0;
//    create_pthreadInit(&item);
//    fclose(fp);
//    my_free(blobTool.buf);
//
//    CT_tool.rowcount[1] = 0;
//    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
//    CT_tool.rowcount[2] = 0;
//    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
//    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != blobTool.size)
//    {
//        item.rc = -1;
//        CT_updatamessage(&item);
//        return -1;
//    }
//    item.rc = 0;
//    CT_updatamessage(&item);
//    return 0;
//}
//
//int sameTablemultiBlob277get()
//{
//    CT_Pthread item;
//    BLOBTOOL blobTool = {0};
//    FILE* fp = NULL;
//    int rc = 0;
//    char rootPath[128] = { 0 };
//    char fileName[] = "blob237.png";
//    bool flag = false;
//    char path[128] = { 0 };
//    sprintf(path, "%s%s", blobPath, blobfile2);
//
//    item.project = NULL;
//    item.operation = "使用线程对WPT表进行插入、查询、并拿取BLOB";
//    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
//                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
//                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
//                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";
//
//    item.ptCount = 3;
//    item.threadFuncs[0] = insertWPTtable_SameTab;
//    item.threadFuncs[1] = selectTableWPTjoinARPT0;
//    item.threadFuncs[2] = getWPTTableBlob;
//    item.blobtool = &blobTool;
//
//    CT_tool.rowcount[2] = 0;
//    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
//    if(rc )
//    {
//        item.rc = -1;
//        CT_updatamessage(&item);
//        return rc;
//    }
//    blobTool.size = CT_tool.rowcount[2];
//    sprintf(path, "%s%s", blobPath, fileName);
//    fp = fopen(path, "wb");
//    blobTool.buf = my_malloc(blobTool.size);
//    if(blobTool.buf == NULL)
//    {
//        fclose(fp);
//        item.rc = -1;
//        CT_updatamessage(&item);
//        return -1;
//    }
//    blobTool.fieldValue = "AFTTM";
//
//    create_pthreadInit(&item);
//    if (item.rc != 0)
//    {
//        fclose(fp);
//        my_free(blobTool.buf);
//        CT_updatamessage(&item);
//        return -1;
//    }
//
//    fwrite(blobTool.buf, blobTool.size, 1, fp);
//    fclose(fp);
//    my_free(blobTool.buf);
//
//    CT_tool.rowcount[1] = 0;
//    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
//    flag = ct_CompareFiles(rootPath, path);
//    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS || CT_tool.rowcount[0] != item.selectCount[0])
//    {
//        item.rc = -1;
//        CT_updatamessage(&item);
//        return -1;
//    }
//    if(!flag)
//    {
//        item.rc = -1;
//        CT_updatamessage(&item);
//        return -1;
//    }
//
//    item.rc = 0;
//    CT_updatamessage(&item);
//    return 0;
//}
//
//int sameTablemultiBlob277delete()
//{
//    CT_Pthread item;
//    BLOBTOOL blobTool = {0};
//
//    item.project = NULL;
//    item.operation = "使用线程对WPT表进行插入、查询、并删除BLOB";
//    item.message = "INSERRT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob)"
//                   "VALUES(ct_wpt.sc8_wpt_ident ct_wpt.f64_lon ct_wpt.f64_lat NULL),"
//                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
//                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";
//
//    item.ptCount = 3;
//    item.threadFuncs[0] = insertWPTtable_SameTab;
//    item.threadFuncs[1] = selectTableWPTjoinARPT0;
//    item.threadFuncs[2] = deleteWPTTableBlob;
//    item.blobtool = &blobTool;
//
//    blobTool.fieldValue = "AFTTM";
//
//    create_pthreadInit(&item);
//    if (item.rc != 0)
//    {
//        CT_updatamessage(&item);
//        return -1;
//    }
//
//    CT_tool.rowcount[1] = 0;
//    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 0, "WPT");
//    CT_tool.rowcount[2] = 0;
//    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
//    if(CT_tool.rowcount[1] != item.selectCount[9] + CT_WPTROWS || CT_tool.rowcount[0] != item.selectCount[0])
//    {
//        item.rc = -1;
//        CT_updatamessage(&item);
//        return -1;
//    }
//    if(CT_tool.rowcount[2] != 0)
//    {
//        item.rc = -1;
//        CT_updatamessage(&item);
//        return -1;
//    }
//
//    item.rc = 0;
//    CT_updatamessage(&item);
//    return 0;
//}

int sameTablemultiBlob337set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、并插入BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = selectWPTtable_2C;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "QJTUJ";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob337get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT337.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、并拿取BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = selectWPTtable_2C;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[1];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "QJTUJ";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob337delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、并删除BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = selectWPTtable_2C;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "QJTUJ";

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob347set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、更新、并插入BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_lat=25.25 WHERE WPT_ident>=YQTGU,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C1;
    item.threadFuncs[1] = updateWPTtable_1C1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C3, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob347get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT347.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、更新、并拿取BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_lon=-25.25 WHERE WPT_ident>=YQTGU,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C;
    item.threadFuncs[1] = updateWPTtable_1C2;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C5, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob347delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、更新、并删除BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "UPDATE WPT SET WPT_lon=-25.25 WHERE WPT_lon>=90.0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C1;
    item.threadFuncs[1] = updateWPTtable_1C4;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C3, NULL, NULL, 1, 0, 1, "WPT", "WPT_lon>=90.0");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob357set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、删除、并插入BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "DELETE FROM WPT WHERE WPT_lon<-22.5,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C1;
    item.threadFuncs[1] = deleteWPTtable_1C1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "WPT", "WPT_lon<-22.5");

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != blobTool.size)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else{
        GNCDB_setBlob(ct_Global.db, "WPT", 3, blobTool.buf, blobTool.size, 1, blobTool.fieldValue);
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob357get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT357.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、删除、并拿取BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "DELETE FROM WPT WHERE WPT_lat<-22.5,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C;
    item.threadFuncs[1] = deleteWPTtable_1C2;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "WPT", "WPT_lat<-22.5");

    create_pthreadInit(&item);


    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_1C5, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident>=YQTGU");
    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != item.selectCount[1])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        if(item.rcArray[9] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }   
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob357delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、删除、并删除BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "DELETE FROM WPT WHERE WPT_lon<22.5 WPT_lon>0,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C1;
    item.threadFuncs[1] = deleteWPTtable_2C0;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_lon>0.0", "WPT_lon<22.5");

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[2] != 0)
    {
        if(item.rcArray[9] == 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }       
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob367set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、并插入BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "QJTUJ";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob367get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT2367.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、并拿取BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[1];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "QJTUJ";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob367delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行查询、并删除BLOB";
    item.message = "SELECT * FROM WPT WHERE WPT_ident>=VCOED WPT_lat<-25.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectWPTtable_2C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "QJTUJ";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob447set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、并插入BLOB";
    item.message = "UPDATE WPT SET WPT_lat=25.25 WHERE WPT_ident>=YQTGU WPT_ident=<ZQTGU,"
                   "UPDATE WPT SET WPT_lat=25.25 WHERE WPT_ident>=DZECO WPT_ident=<FXXZR,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_2C0;
    item.threadFuncs[1] = updateWPTtable_2C1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C0, NULL, NULL, 1, 0, 2, "WPT", "WPT_ident>=YQTGU", "WPT_ident=<ZQTGU");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C1, NULL, NULL, 1, 0, 2, "WPT", "WPT_ident>=DZECO", "WPT_ident=<FXXZR");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob447get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT447.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、并拿取BLOB";
    item.message = "UPDATE WPT SET WPT_lon=0.0 WHERE WPT_ident>=HMSAN WPT_ident=<LSUWL,"
                   "UPDATE WPT SET WPT_lat=125.25 WHERE WPT_lon>=-25.0 WPT_lon=<0.0,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_2C2;
    item.threadFuncs[1] = updateWPTtable_2C3;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    CT_tool.rowcount[0] = 0;
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C2, NULL, NULL, 1, 0, 2, "WPT", "WPT_ident>=HMSAN",
                      "WPT_ident=<LSUWL");
    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C3, NULL, NULL, 1, 0, 2, "WPT", "WPT_lon>=-25.0",
                      "WPT_lon=<0.0");
    flag = ct_CompareFiles(rootPath, path);
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob447delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、并删除BLOB";
    item.message = "UPDATE WPT SET WPT_lat=10.0 WHERE WPT_lon>=45.0 WPT_ident=<JDLYH,"
                   "UPDATE WPT SET WPT_lon=12.0 WHERE WPT_lat=<-45.0 WPT_ident>=LOPAA,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_2C4;
    item.threadFuncs[1] = updateWPTtable_2C5;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C4, NULL, NULL, 1, 0, 2, "WPT", "WPT_lon>=45.0", "WPT_ident=<JDLYH");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C5, NULL, NULL, 1, 0, 2, "WPT", "WPT_lat=<-45.0",
                 "WPT_ident>=LOPAA");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(CT_tool.rowcount[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob457set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、删除、并插入BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_lon<-22.5,"
                   "UPDATE WPT SET WPT_lat=25.25 WHERE WPT_ident>=DZECO WPT_ident=<FXXZR,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_1C1;
    item.threadFuncs[1] = updateWPTtable_2C1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 1, "WPT", "WPT_lon<-22.5");

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C1, NULL, NULL, 1, 0, 2, "WPT", "WPT_ident>=DZECO", "WPT_ident=<FXXZR");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != blobTool.size)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob457get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT457.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、删除、并拿取BLOB";
    item.message = "UPDATE WPT SET WPT_lon=0.0 WHERE WPT_ident>=HMSAN WPT_ident=<LSUWL,"
                   "DELETE FROM WPT WHERE WPT_lat<-22.5,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_2C2;
    item.threadFuncs[1] = deleteWPTtable_1C2;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 1, "WPT", "WPT_lat<-22.5");

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    CT_tool.rowcount[0] = 0;
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C2, NULL, NULL, 1, 0, 2, "WPT", "WPT_ident>=HMSAN",
                      "WPT_ident=<LSUWL");
    flag = ct_CompareFiles(rootPath, path);
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob457delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、删除、并删除BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_lon<22.5 WPT_lon>0,"
                   "UPDATE WPT SET WPT_lon=12.0 WHERE WPT_lat=<-45.0 WPT_ident>=LOPAA,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_2C0;
    item.threadFuncs[1] = updateWPTtable_2C5;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_lon>0.0", "WPT_lon<22.5");

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C5, NULL, NULL, 1, 0, 2, "WPT", "WPT_lat=<-45.0",
                 "WPT_ident>=LOPAA");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob467set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、连接查询并插入BLOB";
    item.message = "UPDATE WPT SET WPT_lat=25.25 WHERE WPT_ident>=YQTGU WPT_ident=<ZQTGU,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_2C0;
    item.threadFuncs[1] = selectTableWPTjoinARPT1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C0, NULL, NULL, 1, 0, 2, "WPT", "WPT_ident>=YQTGU", "WPT_ident=<ZQTGU");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(CT_tool.rowcount[1] != item.selectCount[1] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[2] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob467get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT467.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、连接查询并拿取BLOB";
    item.message = "UPDATE WPT SET WPT_lon=0.0 WHERE WPT_ident>=HMSAN WPT_ident=<LSUWL,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_2C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT1;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    CT_tool.rowcount[0] = 0;
    rc = GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C2, NULL, NULL, 1, 0, 2, "WPT", "WPT_ident>=HMSAN",
                      "WPT_ident=<LSUWL");
    flag = ct_CompareFiles(rootPath, path);
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob467delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行更新、连接查询并删除BLOB";
    item.message = "UPDATE WPT SET WPT_lat=10.0 WHERE WPT_lon>=45.0 WPT_ident=<JDLYH,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = updateWPTtable_2C4;
    item.threadFuncs[1] = selectTableWPTjoinARPT1;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, ct_callBackUpdateWPTtable_2C4, NULL, NULL, 1, 0, 2, "WPT", "WPT_lon>=45.0", "WPT_ident=<JDLYH");
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob557set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行删除、并插入BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_lon>0.0 WPT_lon<22.5,"
                   "DELETE FROM WPT WHERE WPT_ident>=YQTGU WPT_ident=<ZQTGU,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_2C0;
    item.threadFuncs[1] = deleteWPTtable_2C1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_lon>0.0", "WPT_lon<22.5");
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 2, "WPT", "WPT_ident>=YQTGU", "WPT_ident=<ZQTGU");


    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != blobTool.size)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    else{
        GNCDB_setBlob(ct_Global.db, "WPT", 3, blobTool.buf, blobTool.size, 1, blobTool.fieldValue);
    }
    
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob557get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT557.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行删除、并拿取BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_ident>=DZECO WPT_ident=<FXXZR,"
                   "DELETE FROM WPT WHERE WPT_ident>=HMSAN WPT_ident=<LSUWL,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_2C2;
    item.threadFuncs[1] = deleteWPTtable_2C3;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 2, "WPT", "WPT_ident>=DZECO", "WPT_ident=<FXXZR");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_ident>=HMSAN", "WPT_ident=<LSUWL");

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);
    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    flag = ct_CompareFiles(rootPath, path);
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob557delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行删除、并删除BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_lon>=-25.0 WPT_lon=<0.0,"
                   "DELETE FROM WPT WHERE WPT_lon>=45.0 WPT_ident=<JDLYH,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_2C4;
    item.threadFuncs[1] = deleteWPTtable_2C5;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[1], NULL, 1, 0, 2, "WPT", "WPT_lon>=-25.0", "WPT_lon=<0.0");
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_lon>=45.0", "WPT_ident=<JDLYH");
    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob567set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行删除、连接查询、并插入BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_lon>0.0 WPT_lon<22.5,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_2C0;
    item.threadFuncs[1] = selectTableWPTjoinARPT1;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_lon>0.0", "WPT_lon<22.5");

    item.selectCount[9] = 0;
    create_pthreadInit(&item);
    fclose(fp);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            my_free(blobTool.buf);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            my_free(blobTool.buf);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != blobTool.size)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            my_free(blobTool.buf);
            return -1;
        }
    }
    else{
        GNCDB_setBlob(ct_Global.db, "WPT", 3, blobTool.buf, blobTool.size, 1, blobTool.fieldValue);
    }
    item.rc = 0;
    my_free(blobTool.buf);
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob567get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT567.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行删除、连接查询、并拿取BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_ident>=DZECO WPT_ident=<FXXZR,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_2C2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_ident>=DZECO", "WPT_ident=<FXXZR");

    CT_tool.rowcount[2] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(rc)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "AFTTM";

    create_pthreadInit(&item);

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    flag = ct_CompareFiles(rootPath, path);
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(!flag)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }

    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob567delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行删除、连接查询、并删除BLOB";
    item.message = "DELETE FROM WPT WHERE WPT_lon>=-25.0 WPT_lon=<0.0,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=AFTTM";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTtable_2C4;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "AFTTM";

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 2, "WPT", "WPT_lon>=-25.0", "WPT_lon=<0.0");

    create_pthreadInit(&item);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    if(item.rcArray[0] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[0] != item.selectCount[0])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[1] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[1] != item.selectCount[1])
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }
    }
    if(item.rcArray[9] == GNCDB_SUCCESS)
    {
        if(CT_tool.rowcount[2] != 0)
        {
            item.rc = -1;
            CT_updatamessage(&item);
            return -1;
        }

    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob667set()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    char path[128] = { 0 };
    item.project = NULL;
    item.operation = "使用线程对WPT表进行连接查询、并插入BLOB";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableWPTjoinARPT2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = setWPTTableBlob;
    item.blobtool = &blobTool;

    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "rb");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "QJTUJ";

    create_pthreadInit(&item);
    fclose(fp);
    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0] || CT_tool.rowcount[1] != blobTool.size)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob667get()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};
    FILE* fp = NULL;
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT667.png";
    bool flag = false;
    char path[128] = { 0 };
    sprintf(rootPath, "%s%s", blobPath, blobfile2);

    item.project = NULL;
    item.operation = "使用线程对WPT表进行连接查询、并拿取BLOB";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "SELECT WPT_blob FROM WPT WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableWPTjoinARPT2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = getWPTTableBlob;
    item.blobtool = &blobTool;

    CT_tool.rowcount[1] = 0;
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(rc )
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return rc;
    }
    blobTool.size = CT_tool.rowcount[1];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    blobTool.fieldValue = "QJTUJ";

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        fclose(fp);
        my_free(blobTool.buf);
        CT_updatamessage(&item);
        return -1;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);

    flag = ct_CompareFiles(rootPath, path);
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(!flag)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob667delete()
{
    CT_Pthread item;
    BLOBTOOL blobTool = {0};

    item.project = NULL;
    item.operation = "使用线程对WPT表进行连接查询、并删除BLOB";
    item.message = "SELECT * FROM WPT JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat,"
                   "SELECT * FROM WPT JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat,"
                   "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=QJTUJ";

    item.ptCount = 3;
    item.threadFuncs[0] = selectTableWPTjoinARPT2;
    item.threadFuncs[1] = selectTableWPTjoinARPT0;
    item.threadFuncs[2] = deleteWPTTableBlob;
    item.blobtool = &blobTool;

    blobTool.fieldValue = "QJTUJ";

    create_pthreadInit(&item);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(CT_tool.rowcount[2] != item.selectCount[2] || CT_tool.rowcount[0] != item.selectCount[0])
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    if(CT_tool.rowcount[1] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    item.rc = 0;
    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob777set()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对WPT表同时进行setBLOB文件";
    item.message = ",,,";

    item.ptCount = 3;
    item.threadFuncs[0] = setWPTTableBlob_0;
    item.threadFuncs[1] = setWPTTableBlob_1;
    item.threadFuncs[2] = setWPTTableBlob_2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob777get()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对WPT表同时进行getBLOB文件";
    item.message = ",,,";

    item.ptCount = 3;
    item.threadFuncs[0] = getWPTTableBlob_0;
    item.threadFuncs[1] = getWPTTableBlob_1;
    item.threadFuncs[2] = getWPTTableBlob_2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sameTablemultiBlob777delete()
{
    CT_Pthread item;
    item.project = NULL;
    item.operation = "使用线程对WPT表同时进行deleteBLOB文件";
    item.message = ",,,";

    item.ptCount = 3;
    item.threadFuncs[0] = deleteWPTTableBlob_0;
    item.threadFuncs[1] = deleteWPTTableBlob_1;
    item.threadFuncs[2] = deleteWPTTableBlob_2;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}


int sql_multiAggregateFunTestOtherTable1()
{
    CT_Pthread item = {0};
    item.project = "聚集函数测试";
    item.operation = "求和 最大 最小";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT SUM(WPT_lon) FROM WPT;";
    item.SQL[1] = "SELECT MAX(ARPT_lon) FROM ARPT;";
    item.SQL[2] = "SELECT MIN(NAV_vor) FROM NAV;";

    item.callBack[0] = ct_testCallBackSqlAggregate1;
    item.callBack[1] = ct_testCallBackSqlAggregate1;
    item.callBack[2] = ct_testCallBackSqlAggregate1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcounts[0] = 0;
    CT_tool.rowcounts[1] = 0;
    CT_tool.rowcounts[2] = 100000;
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate1, NULL, &CT_tool.rowcounts[0], 1, 0, 0, "WPT");
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate4, NULL, &CT_tool.rowcounts[1], 1, 0, 0, "ARPT");
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate5, NULL, &CT_tool.rowcounts[2], 1, 0, 0, "NAV");

    if(ct_compare_float(CT_tool.rowcounts[0], item.selectCounts[0], 2) 
    || ct_compare_float(CT_tool.rowcounts[1], item.selectCounts[1], 2)
    || ct_compare_float(CT_tool.rowcounts[2], item.selectCounts[2], 2))
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}


int sql_multiAggregateFunTestOtherTable2()
{
    CT_Pthread item = {0};
    int rows = 0;
    double val = 0;
    item.project = NULL;
    item.operation = "平均 子查询 升序";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT AVG(SID_track) FROM SID;";
    item.SQL[1] = "SELECT * FROM WPT WHERE WPT_lon=(SELECT ARPT_lon FROM ARPT WHERE ARPT_ident='KDWTO');";
    item.SQL[2] = "SELECT * FROM NAV ORDER BY NAV_lon;";

    item.callBack[0] = ct_testCallBackSqlAggregate1;
    item.callBack[1] = ct_testCallBackSqlSubquery1;
    item.callBack[2] = ct_testCallBackSqlOrderBy1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcounts[0] = 0;
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate6, &rows, &CT_tool.rowcounts[0], 1, 0, 0, "SID");
    val = CT_tool.rowcounts[0] / rows;
    if(item.selectCounts[1] < 0 || 
        ct_compare_float(val, item.selectCounts[0], 2)
        || item.selectCounts[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }
    CT_updatamessage(&item);
    return 0;
}

int sql_multiAggregateFunTestOtherTable3()
{
    CT_Pthread item = {0};
    item.project = NULL;
    item.operation = "行数 降序 limit";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT COUNT(*) FROM WPT;";
    item.SQL[1] = "SELECT * FROM NAV ORDER BY NAV_lat DESC;";
    item.SQL[2] = " SELECT * FROM ARPT ORDER BY ARPT_lon ASC LIMIT 5;";

    item.callBack[0] = ct_testCallBackSqlAggregate5;
    item.callBack[1] = ct_testCallBackSqlOrderBy2;
    item.callBack[2] = ct_testCallBackSqlLimit1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");

    if(item.selectCounts[1] != 0 || CT_tool.rowcount[0] != item.selectCounts[0]|| item.selectCounts[2] != 5)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}


/* 分组 */
//todo
int sql_multiAggregateFunTesOtherTable4()
{
    CT_Pthread item = {0};
    item.project = NULL;
    item.operation = "行数 降序 limit";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT COUNT(*) FROM WPT;";
    item.SQL[1] = "SELECT * FROM NAV ORDER BY NAV_lat DESC;";
    item.SQL[2] = " SELECT * FROM ARPT ORDER BY ARPT_lon ASC LIMIT 5;";

    item.callBack[0] = ct_testCallBackSqlAggregate5;
    item.callBack[1] = ct_testCallBackSqlOrderBy2;
    item.callBack[2] = ct_testCallBackSqlLimit1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");

    if(item.selectCount[1] != 0 || CT_tool.rowcount[0] != item.selectCount[0]|| item.selectCount[2] != 5)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}





int sql_multiAggregateFunTestSameTable1()
{
    CT_Pthread item = {0};
    item.project = NULL;
    item.operation = "求和 最大 最小";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT SUM(WPT_lon) FROM WPT;";
    item.SQL[1] = "SELECT MAX(WPT_lat) FROM WPT;";
    item.SQL[2] = "SELECT MIN(WPT_lat) FROM WPT;";

    item.callBack[0] = ct_testCallBackSqlAggregate1;
    item.callBack[1] = ct_testCallBackSqlAggregate1;
    item.callBack[2] = ct_testCallBackSqlAggregate1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcounts[0] = 0;
    CT_tool.rowcounts[1] = 0;
    CT_tool.rowcounts[2] = 100000;
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate1, NULL, &CT_tool.rowcounts[0], 1, 0, 0, "WPT");
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate7, NULL, &CT_tool.rowcounts[1], 1, 0, 0, "WPT");
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate8, NULL, &CT_tool.rowcounts[2], 1, 0, 0, "WPT");

    if(ct_compare_float(CT_tool.rowcounts[0], item.selectCounts[0], 2) 
    || ct_compare_float(CT_tool.rowcounts[1], item.selectCounts[1], 2)
    || ct_compare_float(CT_tool.rowcounts[2], item.selectCounts[2], 2))
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sql_multiAggregateFunTestSameTable2()
{
    CT_Pthread item = {0};
    int rows = 0;
    double val = 0;
    item.project = NULL;
    item.operation = "平均 子查询 升序";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT AVG(WPT_lon) FROM WPT;";
    item.SQL[1] = "SELECT * FROM WPT WHERE WPT_lon=(SELECT ARPT_lon FROM ARPT WHERE ARPT_ident='KDWTO');";
    item.SQL[2] = "SELECT * FROM WPT ORDER BY WPT_lon;";

    item.callBack[0] = ct_testCallBackSqlAggregate1;
    item.callBack[1] = ct_testCallBackSqlSubquery1;
    item.callBack[2] = ct_testCallBackSqlOrderBy1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcounts[0] = 0;
    GNCDB_select(ct_Global.db, ct_testCallBackAggregate2, &rows, &CT_tool.rowcounts[0], 1, 0, 0, "WPT");
    val = CT_tool.rowcounts[0] / rows;
    if(item.selectCounts[1] < 0 || 
        ct_compare_float(val, item.selectCounts[0], 2)
        || item.selectCounts[2] != 0)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

int sql_multiAggregateFunTestSameTable3()
{
    CT_Pthread item = {0};
    item.project = NULL;
    item.operation = "行数 降序 limit";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT COUNT(*) FROM WPT;";
    item.SQL[1] = "SELECT * FROM WPT ORDER BY WPT_lat DESC;";
    item.SQL[2] = " SELECT * FROM WPT ORDER BY WPT_lat ASC LIMIT 5;";

    item.callBack[0] = ct_testCallBackSqlAggregate5;
    item.callBack[1] = ct_testCallBackSqlOrderBy3;
    item.callBack[2] = ct_testCallBackSqlLimit1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");
    if(item.selectCounts[1] != 0 || CT_tool.rowcount[0] != item.selectCounts[0]|| item.selectCounts[2] != 5)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}

/* 分组 */
//todo
int sql_multiAggregateFunTesSameTable4()
{
    CT_Pthread item = {0};
    item.project = NULL;
    item.operation = "行数 降序 limit";
    item.message = NULL;

    item.ptCount = 3;
    item.threadFuncs[0] = Sql_AggregateFunThread0;
    item.threadFuncs[1] = Sql_AggregateFunThread1;
    item.threadFuncs[2] = Sql_AggregateFunThread2;

    item.SQL[0] = "SELECT COUNT(*) FROM WPT;";
    item.SQL[1] = "SELECT * FROM WPT ORDER BY WPT_lat DESC;";
    item.SQL[2] = " SELECT * FROM WPT ORDER BY WPT_lat ASC LIMIT 5;";

    item.callBack[0] = ct_testCallBackSqlAggregate5;
    item.callBack[1] = ct_testCallBackSqlOrderBy3;
    item.callBack[2] = ct_testCallBackSqlLimit1;

    create_pthreadInit(&item);
    if (item.rc != 0)
    {
        CT_updatamessage(&item);
        return -1;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, NULL, &CT_tool.rowcount[0], NULL, 1, 0, 0, "WPT");
    if(item.selectCounts[1] != 0 || CT_tool.rowcount[0] != item.selectCounts[0]|| item.selectCounts[2] != 5)
    {
        item.rc = -1;
        CT_updatamessage(&item);
        return -1;
    }

    CT_updatamessage(&item);
    return 0;
}
