#ifndef _CONCURRENCE_H_
#define _CONCURRENCE_H_

#include <stdio.h>
#include <pthread.h>
#include <unistd.h>
#include "demo.h"
#include "pthreadpool.h"
#include "callback.h"
//#include "singletest.h"

#define SQL_CTTESTMODE          1

#define CT_SELECTPRINTF			2           /* 0 控制台 1 文件 2 控制台+文件 */

#define CT_ARPTROWS					1000
#define CT_WPTROWS				    1000
#define CT_NAVROWS					1000
#define CT_SIDROWS					1000
#define CT_APCHROWS					1000
#define CT_DEFINEWPTROWS			1000
#define CT_FPLNROWS					1000


/* 数据类型枚举 */
enum CT_DATETYPE
{
	CT_INT,
	CT_DOUBLE,
	CT_CHAR
};

typedef struct
{
    char tableName1[20];
    char tableName2[20];
    CallBack2 callBack;
    char* cond[5];
    int select;
}JOINTOOL;

typedef struct
{
    char* fieldValue;
    BYTE* buf;
    int size;
}BLOBTOOL;

/* 并发测试结构体  整个测试只有一个 */
typedef struct
{
	FILE* fp;           /*文件句柄*/
	GNCDB* db;          /*数据库指针*/
	int num1;           /*主序号*/
	int num2;           /*副序号*/
	int allTest;        /*总的测试项数量*/
	int successTest;    /*成功的测试项数量*/
	int failTest;       /*失败的测试项数量*/

    ThreadPool* pthreadPool;
    ReadWriteLock latch;
	
}CT_Global;

/* 线程函数结构体  针对删改查 各个线程函数有一个 */
typedef struct CT_Item
{
    int allRows;
    int insertRows;

    int rows;
	char* cond[5];
	CallBack2 callBack;

	int setNum;
	char* fieldName[5];
	int type[5];
	void* value[5];

    JOINTOOL* jointool;
}CT_Item;

/* 测试项结构体  每个测试项有一个 */
typedef struct CT_Pthread
{
	char* project;
	char* operation;
	char* message;
	int rc;
    int rcArray[PTHREAD_MAXCOUNT];

    BLOBTOOL* blobtool;
    JOINTOOL* jointool;
    int selectCount[PTHREAD_MAXCOUNT];
    double selectCounts[PTHREAD_MAXCOUNT];
	int ptCount;            /*线程数量*/
	int (*threadFuncs[PTHREAD_MAXCOUNT])(void*);    /*线程函数组*/

    char *SQL[5];         /*SQL语句*/
    CallBack2 callBack[5]; /*回调函数*/
    

}CT_Pthread;

/* 辅助并发测试验证结构体  整个测试只有一个 */
typedef struct CT_CREATETABLE
{
    int rowNum;
    int rowcount[PTHREAD_MAXCOUNT];
    double rowcounts[PTHREAD_MAXCOUNT];
    char* tableName;
    char* fieldValue;
    bool testFlag;
}CT_CREATETABLE;

/* 关联性测试辅助工具 多个线程访问同一变量 */
typedef struct RELEVANCETEST
{
    int allRows;
    int insertRows;
    bool reuse;
    ReadWriteLock lock;
}RELEVANCETEST;

extern RELEVANCETEST CT_releTest;
extern CT_Global ct_Global;
extern CT_CREATETABLE CT_tool;
extern ReadWriteLock createTableLock;
extern int createFlag;
extern char blobPath[];
extern char savePath[];
extern char blobfile1[];
extern char blobfile2[];
extern char blobfile3[];
extern char blobfile4[];
//extern CT_Item ct_Item;

int create_pthreadInit(CT_Pthread* item);
int concurrencetest();
void multiThreadTest();

int initConcurrenceTest();
void CT_updatamessage(CT_Pthread* ct_Item);

void ct_doubleThreadTest();
void ct_createTableTest();
void ct_insertTableTest();
void ct_selectTableTest();
void ct_updateTableTest();
void ct_deleteTableTest();
void ct_dropTableTest();
int closeConcurrenceTest();
void displayConcurrenceTest();
bool ct_CompareFiles(const char* filename1, const char* filename2);

void sameTableTest();

int sameTablemulti();

int multiCreateTable();
int multiInsertTable();
int multiSelectTable();
int multiUpdateDeleteTable();
int multiJoinTable();
int sameTablemultiJoin();
int multiTableBlobTest();
int sameTablemultiBlob();

int multiThreadInit();
int multiThreadReset();
int createAllTable();
int insertAllTable();
int setJoinOrBlobTest();

extern int createFlag;
int TEST_createTable();
/* ======================== 1-机场数据ARPT ========================= */
int ARPT_createTable();
int ARPT_insertTable(CT_Item* ct_Item);
int ARPT_selectTable(CT_Item* ct_Item);
int ARPT_updateTable(CT_Item* ct_Item);
int ARPT_deleteTable(CT_Item* ct_Item);
int ARPT_dropTable();

/* =======================2-航路点数据WPT============================ */
int WPT_createTable();
int WPT_insertTable(CT_Item* ct_Item);
int WPT_selectTable(CT_Item* ct_Item);
int WPT_updateTable(CT_Item* ct_Item);
int WPT_deleteTable(CT_Item* ct_Item);
int WPT_dropTable();
/* Blob相关函数 */
int WPT_setBlob(BLOBTOOL* blobTool);
int WPT_getBlob(BLOBTOOL* blobTool);
int WPT_deleteBlob(BLOBTOOL* blobTool);

/* ====================== 3-导航数据NAV============================== */
int NAV_createTable();
int NAV_insertTable(CT_Item* ct_Item);
int NAV_selectTable(CT_Item* ct_Item);
int NAV_updateTable(CT_Item* ct_Item);
int NAV_deleteTable(CT_Item* ct_Item);
int NAV_dropTable();

/* ======================= 4-离场程序数据SID ======================== */
int SID_createTable();
int SID_insertTable(CT_Item* ct_Item);
int SID_selectTable(CT_Item* ct_Item);
int SID_updateTable(CT_Item* ct_Item);
int SID_deleteTable(CT_Item* ct_Item);
int SID_dropTable();

/* =========================5-进近程序数据APCH======================= */
int APCH_createTable();
int APCH_insertTable(CT_Item* ct_Item);
int APCH_selectTable(CT_Item* ct_Item);
int APCH_updateTable(CT_Item* ct_Item);
int APCH_deleteTable(CT_Item* ct_Item);
int APCH_dropTable();

/* ========================6-自定义航路点数据DEFINE_WPT================ */
int DEFINE_WPT_createTable();
int DEFINE_WPT_insertTable(CT_Item* ct_Item);
int DEFINE_WPT_selectTable(CT_Item* ct_Item);
int DEFINE_WPT_updateTable(CT_Item* ct_Item);
int DEFINE_WPT_deleteTable(CT_Item* ct_Item);
int DEFINE_WPT_dropTable();

/* ==============================7-飞行计划数据FPLN========================== */
int FPLN_createTable();
int FPLN_insertTable(CT_Item* ct_Item);
int FPLN_selectTable(CT_Item* ct_Item);
int FPLN_updateTable(CT_Item* ct_Item);
int FPLN_deleteTable(CT_Item* ct_Item);
int FPLN_dropTable();

/* ==============================join函数接口========================== */
int Join_selectTable(CT_Item* ct_Item);

/* =================线程函数===================== */

/* 同表测试线程函数 */
int insertARPTtable_SameTab(void* arg);
int insertWPTtable_SameTab(void* arg);
int insertNAVtable_SameTab(void* arg);

/* join函数接口 */
int selectJoinTable(void* arg);
/* 多线程调用接口 */
int multiSelectJoinTable(void* arg);

int selectTableWPTjoinARPT0(void* arg);
int selectTableWPTjoinARPT1(void* arg);
int selectTableWPTjoinARPT2(void* arg);
int selectTableWPTjoinARPT2C1(void* arg);
int selectTableWPTjoinARPT0EX(void* arg);
int selectTableWPTjoinARPT1EX(void* arg);
int selectTableWPTjoinARPT2EX(void* arg);
int selectTableWPTjoinARPT2C1EX(void* arg);
int selectTableARPTjoinSID(void* arg);
int selectTableFPLNjoinNAV(void* arg);
int setWPTTableBlob(void* arg);
int getWPTTableBlob(void* arg);
int deleteWPTTableBlob(void* arg);
int setWPTTableBlob_0(void* arg);
int getWPTTableBlob_0(void* arg);
int deleteWPTTableBlob_0(void* arg);
int setWPTTableBlob_1(void* arg);
int getWPTTableBlob_1(void* arg);
int deleteWPTTableBlob_1(void* arg);
int setWPTTableBlob_2(void* arg);
int getWPTTableBlob_2(void* arg);
int deleteWPTTableBlob_2(void* arg);


int createTesttable(void* arg);
int createARPTtable(void* arg);
int createWPTtable(void *arg);
int createNAVtable(void* arg);
int createSIDtable(void* arg);
int createAPCHtable(void* arg);
int createDEFINEWPTtable(void* arg);
int createFPLNtable(void* arg);

int insertARPTtable(void* arg);
int insertWPTtable(void* arg);
int insertNAVtable(void* arg);
int insertSIDtable(void* arg);
int insertAPCHtable(void* arg);
int insertDEFINEWPTtable(void* arg);
int insertFPLNtable(void* arg);

/* 查询更新删除由于具有条件筛选具有多种可能，在需要时在进行具体编写 */
int selectARPTtable_1C(void* arg);
int selectARPTtable_1C2(void* arg);
int selectARPTtable_2C(void* arg);
int updateARPTtable_1C1(void* arg);
int updateARPTtable_1C2(void* arg);
int updateARPTtable_1C3(void* arg);
int updateARPTtable_1C4(void* arg);
int updateARPTtable_2C(void* arg);
int deleteARPTtable_1C(void* arg);
int deleteARPTtable_1C1(void* arg);
int deleteARPTtable_1C2(void* arg);
int deleteARPTtable_1C3(void* arg);
int deleteARPTtable_1C4(void* arg);


int selectWPTtable_2C(void* arg);
int selectWPTtable_2C1(void* arg);
int selectWPTtable_2C2(void* arg);
int updateWPTtable_1C1(void* arg);
int updateWPTtable_1C2(void* arg);
int updateWPTtable_1C3(void* arg);
int updateWPTtable_1C4(void* arg);
int updateWPTtable_2C0(void* arg);
int updateWPTtable_2C1(void* arg);
int updateWPTtable_2C2(void* arg);
int updateWPTtable_2C3(void* arg);
int updateWPTtable_2C4(void* arg);
int updateWPTtable_2C5(void* arg);
int deleteWPTtable_1C1(void* arg);
int deleteWPTtable_1C2(void* arg);
int deleteWPTtable_2C0(void* arg);
int deleteWPTtable_2C1(void* arg);
int deleteWPTtable_2C2(void* arg);
int deleteWPTtable_2C3(void* arg);
int deleteWPTtable_2C4(void* arg);
int deleteWPTtable_2C5(void* arg);

int selectNAVtable_2C1(void* arg);
int selectNAVtable_2C2(void* arg);
int updateNAVtable_1C1(void* arg);
int updateNAVtable_1C2(void* arg);
int deleteNAVtable_1C1(void* arg);
int deleteNAVtable_1C2(void* arg);

int selectSIDtable_1C1(void* arg);
int updateSIDtable_1C(void* arg);
int deleteSIDtable_1C(void* arg);

int selectAPCHtable_1C1(void* arg);
int updateAPCHtable_1C(void* arg);
int updateAPCHtable_1C2(void* arg);
int deleteAPCHtable_2C(void* arg);

int selectDEFINE_WPTtable_2C1(void* arg);
int updateDEFINE_WPTtable_1C(void* arg);
int deleteDEFINE_WPTtable_2C(void* arg);

int selectFPLNtable_1C1(void* arg);
int updateFPLNtable_2C(void* arg);
int updateFPLNtable_2C1(void* arg);
int deleteFPLNtable_1C(void* arg);

int dropARPTtable(void* arg);
int dropWPTtable(void* arg);
int dropNAVtable(void* arg);
int dropSIDtable(void* arg);
int dropAPCHtable(void* arg);
int dropDEFINEWPTtable(void* arg);
int dropFPLNtable(void* arg);

/* =================测试项===================== */
int createTableTest_1();
int createTableTest_2();
int createTableTest_3();
int createTableTest_4();
int createTableTest_5();
int createTableTest_6();

int insertTableTest_1();
int insertTableTest_2();
int insertTableTest_3();
int insertTableTest_4();
int insertTableTest_5();

int selectTableTest_1();
int selectTableTest_2();
int selectTableTest_3();
int selectTableTest_4();

int updateTableTest_1();
int updateTableTest_2();
int updateTableTest_3();

int deleteTableTest_1();
int deleteTableTest_2();

int dropTableTest_1();

int closeTest_1();

/* 同表测试 */
int sameTableTestinit();
int sameTableTestCreate();
int sameTableTestInsert1();
int sameTableTestInsert2();
int sameTableTestInsertSelect();
int sameTableTestInsertUpdate();
int sameTableTestInsertDelete();
int sameTableTestSelect();
int sameTableTestSelectUpdete();
int sameTableTestSelectDelete();
int sameTableTestUpdate();
int sameTableTestUpdateDelete();
int sameTableTestDelete();
int sameTableTestDropSelect();
int sameTableTestDrop();

int joinTestinsert1();

/* 多线程测试 */
int multiCreateTable111();
int multiCreateTable112();
int multiCreateTable113();
int multiCreateTable114();
int multiCreateTable115();
int multiCreateTable122();
int multiCreateTable123();
int multiCreateTable124();
int multiCreateTable125();
int multiCreateTable133();
int multiCreateTable134();
int multiCreateTable135();
int multiCreateTable144();
int multiCreateTable145();
int multiCreateTable155();
int multiInsertTable222();
int multiInsertTable223();
int multiInsertTable224();
int multiInsertTable225();
int multiInsertTable233();
int multiInsertTable234();
int multiInsertTable235();
int multiInsertTable244();
int multiInsertTable245();
int multiInsertTable255();
int multiSelectTable333();
int multiSelectTable334();
int multiSelectTable335();
int multiSelectTable344();
int multiSelectTable345();
int multiSelectTable355();
int multiUptateTable444();
int multiUptateTable445();
int multiUptateTable455();
int multiDeleteTable555();

int multiJoinTable116();
int multiJoinTable126();
int multiJoinTable136();
int multiJoinTable146();
int multiJoinTable156();
int multiJoinTable166();
int multiJoinTable226();
int multiJoinTable236();
int multiJoinTable246();
int multiJoinTable256();
int multiJoinTable266();
int multiJoinTable336();
int multiJoinTable346();
int multiJoinTable356();
int multiJoinTable366();
int multiJoinTable446();
int multiJoinTable456();
int multiJoinTable466();
int multiJoinTable556();
int multiJoinTable566();
int multiJoinTable666();

/* 多线程BLOB测试 */
int multiBlobTable117set();
int multiBlobTable117get();
int multiBlobTable117delete();
int multiBlobTable127set();
int multiBlobTable127get();
int multiBlobTable127delete();
int multiBlobTable137set();
int multiBlobTable137get();
int multiBlobTable137delete();
int multiBlobTable147set();
int multiBlobTable147get();
int multiBlobTable147delete();
int multiBlobTable157set();
int multiBlobTable157get();
int multiBlobTable157delete();
int multiBlobTable167set();
int multiBlobTable167get();
int multiBlobTable167delete();
int multiBlobTable227set();
int multiBlobTable227get();
int multiBlobTable227delete();
int multiBlobTable237set();
int multiBlobTable237get();
int multiBlobTable237delete();
int multiBlobTable247set();
int multiBlobTable247get();
int multiBlobTable247delete();
int multiBlobTable257set();
int multiBlobTable257get();
int multiBlobTable257delete();
int multiBlobTable267set();
int multiBlobTable267get();
int multiBlobTable267delete();
int multiBlobTable337set();
int multiBlobTable337get();
int multiBlobTable337delete();
int multiBlobTable347set();
int multiBlobTable347get();
int multiBlobTable347delete();
int multiBlobTable357set();
int multiBlobTable357get();
int multiBlobTable357delete();
int multiBlobTable367set();
int multiBlobTable367get();
int multiBlobTable367delete();
int multiBlobTable447set();
int multiBlobTable447get();
int multiBlobTable447delete();
int multiBlobTable457set();
int multiBlobTable457get();
int multiBlobTable457delete();
int multiBlobTable467set();
int multiBlobTable467get();
int multiBlobTable467delete();
int multiBlobTable557set();
int multiBlobTable557get();
int multiBlobTable557delete();
int multiBlobTable567set();
int multiBlobTable567get();
int multiBlobTable567delete();
int multiBlobTable667set();
int multiBlobTable667get();
int multiBlobTable667delete();

int sameTablemultiBlob227set();
int sameTablemultiBlob227get();
int sameTablemultiBlob227delete();
int sameTablemultiBlob237set();
int sameTablemultiBlob237get();
int sameTablemultiBlob237delete();
int sameTablemultiBlob247set();
int sameTablemultiBlob247get();
int sameTablemultiBlob247delete();
int sameTablemultiBlob257set();
int sameTablemultiBlob257get();
int sameTablemultiBlob257delete();
int sameTablemultiBlob267set();
int sameTablemultiBlob267get();
int sameTablemultiBlob267delete();
int sameTablemultiBlob337set();
int sameTablemultiBlob337get();
int sameTablemultiBlob337delete();
int sameTablemultiBlob347set();
int sameTablemultiBlob347get();
int sameTablemultiBlob347delete();
int sameTablemultiBlob357set();
int sameTablemultiBlob357get();
int sameTablemultiBlob357delete();
int sameTablemultiBlob367set();
int sameTablemultiBlob367get();
int sameTablemultiBlob367delete();
int sameTablemultiBlob447set();
int sameTablemultiBlob447get();
int sameTablemultiBlob447delete();
int sameTablemultiBlob457set();
int sameTablemultiBlob457get();
int sameTablemultiBlob457delete();
int sameTablemultiBlob467set();
int sameTablemultiBlob467get();
int sameTablemultiBlob467delete();
int sameTablemultiBlob557set();
int sameTablemultiBlob557get();
int sameTablemultiBlob557delete();
int sameTablemultiBlob567set();
int sameTablemultiBlob567get();
int sameTablemultiBlob567delete();
int sameTablemultiBlob667set();
int sameTablemultiBlob667get();
int sameTablemultiBlob667delete();

/* 同表测试 */
int sameTablemultiTest222();
int sameTablemultiTest223();
int sameTablemultiTest224();
int sameTablemultiTest225();
int sameTablemultiTest233();
int sameTablemultiTest234();
int sameTablemultiTest235();
int sameTablemultiTest244();
int sameTablemultiTest245();
int sameTablemultiTest255();
int sameTablemultiTest333();
int sameTablemultiTest334();
int sameTablemultiTest335();
int sameTablemultiTest344();
int sameTablemultiTest345();
int sameTablemultiTest355();
int sameTablemultiTest444();
int sameTablemultiTest445();
int sameTablemultiTest455();
int sameTablemultiTest555();

int sameTablemultiJoin226T2();
int sameTablemultiJoin236T2();
int sameTablemultiJoin246T2();
int sameTablemultiJoin256T2();
int sameTablemultiJoin266T2();
int sameTablemultiJoin336T2();
int sameTablemultiJoin346T2();
int sameTablemultiJoin356T2();
int sameTablemultiJoin366T2();
int sameTablemultiJoin446T2();
int sameTablemultiJoin456T2();
int sameTablemultiJoin466T2();
int sameTablemultiJoin556T2();
int sameTablemultiJoin566T2();
int sameTablemultiJoin666T2();

int sameTablemultiJoin226T1();
int sameTablemultiJoin236T1();
int sameTablemultiJoin246T1();
int sameTablemultiJoin256T1();
int sameTablemultiJoin266T1();
int sameTablemultiJoin336T1();
int sameTablemultiJoin346T1();
int sameTablemultiJoin356T1();
int sameTablemultiJoin366T1();
int sameTablemultiJoin446T1();
int sameTablemultiJoin456T1();
int sameTablemultiJoin466T1();
int sameTablemultiJoin556T1();
int sameTablemultiJoin566T1();
int sameTablemultiJoin666T1();



int Sql_AggregateFunThread0(void* arg);
int Sql_AggregateFunThread1(void* arg);
int Sql_AggregateFunThread2(void* arg);
int Sql_AggregateFunThread3(void* arg);


/* =================callback===================== */
void ct_initFlag();
int ct_CallBack(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBack1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackinsert1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectARPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectARPTtable_1C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectARPTtable_2C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateARPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateARPTtable_1C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateARPTtable_1C3(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateARPTtable_1C4(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateARPTtable_2C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectWPTtable_2C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectWPTtable_2C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectWPTtable_2C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_1C3(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_1C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_1C5(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_2C0(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_2C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_2C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_2C3(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_2C4(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateWPTtable_2C5(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectNAVtable_2C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectNAVtable_2C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateNAVtable_1C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackupdateNAVtable_1C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectSIDtable_1C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateSIDtable_1C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectAPCHtable_1C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateAPCHtable_1C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateAPCHtable_1C2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectDEFINE_WPTtable_2C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateDEFINE_WPTtable_1C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectFPLNtable_1C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateFPLNtable_2C(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackUpdateFPLNtable_2C1(void *NotUsed, int argc, char** azColName, char** argv);

int ct_callBackSameTableTestCreate(void *NotUsed, int argc, char** azColName, char** argv);

int ct_callBackJoinTestInsert2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackJoinTestMulti116(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT0(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT2C1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT0EX(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT1EX(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT2EX(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableWPTjoinARPT2C1EX(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackJoinTestSelect(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableARPTjoinSID(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSelectTableFPLNjoinNAV(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackBlobSize0(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackBlobSize1(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackBlobSize2(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackBlobSize3(void *NotUsed, int argc, char** azColName, char** argv);
int ct_callBackSQLGetRows(void *NotUsed, int argc, char** azColName, char** argv);

float ctstring_to_float(char* str);
int ct_testCallBackSqlAggregate1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackAggregate1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackAggregate4(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackAggregate3(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackSqlSubquery1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackSqlOrderBy1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackAggregate2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackSqlAggregate5(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackSqlOrderBy2(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackSqlLimit1(void *NotUsed, int argc, char** azColName, char** argv);
int ct_compare_float(float a, float b, int decimal_places);
int ct_testCallBackAggregate6(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackAggregate5(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackAggregate7(void *NotUsed, int argc, char** azColName, char** argv);
int ct_testCallBackAggregate8(void *NotUsed, int argc, char** azColName, char** argv);

int ct_testCallBackSqlOrderBy3(void *NotUsed, int argc, char** azColName, char** argv);



// 聚集函数接口
int multiSpecialTest();

int sql_multiAggregateFunTestOtherTable1();
int sql_multiAggregateFunTestOtherTable2();
int sql_multiAggregateFunTestOtherTable3();
int sql_multiAggregateFunTesOtherTable4();
int sql_multiAggregateFunTestSameTable1();
int sql_multiAggregateFunTestSameTable2();
int sql_multiAggregateFunTestSameTable3();
int sql_multiAggregateFunTesSameTable4();


#endif // !_CONCURRENCE_H_
