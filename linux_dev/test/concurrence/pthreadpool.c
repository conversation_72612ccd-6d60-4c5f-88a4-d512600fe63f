#include "pthreadpool.h"
#include <stdlib.h>
#include <stdio.h>

/* 创建线程池 */
ThreadPool* threadPoolCreate(int numThreads) {
    ThreadPool* pool = (ThreadPool*)my_malloc(sizeof(ThreadPool));
    int i = 0;
    pool->threads = (pthread_t*)my_malloc(numThreads * sizeof(pthread_t));
    pool->numThreads = numThreads;
    pool->queueFront = 0;
    pool->queueRear = 0;
    pool->rcindex = 0;
    pool->queueSize = 0;
    condVariable_MutexInit(&pool->queueMutex);
    condVariable_Init(&pool->queueMutex);
    pool->taskCounter = 0;
    condVariable_MutexInit(&pool->counterMutex);
    condVariable_Init(&pool->counterMutex);
    pool->destroyFlag = 0;

    /* 创建线程 */
    for (i = 0; i < numThreads; i++) {
        pthread_create(&pool->threads[i], NULL, threadPoolWorker, (void*)pool);
//        int rc = 0;
//        PROCESS_ATTRIBUTE_TYPE USEthread[PTHREAD_MAXCOUNT];
//        PROCESS_ID_TYPE processIdType[PTHREAD_MAXCOUNT];
//        CREATE_PROCESS(USEthread[i], processIdType[i], rc);
//        if(rc != NO_ERROR)
//        {
//
//        }
//        START(processIdType[i], rc)
//        if(rc != NO_ERROR)
//        {
//
//        }
    }

    return pool;
}

/* 销毁线程池 */
void threadPoolDestroy(ThreadPool* pool) {
    /* 取消线程 */
    int i = 0;
    if(pool->destroyFlag == 1)
    {
        return ;
    }
    pool->destroyFlag = 1;

    for (i = 0; i < pool->numThreads; i++) {
        condVariable_Signal(&pool->queueMutex);
    }
    /* 等待线程结束 */
    for (i = 0; i < pool->numThreads; i++) {
        pthread_join(pool->threads[i], NULL);
//        STOP(processIdType[i], rc)
//        if(rc != NO_ERROR)
//        {
//
//        }
    }
    condVariable_Destroy(&pool->queueMutex);
    condVariable_MutexDestroy(&pool->queueMutex);
    condVariable_Destroy(&pool->counterMutex);
    condVariable_MutexDestroy(&pool->counterMutex);
    my_free(pool->threads);
    my_free(pool);
}

/* 线程函数，处理任务队列中的任务 */
void* threadPoolWorker(void* arg) {
    ThreadPool* pool = (ThreadPool*)arg;
    int (*taskFunction)(void* arg) = NULL;
    void* taskArg = NULL;
    int rc = 0;

    while (1) {

        //printf("ThreadPool_LOCKingqueueMutex\n");
        condVariable_MutexLock(&pool->queueMutex);
        //printf("ThreadPool_LOCKENDqueueMutex\n");

        /* 等待任务队列非空 */
        while (pool->queueSize == 0 && (pool->destroyFlag == 0)) {
            condVariable_Wait(&pool->queueMutex);
        }
        if(pool->destroyFlag == 1)
        {
            //printf("ThreadPool_UNLOCKingqueueMutex\n");
            condVariable_MutexUnLock(&pool->queueMutex);
            //printf("ThreadPool_UNLOCKENDqueueMutex\n");
            pthread_exit(0);
//            STOP_SELF();
        }

        taskFunction = pool->taskQueue[pool->queueFront].taskFunction;
        taskArg = pool->taskQueue[pool->queueFront].arg;
        pool->queueFront = (pool->queueFront + 1) % PTHREAD_MAXCOUNT;
        pool->queueSize--;

		/* windows下是否要进行释放信号量 */
        //condVariable_Signal(&pool->queueMutex);
        //printf("ThreadPool_UNLOCKingqueueMutex\n");
        condVariable_MutexUnLock(&pool->queueMutex);
        //printf("ThreadPool_UNLOCKENDqueueMutex\n");

        /* 执行任务函数 */
        rc = taskFunction(taskArg);
        pool->pthrc[pool->rcindex] = rc;
        pool->rcindex++;
        //my_free(taskArg);

        /* 增加任务计数器 */
        condVariable_MutexLock(&pool->counterMutex);
        pool->taskCounter++;
        condVariable_Signal(&pool->counterMutex);
        condVariable_MutexUnLock(&pool->counterMutex);
    }

    return NULL;
}

/* 添加任务到线程池 */
void threadPoolAddTask(ThreadPool* pool,int (*taskFunction)(void* arg), void* arg) {
    //printf("ThreadPool_LOCKingqueueMutex\n");
    condVariable_MutexLock(&pool->queueMutex);
    //printf("ThreadPool_LOCKENDqueueMutex\n");

    /* 等待任务队列有空闲位置 */
    while (pool->queueSize >= PTHREAD_MAXCOUNT) {
        condVariable_Wait(&pool->queueMutex);
    }

    pool->taskQueue[pool->queueRear].taskFunction = taskFunction;
    pool->taskQueue[pool->queueRear].arg = arg;
    pool->queueRear = (pool->queueRear + 1) % PTHREAD_MAXCOUNT;
    pool->queueSize++;

    condVariable_Signal(&pool->queueMutex);
    //printf("ThreadPool_UNLOCKingqueueMutex\n");
    condVariable_MutexUnLock(&pool->queueMutex);
    //printf("ThreadPool_UNLOCKENDqueueMutex\n");
}

/* 等待所有任务完成 */
void threadPoolWaitAllTasks(ThreadPool* pool) {
    condVariable_MutexLock(&pool->counterMutex);

    /* 等待任务计数器为空 */
    while (pool->taskCounter < pool->ct_count) {
        condVariable_Wait(&pool->counterMutex);
        //// printf("pool->taskCounter = %d\n", pool->taskCounter);
    }

    condVariable_MutexUnLock(&pool->counterMutex);
}



