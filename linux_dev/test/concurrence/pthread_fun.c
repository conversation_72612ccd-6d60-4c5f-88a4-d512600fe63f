#include "concurrence.h"

RELEVANCETEST CT_releTest;
ReadWriteLock createTableLock;

/* 创建表的线程函数 */
int createTesttable(void* arg)
{
    int rc = 0;
    rc = TEST_createTable();
    return rc;
}

int createARPTtable(void* arg)
{
	int rc = 0;
	rc = ARPT_createTable();
	return rc;
}

int createWPTtable(void *arg)
{
	int rc = 0;
	rc = WPT_createTable();
	return rc;
}

int createNAVtable(void* arg)
{
	int rc = 0;
	rc = NAV_createTable();
	return rc;
}

int createSIDtable(void* arg)
{
	int rc = 0;
	rc = SID_createTable();
	return rc;
}

int createAPCHtable(void* arg)
{
	int rc = 0;
	rc = APCH_createTable();
	return rc;
}

int createDEFINEWPTtable(void* arg)
{
	int rc = 0;
	rc = DEFINE_WPT_createTable();
	return rc;
}

int createFPLNtable(void* arg)
{
	int rc = 0;
	rc = FPLN_createTable();
	return rc;
}


/* 插入表的线程函数 */
int insertARPTtable(void* arg)
{
	int rc = 0;
    CT_Item item = { 0 };
    item.allRows = 0;
    item.insertRows = CT_ARPTROWS;
	rc = ARPT_insertTable(&item);
	return rc;
}

int insertARPTtable_SameTab(void* arg)
{
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    WriteLock(&CT_releTest.lock);
    item.allRows = CT_releTest.allRows;
    item.insertRows = CT_releTest.insertRows;
    if(CT_releTest.reuse)
    {
        CT_releTest.allRows += CT_releTest.insertRows;
    }
    WriteUnLock(&CT_releTest.lock);
    item.rows = 0;
    rc = ARPT_insertTable(&item);
    ct_p->selectCount[9] += item.rows;
    return rc;
}

int insertWPTtable(void* arg)
{
	int rc = 0;
    CT_Item item = { 0 };
    item.allRows = 0;
    item.insertRows = CT_WPTROWS;
	rc = WPT_insertTable(&item);
	return rc;
}

int insertWPTtable_SameTab(void* arg)
{
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    WriteLock(&CT_releTest.lock);
    item.allRows = CT_releTest.allRows;
    item.insertRows = CT_releTest.insertRows;
    if(CT_releTest.reuse)
    {
        CT_releTest.allRows += CT_releTest.insertRows;
    }
    WriteUnLock(&CT_releTest.lock);
    item.rows = 0;
    rc = WPT_insertTable(&item);
    ct_p->selectCount[9] += item.rows;
    return rc;
}

int insertNAVtable(void* arg)
{
	int rc = 0;
    CT_Item item = { 0 };
    item.allRows = 0;
    item.insertRows = CT_NAVROWS;
	rc = NAV_insertTable(&item);
	return rc;
}

int insertNAVtable_SameTab(void* arg)
{
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    WriteLock(&CT_releTest.lock);
    item.allRows = CT_releTest.allRows;
    item.insertRows = CT_releTest.insertRows;
    if(CT_releTest.reuse)
    {
        CT_releTest.allRows += CT_releTest.insertRows;
    }
    WriteUnLock(&CT_releTest.lock);
    item.rows = 0;
    rc = NAV_insertTable(&item);
    ct_p->rcArray[0] = rc;
    ct_p->selectCount[3] = item.rows;
    return rc;
}

int insertSIDtable(void* arg)
{
	int rc = 0;
    CT_Item item = { 0 };
    item.allRows = 0;
    item.insertRows = CT_SIDROWS;
	rc = SID_insertTable(&item);
	return rc;
}

int insertAPCHtable(void* arg)
{
	int rc = 0;
    CT_Item item = { 0 };
    item.allRows = 0;
    item.insertRows = CT_APCHROWS;
	rc = APCH_insertTable(&item);
	return rc;
}

int insertDEFINEWPTtable(void* arg)
{
	int rc = 0;
    CT_Item item = { 0 };
    item.allRows = 0;
    item.insertRows = CT_DEFINEWPTROWS;
	rc = DEFINE_WPT_insertTable(&item);
	return rc;
}

int insertFPLNtable(void* arg)
{
	int rc = 0;
    CT_Item item = { 0 };
    item.allRows = 0;
    item.insertRows = CT_FPLNROWS;
	rc = FPLN_insertTable(&item);
	return rc;
}

/* 查询表的线程函数 */
int selectARPTtable_1C(void* arg)
{
	int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
	CT_Item item = { 0 };
    CT_tool.rowcount[0] = 0;
	item.callBack = ct_callBackSelectARPTtable_1C;
	item.cond[0] = "ARPT_lon>10.0";
	item.rows = 0;
	
	rc = ARPT_selectTable(&item);
    ct_p->rcArray[0] = rc;
    ct_p->selectCount[0] = item.rows;
	return rc;
}

int selectARPTtable_1C2(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };
    CT_tool.rowcount[2] = 0;
    item.callBack = ct_callBackSelectARPTtable_1C2;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "ARPT_ident>'RSMAM'";
#else
    item.cond[0] = "ARPT_ident>RSMAM";
#endif
    item.rows = 0;

    rc = ARPT_selectTable(&item);
    ct_p->rcArray[0] = rc;
    ct_p->selectCount[2] = item.rows;
    return rc;
}

int selectARPTtable_2C(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };
    CT_tool.rowcount[1] = 0;
    item.callBack = ct_callBackSelectARPTtable_2C;
    item.cond[0] = "ARPT_lat>10.0";
    item.cond[1] = "ARPT_lat<50.0";
    item.rows = 0;

    rc = ARPT_selectTable(&item);
    ct_p->rcArray[1] = rc;
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int selectWPTtable_2C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectWPTtable_2C;
    CT_tool.rowcount[0] = 0;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='VCOED'";
#else
    item.cond[0] = "WPT_ident>=VCOED";
#endif
    item.cond[1] = "WPT_lat<-25.0";
    item.rows = 0;

    rc = WPT_selectTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int selectWPTtable_2C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectWPTtable_2C1;
    CT_tool.rowcount[1] = 0;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='VCOED'";
#else
    item.cond[0] = "WPT_ident>=VCOED";
#endif
    item.cond[1] = "WPT_lat<-25.0";
    item.rows = 0;

    rc = WPT_selectTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int selectWPTtable_2C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectWPTtable_2C2;
    CT_tool.rowcount[2] = 0;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='VCOED'";
#else
    item.cond[0] = "WPT_ident>=VCOED";
#endif
    item.cond[1] = "WPT_lat>-25.0";
    item.rows = 0;

    rc = WPT_selectTable(&item);
    ct_p->selectCount[2] = item.rows;
    return rc;
}

int selectNAVtable_2C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectNAVtable_2C1;
    CT_tool.rowcount[0] = 0;
    item.cond[0] = "NAV_lon>-22.5";
    item.cond[1] = "NAV_lon<22.5";
    item.rows = 0;

    rc = NAV_selectTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int selectNAVtable_2C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectNAVtable_2C2;
    CT_tool.rowcount[1] = 0;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "NAV_ident<='LKWHG'";
#else
    item.cond[0] = "NAV_ident<=LKWHG";
#endif
    item.cond[1] = "NAV_tacan<132.483993";
    item.rows = 0;

    rc = NAV_selectTable(&item);
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int selectSIDtable_1C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectSIDtable_1C1;
    CT_tool.rowcount[1] = 0;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "SID_sidident<='HYUBW'";
#else
    item.cond[0] = "SID_sidident<=HYUBW";
#endif
    item.rows = 0;

    rc = SID_selectTable(&item);

    ct_p->selectCount[1] = item.rows;
    return rc;
}

int selectAPCHtable_1C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectAPCHtable_1C1;
    CT_tool.rowcount[0] = 0;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "APCH_apchident='XZDH'";
#else
    item.cond[0] = "APCH_apchident=XZDH";
#endif
    item.rows = 0;

    rc = APCH_selectTable(&item);

    ct_p->selectCount[0] = item.rows;
    return rc;
}

int selectDEFINE_WPTtable_2C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectDEFINE_WPTtable_2C1;
    CT_tool.rowcount[0] = 0;
    item.cond[0] = "DEFINE_WPT_lon>=45.0";
    item.cond[1] = "DEFINE_WPT_lat<-45.0";
    item.rows = 0;

    rc = DEFINE_WPT_selectTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int selectFPLNtable_1C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.callBack = ct_callBackSelectFPLNtable_1C1;
    CT_tool.rowcount[0] = 0;
    item.cond[0] = "FPLN_fixcat=2";
    item.rows = 0;

    rc = FPLN_selectTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

/*更新表的线程函数*/
int updateARPTtable_1C1(void* arg)
{
    double value = 25.0;
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "ARPT_ident='VLSTG'";
#else
    item.cond[0] = "ARPT_ident=VLSTG";
#endif
    item.rows = 0;
    item.setNum = 1;
    item.fieldName[0] = "ARPT_lon";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = ARPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int updateARPTtable_1C2(void* arg)
{
    double value = 25.0;
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "ARPT_ident>='VLSTG'";
#else
    item.cond[0] = "ARPT_ident>=VLSTG";
#endif
    item.rows = 0;
    item.setNum = 1;

    item.fieldName[0] = "ARPT_length";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = ARPT_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int updateARPTtable_1C3(void* arg)
{
    double value = 25.0;
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "ARPT_ident>='VLSTG'";
#else
    item.cond[0] = "ARPT_ident>=VLSTG";
#endif
    item.rows = 0;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    item.fieldName[0] = "ARPT_elev";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;

    rc = ARPT_updateTable(&item);
    ct_p->selectCount[2] = item.rows;
    ct_p->rcArray[2] = rc;
    return rc;
}

int updateARPTtable_1C4(void* arg)
{
    double value = 125.0;
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    item.cond[0] = "ARPT_lat=22.5";
    item.rows = 0;
    item.setNum = 1;
    item.fieldName[0] = "ARPT_lon";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = ARPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int updateARPTtable_2C(void* arg)
{
    double value = 10.0;
    int rc = 0;
    CT_Item item = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "ARPT_ident<'FGHN'";
#else
    item.cond[0] = "ARPT_ident<FGHN";
#endif
    item.cond[1] = "ARPT_length>1";

    item.rows = 0;
    item.setNum = 1;
    item.fieldName[0] = "ARPT_lon";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = ARPT_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int updateWPTtable_1C1(void* arg)
{
    double value = 25.25;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='YQTGU'";
#else
    item.cond[0] = "WPT_ident>=YQTGU";
#endif
    item.rows = 0;
    item.fieldName[0] = "WPT_lat";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int updateWPTtable_1C2(void* arg)
{
    double value = -25.25;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='YQTGU'";
#else
    item.cond[0] = "WPT_ident>=YQTGU";
#endif
    item.rows = 0;
    item.setNum = 1;

    item.fieldName[0] = "WPT_lon";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int updateWPTtable_1C3(void* arg)
{
    double value = 25.25;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='YQTGU'";
#else
    item.cond[0] = "WPT_ident>=YQTGU";
#endif
    item.rows = 0;
    item.fieldName[0] = "WPT_lat";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[2] = item.rows;
    return rc;
}

int updateWPTtable_1C4(void* arg)
{
    double value = 25.25;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lon>=90.0";
    item.rows = 0;
    item.fieldName[0] = "WPT_lat";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int updateWPTtable_2C0(void* arg)
{
    double value = 25.25;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='YQTGU'";
    item.cond[1] = "WPT_ident=<'ZQTGU'";
#else
    item.cond[0] = "WPT_ident>=YQTGU";
    item.cond[1] = "WPT_ident=<ZQTGU";
#endif
    item.rows = 0;
    item.fieldName[0] = "WPT_lat";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int updateWPTtable_2C1(void* arg)
{
    double value = 25.25;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='DZECO'";
    item.cond[1] = "WPT_ident=<'FXXZR'";
#else
    item.cond[0] = "WPT_ident>=DZECO";
    item.cond[1] = "WPT_ident=<FXXZR";
#endif
    item.rows = 0;
    item.fieldName[0] = "WPT_lat";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int updateWPTtable_2C2(void* arg)
{
    double value = 0.0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='HMSAN'";
    item.cond[1] = "WPT_ident=<'LSUWL'";
#else
    item.cond[0] = "WPT_ident>=HMSAN";
    item.cond[1] = "WPT_ident=<LSUWL";
#endif
    item.rows = 0;
    item.fieldName[0] = "WPT_lon";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int updateWPTtable_2C3(void* arg)
{
    double value = 125.25;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lon>=-25.0";
    item.cond[1] = "WPT_lon=<0.0";
    item.rows = 0;
    item.fieldName[0] = "WPT_lat";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int updateWPTtable_2C4(void* arg)
{
    double value = 10.0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lon>=45.0";
#ifdef SQL_CTTESTMODE
    item.cond[1] = "WPT_ident=<'JDLYH'";
#else
    item.cond[1] = "WPT_ident=<JDLYH";
#endif
    item.rows = 0;
    item.fieldName[0] = "WPT_lat";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int updateWPTtable_2C5(void* arg)
{
    double value = 12.0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lat=<-45.0";
#ifdef SQL_CTTESTMODE
    item.cond[1] = "WPT_ident>='LOPAA'";
#else
    item.cond[1] = "WPT_ident>=LOPAA";
#endif
    item.rows = 0;
    item.fieldName[0] = "WPT_lon";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.setNum = 1;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int updateNAVtable_1C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    double value = 0.000;
    item.cond[0] = "NAV_lat=-45.000000";
    item.rows = 0;
    item.setNum = 1;
    item.fieldName[0] = "NAV_vor";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = NAV_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int updateNAVtable_1C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    double value = 102.0101;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "NAV_ident='LDUTL'";
#else
    item.cond[0] = "NAV_ident=LDUTL";
#endif
    item.rows = 0;
    item.setNum = 1;

    item.fieldName[0] = "NAV_vor";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = NAV_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int updateSIDtable_1C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    double value = 52.000;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "SID_arptident<='BDHKC'";
#else
    item.cond[0] = "SID_arptident<=BDHKC";
#endif
    item.rows = 0;
    item.setNum = 1;

    item.fieldName[0] = "SID_track";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = SID_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int updateAPCHtable_1C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    char* str = "4";
#ifdef SQL_CTTESTMODE
    item.cond[0] = "APCH_type='1'";
#else
    item.cond[0] = "APCH_type=1";
#endif
    item.rows = 0;
    item.setNum = 1;
    item.fieldName[0] = "APCH_fixcat";
    item.type[0] = CT_CHAR;
    item.value[0] = str;
    item.callBack = ct_callBackSQLGetRows;

    rc = APCH_updateTable(&item);
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int updateAPCHtable_1C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    char* str = "4";
#ifdef SQL_CTTESTMODE
    item.cond[0] = "APCH_type='1'";
#else
    item.cond[0] = "APCH_type=1";
#endif
    item.rows = 0;
    item.setNum = 1;
    item.fieldName[0] = "APCH_fixcat";
    item.type[0] = CT_CHAR;
    item.value[0] = str;
    item.callBack = ct_callBackSQLGetRows;

    rc = APCH_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int updateDEFINE_WPTtable_1C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    double value = 10.00;
#ifdef SQL_CTTESTMODE
    item.cond[0] = "DEFINE_WPT_ident='KFUKRA'";
#else
    item.cond[0] = "DEFINE_WPT_ident=KFUKRA";
#endif
    item.rows = 0;
    item.setNum = 1;

    item.fieldName[0] = "DEFINE_WPT_lon";
    item.type[0] = CT_DOUBLE;
    item.value[0] = &value;
    item.callBack = ct_callBackSQLGetRows;

    rc = DEFINE_WPT_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int updateFPLNtable_2C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    char* str = "1";
    item.cond[0] = "FPLN_lon>=0.00";
    item.cond[1] = "FPLN_lon<=25.00";
    item.rows = 0;
    item.setNum = 2;

    item.fieldName[0] = "FPLN_stage1";
    item.fieldName[1] = "FPLN_stage2";
    item.type[0] = CT_CHAR;
    item.value[0] = str;
    item.type[1] = CT_CHAR;
    item.value[1] = str;
    item.callBack = ct_callBackSQLGetRows;

    rc = FPLN_updateTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int updateFPLNtable_2C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    char* str = "1";
    item.cond[0] = "FPLN_lon>=0.00";
    item.cond[1] = "FPLN_lon<=25.00";
    item.rows = 0;
    item.setNum = 2;

    item.fieldName[0] = "FPLN_stage1";
    item.fieldName[1] = "FPLN_stage2";
    item.type[0] = CT_CHAR;
    item.value[0] = str;
    item.type[1] = CT_CHAR;
    item.value[1] = str;
    item.callBack = ct_callBackSQLGetRows;

    rc = FPLN_updateTable(&item);
    ct_p->selectCount[2] = item.rows;
    return rc;
}

/* 删除表的线程函数 */
int deleteARPTtable_1C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "ARPT_lat<-45";
    item.rows = 0;
    ct_p->selectCount[0] = 0;
    item.callBack = ct_callBackSQLGetRows;
    rc = ARPT_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[2] = rc;
    return rc;
}

int deleteARPTtable_1C4(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "ARPT_lat<-45";
    item.rows = 0;
    ct_p->selectCount[0] = 0;
    item.callBack = ct_callBackSQLGetRows;
    rc = ARPT_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int deleteARPTtable_1C3(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "ARPT_ident<'BEADE'";
#else
    item.cond[0] = "ARPT_ident<BEADE";
#endif
    item.rows = 0;
    ct_p->selectCount[0] = 0;
    item.callBack = ct_callBackSQLGetRows;
    rc = ARPT_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;

    ct_p->rcArray[0] = rc;
    return rc;
}

int deleteARPTtable_1C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "ARPT_lat<-45";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = ARPT_deleteTable(&item);
    ct_p->selectCount[2] = item.rows;
    ct_p->rcArray[2] = rc;
    return rc;
}

int deleteARPTtable_1C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "ARPT_lon<-45";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = ARPT_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int deleteWPTtable_1C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lon<-22.5";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int deleteWPTtable_1C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lat<-22.5";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int deleteWPTtable_2C0(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lon>0.0";
    item.cond[1] = "WPT_lon<22.5";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int deleteWPTtable_2C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='YQTGU'";
    item.cond[1] = "WPT_ident=<'ZQTGU'";
#else
    item.cond[0] = "WPT_ident>=YQTGU";
    item.cond[1] = "WPT_ident=<ZQTGU";
#endif
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int deleteWPTtable_2C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_ident>=DZECO";
    item.cond[1] = "WPT_ident=<FXXZR";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int deleteWPTtable_2C3(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "WPT_ident>='HMSAN'";
    item.cond[1] = "WPT_ident=<'LSUWL'";
#else
    item.cond[0] = "WPT_ident>=HMSAN";
    item.cond[1] = "WPT_ident=<LSUWL";
#endif
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int deleteWPTtable_2C4(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lon>=-25.0";
    item.cond[1] = "WPT_lon=<0.0";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int deleteWPTtable_2C5(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "WPT_lon>=45.0";
#ifdef SQL_CTTESTMODE
    item.cond[1] = "WPT_ident=<'JDLYH'";
#else
    item.cond[1] = "WPT_ident=<JDLYH";
#endif
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = WPT_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int deleteNAVtable_1C1(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "NAV_ident<'XFQTZ'";
#else
    item.cond[0] = "NAV_ident<XFQTZ";
#endif
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;
    rc = NAV_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int deleteNAVtable_1C2(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "NAV_lat>125.0";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = NAV_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int deleteSIDtable_1C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "SID_type='2'";
#else
    item.cond[0] = "SID_type=2";
#endif
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = SID_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int deleteAPCHtable_2C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "APCH_lon<-25.0";
    item.cond[1] = "APCH_lat<-25.0";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = APCH_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int deleteDEFINE_WPTtable_2C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
    item.cond[0] = "DEFINE_WPT_lon<-25.0";
    item.cond[1] = "DEFINE_WPT_lat<-25.0";
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = DEFINE_WPT_deleteTable(&item);
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int deleteFPLNtable_1C(void* arg)
{
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    int rc = 0;
    CT_Item item = { 0 };
#ifdef SQL_CTTESTMODE
    item.cond[0] = "FPLN_ident>'SLMWREZVQG'";
#else
    item.cond[0] = "FPLN_ident>SLMWREZVQG";
#endif
    item.rows = 0;
    item.callBack = ct_callBackSQLGetRows;

    rc = FPLN_deleteTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

/*销毁表的线程函数*/
int dropARPTtable(void* arg)
{
    int rc = 0;
    rc = ARPT_dropTable();
    return rc;
}

int dropWPTtable(void* arg)
{
    int rc = 0;
    rc = WPT_dropTable();
    return rc;
}

int dropNAVtable(void* arg)
{
    int rc = 0;
    rc = NAV_dropTable();
    return rc;
}

int dropSIDtable(void* arg)
{
    int rc = 0;
    rc = SID_dropTable();
    return rc;
}

int dropAPCHtable(void* arg)
{
    int rc = 0;
    rc = APCH_dropTable();
    return rc;
}

int dropDEFINEWPTtable(void* arg)
{
    int rc = 0;
    rc = DEFINE_WPT_dropTable();
    return rc;
}

int dropFPLNtable(void* arg)
{
    int rc = 0;
    rc = FPLN_dropTable();
    return rc;
}

/* 连接查询线程函数 */

int selectJoinTable(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };
    item.jointool = ct_p->jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    ct_p->selectCount[0] = item.rows;
    return rc;
}

int multiSelectJoinTable(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };
    item.jointool = ct_p->jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    return rc;
}

int selectTableWPTjoinARPT0(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[0] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT0;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int selectTableWPTjoinARPT1(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lat<ARPT_lat";
    jointool.cond[1] = "WPT_lon=ARPT_lon";
    CT_tool.rowcount[1] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT1;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int selectTableWPTjoinARPT2(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_lon<ARPT_lon";
    jointool.cond[1] = "WPT_lat=ARPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT2;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[2] = item.rows;
    return rc;
}

int selectTableWPTjoinARPT2C1(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName1, "WPT");
    strcpy(jointool.tableName2, "ARPT");
    jointool.cond[0] = "WPT_ident<ARPT_ident";
    jointool.cond[1] = "WPT_lon>ARPT_lon";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT2C1;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[2] = item.rows;
    ct_p->rcArray[2] = rc;
    return rc;
}

int selectTableWPTjoinARPT0EX(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName2, "WPT");
    strcpy(jointool.tableName1, "ARPT");
    jointool.cond[0] = "ARPT_lon<WPT_lon";
    jointool.cond[1] = "ARPT_lat=WPT_lat";
    CT_tool.rowcount[0] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT0EX;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

int selectTableWPTjoinARPT1EX(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName2, "WPT");
    strcpy(jointool.tableName1, "ARPT");
    jointool.cond[0] = "ARPT_lat<WPT_lat";
    jointool.cond[1] = "ARPT_lon=WPT_lon";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT1EX;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[1] = item.rows;
    return rc;
}

int selectTableWPTjoinARPT2EX(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName2, "WPT");
    strcpy(jointool.tableName1, "ARPT");
    jointool.cond[0] = "ARPT_lon<WPT_lon";
    jointool.cond[1] = "ARPT_lat=WPT_lat";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT2EX;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[2] = item.rows;
    return rc;
}

int selectTableWPTjoinARPT2C1EX(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName2, "WPT");
    strcpy(jointool.tableName1, "ARPT");
    jointool.cond[0] = "ARPT_ident<WPT_ident";
    jointool.cond[1] = "ARPT_lon>WPT_lon";
    CT_tool.rowcount[2] = 0;
    jointool.callBack = ct_callBackSelectTableWPTjoinARPT2C1EX;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[2] = item.rows;
    ct_p->rcArray[2] = rc;
    return rc;
}

int selectTableARPTjoinSID(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName1, "ARPT");
    strcpy(jointool.tableName2, "SID");
    jointool.cond[0] = "ARPT_lon=SID_lon";
    CT_tool.rowcount[1] = 0;
    jointool.callBack = ct_callBackSelectTableARPTjoinSID;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[1] = item.rows;
    ct_p->rcArray[1] = rc;
    return rc;
}

int selectTableFPLNjoinNAV(void* arg)
{
    int rc = 0;
    JOINTOOL jointool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    CT_Item item = { 0 };

    strcpy(jointool.tableName1, "FPLN");
    strcpy(jointool.tableName2, "NAV");
    jointool.cond[0] = "FPLN_lon=NAV_lon";
    CT_tool.rowcount[0] = 0;
    jointool.callBack = ct_callBackSelectTableFPLNjoinNAV;
    item.jointool = &jointool;
    item.rows = 0;

    rc = Join_selectTable(&item);
    item.jointool->select = item.rows;
    ct_p->selectCount[0] = item.rows;
    ct_p->rcArray[0] = rc;
    return rc;
}

/* BLOB的线程函数 */

int setWPTTableBlob(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;

    rc = WPT_setBlob(ct_p->blobtool);
    ct_p->rcArray[9] = rc;
    return rc;
}

int getWPTTableBlob(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;

    rc = WPT_getBlob(ct_p->blobtool);
    ct_p->rcArray[9] = rc;
    return rc;
}

int deleteWPTTableBlob(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;

    rc = WPT_deleteBlob(ct_p->blobtool);
    ct_p->rcArray[9] = rc;
    return rc;
}

int setWPTTableBlob_0(void* arg)
{
    int rc = 0;
    FILE* fp = NULL;
    char path[128] = { 0 };
    BLOBTOOL blobTool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    ct_p->blobtool = &blobTool;
    sprintf(path, "%s%s", blobPath, blobfile2);
    fp = fopen(path, "r");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "QJTUJ";

    rc = WPT_setBlob(&blobTool);
    if(rc)
    {
        return rc;
    }

    my_free(blobTool.buf);

    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize0, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(CT_tool.rowcount[0] != blobTool.size)
    {
        return -1;
    }
    return rc;
}

int getWPTTableBlob_0(void* arg)
{
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT0.png";
    bool flag = false;
    FILE* fp = NULL;
    char path[128] = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    BLOBTOOL blobTool = {0};
    CT_tool.rowcount[0] = 0;
    ct_p->blobtool = &blobTool;
    sprintf(rootPath, "%s%s", blobPath, blobfile2);
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize0, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");
    if(rc )
    {
        return rc;
    }
    blobTool.size = CT_tool.rowcount[0];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        return -1;
    }
    blobTool.fieldValue = "QJTUJ";

    rc = WPT_getBlob(&blobTool);
    if(rc)
    {
        return rc;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    flag = ct_CompareFiles(rootPath, path);
    if(!flag)
    {
        return -1;
    }

    return rc;
}

int deleteWPTTableBlob_0(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    BLOBTOOL blobTool = {0};
    ct_p->blobtool = &blobTool;
    blobTool.fieldValue = "QJTUJ";

    rc = WPT_deleteBlob(&blobTool);
    if(rc)
    {
        return rc;
    }
    CT_tool.rowcount[0] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize0, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=QJTUJ");

    if(CT_tool.rowcount[0] != 0)
    {
        return -1;
    }

    return rc;
}

int setWPTTableBlob_1(void* arg)
{
    int rc = 0;
    FILE* fp = NULL;
    char path[128] = { 0 };
    BLOBTOOL blobTool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    ct_p->blobtool = &blobTool;
    sprintf(path, "%s%s", blobPath, blobfile3);
    fp = fopen(path, "r");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "FTHLF";

    rc = WPT_setBlob(&blobTool);
    if(rc)
    {
        return rc;
    }

    my_free(blobTool.buf);

    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=FTHLF");
    if(CT_tool.rowcount[1] != blobTool.size)
    {
        return -1;
    }
    return rc;
}

int getWPTTableBlob_1(void* arg)
{
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT1.mp3";
    bool flag = false;
    FILE* fp = NULL;
    char path[128] = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    BLOBTOOL blobTool = {0};
    CT_tool.rowcount[1] = 0;
    ct_p->blobtool = &blobTool;
    sprintf(rootPath, "%s%s", blobPath, blobfile3);
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=FTHLF");
    if(rc )
    {
        return rc;
    }
    blobTool.size = CT_tool.rowcount[1];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        return -1;
    }
    blobTool.fieldValue = "FTHLF";

    rc = WPT_getBlob(&blobTool);
    if(rc)
    {
        return rc;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    flag = ct_CompareFiles(rootPath, path);
    if(!flag)
    {
        return -1;
    }

    return rc;
}

int deleteWPTTableBlob_1(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    BLOBTOOL blobTool = {0};
    ct_p->blobtool = &blobTool;
    blobTool.fieldValue = "FTHLF";

    rc = WPT_deleteBlob(&blobTool);
    if(rc)
    {
        return rc;
    }
    CT_tool.rowcount[1] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize1, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=FTHLF");

    if(CT_tool.rowcount[1] != 0)
    {
        return -1;
    }

    return rc;
}

int setWPTTableBlob_2(void* arg)
{
    int rc = 0;
    FILE* fp = NULL;
    char path[128] = { 0 };
    BLOBTOOL blobTool = {0};
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    ct_p->blobtool = &blobTool;
    sprintf(path, "%s%s", blobPath, blobfile4);
    fp = fopen(path, "r");
    fseek(fp, 0, SEEK_END);
    blobTool.size = ftell(fp);
    rewind(fp);
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        return -1;
    }
    fread(blobTool.buf, blobTool.size, 1, fp);
    blobTool.fieldValue = "SCXEL";

    rc = WPT_setBlob(&blobTool);
    if(rc)
    {
        return rc;
    }

    my_free(blobTool.buf);

    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=SCXEL");
    if(CT_tool.rowcount[2] != blobTool.size)
    {
        return -1;
    }
    return rc;
}

int getWPTTableBlob_2(void* arg)
{
    int rc = 0;
    char rootPath[128] = { 0 };
    char fileName[] = "blobT2.mp4";
    bool flag = false;
    FILE* fp = NULL;
    char path[128] = { 0 };
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    BLOBTOOL blobTool = {0};
    CT_tool.rowcount[2] = 0;
    ct_p->blobtool = &blobTool;
    sprintf(rootPath, "%s%s", blobPath, blobfile4);
    rc = GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=SCXEL");
    if(rc )
    {
        return rc;
    }
    blobTool.size = CT_tool.rowcount[2];
    sprintf(path, "%s%s", savePath, fileName);
    fp = fopen(path, "wb");
    blobTool.buf = my_malloc(blobTool.size);
    if(blobTool.buf == NULL)
    {
        fclose(fp);
        return -1;
    }
    blobTool.fieldValue = "SCXEL";

    rc = WPT_getBlob(&blobTool);
    if(rc)
    {
        return rc;
    }

    fwrite(blobTool.buf, blobTool.size, 1, fp);
    fclose(fp);
    my_free(blobTool.buf);
    flag = ct_CompareFiles(rootPath, path);
    if(!flag)
    {
        return -1;
    }

    return rc;
}

int deleteWPTTableBlob_2(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    BLOBTOOL blobTool = {0};
    ct_p->blobtool = &blobTool;
    blobTool.fieldValue = "SCXEL";

    rc = WPT_deleteBlob(&blobTool);
    if(rc)
    {
        return rc;
    }
    CT_tool.rowcount[2] = 0;
    GNCDB_select(ct_Global.db, testCallBackBlobSize2, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=SCXEL");

    if(CT_tool.rowcount[2] != 0)
    {
        return -1;
    }

    return rc;
}


int Sql_AggregateFunThread0(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    rc = GNCDB_exec(ct_Global.db, ct_p->SQL[0], ct_p->callBack[0], &ct_p->selectCounts[0], NULL);
    ct_p->rcArray[0] = rc;
    return rc;
}

int Sql_AggregateFunThread1(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    rc = GNCDB_exec(ct_Global.db, ct_p->SQL[1], ct_p->callBack[1], &ct_p->selectCounts[1], NULL);
    ct_p->rcArray[1] = rc;
    return rc;
}

int Sql_AggregateFunThread2(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    rc = GNCDB_exec(ct_Global.db, ct_p->SQL[2], ct_p->callBack[2], &ct_p->selectCounts[2], NULL);
    ct_p->rcArray[2] = rc;
    return rc;
}

int Sql_AggregateFunThread3(void* arg)
{
    int rc = 0;
    CT_Pthread * ct_p = (CT_Pthread *)arg;
    rc = GNCDB_exec(ct_Global.db, ct_p->SQL[3], ct_p->callBack[3], &ct_p->selectCounts[3], NULL);
    ct_p->rcArray[3] = rc;
    return rc;
}
