#include "demo.h"

#define MAX_FILE_SIZE 1000000  // 限制图片大小为1MB


char filePath[60] = "./testfile/datafile/";

char FileName1[30] = "ARPT.txt";
char FileName2[30] = "WPT.txt";
char FileName3[30] = "NAV.txt";
char FileName4[30] = "SID.txt";
char FileName5[30] = "APCH.txt";
char FileName6[30] = "DEFINE_WPT.txt";
char FileName7[30] = "FPLN.txt";

ST_ARPT_ROW_INTERNAL        g_st_arpt[10];
ST_SID_ROW_INTERNAL         g_st_sid[10];
ST_WPT_ROW_INTERNAL         g_st_wpt[10];
ST_FPLN_ROW_INTERNAL		g_st_fpln[10];
ST_DEFINE_WPT_ROW_INTERNAL	g_st_define_wpt[10];
ST_APCH_ROW_INTERNAL		g_st_apch[10];
ST_NAV_ROW_INTERNAL			g_st_nav[10];

int demo_createdb(GNCDB** db, char* filename);
int demo_createtable(GNCDB* db);
int demo_insert(GNCDB* db);
int demo_delete(GNCDB* db);
int demo_update(GNCDB* db);
int demo_select(GNCDB* db);
int demo_droptable(GNCDB* db);
int demo_closedb(GNCDB* db);
int demoCallBack(void *NotUsed, int argc, char** azColName, char** argv);
int demoCallBackJoin(void *NotUsed, int argc, char** azColName, char** argv);
int demoCallBackMuti(void *NotUsed, int argc, char** azColName, char** argv);
int loadWPTData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize);
int loadARPTData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize);
int loadSIDData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize);
int loadNAVData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize);
int loadAPCHData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize);
int loadDEFINE_WPTData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize);
int loadFPLNData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize);
int demoBlob(struct GNCDB* db);

int demo_muti(struct GNCDB* db);

int demo()
{
    int rc = 0;
    char* filename = "GNCDB.dat";
    GNCDB* db = NULL;
    remove(filename);
    remove("log_GNCDB.dat");
    // printf("                    The DEMO FOR THE DB %s               \n\n", filename);
    rc = demo_createdb(&db, filename);       // 打开数据库文件
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = demo_createtable(db);       // 建表 
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    demo_insert(db);
    demoBlob(db);
    demo_select(db);
    demo_delete(db);
    demo_update(db);
    //demo_droptable(db);

    /* councurrence */
    demo_muti(db);

    demo_closedb(db);
    
    return 0;
}

int outRowCount = 0;

void initOutput()
{
    outRowCount = 0;
}

int demoCallBack(void *NotUsed, int argc, char** azColName, char** argv) 
{
    int i = 0;
    if (outRowCount == 0)
    {
        //printf("查询出的记录：\n");
        printf("\n");
        printf("|");
        for (i = 0; i < argc - 3; i++)
        {
            printf("%16s|", azColName[i]);
        }
        printf("\n");
        for (i = 0; i <= (argc - 3) * 17; ++i)
        {
            printf("-");
        }
        printf("\n");
    }

    printf("|");
    for (i = 0; i < argc - 3; i++)
    {
        printf("%16s|", argv[i]);
    }
    printf("\n");

    outRowCount++;

    return 0;
}

int demoCallBackJoin(void *NotUsed, int argc, char** azColName, char** argv)
{
    int i = 0;
    int index[7] = {0};
    int j = 0;
    int num = argc;
    if (outRowCount == 0)
    {
        //printf("查询出的记录：\n");
        printf("\n");
        printf("|");
        for (i = 0; i < argc; i++)
        {
            if(strcmp(azColName[i], "rowId") == 0 || strcmp(azColName[i], "createTime") == 0 || strcmp(azColName[i], "updateTime") == 0)
            {
                index[j] = i;
                j ++;
                num --;
                continue;
            }
            printf("%16s|", azColName[i]);
        }
        printf("\n");
        for (i = 0; i <= num * 17; ++i)
        {
            printf("-");
        }
        printf("\n");
    }

    printf("|");
    j = 0;
    for (i = 0; i < argc; i++)
    {
        if(i == index[j])
        {
            j ++;
            continue;
        }
        printf("%16s|", argv[i]);
    }
    printf("\n");

    outRowCount++;

    return 0;
}

int demoCallBackMuti(void *NotUsed, int argc, char** azColName, char** argv)
{
    //int i = 0;
    pthread_t id = 0;
//    if (outRowCount == 0)
//    {
//        //printf("查询出的记录：\n");
//        printf("\n");
//        printf("|");
//        for (i = 0; i < columNum - 3; i++)
//        {
//            printf("%16s|", fieldName[i]);
//        }
//        printf("\n");
//        for (i = 0; i <= (columNum - 3) * 17; ++i)
//        {
//            printf("-");
//        }
//        printf("\n");
//    }
//
//    printf("|");
//    for (i = 0; i < columNum - 3; i++)
//    {
//        printf("%16s|", fieldValue[i]);
//    }
    id = pthread_self();
    printf("    thid = %ld", id);
    //printf("\n");
    fflush(stdout);
    outRowCount++;

    return 0;
}

int demo_createdb(GNCDB** db, char* filename)
{
    int rc = 0;
    printf("/**************** Demo 1 : 创建数据库 ****************/\n");
    rc = GNCDB_open(db, filename, 0, 0);
    if (rc == GNCDB_SUCCESS)
    {
        printf("数据库创建成功\n\n");
    }
    else
    {
        printf("数据库创建失败，状态码%d\n", rc);
    }
    return 0;
}

int demo_createtable(GNCDB* db)
{
    double min = -2147483647.0;
    double max = 2147483647.0;
    int rc = 0;
    printf("/**************** Demo 2 : 创建表 ****************/\n");

    // printf("\n正在创建 WPT 表...\n");
    rc = GNCDB_createTable(db, "WPT", 4,
        "WPT_ident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0,
        "WPT_lon", FIELDTYPE_REAL, 0, 0, min, max,
        "WPT_lat", FIELDTYPE_REAL, 0, 0, min, max,
        "WPT_blob", FIELDTYPE_BLOB, 0, 0, min, max,
        M_MAX_ROWS);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // printf("\n正在创建 ARPT 表...\n");
    rc = GNCDB_createTable(db, "ARPT", 6,
        "ARPT_ident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0,
        "ARPT_lon", FIELDTYPE_REAL, 0, 0, min, max,
        "ARPT_lat", FIELDTYPE_REAL, 0, 0, min, max,
        "ARPT_elev", FIELDTYPE_REAL, 0, 0, min, max,
        "ARPT_length", FIELDTYPE_REAL, 0, 0, min, max,
        "ARPT_mag_var", FIELDTYPE_REAL, 0, 0, min, max,
        M_MAX_ROWS);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // printf("\n正在创建 SID 表...\n");
    rc = GNCDB_createTable(db, "SID", 11,
        "SID_arptident", FIELDTYPE_VARCHAR, 0, 0, min, 8.0,
        "SID_sidident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0,
        "SID_no", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "SID_type", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "SID_track", FIELDTYPE_REAL, 0, 0, min, max,
        "SID_fix", FIELDTYPE_VARCHAR, 0, 0, min, 8.0,
        "SID_cat", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "SID_lon", FIELDTYPE_REAL, 0, 0, min, max,
        "SID_lat", FIELDTYPE_REAL, 0, 0, min, max,
        "SID_rnp", FIELDTYPE_REAL, 0, 0, min, max,
        "SID_alt", FIELDTYPE_REAL, 0, 0, min, max,
        M_MAX_ROWS);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // printf("\n正在创建 NAV 表...\n");
    rc = GNCDB_createTable(db, "NAV", 8, 
        "NAV_ident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0,
        "NAV_type", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "NAV_lon", FIELDTYPE_REAL, 0, 0, min, max,
        "NAV_lat", FIELDTYPE_REAL, 0, 0, min, max,
        "NAV_vor", FIELDTYPE_REAL, 0, 0, min, max,
        "NAV_dme", FIELDTYPE_REAL, 0, 0, min, max,
        "NAV_tacan", FIELDTYPE_REAL, 0, 0, min, max,
        "NAV_ndb", FIELDTYPE_REAL, 0, 0, min, max,
        M_MAX_ROWS);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // printf("\n正在创建 APCH 表...\n");
    rc = GNCDB_createTable(db, "APCH", 12,
        "APCH_arptident", FIELDTYPE_VARCHAR, 0, 0, min, 8.0,
        "APCH_apchident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0, 
        "APCH_no", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "APCH_type", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "APCH_track", FIELDTYPE_REAL, 0, 0, min, max,
        "APCH_fixident", FIELDTYPE_VARCHAR, 0, 0, min, 8.0,
        "APCH_fixcat", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "APCH_stage", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "APCH_lon", FIELDTYPE_REAL, 0, 0, min, max,
        "APCH_lat", FIELDTYPE_REAL, 0, 0, min, max,
        "APCH_rnp", FIELDTYPE_REAL, 0, 0, min, max,
        "APCH_alt", FIELDTYPE_REAL, 0, 0, min, max,
        M_MAX_ROWS);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // printf("\n正在创建 DEFINE_WPT 表...\n");
    rc = GNCDB_createTable(db, "DEFINE_WPT", 3,
        "DEFINE_WPT_ident", FIELDTYPE_VARCHAR, 0, 1, min, 8.0,
        "DEFINE_WPT_lon", FIELDTYPE_REAL, 0, 0, min, max,
        "DEFINE_WPT_lat", FIELDTYPE_REAL, 0, 0, min, max,
        M_MAX_ROWS);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // printf("\n正在创建 FPLN 表...\n");
    rc = GNCDB_createTable(db, "FPLN", 13,
        "FPLN_eng", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "FPLN_ident", FIELDTYPE_VARCHAR, 0, 1, min, 16.0,
        "FPLN_no", FIELDTYPE_VARCHAR, 0, 0, min, 3.0,
        "FPLN_type", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "FPLN_track", FIELDTYPE_REAL, 0, 0, min, max,
        "FPLN_fixident", FIELDTYPE_VARCHAR, 0, 0, min, 8.0,
        "FPLN_fixcat", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "FPLN_stage1", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "FPLN_stage2", FIELDTYPE_VARCHAR, 0, 0, min, 2.0,
        "FPLN_lon", FIELDTYPE_REAL, 0, 0, min, max,
        "FPLN_lat", FIELDTYPE_REAL, 0, 0, min, max,
        "FPLN_rnp", FIELDTYPE_REAL, 0, 0, min, max,
        "FPLN_alt", FIELDTYPE_REAL, 0, 0, min, max,
        M_MAX_ROWS);

    
    return 0;
}

int demo_insert(GNCDB* db)
{
    int rc = 0;
    //int rows = 0;
    printf("/**************** Demo 3 : 插入数据 ****************/\n");

    printf("插入 ARPT 表:\n");
    loadARPTData(db, FileName1, M_DATA_SIZE);
    //rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 0, "ARPT");
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    printf("插入 WPT 表:\n");
    loadWPTData(db, FileName2, M_DATA_SIZE);
    //rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 0, "WPT");
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /*BYTE* bufOfGet = (BYTE*)my_malloc(100);
    if (bufOfGet == NULL) {
        return -1;
    }
    rc = GNCDB_getBlob(db, "WPT", 5, bufOfGet, 100, 1, "0");
    for (int j = 0; j < 100; j++) {
        // printf("%c", bufOfGet[j]);
    }*/

    printf("插入 NAV 表:\n");
    loadNAVData(db, FileName3, M_DATA_SIZE);
    //rows = 0;
    //rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 0, "NAV");        /* 遍历 */
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    
    printf("插入 SID 表:\n");
    loadSIDData(db, FileName4, M_DATA_SIZE);
    //rows = 0;
    //rc = GNCDB_select(db, demoCallBack,&rows, NULL, 1, 0, 0, "SID");        /* 遍历 */
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    
    printf("插入 APCH 表:\n");
    loadAPCHData(db, FileName5, M_DATA_SIZE);
    //rows = 0;
    //rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 0, "APCH");
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    
    printf("插入 DEFINE_WPT 表:\n");
    loadDEFINE_WPTData(db, FileName6, M_DATA_SIZE);
    //rows = 0;
    //rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 0, "DEFINE_WPT");
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    
    printf("插入 FPLN 表:\n");
    loadFPLNData(db, FileName7, M_DATA_SIZE);
    //rows = 0;
    //rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 0, "FPLN");
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    return 0;
}

int demo_delete(GNCDB* db)
{
    int rows = 0;
    printf("/**************** Demo 6 : 删除数据 ****************/\n");

    // printf("\n示例 1 ：删除 ARPT 表中 ARPT_ident = RBKWO 的数据\n");
   
    printf("删除前 %s:\n", "ARPT");
    initOutput();
    GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident=RBKWO");                /* 遍历 */

    rows = 0;
    GNCDB_delete(db, &rows, "ARPT", 1, "ARPT_ident=RBKWO");                 /* 删除 */
    // printf("All delete rows : %d \n", rows);

    printf("删除后 %s:\n", "ARPT");
    initOutput();
    rows = 0;
    GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident=RBKWO");                 /* 遍历 */

    // printf("\n示例 2 ：删除 FPLN 表中 FPLN_ident = HOTAZGVTCS 之间的 FPLN_ident = HYWWEWMQAA 的数据\n");

    printf("删除前 %s:\n", "FPLN");
    initOutput();
    rows = 0;
    GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 2, "FPLN", "FPLN_ident>=HOTAZGVTCS", "FPLN_ident<=HYWWEWMQAA");                /* 遍历 */

    rows = 0;
    GNCDB_delete(db, &rows, "FPLN", 2, "FPLN_ident>HOTAZGVTCS", "FPLN_ident<HYWWEWMQAA");                 /* 删除 */
    // printf("All delete rows : %d \n", rows);

    printf("删除后 %s:\n", "FPLN");
    initOutput();
    rows = 0;
    GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 2, "FPLN", "FPLN_ident>=HOTAZGVTCS", "FPLN_ident<=HYWWEWMQAA");                /* 遍历 */

    return 0;
}

int demo_update(GNCDB* db)
{
    int rows = 0;
    printf("/**************** Demo 7 : 更新数据 ****************/\n");
    
    printf("\n示例 1 ：更新 SID 表中 SID_lon = 90.000000 SID_arptident为 XIANBEI \n");
    printf("更新前 %s:\n", "SID");
    initOutput();
    rows = 0;
    GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "SID", "SID_lon=90.000000");                      /* 遍历 */

    rows = 0;
    GNCDB_update(db, &rows, "SID", 1, 1, "SID_arptident", "XIANBEI", "SID_lon=90.000000");
    // printf("All update rows : %d\n", rows);

    printf("更新后:\n");
    initOutput();
    rows = 0;
    GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "SID", "SID_lon=90.000000");                      /* 遍历 */

    return 0;
}

int demo_select(GNCDB* db)
{
    int rc = 0;
    int rows = 0;
    printf("/**************** Demo 5 : 查询数据 ****************/\n");

    printf("示例1 ：在 ARPT 表中查询 IDENT = KDWTO 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident=KDWTO");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("ARPT is fail\n ");
    }
    // todo 
    //printf("output = ");
    printf("\n示例2 ：在 WPT 表中查询 IDENT = JABRE 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident=JABRE");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("WPT is fail\n ");
    }

    printf("\n示例3 ：在 NAV 表中查询 IDENT = EZMRR 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "NAV", "NAV_ident=EZMRR");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("NAV is fail\n ");
    }

    printf("\n示例4 ：在 DEFNIE_WPT 表中查询 IDENT = FKUYQA 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "DEFINE_WPT", "DEFINE_WPT_ident=FKUYQA");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("DEFNIE_WPT is fail\n ");
    }

    printf("\n示例5 ：在 FPLN 表中查询 CHS_ENG = 1 IDENT = KUHBOOIMND 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 2, "FPLN", "FPLN_eng=1", "FPLN_ident=KUHBOOIMND");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("FPLN is fail\n ");
    }

    printf("\n示例6 ：在 SID 表中查询 ARPT_IDENT = FNBTB 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "SID", "SID_arptident=FNBTB");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("SID is fail\n ");
    }
    
    printf("\n示例7 ：在 APCH 表中查询 ARPT_IDENT = PIMX 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 1, "APCH", "APCH_arptident=PIMX");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("APCH is fail\n ");
    }

    printf("\n示例8 ：在 SID 表中查询 ARPT_IDENT = EIRAF SID_IDENT = LNPDKY 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 2, "SID", "SID_arptident=EIRAF", "SID_sidident=LNPDKY");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("SID is fail\n ");
    }

    printf("\n示例9 ：在 APCH 表中查询 ARPT_IDENT = UOUQ APCH_IDENT = POGB 的数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 2, "APCH", "APCH_arptident=UOUQ", "APCH_apchident=POGB");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("APCH is fail\n ");
    }

    printf("\n示例10 ：在 ARPT 中当前位置(50.0, 20.0)经纬范围20内查询符合要求的机场数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 4, "ARPT", "ARPT_lon>30", "ARPT_lon<70", "ARPT_lat>0", "ARPT_lat<40");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("ARPT is fail\n ");
    }

    printf("\n示例11 ：在 NAV 中当前位置(50.0, 40.0)经纬范围20内查询符合要求的导航台数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 4, "NAV", "NAV_lon>30", "NAV_lon<70", "NAV_lat>20", "NAV_lat<60");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("NAV is fail\n ");
    }

    printf("\n示例12 ：在 WPT 中当前位置(50.0, 40.0)经纬范围20内查询符合要求的航路点数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 4, "WPT", "WPT_lon>30", "WPT_lon<70", "WPT_lat>20", "WPT_lat<60");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("WPT is fail\n ");
    }

    printf("\n示例13 ：在 DEFINE_WPT 中当前位置(50.0, 40.0)经纬范围20内查询符合要求的用户航路点数据 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBack, &rows, NULL, 1, 0, 4, "DEFINE_WPT", "DEFINE_WPT_lon>50", "DEFINE_WPT_lon<70", "DEFINE_WPT_lat>40", "DEFINE_WPT_lat<60");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("DEFINE_WPT is fail\n ");
    }

    /* 双表连接 */

    printf("\n示例14 ：通过 ARPT_IDENT 连接 ARPT 表和 SID 表 \n");
    initOutput();
    rows = 0;
    rc = GNCDB_select(db, demoCallBackJoin, &rows, NULL, 2, 0, 2, "ARPT", "SID", "ARPT_ident=HILDY", "ARPT_ident=SID_arptident");
    if (rc != GNCDB_SUCCESS)
    {
        // printf("ARPT join is fail\n ");
    }

    return 0;
}

int demo_droptable(GNCDB* db)
{
    // char* tablename = "SID";
    int row = 0;
    // printf("/****************Demo 8:drop table--start****************/\n");


    // printf("Before drop the table %s:\n", tablename);

    //GNCDB_delete(db, NULL, "SID", 0);
    //GNCDB_dropTable(db, tablename);                 /* 删表 */
    initOutput();

    //GNCDB_select(db, demoCallBack, &row, NULL, 1, 0, 0, tablename);
    if (row == 0)
    {
        // printf("\n 表名不存在 \n");
    }

    // printf("/****************Demo 8:drop table--end****************/\n\n");
    return 0;
}

int demo_closedb(GNCDB* db)
{
    int code = 0;
    code = GNCDB_close(&db);
    if (!code)
    {
        printf("数据库已关闭\n\n");
    }
    else
    {
        printf("数据库关闭失败 %d\n\n", code);
    }
    return 0;
}

char* join(char* s1, char* s2)
{
    char* result = NULL;
    result = my_malloc(strlen(s1) + strlen(s2) + 1);
    if (result == NULL) return NULL;

    strcpy(result, s1);
    strcat(result, s2);

    return result;
}

void clear_current_line() {
    printf("\033[2K\r");
    fflush(stdout);
}

void print_progress(float progress) {
    //clear_current_line();
    //printf("\r已完成 : %.2f%%", progress);
    //fflush(stdout);
}

int loadARPTData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize)
{
    FILE* fp_data_file;
    char* pash = NULL;
    int i = 0;
    int allRows = 0;
    int rows = 0;
    float proportion = 0;

    pash = join(filePath, file_name);
    if (pash == NULL)
    {
        return -1;
    }

    fp_data_file = fopen(pash, "r");   /*读取文件*/
    if (fp_data_file == NULL)
    {

        printf("无法打开文件 : %s", file_name);
        perror("\n文件打开失败");
        return -1;
    }
    my_free(pash);
    while (!feof(fp_data_file))
    {
        rows = 0;
        
        fscanf(fp_data_file, "%[^,],%lf,%lf,%lf,%lf,%lf,\n", 
            g_st_arpt[0].st_row.sc8_arpt_ident, 
            &g_st_arpt[0].st_row.f64_lon, 
            &g_st_arpt[0].st_row.f64_lat, 
            &g_st_arpt[0].st_row.f64_elev, 
            &g_st_arpt[0].st_row.f64_longest_rwy_length, 
            &g_st_arpt[0].st_row.f64_mag_var);
        GNCDB_insert(db, &rows, "ARPT",
            g_st_arpt[0].st_row.sc8_arpt_ident,
            g_st_arpt[0].st_row.f64_lon,
            g_st_arpt[0].st_row.f64_lat,
            g_st_arpt[0].st_row.f64_elev,
            g_st_arpt[0].st_row.f64_longest_rwy_length,
            g_st_arpt[0].st_row.f64_mag_var);
        i++;
        proportion = (float )i / v_si32_datasize * 100;
        print_progress(proportion);

        allRows += rows;
        if (i == v_si32_datasize)
        {
            break;
        }
    }  
    printf("\n共插入数据 %d 条\n", allRows);
    fclose(fp_data_file);

    return 0;
}

int loadSIDData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize)
{
    FILE* fp_data_file;
    int i = 0;
    int allRows = 0;
    int rows = 0;
    char* pash = NULL;
    float proportion = 0;
    pash = join(filePath, file_name);
    if (pash == NULL)
    {
        return -1;
    }

    fp_data_file = fopen(pash, "r");   /*读取文件*/
    if (fp_data_file == NULL)
    {

        printf("无法打开文件 : %s", file_name);
        perror("\n文件打开失败");
        return -1;
    }
    my_free(pash);

    /*Read data records and store them into global array: g_st_wpt*/

    while (!feof(fp_data_file))
    {
        rows = 0;
        fscanf(fp_data_file, "%[^,],%[^,],%c,%c,%lf,%[^,],%c,%lf,%lf,%lf,%lf,\n", 
            g_st_sid[0].st_row.sc8_arpt_ident,
            g_st_sid[0].st_row.sc8_sid_ident,
            g_st_sid[0].st_row.sc8_seq_no,
            g_st_sid[0].st_row.sc8_flight_seg_type,
            &g_st_sid[0].st_row.f64_track,
            g_st_sid[0].st_row.sc8_fix_ident,
            g_st_sid[0].st_row.sc8_fix_cat,
            &g_st_sid[0].st_row.f64_lon,
            &g_st_sid[0].st_row.f64_lat,
            &g_st_sid[0].st_row.f64_rnp,
            &g_st_sid[0].st_row.f64_alt);
        GNCDB_insert(db, &rows, "SID", g_st_sid[0].st_row.sc8_arpt_ident,
            g_st_sid[0].st_row.sc8_sid_ident,
            g_st_sid[0].st_row.sc8_seq_no,
            g_st_sid[0].st_row.sc8_flight_seg_type,
            g_st_sid[0].st_row.f64_track,
            g_st_sid[0].st_row.sc8_fix_ident,
            g_st_sid[0].st_row.sc8_fix_cat,
            g_st_sid[0].st_row.f64_lon,
            g_st_sid[0].st_row.f64_lat,
            g_st_sid[0].st_row.f64_rnp,
            g_st_sid[0].st_row.f64_alt);
        i++;
        allRows += rows;

        proportion = (float )i / v_si32_datasize * 100;
        print_progress(proportion);

        if (i == v_si32_datasize)
        {
            break;
        }
    }
    printf("\n共插入数据 %d 条\n", allRows);
    fclose(fp_data_file);

    return 0;
}


int loadWPTData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize)
{
    int rc = 0;
    FILE* fp_data_file;
    int i = 0;
    int allRows = 0;
    int rows = 0;
    int size = 0;
    BYTE* ImgBuffer = NULL;
    char* pash = NULL;
    float proportion = 0;
    pash = join(filePath, file_name);
    if (pash == NULL)
    {
        return -1;
    }

    fp_data_file = fopen(pash, "r");   /*读取文件*/
    if (fp_data_file == NULL)
    {
        printf("无法打开文件 : %s", file_name);
        perror("\n文件打开失败");
        return -1;
    }
    my_free(pash);

    while (!feof(fp_data_file))
    {
        rows = 0;

        fscanf(fp_data_file, "%[^,],%lf,%lf,\n",
            g_st_wpt[0].st_row.sc8_wpt_ident,
            &g_st_wpt[0].st_row.f64_lon, 
            &g_st_wpt[0].st_row.f64_lat);

        ImgBuffer = NULL;

        rc = GNCDB_insert(db, &rows, "WPT",
            g_st_wpt[0].st_row.sc8_wpt_ident,
            g_st_wpt[0].st_row.f64_lon,
            g_st_wpt[0].st_row.f64_lat, 
            size, ImgBuffer
            );
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }

        allRows += rows;
        i++;

        proportion = (float )i / v_si32_datasize * 100;
        print_progress(proportion);

        if (i == v_si32_datasize)
        {
            break;
        }

    }
    printf("\n共插入数据 %d 条\n", allRows);

    fclose(fp_data_file);

    return 0;
}

int loadNAVData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize)
{
    FILE* fp_data_file;
    int i = 0;
    int allRows = 0;
    int rows = 0;
    float proportion = 0;

    char* pash = NULL;
    pash = join(filePath, file_name);
    if (pash == NULL)
    {
        return -1;
    }

    fp_data_file = fopen(pash, "r");   /*读取文件*/
    if (fp_data_file == NULL)
    {

        printf("无法打开文件 : %s", file_name);
        perror("\n文件打开失败");
        return -1;
    }
    my_free(pash);
    while (!feof(fp_data_file))
    {
        fscanf(fp_data_file, "%[^,],%c,%lf,%lf,%lf,%lf,%lf,%lf,\n",
            g_st_nav[0].st_row.sc8_nav_ident,
            g_st_nav[0].st_row.sc8_nav_type,
            &g_st_nav[0].st_row.f64_lon,
            &g_st_nav[0].st_row.f64_lat,
            &g_st_nav[0].st_row.f64_vor,
            &g_st_nav[0].st_row.f64_dme,
            &g_st_nav[0].st_row.f64_tacan,
            &g_st_nav[0].st_row.f64_ndb
            );
        GNCDB_insert(db, &rows, "NAV",
            g_st_nav[0].st_row.sc8_nav_ident,
            g_st_nav[0].st_row.sc8_nav_type,
            g_st_nav[0].st_row.f64_lon,
            g_st_nav[0].st_row.f64_lat,
            g_st_nav[0].st_row.f64_vor,
            g_st_nav[0].st_row.f64_dme,
            g_st_nav[0].st_row.f64_tacan,
            g_st_nav[0].st_row.f64_ndb);

        allRows += rows;
        i++;

        proportion = (float )i / v_si32_datasize * 100;
        print_progress(proportion);

        if (i == v_si32_datasize)
        {
            break;
        }
    }
    printf("\n共插入数据 %d 条\n", allRows);

    fclose(fp_data_file);

    return 0;
}

int loadAPCHData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize)
{
    FILE* fp_data_file;
    int i = 0;
    int allRows = 0;
    int rows = 0;
    float proportion = 0;

    char* pash = NULL;
    pash = join(filePath, file_name);
    if (pash == NULL)
    {
        return -1;
    }

    fp_data_file = fopen(pash, "r");   /*读取文件*/
    if (fp_data_file == NULL)
    {

       printf("无法打开文件 : %s", file_name);
        perror("\n文件打开失败");
        return -1;
    }
    my_free(pash);
    while (!feof(fp_data_file))
    {
        fscanf(fp_data_file, "%[^,],%[^,],%c,%c,%lf,%[^,],%c,%c,%lf,%lf,%lf,%lf,\n",
            g_st_apch[0].st_row.sc8_arpt_ident,
            g_st_apch[0].st_row.sc8_apch_ident,
            g_st_apch[0].st_row.sc8_seq_no,
            g_st_apch[0].st_row.sc8_flight_seg_type,
            &g_st_apch[0].st_row.f64_track,
            g_st_apch[0].st_row.sc8_fix_ident,
            g_st_apch[0].st_row.sc8_fix_cat,
            g_st_apch[0].st_row.sc8_flight_stage,
            &g_st_apch[0].st_row.f64_lon,
            &g_st_apch[0].st_row.f64_lat,
            &g_st_apch[0].st_row.f64_rnp,
            &g_st_apch[0].st_row.f64_alt
            );
        
        GNCDB_insert(db, &rows, "APCH",
            g_st_apch[0].st_row.sc8_arpt_ident,
            g_st_apch[0].st_row.sc8_apch_ident,
            g_st_apch[0].st_row.sc8_seq_no,
            g_st_apch[0].st_row.sc8_flight_seg_type,
            g_st_apch[0].st_row.f64_track,
            g_st_apch[0].st_row.sc8_fix_ident,
            g_st_apch[0].st_row.sc8_fix_cat,
            g_st_apch[0].st_row.sc8_flight_stage,
            g_st_apch[0].st_row.f64_lon,
            g_st_apch[0].st_row.f64_lat,
            g_st_apch[0].st_row.f64_rnp,
            g_st_apch[0].st_row.f64_alt);

        allRows += rows;
        i++;

        proportion = (float )i / v_si32_datasize * 100;
        print_progress(proportion);

        if (i == v_si32_datasize)
        {
            break;
        }
    }  
    printf("\n共插入数据 %d 条\n", allRows);

    fclose(fp_data_file);

    return 0;
}


int loadDEFINE_WPTData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize)
{
    FILE* fp_data_file;
    int i = 0;
    int allRows = 0;
    int rows = 0;
    float proportion = 0;

    char* pash = NULL;
    pash = join(filePath, file_name);
    if (pash == NULL)
    {
        return -1;
    }

    fp_data_file = fopen(pash, "r");   /*读取文件*/
    if (fp_data_file == NULL)
    {
        printf("无法打开文件 : %s", file_name);
        perror("\n文件打开失败");
        return -1;
    }
    my_free(pash);
    while (!feof(fp_data_file))
    {
        fscanf(fp_data_file, "%[^,],%lf,%lf,\n",
            g_st_define_wpt[0].st_row.sc8_wpt_ident,
            &g_st_define_wpt[0].st_row.f64_lon,
            &g_st_define_wpt[0].st_row.f64_lat
        );

        GNCDB_insert(db, &rows, "DEFINE_WPT",
            g_st_define_wpt[0].st_row.sc8_wpt_ident,
            g_st_define_wpt[0].st_row.f64_lon,
            g_st_define_wpt[0].st_row.f64_lat);

        allRows += rows;
        i++;


        proportion = (float )i / v_si32_datasize * 100;
        print_progress(proportion);

        if (i == v_si32_datasize)
        {
            break;
        }
    }
    printf("\n共插入数据 %d 条\n", allRows);

    fclose(fp_data_file);

    return 0;
}

int loadFPLNData(GNCDB* db, DT_SCHAR8* file_name, DT_SINT32 v_si32_datasize)
{
    FILE* fp_data_file;
    int i = 0;
    int allRows = 0;
    int rows = 0;
    float proportion = 0;

    char* pash = NULL;
    pash = join(filePath, file_name);
    if (pash == NULL)
    {
        return -1;
    }

    fp_data_file = fopen(pash, "r");   /*读取文件*/
    if (fp_data_file == NULL)
    {

        printf("无法打开文件 : %s", file_name);
        perror("\n文件打开失败");
        return -1;
    }
    my_free(pash);
    while (!feof(fp_data_file))
    {
        rows = 0;
        fscanf(fp_data_file, "%c,%[^,],%[^,],%c,%lf,%[^,],%c,%c,%c,%lf,%lf,%lf,%lf,\n",
            g_st_fpln[0].st_row.sc8_chs_eng,
            g_st_fpln[0].st_row.sc8_fpln_ident,
            g_st_fpln[0].st_row.sc8_seq_no,
            g_st_fpln[0].st_row.sc8_flight_seq_type,
            &g_st_fpln[0].st_row.f64_track,
            g_st_fpln[0].st_row.sc8_fix_ident,
            g_st_fpln[0].st_row.sc8_fix_cat,
            g_st_fpln[0].st_row.sc8_fight_stage1,
            g_st_fpln[0].st_row.sc8_fight_stage2,
            &g_st_fpln[0].st_row.f64_lon,
            &g_st_fpln[0].st_row.f64_lat,
            &g_st_fpln[0].st_row.f64_rnp,
            &g_st_fpln[0].st_row.f64_alt
        );

        GNCDB_insert(db, &rows, "FPLN",
            g_st_fpln[0].st_row.sc8_chs_eng,
            g_st_fpln[0].st_row.sc8_fpln_ident,
            g_st_fpln[0].st_row.sc8_seq_no,
            g_st_fpln[0].st_row.sc8_flight_seq_type,
            g_st_fpln[0].st_row.f64_track,
            g_st_fpln[0].st_row.sc8_fix_ident,
            g_st_fpln[0].st_row.sc8_fix_cat,
            g_st_fpln[0].st_row.sc8_fight_stage1,
            g_st_fpln[0].st_row.sc8_fight_stage2,
            g_st_fpln[0].st_row.f64_lon,
            g_st_fpln[0].st_row.f64_lat,
            g_st_fpln[0].st_row.f64_rnp,
            g_st_fpln[0].st_row.f64_alt);

        allRows += rows;
        i++;

        proportion = (float )i / v_si32_datasize * 100;
        print_progress(proportion);

        if (i == v_si32_datasize)
        {
            break;
        }
    }

    printf("\n共插入数据 %d 条\n", allRows);
    fclose(fp_data_file);

    return 0;
}


/* 测试大对象数据 */
char* Blob(struct GNCDB* db, char* buf, int size) 
{
    int rc = 0;
    char* bufOfGet = NULL;
    if (db == NULL || buf == NULL) {
        return NULL;
    }

    rc = GNCDB_setBlob(db, "WPT", 3, (BYTE*)buf, size, 1, "HMSAN");

    if(rc)
        printf("BLOB 文件已添加\n ");

    GNCDB_select(db, demoCallBack, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=HMSAN");
    bufOfGet = (char*)my_malloc(size);
    if (buf == NULL) {
        return NULL;
    }
    rc = GNCDB_getBlob(db, "WPT", 3, (BYTE*)bufOfGet, size, 1, "HMSAN");
    if(rc){

    }
    return bufOfGet;
}

int demoBlob(struct GNCDB* db) 
{
    char filename[] = "./testfile/blob/blobdata/blob.png";  // 待处理的图片文件名
    FILE* fp;
    size_t fileLen;
    char* buffer;
    char* bufferCopy = NULL;
    printf("/**************** Demo 4 : 大对象演示 ****************/\n");

    // 读取图片到内存中
    fp = fopen(filename, "rb");
    if (!fp) 
    {
        printf("打开文件失败 %s\n", filename);
        return 1;
    }

    // 获取文件大小
    fseek(fp, 0, SEEK_END);
    fileLen = ftell(fp);
    rewind(fp);

    // 限制图片大小不超过1MB
    // if (fileLen > MAX_FILE_SIZE) 
    // {
    //     printf("File size exceeds limit\n");
    //     return 1;
    // }

    // 分配内存
    buffer = (char*)my_malloc(fileLen);
    if (!buffer) 
    {
        printf("内存申请失败\n");
        return 1;
    }

    // 读取文件内容到缓冲区
    fread(buffer, fileLen, 1, fp);

    // 关闭文件
    fclose(fp);

    // 存储到数据库中的数据即为buffer指针指向的二进制数据
    // 以下是将buffer数据重新转换为图像文件的过程
    bufferCopy = Blob(db, buffer, (int)fileLen);

    // 打开文件，准备写入数据
    fp = fopen("./testfile/blob/blobresult/demo/demoblob.png", "wb");
    if (!fp) 
    {
        printf("文件打开失败\n");
        return 1;
    }

    // 写入数据到文件中
    fwrite(bufferCopy, fileLen, 1, fp);

    // 关闭文件
    fclose(fp);

    printf("BLOB 文件已获取: ./testfile/blob/blobresult/demo/demoblob.png\n");

    // 释放内存
    my_free(buffer);

    return 0;
}

void* demo1_thread1(void* arg)
{
    int row = 0;
    GNCDB* db = (GNCDB*) arg;
    printf("线程1开始执行\n");
    GNCDB_delete(db, &row, "WPT", 1, "WPT_ident<=AUXDX");
    printf("线程1执行完毕\n");

    printf("线程1删除数量: %d\n", row);

    return NULL;
}

void* demo1_thread2(void* arg)
{
    int row = 0;
    GNCDB* db = (GNCDB*) arg;
    printf("线程2开始执行\n");
    GNCDB_delete(db, &row, "WPT", 2,  "WPT_ident>CEISI", "WPT_ident<DCIOP");
    printf("线程2执行完毕\n");

    printf("线程2删除数量: %d\n", row);
    return NULL;
}

void* demo2_thread1(void* arg)
{
    //int row = 0;
    GNCDB* db = (GNCDB*) arg;
    printf("线程1开始执行\n");
    GNCDB_select(db, demoCallBackMuti, NULL, NULL, 1, 0, 1, "FPLN", "FPLN_ident<=CLBVOXPDRS");
    printf("线程1执行完毕\n");

    return NULL;
}

void* demo2_thread2(void* arg)
{
    //int row = 0;
    GNCDB* db = (GNCDB*) arg;
    printf("线程2开始执行\n");
    GNCDB_select(db, demoCallBackMuti, NULL, NULL, 1, 0, 2, "FPLN", "FPLN_ident>=BLIBQSCZOC", "FPLN_ident<=FIJERABLTK");
    printf("线程2执行完毕\n");

    return NULL;
}

void* demo2_thread3(void* arg)
{
    //int row = 0;
    GNCDB* db = (GNCDB*) arg;
    printf("线程3开始执行\n");
    GNCDB_select(db, demoCallBackMuti, NULL, NULL, 1, 0, 2, "FPLN", "FPLN_ident>=IXYKKDVULG", "FPLN_ident<=LFWCMBIECY");
    printf("线程3执行完毕\n");

    return NULL;
}

int demo_muti(struct GNCDB* db)
{
    int row = 0;
    pthread_t thid[3] = {0};

    printf("/**************** Demo 8 : 并发 ****************/\n");
    printf("示例 1 : 两个线程分别删除WPT表: WPT_ident<=AUXDX  WPT_ident>CEISI&WPT_ident<DCIOP\n");
    initOutput();
    GNCDB_select(db, demoCallBack, &row, NULL, 1, 0, 1, "WPT", "WPT_ident<DKKUL");
    printf("删除前 %s:%d\n", "WPT", row);

    pthread_create(&thid[0], NULL, demo1_thread1, (void*)db);
    pthread_create(&thid[1], NULL, demo1_thread2, (void*)db);

    pthread_join(thid[0], NULL);
    pthread_join(thid[1], NULL);
    initOutput();
    GNCDB_select(db, demoCallBack, &row, NULL, 1, 0, 1, "WPT", "WPT_ident<DKKUL");
    printf("删除后 %s:%d\n", "WPT", row);

    printf("示例 2 : 三个线程分别查询FPLN表: FPLN_ident<=AUXDX  FPLN_ident>BHYLY FPLN_ident<DCIOP\n");

    initOutput();
    pthread_create(&thid[0], NULL, demo2_thread1, (void*)db);
    pthread_create(&thid[1], NULL, demo2_thread2, (void*)db);
    pthread_create(&thid[2], NULL, demo2_thread3, (void*)db);

    pthread_join(thid[0], NULL);
    pthread_join(thid[1], NULL);
    pthread_join(thid[2], NULL);

    return 0;

}