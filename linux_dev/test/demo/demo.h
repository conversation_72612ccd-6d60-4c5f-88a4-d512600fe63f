#ifndef _GNCDB_DEMO_H
#define _GNCDB_DEMO_H


#include <stdio.h>
#include <string.h>
#include "gncdb.h"

#define M_C_TRUE    1
#define M_C_FALSE   0



typedef char               DT_SCHAR8;
typedef unsigned char      DT_UCHAR8;
typedef short              DT_SINT16;
typedef unsigned short     DT_UINT16;
typedef int                DT_SINT32;
typedef unsigned int       DT_UINT32;
typedef unsigned long long DT_UINT64;
typedef long long          DT_SINT64;
typedef float              DT_FLOAT32;
typedef double             DT_FLOAT64;
typedef unsigned int       DT_BOOL;



#define		M_DATA_SIZE         200
#define		M_DATA_SIZES	50000
#define		M_MAX_ROWS       50000					// 最大行数    

// ======================== 1-机场数据ARPT =========================

// 1.数据表行结构体 */
typedef struct {
	DT_SCHAR8  sc8_arpt_ident[8];						// 机场标识       
	DT_FLOAT64 f64_lon;									// 机场参考点经度 
	DT_FLOAT64 f64_lat;									// 机场参考点纬度 
	DT_FLOAT64 f64_elev;								// 机场标高       
	DT_FLOAT64 f64_longest_rwy_length;					// 最长跑道长度   
	DT_FLOAT64 f64_mag_var;								// 磁差           
}ST_ARPT_ROW;

// 内部数据表行结构体 
typedef struct {
	ST_ARPT_ROW st_row;									// 实体数据表行 
	DT_UINT64   ui64_geohash;							// 地理信息编码 
	DT_BOOL     bl_flg;									// 行有效标志   
}ST_ARPT_ROW_INTERNAL;

// =======================2-航路点数据WPT============================
// 1.数据表行结构体 
typedef struct {
	DT_SCHAR8  sc8_wpt_ident[8];						// 航路点标识 
	DT_FLOAT64 f64_lon;                                 // 航路点经度 
	DT_FLOAT64 f64_lat;                                 // 航路点纬度 
}ST_WPT_ROW;

// 内部数据表行结构体 
typedef struct {
	ST_WPT_ROW st_row;                                  // 实体数据表行 
	DT_UINT64  ui64_geohash;                            // 地理信息编码 
	DT_BOOL    bl_flg;                                  // 行有效标志   
}ST_WPT_ROW_INTERNAL;

// ====================== 3-导航数据NAV==============================
// 1.数据表结构体
typedef struct{ 
	DT_SCHAR8	sc8_nav_ident[8];		// 导航台标识
	DT_SCHAR8	sc8_nav_type[2];								// 导航台类型
	DT_FLOAT64	f64_lon;									// 导航台经度
	DT_FLOAT64	f64_lat;									// 导航台纬度
	DT_FLOAT64	f64_vor;									// VOR频率
	DT_FLOAT64	f64_dme;									// DME频率
	DT_FLOAT64	f64_tacan;									// TANCAN频率
	DT_FLOAT64	f64_ndb;									// NDB频率
}ST_NAV_ROW;
// 内部数据表行结构体
typedef struct {
	ST_NAV_ROW	st_row;										// 实地数据表行
	DT_UINT64	ui64_geohash;								// 地理信息编码
	DT_BOOL		bl_flg;										// 行有效标志
}ST_NAV_ROW_INTERNAL;


// ======================= 4-离场程序数据SID ========================

// 1.数据表行结构体
typedef struct {
	DT_SCHAR8  sc8_arpt_ident[8];    // 机场标识     
	DT_SCHAR8  sc8_sid_ident[8];       // 离场程序标识 
	DT_SCHAR8  sc8_seq_no[2];                                 // 序号         
	DT_SCHAR8  sc8_flight_seg_type[2];                        // 航段类型     
	DT_FLOAT64 f64_track;                                  // 航迹角      
	DT_SCHAR8  sc8_fix_ident[8];       // 定位点标识   
	DT_SCHAR8  sc8_fix_cat[2];                                // 定位点属性   
	DT_FLOAT64 f64_lon;                                    // 定位点经度   
	DT_FLOAT64 f64_lat;                                    // 定位点纬度   
	DT_FLOAT64 f64_rnp;                                    // 水平半宽     
	DT_FLOAT64 f64_alt;                                    // 飞行高度     
}ST_SID_ROW;

// 内部数据表行结构体 
typedef struct {
	ST_SID_ROW st_row;                                     // 实体数据表行 
	DT_UINT64  ui64_geohash;                               // 地理信息编码 
	DT_BOOL    bl_flg;                                     // 行有效标志   
}ST_SID_ROW_INTERNAL;

// =========================5-进近程序数据APCH===================

// 1.数据表行结构体
typedef struct {
	DT_SCHAR8	sc8_arpt_ident[8];
	DT_SCHAR8	sc8_apch_ident[8];
	DT_SCHAR8	sc8_seq_no[2];
	DT_SCHAR8	sc8_flight_seg_type[2];
	DT_FLOAT64	f64_track;
	DT_SCHAR8	sc8_fix_ident[8];
	DT_SCHAR8	sc8_fix_cat[2];
	DT_SCHAR8	sc8_flight_stage[2];
	DT_FLOAT64	f64_lon;
	DT_FLOAT64	f64_lat;
	DT_FLOAT64	f64_rnp;
	DT_FLOAT64	f64_alt;
}ST_APCH_ROW;

// 内部数据表行结构体
typedef struct {
	ST_APCH_ROW st_row;
	DT_UINT64	ui64_geohash;
	DT_BOOL		bl_flg;
}ST_APCH_ROW_INTERNAL;

// ========================6-自定义航路点数据DEFINE_WPT================

// 1. 数据表行结构体*/
typedef struct {
	DT_SCHAR8	sc8_wpt_ident[8];
	DT_FLOAT64	f64_lon;
	DT_FLOAT64	f64_lat;
}ST_DEFINE_WPT_ROW;

// 内部数据表行结构体*/
typedef struct {
	ST_DEFINE_WPT_ROW	st_row;
	DT_UINT64			ui64_geohash;
	DT_BOOL				bl_flg;
}ST_DEFINE_WPT_ROW_INTERNAL;

// ==============================7-飞行计划数据FPLN==========================

// 1. 数据表行结构体*/
typedef struct {
	DT_SCHAR8	sc8_chs_eng[2];
	DT_SCHAR8	sc8_fpln_ident[16];
	DT_SCHAR8	sc8_seq_no[3];
	DT_SCHAR8	sc8_flight_seq_type[2];
	DT_FLOAT64	f64_track;
	DT_SCHAR8	sc8_fix_ident[8];
	DT_SCHAR8	sc8_fix_cat[2];
	DT_SCHAR8	sc8_fight_stage1[2];
	DT_SCHAR8	sc8_fight_stage2[2];
	DT_FLOAT64	f64_lon;
	DT_FLOAT64	f64_lat;
	DT_FLOAT64	f64_rnp;
	DT_FLOAT64	f64_alt;
}ST_FPLN_ROW;

typedef struct {
	ST_FPLN_ROW	st_row;
	DT_BOOL		bl_flg;
}ST_FPLN_ROW_INTERNAL;


int demo();
#endif