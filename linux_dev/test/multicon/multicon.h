#ifndef _MULTICON_H_
#define _MULTICON_H_

#include <stdio.h>

#include "dbsqlitetest.h"
#include "pthreadpool.h"
#include "gncdbconstant.h"

#define MULTICON_SQL 0

/* 多线程的数量 */
#define NUM_THREADS PTHREAD_MAXCOUNT
/* 一条记录最多存放的列数 */
#define MAX_TUPLE_COLUMN 20
/* 条件数量 */
#define NUM_CONDITION 5
/* 测试插入时每次插入的条数 */
#define NUM_INSERT 10
/* 测试项数量 */
#define MULTI_TESTNUM   11
/* 同表测试标记 */
extern int SAMETABLE;

#define MU_TABLE_INSERT_NUM 100
#define SAME_TABLE_INSERT_NUM 1000

/* 测试结构体 */
typedef struct {
    ThreadPool* threadPool;
    GNCDB* db;
    int testCount;
}MultiCon;


extern MultiCon* multiCon;

typedef enum {
    MUTIC_INSERT = 0,
    MUTIC_SELECT = 1,
    MUTIC_UPDATE = 2,
    MUTIC_DELETE = 3,
    MUTIC_SATUPLE = 4
} MUTIC_OP;

typedef struct {
    MUTIC_OP op;
    int (*taskFunction)(void* arg);
    int rc;
    int rowCount;
    char tableName[32];
    char condition[4][50];
    int index;
    SUPPLIER supplier[NUM_INSERT];
}OP_FUN;

int muticoncurrence();
int muticoncurrencemodule();
/* 初始化测试，创建线程池和数据库 */
int initMuticoncurrenceTest();
/* 关闭测试 */
int closeMuticoncurrenceTest();

/* OP_FUN 销毁函数 */
void destroyOpFun(void* data);
int locationFileFp(FILE** fp);

int createTableModule();
int insertTableModule();
int createSameTableModule();
int insertSameTableModule();

/* 处理结果 */
int mutiInterpretationResult(varArrayList* op_Fun);
int sameInterpretationResult(varArrayList* op_Fun);

/* 不同表测试操作函数 */
int insertMultiSupplierTable(void* arg);
int selectMultiSupplierTable(void* arg);
int updateMultiSupplierTable(void* arg);
int deleteMultiSupplierTable(void* arg);

/* 同表测试操作函数 */
int insertSameSupplierTable(void* arg);
int selectSameSupplierTable(void* arg);
int updateSameSupplierTable(void* arg);
int deleteSameSupplierTable(void* arg);
int updateSameTableTuple(void* arg);

/* 基础功能测试项 */
int multiThreadInsertOPTest(varArrayList* op_Fun);
int multiThreadSelectOPTest(varArrayList* op_Fun);
int multiThreadUpdateOPTest(varArrayList* op_Fun);
int multiThreadDeleteOPTest(varArrayList* op_Fun);
int mutiThreadRandomOPTest(varArrayList* op_Fun);
int mutiThreadupdateSameTableTuple(varArrayList* op_Fun);

/* 结果处理回调函数 */
int mu_CallBack(void *NotUsed, int argc, char** azColName, char** argv);
int updateCallBack_1(void *NotUsed, int argc, char** azColName, char** argv);
int updateCallBack_2(void *NotUsed, int argc, char** azColName, char** argv);
int mu_CallBackAllPrintf(void *NotUsed, int argc, char** azColName, char** argv);
int multestCallBackSqlcount(void *NotUsed, int argc, char** azColName, char** argv);
int mulcallBackSQLGetRows(void *NotUsed, int argc, char** azColName, char** argv);
int mu_testCallBack(void *NotUsed, int argc, char** azColName, char** argv);

#endif // _MULTICON_H_