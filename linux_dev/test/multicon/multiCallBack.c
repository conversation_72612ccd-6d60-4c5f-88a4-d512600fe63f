#include "multicon.h"

int mu_CallBack(void *NotUsed, int argc, char** azColName, char** argv)
{
    
    // printf("fieldValue[0]:%s\n", argv[0]);
    return 0;
}
int muflag = 0;

void muinitFlag()
{
    muflag = 0;
}
int mu_testCallBack(void *NotUsed, int argc, char** azColName, char** argv)
{
    int i = 0;
    if (muflag == 0)
    {
        //printf("查询出的记录：\n");
        printf("\n");
        printf("|");
        for (i = 0; i < argc; i++)
        {
            printf("%16s|", azColName[i]);
        }
        printf("\n");
        for (i = 0; i <= argc * 17; ++i)
        {
            printf("-");
        }
        printf("\n");
    }

    printf("|");
    for (i = 0; i < argc; i++)
    {
        printf("%16s|", argv[i]);
    }
    printf("\n");

    muflag = 1;

    return 0;
}

int updateCallBack_1(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(strcmp(argv[4], "1111111111") == 0)
    {
        multiCon->testCount++;
    }
    else{
        //printf("111");
    }
    return 0;
}

int updateCallBack_2(void *NotUsed, int argc, char** azColName, char** argv)
{
    int i = 0;
    for(; i < 21; i++)
    {
        // printf("argv[%d]:%s\n", i, argv[i]);
        if(strcmp(argv[i], "11") == 0)
        {
            multiCon->testCount++;
        }
        else{
            //printf("222");
        }
    }
    return 0;
}


int mu_CallBackAllPrintf(void *NotUsed, int argc, char** azColName, char** argv)
{
    //printf("查询出的记录：\n");
    // printf("\n");
    // printf("|");
    // for (int i = 0; i < columnNum; i++)
    // {
    //     printf("%16s|", fieldName[i]);
    // }
    // printf("\n");
    // for (int i = 0; i <= columnNum * 17; ++i)
    // {
    //     printf("-");
    // }
    // printf("\n");

    printf("|");
    for (int i = 0; i < argc - 3; i++)
    {
        printf("%4s|", argv[i]);
    }
    printf("\n");

    return 0;
}

int multestCallBackSqlcount(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p= NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p)++;

    }
    return 0;
}

int mulcallBackSQLGetRows(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(NotUsed != NULL)
    {
        int* num = (int*)NotUsed;
        (*num) = atoi(argv[0]);
    }

    return 0;
}