#include "multicon.h"
#include "gncdb.h"
#include <unistd.h>

int SAMETABLE = 0;
MultiCon* multiCon = NULL;
typedef int (*operationFun)(varArrayList*);

operationFun operationFunArray[MULTI_TESTNUM] = {
    multiThreadInsertOPTest,
    multiThreadSelectOPTest,
    multiThreadUpdateOPTest,
    multiThreadDeleteOPTest,
    mutiThreadRandomOPTest,
    multiThreadInsertOPTest,
    multiThreadSelectOPTest,
    multiThreadUpdateOPTest,
    multiThreadDeleteOPTest,
    mutiThreadRandomOPTest,
    mutiThreadupdateSameTableTuple
};

int muticoncurrence()
{
    int i = 0;
    int rc = 0;

    SAMETABLE = 0;
    for(i = 0; i < MULTI_TESTNUM; i++)
    {
        if(i == 5)
        {
            SAMETABLE = 1;
        }
       rc = muticoncurrencemodule(operationFunArray[i]);
       if(rc != 0)
       {
           return -1;
       }
    }

    return 0;
}



int muticoncurrencemodule(operationFun opFun)
{
    int rc = 0;
    int threadNum = NUM_THREADS;


    int i = 0;
    varArrayList* op_Fun = NULL;
    OP_FUN* op_Fun_p = NULL;

    rc = initMuticoncurrenceTest();
    if(rc != 0)
    {
        return -1;
    }
    /* 创建测试的条件，创建足够的表并且在表中插入数据 */
    if(SAMETABLE == 0)
    {
        rc = createTableModule();
        if(rc != 0)
        {
            return -1;
        }
        rc = insertTableModule();
        if(rc != 0)
        {
            return -1;
        }
    }
    else
    {
        rc = createSameTableModule();
        if(rc != 0)
        {
            return -1;
        }
        rc = insertSameTableModule();
        if(rc != 0)
        {
            return -1;
        }
    }
    
    op_Fun = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    if(op_Fun == NULL)
    {
        return -1;
    }

    opFun(op_Fun);

    /* 执行测试 */
    multiCon->threadPool->ct_count = threadNum;
    for(i = 0; i < threadNum; i++)
    {
        op_Fun_p = varArrayListGetPointer(op_Fun, i);
        if(op_Fun_p == NULL)
        {
            multiCon->threadPool->ct_count = i;
            break;
        }
        threadPoolAddTask(multiCon->threadPool, op_Fun_p->taskFunction, (void*)op_Fun_p);
    }

    threadPoolWaitAllTasks(multiCon->threadPool);

    if(SAMETABLE == 0)
    {
        mutiInterpretationResult(op_Fun);
    }
    else
    {
        sameInterpretationResult(op_Fun);
    }

    varArrayListDestroy(&op_Fun);
    rc = closeMuticoncurrenceTest();

    return rc;
}



/* 初始化测试，创建线程池和数据库 */
int initMuticoncurrenceTest()
{
    int rc = 0;
    char *db_name = "multicondb.dat";

    multiCon = my_malloc(sizeof(MultiCon));
    if(multiCon == NULL)
    {
        return -1;
    }
    multiCon->threadPool = threadPoolCreate(NUM_THREADS);
    if(multiCon->threadPool == NULL)
    {
        my_free(multiCon);
        return -1;
    }

    remove(db_name);
	remove("log_multicondb.dat");

    rc = GNCDB_open(&(multiCon->db), db_name, 0, 0);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(multiCon);
        threadPoolDestroy(multiCon->threadPool);
        return -1;
    }

    return 0;
}

void destroyOpFun(void* data)
{
    OP_FUN** pointer = data;
    OP_FUN* op_Fun = *pointer;
    my_free(op_Fun);
}


/* 关闭测试 */
int closeMuticoncurrenceTest()
{
    GNCDB_close(&(multiCon->db));
    //varArrayListDestroy(&op_Fun);
    threadPoolDestroy(multiCon->threadPool);
    my_free(multiCon);
    return 0;
}

int multiThreadInsertOPTest(varArrayList* op_Fun)
{
    int i = 0;
    int j = 0;
    int rc = 0;
    int threadNum = NUM_THREADS;
    OP_FUN* funPointer = NULL;
    FILE *fp = NULL;

    rc = locationFileFp(&fp);
    if(rc != 0)
    {
        return -1;
    }

    for(i = 0; i < threadNum; ++i)
    {
        funPointer = my_malloc(sizeof(OP_FUN));
        if(funPointer == NULL)
        {
            return -1;
        }
        funPointer->rowCount = 0;
        funPointer->op = MUTIC_INSERT;
        if(SAMETABLE == 0)
        {
            funPointer->taskFunction = insertMultiSupplierTable;
        }
        else
        {
            for(j = 0; j < NUM_INSERT; ++j)
            {
                fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
                    &funPointer->supplier[j].suppkey,
                    funPointer->supplier[j].name,
                    funPointer->supplier[j].address,
                    &funPointer->supplier[j].nationkey,
                    funPointer->supplier[j].phone,
                    &funPointer->supplier[j].acctbal,
                    funPointer->supplier[j].comment);
            }
            funPointer->taskFunction = insertSameSupplierTable;
        }
        funPointer->index = i;
        rc = varArrayListAddPointer(op_Fun, funPointer);
        if(rc != GNCDB_SUCCESS)
        {
            fclose(fp);
            my_free(funPointer);
            return -1;
        }
    }
    fclose(fp);
    return 0;
}

int multiThreadSelectOPTest(varArrayList* op_Fun)
{
    int i = 0;
    int rc = 0;
    int threadNum = NUM_THREADS;
    OP_FUN* funPointer = NULL;
    int space = SAME_TABLE_INSERT_NUM / NUM_THREADS;
    int start = 0;

    for(i = 0; i < threadNum; ++i)
    {
        funPointer = my_malloc(sizeof(OP_FUN));
        if(funPointer == NULL)
        {
            return -1;
        }
        funPointer->rowCount = 0;
        funPointer->op = MUTIC_SELECT;
        funPointer->index = i;
        if(SAMETABLE == 0)
        {
            funPointer->taskFunction = selectMultiSupplierTable;
        }
        else
        {
            sprintf(funPointer->condition[1], "s_suppkey>%d", start);
            start += space;
            sprintf(funPointer->condition[2], "s_suppkey<%d", start - 5);
            funPointer->taskFunction = selectSameSupplierTable;
        }
        
        rc = varArrayListAddPointer(op_Fun, funPointer);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(funPointer);
            return -1;
        }
    }
    return 0;
}

int multiThreadUpdateOPTest(varArrayList* op_Fun)
{
    int i = 0;
    int rc = 0;
    int threadNum = NUM_THREADS;
    OP_FUN* funPointer = NULL;
    int space = SAME_TABLE_INSERT_NUM / NUM_THREADS;
    int start = 0;

    for(i = 0; i < threadNum; ++i)
    {
        funPointer = my_malloc(sizeof(OP_FUN));
        if(funPointer == NULL)
        {
            return -1;
        }
        funPointer->rowCount = 0;
        funPointer->op = MUTIC_UPDATE;
        funPointer->index = i;
        if(SAMETABLE == 0)
        {
            funPointer->taskFunction = updateMultiSupplierTable;
        }
        else
        {
            sprintf(funPointer->condition[1], "s_suppkey>%d", start);
            start += space;
            sprintf(funPointer->condition[2], "s_suppkey<%d", start - 5);
            funPointer->taskFunction = updateSameSupplierTable;
        }
        rc = varArrayListAddPointer(op_Fun, funPointer);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(funPointer);
            return -1;
        }
    }
    return 0;
}

int multiThreadDeleteOPTest(varArrayList* op_Fun)
{
    int i = 0;
    int rc = 0;
    int threadNum = NUM_THREADS;
    OP_FUN* funPointer = NULL;
    int space = SAME_TABLE_INSERT_NUM / NUM_THREADS;
    int start = 0;

    for(i = 0; i < threadNum; ++i)
    {
        funPointer = my_malloc(sizeof(OP_FUN));
        if(funPointer == NULL)
        {
            return -1;
        }

        funPointer->rowCount = 0;
        funPointer->op = MUTIC_DELETE;
        funPointer->index = i;
        if(SAMETABLE == 0)
        {
            funPointer->taskFunction = deleteMultiSupplierTable;
        }
        else
        {
            sprintf(funPointer->condition[1], "s_suppkey>%d", start);
            start += space;
            sprintf(funPointer->condition[2], "s_suppkey<%d", start - 5);
            funPointer->taskFunction = deleteSameSupplierTable;
        }
        rc = varArrayListAddPointer(op_Fun, funPointer);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(funPointer);
            return -1;
        }
    }
    return 0;
}

int mutiThreadRandomOPTest(varArrayList* op_Fun)
{
    int i = 0;
    int j = 0;
    int rc = 0;
    int threadNum = NUM_THREADS;
    OP_FUN* funPointer = NULL;
    int space = SAME_TABLE_INSERT_NUM / NUM_THREADS;
    int start = 0;
    FILE *fp = NULL;

    rc = locationFileFp(&fp);
    if(rc != 0)
    {
        return -1;
    }

    for(i = 0; i < threadNum; ++i)
    {
        funPointer = my_malloc(sizeof(OP_FUN));
        if(funPointer == NULL)
        {
            return -1;
        }
        if(i % 4 == 1)
        {
            funPointer->op = MUTIC_INSERT;
            if(SAMETABLE == 0)
            {
                funPointer->taskFunction = insertMultiSupplierTable;
            }
            else
            {
                for(j = 0; j < NUM_INSERT; ++j)
                {
                    fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
                        &funPointer->supplier[j].suppkey,
                        funPointer->supplier[j].name,
                        funPointer->supplier[j].address,
                        &funPointer->supplier[j].nationkey,
                        funPointer->supplier[j].phone,
                        &funPointer->supplier[j].acctbal,
                        funPointer->supplier[j].comment);
                }
                funPointer->taskFunction = insertSameSupplierTable;
            }
        }
        else if(i % 4 == 2)
        {
            funPointer->op = MUTIC_SELECT;
            if(SAMETABLE == 0)
            {
                funPointer->taskFunction = selectMultiSupplierTable;
            }
            else
            {
                sprintf(funPointer->condition[1], "s_suppkey>%d", start);
                start += space;
                sprintf(funPointer->condition[2], "s_suppkey<%d", start - 5);
                funPointer->taskFunction = selectSameSupplierTable;
            }
        }
        else if(i % 4 == 3)
        {
            funPointer->op = MUTIC_UPDATE;
            if(SAMETABLE == 0)
            {
                funPointer->taskFunction = updateMultiSupplierTable;
            }
            else
            {
                sprintf(funPointer->condition[1], "s_suppkey>%d", start);
                start += space;
                sprintf(funPointer->condition[2], "s_suppkey<%d", start - 5);
                funPointer->taskFunction = updateSameSupplierTable;
            }
        }
        else if(i % 4 == 0)
        {
            funPointer->op = MUTIC_DELETE;
            if(SAMETABLE == 0)
            {
                funPointer->taskFunction = deleteMultiSupplierTable;
            }
            else
            {
                sprintf(funPointer->condition[1], "s_suppkey>%d", start);
                start += space;
                sprintf(funPointer->condition[2], "s_suppkey<%d", start - 5);
                funPointer->taskFunction = deleteSameSupplierTable;
            }
        }
        funPointer->rowCount = 0;
        funPointer->index = i;
        rc = varArrayListAddPointer(op_Fun, funPointer);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(funPointer);
            return -1;
        }
    }
    return 0;
}

int mutiThreadupdateSameTableTuple(varArrayList* op_Fun)
{
    int i = 0;
    int rc = 0;
    int threadNum = 0;
    OP_FUN* funPointer = NULL;
    threadNum = NUM_THREADS > MAX_TUPLE_COLUMN ? MAX_TUPLE_COLUMN : NUM_THREADS;

    for(i = 0; i < threadNum; ++i)
    {
        funPointer = my_malloc(sizeof(OP_FUN));
        if(funPointer == NULL)
        {
            return -1;
        }
        funPointer->rowCount = 0;
        funPointer->op = MUTIC_SATUPLE;
        funPointer->index = i;

        funPointer->taskFunction = updateSameTableTuple;

        rc = varArrayListAddPointer(op_Fun, funPointer);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(funPointer);
            return -1;
        }
    }
    return 0;
}

void removeSingleQuotes(char *str) {
    int i = 0; // 遍历原始字符串的索引
    int j = 0; // 用于覆盖的索引

    while (str[i] != '\0') { // 遍历字符串
        if (str[i] != '\'') { // 如果当前字符不是单引号
            str[j++] = str[i]; // 将字符复制到覆盖位置
        }
        i++; // 继续遍历
    }
    str[j] = '\0'; // 添加字符串结束符
}


int mutiInterpretationResult(varArrayList* op_Fun)
{
    int i = 0;
    int rc = 0;
    int threadNum = NUM_THREADS;
    char opName[NUM_THREADS] = {0};
    char result[NUM_THREADS] = {0};
    int allRow = 0;
    int row = MU_TABLE_INSERT_NUM;
    int insertCount = 0;
    int selectCount = 0;
    int updateCount = 0;
    int deleteCount = 0;
    OP_FUN* funPointer = NULL;

    for(i = 0; i < threadNum; ++i)
    {
        funPointer = varArrayListGetPointer(op_Fun, i);
        if(funPointer == NULL)
        {
            return -1;
        }

        if(funPointer->op == MUTIC_INSERT)
        {
            opName[i] = 'I';
            rc = GNCDB_select(multiCon->db, NULL, &insertCount, NULL, 1, 0, 0, funPointer->tableName);
            rc = GNCDB_select(multiCon->db, NULL, &insertCount, NULL, 1, 0, 0, funPointer->tableName);
            if(rc != GNCDB_SUCCESS)
            {
                printf("%s table result select error\n", funPointer->tableName);
            }
            allRow = row + funPointer->rowCount;
            if(allRow != insertCount || funPointer->rowCount == 0)
            {
                result[i] = 'F';
                GNCDB_select(multiCon->db, mu_CallBack, NULL, NULL, 1, 0, 0, funPointer->tableName);
                GNCDB_select(multiCon->db, mu_CallBack, NULL, NULL, 1, 0, 0, funPointer->tableName);
                //printf("insert table %s error %d %d %d\n", funPointer->tableName, allRow, funPointer->rowCount, insertCount);
            }
            else
            {
                result[i] = 'Y';
                //printf("insert table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }

        }
        else if(funPointer->op == MUTIC_SELECT)
        {
            opName[i] = 'S';
            removeSingleQuotes(funPointer->condition[0]);
            rc = GNCDB_select(multiCon->db, NULL, &selectCount, NULL, 1, 0, 1, funPointer->tableName, funPointer->condition[0]);
            if(rc != GNCDB_SUCCESS)
            {
                printf("%s table select error\n", funPointer->tableName);
            }
            if(funPointer->rowCount != selectCount)
            {
                result[i] = 'N';
                //printf("select table %s error\n", funPointer->tableName);
            }
            else
            {
                result[i] = 'Y';
                //printf("select table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }
        }
        else if(funPointer->op == MUTIC_UPDATE)
        {
            opName[i] = 'U';
            multiCon->testCount = 0;
            removeSingleQuotes(funPointer->condition[0]);
            rc = GNCDB_select(multiCon->db, updateCallBack_1, &updateCount, NULL, 1, 0, 2, funPointer->tableName, funPointer->condition[0], funPointer->condition[1]);
            if(rc != GNCDB_SUCCESS)
            {
                printf("%s table select error\n", funPointer->tableName);
            }
            if(funPointer->rowCount != updateCount || multiCon->testCount != funPointer->rowCount)
            {
                result[i] = 'N';
                //printf("update table %s error\n", funPointer->tableName);
            }
            else
            {
                result[i] = 'Y';
                //printf("update table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }
        }
        else if(funPointer->op == MUTIC_DELETE)
        {
            opName[i] = 'D';
            removeSingleQuotes(funPointer->condition[0]);
            rc = GNCDB_select(multiCon->db, NULL, &deleteCount, NULL, 1, 0, 2, funPointer->tableName, funPointer->condition[0], funPointer->condition[1]);
            if(rc != GNCDB_SUCCESS)
            {
                //printf("%s table select error\n", funPointer->tableName);
            }
            if(deleteCount != 0)
            {
                result[i] = 'N';
                //printf("delete table %s error\n", funPointer->tableName);
            }
            else
            {
                result[i] = 'Y';
                //printf("delete table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }
        }
    }
    
    printf("|");
    for(i = 0; i < threadNum; ++i)
    {
        printf("  %c|", opName[i]);
    }
    printf("\n|");

    for(i = 0; i < threadNum; ++i)
    {
        printf("  %c|", result[i]);
    }
    printf("\n");

    return 0;
}

int sameInterpretationResult(varArrayList* op_Fun)
{
    int i = 0;
    int rc = 0;
    int threadNum = NUM_THREADS;
    char opName[NUM_THREADS] = {0};
    char result[NUM_THREADS] = {0};
    int insertCount = 0;
    int selectCount = 0;
    int updateCount = 0;
    int deleteCount = 0;
    int allTuple = 0;
    int sameTupleFlag = 0;
    OP_FUN* funPointer = NULL;

    for(i = 0; i < op_Fun->elementCount; ++i)
    {
        funPointer = varArrayListGetPointer(op_Fun, i);
        if(funPointer == NULL)
        {
            return -1;
        }

        if(funPointer->op == MUTIC_INSERT)
        {
            opName[i] = 'I';
            sprintf(funPointer->condition[1], "s_suppkey>=%d", funPointer->supplier[0].suppkey);
            sprintf(funPointer->condition[2], "s_suppkey<=%d", funPointer->supplier[NUM_INSERT - 1].suppkey);
            rc = GNCDB_select(multiCon->db, NULL, &insertCount, NULL, 1, 0, 2, "supplier", funPointer->condition[1], funPointer->condition[2]);
            rc = GNCDB_select(multiCon->db, NULL, &insertCount, NULL, 1, 0, 2, "supplier", funPointer->condition[1], funPointer->condition[2]);
            if(rc != GNCDB_SUCCESS)
            {
                printf("%s table select error\n", funPointer->tableName);
            }
            if(funPointer->rowCount != insertCount || funPointer->rowCount == 0)
            {
                result[i] = 'N';
                //printf("insert table %s error %d %d\n", funPointer->tableName, funPointer->rowCount, insertCount);
            }
            else
            {
                result[i] = 'Y';
                //printf("insert table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }

        }
        else if(funPointer->op == MUTIC_SELECT)
        {
            opName[i] = 'S';
            removeSingleQuotes(funPointer->condition[0]);
            rc = GNCDB_select(multiCon->db, NULL, &selectCount, NULL, 1, 0, 3, "supplier", funPointer->condition[0], funPointer->condition[1], funPointer->condition[2]);
            if(rc != GNCDB_SUCCESS)
            {
                printf("%s table select error\n", funPointer->tableName);
            }
            if(funPointer->rowCount != selectCount)
            {
                result[i] = 'N';
                //printf("select table %s error\n", funPointer->tableName);
            }
            else
            {
                result[i] = 'Y';
                //printf("select table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }
        }
        else if(funPointer->op == MUTIC_UPDATE)
        {
            opName[i] = 'U';
            multiCon->testCount = 0;
            updateCount = 0;
            removeSingleQuotes(funPointer->condition[0]);
            rc = GNCDB_select(multiCon->db, updateCallBack_1, &updateCount, NULL, 1, 0, 3, "supplier", funPointer->condition[0], funPointer->condition[1], funPointer->condition[2]);
            if(rc != GNCDB_SUCCESS)
            {
                printf("%s table select error\n", funPointer->tableName);
            }
            if(funPointer->rowCount != updateCount || multiCon->testCount != funPointer->rowCount)
            {
                result[i] = 'N';
                //printf("update table %s %d error\n", funPointer->tableName, funPointer->rowCount);
            }
            else
            {
                result[i] = 'Y';
                //printf("update table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }
        }
        else if(funPointer->op == MUTIC_DELETE)
        {
            opName[i] = 'D';
            removeSingleQuotes(funPointer->condition[0]);
            rc = GNCDB_select(multiCon->db, NULL, &deleteCount,NULL, 1, 0, 3, "supplier", funPointer->condition[0], funPointer->condition[1], funPointer->condition[2]);
            if(rc != GNCDB_SUCCESS)
            {
                printf("%s table select error\n", funPointer->tableName);
            }
            if(deleteCount != 0)
            {
                result[i] = 'N';
                //printf("delete table %s error %d \n", funPointer->tableName, funPointer->rc);
            }
            else
            {
                result[i] = 'Y';
                //printf("delete table %s %d success\n", funPointer->tableName, funPointer->rowCount);
            }
        }
        else if(funPointer->op == MUTIC_SATUPLE)
        {
            if(funPointer->rc == 0)
            {
                allTuple++;
            }
            sameTupleFlag++;
        }
    }



    if(sameTupleFlag != 0)
    {
        multiCon->testCount = 0;
        // GNCDB_select(multiCon->db, mu_testCallBack, NULL, NULL, 1, 0, 0, "mutitable");
        rc = GNCDB_select(multiCon->db, updateCallBack_2, NULL, NULL, 1, 0, 1, "mutitable", "mukey=5");
        if(rc != GNCDB_SUCCESS)
        {
            printf("%s table select error\n", funPointer->tableName);
        }
        if(allTuple != multiCon->testCount)
        {
            printf("update table %s %d %d error\n", funPointer->tableName, allTuple, multiCon->testCount);
        }
        else
        {
            printf("update table %s %d success\n", funPointer->tableName, allTuple);
        }
        GNCDB_select(multiCon->db, mu_CallBackAllPrintf, NULL, NULL, 1, 0, 1, "mutitable", "mukey=5");
    }
    else
    {
        printf("|");
        for(i = 0; i < threadNum; ++i)
        {
            printf("  %c|", opName[i]);
        }
        printf("\n|");

        for(i = 0; i < threadNum; ++i)
        {
            printf("  %c|", result[i]);
        }
        printf("\n");
    }

    
    return 0;
}