#include "multicon.h"


static char optpchpath[] = "./testfile/datafile/";
static char opsupplierFileName[] = "supplier.tbl";

SUPPLIER sqsuppliertbl;

SUPPLIER supplier[NUM_INSERT];

/* 查询条件 */
char selectCondition[NUM_CONDITION][50] = {
    //"s_suppkey<=10",
    "s_name>Supplier#000000012",
    "s_address>YjP5C55zHDXL7LalK27zfQnwejdpin4AMpvh",
    "s_nationkey>22",
    "s_phone<=34-************",
    "s_acctbal<=530.82",
    //"s_comment"
};
/* 更新条件 */
char updateCondition[NUM_CONDITION * 2][50] = {
    //"s_suppkey>1",
    "s_nationkey>5",
    "s_address>X",
    "s_address>A",
    "s_nationkey>10",
    "s_acctbal<=530.82",
    //"s_suppkey<10",
    "s_nationkey<10",
    "s_address<Z",
    "s_address<D",
    "s_nationkey<15",
    "s_acctbal>=0",
};
/* 删除条件 */
char deleteCondition[NUM_CONDITION * 2][50] = {
    //"s_suppkey>1",
    "s_nationkey>5",
    "s_address>X",
    "s_address>A",
    "s_nationkey>10",
    "s_acctbal<=530.82",
    //"s_suppkey<10",
    "s_nationkey<10",
    "s_address<Z",
    "s_address<D",
    "s_nationkey<15",
    "s_acctbal>=0",
};


/* 查询条件 */
char sqlselectCondition[NUM_CONDITION][50] = {
    //"s_suppkey<=10",
    "s_name>'Supplier#000000012'",
    "s_address>'YjP5C55zHDXL7LalK27zfQnwejdpin4AMpvh'",
    "s_nationkey>22",
    "s_phone<='34-************'",
    "s_acctbal<=530.82",
    //"s_comment"
};
/* 更新条件 */
char sqlupdateCondition[NUM_CONDITION * 2][50] = {
    //"s_suppkey>1",
    "s_nationkey>5",
    "s_address>'X'",
    "s_address>'A'",
    "s_nationkey>10",
    "s_acctbal<=530.82",
    //"s_suppkey<10",
    "s_nationkey<10",
    "s_address<'Z'",
    "s_address<'D'",
    "s_nationkey<15",
    "s_acctbal>=0",
};
/* 删除条件 */
char sqldeleteCondition[NUM_CONDITION * 2][50] = {
    //"s_suppkey>1",
    "s_nationkey>5",
    "s_address>'X'",
    "s_address>'A'",
    "s_nationkey>10",
    "s_acctbal<=530.82",
    //"s_suppkey<10",
    "s_nationkey<10",
    "s_address<'Z'",
    "s_address<'D'",
    "s_nationkey<15",
    "s_acctbal>=0",
};

int createTableModule()
{
    int rc = 0;
    int i = 0;
    double min = -100000000.0;
	double max = 100000000.0;
    char tableName[20] = {0};

    //rc = perCreateTable(multiCon->db);
    if (rc != 0)
    {
        return -1;
    }
    for(i = 0; i < NUM_THREADS; i++)
    {
        sprintf(tableName, "supplier_%d", i);
        if(MULTICON_SQL)
        {
            char sql[256] = {0};
            sprintf(sql, "CREATE TABLE %s(s_suppkey INT PRIMARY KEY, "
                "s_name CHAR(25), "
                "s_address CHAR(40), "
                "s_nationkey INT, "
                "s_phone CHAR(15), "
                "s_acctbal FLOAT, "
                "s_comment CHAR(101));", tableName);
            rc = GNCDB_exec(multiCon->db, sql, NULL, NULL, NULL);
        }
        else
        {
            rc = GNCDB_createTable(multiCon->db, tableName, 7,
                "s_suppkey", FIELDTYPE_INTEGER, 0, 1, min, max,
                "s_name", FIELDTYPE_VARCHAR, 0, 0, 0.0, 25.0,
                "s_address", FIELDTYPE_VARCHAR, 0, 0, 0.0, 40.0,
                "s_nationkey", FIELDTYPE_INTEGER, 0, 0, min, max,
                "s_phone", FIELDTYPE_VARCHAR, 0, 0, 0.0, 15.0,
                "s_acctbal", FIELDTYPE_REAL, 0, 0, min, max,
                "s_comment", FIELDTYPE_VARCHAR, 0, 0, 0.0, 101.0,
                10000);
        }
        
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }
    return 0;
}

int insertTableModule()
{
    int rc = 0;
    int i = 0;
    int j = 0;
    char tableName[20] = {0};
    char supplierPath[100] = {0};
    FILE *fp = NULL;

    sprintf(supplierPath, "%s%s", optpchpath, opsupplierFileName);
	fp = fopen(supplierPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < MU_TABLE_INSERT_NUM; ++i)
	{
		fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&sqsuppliertbl.suppkey,
			sqsuppliertbl.name,
			sqsuppliertbl.address,
			&sqsuppliertbl.nationkey,
			sqsuppliertbl.phone,
			&sqsuppliertbl.acctbal,
			sqsuppliertbl.comment);

        for(j = 0; j < NUM_THREADS; j++)
        {
            sprintf(tableName, "supplier_%d", j);
            if(MULTICON_SQL)
            {
                char sql[256] = {0};
                sprintf(sql, "insert into %s values(%d, '%s', '%s', %d, '%s', %lf, '%s')", tableName,
                    sqsuppliertbl.suppkey,
                    sqsuppliertbl.name,
                    sqsuppliertbl.address,
                    sqsuppliertbl.nationkey,
                    sqsuppliertbl.phone,
                    sqsuppliertbl.acctbal,
                    sqsuppliertbl.comment);
                rc = GNCDB_exec(multiCon->db, sql, NULL, NULL, NULL);
            }
            else
            {
                rc = GNCDB_insert(multiCon->db, NULL, tableName,
                    sqsuppliertbl.suppkey,
                    sqsuppliertbl.name,
                    sqsuppliertbl.address,
                    sqsuppliertbl.nationkey,
                    sqsuppliertbl.phone,
                    sqsuppliertbl.acctbal,
                    sqsuppliertbl.comment);
                    if (rc != GNCDB_SUCCESS)
                    {
                        fclose(fp);
                        return rc;
                    }
            }
            
        }
	}
	for(i = 0; i < NUM_INSERT; i++)
    {
        fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&supplier[i].suppkey,
			supplier[i].name,
			supplier[i].address,
			&supplier[i].nationkey,
			supplier[i].phone,
			&supplier[i].acctbal,
			supplier[i].comment);
    }
    
    fclose(fp);

    return 0;
}


int createSameTableModule()
{
    int rc = 0;
    double min = -100000000.0;
	double max = 100000000.0;

    if(MULTICON_SQL)
    {
        char sql1[256] = {0};
        char sql2[1024] = {0};
        sprintf(sql1, "create table supplier"
            "(s_suppkey INT PRIMARY KEY, "
            "s_name CHAR(25), "
            "s_address CHAR(40), "
            "s_nationkey INT, "
            "s_phone CHAR(15), "
            "s_acctbal FLOAT, "
            "s_comment CHAR(101));");
        rc = GNCDB_exec(multiCon->db, sql1, NULL, NULL, NULL);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        sprintf(sql2, "create table mutitable"
            "(mukey INT PRIMARY KEY, "
            "name1 CHAR(3),"
            "name2 CHAR(3),"
            "name3 CHAR(3),"
            "name4 CHAR(3),"
            "name5 CHAR(3),"
            "name6 CHAR(3),"
            "name7 CHAR(3),"
            "name8 CHAR(3),"
            "name9 CHAR(3),"
            "name10 CHAR(3),"
            "name11 CHAR(3),"
            "name12 CHAR(3),"
            "name13 CHAR(3),"
            "name14 CHAR(3),"
            "name15 CHAR(3),"
            "name16 CHAR(3),"
            "name17 CHAR(3),"
            "name18 CHAR(3),"
            "name19 CHAR(3),"
            "name20 CHAR(3));");
        rc = GNCDB_exec(multiCon->db, sql2, NULL, NULL, NULL);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }
    else
    {
        rc = GNCDB_createTable(multiCon->db, "supplier", 7,
            "s_suppkey", FIELDTYPE_INTEGER, 0, 1, min, max,
            "s_name", FIELDTYPE_VARCHAR, 0, 0, 0.0, 25.0,
            "s_address", FIELDTYPE_VARCHAR, 0, 0, 0.0, 40.0,
            "s_nationkey", FIELDTYPE_INTEGER, 0, 0, min, max,
            "s_phone", FIELDTYPE_VARCHAR, 0, 0, 0.0, 15.0,
            "s_acctbal", FIELDTYPE_REAL, 0, 0, min, max,
            "s_comment", FIELDTYPE_VARCHAR, 0, 0, 0.0, 101.0,
            10000);
    
        rc = GNCDB_createTable(multiCon->db, "mutitable", 21,
            "mukey", FIELDTYPE_INTEGER, 0, 1, min, max,
            "name1", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name2", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name3", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name4", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name5", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name6", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name7", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name8", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name9", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name10", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name11", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name12", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name13", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name14", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name15", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name16", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name17", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name18", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name19", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
            "name20", FIELDTYPE_VARCHAR, 0, 0, 0.0, 3.0,
        10000);
    }

    
    return rc;
}

int insertSameTableModule()
{
    int rc = 0;
    int i = 0;
    char supplierPath[100] = {0};
    FILE *fp = NULL;
    char sql[1024] = {0};

    sprintf(supplierPath, "%s%s", optpchpath, opsupplierFileName);
	fp = fopen(supplierPath, "r");
	if (fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < SAME_TABLE_INSERT_NUM; ++i)
	{
		fscanf(fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&sqsuppliertbl.suppkey,
			sqsuppliertbl.name,
			sqsuppliertbl.address,
			&sqsuppliertbl.nationkey,
			sqsuppliertbl.phone,
			&sqsuppliertbl.acctbal,
			sqsuppliertbl.comment);
        if(MULTICON_SQL)
        {
            sprintf(sql, "insert into supplier values(%d, '%s', '%s', %d, '%s', %lf, '%s')",
                sqsuppliertbl.suppkey,
                sqsuppliertbl.name,
                sqsuppliertbl.address,
                sqsuppliertbl.nationkey,
                sqsuppliertbl.phone,
                sqsuppliertbl.acctbal,
                sqsuppliertbl.comment);
            rc = GNCDB_exec(multiCon->db, sql, NULL, NULL, NULL);
        }
        else
        {
            rc = GNCDB_insert(multiCon->db, NULL, "supplier",
                sqsuppliertbl.suppkey,
                sqsuppliertbl.name,
                sqsuppliertbl.address,
                sqsuppliertbl.nationkey,
                sqsuppliertbl.phone,
                sqsuppliertbl.acctbal,
                sqsuppliertbl.comment);
            if (rc != GNCDB_SUCCESS)
            {
                fclose(fp);
                return rc;
            }
        }
	}
    fclose(fp);

    /* 同表多个线程更新同一数据情况 */

    for(i = 0; i < 20; ++i)
    {
        if(MULTICON_SQL)
        {
            sprintf(sql, "insert into mutitable values(%d, '00', '00', '00', '00', '00',"
                "'00', '00', '00', '00', '00', "
                "'00', '00', '00', '00', '00', "
                "'00', '00', '00', '00', '00');", i);
            rc = GNCDB_exec(multiCon->db, sql, NULL, NULL, NULL);
        }
        else
        {
            rc = GNCDB_insert(multiCon->db, NULL, "mutitable",
                i, "00", "00", "00", "00", "00", 
                "00", "00", "00", "00", "00", 
                "00", "00", "00", "00", "00", 
                "00", "00", "00", "00", "00"
                );
        }
        
    }

    return 0;
}

int locationFileFp(FILE** fp)
{
    int i = 0;
    char supplierPath[100] = {0};

    sprintf(supplierPath, "%s%s", optpchpath, opsupplierFileName);
	*fp = fopen(supplierPath, "r");
	if (*fp == NULL)
	{
		return -1;
	}
	for (i = 0; i < SAME_TABLE_INSERT_NUM; ++i)
	{
		fscanf(*fp, "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
			&sqsuppliertbl.suppkey,
			sqsuppliertbl.name,
			sqsuppliertbl.address,
			&sqsuppliertbl.nationkey,
			sqsuppliertbl.phone,
			&sqsuppliertbl.acctbal,
			sqsuppliertbl.comment);
	}
    return 0;
}

int insertMultiSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index;
    char tableName[20] = {0};
    int i = 0;
    int row = 0;
    op_Fun->rowCount = 0;

    sprintf(tableName, "supplier_%d", num);

    strcpy(op_Fun->tableName, tableName);

    for(i = 0; i < NUM_INSERT; ++i)
    {
        if(MULTICON_SQL)
        {
            char sql[1024] = {0};
            sprintf(sql, "insert into %s values(%d, '%s', '%s', %d, '%s', %lf, '%s')",
                tableName,
                supplier[i].suppkey,
                supplier[i].name,
                supplier[i].address,
                supplier[i].nationkey,
                supplier[i].phone,
                supplier[i].acctbal,
               supplier[i].comment);
            rc = GNCDB_exec(multiCon->db, sql, NULL, NULL, NULL);
            if(rc == GNCDB_SUCCESS)
            {
                row = 1;
            }
        }
        else
        {
            rc = GNCDB_insert(multiCon->db, &row, tableName,
                supplier[i].suppkey,
                supplier[i].name,
                supplier[i].address,
                supplier[i].nationkey,
                supplier[i].phone,
                supplier[i].acctbal,
                supplier[i].comment
                );
        }
        
        if (rc == GNCDB_SUCCESS || rc == GNCDB_LEAFPAGE_NOT_FOUND)
        {
            op_Fun->rowCount += row;
        }else{
            op_Fun->rc = rc;
			return rc;
		}

    }
    op_Fun->rc = rc;
    return rc;
}

int selectMultiSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index % NUM_CONDITION;
    char tableName[20] = {0};
    int row = 0;
    op_Fun->rowCount = 0;

    sprintf(tableName, "supplier_%d", op_Fun->index);
    strcpy(op_Fun->tableName, tableName);

    //strcpy(op_Fun->condition[0], selectCondition[num]);
    if(MULTICON_SQL)
    {
        char sql[1024] = {0};
        strcpy(op_Fun->condition[0], sqlselectCondition[num]);
        sprintf(sql, "select * from %s where %s", tableName, op_Fun->condition[0]);
        rc = GNCDB_exec(multiCon->db, sql, multestCallBackSqlcount, &row, NULL);
    }
    else
    {
        strcpy(op_Fun->condition[0], selectCondition[num]);
        rc = GNCDB_select(multiCon->db, NULL, &row, NULL, 1, 0, 1, tableName, op_Fun->condition[0]);
    }
    
    op_Fun->rowCount = row;
    op_Fun->rc = rc;
    return rc;
}

int updateMultiSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index % NUM_CONDITION;
    char tableName[20] = {0};
    int row = 0;
    op_Fun->rowCount = 0;

    sprintf(tableName, "supplier_%d", op_Fun->index);
    strcpy(op_Fun->tableName, tableName);

    if(MULTICON_SQL)
    {
        char sql[1024] = {0};
        strcpy(op_Fun->condition[0], sqlupdateCondition[num]);
        strcpy(op_Fun->condition[1], sqlupdateCondition[num + NUM_CONDITION]);
        sprintf(sql, "update %s set s_phone = '1111111111' where %s AND %s", tableName, op_Fun->condition[0], op_Fun->condition[1]);
        rc = GNCDB_exec(multiCon->db, sql, mulcallBackSQLGetRows, &row, NULL);
    }
    else
    {
        strcpy(op_Fun->condition[0], updateCondition[num]);
        strcpy(op_Fun->condition[1], updateCondition[num + NUM_CONDITION]);
        rc = GNCDB_update(multiCon->db, &row, tableName, 1, 2, "s_phone", "1111111111", op_Fun->condition[0], op_Fun->condition[1]);
    }
    
    op_Fun->rowCount = row;
    op_Fun->rc = rc;
    return rc;
}

int deleteMultiSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index % NUM_CONDITION;
    char tableName[20] = {0};
    int row = 0;
    op_Fun->rowCount = 0;

    sprintf(tableName, "supplier_%d", op_Fun->index);
    strcpy(op_Fun->tableName, tableName);

    if(MULTICON_SQL)
    {
        char sql[1024] = {0};
        strcpy(op_Fun->condition[0], sqldeleteCondition[num]);
        strcpy(op_Fun->condition[1], sqldeleteCondition[num + NUM_CONDITION]);
        sprintf(sql, "delete from %s where %s AND %s", tableName, op_Fun->condition[0], op_Fun->condition[1]);
        rc = GNCDB_exec(multiCon->db, sql, mulcallBackSQLGetRows, &row, NULL);
    }
    else
    {
        strcpy(op_Fun->condition[0], deleteCondition[num]);
        strcpy(op_Fun->condition[1], deleteCondition[num + NUM_CONDITION]);
        rc = GNCDB_delete(multiCon->db, &row, tableName, 2, op_Fun->condition[0], op_Fun->condition[1]);
    }
    op_Fun->rowCount = row;
    op_Fun->rc = rc;
    return rc;
}

/* 同表 */
int insertSameSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    char tableName[20] = "supplier";
    int i = 0;
    int row = 0;
    op_Fun->rowCount = 0;
    
    strcpy(op_Fun->tableName, tableName);

    for(i = 0; i < NUM_INSERT; ++i)
    {
        row = 0;
        if(MULTICON_SQL)
        {
            char sql[256] = {0};
            sprintf(sql, "insert into %s values(%d, '%s', '%s', %d, '%s', %f, '%s');", tableName, 
                op_Fun->supplier[i].suppkey,
                op_Fun->supplier[i].name,
                op_Fun->supplier[i].address,
                op_Fun->supplier[i].nationkey,
                op_Fun->supplier[i].phone,
                op_Fun->supplier[i].acctbal,
                op_Fun->supplier[i].comment);
            rc = GNCDB_exec(multiCon->db, sql, NULL, NULL, NULL);
            if(rc == 0)
            {
                row = 1;
            }
        }
        else
        {
            rc = GNCDB_insert(multiCon->db, &row, tableName,
                op_Fun->supplier[i].suppkey,
                op_Fun->supplier[i].name,
                op_Fun->supplier[i].address,
                op_Fun->supplier[i].nationkey,
                op_Fun->supplier[i].phone,
                op_Fun->supplier[i].acctbal,
                op_Fun->supplier[i].comment
                );
        }
        if (rc == GNCDB_SUCCESS || rc == GNCDB_LEAFPAGE_NOT_FOUND)
        {
            op_Fun->rowCount += row;
        }else{
            op_Fun->rc = rc;
			return rc;
		}

    }
    op_Fun->rc = rc;
    return rc;
}

int selectSameSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index % NUM_CONDITION;
    char tableName[20] = "supplier";
    int row = 0;
    op_Fun->rowCount = 0;

    strcpy(op_Fun->tableName, tableName);
    if(MULTICON_SQL)
    {
        char sql[256] = {0};
        strcpy(op_Fun->condition[0], sqlselectCondition[num]);
        sprintf(sql, "select * from %s where %s and %s and %s;", tableName, op_Fun->condition[0], op_Fun->condition[1], op_Fun->condition[2]);
        rc = GNCDB_exec(multiCon->db, sql, multestCallBackSqlcount, &row, NULL);
    }
    else
    {
        strcpy(op_Fun->condition[0], selectCondition[num]);
        rc = GNCDB_select(multiCon->db, NULL, &row, NULL, 1, 0, 3, tableName, op_Fun->condition[0], op_Fun->condition[1], op_Fun->condition[2]);
    }
    op_Fun->rowCount = row;
    op_Fun->rc = rc;
    return rc;
}

int updateSameSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index % (NUM_CONDITION * 2);
    char tableName[20] = "supplier";
    int row = 0;
    op_Fun->rowCount = 0;
    strcpy(op_Fun->tableName, tableName);
    if(MULTICON_SQL)
    {
        char sql[256] = {0};
        strcpy(op_Fun->condition[0], sqlupdateCondition[num]);
        sprintf(sql, "update %s set s_phone = '1111111111' where %s and %s and %s;", tableName, op_Fun->condition[0], op_Fun->condition[1], op_Fun->condition[2]);
        rc = GNCDB_exec(multiCon->db, sql, mulcallBackSQLGetRows, &row, NULL);
    }   
    else
    {
        strcpy(op_Fun->condition[0], updateCondition[num]);
        rc = GNCDB_update(multiCon->db, &row, tableName, 1, 3, "s_phone", "1111111111", op_Fun->condition[0], op_Fun->condition[1], op_Fun->condition[2]);
    }
    op_Fun->rowCount = row;
    op_Fun->rc = rc;
    return rc;
}

int deleteSameSupplierTable(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index % (NUM_CONDITION * 2);
    char tableName[20] = "supplier";
    int row = 0;
    op_Fun->rowCount = 0;
    strcpy(op_Fun->tableName, tableName);
    if(MULTICON_SQL)
    {
        char sql[256] = {0};
        strcpy(op_Fun->condition[0], sqldeleteCondition[num]);
        sprintf(sql, "delete from %s where %s and %s and %s;", tableName, op_Fun->condition[0], op_Fun->condition[1], op_Fun->condition[2]);
        rc = GNCDB_exec(multiCon->db, sql, mulcallBackSQLGetRows, &row, NULL);
    }
    else
    {
        strcpy(op_Fun->condition[0], deleteCondition[num]);
        rc = GNCDB_delete(multiCon->db, &row, tableName, 3, op_Fun->condition[0], op_Fun->condition[1], op_Fun->condition[2]);
    }
    op_Fun->rowCount = row;
    op_Fun->rc = rc;
    return rc;
}

int updateSameTableTuple(void* arg)
{
    int rc = 0;
    OP_FUN* op_Fun = (OP_FUN*)arg;
    int num = op_Fun->index + 1;
    char tableName[20] = "mutitable";
    int row = 0;
    char fieldName[20] = {0};
    op_Fun->rowCount = 0;
    strcpy(op_Fun->tableName, tableName);

    sprintf(op_Fun->condition[0], "mukey=5");
    sprintf(fieldName, "name%d", num);
    if(MULTICON_SQL)
    {
        char sql[256] = {0};
        sprintf(sql, "update %s set %s = '11' where %s;", tableName, fieldName, op_Fun->condition[0]);
        rc = GNCDB_exec(multiCon->db, sql, mulcallBackSQLGetRows, &row, NULL);
    }
    else
    {
        rc = GNCDB_update(multiCon->db, &row, tableName, 1, 1, fieldName, "11", op_Fun->condition[0]);
    }
    op_Fun->rowCount = row;
    op_Fun->rc = rc;
    return rc;
}