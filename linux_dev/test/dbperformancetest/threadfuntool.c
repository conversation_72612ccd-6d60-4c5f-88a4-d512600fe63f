#include "dbsqlitetest.h"

void* gncdbExec(void* arg){
    int rc = 0;
    gncdbExecArg* pArg = (gncdbExecArg*)arg;
    if(pArg->opSelect == 0){}
    else if(pArg->opSelect == 1){}
    else if(pArg->opSelect == 2){}
    rc = GNCDB_exec(pArg->pDb, pArg->sql, NULL, NULL, NULL);
    if(rc){
        printf("GNCDB_select error:%d\n", rc);
    }
    return NULL;
}
double gncdbConcurrentExecTime(GNCDB *perDB, char **sql, int pthreadcount){
	int rc = 0;
	double time_used = 0;
    int i = 0;
	struct timespec start, end;
    
    pthread_t* id = (pthread_t*)my_malloc(sizeof(pthread_t) * pthreadcount);
	//printf("%s\n", sql);
    clock_gettime(CLOCK_MONOTONIC, &start);
    for(; i < pthreadcount; i++){
        gncdbExecArg arg;
        arg.pDb = perDB;
        arg.sql = sql[i];
        rc = pthread_create(&id[i], NULL, gncdbExec, (void*)(&arg));
        if(rc){
            printf("pthread_create error:%s\n", strerror(rc));
            return -1;
        }
    }
    for(i = 0; i < pthreadcount; i++){
        pthread_join(id[i], NULL);
    }
    clock_gettime(CLOCK_MONOTONIC, &end);

    my_free(id);
	
	time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
	return time_used;
}
void* sqliteExec(void* arg){
    int rc = 0;
    sqliteExecArg* pArg = (sqliteExecArg*)arg;
    if(pArg->opSelect == 0){}
    else if(pArg->opSelect == 1){}
    else if(pArg->opSelect == 2){}
    rc = sqlite3_exec(pArg->pDb, pArg->sql, NULL, 0, NULL);
    if(rc){
        printf("sqlite3_exec error:%d\n", rc);
    }
    return NULL;
}
double sqliteConcurrentExecTime(sqlite3 *pDb, char **sql, int pthreadcount){
	int rc = 0;
	double time_used = 0;
    int i = 0;
	struct timespec start, end;
    
    pthread_t* id = (pthread_t*)my_malloc(sizeof(pthread_t) * pthreadcount);
	//printf("%s\n", sql);
    clock_gettime(CLOCK_MONOTONIC, &start);
    for(; i < pthreadcount; i++){
        sqliteExecArg arg;
        arg.pDb = pDb;
        arg.sql = sql[i];
        rc = pthread_create(&id[i], NULL, sqliteExec, (void*)(&arg));
        if(rc){
            printf("pthread_create error:%s\n", strerror(rc));
            return -1;
        }
    }
    for(i = 0; i < pthreadcount; i++){
        pthread_join(id[i], NULL);
    }
    clock_gettime(CLOCK_MONOTONIC, &end);

    my_free(id);
	
	time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
	return time_used;
}


double gncdbAPIConcurrentExecTime(GNCDB *perDB, gncdbApiExecArg* argArr, int pthreadcount){
	int rc = 0;
	double time_used = 0;
    int i = 0;
	struct timespec start, end;
    
    pthread_t* id = (pthread_t*)my_malloc(sizeof(pthread_t) * pthreadcount);
	//printf("%s\n", sql);
    clock_gettime(CLOCK_MONOTONIC, &start);
    for(; i < pthreadcount; i++){
        gncdbApiExecArg* arg = argArr + i;
        arg->pDb = perDB;
        rc = pthread_create(&id[i], NULL, tableAllSelect, (void*)arg);
        if(rc){
            printf("pthread_create error:%s\n", strerror(rc));
            return -1;
        }
    }
    for(i = 0; i < pthreadcount; i++){
        pthread_join(id[i], NULL);
    }
    clock_gettime(CLOCK_MONOTONIC, &end);

    my_free(id);
	
	time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
	return time_used;
}

void* tableAllSelect(void* arg){
    int rc = 0;
    gncdbApiExecArg* pArg = (gncdbApiExecArg*)arg;
    if(pArg->opSelect == 0){
        rc = GNCDB_select(pArg->pDb, NULL, NULL, NULL, 1, 0, pArg->conditionNum, pArg->tableName, 
            pArg->condition[0], pArg->condition[1], pArg->condition[2], pArg->condition[3]);
    }
    else if(pArg->opSelect == 1){
        rc = GNCDB_update(pArg->pDb, NULL, pArg->tableName, 1, pArg->conditionNum, pArg->attrName, pArg->attrValue, 
            pArg->condition[0], pArg->condition[1], pArg->condition[2], pArg->condition[3]);
    }
    else if(pArg->opSelect == 2){
        rc = GNCDB_delete(pArg->pDb, NULL, pArg->tableName, pArg->conditionNum, 
            pArg->condition[0], pArg->condition[1], pArg->condition[2], pArg->condition[3]); 
    }
    
    if(rc){
        printf("GNCDB_select error:%d\n", rc);
    }
    return NULL;
}