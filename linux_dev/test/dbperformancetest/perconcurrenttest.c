#include <pthread.h>
#include "dbsqlitetest.h"

int readConcurrenceTest1(int pthreadNum);
int readConcurrenceTest2(int pthreadNum);

int readWriteConcurrenceTest(int pthreadNum);

int writeConcurrenceTest1(int pthreadNum);
int writeConcurrenceTest2(int pthreadNum);
int writeConcurrenceTest3(int pthreadNum);
int writeConcurrenceTest4(int pthreadNum);
int writeConcurrenceTest5(int pthreadNum);
int writeConcurrenceTest6(int pthreadNum);


int concurrencePerTest(int pthreadNum)
{
    // GNCDB* perDB = NULL;
	// sqlite3 *ppDb = NULL;
    int x = perEx.x;
 
	// perTestDBInit(&perDB);
	// perTestDataInit(perDB, SELECT_DATA_ININT, 0);
	// sqliteDBInit(&ppDb);
	// sqliteDataInit(ppDb, SELECT_DATA_ININT, 0);
	// closeDb(perDB, ppDb);
    
    readConcurrenceTest1(pthreadNum);
    readConcurrenceTest2(pthreadNum);
    // writeConcurrenceTest1(pthreadNum);
    // writeConcurrenceTest2(pthreadNum);
    
	writeOPType("并发测试", x);
    return 0;
}

int readConcurrenceTest1(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0;
	// int falseNum = 0;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
		"多线程全表查询", "SELECT * FROM part",
		"查询part表所有内容",
		PTR_arg.TestCount, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX,
		0.0, DBL_MIN, DBL_MAX,
        0
		}; 

        double time_used_singel, time_used_sqlite;

        char *query[10];
        gncdbApiExecArg arg[10]; 
        for(i = 0; i < pthreadNum; i++){
            query[i] = "SELECT * FROM part";
            strcpy(arg[i].tableName, "part");
            arg[i].opSelect = 0;
            arg[i].conditionNum = 0;
        }
        for(i = 0; i < PTR_arg.TestCount; i++){

            rc = openDb(&perDB, &ppDb, 1);
            if(rc != 0){
                printf("openDb error\n");
                continue;
            }
    
            // time_per_read = 0.0;
            // time_per_write = 0.0;
    
            if(PERSQLTEST){
                time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
            } else {
                
                time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
            }
            getTimeUsed(&result, time_used_singel);
    

            rc = closeDb(perDB, ppDb, 1);
            if(rc)
            {
                printf("closeDb error\n");
                continue;
            }
            rc = openDb(&perDB, &ppDb, 2);
            if(rc)
            {
                printf("openDb error\n");
                continue;
            }
		
            time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
            if (time_used_sqlite > 0){
                result.time_sqlite_avg += time_used_sqlite;
                if(result.time_sqlite_max < time_used_sqlite){
                    result.time_sqlite_max = time_used_sqlite;
                }
                if(result.time_sqlite_min > time_used_sqlite){
                    result.time_sqlite_min = time_used_sqlite;
                }
            }
            else 
                printf("sqlite insert error\n");
            rc = closeDb(perDB, ppDb, 2);
            if(rc){
                printf("close db error\n");
                continue;
            }
        }
        writeToCSV(result);
        return rc ;
}

int readConcurrenceTest2(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0, j = 0;
	// int falseNum = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
		"多线程主键查询", "SELECT * FROM part WHERE p_partkey=??",
		"主键查询part表内容",
		PTR_arg.TestCount, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX,
		0.0,  DBL_MIN, DBL_MAX,
        0
		}; 

        double time_used_singel, time_used_sqlite;

        char *query[10];
        gncdbApiExecArg arg[10]; 
        for(i = 0; i < PTR_arg.TestCount; i++){

            rc = openDb(&perDB, &ppDb, 1);
            if(rc != 0){
                printf("openDb error\n");
                continue;
            }

            for(j = 0; j < pthreadNum; j++){
                token = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
                query[j] = my_malloc0(sizeof(char) * 256);
                sprintf(query[j], "SELECT * FROM part WHERE p_partkey=%s;", token);
    
                strcpy(arg[j].tableName, "part");
                arg[j].opSelect = 0;
                arg[j].conditionNum = 1;
                sprintf(arg[j].condition[0], "p_partkey=%s", token);
            }
    
            // time_per_read = 0.0;
            // time_per_write = 0.0;
    
            if(PERSQLTEST){
                time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
            } else {
                
                time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
            }
            getTimeUsed(&result, time_used_singel);
    
            rc = closeDb(perDB, ppDb, 1);
            if(rc)
            {
                printf("closeDb error\n");
                continue;
            }
            rc = openDb(&perDB, &ppDb, 2);
            if(rc)
            {
                printf("openDb error\n");
                continue;
            }
		
            time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
            if (time_used_sqlite > 0){
                result.time_sqlite_avg += time_used_sqlite;
                if(result.time_sqlite_max < time_used_sqlite){
                    result.time_sqlite_max = time_used_sqlite;
                }
                if(result.time_sqlite_min > time_used_sqlite){
                    result.time_sqlite_min = time_used_sqlite;
                }
            }
            else 
                printf("sqlite insert error\n");
            rc = closeDb(perDB, ppDb, 2);
            if(rc){
                printf("close db error\n");
                continue;
            }

            for(j = 0; j < pthreadNum; j++){
                my_free(query[j]);
            }
        }
        writeToCSV(result);
        return rc ;
}


int readWriteConcurrenceTest(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0, j = 0;
	// int falseNum = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
		"多线程读写操作", "SELECT * FROM part WHERE p_partkey=??",
		"主键读写操作part表内容",
		PTR_arg.TestCount, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX,
		0.0,  DBL_MIN, DBL_MAX,
        0
		}; 

        double time_used_singel, time_used_sqlite;

        char *query[10];
        gncdbApiExecArg arg[10]; 
        for(i = 0; i < PTR_arg.TestCount; i++){

            rc = openDb(&perDB, &ppDb, 1);
            if(rc != 0){
                printf("openDb error\n");
                continue;
            }

            for(j = 0; j < pthreadNum; j++){
                token = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
                query[j] = my_malloc0(sizeof(char) * 256);
                if(j == 3 || j == 7){
                    sprintf(query[j], "UPDATE part SET p_name = 'gncbdupdate' WHERE p_partkey<=%s;", token);

                    strcpy(arg[j].tableName, "part");
                    arg[j].opSelect = 1;
                    arg[j].conditionNum = 1;
                    sprintf(arg[j].condition[0], "p_partkey<=%s", token);
                    strcpy(arg[j].attrName, "p_name");
                    strcpy(arg[j].attrValue, "gncbdupdate");
                }
                else if(j == 4 || j == 8){
                    sprintf(query[j], "DELETE FROM part WHERE p_partkey<=%s;", token);

                    strcpy(arg[j].tableName, "part");
                    arg[j].opSelect = 2;
                    arg[j].conditionNum = 1;
                    sprintf(arg[j].condition[0], "p_partkey<=%s", token);
                }
                else{
                    sprintf(query[j], "SELECT * FROM part WHERE p_partkey<=%s;", token);
        
                    strcpy(arg[j].tableName, "part");
                    arg[j].opSelect = 0;
                    arg[j].conditionNum = 1;
                    sprintf(arg[j].condition[0], "p_partkey<=%s", token);
                }
            }
    
            // time_per_read = 0.0;
            // time_per_write = 0.0;
    
            if(PERSQLTEST){
                time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
            } else {
                
                time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
            }
            getTimeUsed(&result, time_used_singel);
    
            rc = closeDb(perDB, ppDb, 1);
            if(rc)
            {
                printf("closeDb error\n");
                continue;
            }
            rc = openDb(&perDB, &ppDb, 2);
            if(rc)
            {
                printf("openDb error\n");
                continue;
            }
		
            time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
            if (time_used_sqlite > 0){
                result.time_sqlite_avg += time_used_sqlite;
                if(result.time_sqlite_max < time_used_sqlite){
                    result.time_sqlite_max = time_used_sqlite;
                }
                if(result.time_sqlite_min > time_used_sqlite){
                    result.time_sqlite_min = time_used_sqlite;
                }
            }
            else 
                printf("sqlite insert error\n");
            rc = closeDb(perDB, ppDb, 2);
            if(rc){
                printf("close db error\n");
                continue;
            }

            for(j = 0; j < pthreadNum; j++){
                my_free(query[j]);
            }

        }
        writeToCSV(result);
        return rc ;
}


int writeConcurrenceTest1(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0, j = 0;
	// int falseNum = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;


	struct PerTestRes result = {
		"多线程主键等值条件更新", "UPDATE part SET p_name = 'gncbdupdate' WHERE partkey=?",
		"随机主键等值条件更新part表中某条数据",
		PTR_arg.TestCount, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX,
		0.0, DBL_MIN, DBL_MAX,
        0
		}; 

	double time_used_singel, time_used_sqlite = 0;

    char *query[10];
    gncdbApiExecArg arg[10]; 

	for(i = 0; i < PTR_arg.TestCount; i++){
		rc = openDb(&perDB, &ppDb, 1);
        if(rc != 0){
            printf("openDb error\n");
            continue;
        }

		// time_per_read = 0.0;
		// time_per_write = 0.0;
		// 生成随机数查询，获取查询条件		

        for(j = 0; j < pthreadNum; j++){
            token = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
            query[j] = my_malloc0(sizeof(char) * 256);
            sprintf(query[j], "UPDATE part SET p_name = 'gncbdupdate' WHERE p_partkey=%s;", token);

            strcpy(arg[j].tableName, "part");
            arg[j].opSelect = 1;
            arg[j].conditionNum = 1;
            sprintf(arg[j].condition[0], "p_partkey=%s", token);
            strcpy(arg[j].attrName, "p_name");
            strcpy(arg[j].attrValue, "gncbdupdate");
        }

        if(PERSQLTEST){
            time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
        } else {
            
            time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
        }
        getTimeUsed(&result, time_used_singel);

        rc = closeDb(perDB, ppDb, 1);
		if(rc)
		{
			printf("closeDb error\n");
			continue;
		}
        rc = openDb(&perDB, &ppDb, 2);
		if(rc)
		{
			printf("openDb error\n");
			continue;
		}
		
        time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
        if (time_used_sqlite > 0){
			result.time_sqlite_avg += time_used_sqlite;
			if(result.time_sqlite_max < time_used_sqlite){
				result.time_sqlite_max = time_used_sqlite;
			}
			if(result.time_sqlite_min > time_used_sqlite){
				result.time_sqlite_min = time_used_sqlite;
			}
		}
        else 
            printf("sqlite insert error\n");
        rc = closeDb(perDB, ppDb, 2);
		if(rc){
		    printf("close db error\n");
			continue;
		}

        for(j = 0; j < pthreadNum; j++){
            my_free(query[j]);
        }
	}
	writeToCSV(result);
    return rc;
}

int writeConcurrenceTest2(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0, j = 0;
	// int falseNum = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
        "多线程主键范围条件更新", "UPDATE part SET p_name = 'gncbdupdate' WHERE partkey>=??",
        "随机主键范围条件更新part表中数据",
        PTR_arg.TestCount, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX,
        0.0, DBL_MIN, DBL_MAX,
        0
        };

	double time_used_singel, time_used_sqlite = 0;

    char *query[10];
    gncdbApiExecArg arg[10]; 

	for(i = 0; i < PTR_arg.TestCount; i++){
		rc = openDb(&perDB, &ppDb, 1);
        if(rc != 0){
            printf("openDb error\n");
            continue;
        }

		// time_per_read = 0.0;
		// time_per_write = 0.0;
		// 生成随机数查询，获取查询条件		

        for(j = 0; j < pthreadNum; j++){
            token = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
            query[j] = my_malloc0(sizeof(char) * 256);
            sprintf(query[j], "UPDATE part SET p_name = 'gncbdupdate' WHERE p_partkey>=%s;", token);

            strcpy(arg[j].tableName, "part");
            arg[j].opSelect = 1;
            arg[j].conditionNum = 1;
            sprintf(arg[j].condition[0], "p_partkey>=%s", token);
            strcpy(arg[j].attrName, "p_name");
            strcpy(arg[j].attrValue, "gncbdupdate");
        }

        if(PERSQLTEST){
            time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
        } else {
            
            time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
        }
        getTimeUsed(&result, time_used_singel);

        rc = closeDb(perDB, ppDb, 1);
		if(rc)
		{
			printf("closeDb error\n");
			continue;
		}
        rc = openDb(&perDB, &ppDb, 2);
		if(rc)
		{
			printf("openDb error\n");
			continue;
		}
		
        time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
        if (time_used_sqlite > 0){
			result.time_sqlite_avg += time_used_sqlite;
			if(result.time_sqlite_max < time_used_sqlite){
				result.time_sqlite_max = time_used_sqlite;
			}
			if(result.time_sqlite_min > time_used_sqlite){
				result.time_sqlite_min = time_used_sqlite;
			}
		}
        else 
            printf("sqlite insert error\n");
        rc = closeDb(perDB, ppDb, 2);
		if(rc){
		    printf("close db error\n");
			continue;
		}

        for(j = 0; j < pthreadNum; j++){
            my_free(query[j]);
        }
	}
	writeToCSV(result);
    return rc;
}

int writeConcurrenceTest3(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0, j = 0;
	// int falseNum = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
        "多线程非主键范围条件更新", "UPDATE part SET p_name = 'gncbdupdate' WHERE p_retailprice??",
        "随机非主键范围条件更新part表中数据",
        PTR_arg.TestCount, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX,
        0.0, DBL_MIN, DBL_MAX,
        0
        };

	double time_used_singel, time_used_sqlite = 0;

    char *query[10];
    gncdbApiExecArg arg[10]; 

	for(i = 0; i < PTR_arg.TestCount; i++){
		rc = openDb(&perDB, &ppDb, 1);
        if(rc != 0){
            printf("openDb error\n");
            continue;
        }

		// time_per_read = 0.0;
		// time_per_write = 0.0;
		// 生成随机数查询，获取查询条件		

        for(j = 0; j < pthreadNum; j++){
            token = getRandomColumnValue("part", 8, UPDATE_DATA_ININT);
            query[j] = my_malloc0(sizeof(char) * 256);
            sprintf(query[j], "UPDATE part SET p_name = 'gncbdupdate' WHERE p_retailprice<=%s;", token);

            strcpy(arg[j].tableName, "part");
            arg[j].opSelect = 1;
            arg[j].conditionNum = 1;
            sprintf(arg[j].condition[0], "p_retailprice<=%s", token);
            strcpy(arg[j].attrName, "p_name");
            strcpy(arg[j].attrValue, "gncbdupdate");
        }

        if(PERSQLTEST){
            time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
        } else {
            
            time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
        }
        getTimeUsed(&result, time_used_singel);

        rc = closeDb(perDB, ppDb, 1);
		if(rc)
		{
			printf("closeDb error\n");
			continue;
		}
        rc = openDb(&perDB, &ppDb, 2);
		if(rc)
		{
			printf("openDb error\n");
			continue;
		}
		
        time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
        if (time_used_sqlite > 0){
			result.time_sqlite_avg += time_used_sqlite;
			if(result.time_sqlite_max < time_used_sqlite){
				result.time_sqlite_max = time_used_sqlite;
			}
			if(result.time_sqlite_min > time_used_sqlite){
				result.time_sqlite_min = time_used_sqlite;
			}
		}
        else 
            printf("sqlite insert error\n");
        rc = closeDb(perDB, ppDb, 2);
		if(rc){
		    printf("close db error\n");
			continue;
		}

        for(j = 0; j < pthreadNum; j++){
            my_free(query[j]);
        }
	}
	writeToCSV(result);
    return rc;
}

int writeConcurrenceTest4(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0, j = 0;
	// int falseNum = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
		"多线程主键删除", "DELETE FROM part WHERE p_partkey=?",
		"随机主键等值条件删除part表中指定某条数据",
		PTR_arg.TestCount, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX, 
		0.0, DBL_MIN, DBL_MAX,
		0.0, DBL_MIN, DBL_MAX,
        0
		}; 

	double time_used_singel, time_used_sqlite = 0;

    char *query[10];
    gncdbApiExecArg arg[10]; 

	for(i = 0; i < PTR_arg.TestCount; i++){
		rc = openDb(&perDB, &ppDb, 1);
        if(rc != 0){
            printf("openDb error\n");
            continue;
        }

		// time_per_read = 0.0;
		// time_per_write = 0.0;
		// 生成随机数查询，获取查询条件		

        for(j = 0; j < pthreadNum; j++){
            token = getRandomColumnValue("part", 1, DELETE_DATA_ININT);
            query[j] = my_malloc0(sizeof(char) * 256);
            sprintf(query[j], "DELETE FROM part WHERE p_partkey=%s;", token);

            strcpy(arg[j].tableName, "part");
            arg[j].opSelect = 2;
            arg[j].conditionNum = 1;
            sprintf(arg[j].condition[0], "p_partkey=%s", token);
        }

        if(PERSQLTEST){
            time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
        } else {
            
            time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
        }
        getTimeUsed(&result, time_used_singel);

        rc = closeDb(perDB, ppDb, 1);
		if(rc)
		{
			printf("closeDb error\n");
			continue;
		}
        rc = openDb(&perDB, &ppDb, 2);
		if(rc)
		{
			printf("openDb error\n");
			continue;
		}
		
        time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
        if (time_used_sqlite > 0){
			result.time_sqlite_avg += time_used_sqlite;
			if(result.time_sqlite_max < time_used_sqlite){
				result.time_sqlite_max = time_used_sqlite;
			}
			if(result.time_sqlite_min > time_used_sqlite){
				result.time_sqlite_min = time_used_sqlite;
			}
		}
        else 
            printf("sqlite insert error\n");
        rc = closeDb(perDB, ppDb, 2);
		if(rc){
		    printf("close db error\n");
			continue;
		}

        for(j = 0; j < pthreadNum; j++){
            my_free(query[j]);
        }
	}
	writeToCSV(result);
    return rc;
}

int writeConcurrenceTest5(int pthreadNum)
{
    int rc = 0;
    // int rows = 0;
	int i = 0, j = 0;
	// int falseNum = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
        "多线程主键范围条件删除", "DELETE FROM part WHERE p_partkey>=??",
        "随机主键范围条件删除part表中数据",
        PTR_arg.TestCount, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX,
        0.0, DBL_MIN, DBL_MAX,
        0
        };

	double time_used_singel, time_used_sqlite = 0;

    char *query[10];
    gncdbApiExecArg arg[10]; 

	for(i = 0; i < PTR_arg.TestCount; i++){
		rc = openDb(&perDB, &ppDb, 1);
        if(rc != 0){
            printf("openDb error\n");
            continue;
        }

		// time_per_read = 0.0;
		// time_per_write = 0.0;
		// 生成随机数查询，获取查询条件		

        for(j = 0; j < pthreadNum; j++){
            token = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
            query[j] = my_malloc0(sizeof(char) * 256);
            sprintf(query[j], "DELETE FROM part WHERE p_partkey>=%s;", token);

            strcpy(arg[j].tableName, "part");
            arg[j].opSelect = 2;
            arg[j].conditionNum = 1;
            sprintf(arg[j].condition[0], "p_partkey>=%s", token);
        }

        if(PERSQLTEST){
            time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
        } else {
            
            time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
        }
        getTimeUsed(&result, time_used_singel);
        
        rc = closeDb(perDB, ppDb, 1);
		if(rc)
		{
			printf("closeDb error\n");
			continue;
		}
        rc = openDb(&perDB, &ppDb, 2);
		if(rc)
		{
			printf("openDb error\n");
			continue;
		}
		

        time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
        if (time_used_sqlite > 0){
			result.time_sqlite_avg += time_used_sqlite;
			if(result.time_sqlite_max < time_used_sqlite){
				result.time_sqlite_max = time_used_sqlite;
			}
			if(result.time_sqlite_min > time_used_sqlite){
				result.time_sqlite_min = time_used_sqlite;
			}
		}
        else 
            printf("sqlite insert error\n");
        rc = closeDb(perDB, ppDb, 2);
		if(rc){
		    printf("close db error\n");
			continue;
		}

        for(j = 0; j < pthreadNum; j++){
            my_free(query[j]);
        }
	}
	writeToCSV(result);

    return rc;
}

int writeConcurrenceTest6(int pthreadNum)
{
    int rc = 0;
	int i = 0, j = 0;
    char *token;
    GNCDB* perDB;
    sqlite3 *ppDb;

    struct PerTestRes result = {
        "多线程非主键范围条件删除", "DELETE FROM part WHERE p_retailprice??",
        "随机非主键范围条件删除part表中数据",
        PTR_arg.TestCount, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX, 
        0.0, DBL_MIN, DBL_MAX,
        0.0, DBL_MIN, DBL_MAX,
        0
        };

	double time_used_singel, time_used_sqlite = 0;

    char *query[10];
    gncdbApiExecArg arg[10]; 

	for(i = 0; i < PTR_arg.TestCount; i++){
		rc = openDb(&perDB, &ppDb, 1);
        if(rc != 0){
            printf("openDb error\n");
            continue;
        }

		// time_per_read = 0.0;
		// time_per_write = 0.0;
		// 生成随机数查询，获取查询条件		

        for(j = 0; j < pthreadNum; j++){
            token = getRandomColumnValue("part", 8, UPDATE_DATA_ININT);
            query[j] = my_malloc0(sizeof(char) * 256);
            sprintf(query[j], "DELETE FROM part WHERE p_retailprice<=%s;", token);

            strcpy(arg[j].tableName, "part");
            arg[j].opSelect = 2;
            arg[j].conditionNum = 1;
            sprintf(arg[j].condition[0], "p_retailprice<=%s", token);
        }

        if(PERSQLTEST){
            time_used_singel = gncdbConcurrentExecTime(perDB, query, pthreadNum);
        } else {
            
            time_used_singel = gncdbAPIConcurrentExecTime(perDB, arg, pthreadNum);
        }
        getTimeUsed(&result, time_used_singel);
        
        rc = closeDb(perDB, ppDb, 1);
		if(rc)
		{
			printf("closeDb error\n");
			continue;
		}
        rc = openDb(&perDB, &ppDb, 2);
		if(rc)
		{
			printf("openDb error\n");
			continue;
		}
		
        time_used_sqlite = sqliteConcurrentExecTime(ppDb, query, pthreadNum);
        if (time_used_sqlite > 0){
			result.time_sqlite_avg += time_used_sqlite;
			if(result.time_sqlite_max < time_used_sqlite){
				result.time_sqlite_max = time_used_sqlite;
			}
			if(result.time_sqlite_min > time_used_sqlite){
				result.time_sqlite_min = time_used_sqlite;
			}
		}
        else 
            printf("sqlite insert error\n");
        rc = closeDb(perDB, ppDb, 2);
		if(rc){
		    printf("close db error\n");
			continue;
		}

        for(j = 0; j < pthreadNum; j++){
            my_free(query[j]);
        }
	}
	writeToCSV(result);
    return rc;
}