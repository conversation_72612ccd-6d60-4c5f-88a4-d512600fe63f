#ifndef DBSQLITETEST_H
#define DBSQLITETEST_H

#include <stdio.h>
#include <float.h>
#include <time.h>
#include "tpch.h"

#ifdef _WIN32 

#else

#include "../testdependlib/sqlitelib/sqlite3.h"

#endif

#include "gncdb.h"

#define SQLITE_CACHE_SIZE				600		//SQLite3 缓存大小
#define EXECUTION_COUNT					100
#define PERPARTROWS					10000

#define BLOB_WPT_ROWS					10000
#define BLOB_NAME						"blob.mp4"


#define INSERT_DATA_ININT					0	// 0 顺序插入 1 随机插入

extern int SELECT_DATA_ININT;
extern int DELETE_DATA_ININT; 
extern int UPDATE_DATA_ININT;


typedef struct PerTestRes
{
	char op[128];
	char sql[1024];
	char opDetail[1024];

	int exeCount;
	double time_used_avg;
	double time_used_max;
	double time_used_min;

	double time_read_avg;
	double time_read_max;
	double time_read_min;

	double time_write_avg;
	double time_write_max;
	double time_write_min;

	double time_sqlite_avg;
	double time_sqlite_max;
	double time_sqlite_min;
	int falseNum;	
}PerTestRes;

typedef struct PerTestReset{
	int MaxMinOp;
	int FileRWOp;
	int FileNameOp;
	int TestCount;
	char fileName[128];
}PerTestReset;


extern PerTestReset PTR_arg;


// extern FILE* resFp;

int perTestMain(int argc, char *argv[]);

int	perTestCSVInit();
int writeCSVPerHead(int x);
int perTestDBInit(GNCDB** perDB);
int perTestDataInit(GNCDB* perDB, int rows, int flag);
int perTestBolbDBInit(GNCDB** perDB);
int writeToCSV(struct PerTestRes result);
char* getFileSizeString(const char* filePath);
void closeCSVfile();

int sqliteDBInit(sqlite3 **ppDb);
double sqliteExecTime(sqlite3 *pDb, const char *sql);
double sqliteExecTimecallback(sqlite3 *pDb, const char *sql);
double sqliteExecTimeNULLcallback(sqlite3 *pDb, const char *sql);
int sqliteDataInit(sqlite3 *ppDb, int rows, int flag);
int sqliteBolbDBInit(sqlite3** ppDb);
int insertBlobData(sqlite3 *db, const char *key, const char *filePath);
double sqliteBlobExecTime(sqlite3 *db, const char *sql, const char *filePath);


int closeDb(GNCDB* perDB, sqlite3 *ppDb, int flag);
int openDb(GNCDB** perDB, sqlite3 **ppDb, int flag);
double gncdbSqlExecTime(GNCDB* perDB, const char *sql, int* rc);
double gncdbSqlExecTimecallback(GNCDB* perDB, const char *sql, int* rc);
double gncdbSqlExecTimeNULLcallback(GNCDB* perDB, const char *sql, int* rc);

void insertPerTest();					//插入测试
void selectPerTest();					//查询测试
void deletePerTest();					//删除测试
void updatePerTest();					//更新测试
void blobPerTest();						//大对象测试
void recoverPerTest();


int perCreateTable(GNCDB* db);

int tpcH1Test();

int generateRandomNumber(int min, int max);
char* getRandomColumnValue(const char* tableName, int column, int maxLine);
char* getRandomValue(const char* tableName,  int maxLine);
void get_cache_status(sqlite3* conn);

int getSystemInfo();


// 并发性能测试相关

typedef struct {
    sqlite3 *pDb;
	int opSelect;
    const char *sql;
}sqliteExecArg;

typedef struct {
    GNCDB *pDb;
	int opSelect;
    const char *sql;
}gncdbExecArg;

typedef struct {
    GNCDB *pDb;			
    int tableNum;
	int opSelect;			// 0:select, 1:update, 2:delete
	int conditionNum;
	char tableName[50];
	char condition[4][50];  // 条件
	char attrName[50];		// 更新属性名
	char attrValue[50];		// 更新属性值
}gncdbApiExecArg;

typedef struct {
	int x;
	int y;
	int flag;
}ExcelPoint;

extern ExcelPoint perEx;

#define CONCURRENT_TEST     5

int concurrencePerTest(int pthreadNum);
void getTimeUsed(struct PerTestRes *result, double time_used_singel);
void writeOPType(char *opType, int x);
double sqliteConcurrentExecTime(sqlite3 *pDb, char **sql, int pthreadcount);
double gncdbConcurrentExecTime(GNCDB *perDB, char **sql, int pthreadcount);
double gncdbAPIConcurrentExecTime(GNCDB *perDB, gncdbApiExecArg* argArr, int pthreadcount);

void* tableAllSelect(void* arg);


// =======================航路点数据WPT============================
// 数据表行结构体
typedef struct {
	char  sc8_wpt_ident[8];							// 航路点标识 
	double f64_lon;                                 // 航路点经度 
	double f64_lat;                                 // 航路点纬度 
}WPT;		
/* 8660000*SF + 30 */



#endif // !_TPCH_H_
