#include "gncdbconstant.h"
#include "tpch.h"
#include "tpcc.h"
#include "dbsqlitetest.h"
#include "gncdb.h"
#include <stdio.h>
#include <stdarg.h>
#include <unistd.h>
#include <string.h>
#include <ctype.h>
#include <sys/time.h>
#include <time.h>
#include "os.c"
// #include <gperftools/profiler.h>
#include "pagepool.h"
#include <sys/utsname.h>

int SELECT_DATA_ININT = 0;
int DELETE_DATA_ININT = 0;
int UPDATE_DATA_ININT = 0;

char pertpchpath[]         = "./testfile/datafile/tpcc/";
char persupplierFileName[] = "supplier.tbl";
char perpartFileName[]     = "part.tbl";
char perpartsuppFileName[] = "partsupp.tbl";
char wptFileName[]         = "WPT.txt";
char blob<PERSON>ey[10][6]        = {"AFTTM", "SRTPX", "RMGGP", "TBJVS", "QRFYP", "UCSRF", "TJIPA", "KYLZV", "MLJXB", "QHGPQ"};

static WPT wpt;
REGION     regiontbl;
NATION     nationtbl;
SUPPLIER   suppliertbl;
PART       parttbl;
PARTSUPP   partsupptbl;
CUSTOMER   customertbl;
ORDERS     orderstbl;
LINEITEM   lineitemtbl;

int myCallBack1(void *data, int argc, char **azColName, char **argv)
{
  int i;
  for (i = 0; i < argc; i++) {
    printf("%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
  }
  printf("\n");
  for (i = 0; i < argc; i++) {
    printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");
  }
  printf("\n");
  return 0;
}

int myCallBackNULL1(void *data, int argc, char **azColName, char **argv) { return 0; }

int perCreateTable(GNCDB *db)
{
  int    rc  = 0;
  double min = -100000000.0;
  double max = 100000000.0;
  /* tpch测试创建8张表 */

  /* region表		地区的信息
  R_REGIONKEY		INTEGER			主键（5个地区）
  R_NAME			VARCHAR(25)
  R_COMMENT		VARCHAR(152) 元数据中只有三列数据
  PS_SUPPLYCOST	DOUBLE
  PS_COMMENT		VARCHAR(199)*/
  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE region ("
        "r_regionkey INT PRIMARY KEY,"
        "r_name CHAR(25), "
        " r_comment CHAR(152));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "region",
        3,
        "r_regionkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "r_name",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        25.0,
        "r_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        152.0,
        5);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* nation表		国家的信息
  N_NATIONKEY		INTEGER			主键（25个国家）
  N_NAME			VARCHAR(25)
  N_REGIONKEY		INTEGER			Foreign Key to R_REGIONKEY
  N_COMMENT		VARCHAR(152)*/
  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE nation ("
        "n_nationkey INT PRIMARY KEY,"
        "n_name CHAR(25), "
        "n_regionkey INT,"
        "n_comment CHAR(152));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "nation",
        4,
        "n_nationkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "n_name",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        25.0,
        "n_regionkey",
        FIELDTYPE_INTEGER,
        0,
        0,
        min,
        max,
        "n_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        152.0,
        25);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* supplier表	供货商的信息
  S_SUPPKEY		INTEGER			主键，值范围是1到SF*10000
  S_NAME			VARCHAR(25)
  S_ADDRESS		VARCHAR(40)
  S_NATIONKEY		INTEGER			Foreign Key to N_NATIONKEY
  S_PHONE			VARCHAR(15)
  S_ACCTBAL		DOUBLE
  S_COMMENT		VARCHAR(101)*/

  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE supplier ("
        "s_suppkey INT PRIMARY KEY,"
        "s_name CHAR(25), "
        "s_address CHAR(40),"
        "s_nationkey INT,"
        "s_phone CHAR(15),"
        "s_acctbal FLOAT,"
        "s_comment CHAR(101));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "supplier",
        7,
        "s_suppkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "s_name",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        25.0,
        "s_address",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        40.0,
        "s_nationkey",
        FIELDTYPE_INTEGER,
        0,
        0,
        min,
        max,
        "s_phone",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        15.0,
        "s_acctbal",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "s_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        101.0,
        10000);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* part表		零件的信息
  P_PARTKEY		INTEGER			主键，值范围是1到SF*200000
  P_NAME			VARCHAR(55)
  P_MFGR			VARCHAR(25)
  P_BRAND			VARCHAR(10)
  P_TYPE			VARCHAR(25)
  P_SIZE			INTEGER
  P_CONTAINER		VARCHAR(10)
  P_RETAILPRICE	DOUBLE
  P_COMMENT		VARCHAR(23)
  */
  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE part ("
        "p_partkey INT PRIMARY KEY,"
        "p_name CHAR(55), "
        "p_mfgr CHAR(25),"
        "p_brand CHAR(10),"
        "p_type CHAR(25),"
        "p_size INT,"
        "p_container CHAR(10),"
        "p_retailprice FLOAT,"
        "p_comment CHAR(23));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "part",
        9,
        "p_partkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "p_name",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        55.0,
        "p_mfgr",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        25.0,
        "p_brand",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        10.0,
        "p_type",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        25.0,
        "p_size",
        FIELDTYPE_INTEGER,
        0,
        0,
        min,
        max,
        "p_container",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        10.0,
        "p_retailprice",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "p_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        23.0,
        200000);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* partsupp表	供货商的零件的信息
  PS_PARTKEY		INTEGER			Foreign Key to P_PARTKEY。与PS_SUPPKEY联合构成主键
  PS_SUPPKEY		INTEGER			Foreign Key to S_SUPPKEY
  PS_AVAILQTY		INTEGER
  PS_SUPPLYCOST	DOUBLE
  PS_COMMENT		VARCHAR(199)
  */
  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE partsupp ("
        "ps_partkey INT primary key,"
        "ps_suppkey INT primary key,"
        "ps_availqty INT,"
        "ps_supplycost FLOAT,"
        "ps_comment CHAR(199));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "partsupp",
        5,
        "ps_partkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "ps_suppkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "ps_availqty",
        FIELDTYPE_INTEGER,
        0,
        0,
        min,
        max,
        "ps_supplycost",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "ps_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        199.0,
        800000);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* customer表	消费者的信息
  C_CUSTKEY		INTEGER			主键。值范围是1到SF*150000
  C_NAME			VARCHAR(25)
  C_ADDRESS		VARCHAR(40)
  C_NATIONKEY		INTEGER			Foreign Key to N_NATIONKEY
  C_PHONE			VARCHAR(15)
  C_ACCTBAL		DOUBLE
  C_MKTSEGMENT	VARCHAR(10)
  C_COMMENT		VARCHAR(117)
  */
  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE customer ("
        "c_custkey INT PRIMARY KEY,"
        "c_name CHAR(25), "
        "c_address CHAR(40),"
        "c_nationkey INT,"
        "c_phone CHAR(15),"
        "c_acctbal FLOAT,"
        "c_mktsegment CHAR(10),"
        "c_comment CHAR(117));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "customer",
        8,
        "c_custkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "c_name",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        25.0,
        "c_address",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        40.0,
        "c_nationkey",
        FIELDTYPE_INTEGER,
        0,
        0,
        min,
        max,
        "c_phone",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        15.0,
        "c_acctbal",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "c_mktsegment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        10.0,
        "c_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        117.0,
        1500000);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* orders表		订单的信息
  O_ORDERKEY		INTEGER			主键。值范围是1到SF*150000。
  O_CUSTKEY		INTEGER			Foreign Key to C_CUSTKEY
  O_ORDERSTATUS	VARCHAR(1)
  O_TOTALPRICE	DOUBLE
  O_ORDERDATE		VARCHAR(12)
  O_ORDERPRIORITY	VARCHAR(15)
  O_CLERK			VARCHAR(15)
  O_SHIPPRIORITY	INTEGER
  O_COMMENT		VARCHAR(79)
  */
  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE orders ("
        " o_orderkey INT PRIMARY KEY,"
        " o_custkey INT,"
        " o_orderstatus CHAR(1),"
        " o_totalprice FLOAT,"
        " o_orderdate CHAR(12),"
        " o_orderpriority CHAR(15),"
        " o_clerk CHAR(15),"
        " o_shippriority INT,"
        " o_comment CHAR(79));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "orders",
        9,
        "o_orderkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "o_custkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "o_orderstatus",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        2.0,
        "o_totalprice",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "o_orderdate",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        12.0,
        "o_orderpriority",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        15.0,
        "o_clerk",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        15.0,
        "o_shippriority",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "o_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        79.0,
        150000);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* lineitem表	在线商品的信息
  L_ORDERKEY		INTEGER			Foreign Key to O_ORDERKEY。与L_LINENUMBER联合构成主键
  L_PARTKEY		INTEGER			Foreign key to P_PARTKEY, first part of the compound Foreign Key
                                  to (PS_PARTKEY,PS_SUPPKEY) with L_SUPPKEY
  L_SUPPKEY		INTEGER			Foreign key to S_SUPPKEY, second part of thecompound Foreign Key
                                  to (PS_PARTKEY,TPC BenchmarkTM H Standard Specification
                                  Revision 2.16.0 Page 17PS_SUPPKEY) with L_PARTKEY
  L_LINENUMBER	INTEGER
  L_QUANTITY		DOUBLE
  L_EXTENDEDPRICE	DOUBLE
  L_DISCOUNT		DOUBLE			between 0.00 and 1.00
  L_TAX			DOUBLE
  L_RETURNFLAG	VARCHAR(2)
  L_LINESTATUS	VARCHAR(2)
  L_SHIPDATE		VARCHAR(12)			L_SHIPDATE <= L_RECEIPTDAT
  L_COMMITDATE	VARCHAR(12)
  L_RECEIPTDATE	VARCHAR(12)
  L_SHIPINSTRUCT	VARCHAR(25)
  L_SHIPMODE		VARCHAR(10)
  L_COMMENT		VARCHAR(44)
   */
  if (PERSQLTEST) {
    rc = GNCDB_exec(db,
        "CREATE TABLE lineitem ("
        "l_orderkey INT PRIMARY KEY,"
        "l_partkey INT,"
        "l_suppkey INT,"
        "l_linenumber INT,"
        "l_quantity FLOAT,"
        "l_extendedprice FLOAT,"
        "l_discount FLOAT,"
        "l_tax FLOAT,"
        "l_returnflag CHAR(2),"
        "l_linestatus CHAR(2), "
        "l_shipdate CHAR(12),"
        "l_commitdate CHAR(12),"
        "l_receiptdate CHAR(12),"
        "l_shipinstruct CHAR(25),"
        "l_shipmode CHAR(10),"
        "l_comment CHAR(44));",
        NULL,
        NULL,
        NULL);
  } else {
    rc = GNCDB_createTable(db,
        "lineitem",
        16,
        "l_orrderkey",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "l_partkey",
        FIELDTYPE_INTEGER,
        0,
        0,
        min,
        max,
        "l_suppkey",
        FIELDTYPE_INTEGER,
        0,
        0,
        min,
        max,
        "l_linenumber",
        FIELDTYPE_INTEGER,
        0,
        1,
        min,
        max,
        "l_quantity",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "l_extendedprice",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "l_discount",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "l_tax",
        FIELDTYPE_REAL,
        0,
        0,
        min,
        max,
        "l_returnflag",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        2.0,
        "l_linestatus",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        2.0,
        "l_shipdate",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        12.0,
        "l_commitdate",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        12.0,
        "l_receiptdate",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        12.0,
        "l_shipinstruct",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        25.0,
        "l_shipmode",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        10.0,
        "l_comment",
        FIELDTYPE_VARCHAR,
        0,
        0,
        0.0,
        44.0,
        6000000);
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}
unsigned long long getNanoseconds()
{
  struct timespec ts;
  clock_gettime(CLOCK_REALTIME, &ts);
  return (unsigned long long)ts.tv_sec * 1000000000ULL + (unsigned long long)ts.tv_nsec;
}
/// @brief 使用当前时间生成随机数
/// @param min
/// @param max
/// @return
int generateRandomNumber(int min, int max)
{
  // 使用当前时间作为随机数种子
  unsigned long long seed = getNanoseconds();
  srand(seed);

  // 生成随机数
  return rand() % (max - min + 1) + min;
}

/// @brief sqlite回调函数
/// @param data
/// @param argc
/// @param argv
/// @param azColName
/// @return
int callback(void *data, int argc, char **argv, char **azColName)
{
  for (int i = 0; i < argc; i++) {
    printf("Table name: %s\n", argv[i]);
  }
  return 0;
}
/// @brief 性能测试主函数
/// @return

/**
 * 单个参数解析：检查是否 "-t<num>" 或 "-T<num>"，
 * 是则返回解析的 int（不做向 10 取整处理，与示例 2 中要求一致），
 * 否则返回 -1。
 */
int parse_t_option(const char *s)
{
  char *endptr;
  long  val = 0;
  if (s == NULL)
    return -1;
  if (s[1] == '\0') {
    return -1;  // 没有数字部分
  }

  errno = 0;
  val   = strtol(s + 1, &endptr, 10);

  // 检查转换过程中的错误
  if (errno == ERANGE || val < 0) {
    return -1;
  }
  // 确保整串都是数字
  if (*endptr != '\0') {
    return -1;
  }
  return (int)val;
}

/**
 * 在 args[0..n-1] 中查找第一个以 -t/-T 开头的参数：
 *   - 第一次遇到就调用 parse_t_option：
 *       * 如果返回 -1，则函数返回 -1（格式不合法）
 *       * 否则返回该值
 *   - 如果扫描结束都没遇到，则返回 0
 */
int parse_first_t_option(int n, char *args[])
{
  for (int i = 0; i < n; i++) {
    // 只在前缀匹配时才调用解析
    if (args[i] != NULL && (args[i][0] == 't' || args[i][0] == 'T')) {
      int v = parse_t_option(args[i]);
      return v;  // v 可能是 -1（非法） 或 非负整数
    }
  }
  // 没有找到任何 -t 或 -T 参数
  return 0;
}

bool ends_with_case_insensitive(const char *str, const char *suffix)
{
  size_t str_len    = strlen(str);
  size_t suffix_len = strlen(suffix);

  if (str_len < suffix_len) {
    return false;
  }

  for (size_t i = 0; i < suffix_len; i++) {
    if (tolower(str[str_len - suffix_len + i]) != tolower(suffix[i])) {
      return false;
    }
  }

  return true;
}
PerTestReset PTR_arg;

// 命令行参数解析函数
int parse_args(int argc, char **argv)
{
  int opt;
  // int opterr = 0; // 禁用默认错误提示
  int testCount = 0;

  // 定义支持的大小写不敏感选项
  const char *optstring = "mMfFh";

  while ((opt = getopt(argc, argv, optstring)) != -1) {
    switch (tolower(opt)) {
      case 'm': PTR_arg.MaxMinOp = 1; break;
      case 'f': PTR_arg.FileRWOp = 1; break;
      case 't': break;
      case 'h':
        printf("参数说明：\n");
        printf("  -M/-m: 显示最大最小值\n");
        printf("  -F/-f: 显示文件读写时间\n");
        printf("  T/t: 指定测试重复的次数Tnum\n");
        printf("  .csv : 输出到指定文件\n");
        printf("  -H/-h: 显示帮助\n");
        return -1;  // 直接退出
      case '?':
        // 自定义错误处理
        if (optopt == 'm' || optopt == 'M' || optopt == 'f' || optopt == 'F') {
          fprintf(stderr, "错误: 选项-%c不需要参数\n", optopt);
        } else if (isprint(optopt)) {
          fprintf(stderr, "错误: 未知选项 '-%c'\n", optopt);
        } else {
          fprintf(stderr, "错误: 未知选项字符 '\\x%x'\n", optopt);
        }
        return -1;
    }
  }

  // 处理非选项参数（文件名）
  for (int i = optind; i < argc; i++) {
    if (ends_with_case_insensitive(argv[i], ".csv")) {
      PTR_arg.FileNameOp = 1;
      memset(PTR_arg.fileName, 0, sizeof(PTR_arg.fileName));
      strncpy(PTR_arg.fileName, argv[i], sizeof(PTR_arg.fileName) - 1);
      break;  // 只处理第一个CSV文件
    }
  }
  testCount = parse_first_t_option(argc, argv);
  if (testCount > 0) {
    PTR_arg.TestCount = testCount;
  } else if (testCount == 0) {
    PTR_arg.TestCount = EXECUTION_COUNT;
  } else {
    printf("错误: 指定测试重复的次数-Tnum不合法\n");
    return -1;  // 直接退出
  }

  // 检查必填参数（可选）
  // if (!PTR_arg.FileNameOp) {
  //     fprintf(stderr, "错误: 必须指定CSV文件\n");
  //     return  -1;
  // }

  return 0;
}

int copy_file(const char *src_filename, const char *dst_filename)
{
  FILE  *src = NULL;
  FILE  *dst = NULL;
  char   buffer[4096];
  size_t bytes;

  src = fopen(src_filename, "rb");
  if (!src) {
    perror("无法打开源文件");
    return -1;
  }

  dst = fopen(dst_filename, "wb");
  if (!dst) {
    perror("无法创建目标文件");
    fclose(src);
    return -1;
  }

  while ((bytes = fread(buffer, 1, sizeof(buffer), src)) > 0) {
    if (fwrite(buffer, 1, bytes, dst) != bytes) {
      perror("写入目标文件失败");
      fclose(src);
      fclose(dst);
      return -1;
    }
  }

  fclose(src);
  fclose(dst);
  return 0;  // 成功
}

int count = 0;

int pageCacheSize[5] = {600, 300, 1000, 1500, 2000};

int tableDataSize[5] = {5000, 2000, 10000, 20000, 40000};

int partDataSize[5] = {5000, 2000, 10000, 20000, 40000};

int partSuppDataSize[5] = {20000, 8000, 40000, 80000, 160000};

int supplierDataSize[5] = {250, 100, 500, 1000, 2000};

ExcelPoint perEx;

int perTestMain(int argc, char *argv[])
{
  char cacheEdit[64];
  char testMessage[128];
  char threadEdit[64];
  int  dataSize   = 0;
  int  pthreadNum = 0;
  // int i = 0;
  GNCDB      *perDB;
  sqlite3    *ppDb;
  int         rc  = 0;
  int         len = 0;
  lxw_format *title_format;
  char        fileName[]        = "per_data.dat";
  char        fileName_sqlite[] = "sqlite_per_data.db";

  PTR_arg.MaxMinOp   = 0;
  PTR_arg.FileRWOp   = 0;
  PTR_arg.FileNameOp = 0;

  rc = parse_args(argc, argv);
  if (rc != 0) {
    // printf("parse_args error\n");
    return 0;
  }
  if (PTR_arg.FileRWOp == 1) {
    len = 9;
  } else {
    len = 7;
  }

  perTestCSVInit();

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  format_set_bold(title_format);
  format_set_font_size(title_format, 18);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);

  /* tpch测试 */
  // rc = tpccTest();
  if (rc != 0) {
    printf("tpccTest error\n");
    // return -1;
  }
  // rc = tpcH1Test();
  if (rc != 0) {
    printf("tpcH1Test error\n");
    // return -1;
  }

  // 添加关闭MAX MIN 添加文件读写时间 页大小
  // 添加数据量大小，缓冲池大小影响查询的性能
  // sprintf(threadEdit, "单线程性能测试;\n", pthreadNum);
  // fwrite(threadEdit, strlen(threadEdit), 1, resFp);

  perEx.x    = 1;
  perEx.y    = 0;
  perEx.flag = 1;

  sprintf(testMessage,
      "采用TPCH标准数据的part表和supplier表, 数据页大小为 %d, 测试执行重复次数为%d;",
      PAGE_SIZE,
      PTR_arg.TestCount);
  worksheet_merge_range(pertest_worksheet, perEx.x, 0, perEx.x, len, testMessage, title_format);
  worksheet_set_row(pertest_worksheet, perEx.x, 30, NULL);
  for (count = 0; count < 1; count++) {
    if (count == 2) {
      for (dataSize = 0; dataSize < 5; dataSize++) {
        sprintf(cacheEdit, "缓冲池大小 = %d页 数据量大小 = %d条\n;", pageCacheSize[count], tableDataSize[dataSize]);
        // fwrite(cacheEdit, strlen(cacheEdit), 1, resFp);
        ++perEx.x;
        worksheet_merge_range(pertest_worksheet, perEx.x, 0, perEx.x, len, cacheEdit, title_format);
        worksheet_set_row(pertest_worksheet, perEx.x, 30, NULL);
        writeCSVPerHead(++perEx.x);
        SELECT_DATA_ININT = tableDataSize[dataSize];
        DELETE_DATA_ININT = SELECT_DATA_ININT;
        UPDATE_DATA_ININT = SELECT_DATA_ININT;
        insertPerTest();

        remove("per_data.dat");
        remove("sqlite_per_data.db");
        // rc = copy_file("per_data_1.dat", "per_data.dat");
        // if(rc){
        // 	printf("copy_file error\n");
        // 	return -1;
        // }
        // rc = copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
        // if(rc){
        // 	printf("copy_file error\n");
        // 	return -1;
        // }
        perTestDBInit(&perDB);
        perTestDataInit(perDB, tableDataSize[dataSize], 1);
        sqliteDBInit(&ppDb);
        sqliteDataInit(ppDb, tableDataSize[dataSize], 1);

        selectPerTest();
        // deletePerTest();
        // updatePerTest();
      }
    } else {
      // int rows = 0;
      sprintf(cacheEdit, "缓冲池大小 = %d 数据量大小 = %d;\n", pageCacheSize[count], tableDataSize[2]);
      // fwrite(cacheEdit, strlen(cacheEdit), 1, resFp);
      ++perEx.x;
      worksheet_merge_range(pertest_worksheet, perEx.x, 0, perEx.x, len, cacheEdit, title_format);
      worksheet_set_row(pertest_worksheet, perEx.x, 30, NULL);
      writeCSVPerHead(++perEx.x);
      SELECT_DATA_ININT = tableDataSize[2];
      DELETE_DATA_ININT = SELECT_DATA_ININT;
      UPDATE_DATA_ININT = SELECT_DATA_ININT;
      // insertPerTest();

      perTestDBInit(&perDB);
      perTestDataInit(perDB, SELECT_DATA_ININT, 1);
      sqliteDBInit(&ppDb);
      sqliteDataInit(ppDb, SELECT_DATA_ININT, 1);
      closeDb(perDB, ppDb, 3);
      // 复制per_data.dat和sqlite_per_data.db文件为per_data_1.dat和sqlite_per_data_1.db
      copy_file(fileName, "per_data_1.dat");
      copy_file(fileName_sqlite, "sqlite_per_data_1.db");

      selectPerTest();
      // deletePerTest();
      // updatePerTest();
    }
  }

  perEx.x    = 1;
  perEx.y    = 0;
  perEx.flag = 0;
  // writeCSVPerHead();

  sprintf(testMessage, "采用TPCH标准数据的part表和supplier表，测试执行重复次数为%d\n;", PTR_arg.TestCount);
  worksheet_merge_range(conpertest_worksheet, perEx.x, 0, perEx.x, len, testMessage, title_format);
  worksheet_set_row(conpertest_worksheet, perEx.x, 30, NULL);

  for (count = 0; count < 0; count++) {
    if (count == 2) {
      for (dataSize = 0; dataSize < 5; dataSize++) {
        sprintf(cacheEdit, "缓冲池大小 = %d 数据量大小 = %d\n;", pageCacheSize[count], tableDataSize[dataSize]);
        // fwrite(cacheEdit, strlen(cacheEdit), 1, resFp);
        ++perEx.x;
        worksheet_merge_range(conpertest_worksheet, perEx.x, 0, perEx.x, len, cacheEdit, title_format);
        worksheet_set_row(conpertest_worksheet, perEx.x, 30, NULL);
        SELECT_DATA_ININT = tableDataSize[dataSize];
        DELETE_DATA_ININT = SELECT_DATA_ININT;
        UPDATE_DATA_ININT = SELECT_DATA_ININT;
        for (pthreadNum = 2; pthreadNum < CONCURRENT_TEST; pthreadNum++) {
          sprintf(threadEdit, "线程数量 = %d;\n", pthreadNum);
          ++perEx.x;
          worksheet_merge_range(conpertest_worksheet, perEx.x, 0, perEx.x, len, threadEdit, title_format);
          worksheet_set_row(conpertest_worksheet, perEx.x, 30, NULL);
          writeCSVPerHead(++perEx.x);

          remove("per_data.dat");
          remove("sqlite_per_data.db");
          copy_file("per_data_1.dat", "per_data.dat");
          copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");

          concurrencePerTest(pthreadNum);
        }
      }
    } else {
      sprintf(cacheEdit, "缓冲池大小 = %d 数据量大小 = %d;\n", pageCacheSize[count], tableDataSize[2]);
      // fwrite(cacheEdit, strlen(cacheEdit), 1, resFp);
      ++perEx.x;
      worksheet_merge_range(conpertest_worksheet, perEx.x, 0, perEx.x, len, cacheEdit, title_format);
      worksheet_set_row(conpertest_worksheet, perEx.x, 30, NULL);
      writeCSVPerHead(++perEx.x);
      SELECT_DATA_ININT = tableDataSize[2];
      DELETE_DATA_ININT = SELECT_DATA_ININT;
      UPDATE_DATA_ININT = SELECT_DATA_ININT;
      for (pthreadNum = 2; pthreadNum < CONCURRENT_TEST; pthreadNum++) {
        sprintf(threadEdit, "线程数量 = %d;\n", pthreadNum);
        ++perEx.x;
        worksheet_merge_range(conpertest_worksheet, perEx.x, 0, perEx.x, len, threadEdit, title_format);
        worksheet_set_row(conpertest_worksheet, perEx.x, 30, NULL);
        writeCSVPerHead(++perEx.x);

        perTestDBInit(&perDB);
        perTestDataInit(perDB, tableDataSize[dataSize], 1);
        sqliteDBInit(&ppDb);
        sqliteDataInit(ppDb, tableDataSize[dataSize], 1);
        closeDb(perDB, ppDb, 3);
        // 复制per_data.dat和sqlite_per_data.db文件为per_data_1.dat和sqlite_per_data_1.db
        copy_file(fileName, "per_data_1.dat");
        copy_file(fileName_sqlite, "sqlite_per_data_1.db");

        concurrencePerTest(pthreadNum);
      }
    }
  }

  // blobPerTest();
  // recoverPerTest();
  closeCSVfile();
  return 0;
}
/// @brief 打开csv文件
/// @return
int perTestCSVInit()
{
  int         rc              = 0;
  char        fileName[1024]  = {0};
  char        tpchMessage[64] = {0};
  lxw_format *title_format;
  lxw_format *title_format1;
  int         len = 0;
  if (PTR_arg.FileRWOp == 1) {
    len = 9;
  } else {
    len = 7;
  }

  if (PTR_arg.FileNameOp == 1) {
    sprintf(fileName, "./testfile/result/%s", PTR_arg.fileName);
  } else {
    sprintf(fileName, "./testfile/result/pertest.xlsx");
  }

  remove(fileName);

  // resFp = fopen(fileName, "w+");
  // if (resFp== NULL)
  // {
  // 	perror("Error\n");
  // 	printf(".csv 文件打开失败\n");
  // 	return -1;
  // }

  workbook = workbook_new(fileName);
  if (workbook == NULL) {
    printf("创建工作簿失败\n");
    return -1;
  }

  main_worksheet       = workbook_add_worksheet(workbook, "maininterface");
  tpcc_worksheet       = workbook_add_worksheet(workbook, "tpcc");
  tpch_worksheet       = workbook_add_worksheet(workbook, "tpch");
  pertest_worksheet    = workbook_add_worksheet(workbook, "pertest");
  conpertest_worksheet = workbook_add_worksheet(workbook, "conpertest");

  // 获取当前的系统信息
  rc = getSystemInfo();
  if (rc != 0) {
    printf("获取系统信息失败\n");
    return -1;
  }

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  format_set_bold(title_format);
  format_set_font_size(title_format, 18);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);

  title_format1 = workbook_add_format(workbook);
  format_set_border(title_format1, LXW_BORDER_THIN);
  format_set_font_size(title_format1, 16);
  format_set_align(title_format1, LXW_ALIGN_CENTER);
  format_set_align(title_format1, LXW_ALIGN_VERTICAL_CENTER);

  worksheet_merge_range(tpch_worksheet, 0, 0, 0, 4, "GNCDB TPCH性能测试", title_format);
  sprintf(tpchMessage, "采用TPCH标准数据,SF值为%.2f, 数据量约为%.2fGB", SF, SF);
  worksheet_merge_range(tpch_worksheet, 1, 0, 1, 4, tpchMessage, title_format1);

  worksheet_write_string(tpch_worksheet, 2, 0, "查询序号", title_format1);
  worksheet_write_string(tpch_worksheet, 2, 1, "查询语句", title_format1);
  worksheet_write_string(tpch_worksheet, 2, 2, "GNCDB时间", title_format1);
  worksheet_write_string(tpch_worksheet, 2, 3, "sqlite时间", title_format1);
  worksheet_write_string(tpch_worksheet, 2, 4, "比值", title_format1);
  worksheet_set_row(tpch_worksheet, 0, 35, NULL);
  worksheet_set_row(tpch_worksheet, 1, 30, NULL);
  worksheet_set_row(tpch_worksheet, 2, 30, NULL);

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  format_set_bold(title_format);
  format_set_font_size(title_format, 18);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
  worksheet_merge_range(tpcc_worksheet, 0, 0, 0, 6, "GNCDB TPCC性能测试", title_format);

  worksheet_set_row(tpcc_worksheet, 0, 35, NULL);
  // worksheet_set_row(tpcc_worksheet, 1, 30, NULL);

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  format_set_bold(title_format);
  format_set_font_size(title_format, 18);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
  worksheet_merge_range(pertest_worksheet, 0, 0, 0, len, "GNCDB 单线程sqlite性能对比测试", title_format);

  worksheet_set_row(pertest_worksheet, 0, 35, NULL);

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  format_set_bold(title_format);
  format_set_font_size(title_format, 18);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
  worksheet_merge_range(conpertest_worksheet, 0, 0, 0, len, "GNCDB 单线程sqlite性能对比测试", title_format);

  worksheet_set_row(conpertest_worksheet, 0, 35, NULL);
  // fflush(resFp);

  return 1;
}

int writeCSVPerHead(int x)
{
  // char *pcsvH = NULL;
  lxw_format *title_format;
  // char csvhead1[1024] =
  // "测试类型,测试操作,场景描述,SQL,测试次数,时间类型,文件读时间(us),文件写时间(us),GNCDB总时间(us),SQLITE总时间(us),比值\n";
  // char csvhead2[1024] = "测试类型,测试操作,场景描述,SQL,测试次数,时间类型,GNCDB总时间(us),SQLITE总时间(us),比值\n";
  lxw_worksheet *current = NULL;
  if (perEx.flag == 1) {
    current = pertest_worksheet;
  } else {
    current = conpertest_worksheet;
  }

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  format_set_font_size(title_format, 16);
  format_set_bold(title_format);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
  worksheet_set_row(current, x, 30, NULL);
  if (PTR_arg.FileRWOp == 1) {
    // pcsvH = csvhead1;
    worksheet_write_string(current, x, 0, "测试类型", title_format);
    worksheet_write_string(current, x, 1, "测试操作", title_format);
    worksheet_write_string(current, x, 2, "场景描述", title_format);
    worksheet_write_string(current, x, 3, "SQL", title_format);
    // worksheet_write_string(current, 1, 4, "测试次数", title_format);
    worksheet_write_string(current, x, 4, "时间类型", title_format);
    worksheet_write_string(current, x, 5, "文件读时间(us)", title_format);
    worksheet_write_string(current, x, 6, "文件写时间(us)", title_format);
    worksheet_write_string(current, x, 7, "GNCDB总时间(us)", title_format);
    worksheet_write_string(current, x, 8, "SQLITE总时间(us)", title_format);
    worksheet_write_string(current, x, 9, "比值", title_format);
  } else {
    // pcsvH = csvhead2;
    worksheet_write_string(current, x, 0, "测试类型", title_format);
    worksheet_write_string(current, x, 1, "测试操作", title_format);
    worksheet_write_string(current, x, 2, "场景描述", title_format);
    worksheet_write_string(current, x, 3, "SQL", title_format);
    // worksheet_write_string(current, 1, 4, "测试次数", title_format);
    worksheet_write_string(current, x, 4, "时间类型", title_format);
    worksheet_write_string(current, x, 5, "GNCDB总时间(us)", title_format);
    worksheet_write_string(current, x, 6, "SQLITE总时间(us)", title_format);
    worksheet_write_string(current, x, 7, "比值", title_format);
  }

  // fwrite(pcsvH, strlen(pcsvH), 1, resFp);
  // fflush(resFp);
  return 0;
}

int sqliteCreateTable(sqlite3 *ppDb)
{
  int   result = 0;
  char *errMsg;
  // char *createTable1Query = "CREATE TABLE supplier ("
  //                          "s_suppkey INTEGER PRIMARY KEY,"
  //                          "s_name TEXT,"
  //                          "s_address TEXT,"
  //                          "s_nationkey INTEGER,"
  //                          "s_phone TEXT,"
  //                          "s_acctbal REAL,"
  //                          "s_comment TEXT"
  //                          ");";
  char *createTable1Query = "CREATE TABLE supplier ("
                            "s_suppkey INTEGER PRIMARY KEY,"
                            "s_name CHAR(25), "
                            "s_address CHAR(40),"
                            "s_nationkey INTEGER,"
                            "s_phone CHAR(15),"
                            "s_acctbal FLOAT,"
                            "s_comment CHAR(101));";

  // char *createTable2Query = "CREATE TABLE part ("
  // 						 "p_partkey INTEGER PRIMARY KEY,"
  // 						 "p_name TEXT,"
  // 						 "p_mfgr TEXT,"
  // 						 "p_brand TEXT,"
  // 						 "p_type TEXT,"
  // 						 "p_size INTEGER,"
  // 						 "p_container TEXT,"
  // 						 "p_retailprice REAL,"
  // 						 "p_comment TEXT"
  // 						 ");";

  char *createTable2Query = "CREATE TABLE part ("
                            "p_partkey INTEGER PRIMARY KEY,"
                            "p_name CHAR(55), "
                            "p_mfgr CHAR(25),"
                            "p_brand CHAR(10),"
                            "p_type CHAR(25),"
                            "p_size INTEGER,"
                            "p_container CHAR(24),"
                            "p_retailprice FLOAT,"
                            "p_comment CHAR(23));";

  char *createTable3Query = "CREATE TABLE partsupp ("
                            "ps_partkey INT,"
                            "ps_suppkey INT,"
                            "ps_availqty INT,"
                            "ps_supplycost FLOAT,"
                            "ps_comment CHAR(199));";
  result                  = sqlite3_exec(ppDb, createTable1Query, 0, 0, &errMsg);
  if (result != SQLITE_OK) {
    printf("Failed to create table: %s\n", errMsg);
    sqlite3_free(errMsg);
  }
  result = sqlite3_exec(ppDb, createTable2Query, 0, 0, &errMsg);
  if (result != SQLITE_OK) {
    printf("Failed to create table: %s\n", errMsg);
    sqlite3_free(errMsg);
  }
  result = sqlite3_exec(ppDb, createTable3Query, 0, 0, &errMsg);
  if (result != SQLITE_OK) {
    printf("Failed to create table: %s\n", errMsg);
    sqlite3_free(errMsg);
  }

  // sqlite3_close(ppDb);

  return 0;
}
/// @brief 打开数据库，创建八张表
/// @return
int perTestDBInit(GNCDB **perDB)
{
  char fileName[] = "per_data.dat";
  int  rc         = 0;

  remove(fileName);

  remove("log_per_data.dat");

  rc = GNCDB_open(perDB, fileName, 0, pageCacheSize[count]);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 创建表 */
  rc = perCreateTable(*perDB);

  return rc;
}

double gncdbSqlExecTime(GNCDB *perDB, const char *sql, int *rc)
{
  double          time_used = 0;
  struct timespec start, end;

  clock_gettime(CLOCK_MONOTONIC, &start);
  *rc = GNCDB_exec(perDB, sql, NULL, NULL, NULL);
  clock_gettime(CLOCK_MONOTONIC, &end);

  time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  return time_used;
}

double gncdbSqlExecTimecallback(GNCDB *perDB, const char *sql, int *rc)
{
  double          time_used = 0;
  struct timespec start, end;

  clock_gettime(CLOCK_MONOTONIC, &start);
  *rc = GNCDB_exec(perDB, sql, myCallBack1, NULL, NULL);
  clock_gettime(CLOCK_MONOTONIC, &end);

  time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  return time_used;
}

double gncdbSqlExecTimeNULLcallback(GNCDB *perDB, const char *sql, int *rc)
{
  double          time_used = 0;
  struct timespec start, end;

  clock_gettime(CLOCK_MONOTONIC, &start);
  *rc = GNCDB_exec(perDB, sql, myCallBackNULL1, NULL, NULL);
  clock_gettime(CLOCK_MONOTONIC, &end);

  time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  return time_used;
}

/// @brief 打开sqlite数据库，创建2张表
/// @return
int sqliteDBInit(sqlite3 **ppDb)
{
  char  fileName_sqlite[]   = "sqlite_per_data.db";
  char  pragmaQuery[1024]   = {0};
  char  pageSizeQuery[1024] = {0};
  int   rc                  = 0;
  char *errMsg;
  // const char* query = "SELECT name FROM sqlite_master WHERE type='table';";
  remove(fileName_sqlite);

  rc = sqlite3_open(fileName_sqlite, ppDb);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 设置sqlite缓存池大小 */
  sprintf(pragmaQuery, "PRAGMA cache_size = %d", pageCacheSize[count]);
  rc = sqlite3_exec(*ppDb, pragmaQuery, 0, 0, &errMsg);
  if (rc != SQLITE_OK) {
    printf("Failed to set cache size: %s\n", errMsg);
    sqlite3_free(errMsg);
  }
  sprintf(pageSizeQuery, "PRAGMA page_size = %d", PAGE_SIZE);
  rc = sqlite3_exec(*ppDb, pageSizeQuery, 0, 0, &errMsg);
  if (rc != SQLITE_OK) {
    printf("Error setting page size: %s\n", errMsg);
    sqlite3_free(errMsg);
  }
  /* 创建表 */
  rc = sqliteCreateTable(*ppDb);

  // rc = sqlite3_exec(*ppDb, query, callback, 0, NULL);  // 执行查询语句并调用回调函数处理结果

  return rc;
}

/// @brief 执行该条语句所花时间
/// @param pDb
/// @param sql
/// @return
double sqliteExecTime(sqlite3 *pDb, const char *sql)
{
  int             rc        = 0;
  double          time_used = 0;
  struct timespec start, end;
  // printf("%s\n", sql);

  clock_gettime(CLOCK_MONOTONIC, &start);
  rc = sqlite3_exec(pDb, sql, NULL, 0, NULL);
  clock_gettime(CLOCK_MONOTONIC, &end);
  if (rc) {
    if (INSERT_DATA_ININT) {
      return -1;
    }
    printf("sqlite3_exec error:%s\n", sqlite3_errmsg(pDb));
    return -1;
  }
  time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  return time_used;
}

double sqliteExecTimecallback(sqlite3 *pDb, const char *sql)
{
  int             rc        = 0;
  double          time_used = 0;
  struct timespec start, end;
  // printf("%s\n", sql);

  clock_gettime(CLOCK_MONOTONIC, &start);
  rc = sqlite3_exec(pDb, sql, myCallBack1, 0, NULL);
  clock_gettime(CLOCK_MONOTONIC, &end);
  if (rc) {
    printf("sqlite3_exec error:%s\n", sqlite3_errmsg(pDb));
    return -1;
  }
  time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  return time_used;
}

double sqliteExecTimeNULLcallback(sqlite3 *pDb, const char *sql)
{
  int             rc        = 0;
  double          time_used = 0;
  struct timespec start, end;
  // printf("%s\n", sql);

  clock_gettime(CLOCK_MONOTONIC, &start);
  rc = sqlite3_exec(pDb, sql, myCallBackNULL1, 0, NULL);
  clock_gettime(CLOCK_MONOTONIC, &end);
  if (rc) {
    printf("sqlite3_exec error:%s\n", sqlite3_errmsg(pDb));
    return -1;
  }
  time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  return time_used;
}
/// @brief csv文件写入测试结果
/// @param result
/// @param head
/// @return
int writeToCSV(struct PerTestRes result)
{
  char           str[3072];
  double         rat     = 0;
  double         MAXrat  = 0;
  double         MINrat  = 0;
  lxw_worksheet *current = NULL;
  lxw_format    *title_format;
  lxw_format    *title_format2;
  lxw_format    *title_formatrat;
  lxw_format    *title_formatmaxrat;
  lxw_format    *title_formatminrat;
  lxw_format    *title_format4;

  int sucessNum = result.exeCount - result.falseNum;
  if (result.time_sqlite_avg != 0) {
    rat    = ((double)result.time_used_avg / result.time_sqlite_avg);
    MAXrat = ((double)result.time_used_max / result.time_sqlite_max);
    MINrat = ((double)result.time_used_min / result.time_sqlite_min);
  }
  if (perEx.flag == 1) {
    current = pertest_worksheet;
  } else {
    current = conpertest_worksheet;
  }

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  title_formatrat = workbook_add_format(workbook);
  format_set_border(title_formatrat, LXW_BORDER_THIN);
  title_format2 = workbook_add_format(workbook);
  format_set_border(title_format2, LXW_BORDER_THIN);
  title_format4 = workbook_add_format(workbook);
  format_set_border(title_format4, LXW_BORDER_THIN);
  title_formatmaxrat = workbook_add_format(workbook);
  format_set_border(title_formatmaxrat, LXW_BORDER_THIN);
  title_formatminrat = workbook_add_format(workbook);
  format_set_border(title_formatminrat, LXW_BORDER_THIN);
  format_set_font_size(title_format, 16);
  format_set_font_size(title_formatrat, 16);
  format_set_font_size(title_formatmaxrat, 16);
  format_set_font_size(title_formatminrat, 16);
  format_set_font_size(title_format4, 16);
  format_set_font_size(title_format2, 16);
  format_set_align(title_format, LXW_ALIGN_RIGHT);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
  format_set_align(title_format2, LXW_ALIGN_VERTICAL_CENTER);
  format_set_num_format(title_format, "#,##0.00");
  format_set_align(title_formatrat, LXW_ALIGN_RIGHT);
  format_set_align(title_formatrat, LXW_ALIGN_VERTICAL_CENTER);
  format_set_num_format(title_formatrat, "#,##0.00");
  format_set_align(title_format4, LXW_ALIGN_CENTER);
  format_set_align(title_format4, LXW_ALIGN_VERTICAL_CENTER);
  format_set_align(title_formatmaxrat, LXW_ALIGN_RIGHT);
  format_set_align(title_formatmaxrat, LXW_ALIGN_VERTICAL_CENTER);
  format_set_num_format(title_formatmaxrat, "#,##0.00");
  format_set_align(title_formatminrat, LXW_ALIGN_RIGHT);
  format_set_align(title_formatminrat, LXW_ALIGN_VERTICAL_CENTER);
  format_set_num_format(title_formatminrat, "#,##0.00");

  perEx.x++;
  perEx.y = 0;

  if (PTR_arg.FileRWOp == 1) {
    // sprintf(str,",%s,%s,%s,%d,%s,%.0lf,%.0lf,%.0lf,%.0lf,%.1f\n",
    // 	result.op, result.opDetail,result.sql,
    // 	result.exeCount,"AVG",
    // 	result.time_read_avg / sucessNum,
    // 	result.time_write_avg/ sucessNum,
    // 	result.time_used_avg / sucessNum,
    // 	result.time_sqlite_avg / sucessNum,
    // 	rat);
    // fwrite(str, strlen(str), 1, resFp);
    worksheet_write_string(current, perEx.x, ++perEx.y, result.op, title_format4);
    // format_set_align(title_format, LXW_ALIGN_LEFT);
    worksheet_write_string(current, perEx.x, ++perEx.y, result.opDetail, title_format2);
    worksheet_write_string(current, perEx.x, ++perEx.y, result.sql, title_format2);
    // format_set_align(title_format, LXW_ALIGN_CENTER);
    // worksheet_write_number(current, perEx.x, ++perEx.y, result.exeCount, title_format);
    worksheet_write_string(current, perEx.x, ++perEx.y, "AVG", title_format4);
    worksheet_set_row(current, perEx.x, 30, NULL);
    worksheet_write_number(current, perEx.x, ++perEx.y, result.time_read_avg / sucessNum, title_format);
    worksheet_write_number(current, perEx.x, ++perEx.y, result.time_write_avg / sucessNum, title_format);
    worksheet_write_number(current, perEx.x, ++perEx.y, result.time_used_avg / sucessNum, title_format);
    worksheet_write_number(current, perEx.x, ++perEx.y, result.time_sqlite_avg / sucessNum, title_format);

    if (rat <= 1.0) {
      format_set_bold(title_formatrat);
    } else if (rat > 1.0 && rat <= 2.0) {
      format_set_bg_color(title_formatrat, 0xFBE5D6);  // #FBE5D6
    } else if (rat > 2.0 && rat <= 5.0) {
      format_set_bg_color(title_formatrat, 0xF8CBAD);  // #F8CBAD
    } else if (rat > 5.0 && rat <= 10.0) {
      format_set_bg_color(title_formatrat, 0xED7D31);  // #ED7D31
    } else if (rat > 10.0 && rat <= 15.0) {
      format_set_bg_color(title_formatrat, 0xB25E25);  // #B25E25
    } else {
      format_set_bg_color(title_formatrat, 0x763E18);  // #763E18
    }

    worksheet_write_number(current, perEx.x, ++perEx.y, rat, title_formatrat);

    memset(str, 0, sizeof(str));
    if (PTR_arg.MaxMinOp == 1) {
      // sprintf(str,",,,,,%s,%.0lf,%.0lf,%.0lf,%.0lf,%.1f\n",
      // 	"MAX",
      // 	result.time_read_max,
      // 	result.time_write_max,
      // 	result.time_used_max,
      // 	result.time_sqlite_max,
      // 	MAXrat);

      // fwrite(str, strlen(str), 1, resFp);
      // memset(str, 0, sizeof(str));
      perEx.x++;
      perEx.y = 1;
      // perEx.y += 3,
      // worksheet_write_string(current, perEx.x, ++perEx.y, result.op, title_format4);
      worksheet_merge_range(current, perEx.x - 1, perEx.y, perEx.x + 1, perEx.y, result.op, title_format4);
      // format_set_align(title_format, LXW_ALIGN_LEFT);
      ++perEx.y;
      // worksheet_write_string(current, perEx.x, ++perEx.y, result.opDetail, title_format2);
      worksheet_merge_range(current, perEx.x - 1, perEx.y, perEx.x + 1, perEx.y, result.opDetail, title_format2);
      ++perEx.y;
      // worksheet_write_string(current, perEx.x, ++perEx.y, result.sql, title_format2);
      worksheet_merge_range(current, perEx.x - 1, perEx.y, perEx.x + 1, perEx.y, result.sql, title_format2);

      worksheet_write_string(current, perEx.x, ++perEx.y, "MAX", title_format4);
      worksheet_set_row(current, perEx.x, 30, NULL);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_read_max, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_write_max, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_used_max, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_sqlite_max, title_format);
      if (MAXrat <= 1.0) {
        format_set_bold(title_formatmaxrat);
      } else if (MAXrat > 1.0 && MAXrat <= 2.0) {
        format_set_bg_color(title_formatmaxrat, 0xFBE5D6);
      } else if (MAXrat > 2.0 && MAXrat <= 5.0) {
        format_set_bg_color(title_formatmaxrat, 0xF8CBAD);
      } else if (MAXrat > 5.0 && MAXrat <= 10.0) {
        format_set_bg_color(title_formatmaxrat, 0xED7D31);
      } else if (MAXrat > 10.0 && MAXrat <= 15.0) {
        format_set_bg_color(title_formatmaxrat, 0xB25E25);
      } else {
        format_set_bg_color(title_formatmaxrat, 0x763E18);
      }
      worksheet_write_number(current, perEx.x, ++perEx.y, MAXrat, title_formatmaxrat);

      // sprintf(str,",,,,,%s,%.0lf,%.0lf,%.0lf,%.0lf,%.lf\n",
      // 	"MIN",
      // 	result.time_read_min,
      // 	result.time_write_min,
      // 	result.time_used_min,
      // 	result.time_sqlite_min,
      // 	MINrat);
      // fwrite(str, strlen(str), 1, resFp);
      perEx.x++;
      perEx.y = 0;
      perEx.y += 3, worksheet_write_string(current, perEx.x, ++perEx.y, "MIN", title_format4);
      worksheet_set_row(current, perEx.x, 30, NULL);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_read_min, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_write_min, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_used_min, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_sqlite_min, title_format);
      if (MINrat <= 1.0) {
        format_set_bold(title_formatminrat);
      } else if (MINrat > 1.0 && MINrat <= 2.0) {
        format_set_bg_color(title_formatminrat, 0xFBE5D6);
      } else if (MINrat > 2.0 && MINrat <= 5.0) {
        format_set_bg_color(title_formatminrat, 0xF8CBAD);
      } else if (MINrat > 5.0 && MINrat <= 10.0) {
        format_set_bg_color(title_formatminrat, 0xED7D31);
      } else if (MINrat > 10.0 && MINrat <= 15.0) {
        format_set_bg_color(title_formatminrat, 0xB25E25);
      } else {
        format_set_bg_color(title_formatminrat, 0x763E18);
      }
      worksheet_write_number(current, perEx.x, ++perEx.y, MINrat, title_formatminrat);
    }
    // fflush(resFp);
    printf("%s 失败次数:%d\n", result.op, result.falseNum);
  } else {
    // sprintf(str,",%s,%s,%s,%d,%s,%.0lf,%.0lf,%.1f\n",
    // 	result.op, result.opDetail,result.sql,
    // 	result.exeCount,"AVG",
    // 	result.time_used_avg / sucessNum,
    // 	result.time_sqlite_avg / sucessNum,
    // 	rat);
    // fwrite(str, strlen(str), 1, resFp);
    worksheet_write_string(current, perEx.x, ++perEx.y, result.op, title_format4);
    format_set_align(title_format, LXW_ALIGN_LEFT);
    worksheet_write_string(current, perEx.x, ++perEx.y, result.opDetail, title_format2);
    worksheet_write_string(current, perEx.x, ++perEx.y, result.sql, title_format2);
    format_set_align(title_format, LXW_ALIGN_CENTER);
    // worksheet_write_number(current, perEx.x, ++perEx.y, result.exeCount, title_format);
    worksheet_write_string(current, perEx.x, ++perEx.y, "AVG", title_format4);
    worksheet_set_row(current, perEx.x, 30, NULL);
    worksheet_write_number(current, perEx.x, ++perEx.y, result.time_used_avg / sucessNum, title_format);
    worksheet_write_number(current, perEx.x, ++perEx.y, result.time_sqlite_avg / sucessNum, title_format);
    if (rat <= 1.0) {
      format_set_bold(title_formatrat);
    } else if (rat > 1.0 && rat <= 2.0) {
      format_set_bg_color(title_formatrat, 0xFBE5D6);
    } else if (rat > 2.0 && rat <= 5.0) {
      format_set_bg_color(title_formatrat, 0xF8CBAD);
    } else if (rat > 5 && rat <= 10.0) {
      format_set_bg_color(title_formatrat, 0xED7D31);
    } else if (rat > 10.0 && rat <= 15.0) {
      format_set_bg_color(title_formatrat, 0xB25E25);
    } else {
      format_set_bg_color(title_formatrat, 0x763E18);
    }
    worksheet_write_number(current, perEx.x, ++perEx.y, rat, title_formatrat);
    perEx.y = 0;
    memset(str, 0, sizeof(str));
    if (PTR_arg.MaxMinOp == 1) {
      // sprintf(str,",,,,,%s,%.0lf,%.0lf,%.lf\n",
      // 	"MAX",
      // 	result.time_used_max,
      // 	result.time_sqlite_max,
      // 	MAXrat);
      // fwrite(str, strlen(str), 1, resFp);
      // memset(str, 0, sizeof(str));
      perEx.x++;
      perEx.y = 0;
      perEx.y += 3, worksheet_write_string(current, perEx.x, ++perEx.y, "MAX", title_format4);
      worksheet_set_row(current, perEx.x, 30, NULL);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_used_max, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_sqlite_max, title_format);
      if (MAXrat <= 1.0) {
        format_set_bold(title_formatmaxrat);
      } else if (MAXrat > 1.0 && MAXrat <= 2.0) {
        format_set_bg_color(title_formatmaxrat, 0xFBE5D6);
      } else if (MAXrat > 2.0 && MAXrat <= 5.0) {
        format_set_bg_color(title_formatmaxrat, 0xF8CBAD);
      } else if (MAXrat > 5.0 && MAXrat <= 10.0) {
        format_set_bg_color(title_formatmaxrat, 0xED7D31);
      } else if (MAXrat > 10.0 && MAXrat <= 15.0) {
        format_set_bg_color(title_formatmaxrat, 0xB25E25);
      } else {
        format_set_bg_color(title_formatmaxrat, 0x763E18);
      }
      worksheet_write_number(current, perEx.x, ++perEx.y, MAXrat, title_formatmaxrat);

      // sprintf(str,",,,,,%s,%.0lf,%.0lf,%.lf\n",
      // 	"MIN",
      // 	result.time_used_min,
      // 	result.time_sqlite_min,
      // 	MINrat);
      // fwrite(str, strlen(str), 1, resFp);
      perEx.x++;
      perEx.y = 0;
      perEx.y += 3, worksheet_write_string(current, perEx.x, ++perEx.y, "MIN", title_format4);
      worksheet_set_row(current, perEx.x, 30, NULL);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_used_min, title_format);
      worksheet_write_number(current, perEx.x, ++perEx.y, result.time_sqlite_min, title_format);
      if (MINrat <= 1.0) {
        format_set_bold(title_formatminrat);
      } else if (MINrat > 1.0 && MINrat <= 2.0) {
        format_set_bg_color(title_formatminrat, 0xFBE5D6);
      } else if (MINrat > 2.0 && MINrat <= 5.0) {
        format_set_bg_color(title_formatminrat, 0xF8CBAD);
      } else if (MINrat > 5.0 && MINrat <= 10.0) {
        format_set_bg_color(title_formatminrat, 0xED7D31);
      } else if (MINrat > 10.0 && MINrat <= 15.0) {
        format_set_bg_color(title_formatminrat, 0xB25E25);
      } else {
        format_set_bg_color(title_formatminrat, 0x763E18);
      }
      worksheet_write_number(current, perEx.x, ++perEx.y, MINrat, title_formatminrat);
    }
    // fflush(resFp);
    printf("%s 失败次数:%d\n", result.op, result.falseNum);
  }
  return 1;
}
void getTimeUsed(struct PerTestRes *result, double time_used_singel)
{
  if (result->time_used_max < time_used_singel) {
    result->time_used_max  = time_used_singel;
    result->time_read_max  = time_per_read;
    result->time_write_max = time_per_write;
  }
  if (result->time_used_min > time_used_singel) {
    result->time_used_min  = time_used_singel;
    result->time_read_min  = time_per_read;
    result->time_write_min = time_per_write;
  }
  result->time_used_avg += time_used_singel;
  result->time_read_avg += time_per_read;
  result->time_write_avg += time_per_write;
}
void closeSqliteIndex(sqlite3 **ppDb)
{
  sqlite3_stmt *stmt;
  int           rc;
  const char   *indexName;
  char          query[100];
  int           isBTree;
  char          dropQuery[100];
  // 获取所有索引名称
  rc = sqlite3_prepare_v2(*ppDb, "SELECT name FROM sqlite_master WHERE type = 'index';", -1, &stmt, 0);
  if (rc != SQLITE_OK) {
    printf("Failed to retrieve index names: %s\n", sqlite3_errmsg(*ppDb));
    sqlite3_close(*ppDb);
    return;
  }
  // 打印索引名称
  while (sqlite3_step(stmt) == SQLITE_ROW) {
    printf("Index Names:\n");
    indexName = (const char *)sqlite3_column_text(stmt, 0);
    printf("%s\n", indexName);
  }

  // 遍历索引并关闭非 B+ 树索引
  while (sqlite3_step(stmt) == SQLITE_ROW) {
    indexName = (const char *)sqlite3_column_text(stmt, 0);

    // 获取索引类型
    sprintf(query, "PRAGMA index_info(%s);", indexName);
    rc = sqlite3_prepare_v2(*ppDb, query, -1, &stmt, 0);
    if (rc != SQLITE_OK) {
      printf("Failed to retrieve index info: %s\n", sqlite3_errmsg(*ppDb));
      sqlite3_finalize(stmt);
      sqlite3_close(*ppDb);
      return;
    }

    isBTree = 0;
    while (sqlite3_step(stmt) == SQLITE_ROW) {
      const char *type = (const char *)sqlite3_column_text(stmt, 2);
      if (strcmp(type, "BINARY") != 0) {
        isBTree = 1;
        break;
      }
    }
    sqlite3_finalize(stmt);

    // 关闭非 B+ 树索引
    if (!isBTree) {
      sprintf(dropQuery, "DROP INDEX %s;", indexName);
      rc = sqlite3_exec(*ppDb, dropQuery, 0, 0, 0);
      if (rc != SQLITE_OK) {
        printf("Failed to drop index: %s\n", sqlite3_errmsg(*ppDb));
        sqlite3_close(*ppDb);
        return;
      }
    }
  }
}
int closeDb(GNCDB *perDB, sqlite3 *ppDb, int flag)
{
  int rc = 0;
  if (flag == 1) {
    rc = GNCDB_close(&perDB);
    if (rc != GNCDB_SUCCESS) {
      printf("close GNCDB database error\n");
      return -1;
    }
    perDB = NULL;
  } else if (flag == 2) {
    rc = sqlite3_close(ppDb);
    if (rc != SQLITE_OK) {
      printf("close sqlite database error\n");
      return -1;
    }
    ppDb = NULL;
  } else {
    rc = GNCDB_close(&perDB);
    if (rc != GNCDB_SUCCESS) {
      printf("close GNCDB database error\n");
      return -1;
    }
    rc = sqlite3_close(ppDb);
    if (rc != SQLITE_OK) {
      printf("close sqlite database error\n");
      return -1;
    }
    perDB = NULL;
    ppDb  = NULL;
  }

  return 0;
}
int openDb(GNCDB **perDB, sqlite3 **ppDb, int flag)
{
  char  pragmaQuery[1024]   = {0};
  char  pageSizeQuery[1024] = {0};
  int   rc                  = 0;
  char *errMsg;
  if (flag == 1) {
    rc = GNCDB_open(perDB, "per_data.dat", 0, pageCacheSize[count]);
    if (rc != GNCDB_SUCCESS) {
      printf("open GNCDB database error\n");
      return -1;
    }
  } else if (flag == 2) {
    rc = sqlite3_open("sqlite_per_data.db", ppDb);
    if (rc != SQLITE_OK) {
      printf("Failed to open SQLite database: %s\n", sqlite3_errmsg(*ppDb));
      sqlite3_free(errMsg);
      return -1;
    }
    /*设置sqlite页大小*/
    sprintf(pageSizeQuery, "PRAGMA page_size = %d", PAGE_SIZE);
    rc = sqlite3_exec(*ppDb, pageSizeQuery, 0, 0, &errMsg);
    if (rc != SQLITE_OK) {
      printf("Error setting page size: %s\n", errMsg);
      sqlite3_free(errMsg);
      return -1;
    }

    /* 设置sqlite缓存池大小 */
    sprintf(pragmaQuery, "PRAGMA cache_size = %d", pageCacheSize[count]);
    rc = sqlite3_exec(*ppDb, pragmaQuery, 0, 0, &errMsg);
    if (rc != SQLITE_OK) {
      printf("Failed to set cache size: %s\n", errMsg);
      sqlite3_free(errMsg);
      return -1;
    }
  } else {
    rc = GNCDB_open(perDB, "per_data.dat", 0, pageCacheSize[count]);
    if (rc != GNCDB_SUCCESS) {
      printf("open GNCDB database error\n");
      return -1;
    }
    rc = sqlite3_open("sqlite_per_data.db", ppDb);
    if (rc != SQLITE_OK) {
      printf("Failed to open SQLite database: %s\n", sqlite3_errmsg(*ppDb));
      sqlite3_free(errMsg);
      return -1;
    }

    /*设置sqlite页大小*/
    sprintf(pageSizeQuery, "PRAGMA page_size = %d", PAGE_SIZE);
    rc = sqlite3_exec(*ppDb, pageSizeQuery, 0, 0, &errMsg);
    if (rc != SQLITE_OK) {
      printf("Error setting page size: %s\n", errMsg);
      sqlite3_free(errMsg);
      return -1;
    }

    /* 设置sqlite缓存池大小 */
    sprintf(pragmaQuery, "PRAGMA cache_size = %d", pageCacheSize[count]);
    rc = sqlite3_exec(*ppDb, pragmaQuery, 0, 0, &errMsg);
    if (rc != SQLITE_OK) {
      printf("Failed to set cache size: %s\n", errMsg);
      sqlite3_free(errMsg);
      return -1;
    }
  }

  return 0;
  // closeSqliteIndex(ppDb);
}

void closeCSVfile()
{
  // fclose(resFp);

  worksheet_set_column(tpcc_worksheet, 0, 0, 15, NULL);
  worksheet_set_column(tpcc_worksheet, 1, 1, 10, NULL);
  worksheet_set_column(tpcc_worksheet, 2, 2, 10, NULL);
  worksheet_set_column(tpcc_worksheet, 3, 3, 10, NULL);
  worksheet_set_column(tpcc_worksheet, 4, 4, 10, NULL);
  worksheet_set_column(tpcc_worksheet, 5, 5, 25, NULL);
  worksheet_set_column(tpcc_worksheet, 6, 6, 15, NULL);

  worksheet_set_column(tpch_worksheet, 0, 0, 35, NULL);
  worksheet_set_column(tpch_worksheet, 1, 1, 25, NULL);
  worksheet_set_column(tpch_worksheet, 2, 2, 15, NULL);
  worksheet_set_column(tpch_worksheet, 3, 3, 15, NULL);
  worksheet_set_column(tpch_worksheet, 4, 4, 10, NULL);

  worksheet_set_column(pertest_worksheet, 0, 0, 15, NULL);
  worksheet_set_column(pertest_worksheet, 1, 1, 65, NULL);
  worksheet_set_column(pertest_worksheet, 2, 2, 65, NULL);
  worksheet_set_column(pertest_worksheet, 3, 3, 30, NULL);
  worksheet_set_column(pertest_worksheet, 4, 4, 15, NULL);
  worksheet_set_column(pertest_worksheet, 5, 5, 20, NULL);
  worksheet_set_column(pertest_worksheet, 6, 6, 20, NULL);
  worksheet_set_column(pertest_worksheet, 7, 7, 25, NULL);
  worksheet_set_column(pertest_worksheet, 8, 8, 25, NULL);
  worksheet_set_column(pertest_worksheet, 9, 9, 15, NULL);

  worksheet_set_column(conpertest_worksheet, 0, 0, 15, NULL);
  worksheet_set_column(conpertest_worksheet, 1, 1, 65, NULL);
  worksheet_set_column(conpertest_worksheet, 2, 2, 65, NULL);
  worksheet_set_column(conpertest_worksheet, 3, 3, 30, NULL);
  worksheet_set_column(conpertest_worksheet, 4, 4, 15, NULL);
  worksheet_set_column(conpertest_worksheet, 5, 5, 20, NULL);
  worksheet_set_column(conpertest_worksheet, 6, 6, 20, NULL);
  worksheet_set_column(conpertest_worksheet, 7, 7, 25, NULL);
  worksheet_set_column(conpertest_worksheet, 8, 8, 25, NULL);
  worksheet_set_column(conpertest_worksheet, 9, 9, 15, NULL);

  workbook_close(workbook);
}

/// @brief 空表插入
/// @return
int insertPerTest_1(GNCDB *perDB, sqlite3 *ppDb)
{
  FILE             *fp           = NULL;
  int               i            = 0;
  int               rc           = 0;
  int               count        = 0;
  char              partPath[60] = {0};
  int               falseNum     = 0;
  struct PerTestRes result       = {"空表插入",
            "INSERT INTO part (...) VALUES (...)",
            "向空表中顺序插入数据",
            PERPARTROWS,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  char insertQuery[1024];

  /* part表 */
  sprintf(partPath, "%s%s", pertpchpath, perpartFileName);
  fp = fopen(partPath, "r");
  if (fp == NULL) {
    return -1;
  }
  for (i = 0; i < PERPARTROWS; ++i) {
    time_per_read  = 0.0;
    time_per_write = 0.0;
    count          = fscanf(fp,
        "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
        &parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        &parttbl.size,
        parttbl.container,
        &parttbl.retailprice,
        parttbl.comment);
    if (count != 9) {}
    sprintf(insertQuery,
        "INSERT INTO part (p_partkey, p_name, p_mfgr, p_brand, p_type, p_size, p_container, p_retailprice, p_comment) "
        "VALUES (%d, '%s', '%s', '%s', '%s', %d, '%s', %lf, '%s');",
        parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        parttbl.size,
        parttbl.container,
        parttbl.retailprice,
        parttbl.comment);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, insertQuery, &rc);
      if (rc) {
        falseNum++;
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_insert(perDB,
          NULL,
          "part",
          parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          parttbl.size,
          parttbl.container,
          parttbl.retailprice,
          parttbl.comment);
      clock_gettime(CLOCK_MONOTONIC, &end);
      if (rc) {
        falseNum++;
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);
    // sqlite操作时间
    time_used_sqlite = sqliteExecTime(ppDb, insertQuery);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
  }
  fclose(fp);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 已有5000条数据再进行插入
/// @return
int insertPerTest_2(GNCDB *perDB, sqlite3 *ppDb)
{
  FILE             *fp           = NULL;
  int               i            = 0;
  int               rc           = 0;
  int               count        = 0;
  char              partPath[60] = {0};
  int               falseNum     = 0;
  struct PerTestRes result       = {"非空表插入",
            "INSERT INTO part (...) VALUES (...)",
            "向非空表顺序插入",
            PERPARTROWS,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0.0,
            DBL_MIN,
            DBL_MAX,
            0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  char insertQuery[1024];
  /* part表 */
  sprintf(partPath, "%s%s", pertpchpath, perpartFileName);
  fp = fopen(partPath, "r");
  if (fp == NULL) {
    return -1;
  }
  for (i = 0; i < PERPARTROWS * 2 + falseNum; ++i) {
    time_per_read  = 0.0;
    time_per_write = 0.0;
    count          = fscanf(fp,
        "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
        &parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        &parttbl.size,
        parttbl.container,
        &parttbl.retailprice,
        parttbl.comment);
    if (count != 9) {}
    if (i < PERPARTROWS) {
      continue;
    }

    sprintf(insertQuery,
        "INSERT INTO part (p_partkey, p_name, p_mfgr, p_brand, p_type, p_size, p_container, p_retailprice, p_comment) "
        "VALUES (%d, '%s', '%s', '%s', '%s', %d, '%s', %lf, '%s');",
        parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        parttbl.size,
        parttbl.container,
        parttbl.retailprice,
        parttbl.comment);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, insertQuery, &rc);
      if (rc) {
        falseNum++;
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_insert(perDB,
          NULL,
          "part",
          parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          parttbl.size,
          parttbl.container,
          parttbl.retailprice,
          parttbl.comment);
      if (rc) {
        falseNum++;
        continue;
      }
      clock_gettime(CLOCK_MONOTONIC, &end);
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);
    // sqlite操作时间
    time_used_sqlite = sqliteExecTime(ppDb, insertQuery);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
  }
  fclose(fp);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 空表随机插入
/// @return
int insertPerTest_3(GNCDB *perDB, sqlite3 *ppDb)
{
  FILE             *fp           = NULL;
  int               i            = 0;
  int               rc           = 0;
  int               count        = 0;
  int               falseNum     = 0;
  char              partPath[60] = {0};
  char             *data         = NULL;
  int               random       = 0;
  char              line[1024];
  struct PerTestRes result = {"空表随机插入",
      "INSERT INTO part (...) VALUES (...)",
      "向空表中随机插入数据",
      PERPARTROWS,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  char insertQuery[1024];

  /* part表 */
  sprintf(partPath, "%s%s", pertpchpath, perpartFileName);
  fp = fopen(partPath, "r");
  for (i = 0; i < PERPARTROWS; ++i) {
    time_per_read  = 0.0;
    time_per_write = 0.0;
    random         = generateRandomNumber(0, 200000);
    fseek(fp, 0, SEEK_SET);
    // 逐行读取文件
    while (fgets(line, sizeof(line), fp) != NULL) {
      if (random-- == 0) {
        data = my_strdup(line);
        break;
      }
    }
    count = sscanf(data,
        "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
        &parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        &parttbl.size,
        parttbl.container,
        &parttbl.retailprice,
        parttbl.comment);
    if (count != 9) {}
    sprintf(insertQuery,
        "INSERT INTO part (p_partkey, p_name, p_mfgr, p_brand, p_type, p_size, p_container, p_retailprice, p_comment) "
        "VALUES (%d, '%s', '%s', '%s', '%s', %d, '%s', %lf, '%s');",
        parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        parttbl.size,
        parttbl.container,
        parttbl.retailprice,
        parttbl.comment);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, insertQuery, &rc);
      if (rc == GNCDB_DUPLICATE_PRIMARY_KEY) {
        i--;
        continue;
      }
      if (rc) {
        falseNum++;
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_insert(perDB,
          NULL,
          "part",
          parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          parttbl.size,
          parttbl.container,
          parttbl.retailprice,
          parttbl.comment);
      clock_gettime(CLOCK_MONOTONIC, &end);
      if (rc == GNCDB_DUPLICATE_PRIMARY_KEY) {
        i--;
        continue;
      }
      if (rc) {
        falseNum++;
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);
    // sqlite操作时间
    time_used_sqlite = sqliteExecTime(ppDb, insertQuery);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
  }
  result.exeCount -= falseNum;

  fclose(fp);
  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 非空表随机插入
/// @return
int insertPerTest_4(GNCDB *perDB, sqlite3 *ppDb)
{
  FILE             *fp           = NULL;
  int               i            = 0;
  int               rc           = 0;
  int               count        = 0;
  char             *data         = NULL;
  int               falseNum     = 0;
  char              partPath[60] = {0};
  int               random       = 0;
  char              line[1024];
  struct PerTestRes result = {"非空表随机插入",
      "INSERT INTO part (...) VALUES (...)",
      "向非空表中随机插入数据",
      PERPARTROWS,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  char insertQuery[1024];

  /* part表 */
  sprintf(partPath, "%s%s", pertpchpath, perpartFileName);
  fp = fopen(partPath, "r");
  for (i = 0; i < PERPARTROWS; ++i) {
    random = generateRandomNumber(0, 200000);
    fseek(fp, 0, SEEK_SET);
    // 逐行读取文件
    while (fgets(line, sizeof(line), fp) != NULL) {
      if (random-- == 0) {
        data = my_strdup(line);
        break;
      }
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;
    count          = sscanf(data,
        "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
        &parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        &parttbl.size,
        parttbl.container,
        &parttbl.retailprice,
        parttbl.comment);
    if (count != 9) {}
    sprintf(insertQuery,
        "INSERT INTO part (p_partkey, p_name, p_mfgr, p_brand, p_type, p_size, p_container, p_retailprice, p_comment) "
        "VALUES (%d, '%s', '%s', '%s', '%s', %d, '%s', %lf, '%s');",
        parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        parttbl.size,
        parttbl.container,
        parttbl.retailprice,
        parttbl.comment);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, insertQuery, &rc);
      if (rc == GNCDB_DUPLICATE_PRIMARY_KEY) {
        i--;
        continue;
      }
      if (rc) {
        falseNum++;
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_insert(perDB,
          NULL,
          "part",
          parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          parttbl.size,
          parttbl.container,
          parttbl.retailprice,
          parttbl.comment);
      clock_gettime(CLOCK_MONOTONIC, &end);
      if (rc == GNCDB_DUPLICATE_PRIMARY_KEY) {
        i--;
        continue;
      }
      if (rc) {
        falseNum++;
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    getTimeUsed(&result, time_used_singel);
    // sqlite操作时间
    time_used_sqlite = sqliteExecTime(ppDb, insertQuery);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
  }
  result.exeCount -= falseNum;

  fclose(fp);
  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
void writeOPType(char *opType, int x)
{
  // fwrite(opType,strlen(opType),1,resFp);
  // fflush(resFp);

  // int x; int y;
  lxw_format    *title_format;
  lxw_worksheet *current = NULL;
  if (perEx.flag == 1) {
    current = pertest_worksheet;
  } else {
    current = conpertest_worksheet;
  }
  // x = perEx.x;
  // y = 0;

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);
  format_set_font_size(title_format, 16);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
  worksheet_merge_range(current, x + 1, 0, perEx.x, 0, opType, title_format);
}
/// @brief 插入测试
/// @return
void insertPerTest()
{
  GNCDB   *perDB;
  sqlite3 *ppDb;
  int      rc = 0;
  int      x  = perEx.x;
  rc          = sqliteDBInit(&ppDb);
  if (rc != 0) {
    printf("sqlite init error\n");
    return;
  }
  rc = perTestDBInit(&perDB);
  if (rc != 0) {
    printf("perTest init error\n");
    return;
  }
  insertPerTest_1(perDB, ppDb);
  insertPerTest_2(perDB, ppDb);
  rc = GNCDB_close(&perDB);
  if (rc) {
    printf("perTest close error\n");
    return;
  }
  rc = sqlite3_close(ppDb);
  if (rc) {
    printf("sqlite close error\n");
    return;
  }

  rc = sqliteDBInit(&ppDb);
  if (rc != 0) {
    printf("sqlite init error\n");
    return;
  }
  rc = perTestDBInit(&perDB);
  if (rc != 0) {
    printf("perTest init error\n");
    return;
  }

  insertPerTest_3(perDB, ppDb);
  insertPerTest_4(perDB, ppDb);
  rc = GNCDB_close(&perDB);
  if (rc) {
    printf("perTest close error\n");
    return;
  }
  rc = sqlite3_close(ppDb);
  if (rc) {
    printf("sqlite close error\n");
    return;
  }

  writeOPType("插入测试", x);
}
/// @brief SQLITE数据库数据初始化
/// @param ppDb 数据库
/// @param rows 行数
/// @return
int sqliteDataInit(sqlite3 *ppDb, int rows, int flag)
{
  FILE *fp               = NULL;
  int   i                = 0;
  int   count            = 0;
  char  partPath[60]     = {0};
  char  supplierPath[60] = {0};
  char  partsuppPath[60] = {0};
  char  insertPartQuery[1024];
  char  insertSuppQuery[1024];
  char  insertPartSuppQuery[1024];
  int   falseNum = 0;

  char *data   = NULL;
  int   random = 0;
  char  line[1024];
  // bool initpart = flag / 2 < 1 ? true : false;
  // bool initsupp = flag % 2 == 1 ? false : true;
  bool initpart = true;
  bool initsupp = true;
  // const char *query0 = "PRAGMA page_size;";
  // sqlite3_stmt *stmt;

  /* 查询sqlite页面大小 */
  // sqlite3_prepare_v2(ppDb, query0, -1, &stmt, NULL);
  // sqlite3_step(stmt);
  // printf("sqlite Page size: %d bytes\n", sqlite3_column_int(stmt, 0));

  /* part表 */  // 开始事务
  sqlite3_exec(ppDb, "BEGIN TRANSACTION;", NULL, 0, NULL);
  sprintf(partPath, "%s%s", pertpchpath, perpartFileName);
  fp = fopen(partPath, "r");
  if (fp == NULL) {
    return -1;
  }
  rows = partDataSize[2];
  for (i = 0; i < rows && initpart; ++i) {

    if (INSERT_DATA_ININT) {
      random = generateRandomNumber(0, rows);
      fseek(fp, 0, SEEK_SET);
      // 逐行读取文件
      while (fgets(line, sizeof(line), fp) != NULL) {
        if (random-- == 0) {
          data = my_strdup(line);
          break;
        }
      }
      count = sscanf(data,
          "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
          &parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          &parttbl.size,
          parttbl.container,
          &parttbl.retailprice,
          parttbl.comment);
      if (count != 9) {}
    } else {
      count = fscanf(fp,
          "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
          &parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          &parttbl.size,
          parttbl.container,
          &parttbl.retailprice,
          parttbl.comment);
    }
    sprintf(insertPartQuery,
        "INSERT INTO part (p_partkey, p_name, p_mfgr, p_brand, p_type, p_size, p_container, p_retailprice, p_comment) "
        "VALUES (%d, '%s', '%s', '%s', '%s', %d, '%s', %lf, '%s');",
        parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        parttbl.size,
        parttbl.container,
        parttbl.retailprice,
        parttbl.comment);
    if (sqliteExecTime(ppDb, insertPartQuery) < 0) {
      i--;
      falseNum++;
    }
  }
  fclose(fp);
  if (falseNum > 0)
    printf("sqlite insert part falseNum:%d\n", falseNum);

  /* supplier表 */
  falseNum = 0;
  sprintf(supplierPath, "%s%s", pertpchpath, persupplierFileName);
  fp = fopen(supplierPath, "r");
  if (fp == NULL) {
    return -1;
  }
  rows = supplierDataSize[2];
  for (i = 0; i < rows && initsupp; ++i) {
    count = fscanf(fp,
        "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
        &suppliertbl.suppkey,
        suppliertbl.name,
        suppliertbl.address,
        &suppliertbl.nationkey,
        suppliertbl.phone,
        &suppliertbl.acctbal,
        suppliertbl.comment);
    // len = strlen(suppliertbl.name);
    // if (len < 24) {
    //   memset(suppliertbl.name + len, ' ', 24 - len);
    //   suppliertbl.name[24] = '\0';
    // }
    // len = strlen(suppliertbl.address);
    // if (len < 39) {
    //   memset(suppliertbl.address + len, ' ', 39 - len);
    //   suppliertbl.address[39] = '\0';
    // }
    // len = strlen(suppliertbl.phone);
    // if (len < 14) {
    //   memset(suppliertbl.phone + len, ' ', 14 - len);
    //   suppliertbl.phone[14] = '\0';
    // }
    // len = strlen(suppliertbl.comment);
    // if (len < 100) {
    //   memset(suppliertbl.comment + len, ' ', 100 - len);
    //   suppliertbl.comment[100] = '\0';
    // }
    sprintf(insertSuppQuery,
        "INSERT INTO supplier (s_suppkey, s_name, s_address, s_nationkey, s_phone, s_acctbal, s_comment) "
        "VALUES (%d, '%s', '%s', %d, '%s', %lf, '%s');",
        suppliertbl.suppkey,
        suppliertbl.name,
        suppliertbl.address,
        suppliertbl.nationkey,
        suppliertbl.phone,
        suppliertbl.acctbal,
        suppliertbl.comment);
    if (sqliteExecTime(ppDb, insertSuppQuery) < 0)
      falseNum++;
  }
  fclose(fp);
  if (falseNum > 0)
    printf("sqlite insert supplier falseNum:%d\n", falseNum);

  /* partsupp表 */
  falseNum = 0;
  sprintf(partsuppPath, "%s%s", pertpchpath, perpartsuppFileName);
  fp = fopen(partsuppPath, "r");
  if (fp == NULL) {
    return -1;
  }
  rows = partSuppDataSize[2];
  for (i = 0; i < rows && initsupp; ++i) {
    count = fscanf(fp,
        "%d|%d|%d|%lf|%[^|]|\n",
        &partsupptbl.partkey,
        &partsupptbl.suppkey,
        &partsupptbl.availqty,
        &partsupptbl.supplycost,
        partsupptbl.comment);
    // len     = strlen(partsupptbl.comment);
    // if (len < 24) {
    //   memset(partsupptbl.comment + len, ' ', 24 - len);
    //   partsupptbl.comment[24] = '\0';
    // }
    sprintf(insertPartSuppQuery,
        "INSERT INTO partsupp (ps_partkey, ps_suppkey, ps_availqty, ps_supplycost, ps_comment) "
        "VALUES (%d, %d, %d, %lf, '%s');",
        partsupptbl.partkey,
        partsupptbl.suppkey,
        partsupptbl.availqty,
        partsupptbl.supplycost,
        partsupptbl.comment);
    if (sqliteExecTime(ppDb, insertPartSuppQuery) < 0)
      falseNum++;
  }
  fclose(fp);
  // 提交事务
  sqlite3_exec(ppDb, "COMMIT;", NULL, 0, NULL);
  if (falseNum > 0)
    printf("sqlite insert supplier falseNum:%d\n", falseNum);
  return 0;
}
/// @brief 数据库数据初始化
/// @param perDB 数据库
/// @param rows 行数
/// @param flag 0初始化两张表，1初始化PART，2初始化SUPPLIER
/// @return
int perTestDataInit(GNCDB *perDB, int rows, int flag)
{
  FILE *fp     = NULL;
  int   i      = 0;
  int   rc     = 0;
  int   count  = 0;
  char *data   = NULL;
  int   random = 0;
  char  line[1024];
  char  partPath[60]     = {0};
  char  supplierPath[60] = {0};
  char  partsuppPath[60] = {0};
  // bool initpart = flag / 2 < 1 ? true : false;
  // bool initsupp = flag % 2 == 1 ? false : true;

  bool initpart = true;
  bool initsupp = true;

  /* part表 */
  sprintf(partPath, "%s%s", pertpchpath, perpartFileName);
  fp = fopen(partPath, "r");
  if (fp == NULL) {
    return -1;
  }
  rows = partDataSize[2];
  for (i = 0; i < rows && initpart; ++i) {
    if (INSERT_DATA_ININT) {
      random = generateRandomNumber(0, rows);
      fseek(fp, 0, SEEK_SET);
      // 逐行读取文件
      while (fgets(line, sizeof(line), fp) != NULL) {
        if (random-- == 0) {
          data = my_strdup(line);
          break;
        }
      }
      count = sscanf(data,
          "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
          &parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          &parttbl.size,
          parttbl.container,
          &parttbl.retailprice,
          parttbl.comment);
      if (count != 9) {}
    } else {
      count = fscanf(fp,
          "%d|%[^|]|%[^|]|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
          &parttbl.partkey,
          parttbl.name,
          parttbl.mfgr,
          parttbl.brand,
          parttbl.type,
          &parttbl.size,
          parttbl.container,
          &parttbl.retailprice,
          parttbl.comment);
    }

    rc = GNCDB_insert(perDB,
        NULL,
        "part",
        parttbl.partkey,
        parttbl.name,
        parttbl.mfgr,
        parttbl.brand,
        parttbl.type,
        parttbl.size,
        parttbl.container,
        parttbl.retailprice,
        parttbl.comment);

    if (rc == GNCDB_DUPLICATE_PRIMARY_KEY || rc == -90) {
      i--;
      continue;
    }

    if (rc != GNCDB_SUCCESS) {
      fclose(fp);
      return rc;
    }
  }
  fclose(fp);

  /* supplier表 */
  sprintf(supplierPath, "%s%s", pertpchpath, persupplierFileName);
  fp = fopen(supplierPath, "r");
  if (fp == NULL) {
    return -1;
  }
  rows = supplierDataSize[2];
  for (i = 0; i < rows && initsupp; ++i) {
    count = fscanf(fp,
        "%d|%[^|]|%[^|]|%d|%[^|]|%lf|%[^|]|\n",
        &suppliertbl.suppkey,
        suppliertbl.name,
        suppliertbl.address,
        &suppliertbl.nationkey,
        suppliertbl.phone,
        &suppliertbl.acctbal,
        suppliertbl.comment);
    if (count != 7) {}
    rc = GNCDB_insert(perDB,
        NULL,
        "supplier",
        suppliertbl.suppkey,
        suppliertbl.name,
        suppliertbl.address,
        suppliertbl.nationkey,
        suppliertbl.phone,
        suppliertbl.acctbal,
        suppliertbl.comment);
    if (rc != GNCDB_SUCCESS) {
      fclose(fp);
      return rc;
    }
  }
  fclose(fp);

  /* partsupp表 */
  sprintf(partsuppPath, "%s%s", pertpchpath, perpartsuppFileName);
  fp = fopen(partsuppPath, "r");
  if (fp == NULL) {
    return -1;
  }
  rows = partSuppDataSize[2];
  for (i = 0; i < rows && initsupp; ++i) {
    count = fscanf(fp,
        "%d|%d|%d|%lf|%[^|]|\n",
        &partsupptbl.partkey,
        &partsupptbl.suppkey,
        &partsupptbl.availqty,
        &partsupptbl.supplycost,
        partsupptbl.comment);
    rc    = GNCDB_insert(perDB,
        NULL,
        "partsupp",
        partsupptbl.partkey,
        partsupptbl.suppkey,
        partsupptbl.availqty,
        partsupptbl.supplycost,
        partsupptbl.comment);
    if (rc != GNCDB_SUCCESS) {
      fclose(fp);
      return rc;
    }
  }
  fclose(fp);
  return 0;
}

/// @brief 单表全查询
/// @return
int rc = 0;  // 用于存储函数返回码
int selectPerTest_1(GNCDB *perDB, sqlite3 *ppDb)
{
  int rows     = 0;  // 用于存储查询结果的行数
  int i        = 0;  // 循环计数器
  int falseNum = 0;  // 记录执行失败的次数

  const char *query = "SELECT * FROM part;";  // SQL查询语句，查询part表的所有内容
                                              // 初始化测试结果结构体，用于存储无回调函数的测试结果
  struct PerTestRes result = {"单表全查询(无callback)",
      "SELECT * FROM part",
      "查询part表所有内容(无callback)",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  // 初始化测试结果结构体，用于存储有回调函数的测试结果
  struct PerTestRes result1 = {"单表全查询(有callback)",
      "SELECT * FROM part",
      "查询part表所有内容(有callback)",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  // 初始化测试结果结构体，用于存储空回调函数的测试结果
  struct PerTestRes result2 = {"单表全查询(空callback)",
      "SELECT * FROM part",
      "查询part表所有内容(空callback)",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;                              // 用于记录时间戳
  double          time_used_singel, time_used_sqlite = 0;  // 用于存储单次执行时间和SQLite执行时间
  // 单表全查询,不带回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc) {
      printf("openDb error\n");
      continue;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        continue;
      }
    } else {

      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, NULL, &rows, NULL, 1, 0, 0, "part");
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    getTimeUsed(&result, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }

    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      // printf("sqlite insert error\n");
      rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
  }

  result.falseNum = falseNum;
  writeToCSV(result);

  // //单表全查询,带回调函数
  falseNum = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimecallback(perDB, query, &rc);
      if (rc)
        falseNum++;
    } else {

      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBack1, &rows, NULL, 1, 0, 0, "part");
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc)
        falseNum++;

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    getTimeUsed(&result1, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }

    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }
    time_used_sqlite = sqliteExecTimecallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result1.time_sqlite_avg += time_used_sqlite;
      if (result1.time_sqlite_max < time_used_sqlite) {
        result1.time_sqlite_max = time_used_sqlite;
      }
      if (result1.time_sqlite_min > time_used_sqlite) {
        result1.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
  }

  result1.falseNum = falseNum;
  writeToCSV(result1);

  // 单表全查询,空回调函数
  falseNum = 0;
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBackNULL1, &rows, NULL, 1, 0, 0, "part");
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result2, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }

    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimeNULLcallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result2.time_sqlite_avg += time_used_sqlite;
      if (result2.time_sqlite_max < time_used_sqlite) {
        result2.time_sqlite_max = time_used_sqlite;
      }
      if (result2.time_sqlite_min > time_used_sqlite) {
        result2.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
  }
  result2.falseNum = falseNum;
  writeToCSV(result2);

  return 1;
}
/// @brief 单表主键查询
/// @return
int selectPerTest_2(GNCDB *perDB, sqlite3 *ppDb)
{
  int               rc       = 0;
  int               rows     = 0;
  int               i        = 0;
  int               falseNum = 0;
  char             *token    = NULL;
  char              str[128];
  char              query[1024] = {0};
  struct PerTestRes result      = {"单表主键查询(无callback)",
           "SELECT * FROM part WHERE p_partkey=? ",
           "根据主键值查询指定某条数据",
           PTR_arg.TestCount,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0};

  struct PerTestRes result1 = {"单表主键查询(有callback)",
      "SELECT * FROM part WHERE p_partkey=? ",
      "根据主键值查询指定某条数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result2 = {"单表主键查询(空callback)",
      "SELECT * FROM part WHERE p_partkey=? ",
      "根据主键值查询指定某条数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token = getRandomColumnValue("part", 1, partDataSize[2]);
    sprintf(str, "p_partkey=%s", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, NULL, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }

    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }
    // sqlite执行时间
    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
  }

  result.falseNum = falseNum;
  writeToCSV(result);

  falseNum = 0;
  // 单表主键查询,带回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    token = getRandomColumnValue("part", 1, partDataSize[2]);
    sprintf(str, "p_partkey=%s", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimecallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBack1, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result1, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }

    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimecallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result1.time_sqlite_avg += time_used_sqlite;
      if (result1.time_sqlite_max < time_used_sqlite) {
        result1.time_sqlite_max = time_used_sqlite;
      }
      if (result1.time_sqlite_min > time_used_sqlite) {
        result1.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
  }

  result1.falseNum = falseNum;
  writeToCSV(result1);

  falseNum = 0;
  // 单表主键查询,空回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    token = getRandomColumnValue("part", 1, partDataSize[2]);
    sprintf(str, "p_partkey=%s", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBackNULL1, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result2, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }

    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimeNULLcallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result2.time_sqlite_avg += time_used_sqlite;
      if (result2.time_sqlite_max < time_used_sqlite) {
        result2.time_sqlite_max = time_used_sqlite;
      }
      if (result2.time_sqlite_min > time_used_sqlite) {
        result2.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
  }
  result2.falseNum = falseNum;
  writeToCSV(result2);
  return 1;
}
/// @brief 单表主键范围查询
/// @return
int selectPerTest_3(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc   = 0;
  int   rows = 0, rowsSum = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   random   = 0;
  char *token;
  char  str[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"单表主键范围查询(无callback)",
      "SELECT * FROM part WHERE p_partkey??",
      "主键随机指定范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result1 = {"单表主键范围查询(有callback)",
      "SELECT * FROM part WHERE p_partkey??",
      "主键随机指定范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result2 = {"单表主键范围查询(空callback)",
      "SELECT * FROM part WHERE p_partkey??",
      "主键随机指定范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "supplier");
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, NULL, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间
    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);

  falseNum = 0;
  // 单表主键范围查询,带回调函数
  for (i = 0; i < EXECUTION_COUNT; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimecallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBack1, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result1, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc != 0) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimecallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result1.time_sqlite_avg += time_used_sqlite;
      if (result1.time_sqlite_max < time_used_sqlite) {
        result1.time_sqlite_max = time_used_sqlite;
      }
      if (result1.time_sqlite_min > time_used_sqlite) {
        result1.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc != 0) {

      printf("closeDb error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (EXECUTION_COUNT - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result1.op, percentageStr);

  result1.falseNum = falseNum;
  writeToCSV(result1);

  falseNum = 0;
  // 单表主键范围查询,空回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBackNULL1, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    rowsSum += rows;
    getTimeUsed(&result2, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc != 0) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimeNULLcallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result2.time_sqlite_avg += time_used_sqlite;
      if (result2.time_sqlite_max < time_used_sqlite) {
        result2.time_sqlite_max = time_used_sqlite;
      }
      if (result2.time_sqlite_min > time_used_sqlite) {
        result2.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result2.op, percentageStr);

  result2.falseNum = falseNum;
  writeToCSV(result2);

  return 1;
}
/// @brief 单表非主键范围查询
/// @return
int selectPerTest_4(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result  = {"单表非主键范围查询(无callback)",
       "SELECT * FROM part WHERE p_size??",
       "非主键随机指定范围查询",
       PTR_arg.TestCount,
       0.0,
       DBL_MIN,
       DBL_MAX,
       0.0,
       DBL_MIN,
       DBL_MAX,
       0.0,
       DBL_MIN,
       DBL_MAX,
       0.0,
       DBL_MIN,
       DBL_MAX,
       0};
  struct PerTestRes result1 = {"单表非主键范围查询(有callback)",
      "SELECT * FROM part WHERE p_size??",
      "非主键随机指定范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result2 = {"单表非主键范围查询(空callback)",
      "SELECT * FROM part WHERE p_size??",
      "非主键随机指定范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, NULL, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc != 0) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);

  falseNum = 0;
  // 单表非主键范围查询,带回调函数
  for (i = 0; i < EXECUTION_COUNT; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimecallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBack1, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result1, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      return -1;
    }

    rc = openDb(&perDB, &ppDb, 2);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimecallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result1.time_sqlite_avg += time_used_sqlite;
      if (result1.time_sqlite_max < time_used_sqlite) {
        result1.time_sqlite_max = time_used_sqlite;
      }
      if (result1.time_sqlite_min > time_used_sqlite) {
        result1.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (EXECUTION_COUNT - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result1.op, percentageStr);

  result1.falseNum = falseNum;
  writeToCSV(result1);

  falseNum = 0;
  // 单表非主键范围查询,空回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("open db error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT * FROM part WHERE %s;", str);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBackNULL1, &rows, NULL, 1, 0, 1, "part", str);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    rowsSum += rows;
    getTimeUsed(&result2, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("open db error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimeNULLcallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result2.time_sqlite_avg += time_used_sqlite;
      if (result2.time_sqlite_max < time_used_sqlite) {
        result2.time_sqlite_max = time_used_sqlite;
      }
      if (result2.time_sqlite_min > time_used_sqlite) {
        result2.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result2.op, percentageStr);

  result2.falseNum = falseNum;
  writeToCSV(result2);

  return 1;
}
/// @brief 单表多条件含主键范围查询
/// @return
int selectPerTest_5(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str1[128];
  char  str2[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"单表多条件含主键范围查询(无callback)",
      "SELECT * FROM part WHERE p_partkey?? AND p_size??",
      "随机主键范围+非主键范围查询单表数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result1 = {"单表多条件含主键范围查询(有callback)",
      "SELECT * FROM part WHERE p_partkey?? AND p_size??",
      "随机主键范围+非主键范围查询单表数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result2 = {"单表多条件含主键范围查询(空callback)",
      "SELECT * FROM part WHERE p_partkey?? AND p_size??",
      "随机主键范围+非主键范围查询单表数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("open db error\n");
      continue;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str2, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "SELECT * FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      // rc = GNCDB_select(perDB, NULL, &rows, 1, 0, 1, "supplier", "s_acctbal>2000.00");
      rc = GNCDB_select(perDB, NULL, &rows, NULL, 1, 0, 2, "part", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间
    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("open db error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);

  falseNum = 0;
  // 单表多条件含主键范围查询,带回调函数
  for (i = 0; i < EXECUTION_COUNT; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("open db error\n");
      continue;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str2, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "SELECT * FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBack1, &rows, NULL, 1, 0, 2, "part", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result1, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("open db error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimecallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result1.time_sqlite_avg += time_used_sqlite;
      if (result1.time_sqlite_max < time_used_sqlite) {
        result1.time_sqlite_max = time_used_sqlite;
      }
      if (result1.time_sqlite_min > time_used_sqlite) {
        result1.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }

  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (EXECUTION_COUNT - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result1.op, percentageStr);

  result1.falseNum = falseNum;
  writeToCSV(result1);

  falseNum = 0;
  // 单表多条件含主键范围查询,空回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("open db error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str2, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "SELECT * FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBackNULL1, &rows, NULL, 1, 0, 2, "part", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result2, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("open db error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimeNULLcallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result2.time_sqlite_avg += time_used_sqlite;
      if (result2.time_sqlite_max < time_used_sqlite) {
        result2.time_sqlite_max = time_used_sqlite;
      }
      if (result2.time_sqlite_min > time_used_sqlite) {
        result2.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result2.op, percentageStr);

  result2.falseNum = falseNum;
  writeToCSV(result2);

  return 1;
}
/// @brief 单表多条件不含主键范围查询
/// @return
int selectPerTest_6(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  int   random  = 0;
  char *token;
  char  str1[128];
  char  str2[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"单表多条件不含主键范围查询(无callback)",
      "SELECT * FROM part WHERE p_size?? AND p_retailprice??",
      "两个非主键属性随机范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result1 = {"单表多条件不含主键范围查询(有callback)",
      "SELECT * FROM part WHERE p_size?? AND p_retailprice??",
      "两个非主键属性随机范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct PerTestRes result2 = {"单表多条件不含主键范围查询(空callback)",
      "SELECT * FROM part WHERE p_size?? AND p_retailprice??",
      "两个非主键属性随机范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;
  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("open db error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 8, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str2, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "SELECT * FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      // rc = GNCDB_select(perDB, NULL, &rows, 1, 0, 1, "supplier", "s_acctbal>2000.00");
      rc = GNCDB_select(perDB, NULL, &rows, NULL, 1, 0, 2, "part", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("open db error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);

  falseNum = 0;
  // 单表多条件不含主键范围查询,带回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("open db error\n");
      continue;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 8, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str2, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "SELECT * FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBack1, &rows, NULL, 1, 0, 2, "part", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    rowsSum += rows;
    getTimeUsed(&result1, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("open db error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimecallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result1.time_sqlite_avg += time_used_sqlite;
      if (result1.time_sqlite_max < time_used_sqlite) {
        result1.time_sqlite_max = time_used_sqlite;
      }
      if (result1.time_sqlite_min > time_used_sqlite) {
        result1.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  result1.falseNum = falseNum;
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (EXECUTION_COUNT - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result1.op, percentageStr);

  result1.falseNum = falseNum;
  writeToCSV(result1);

  falseNum = 0;
  // 单表多条件不含主键范围查询,空回调函数
  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("open db error\n");
      continue;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 8, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str2, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "SELECT * FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTimeNULLcallback(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(perDB, myCallBackNULL1, &rows, NULL, 1, 0, 2, "part", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    rowsSum += rows;
    getTimeUsed(&result2, time_used_singel);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("open db error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTimeNULLcallback(ppDb, query);
    if (time_used_sqlite > 0) {
      result2.time_sqlite_avg += time_used_sqlite;
      if (result2.time_sqlite_max < time_used_sqlite) {
        result2.time_sqlite_max = time_used_sqlite;
      }
      if (result2.time_sqlite_min > time_used_sqlite) {
        result2.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result2.op, percentageStr);

  result2.falseNum = falseNum;
  writeToCSV(result2);

  return 1;
}
/// @brief 连接主键等值查询
/// @return
int selectPerTest_7(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  char *token;
  char  str1[128];
  char  query[1024] = {0};

  struct PerTestRes result = {"两表连接主键等值查询",
      "SELECT p_name, ps_availqty FROM part, partsupp "
      "WHERE p_partkey = ps_partkey AND p_partkey=?;",
      "两表主键连接后并且part主键等值查询某条数据（查询某一零件的名字和可用库存）",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {

    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token = getRandomColumnValue("part", 1, partDataSize[2]);
    sprintf(str1, "p_partkey=%s", token);
    sprintf(query, "SELECT p_name, ps_availqty FROM part, partsupp WHERE p_partkey = ps_partkey AND %s;", str1);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      // rc = GNCDB_select(perDB, NULL, &rows, 1, 0, 1, "supplier", "s_acctbal>2000.00");
      rc = GNCDB_select(
          perDB, NULL, &rows, NULL, 2, 2, 2, "part", "partsupp", "p_name", "ps_availqty", "p_partkey=ps_partkey", str1);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    // 单条执行的微秒；
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 连接主键范围查询
/// @return
int selectPerTest_8(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  int   random  = 0;
  char *token;
  char  str1[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"两表连接主键范围查询",
      "SELECT p_name, ps_availqty FROM part, partsupp "
      "WHERE p_partkey = ps_partkey AND p_partkey??;",
      "两表主键连接后并且part主键范围查询数据（查询某些零件的名字和可用库存）",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT p_name, ps_availqty FROM part, partsupp WHERE p_partkey = ps_partkey AND %s;", str1);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(
          perDB, NULL, &rows, NULL, 2, 2, 2, "part", "partsupp", "p_name", "ps_availqty", "p_partkey=ps_partkey", str1);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    getTimeUsed(&result, time_used_singel);
    rowsSum += rows;
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    closeDb(perDB, ppDb, 2);
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 连接非主键范围查询
/// @return
int selectPerTest_9(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  int   random  = 0;
  char *token;
  char  str1[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"两表连接非主键范围查询",
      "SELECT p_name, ps_availqty FROM part, partsupp "
      "WHERE p_partkey = ps_partkey AND p_size??;",
      "两表主键连接后part表随机非主键范围查询（查询某些零件的名字和可用库存）",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "SELECT p_name, ps_availqty FROM part, partsupp WHERE p_partkey = ps_partkey AND %s;", str1);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        rc = closeDb(perDB, ppDb, 1);
        if (rc) {
          printf("closeDb error\n");
          continue;
        }
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_select(
          perDB, NULL, &rows, NULL, 2, 2, 2, "part", "partsupp", "p_name", "ps_availqty", "p_partkey=ps_partkey", str1);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        rc = closeDb(perDB, ppDb, 1);
        if (rc) {
          printf("closeDb error\n");
          continue;
        }
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    getTimeUsed(&result, time_used_singel);
    rowsSum += rows;
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 连接多条件含主键范围查询
/// @return
int selectPerTest_10(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  int   random  = 0;
  char *token;
  char  str1[128];
  char  str2[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"两表连接多条件含主键范围查询",
      "SELECT p_name, ps_availqty FROM part, partsupp "
      "WHERE p_partkey = ps_partkey AND p_partkey?? AND p_size??;",
      "两表主键连接后随机主键范围+非主键范围查询",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 6, partDataSize[2]);
    random = generateRandomNumber(0, SELECT_DATA_ININT);
    sprintf(str2, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query,
        "SELECT p_name, ps_availqty FROM part, partsupp WHERE p_partkey = ps_partkey AND %s AND %s;",
        str1,
        str2);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        rc = closeDb(perDB, ppDb, 1);
        if (rc) {
          printf("closeDb error\n");
          continue;
        }
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      // rc = GNCDB_select(perDB, NULL, &rows, 1, 0, 1, "supplier", "s_acctbal>2000.00");
      rc = GNCDB_select(perDB,
          NULL,
          &rows,
          NULL,
          2,
          2,
          3,
          "part",
          "partsupp",
          "p_name",
          "ps_availqty",
          "p_partkey=ps_partkey",
          str1,
          str2);
      clock_gettime(CLOCK_MONOTONIC, &end);

      if (rc) {
        falseNum++;
        rc = closeDb(perDB, ppDb, 1);
        if (rc) {
          printf("closeDb error\n");
          continue;
        }
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    getTimeUsed(&result, time_used_singel);
    rowsSum += rows;
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    closeDb(perDB, ppDb, 2);
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}

/**
 * @description: TPCH part-partsupp-supplier连接 + part主键等值查询
 * @param {GNCDB} *perDB
 * @param {sqlite3} *ppDb
 * @return {*}
 */
int selectPerTest_11(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  char *token;
  char  str1[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"三表连接含主键等值查询1",
      "SELECT part.p_name, supplier.s_name "
      "FROM part "
      "INNER JOIN partsupp ON part.p_partkey = partsupp.ps_partkey "
      "INNER JOIN supplier ON partsupp.ps_suppkey = supplier.s_suppkey "
      "WHERE part.p_partkey = ?;",
      "三表主键连接后主键等值查询（通过主键查找某一零件的名字和供应商名字）",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  double time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    while (token == NULL) {
      token = getRandomColumnValue("supplier", 1, supplierDataSize[2]);
    }
    sprintf(str1, "part.p_partkey=%s", token);
    sprintf(query,
        "SELECT part.p_name, supplier.s_name "
        "FROM part "
        "INNER JOIN partsupp ON part.p_partkey = partsupp.ps_partkey "
        "INNER JOIN supplier ON partsupp.ps_suppkey = supplier.s_suppkey "
        "WHERE %s;",
        str1);

    time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
    if (rc) {
      falseNum++;
      rc = closeDb(perDB, ppDb, 1);
      if (rc) {
        printf("closeDb error\n");
        continue;
      }
      continue;
    }

    getTimeUsed(&result, time_used_singel);
    rowsSum += rows;

    // sqlite执行时间
    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    closeDb(perDB, ppDb, 2);
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}

/**
 * @description: TPCH part-partsupp-supplier连接 + supplier主键等值查询
 * @param {GNCDB} *perDB
 * @param {sqlite3} *ppDb
 * @return {*}
 */
int selectPerTest_12(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  char *token;
  char  str1[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  struct PerTestRes result = {"三表连接含主键等值查询2",
      "SELECT part.p_name, supplier.s_name "
      "FROM part "
      "INNER JOIN partsupp ON part.p_partkey = partsupp.ps_partkey "
      "INNER JOIN supplier ON partsupp.ps_suppkey = supplier.s_suppkey "
      "WHERE supplier.s_suppkey = ?;",
      "三表主键连接后主键等值查询（通过主键查找某一供应商的名字和其供应的零件名字）",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  double time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    while (token == NULL) {
      token = getRandomColumnValue("supplier", 1, supplierDataSize[2]);
    }
    sprintf(str1, "supplier.s_suppkey=%s", token);
    sprintf(query,
        "SELECT part.p_name, supplier.s_name "
        "FROM part "
        "INNER JOIN partsupp ON part.p_partkey = partsupp.ps_partkey "
        "INNER JOIN supplier ON partsupp.ps_suppkey = supplier.s_suppkey "
        "WHERE %s;",
        str1);

    time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
    if (rc) {
      falseNum++;
      rc = closeDb(perDB, ppDb, 1);
      if (rc) {
        printf("closeDb error\n");
        continue;
      }
      continue;
    }

    getTimeUsed(&result, time_used_singel);
    rowsSum += rows;

    // sqlite执行时间
    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    closeDb(perDB, ppDb, 2);
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / SELECT_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}

/// @brief 查询测试
/// @return
void selectPerTest()
{
  GNCDB   *perDB = NULL;
  sqlite3 *ppDb  = NULL;
  int      x     = 0;
  x              = perEx.x;

  // perTestDBInit(&perDB);
  // perTestDataInit(perDB, SELECT_DATA_ININT, 0);
  // sqliteDBInit(&ppDb);
  // sqliteDataInit(ppDb, SELECT_DATA_ININT, 0);
  // closeDb(perDB, ppDb);

  // selectPerTest_1(perDB, ppDb);
  // selectPerTest_2(perDB, ppDb);
  // selectPerTest_3(perDB, ppDb);
  // selectPerTest_4(perDB, ppDb);
  // selectPerTest_5(perDB, ppDb);
  // selectPerTest_6(perDB, ppDb);
  selectPerTest_7(perDB, ppDb);
  selectPerTest_8(perDB, ppDb);
  selectPerTest_9(perDB, ppDb);
  selectPerTest_10(perDB, ppDb);
  selectPerTest_11(perDB, ppDb);
  selectPerTest_12(perDB, ppDb);

  // GNCDB_close(&perDB);
  // sqlite3_close(ppDb);
  writeOPType("查询测试", x);
}
/// @brief 单表全删除
/// @return
int deletePerTest_1(GNCDB *perDB, sqlite3 *ppDb)
{
  int         rc       = 0;
  int         rows     = 0;
  int         i        = 0;
  int         falseNum = 0;
  const char *query    = "DELETE FROM supplier";

  struct PerTestRes result = {"单表全删除",
      "DELETE FROM supplier",
      "全删除supplier表中数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;
  // 装入缓冲池不溢出大小的数据
  //  openDb(&perDB, &ppDb);
  //  rc = perTestDataInit(perDB, perDB->pagePoolCount, 2);
  //  if(rc) return rc;

  // sqliteDataInit(ppDb, perDB->pagePoolCount, 2);
  // closeDb(perDB, ppDb);

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // printf("cnt:%d\n", i);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        rc = closeDb(perDB, ppDb, 1);
        if (rc) {
          printf("closeDb error\n");
          continue;
        }
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_delete(perDB, &rows, "supplier", 0);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        rc = closeDb(perDB, ppDb, 1);
        if (rc) {
          printf("closeDb error\n");
          continue;
        }
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite insert error\n");

    // rc = perTestDataInit(perDB, perDB->pagePoolCount, 2);
    // sqliteDataInit(ppDb, perDB->pagePoolCount, 2);
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }

  // GNCDB_delete(perDB, &rows, "supplier", 0);
  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表主键删除
/// @return
int deletePerTest_2(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  char *token;
  char  str1[128];
  char  query[1024] = {0};

  struct PerTestRes result = {"单表主键删除",
      "DELETE FROM part WHERE p_partkey=?",
      "随机主键等值条件删除part表中指定某条数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数查询，获取查询条件
    token = getRandomColumnValue("part", 1, DELETE_DATA_ININT);
    sprintf(str1, "p_partkey=%s", token);
    sprintf(query, "DELETE FROM part WHERE %s;", str1);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_delete(perDB, &rows, "part", 1, str1);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表主键范围删除
/// @return
int deletePerTest_3(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  int   random  = 0;
  char *token;
  char  str1[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表主键范围删除",
      "DELETE FROM part WHERE p_partkey??",
      "随机主键范围删除part表中数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // printf("cnt:%d\n", i);
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, DELETE_DATA_ININT);
    random = generateRandomNumber(0, DELETE_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "DELETE FROM part WHERE %s;", str1);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_delete(perDB, &rows, "part", 1, str1);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");

    // 恢复数据
    //  rc = GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, DELETE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, DELETE_DATA_ININT, 1);
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }

  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / DELETE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表非主键范围删除
/// @return
int deletePerTest_4(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  int   rowsSum = 0;
  int   random  = 0;
  char *token;
  char  str1[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表非主键范围删除",
      "DELETE FROM part WHERE p_retailprice??",
      "随机非主键范围删除part表中的数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 8, DELETE_DATA_ININT);
    random = generateRandomNumber(0, DELETE_DATA_ININT);
    sprintf(str1, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "DELETE FROM part WHERE %s;", str1);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_delete(perDB, &rows, "part", 1, str1);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);

    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");

    // 恢复数据
    //  GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, DELETE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, DELETE_DATA_ININT, 1);
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / DELETE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表多条件含主键范围删除
/// @return
int deletePerTest_5(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str1[128];
  char  str2[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表多条件含主键范围删除",
      "DELETE FROM part WHERE p_partkey?? AND p_retailprice??",
      "随机主键范围+非主键范围删除part表中的数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, DELETE_DATA_ININT);
    random = generateRandomNumber(0, DELETE_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 8, DELETE_DATA_ININT);
    random = generateRandomNumber(0, DELETE_DATA_ININT);
    sprintf(str2, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(query, "DELETE FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_delete(perDB, &rows, "part", 2, str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);

    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");

    // 恢复数据
    //  GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, DELETE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, DELETE_DATA_ININT, 1);
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / DELETE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表多条件不含主键范围删除
/// @return
int deletePerTest_6(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str1[128];
  char  str2[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表多条件不含主键范围删除",
      "DELETE FROM part WHERE p_partkey?? AND p_retailprice??",
      "随机两个非主键范围删除part表中的数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, DELETE_DATA_ININT);
    random = generateRandomNumber(0, DELETE_DATA_ININT);
    sprintf(str1, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 8, DELETE_DATA_ININT);
    random = generateRandomNumber(0, DELETE_DATA_ININT);
    sprintf(str2, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "DELETE FROM part WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_delete(perDB, &rows, "part", 2, str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);

    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");

    // 恢复数据
    //  GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, DELETE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, DELETE_DATA_ININT, 1);
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / DELETE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 删除测试
/// @return
void deletePerTest()
{
  GNCDB   *perDB = NULL;
  sqlite3 *ppDb  = NULL;
  int      x     = perEx.x;

  // perTestDBInit(&perDB);
  // perTestDataInit(perDB, UPDATE_DATA_ININT, 1);
  // sqliteDBInit(&ppDb);
  // sqliteDataInit(ppDb, UPDATE_DATA_ININT, 1);
  // closeDb(perDB, ppDb);

  deletePerTest_1(perDB, ppDb);  // supp
  deletePerTest_2(perDB, ppDb);  // part
  deletePerTest_3(perDB, ppDb);  // part
  deletePerTest_4(perDB, ppDb);  // part
  deletePerTest_5(perDB, ppDb);  // part
  deletePerTest_6(perDB, ppDb);  // part
  // GNCDB_close(&perDB);
  // sqlite3_close(ppDb);
  writeOPType("删除测试", x);
}
/// @brief 单表全更新
/// @return
int updatePerTest_1(GNCDB *perDB, sqlite3 *ppDb)
{
  int  rc          = 0;
  int  rows        = 0;
  int  i           = 0;
  int  falseNum    = 0;
  char query[1024] = "UPDATE supplier SET s_name = 'gncdbupdate';";
  // sqlite3_stmt *stmt;

  // const char *delSql = "DELETE FROM supplier;";

  struct PerTestRes result = {"单表全更新",
      "UPDATE supplier SET s_name = 'gncdbupdate'",
      "更新全部supplier表中s_nationkey值为0",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;
  // 装入缓冲池不溢出大小的数据
  //  openDb(&perDB, &ppDb);
  //  rc = perTestDataInit(perDB, perDB->pagePoolCount, 2);
  //  if(rc) return rc;
  //  sqliteDataInit(ppDb, perDB->pagePoolCount, 2);
  //  //get_cache_status(ppDb);
  //  closeDb(perDB, ppDb);

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_update(perDB, &rows, "supplier", 1, 0, "s_name", "gncdbupdate");
      clock_gettime(CLOCK_MONOTONIC, &end);
      // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "supplier");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);

    // sqlite执行的微秒
    // get_cache_status(ppDb);

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    // get_cache_status(ppDb);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");

    // rc = GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "supplier");
  }

  // GNCDB_delete(perDB, &rows, "supplier", 0);
  // sqliteExecTime(ppDb, delSql);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表主键等值条件更新
/// @return
int updatePerTest_2(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  char *token;
  char  str1[128]   = {0};
  char  query[1024] = {0};

  struct PerTestRes result = {"单表主键等值条件更新",
      "UPDATE part SET p_name = 'gncbdupdate' WHERE partkey=?",
      "随机主键等值条件更新part表中某条数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
    sprintf(str1, "p_partkey=%s", token);
    sprintf(query, "UPDATE part SET p_name = 'gncbdupdate' WHERE %s;", str1);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_update(perDB, &rows, "part", 1, 1, "p_name", "gncbdupdate", str1);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);

    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表非主键等值条件更新
/// @return
int updatePerTest_3(GNCDB *perDB, sqlite3 *ppDb)
{
  int rc       = 0;
  int rows     = 0;
  int i        = 0;
  int falseNum = 0;

  char *token;
  char  str1[128]   = {0};
  char  query[1024] = {0};

  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表非主键等值条件更新",
      "UPDATE part SET p_retailprice = 0.0 WHERE p_retailprice=?",
      "随机非主键主键等值条件更新part表中某条数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token = getRandomColumnValue("part", 8, UPDATE_DATA_ININT);
    sprintf(str1, "p_retailprice=%s", token);
    sprintf(query, "UPDATE part SET p_name = 'gncbdupdate' WHERE %s;", str1);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_update(perDB, &rows, "part", 1, 1, "p_name", "gncbdupdate", str1);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);

    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }

    // 恢复数据
    //  rc = GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, UPDATE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, UPDATE_DATA_ININT, 1);
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表主键范围条件更新
/// @return
int updatePerTest_4(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str1[128]          = {0};
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表主键范围条件更新",
      "UPDATE part SET p_name = 'gncbdupdate' WHERE partkey??",
      "随机主键范围条件更新part表中数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
    random = generateRandomNumber(0, UPDATE_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "UPDATE part SET p_name = 'gncbdupdate' WHERE %s;", str1);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_update(perDB, &rows, "part", 1, 1, "p_name", "gncbdupdate", str1);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);
    rowsSum += rows;

    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }

    // 恢复数据
    // GNCDB_delete(perDB, NULL, "part", 0);
    // rc = perTestDataInit(perDB, UPDATE_DATA_ININT, 1);
    // if(rc) return rc;
    // sqliteExecTime(ppDb, delSql);
    // sqliteDataInit(ppDb, UPDATE_DATA_ININT, 1);
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / UPDATE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表非主键范围条件更新
/// @return
int updatePerTest_5(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str1[128]          = {0};
  char  str2[128]          = {0};
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};

  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表非主键范围条件更新",
      "UPDATE part SET p_name = 'gncbdupdate' WHERE p_retailprice??",
      "随机非主键范围条件更新part表中数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 8, UPDATE_DATA_ININT);
    random = generateRandomNumber(0, UPDATE_DATA_ININT);
    sprintf(str1, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(str2, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "UPDATE part SET p_name = 'gncbdupdate' WHERE %s;", str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_update(perDB, &rows, "part", 1, 1, "p_name", "gncbdupdate", str1);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 1, "part", str1);

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    getTimeUsed(&result, time_used_singel);
    rowsSum += rows;

    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }

    // 恢复数据
    //  GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, UPDATE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, UPDATE_DATA_ININT, 1);
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / UPDATE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表多条件含主键范围更新
/// @return
int updatePerTest_6(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str1[128];
  char  str2[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};
  // const char *delSql = "DELETE FROM part;";
  struct PerTestRes result = {"单表多条件含主键范围更新",
      "UPDATE part SET p_name = 'gncbdupdate' WHERE p_partkey?? AND p_size??",
      "随机主键范围条件+非主键范围条件更新part表中数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 1, UPDATE_DATA_ININT);
    random = generateRandomNumber(0, UPDATE_DATA_ININT);
    sprintf(str1, "p_partkey%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 6, UPDATE_DATA_ININT);
    random = generateRandomNumber(0, UPDATE_DATA_ININT);
    sprintf(str2, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "UPDATE part SET p_name = 'gncbdupdate' WHERE %s AND %s;", str1, str2);

    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }

    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_update(perDB, &rows, "part", 1, 2, "p_name", "gncbdupdate", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }

    // 恢复数据
    //  GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, UPDATE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, UPDATE_DATA_ININT, 1);
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / UPDATE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 单表多条件不含主键范围更新
/// @return
int updatePerTest_7(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc       = 0;
  int   rows     = 0;
  int   i        = 0;
  int   falseNum = 0;
  int   rowsSum  = 0;
  int   random   = 0;
  char *token;
  char  str1[128];
  char  str2[128];
  char  str3[128];
  char  query[1024]        = {0};
  int   percentage         = 0;
  char  percentageStr[128] = {0};
  // const char *delSql = "DELETE FROM part;";

  struct PerTestRes result = {"单表多条件不含主键范围更新",
      "UPDATE part SET p_name = 'gncbdupdate' WHERE p_size?? AND p_name??",
      "随机两个非主键范围条件更新part表中数据",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;
    // 生成随机数查询，获取查询条件
    token  = getRandomColumnValue("part", 6, UPDATE_DATA_ININT);
    random = generateRandomNumber(0, UPDATE_DATA_ININT);
    sprintf(str1, "p_size%s%s", random % 2 == 0 ? ">=" : "<=", token);

    token  = getRandomColumnValue("part", 8, UPDATE_DATA_ININT);
    random = generateRandomNumber(0, UPDATE_DATA_ININT);
    sprintf(str2, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);
    sprintf(str3, "p_retailprice%s%s", random % 2 == 0 ? ">=" : "<=", token);

    sprintf(query, "UPDATE part SET p_name = 'gncbdupdate' WHERE %s AND %s;", str1, str3);
    if (PERSQLTEST) {
      time_used_singel = gncdbSqlExecTime(perDB, query, &rc);
      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
    } else {
      clock_gettime(CLOCK_MONOTONIC, &start);
      rc = GNCDB_update(perDB, &rows, "part", 1, 2, "p_name", "gncbdupdate", str1, str2);
      clock_gettime(CLOCK_MONOTONIC, &end);
      // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part");

      if (rc) {
        falseNum++;
        closeDb(perDB, ppDb, 1);
        continue;
      }
      // 单条执行的微秒；
      time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    }

    rowsSum += rows;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    time_used_sqlite = sqliteExecTime(ppDb, query);
    if (time_used_sqlite > 0) {
      result.time_sqlite_avg += time_used_sqlite;
      if (result.time_sqlite_max < time_used_sqlite) {
        result.time_sqlite_max = time_used_sqlite;
      }
      if (result.time_sqlite_min > time_used_sqlite) {
        result.time_sqlite_min = time_used_sqlite;
      }
    } else
      printf("sqlite exec error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }

    // 恢复数据
    //  GNCDB_delete(perDB, NULL, "part", 0);
    //  rc = perTestDataInit(perDB, UPDATE_DATA_ININT, 1);
    //  if(rc) return rc;
    //  sqliteExecTime(ppDb, delSql);
    //  sqliteDataInit(ppDb, UPDATE_DATA_ININT, 1);
    remove("per_data.dat");
    remove("sqlite_per_data.db");
    copy_file("per_data_1.dat", "per_data.dat");
    copy_file("sqlite_per_data_1.db", "sqlite_per_data.db");
  }
  // 计算查询结果的百分比
  percentage = (int)(((double)rowsSum / (PTR_arg.TestCount - falseNum) / UPDATE_DATA_ININT) * 100);
  sprintf(percentageStr, "(%d%%)", percentage);
  strcat(result.op, percentageStr);

  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 更新测试
/// @return
void updatePerTest()
{
  GNCDB   *perDB = NULL;
  sqlite3 *ppDb  = NULL;
  int      x     = perEx.x;
  // perTestDBInit(&perDB);
  // perTestDataInit(perDB, UPDATE_DATA_ININT, 1);
  // sqliteDBInit(&ppDb);
  // sqliteDataInit(ppDb, UPDATE_DATA_ININT, 1);
  // closeDb(perDB, ppDb, 2);

  updatePerTest_1(perDB, ppDb);  // supp
  updatePerTest_2(perDB, ppDb);
  updatePerTest_3(perDB, ppDb);
  updatePerTest_4(perDB, ppDb);
  updatePerTest_5(perDB, ppDb);
  updatePerTest_6(perDB, ppDb);
  updatePerTest_7(perDB, ppDb);
  // GNCDB_close(&perDB);
  // sqlite3_close(ppDb);
  writeOPType("更新测试", x);
}

/// @brief 打开数据库，创建大对象表
/// @return
int perTestBolbDBInit(GNCDB **perDB)
{
  char  fileName[]     = "per_data.dat";
  int   rc             = 0;
  int   i              = 0;
  FILE *fileData       = NULL;
  BYTE *ImgBuffer      = NULL;
  char  path[1024]     = {0};
  char  blobPath[1024] = {0};
  FILE *fp             = NULL;
  int   fileLen        = 0;
  BYTE *buffer         = NULL;

  remove(fileName);
  remove("log_per_data.dat");

  rc = GNCDB_open(perDB, fileName, 0, 0);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 创建表 */
  rc = GNCDB_createTable(*perDB,
      "WPT",
      2,
      "key",
      FIELDTYPE_VARCHAR,
      0,
      1,
      -10000.0,
      10.0,
      "data",
      FIELDTYPE_BLOB,
      0,
      0,
      -10000.0,
      10000.0,
      10000);

  sprintf(path, "%s/testfile/datafile/WPT.txt", LOCAL_PATH);
  fileData = fopen(path, "r");

  if (fileData == NULL)
    return -1;

  for (i = 0; i < BLOB_WPT_ROWS; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);

    rc = GNCDB_insert(*perDB, NULL, "WPT", wpt.sc8_wpt_ident, 0, ImgBuffer);
    if (rc != GNCDB_SUCCESS) {
      return -1;
    }
  }
  fclose(fileData);
  // 存入大对象
  sprintf(blobPath, "%s/testfile/blob/blobdata/%s", LOCAL_PATH, BLOB_NAME);
  fp = fopen(blobPath, "rb");
  if (fp == NULL)
    return -1;

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL)
    return -1;

  fread(buffer, fileLen, 1, fp);
  fclose(fp);
  // 存入10个大对象
  for (i = 0; i < sizeof(blobKey) / sizeof(blobKey[0]); i++) {
    rc = GNCDB_setBlob(*perDB, "WPT", 1, buffer, fileLen, 1, blobKey[i]);
    if (rc) {
      printf("GNCDB insert blob failed,key = %s\n", blobKey[i]);
      return rc;
    }
  }

  return rc;
}

/// @brief 大对象插入(126k)
/// @return
int blobPerTest_1(GNCDB *perDB, sqlite3 *ppDb)
{
  int             rc         = 0;
  int             i          = 0;
  int             falseNum   = 0;
  char            path[1024] = {0};
  FILE           *fp         = NULL;
  int             fileLen    = 0;
  BYTE           *buffer     = NULL;
  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;
  char            inserSql[1024] = {0};
  char            token[128];
  // char* delSql = "DELETE FROM WPT WHERE key = 'BTZCE'";
  struct PerTestRes result = {"大对象插入",
      "UPDATE WPT SET data = ? WHERE key = ?",
      "随机主键等值条件向WPT表插入大对象",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  sprintf(path, "%s/testfile/blob/blobdata/%s", LOCAL_PATH, BLOB_NAME);

  fp = fopen(path, "rb");
  if (fp == NULL)
    return -1;

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL)
    return -1;

  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    // 生成随机数
    strcpy(token, getRandomColumnValue("WPT", 1, BLOB_WPT_ROWS));

    clock_gettime(CLOCK_MONOTONIC, &start);
    rc = GNCDB_setBlob(perDB, "WPT", 1, buffer, fileLen, 1, token);
    clock_gettime(CLOCK_MONOTONIC, &end);
    // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "supplier");

    if (rc) {
      falseNum++;
      closeDb(perDB, ppDb, 1);
      continue;
    }

    // 单条执行的微秒；
    time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    sprintf(inserSql, "UPDATE WPT SET data = ? WHERE key = \'%s\'", token);
    time_used_sqlite = sqliteBlobExecTime(ppDb, inserSql, path);
    if (time_used_sqlite > 0)
      result.time_sqlite_avg += time_used_sqlite;
    else
      printf("sqlite exec error\n");
    // 删除
    rc = GNCDB_deleteBlob(perDB, "WPT", 1, 1, token);
    if (rc) {
      printf("%s:删除大对象失败", result.op);
      return rc;
    }
    // sqliteBlobExecTime(ppDb, delSql, path);
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }

  strcat(result.op, getFileSizeString(path));
  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 大对象查询
/// @return
int blobPerTest_2(GNCDB *perDB, sqlite3 *ppDb)
{
  int               rc          = 0;
  int               i           = 0;
  int               falseNum    = 0;
  BYTE             *bufferCopy  = NULL;
  int               BlobNameLen = 0;
  char              path[1024]  = {0};
  FILE             *fp          = NULL;
  int               fileLen     = 0;
  char              query[1024] = {0};
  struct PerTestRes result      = {"大对象查询",
           "SELECT data FROM WPT WHERE key = ?",
           "随机主键等值条件向WPT表查询大对象",
           PTR_arg.TestCount,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0.0,
           DBL_MIN,
           DBL_MAX,
           0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  BlobNameLen = sizeof(blobKey) / sizeof(blobKey[0]);

  sprintf(path, "%s/testfile/blob/blobdata/%s", LOCAL_PATH, BLOB_NAME);

  fp = fopen(path, "rb");
  if (fp == NULL)
    return -1;

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL)
    return -1;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    time_per_read  = 0.0;
    time_per_write = 0.0;

    clock_gettime(CLOCK_MONOTONIC, &start);
    rc = GNCDB_getBlob(perDB, "WPT", 1, bufferCopy, fileLen, 1, blobKey[i % BlobNameLen]);
    clock_gettime(CLOCK_MONOTONIC, &end);

    if (rc || bufferCopy == NULL) {
      falseNum++;
      closeDb(perDB, ppDb, 1);
      continue;
    }
    memset(bufferCopy, 0, fileLen);
    // 单条执行的微秒；
    time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    getTimeUsed(&result, time_used_singel);
    memset(bufferCopy, 0, fileLen);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    sprintf(query, "SELECT * FROM WPT WHERE key = '%s';", blobKey[i % BlobNameLen]);
    time_used_sqlite = sqliteBlobExecTime(ppDb, query, NULL);
    if (time_used_sqlite > 0)
      result.time_sqlite_avg += time_used_sqlite;
    else
      printf("sqlite exec error\n");
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }

  strcat(result.op, getFileSizeString(path));
  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 大对象删除
/// @return
int blobPerTest_3(GNCDB *perDB, sqlite3 *ppDb)
{
  int   rc          = 0;
  int   i           = 0;
  int   falseNum    = 0;
  BYTE *bufferCopy  = NULL;
  int   BlobNameLen = 0;
  char  path[1024]  = {0};
  FILE *fp          = NULL;
  int   fileLen     = 0;
  char  query[1024] = {0};
  // char insertSql[1024] = {0};
  struct PerTestRes result = {"大对象删除",
      "DELETE FROM WPT WHERE key = ?",
      "随机主键等值条件向WPT表删除大对象",
      PTR_arg.TestCount,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0.0,
      DBL_MIN,
      DBL_MAX,
      0};

  struct timespec start, end;
  double          time_used_singel, time_used_sqlite = 0;

  BlobNameLen = sizeof(blobKey) / sizeof(blobKey[0]);

  sprintf(path, "%s/testfile/blob/blobdata/%s", LOCAL_PATH, BLOB_NAME);
  fp = fopen(path, "rb");
  if (fp == NULL)
    return -1;

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL)
    return -1;

  for (i = 0; i < PTR_arg.TestCount; i++) {
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    // 先获取，删完再插入
    rc = GNCDB_getBlob(perDB, "WPT", 1, bufferCopy, fileLen, 1, blobKey[i % BlobNameLen]);
    if (rc) {
      printf("%s:获取大对象失败", result.op);
      return rc;
    }
    time_per_read  = 0.0;
    time_per_write = 0.0;

    clock_gettime(CLOCK_MONOTONIC, &start);
    rc = GNCDB_deleteBlob(perDB, "WPT", 1, 1, blobKey[i % BlobNameLen]);
    clock_gettime(CLOCK_MONOTONIC, &end);

    if (rc) {
      falseNum++;
      closeDb(perDB, ppDb, 1);
      continue;
    }

    // 单条执行的微秒；
    time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
    getTimeUsed(&result, time_used_singel);
    // sqlite执行时间

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    sprintf(query, "DELETE FROM WPT WHERE key = '%s';", blobKey[i % BlobNameLen]);
    time_used_sqlite = sqliteBlobExecTime(ppDb, query, NULL);
    if (time_used_sqlite > 0)
      result.time_sqlite_avg += time_used_sqlite;
    else
      printf("sqlite exec error\n");

    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 1);
    if (rc != 0) {
      printf("openDb error\n");
      continue;
    }

    // 恢复
    rc = GNCDB_setBlob(perDB, "WPT", 1, bufferCopy, fileLen, 1, blobKey[i % BlobNameLen]);
    if (rc) {
      printf("%s:重新插入大对象失败", result.op);
      return rc;
    }
    memset(bufferCopy, 0, fileLen);
    // sqlite重新插入

    rc = closeDb(perDB, ppDb, 1);
    if (rc) {
      printf("closeDb error\n");
      continue;
    }
    rc = openDb(&perDB, &ppDb, 2);
    if (rc) {
      printf("openDb error\n");
      continue;
    }

    rc = insertBlobData(ppDb, blobKey[i % BlobNameLen], path);
    if (rc) {
      printf("insertBlobData error\n");
      continue;
    }
    rc = closeDb(perDB, ppDb, 2);
    if (rc) {
      printf("close db error\n");
      continue;
    }
  }
  strcat(result.op, getFileSizeString(path));
  result.falseNum = falseNum;
  writeToCSV(result);
  return 1;
}
/// @brief 大对象
/// @return
void blobPerTest()
{
  GNCDB   *perDB;
  sqlite3 *ppDb;
  int      x = perEx.x;
  perTestBolbDBInit(&perDB);
  sqliteBolbDBInit(&ppDb);
  closeDb(perDB, ppDb, 2);
  blobPerTest_1(perDB, ppDb);
  blobPerTest_2(perDB, ppDb);
  blobPerTest_3(perDB, ppDb);
  writeOPType("大对象测试", x);
}
/// @brief 初始化数据库并进行增删改查操作
/// @return
int perTestDBRevocerInit(GNCDB **perDB)
{
  int rc   = 0;
  int rows = 0;

  // 初始化数据库并且存入数据
  rc = perTestDBInit(perDB);
  if (rc)
    return rc;
  rc = perTestDataInit(*perDB, 5000, 0);
  if (rc)
    return rc;

  // 删除supplier表中的数据
  rc = GNCDB_delete(*perDB, &rows, "supplier", 0);
  if (rc)
    return rc;

  // 更新part表中的数据
  rc = GNCDB_update(*perDB, &rows, "part", 1, 0, "p_retailprice", 0.0);
  if (rc)
    return rc;

  // 查询part表
  rc = GNCDB_select(*perDB, NULL, &rows, NULL, 1, 0, 0, "part", "p_retailprice");

  return rc;
}
/// @brief 执行该函数之前需要先执行perTestDBRevocerInit，手动结束后直接执行该函数
/// @return
int recoverPerTest_1(GNCDB **perDB)
{
  char              log_fileName[] = "log_per_data.dat";
  char              fileName[]     = "per_data.dat";
  int               rc             = 0;
  struct PerTestRes result         = {"故障恢复",
              "",
              "关闭检查点故障恢复操作",
              1,
              0.0,
              DBL_MIN,
              DBL_MAX,
              0.0,
              DBL_MIN,
              DBL_MAX,
              0.0,
              DBL_MIN,
              DBL_MAX,
              0.0,
              DBL_MIN,
              DBL_MAX,
              0};

  char            str[2096];
  struct timespec start, end;
  double          time_used_singel = 0;

  // resFp = fopen("./testfile/result/pertest.csv", "a+");
  // if (resFp== NULL)
  // {
  // 	perror("Error\n");
  // 	printf("test.csv 文件打开失败\n");
  // 	return -1;
  // }

  // 检测log文件是否存在
  if (!isFileExist(log_fileName)) {
    printf("故障恢复:log文件不存在\n");
    return -1;
  }
  time_per_read  = 0.0;
  time_per_write = 0.0;
  clock_gettime(CLOCK_MONOTONIC, &start);
  rc = GNCDB_open(perDB, fileName, 0, 0);
  clock_gettime(CLOCK_MONOTONIC, &end);

  if (rc) {
    printf("故障恢复:数据库打开失败\n");
    return rc;
  }

  printf("故障恢复:数据库打开成功\n");
  // 单条执行的微秒；
  time_used_singel = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  sprintf(str,
      ",%s,%s,,%d,%s,%.0lf,%.0lf,%.0lf,,\n",
      result.op,
      result.opDetail,
      result.exeCount,
      "AVG",
      time_per_read,
      time_per_write,
      time_used_singel);
  // fwrite(str, strlen(str), 1, resFp);
  // fflush(resFp);

  return 0;
}
/// @brief 故障恢复
/// @return
void recoverPerTest()
{
  GNCDB *perDB;
  int    rc;
  rc = perTestDBRevocerInit(&perDB);
  if (rc) {
    printf("故障恢复:数据库初始化失败\n");
  }
  printf("故障恢复:数据库初始化成功\n");

  perDB = NULL;
  recoverPerTest_1(&perDB);
  // GNCDB_select(perDB, tpchCallBack, NULL, 1, 0, 0, "part", "p_retailprice");
}
/// @brief 随机获取指定表中某行的数据
/// @param tableName 指定表名
/// @param maxLine 最大行数
/// @return
char *getRandomValue(const char *tableName, int maxLine)
{
  FILE *file;
  char  partPath[1024];
  char  line[1024];
  char *value     = NULL;
  int   lineCount = 0;
  int   random    = 0;

  sprintf(partPath, "%s%s.tbl", pertpchpath, tableName);
  if (!strcmp(tableName, "WPT")) {
    sprintf(partPath, "%s%s.txt", pertpchpath, tableName);
  }
  file = fopen(partPath, "r");
  if (file == NULL) {
    printf("无法打开文件: %s\n", tableName);
    return NULL;
  }
  random = generateRandomNumber(0, maxLine);
  // 逐行读取文件
  while (fgets(line, sizeof(line), file) != NULL) {
    lineCount++;
    if (lineCount == random) {
      value = my_strdup(line);
      break;
    }
  }

  fclose(file);

  return value;
}
/// @brief 随机获取指定表中某行第m列数据
/// @param tableName 指定表名
/// @param column 指定列
/// @param maxLine 最大行数
/// @return
char *getRandomColumnValue(const char *tableName, int column, int maxLine)
{
  FILE *file;
  char  partPath[1024];
  char  line[1024];
  char *value     = NULL;
  int   lineCount = 0;
  int   random    = 0;
  char  flit[6]   = "|";

  sprintf(partPath, "%s%s.tbl", pertpchpath, tableName);
  if (!strcmp(tableName, "WPT")) {
    sprintf(partPath, "%s%s.txt", pertpchpath, tableName);
    sprintf(flit, ",");
  }
  file = fopen(partPath, "r");
  if (file == NULL) {
    printf("无法打开文件: %s\n", tableName);
    return NULL;
  }
  random = generateRandomNumber(0, maxLine);
  // 逐行读取文件
  while (fgets(line, sizeof(line), file) != NULL) {
    lineCount++;
    if (lineCount == random) {
      // 解析行数据
      char *token       = strtok(line, flit);
      int   columnCount = 1;
      while (token != NULL) {
        if (columnCount == column) {
          // 找到目标列数据
          value = my_strdup(token);
          break;
        }
        token = strtok(NULL, flit);
        columnCount++;
      }
      break;
    }
  }

  fclose(file);

  return value;
}
/// @brief sqlite打开数据库，创建大对象表
/// @return
int sqliteBolbDBInit(sqlite3 **ppDb)
{
  char          fileName[] = "sqlite_per_data.db";
  char         *errMsg;
  int           rc;
  int           i              = 0;
  int           count          = 0;
  const char   *createTableSql = "CREATE TABLE WPT (key TEXT PRIMARY KEY, data BLOB)";
  char          filePath[256]  = {0};
  FILE         *file;
  char          line[256];
  char          insertSql[512 + 50];  // Extra space for insert statement
  char          key[128];
  sqlite3_stmt *stmt;
  char          blobPath[1024] = {0};
  char          updateSql[1000];

  remove(fileName);

  rc = sqlite3_open(fileName, ppDb);
  if (rc != SQLITE_OK) {
    fprintf(stderr, "Cannot open database: %s\n", sqlite3_errmsg(*ppDb));
    return 1;
  }
  /* 创建表 */
  rc = sqlite3_exec(*ppDb, createTableSql, 0, 0, &errMsg);
  if (rc != SQLITE_OK) {
    fprintf(stderr, "SQL error: %s\n", errMsg);
    sqlite3_free(errMsg);
    return 1;
  }
  // 插入主键数据
  sprintf(filePath, "%s/testfile/datafile/WPT.txt", LOCAL_PATH);
  file = fopen(filePath, "r");

  if (file == NULL)
    return -1;

  while (fgets(line, sizeof(line), file) && count++ < BLOB_WPT_ROWS) {
    sscanf(line, "%s", key);

    sprintf(insertSql, "INSERT INTO WPT (key, data) VALUES ('%s', ?)", key);

    rc = sqlite3_prepare_v2(*ppDb, insertSql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
      fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(*ppDb));
      fclose(file);
      sqlite3_close(*ppDb);
      return 1;
    }

    rc = sqlite3_bind_blob(stmt, 1, NULL, 0, SQLITE_STATIC);
    if (rc != SQLITE_OK) {
      fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(*ppDb));
      fclose(file);
      sqlite3_close(*ppDb);
      return 1;
    }

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
      fprintf(stderr, "Execution failed: %s\n", sqlite3_errmsg(*ppDb));
      fclose(file);
      sqlite3_close(*ppDb);
      return 1;
    }

    sqlite3_finalize(stmt);
  }
  fclose(file);

  // 存入大对象
  sprintf(blobPath, "%s/testfile/blob/blobdata/%s", LOCAL_PATH, BLOB_NAME);
  // 存入10个大对象
  for (i = 0; i < sizeof(blobKey) / sizeof(blobKey[0]); i++) {
    sprintf(updateSql, "UPDATE WPT SET data = ? WHERE key = '%s'", blobKey[i]);
    if (insertBlobData(*ppDb, blobKey[i], blobPath)) {
      printf("sqlite blob insert blob failed\n");
    }
  }

  return 0;
}
/// @brief 根据key值插入大对象，若无key返回错误
/// @param db
/// @param key
/// @param filePath
/// @return
int sqliteInsertBlobData(sqlite3 *db, const char *sql, const char *filePath)
{
  FILE          *file;
  long           fileSize;
  unsigned char *data;
  sqlite3_stmt  *stmt;
  int            rc;

  file = fopen(filePath, "rb");
  if (!file) {
    fprintf(stderr, "Failed to open file: %s\n", filePath);
    return 1;
  }

  fseek(file, 0, SEEK_END);
  fileSize = ftell(file);
  rewind(file);

  data = (unsigned char *)my_malloc0(fileSize);
  if (!data) {
    fprintf(stderr, "Memory allocation failed\n");
    fclose(file);
    return 1;
  }

  if (fread(data, fileSize, 1, file) != 1) {
    fprintf(stderr, "Failed to read data from file: %s\n", filePath);
    fclose(file);
    my_free(data);
    return 1;
  }

  fclose(file);

  rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
  if (rc != SQLITE_OK) {
    fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(db));
    my_free(data);
    return 1;
  }

  rc = sqlite3_bind_blob(stmt, 1, data, fileSize, SQLITE_STATIC);
  if (rc != SQLITE_OK) {
    fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(db));
    sqlite3_finalize(stmt);
    my_free(data);
    return 1;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_DONE) {
    fprintf(stderr, "Execution failed: %s\n", sqlite3_errmsg(db));
    sqlite3_finalize(stmt);
    my_free(data);
    return 1;
  }

  sqlite3_finalize(stmt);
  my_free(data);

  return 0;
}
/// @brief 统一sqlite大对象处理
/// @param db
/// @param sql
/// @param filePath
/// @return
int handleBlobData(sqlite3 *db, const char *sql, const char *filePath)
{
  if (strstr(sql, "UPDATE") != NULL) {
    return sqliteInsertBlobData(db, sql, filePath);
  } else if (strstr(sql, "SELECT") != NULL) {
    sqlite3_stmt *stmt;
    int           rc;

    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
      fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(db));
      return 1;
    }

    rc = sqlite3_step(stmt);
    if (rc == SQLITE_ROW) {
      // 从结果集中获取大对象数据并处理...
      // sqlite3_column_blob 和 sqlite3_column_bytes 函数可以用于提取 BLOB 数据
      // 例如:
      // const void *blobData = sqlite3_column_blob(stmt, 0);
      // int blobSize = sqlite3_column_bytes(stmt, 0);
      // printf("Found blob data for the query: %s\n", sql);
    } else if (rc == SQLITE_DONE) {
      printf("No blob data found for the query: %s\n", sql);
    } else {
      fprintf(stderr, "Execution failed: %s\n", sqlite3_errmsg(db));
      sqlite3_finalize(stmt);
      return 1;
    }

    sqlite3_finalize(stmt);
  } else if (strstr(sql, "DELETE") != NULL) {
    sqlite3_stmt *stmt;
    int           rc;

    rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
    if (rc != SQLITE_OK) {
      fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(db));
      return 1;
    }

    rc = sqlite3_step(stmt);
    if (rc != SQLITE_DONE) {
      fprintf(stderr, "Execution failed: %s\n", sqlite3_errmsg(db));
      sqlite3_finalize(stmt);
      return 1;
    }

    sqlite3_finalize(stmt);
  } else {
    fprintf(stderr, "Unsupported SQL operation\n");
    return 1;
  }

  return 0;
}
/// @brief 执行大对象该条语句所花时间
/// @param pDb
/// @param sql
/// @return
double sqliteBlobExecTime(sqlite3 *db, const char *sql, const char *filePath)
{
  int             rc        = 0;
  double          time_used = 0;
  struct timespec start, end;
  // printf("%s\n", sql);

  clock_gettime(CLOCK_MONOTONIC, &start);
  rc = handleBlobData(db, sql, filePath);
  clock_gettime(CLOCK_MONOTONIC, &end);
  if (rc) {
    printf("sqlite3_exec error:%s\n", sqlite3_errmsg(db));
    return -1;
  }
  time_used = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  return time_used;
}
/// @brief sqlite插入大对象数据
/// @param db
/// @param key
/// @param filePath
/// @return
int insertBlobData(sqlite3 *db, const char *key, const char *filePath)
{
  FILE          *file;
  long           fileSize;
  unsigned char *data;
  sqlite3_stmt  *stmt;
  const char    *sql = "INSERT INTO WPT (key, data) VALUES (?, ?)";
  int            rc;

  file = fopen(filePath, "rb");
  if (!file) {
    fprintf(stderr, "Failed to open file: %s\n", filePath);
    return 1;
  }

  fseek(file, 0, SEEK_END);
  fileSize = ftell(file);
  rewind(file);

  data = (unsigned char *)my_malloc0(fileSize);
  if (!data) {
    fprintf(stderr, "Memory allocation failed\n");
    fclose(file);
    return 1;
  }

  if (fread(data, fileSize, 1, file) != 1) {
    fprintf(stderr, "Failed to read data from file: %s\n", filePath);
    fclose(file);
    my_free(data);
    return 1;
  }

  fclose(file);

  rc = sqlite3_prepare_v2(db, sql, -1, &stmt, NULL);
  if (rc != SQLITE_OK) {
    fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(db));
    my_free(data);
    return 1;
  }

  rc = sqlite3_bind_text(stmt, 1, key, -1, SQLITE_STATIC);
  if (rc != SQLITE_OK) {
    fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(db));
    sqlite3_finalize(stmt);
    my_free(data);
    return 1;
  }

  rc = sqlite3_bind_blob(stmt, 2, data, fileSize, SQLITE_STATIC);
  if (rc != SQLITE_OK) {
    fprintf(stderr, "SQL error: %s\n", sqlite3_errmsg(db));
    sqlite3_finalize(stmt);
    my_free(data);
    return 1;
  }

  rc = sqlite3_step(stmt);
  if (rc != SQLITE_DONE) {
    fprintf(stderr, "Execution failed: %s\n", sqlite3_errmsg(db));
    sqlite3_finalize(stmt);
    my_free(data);
    return 1;
  }

  sqlite3_finalize(stmt);
  my_free(data);

  return 0;
}
/// @brief 输出文件大小
/// @param filePath
/// @return
char *getFileSizeString(const char *filePath)
{
  FILE     *file;
  long long fileSize;
  char     *sizeString;

  file = fopen(filePath, "rb");
  if (!file) {
    fprintf(stderr, "Failed to open file: %s\n", filePath);
    return NULL;
  }

  fseek(file, 0, SEEK_END);
  fileSize = ftell(file);
  fclose(file);

  sizeString = (char *)my_malloc0(40);
  if (!sizeString) {
    fprintf(stderr, "Memory allocation failed\n");
    return NULL;
  }

  if (fileSize >= 1024 * 1024 * 1024) {
    sprintf(sizeString, "%.2f GB", (double)fileSize / (1024 * 1024 * 1024));
  } else if (fileSize >= 1024 * 1024) {
    sprintf(sizeString, "%.2f MB", (double)fileSize / (1024 * 1024));
  } else if (fileSize >= 1024) {
    sprintf(sizeString, "%.2f KB", (double)fileSize / 1024);
  } else {
    sprintf(sizeString, "%lld bytes", fileSize);
  }

  return sizeString;
}
/// @brief 查询sqlite页数
/// @param db
void get_cache_status(sqlite3 *conn)
{
  sqlite3_stmt *stmt;
  int           cache_used, cache_spill, page_count;

  // 获取缓冲池的使用情况和脏页数
  sqlite3_prepare_v2(conn, "PRAGMA cache_size;", -1, &stmt, NULL);
  if (sqlite3_step(stmt) == SQLITE_ROW) {
    cache_used = sqlite3_column_int(stmt, 0);
  }
  sqlite3_finalize(stmt);

  sqlite3_prepare_v2(conn, "PRAGMA cache_spill;", -1, &stmt, NULL);
  if (sqlite3_step(stmt) == SQLITE_ROW) {
    cache_spill = sqlite3_column_int(stmt, 0);
  }
  sqlite3_finalize(stmt);

  // 获取数据库总页数
  sqlite3_prepare_v2(conn, "PRAGMA page_count;", -1, &stmt, NULL);
  if (sqlite3_step(stmt) == SQLITE_ROW) {
    page_count = sqlite3_column_int(stmt, 0);
  }
  sqlite3_finalize(stmt);

  // 打印结果
  printf("Cache Used: %d\n", cache_used);
  printf("Dirty Pages: %d\n", cache_spill);
  printf("Total Pages: %d\n", page_count);
}

double extract_memtotal_gb(const char *line)
{
  const char *key     = "MemTotal:";
  const char *pos     = strstr(line, key);
  long        mem_kb  = 0;
  char        unit[4] = {0};
  if (!pos)
    return -1.0;

  // 跳过 "MemTotal:" 和空格
  pos += strlen(key);
  while (*pos == ' ' || *pos == '\t')
    pos++;

  // 提取数值和单位

  if (sscanf(pos, "%ld %3s", &mem_kb, unit) != 2) {
    return -1.0;  // 解析失败
  }

  // 验证单位是 kB（大小写敏感）
  if (strcmp(unit, "kB") != 0) {
    return -1.0;  // 单位错误
  }

  // 转换为 GB
  return mem_kb / (1024.0 * 1024.0);
}

int getSystemInfo()
{

#if defined _WIN32

#else
  char           line[256];
  char           MHz[64];
  int            flag = 0;
  int            len  = 0;
  lxw_format    *title_format;
  const char    *version = NULL;
  double         mem_total_gb;
  struct utsname sysinfo;
  char           system_info[12];
  time_t         now = time(NULL);
  struct tm     *t   = localtime(&now);
  char           time_str[20];
  FILE          *file = NULL;

  FILE *fp = fopen("/proc/cpuinfo", "r");
  if (fp == NULL) {
    printf("Failed to open /proc/cpuinfo\n");
    return 1;
  }
  file = fopen("build/CMakeCache.txt", "r");

  title_format = workbook_add_format(workbook);
  format_set_border(title_format, LXW_BORDER_THIN);

  format_set_bold(title_format);
  format_set_font_size(title_format, 20);
  format_set_align(title_format, LXW_ALIGN_CENTER);
  format_set_align(title_format, LXW_ALIGN_VERTICAL_CENTER);
  worksheet_set_column(main_worksheet, 0, 1, 30, NULL);
  worksheet_set_column(main_worksheet, 2, 3, 40, NULL);

  worksheet_merge_range(main_worksheet, len, 0, len, 3, "GNCDB 性能测试对比报告", title_format);
  len++;
  // format_set_font_size(title_format, 20);
  worksheet_merge_range(main_worksheet, len, 0, len, 1, "对比目标:", title_format);
  worksheet_merge_range(main_worksheet, len, 2, len, 3, "SQLite", title_format);
  len++;
  worksheet_merge_range(main_worksheet, len, 0, len, 1, "SQLite版本:", title_format);
  version = sqlite3_libversion();
  worksheet_merge_range(main_worksheet, len, 2, len, 3, version, title_format);
  len++;

  // 打开当前目录下的build的CMakeCache.txt文件
  if (file == NULL) {
    worksheet_merge_range(main_worksheet, len, 0, len, 1, "编译选项:", title_format);
    worksheet_merge_range(main_worksheet, len, 2, len, 3, "无优化编译", title_format);
  } else {
    int x = len;
    while (fgets(line, sizeof(line), file)) {
      if (strncmp(line, "CMAKE_BUILD_TYPE", 16) == 0) {
        // fwrite(line, strlen(line), 1, file);
        char *p = strchr(line, '=');
        worksheet_write_string(main_worksheet, len, 2, "CAMKE_BUILD_TYPE", title_format);
        worksheet_write_string(main_worksheet, len, 3, p + 1, title_format);
        len++;
        printf("%s", line);
      } else if (strncmp(line, "CMAKE_C_COMPILER:", 17) == 0) {
        // fwrite(line, strlen(line), 1, file);
        char *p = strchr(line, '=');
        worksheet_write_string(main_worksheet, len, 2, "CMAKE_C_COMPILER", title_format);
        worksheet_write_string(main_worksheet, len, 3, p + 1, title_format);
        len++;
        printf("%s", line);
      } else if (strncmp(line, "ENABLE_O2", 9) == 0) {
        // fwrite(line, strlen(line), 1, file);
        char *p = strchr(line, '=');
        worksheet_write_string(main_worksheet, len, 2, "ENABLE_O2", title_format);
        worksheet_write_string(main_worksheet, len, 3, p + 1, title_format);
        len++;
        printf("%s", line);
      }
    }
    fclose(file);
    worksheet_merge_range(main_worksheet, x, 0, len - 1, 1, "编译选项:", title_format);
  }

  worksheet_merge_range(main_worksheet, len, 0, len, 1, "系统位数:", title_format);
  if (sizeof(void *) == 8) {
    sprintf(system_info, "64 位");
  } else if (sizeof(void *) == 4) {
    sprintf(system_info, "32 位");
  } else {
    sprintf(system_info, "无法识别");
  }
  worksheet_merge_range(main_worksheet, len, 2, len, 3, system_info, title_format);
  len++;

  while (fgets(line, sizeof(line), fp)) {
    if (strncmp(line, "model name: ", 10) == 0) {
      // fwrite(line, strlen(line), 1, file);
      worksheet_merge_range(main_worksheet, len, 0, len, 1, "CPU型号:", title_format);
      worksheet_merge_range(main_worksheet, len, 2, len, 3, line + strlen("model name: "), title_format);
      len++;
      printf("%s", line);
      flag++;
    }
    if (strncmp(line, "cpu MHz", 7) == 0) {
      char  *p      = strchr(line, ':');
      double cpuMHz = 0.0;
      if (p) {
        // 跳过冒号和可能的空格
        while (*++p == ' ')
          ;  // 指向数字开始的位置
      }
      cpuMHz = atof(p);
      // fwrite(line, strlen(line), 1, file);
      worksheet_merge_range(main_worksheet, len, 0, len, 1, "主存频率:", title_format);
      sprintf(MHz, "%.2f MHz", cpuMHz);
      worksheet_merge_range(main_worksheet, len, 2, len, 3, MHz, title_format);
      len++;
      printf("%s", line);
      flag++;
    }
    if (flag >= 2) {
      break;
    }
  }

  fclose(fp);
  fp = fopen("/proc/meminfo", "r");
  if (!fp) {
    perror("Failed to open /proc/meminfo");
    return 1;
  }

  fgets(line, sizeof(line), fp);
  // fwrite(line, strlen(line), 1, file);
  remove_extra_spaces(line);
  mem_total_gb = extract_memtotal_gb(line);
  worksheet_merge_range(main_worksheet, len, 0, len, 1, "内存:", title_format);
  sprintf(MHz, "%.2f GB", mem_total_gb);
  worksheet_merge_range(main_worksheet, len, 2, len, 3, MHz, title_format);
  len++;
  printf("%s", line);
  fclose(fp);

  if (uname(&sysinfo) == 0) {
    printf("System Name : %s\n", sysinfo.sysname);
    printf("Node Name   : %s\n", sysinfo.nodename);
    printf("Release     : %s\n", sysinfo.release);
    printf("Version     : %s\n", sysinfo.version);
    printf("Machine     : %s\n", sysinfo.machine);

    worksheet_merge_range(main_worksheet, len, 0, len, 1, "系统名称:", title_format);
    worksheet_merge_range(main_worksheet, len, 2, len, 3, sysinfo.sysname, title_format);
    len++;

  } else {
    perror("uname");
  }

  fp = fopen("/etc/os-release", "r");
  if (!fp) {
    perror("Failed to open /etc/os-release");
    return 1;
  }
  while (fgets(line, sizeof(line), fp)) {
    if (strncmp(line, "VERSION=", 8) == 0) {
      // fwrite(line, strlen(line), 1, file);
      worksheet_merge_range(main_worksheet, len, 0, len, 1, "系统版本:", title_format);
      worksheet_merge_range(main_worksheet, len, 2, len, 3, line + strlen("VERSION="), title_format);
      len++;
      printf("%s", line);
    }
  }

  // format_set_font_size(title_format, 20);
  worksheet_merge_range(main_worksheet, len, 0, len, 1, "测试时间:", title_format);

  // 获取系统时间并转化为年月日时分秒格式
  now = time(NULL);
  t   = localtime(&now);
  strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", t);

  worksheet_merge_range(main_worksheet, len, 2, len, 3, time_str, title_format);
  len++;

  for (int i = 0; i < len; i++) {
    worksheet_set_row(main_worksheet, i, 40, NULL);
  }
  // fclose(fp);
  return 0;

#endif
}