#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "gncdbconstant.h"
#include "pagepool.h"
#include "gncdbconstant.h"
#include "pagepool.h"
#include "singletest.h"
#include "gncdb.h"
#include "callback.h"
#include "gncdb.h"
#include "callback.h"

int      lockCount   = 0;
int      unlockCount = 0;
int      maxLockNum  = 0;
int      currentLock = 0;
WPT_ROW  wpt;
ARPT_ROW arpt;

bool        tableExist = false;
CREATETABLE test_table_count;

int sql_SpecialTest(TESTSUM *testSum);

int hash_index_test(TESTSUM *testSum);

#if defined(_WIN32)

char testFilePath[60]   = ".\\testfile\\datafile\\";
char testBlobPath[60]   = ".\\testfile\\blob\\blobdata\\";
char resultBlobPath[60] = ".\\testfile\\blob\\blobresult\\single\\";

#else
char testFilePath[60]   = "./testfile/datafile/";
char testBlobPath[60]   = "./testfile/blob/blobdata/";
char resultBlobPath[60] = "./testfile/blob/blobresult/single/";
#endif

/* 文件名 */
char testFileName2[30] = "ARPT.txt";
char testFileName1[30] = "WPT.txt";

int dbtest()
{
  // char* start = "数据库测试开始\n";
  TESTSUM *allTest  = NULL;
  GNCDB   *db       = NULL;
  void    *other_db = NULL;
  int      tableNum = 0, pageNum = 0;
  int      i      = 0;
  int      arr[5] = {100, 300, 500, 600, 700};


  for (i = 0; i < 1; i++) {
    printf("缓冲池大小为%d\n", arr[i]);

    allTest = initTest();
    if (allTest == NULL) {
      return -1;
    }
    outputTestAttr(allTest);

    sql_RecoveryFun(allTest);

    hash_index_test(allTest);
#ifdef SQL_TESTMODE
    sql_SpecialTest(allTest);
#endif
    testCreatedb(&db, &other_db, allTest);
    testCreateTable(db, allTest);
    testInsertTable(db, allTest);
    testBlobTable(db, allTest);
    // testSelectTable(db, allTest);
    testUpdataTable(db, allTest);
    testDeleteTable(db, allTest);
    testOpendb(&db, allTest);
    testOpendbInsert(db, allTest);
    testOpendbBlob(db, allTest);
    // testOpendbSelect(db, allTest);
    testOpendbUpdate(db, allTest);
    testOpendbDelete(db, allTest);
    // GNCDB_select(db, NULL, &tableNum, NULL, 1, 0, 0, "master");
    pageNum = db->totalPageNum;

    // initFlag();
    // GNCDB_exec(db, "select * from master;", testCallBack, NULL, NULL);
    // initFlag();
    // GNCDB_exec(db, "select * from schema;", testCallBack, NULL, NULL);

    testClosedb(db, allTest);

    displayTest(allTest);
    printf("tableNum = %d  pageNum = %d \n", tableNum, pageNum);
  }
  return 0;
}

/*
test2_1and2_2(db, allTest);
    test3_1(db, allTest);

    for (int row = 0; row < WPTROWS; ++row)
    {
        char str[12] = { 0 };
        sprintf(str, "rowId=%d", row);
        GNCDB_delete(db, NULL, "WPT", 1, str);
    }

    char* path = strJoin(testFilePath, testFileName1);
    if (path == NULL)
    {
        return -1;
    }

    FILE* fileData = fopen(path, "r");
    if (fileData == NULL)
    {
        return -1;
    }
    int i = 0;
    int rc = 0;

    for (i = 0; i < WPTROWS; ++i)
    {
        int fscanf(fileData, "%[^,],%lf,%lf,\n",
            wpt.sc8_wpt_ident,
            &wpt.f64_lon,
            &wpt.f64_lat);

        BYTE* ImgBuffer = NULL;

        printf("h:");
        for (int i = 0; i < test_table_count.db->pagePool->historyArray->elementCount; ++i)
        {
            int* id = varArrayListGet(test_table_count.db->pagePool->historyArray, i);
            printf("%d  ", *id);
        }
        printf("\nc:");
        for (int i = 0; i < test_table_count.db->pagePool->cacheArray->elementCount; ++i)
        {
            int* id = varArrayListGet(test_table_count.db->pagePool->cacheArray, i);
            printf("%d  ", *id);
        }
        printf("\n");

        rc = GNCDB_insert(db, NULL, "WPT",
            wpt.sc8_wpt_ident,
            wpt.f64_lon,
            wpt.f64_lat,
            0, ImgBuffer);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }
    fclose(fileData);


    GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "WPT");
    GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "WPT");



*/

int test1_1(GNCDB **db, TESTSUM *testSum)
{
  char  createTest[] = "1.1:传入GNCDB类型指针创建数据库";
  char *filename     = "dbtest.dat";
  int   rc;

  test_table_count.SQL = "CREATE DATABASE dbtest.dat";
  currentTest(testSum, createTest);
  remove(filename);
#if defined _WIN32
  remove("..\\log_dbtest.dat");
#else
  remove("log_dbtest.dat");
#endif
  rc = GNCDB_open(db, filename, 0, 0);

  test_table_count.db = *db;
  addTest(testSum, rc);

  return 0;
}

int test1_2(void **db, TESTSUM *testSum)
{
  char  createTest[] = "测试项 1.2:传入void**指针创建数据库";
  char *filename     = "voiddbtest.dat";
  int   rc;

  currentTest(testSum, createTest);

  remove(filename);
  rc = remove("log.dat");
  rc = GNCDB_open((GNCDB **)db, filename, 0, 0);

  if (rc) {}

  addTest(testSum, -1);

  return 0;
}

int testCreatedb(GNCDB **db, void **dbVoid, TESTSUM *testSum)
{
  test_table_count.testProject = "1:创建数据库";
  test1_1(db, testSum);
  // test1_2(dbVoid, testSum);
  return 0;
}

int test2_1and2_2(GNCDB *db, TESTSUM *testSum)
{
  int rc  = 0;
  int rc1 = 0;
  int rc2 = 0;
  int row = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  double min = -10000.0;
  double max = 10000.0;
#endif
  char  createTable1[] = "2.1:创建WPT表";
  char  createTable2[] = "2.2:创建ARPT表";
  char *WPT_field[4]   = {"WPT_ident", "WPT_lon", "WPT_lat", "WPT_blob"};
  char *ARPT_field[6]  = {"ARPT_ident", "ARPT_lon", "ARPT_lat", "ARPT_elev", "ARPT_length", "ARPT_mag_var"};

  test_table_count.SQL = "CREATE TABLE WPT("
                         "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL "
                         "WPT_lon FLOAT  NOT NULL "
                         "WPT_lat  FLOAT	NOT NULL "
                         "WPT_blob  INT);";

  currentTest(testSum, createTable1);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE WPT (WPT_ident CHAR(10) PRIMARY KEY NOT NULL, WPT_lon FLOAT, WPT_lat FLOAT, WPT_blob INT);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "WPT",
      4,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      10.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_blob",
      FIELDTYPE_BLOB,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);
#endif

  test_table_count.tableName = "WPT";
  test_table_count.fieldName = WPT_field;
  test_table_count.rowNum    = 4;
  test_table_count.rowcount  = 0;

  // printfPagePin(test_table_count.db->pagePool);

  rc1 = GNCDB_select(db, testCallBackMasterTable, &row, NULL, 1, 0, 0, "master");
  rc2 = GNCDB_select(db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", "tableName=WPT");

  if (tableExist && (rc | rc1 | rc2) == GNCDB_SUCCESS && row == 1) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist           = false;
  row                  = 0;
  test_table_count.SQL = "CREATE TABLE ARPT("
                         "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL "
                         "ARPT_lon FLOAT  NOT NULL "
                         "ARPT_lat FLOAT NOT NULL "
                         "ARPT_elev FLOAT NOT NULL "
                         "ARPT_length FLOAT NOT NULL "
                         "ARPT_mag_var FLOAT  NOT NULL);";

  currentTest(testSum, createTable2);

#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE ARPT("
      "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL,"
      "ARPT_lon FLOAT  NOT NULL,"
      "ARPT_lat FLOAT NOT NULL,"
      "ARPT_elev FLOAT NOT NULL,"
      "ARPT_length FLOAT NOT NULL,"
      "ARPT_mag_var FLOAT  NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "ARPT",
      6,
      "ARPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "ARPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_elev",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_length",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_mag_var",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);
#endif

  test_table_count.tableName = "ARPT";
  test_table_count.fieldName = ARPT_field;
  test_table_count.rowNum    = 6;
  test_table_count.rowcount  = 0;

  rc1 = GNCDB_select(db, testCallBack, &row, NULL, 1, 0, 0, "master");
  rc1 = GNCDB_select(db, testCallBack, &row, NULL, 1, 0, 0, "schema");

  rc1 = GNCDB_select(db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", "tableName=ARPT");
  rc2 = GNCDB_select(db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", "tableName=ARPT");

  if (tableExist && (rc | rc1 | rc2) == GNCDB_SUCCESS && row == 1) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist = false;

  return 0;
}

int test2_3(GNCDB *db, TESTSUM *testSum)
{
  int rc = 0;

  char  name[10]         = "WPT";
  char  tableName[15]    = {0};
  int   i                = 0;
  int   row              = 0;
  int   rc1              = 0;
  int   rc2              = 0;
  char *WPT_num_field[4] = {"WPT_ident", "WPT_lon", "WPT_lat", "WPT_blob"};
  char  condition[30]    = {0};
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  double min = -10000.0;
  double max = 10000.0;
#endif

  char createTable[]   = "2.3:创建多张表(自定义数量CREATE_TABLE_NUM)";
  test_table_count.SQL = "CREATE TABLE WPT0-9("
                         "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL "
                         "WPT_lon FLOAT  NOT NULL "
                         "WPT_lat  FLOAT	NOT NULL "
                         "WPT_blob  INT);";
  currentTest(testSum, createTable);

  for (i = 0; i < CREATE_TABLE_NUM; ++i) {
    sprintf(tableName, "%s%d", name, i);
#if defined SQL_TESTMODE
    sprintf(sql,
        "CREATE TABLE WPT%d(WPT_ident CHAR(10) PRIMARY KEY NOT NULL, "
        "WPT_lon FLOAT  NOT NULL, "
        "WPT_lat  FLOAT	NOT NULL, "
        "WPT_blob  INT);",
        i);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else

    rc = GNCDB_createTable(db,
        tableName,
        4,
        "WPT_ident",
        FIELDTYPE_VARCHAR,
        0,
        1,
        min,
        10.0,
        "WPT_lon",
        FIELDTYPE_REAL,
        0,
        0,
        -1000.0,
        1000.0,
        "WPT_lat",
        FIELDTYPE_REAL,
        0,
        0,
        -1000.0,
        max,
        "WPT_blob",
        FIELDTYPE_BLOB,
        0,
        0,
        min,
        max,
        10);
#endif
    row                        = 0;
    test_table_count.tableName = tableName;
    test_table_count.fieldName = WPT_num_field;
    test_table_count.rowNum    = 4;
    test_table_count.rowcount  = 0;
    sprintf(condition, "tableName=%s", tableName);

    // printfPagePin(db->pagePool);

    /* 查询master表中是否存在 */
    rc1 = GNCDB_select(db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", condition);
    /* 查询schema表中属性是否存在 */
    rc2 = GNCDB_select(db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", condition);

    if (tableExist && (rc | rc1 | rc2) == GNCDB_SUCCESS && row == 1) {
      // 表存在，继续下一张表
    } else {
      if (rc == GNCDB_SUCCESS) {
        rc = -1;
      }
      tableExist = false;
      break;
    }
  }

  addTest(testSum, rc);
  tableExist = false;
  return 0;
}

int test2_4(GNCDB *db, TESTSUM *testSum)
{
  char createTable[] = "2.4:创建与WPT表名重复的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  double min = -10000.0;
  double max = 10000.0;
#endif
  test_table_count.SQL = "CREATE TABLE WPT("
                         "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL "
                         "WPT_lon FLOAT  NOT NULL "
                         "WPT_lat  FLOAT	NOT NULL "
                         "WPT_blob  INT);";

  currentTest(testSum, createTable);

#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE WPT(WPT_ident CHAR(10) PRIMARY KEY NOT NULL,"
      "WPT_lon FLOAT  NOT NULL,"
      "WPT_lat  FLOAT	NOT NULL,"
      "WPT_blob  INT);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "WPT",
      4,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      10.0,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      10.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_blob",
      FIELDTYPE_BLOB,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);
#endif
  if (rc == GNCDB_TABLE_EXIST) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_5(GNCDB *db, TESTSUM *testSum)
{
  char createTable[] = "2.5:创建表名超长的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  double min = -10000.0;
  double max = 10000.0;
#endif
  test_table_count.SQL = "CREATE TABLE abcdefghigklmnopqrstuvwxyz1234567890("
                         "qqid  FLOAT PRIMARY KEY  NOT NULL  "
                         "qqid  FLOAT PRIMARY KEY  NOT NULL  "
                         "qqname CHAR(10)  NOT NULL  "
                         "qqage  FLOAT  NOT NULL);";

  currentTest(testSum, createTable);

#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE abcdefghigklmnopqrstuvwxyz1234567890"
      "(qqid  FLOAT PRIMARY KEY  NOT NULL,"
      "qname CHAR(10)  NOT NULL,"
      "qqage  FLOAT  NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "abcdefghigklmnopqrstuvwxyz1234567890",
      3,
      "qqid",
      FIELDTYPE_REAL,
      0,
      1,
      min,
      max,
      "qqname",
      FIELDTYPE_VARCHAR,
      0,
      0,
      min,
      100.0,
      "qqage",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);
#endif
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_6(GNCDB *db, TESTSUM *testSum)
{
  char createTable[] = "2.6:创建表属性名超长";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  double min = -10000.0;
  double max = 10000.0;
#endif
  test_table_count.SQL = "CREATE TABLE student("
                         "student_NAME  CHAR(10) PRIMARY KEY  NOT NULL  "
                         "student_ID FLOAT  NOT NULL  "
                         "student_qwertyuiop_1234567890  FLOAT NOT NULL);";

  currentTest(testSum, createTable);

#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE student("
      "student_NAME  CHAR(10) PRIMARY KEY  NOT NULL,"
      "student_ID FLOAT  NOT NULL"
      "student_qwertyuiop_1234567890  FLOAT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "student",
      3,
      "student_NAME",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "student_ID",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "student_qwertyuiop_1234567890",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);
#endif
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_7(GNCDB *db, TESTSUM *testSum)
{
  char  createTable[]      = "2.7:创建单列的表singlerow";
  int   rc                 = 0;
  int   row                = 0;
  char *singlerow_field[1] = {"Id"};
  int   rc1                = 0;
  int   rc2                = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "CREATE TABLE singlerow("
                         "Id  INT PRIMARY KEY  NOT NULL);";

  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE singlerow("
      "Id INT PRIMARY KEY NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db, "singlerow", 1, "Id", FIELDTYPE_INTEGER, 0, 1, 0.0, 10.0, 10);
#endif

  test_table_count.tableName = "singlerow";
  test_table_count.fieldName = singlerow_field;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  /* 查询master表中是否存在 */
  rc1 = GNCDB_select(db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", "tableName=singlerow");
  /* 查询schema表中属性是否存在 */
  rc2 = GNCDB_select(db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", "tableName=singlerow");

  if (tableExist && (rc | rc1 | rc2) == GNCDB_SUCCESS && row == 1) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist = false;
  return 0;
}

int test2_8(GNCDB *db, TESTSUM *testSum)
{
  char createTable[] = "2.8:创建多列的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "CREATE TABLE multilist("
                         "row0  INT PRIMARY KEY  NOT NULL  "
                         "row1 INT  NOT NULL  ..."
                         "row19  INT NOT NULL);";

  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE multilist( "
      "row0 INT PRIMARY KEY NOT NULL,"
      "row1  INT NOT NULL,"
      "row2  INT NOT NULL,"
      "row3  INT NOT NULL,"
      "row4  INT NOT NULL,"
      "row5  INT NOT NULL,"
      "row6  INT NOT NULL,"
      "row7  INT NOT NULL,"
      "row8  INT NOT NULL,"
      "row9  INT NOT NULL,"
      "row10  INT NOT NULL,"
      "row11  INT NOT NULL,"
      "row12  INT NOT NULL,"
      "row13  INT NOT NULL,"
      "row14  INT NOT NULL,"
      "row15  INT NOT NULL,"
      "row16  INT NOT NULL,"
      "row17  INT NOT NULL,"
      "row18  INT NOT NULL,"
      "row19  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE multilist( "
      "row0 INT PRIMARY KEY NOT NULL,"
      "row1  INT NOT NULL,"
      "row2  INT NOT NULL,"
      "row3  INT NOT NULL,"
      "row4  INT NOT NULL,"
      "row5  INT NOT NULL,"
      "row6  INT NOT NULL,"
      "row7  INT NOT NULL,"
      "row8  INT NOT NULL,"
      "row9  INT NOT NULL,"
      "row10  INT NOT NULL,"
      "row11  INT NOT NULL,"
      "row12  INT NOT NULL,"
      "row13  INT NOT NULL,"
      "row14  INT NOT NULL,"
      "row15  INT NOT NULL,"
      "row16  INT NOT NULL,"
      "row17  INT NOT NULL,"
      "row18  INT NOT NULL,"
      "row19  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "multilist",
      20,
      "row0",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "row1",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row4",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row5",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row6",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row7",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row8",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row9",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row10",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row11",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row12",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row13",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row14",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row15",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row16",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row17",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row18",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "row19",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif

#endif

  if (0 /*rc == "列名超出数量"*/) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist = false;
  return 0;
}

int test2_9(GNCDB *db, TESTSUM *testSum)
{
  char createTable[] = "2.9:创建表属性名重复";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "CREATE TABLE fieldagain("
                         "field_id  INT PRIMARY KEY  NOT NULL  "
                         "field_id INT  NOT NULL  "
                         "field_name INT  NOT NULL  "
                         "field_high  INT NOT NULL);";

  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE fieldagain("
      "field_id INT PRIMARY KEY  NOT NULL,"
      "field_id INT  NOT NULL,"
      "field_name INT  NOT NULL,"
      "field_high  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "fieldagain",
      4,
      "field_id",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "field_id",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "field_name",
      FIELDTYPE_VARCHAR,
      0,
      0,
      0.0,
      100.0,
      "field_high",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist = false;
  return 0;
}

int test2_10(GNCDB *db, TESTSUM *testSum)
{
  int   rc  = 0;
  int   row = 0;
  int   rc1 = 0, rc2 = 0;
  char  createTable[]         = "2.10:创建无主键的表";
  char *noprimarykey_field[5] = {"name", "id", "age", "high", "gender"};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE noprimarykey("
                         "name  CHAR(10) NOT NULL "
                         "id INT  NOT NULL "
                         "age INT NOT NULL "
                         "high INT NOT NULL "
                         "gender  CHAR(2) NOT NULL);";

  /* 为计数设置表格名称和字段名称 */
  test_table_count.tableName = "noprimarykey";
  test_table_count.fieldName = noprimarykey_field;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE noprimarykey("
      "name  CHAR(10) NOT NULL,"
      "id INT  NOT NULL,"
      "age INT NOT NULL,"
      "high INT NOT NULL,"
      "gender  CHAR(8) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE noprimarykey("
      "name  CHAR(10) NOT NULL,"
      "id INT  NOT NULL,"
      "age INT NOT NULL,"
      "high INT NOT NULL,"
      "gender  CHAR(8) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "noprimarykey",
      5,
      "name",
      FIELDTYPE_VARCHAR,
      0,
      0,
      0.0,
      20.0,
      "id",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "age",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      50.0,
      "high",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      250.0,
      "gender",
      FIELDTYPE_VARCHAR,
      0,
      0,
      0.0,
      10.0,
      50);
#endif
#endif
  /* 检查表格属性是否存在于schema表格中 */
  // rc2 = GNCDB_select(db, testCallBackSchemaTable, &row, NULL, 1, 0, 1, "schema", "tableName=noprimarykey");
  /* 检查表格是否存在于主表格中 */
  // rc1 = GNCDB_select(db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", "tableName=noprimarykey");
  if (tableExist && rc == rc1 && rc1 == rc2 && rc2 == GNCDB_SUCCESS && row == 1) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist = false;
  return 0;
}

int test2_11(GNCDB *db, TESTSUM *testSum)
{
  char  createTable[]           = "2.11:创建多主键的表";
  int   rc                      = 0;
  int   row                     = 0;
  int   rc1                     = 0;
  int   rc2                     = 0;
  char *someprimarykey_field[5] = {"name", "id", "age", "high", "gender"};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "CREATE TABLE someprimarykey("
                         "name  CHAR(10) PRIMARY KEY NOT NULL  "
                         "id INT  PRIMARY KEY NOT NULL  "
                         "age INT  PRIMARY KEY NOT NULL  "
                         "high INT  NOT NULL  "
                         "gender  CHAR(2) NOT NULL);";

  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE someprimarykey("
      "name  CHAR(10) PRIMARY KEY NOT NULL,"
      "id INT  PRIMARY KEY NOT NULL,"
      "age INT  PRIMARY KEY NOT NULL,"
      "high INT  NOT NULL,"
      "gender CHAR(8) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE someprimarykey("
      "name  CHAR(10) PRIMARY KEY NOT NULL,"
      "id INT  PRIMARY KEY NOT NULL,"
      "age INT  PRIMARY KEY NOT NULL,"
      "high INT  NOT NULL,"
      "gender CHAR(8) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "someprimarykey",
      5,
      "name",
      FIELDTYPE_VARCHAR,
      0,
      1,
      0.0,
      20.0,
      "id",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "age",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      50.0,
      "high",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      250.0,
      "gender",
      FIELDTYPE_VARCHAR,
      0,
      0,
      0.0,
      10.0,
      50);
#endif
#endif
  test_table_count.tableName = "someprimarykey";
  test_table_count.fieldName = someprimarykey_field;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  /* 查询master表中是否存在 */
  rc1 = GNCDB_select(db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", "tableName=someprimarykey");

  /* 查询schema表中属性是否存在 */
  rc2 = GNCDB_select(db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", "tableName=someprimarykey");

  if (tableExist && (rc | rc1 | rc2) == GNCDB_SUCCESS && row == 1) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist = false;
  return 0;
}

int test2_12(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.12:创建全主键的表";
  int  rc            = 0;
  int  row           = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  int   rc1, rc2;
  char *allprimarykey_field[5] = {"name", "id", "age", "high", "gender"};

  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE allprimarykey("
                         "name  CHAR(10) PRIMARY KEY NOT NULL "
                         "id INT  PRIMARY KEY NOT NULL "
                         "age INT  PRIMARY KEY NOT NULL "
                         "high INT PRIMARY KEY NOT NULL "
                         "gender CHAR(2) PRIMARY KEY NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE allprimarykey("
      "name  CHAR(10) PRIMARY KEY NOT NULL,"
      "id INT  PRIMARY KEY NOT NULL,"
      "age INT  PRIMARY KEY NOT NULL,"
      "high INT PRIMARY KEY NOT NULL,"
      "gender CHAR(8) PRIMARY KEY NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE allprimarykey("
      "name  CHAR(10) PRIMARY KEY NOT NULL,"
      "id INT  PRIMARY KEY NOT NULL,"
      "age INT  PRIMARY KEY NOT NULL,"
      "high INT PRIMARY KEY NOT NULL,"
      "gender CHAR(8) PRIMARY KEY NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "allprimarykey",
      5,
      "name",
      FIELDTYPE_VARCHAR,
      0,
      1,
      0.0,
      20.0,
      "id",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "age",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      50.0,
      "high",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      250.0,
      "gender",
      FIELDTYPE_VARCHAR,
      0,
      1,
      0.0,
      10.0,
      50);
#endif
#endif
  /* 设置计数相关变量 */
  test_table_count.tableName = "allprimarykey";
  test_table_count.fieldName = allprimarykey_field;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  /* 查询master表中是否存在 */
  rc1 = GNCDB_select(db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", "tableName=allprimarykey");

  /* 查询schema表中属性是否存在 */
  rc2 = GNCDB_select(db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", "tableName=allprimarykey");

  /* 检查测试结果 */
  if (tableExist && rc == GNCDB_SUCCESS && rc1 == GNCDB_SUCCESS && rc2 == GNCDB_SUCCESS && row == 1) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  tableExist = false;
  return 0;
}

int test2_13(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.13:创建与master重名的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE master("
                         "master_ident  CHAR(10) PRIMARY KEY NOT NULL "
                         "master_lon FLOAT NOT NULL "
                         "master_lat FLOAT NOT NULL "
                         "master_lon FLOAT NOT NULL "
                         "master_lat FLOAT NOT NULL "
                         "master_str  CHAR(10) NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE master("
      "master_ident  CHAR(10) PRIMARY KEY NOT NULL,"
      "master_lon FLOAT NOT NULL,"
      "master_lat FLOAT NOT NULL,"
      "master_str  CHAR(10) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE master("
      "master_ident  CHAR(10) PRIMARY KEY NOT NULL,"
      "master_lon FLOAT NOT NULL,"
      "master_lat FLOAT NOT NULL,"
      "master_str  CHAR(10) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "master",
      4,
      "master_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      0.0,
      100.0,
      "master_lon",
      FIELDTYPE_REAL,
      0,
      0,
      0.0,
      100.0,
      "master_lat",
      FIELDTYPE_REAL,
      0,
      0,
      0.0,
      12000.0,
      "master_str",
      FIELDTYPE_VARCHAR,
      0,
      0,
      0.0,
      10.0,
      100);
#endif
#endif

  if (rc == GNCDB_TABLE_EXIST) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_14(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.14:创建与schema重名的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE schema("
                         "schema_ident  CHAR(10) PRIMARY KEY NOT NULL "
                         "schema_lon FLOAT NOT NULL "
                         "schema_lat FLOAT Not NULL "
                         "schema_lon FLOAT NOT NULL "
                         "schema_lat FLOAT Not NULL "
                         "schema_str  CHAR(10) NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE schema("
      "schema_ident  CHAR(10) PRIMARY KEY NOT NULL,"
      "schema_lon FLOAT NOT NULL,"
      "schema_lat FLOAT NOT NULL,"
      "schema_str  CHAR(10) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE schema("
      "schema_ident  CHAR(10) PRIMARY KEY NOT NULL,"
      "schema_lon FLOAT NOT NULL,"
      "schema_lat FLOAT NOT NULL,"
      "schema_str  CHAR(10) NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "schema",
      4,
      "schema_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      0.0,
      100.0,
      "schema_lon",
      FIELDTYPE_REAL,
      0,
      0,
      0.0,
      100.0,
      "schema_lat",
      FIELDTYPE_REAL,
      0,
      0,
      0.0,
      12000.0,
      "schema_str",
      FIELDTYPE_VARCHAR,
      0,
      0,
      0.0,
      10.0,
      100);
#endif
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_TABLE_EXIST) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_15(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.15:创建最大行数为0的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE maxrow_0("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE maxrow_0("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "maxrow_0",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      0);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_16(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.16:创建最大行数为负的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE maxrow_minus("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE maxrow_minus("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "maxrow_minus",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      -10);
#endif
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_17(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.17:创建最大行数缺省的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE maxrow_not("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE maxrow_not("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "maxrow_not",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      1000);  // 这里少了一个参数
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_18(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.18:创建最大行数之后有其他输入的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE maxrow_excess("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);2322343";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE maxrow_excess("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "maxrow_excess",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10,
      2322343);
#endif
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_19(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.19:创建最大行数大于int值的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE maxrow_machint("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE maxrow_machint("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "maxrow_machint",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      1000000000000000);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_20(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.20:创建表名字符串为空的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE ("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE ("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      "",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID || rc == -82) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_21(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.21:创建表名为NULL的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE NULL("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE NULL ("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(db,
      NULL,
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAMNULL || rc == -82) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_22(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.22:创建表名缺省的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE ("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE ("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /*
  GNCDB_createTable(db, 3,
                   "attr1", FIELDTYPE_INTEGER, 0, 1, 0.0, 100.0,
                   "attr2", FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
                   "attr3", FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
                   10);
  */
#endif
  if (rc == GNCDB_PARAM_INVALID || rc == -82) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_23(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.23:创建属性名字符串为空的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE attr_not("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "  INT NOT NULL "
                         "attr3  INT NOT NULL);";
  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE attr_not("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "  INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，属性名字符串为空 */
  rc = GNCDB_createTable(db,
      "attr_not",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID || rc == -82) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_24(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.24:创建属性名为NULL的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE attr_null("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "NULL INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE attr_null("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "NULL INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，属性名为NULL */
  rc = GNCDB_createTable(db,
      "attr_null",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      NULL,
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID || rc == -82) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_25(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.25:创建属性名缺省的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE attr_lack("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         " INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE attr_lack("
      "attr1  INT PRIMARY KEY NOT NULL,"
      " INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /*
  GNCDB_createTable(db, "attr_lack", 3,
                   "attr1", FIELDTYPE_INTEGER, 0, 1, 0.0, 100.0,
                    FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
                   "attr3", FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
                   10);
  */
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID || rc == -82) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_26(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.26:创建列数大于指定参数表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE column_excess("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE column_excess("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，列数大于指定参数 */
  rc = GNCDB_createTable(db,
      "column_excess",
      2,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_27(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.27:创建列数小于指定参数表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE column_lack("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE column_lack("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2  INT PRIMARY KEY NOT NULL,"
      "attr3 INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else

  /* 将下面的代码注释掉，因为没有在这个函数中使用 */
  /*
  GNCDB_createTable(db, "column_lack", 4,
                   "attr1", FIELDTYPE_INTEGER, 0, 1, 0.0, 100.0,
                   "attr2", FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
                   "attr3", FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
                   10);
  */
#endif

  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_28(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.28:创建主键可以为空的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE keyisnull("
                         "attr1  INT PRIMARY KEY NULL "
                         "attr2 INT NOT NULL "
                         "attr3  INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE keyisnull("
      "attr1  INT PRIMARY KEY ,"
      "attr2 INT NOT NULL,"
      "attr3  INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
  if (rc == 0) {
    /* sql默认设置主键不能为空，所以该测试项没有意义 */
    rc = GNCDB_PRIMARYKEY_NOTNULL;
  }
#else
  /* 在数据库中创建表格，将主键可以为空 */
  rc = GNCDB_createTable(db,
      "keyisnull",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      1,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PRIMARYKEY_NOTNULL) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_29(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.29:创建非主键可以为空的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE attrisnull("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT NULL "
                         "attr3 INT NULL "
                         "attr4  INT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE attrisnull("
      "attr1 INT PRIMARY KEY NOT NULL,"
      "attr2 INT,"
      "attr3 float,"
      "attr4  CHAR(10));",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，其中某些属性可以为空 */
  rc = GNCDB_createTable(db,
      "attrisnull",
      4,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      1,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_REAL,
      1,
      0,
      0.0,
      100.0,
      "attr4",
      FIELDTYPE_VARCHAR,
      1,
      0,
      0.0,
      10.0,
      10);
#endif
  /* 添加测试结果 */
  addTest(testSum, rc);

  return 0;
}

int test2_30(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.30:创建所有属性值可为空的表";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE allisnull("
                         "attr1  INT PRIMARY KEY NULL "
                         "attr2 INT NULL "
                         "attr3 INT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE allisnull("
      "attr1  INT PRIMARY KEY,"
      "attr2 INT,"
      "attr3 INT);",
      NULL,
      NULL,
      &errmsg);
  if (rc == 0) {
    /* sql默认设置主键不能为空，所以该测试项没有意义 */
    rc = GNCDB_PRIMARYKEY_NOTNULL;
  }
#else
  /* 在数据库中创建表格，所有属性值都可以为空 */
  rc = GNCDB_createTable(db,
      "allisnull",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      1,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      1,
      0,
      0.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      1,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PRIMARYKEY_NOTNULL) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_31(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.31:int和real最大最小值相等";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE intrealmaxmineq("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT CHECK (attr2>=100.0 AND attr2<=100.0) NOT NULL "
                         "attr3 INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE intrealmaxmineq("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT CHECK (attr2>=100.0 AND attr2<=100.0) NOT NULL,"
      "attr3 INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，其中int和real属性的最大最小值相等 */
  rc = GNCDB_createTable(db,
      "intrealmaxmineq",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      100.0,
      100.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_32(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.32:int和real类型最大值小于最小值";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE intrealmaxminle("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 INT CHECK (attr2>=100.0 AND attr2<=10.0) NOT NULL "
                         "attr3 INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE intrealmaxminle("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 INT CHECK (attr2>=100.0 AND attr2<=10.0) NOT NULL,"
      "attr3 INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，其中int和real属性的最大值小于最小值 */
  rc = GNCDB_createTable(db,
      "intrealmaxminle",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_INTEGER,
      0,
      0,
      100.0,
      10.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test2_33(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.33:string类型最大值等于最小值";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE stringmaxmineq("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 CHAR CHECK (LENGTH(attr2)>=10.0 AND LENGTH(attr2)<=10.0) NOT NULL "
                         "attr3 INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE stringmaxmineq("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 CHAR CHECK (LENGTH(attr2)>=10.0 AND LENGTH(attr2)<=10.0) NOT NULL,"
      "attr3 INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，其中string类型的属性的最大值等于最小值 */
  rc = GNCDB_createTable(db,
      "stringmaxmineq",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_VARCHAR,
      0,
      0,
      10.0,
      10.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  addTest(testSum, rc);

  return 0;
}

int test2_34(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char createTable[] = "2.34:string类型最大值小于最小值";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  /* SQL语句用于创建表格 */
  test_table_count.SQL = "CREATE TABLE stringmaxminls("
                         "attr1  INT PRIMARY KEY NOT NULL "
                         "attr2 CHAR CHECK (LENGTH(attr2)>=10.0 AND LENGTH(attr2)<=0.0) NOT NULL "
                         "attr3 INT NOT NULL);";

  /* 测试名称 */
  currentTest(testSum, createTable);
#if defined SQL_TESTMODE
  rc = GNCDB_exec(db,
      "CREATE TABLE stringmaxminls("
      "attr1  INT PRIMARY KEY NOT NULL,"
      "attr2 CHAR CHECK (LENGTH(attr2)>=10.0 AND LENGTH(attr2)<=0.0) NOT NULL,"
      "attr3 INT NOT NULL);",
      NULL,
      NULL,
      &errmsg);
#else
  /* 在数据库中创建表格，其中string类型的属性的最大值小于最小值 */
  rc = GNCDB_createTable(db,
      "stringmaxminls",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_VARCHAR,
      0,
      0,
      10.0,
      0.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);
#endif
  /* 检查测试结果 */
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int testCreateTable(GNCDB *db, TESTSUM *testSum)
{
  test_table_count.testProject = "2:创建表";
  // currentTest(testSum, createTable);

  test2_1and2_2(db, testSum);
  test2_3(db, testSum);
  test2_4(db, testSum);
  test2_5(db, testSum);
  test2_6(db, testSum);
  test2_7(db, testSum);
  test2_8(db, testSum);
  test2_9(db, testSum);
  test2_10(db, testSum);
  test2_11(db, testSum);
  test2_12(db, testSum);
  test2_13(db, testSum);
  test2_14(db, testSum);
  test2_15(db, testSum);
  test2_16(db, testSum);
  test2_17(db, testSum);
  test2_18(db, testSum);
  test2_19(db, testSum);
  test2_20(db, testSum);
  test2_21(db, testSum);
  test2_22(db, testSum);
  test2_23(db, testSum);
  test2_24(db, testSum);
  test2_25(db, testSum);
  test2_26(db, testSum);
  test2_27(db, testSum);
  test2_28(db, testSum);
  test2_29(db, testSum);
  test2_30(db, testSum);
  test2_31(db, testSum);
  test2_32(db, testSum);
  test2_33(db, testSum);
  test2_34(db, testSum);

  return 0;
}

int test3_1(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char  insertTable[] = "3.1:在WPT表中插入指定数据";
  int   rc            = 0;
  int   i             = 0;
  FILE *fileData      = NULL;
  char  lon[20]       = {0};
  char  lat[20]       = {0};
  char  str[100]      = {0};
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  BYTE *ImgBuffer = NULL;
#endif
  char *wptvalue[3] = {wpt.sc8_wpt_ident, lon, lat};
  char *path        = strJoin(testFilePath, testFileName1);

  if (path == NULL) {
    return -1;
  }

  /* SQL语句用于插入数据 */
  test_table_count.SQL =
      "INSERT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob) VALUES(wpt.sc8_wpt_ident wpt.f64_lon wpt.f64_lat NULL);";

  /* 测试名称 */
  currentTest(testSum, insertTable);

  fileData = fopen(path, "r");

  if (fileData == NULL) {
    return -1;
  }

  for (i = 0; i < WPTROWS; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
#if defined SQL_TESTMODE
    sprintf(sql,
        "INSERT INTO WPT(WPT_ident,WPT_lon,WPT_lat,WPT_blob) VALUES('%s',%f,%f,0);",
        wpt.sc8_wpt_ident,
        wpt.f64_lon,
        wpt.f64_lat);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
    rc = GNCDB_insert(db, NULL, "WPT", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat, 0, ImgBuffer);
#endif
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);

  fileData = fopen(path, "r");

  for (i = 0; i < WPTROWS; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    sprintf(lon, "%f", wpt.f64_lon);
    sprintf(lat, "%f", wpt.f64_lat);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = wptvalue;
    test_table_count.rowNum    = 1;
    test_table_count.rowcount  = 0;
    test_table_count.testFlag  = false;

    sprintf(str, "rowId=%d", i);

    GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "WPT", str);
  }
  fclose(fileData);
  // GNCDB_exec(db, "select * from WPT", testCallBack, NULL, NULL);
  GNCDB_select(db, NULL, &test_table_count.rowcount, NULL, 1, 0, 0, "WPT", str);

  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  return 0;
}

int test3_2(GNCDB *db, TESTSUM *testSum)
{
  /* 变量定义 */
  char insertTable[] = "3.2:在ARPT表中插入指定数据";
  int  rc            = 0;
  int  i             = 0;
  char lon[20]       = {0};
  char lat[20]       = {0};
  char elev[20]      = {0};
  char length[20]    = {0};
  char var[20]       = {0};
  char str[100]      = {0};
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif
  char *arptvalue[6] = {0};
  char *path         = strJoin(testFilePath, testFileName2);
  FILE *fileData     = NULL;
  if (path == NULL) {
    return -1;
  }

  /* SQL语句用于插入数据 */
  test_table_count.SQL =
      "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var) VALUES(arpt.sc8_arpt_ident "
      "arpt.f64_lon arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var);";

  /* 测试名称 */
  currentTest(testSum, insertTable);

  fileData = fopen(path, "r");

  if (fileData == NULL) {
    return -1;
  }

  for (i = 0; i < ARPRROWS; ++i) {
    fscanf(fileData,
        "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
        arpt.sc8_arpt_ident,
        &arpt.f64_lon,
        &arpt.f64_lat,
        &arpt.f64_elev,
        &arpt.f64_longest_rwy_length,
        &arpt.f64_mag_var);
#if defined SQL_TESTMODE
    sprintf(sql,
        "INSERT INTO ARPT (ARPT_ident, ARPT_lon, ARPT_lat, ARPT_elev, ARPT_length, ARPT_mag_var)"
        "VALUES('%s', %lf, %lf, %lf, %lf, %lf);",
        arpt.sc8_arpt_ident,
        arpt.f64_lon,
        arpt.f64_lat,
        arpt.f64_elev,
        arpt.f64_longest_rwy_length,
        arpt.f64_mag_var);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
    rc = GNCDB_insert(db,
        NULL,
        "ARPT",
        arpt.sc8_arpt_ident,
        arpt.f64_lon,
        arpt.f64_lat,
        arpt.f64_elev,
        arpt.f64_longest_rwy_length,
        arpt.f64_mag_var);
#endif

    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);

  fileData = fopen(path, "r");

  for (i = 0; i < ARPRROWS; ++i) {
    fscanf(fileData,
        "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
        arpt.sc8_arpt_ident,
        &arpt.f64_lon,
        &arpt.f64_lat,
        &arpt.f64_elev,
        &arpt.f64_longest_rwy_length,
        &arpt.f64_mag_var);
    arptvalue[0] = arpt.sc8_arpt_ident;
    arptvalue[1] = lon;
    arptvalue[2] = lat;
    arptvalue[3] = elev;
    arptvalue[4] = length;
    arptvalue[5] = var;
    sprintf(lon, "%f", arpt.f64_lon);
    sprintf(lat, "%f", arpt.f64_lat);
    sprintf(elev, "%f", arpt.f64_elev);
    sprintf(length, "%f", arpt.f64_longest_rwy_length);
    sprintf(var, "%f", arpt.f64_mag_var);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = arptvalue;
    test_table_count.rowNum    = 6;
    test_table_count.rowcount  = 0;
    test_table_count.testFlag  = false;
    sprintf(str, "rowId=%d", i);

    GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "ARPT", str);
  }
  fclose(fileData);

  i = 0;
  GNCDB_select(db, NULL, &i, NULL, 1, 0, 0, "ARPT");

  if (rc == GNCDB_SUCCESS && test_table_count.testFlag == true) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  return 0;
}

int test3_3(GNCDB *db, TESTSUM *testSum)
{
  int  rows          = 0;
  char insertTable[] = "3.3:WPT插入主键重复的记录";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif
  test_table_count.SQL =
      "INSERT INTO WPT(WPT_ident WPT_lon WPT_lat WPT_blob) VALUES(TDOQE wpt.f64_lon wpt.f64_lat NULL);";

  currentTest(testSum, insertTable);

  strcpy(wpt.sc8_wpt_ident, "TDOQE");

#if defined SQL_TESTMODE
  sprintf(sql,
      "INSERT INTO WPT(WPT_ident, WPT_lon, WPT_lat, WPT_blob)"
      "VALUES('%s', %f, %f, 0);",
      wpt.sc8_wpt_ident,
      wpt.f64_lon,
      wpt.f64_lat);
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "WPT", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat, 0, NULL);
#endif
  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident=TDOQE");

  if ((rc == GNCDB_DUPLICATE_PRIMARY_KEY || rc == -90) && rows == 1) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_4(GNCDB *db, TESTSUM *testSum)
{
  int  rows          = 0;
  char insertTable[] = "3.4:ARPT插入字符串不合法的记录";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif
  test_table_count.SQL =
      "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var) VALUES(123213 arpt.f64_lon "
      "arpt.f64_lat arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var);";

  currentTest(testSum, insertTable);
#if defined SQL_TESTMODE
  sprintf(sql,
      "INSERT INTO ARPT (ARPT_ident, ARPT_lon, ARPT_lat, ARPT_elev, ARPT_length, ARPT_mag_var)"
      "VALUES(123213, %f, %f, %f, %f, %f);",
      arpt.f64_lon,
      arpt.f64_lat,
      arpt.f64_elev,
      arpt.f64_longest_rwy_length,
      arpt.f64_mag_var);
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
/*
    rc = GNCDB_insert(db, &rows, "ARPT",
                      123213,
                      arpt.f64_lon,
                      arpt.f64_lat,
                      arpt.f64_elev,
                      arpt.f64_longest_rwy_length,
                      arpt.f64_mag_var);
*/
#endif

  GNCDB_select(db, NULL, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident=123213");
  if ((rc == GNCDB_PARAM_INVALID || rc == GNCDB_SCHEMA_FIELD_TYPE_MISMATCH) && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_5(GNCDB *db, TESTSUM *testSum)
{
  int  rows          = 0;
  char insertTable[] = "3.5:ARPT表插入int或real不合法的记录";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif
  test_table_count.SQL =
      "INSERT INTO ARPT (ARPT_ident ARPT_lon ARPT_lat ARPT_elev ARPT_length ARPT_mag_var) VALUES(arpt.sc8_arpt_ident  "
      "arpt.f64_lon abdsscdef arpt.f64_elev arpt.f64_longest_rwy_length arpt.f64_mag_var);";

  currentTest(testSum, insertTable);
#if defined SQL_TESTMODE
  sprintf(sql,
      "INSERT INTO ARPT (ARPT_ident, ARPT_lon, ARPT_lat, ARPT_elev, ARPT_length, ARPT_mag_var)"
      "VALUES('ZZZZZZZ' , %f, 'abdsscdef', %f, %f, %f);",
      arpt.f64_lon,
      arpt.f64_elev,
      arpt.f64_longest_rwy_length,
      arpt.f64_mag_var);
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db,
      &rows,
      "ARPT",
      "ZZZZZZZ",
      arpt.f64_lon,
      "abdsscdef",
      arpt.f64_elev,
      arpt.f64_longest_rwy_length,
      arpt.f64_mag_var);
#endif
  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident=ZZZZZZZ");

  if (rc == GNCDB_PARAM_INVALID && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_6(GNCDB *db, TESTSUM *testSum)
{
  char  insertTable[] = "3.6:插入超过最大行数的记录";
  char *path          = NULL;
  FILE *fileData      = NULL;
  int   rc            = 0;
  int   i             = 0;
  int   rows          = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif

  test_table_count.SQL = "INSERT INTO WPT0(WPT_ident  WPT_lon  WPT_lat  WPT_blob)"
                         "VALUES(wpt.sc8_wpt_ident wpt.f64_lon wpt.f64_lat  NULL); ";

  currentTest(testSum, insertTable);
  path = strJoin(testFilePath, testFileName1);

  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");

  if (fileData == NULL) {
    my_free(path);
    return -1;
  }

  for (i = 0; i < 12; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
#if defined SQL_TESTMODE
    sprintf(sql,
        "INSERT INTO WPT0(WPT_ident, WPT_lon, WPT_lat, WPT_blob)"
        "VALUES('%s', %f, %f, NULL);",
        wpt.sc8_wpt_ident,
        wpt.f64_lon,
        wpt.f64_lat);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
    rc = GNCDB_insert(db, NULL, "WPT0", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat, 0, NULL);
#endif
    if (rc != GNCDB_SUCCESS) {
      break;
    }
  }

  fclose(fileData);

  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 0, "WPT0");

  if (rc == GNCDB_ROWS_OVERFLOW && rows == 10) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  return 0;
}

int test3_7(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  BYTE *ImgBuffer = NULL;
#endif

  char insertTable[] = "3.7:插入字符串超出限制的记录";

  test_table_count.SQL = "INSERT INTO WPT1(WPT_ident  WPT_lon  WPT_lat  WPT_blob)"
                         "VALUES(abcde12345678910 wpt.f64_lon wpt.f64_lat  NULL); ";
  currentTest(testSum, insertTable);

#if defined SQL_TESTMODE
  sprintf(sql,
      "INSERT INTO WPT1(WPT_ident, WPT_lon, WPT_lat, WPT_blob) VALUES('abcde12345678910', %f, %f, 0);",
      wpt.f64_lon,
      wpt.f64_lat);
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, &rows, "WPT1", "abcde12345678910", wpt.f64_lon, wpt.f64_lat, 0, ImgBuffer);
#endif

  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident=abcde12345678910");

  if ((rc == GNCDB_PARAM_INVALID) && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_8(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  BYTE *ImgBuffer = NULL;
#endif
  char insertTable[]   = "3.8:插入real值大于最大值的记录";
  test_table_count.SQL = "INSERT INTO WPT1(WPT_ident  WPT_lon  WPT_lat  WPT_blob)"
                         "VALUES(wpt.sc8_wpt_ident 2000.0 wpt.f64_lat  NULL); ";
  currentTest(testSum, insertTable);

#if defined SQL_TESTMODE
  sprintf(sql, "INSERT INTO WPT1(WPT_ident, WPT_lon, WPT_lat, WPT_blob) VALUES('abcde', 2000.0, %f, 0);", wpt.f64_lat);
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "WPT1", "abcde", 2000.0, wpt.f64_lat, 0, ImgBuffer);
#endif

  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 1, "WPT1", "WPT_ident=abcde");

  if ((rc == GNCDB_PARAM_INVALID || rc == GNCDB_INSERT_FAILED) && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_9(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  BYTE *ImgBuffer = NULL;
#endif

  char insertTable[]   = "3.9:插入real值小于最小值的记录";
  test_table_count.SQL = "INSERT INTO WPT1(WPT_ident  WPT_lon  WPT_lat  WPT_blob)"
                         "VALUES(wpt.sc8_wpt_ident -2000.0 wpt.f64_lat  NULL); ";
  currentTest(testSum, insertTable);

#if defined SQL_TESTMODE
  sprintf(sql, "INSERT INTO WPT1(WPT_ident, WPT_lon, WPT_lat, WPT_blob) VALUES('abcde', -2000.0, %f, 0);", wpt.f64_lat);
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "WPT1", "abcde", -2000.0, wpt.f64_lat, 0, ImgBuffer);
#endif

  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident=abcde");

  if ((rc == GNCDB_PARAM_INVALID || rc == GNCDB_INSERT_FAILED) && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_10(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.10:插入int值大于最大值的记录";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif

  test_table_count.SQL = "INSERT INTO WPTTEST(WPT_ident  WPT_lon  WPT_lat)"
                         "VALUES(wpt.sc8_wpt_ident wpt.f64_lon 502); ";

  currentTest(testSum, insertTable);

  rc = GNCDB_createTable(db,
      "WPTTEST",
      3,
      "TEXT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      -1.0,
      10.0,
      "TEXT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      -1000.0,
      1000.0,
      "TEXT_lat",
      FIELDTYPE_INTEGER,
      0,
      0,
      -500.0,
      500.0,
      10);

  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
  }

#if defined SQL_TESTMODE
  sprintf(sql, "INSERT INTO WPTTEST(TEXT_ident, TEXT_lon, TEXT_lat) VALUES('aaaaa', 100.0, 502);");
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "WPTTEST", "aaaaa", 100.0, 502);
#endif
  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 1, "WPTTEST", "TEXT_ident=aaaaa");

  if ((rc == GNCDB_PARAM_INVALID || rc == GNCDB_INSERT_FAILED) && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_11(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.11:插入int值小于最小值的记录";
  int  rc            = 0;
  int  rows          = -1;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif

  test_table_count.SQL = "INSERT INTO WPTTEST(WPT_ident  WPT_lon  WPT_lat)"
                         "VALUES(wpt.sc8_wpt_ident wpt.f64_lon -502); ";

  currentTest(testSum, insertTable);
#if defined SQL_TESTMODE
  sprintf(sql, "INSERT INTO WPTTEST(TEXT_ident, TEXT_lon, TEXT_latb) VALUES('aaaaa', 100.0, -502);");
  rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "WPTTEST", "aaaaa", "aaaaa", 100.0, -502);
#endif
  GNCDB_select(db, testNoNeedDataCallBack, &rows, NULL, 1, 0, 1, "WPTTEST", "TEXT_ident=aaaaab");

  if (rc == GNCDB_INSERT_FAILED && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_12(GNCDB *db, TESTSUM *testSum)
{
  char  insertTable[]   = "3.12:在单列表中插入记录";
  int   rc              = 0;
  int   rows            = 0;
  char *singlervalue[5] = {"1", "2", "3", "4", "5"};
  int   i               = 1;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
#endif

  test_table_count.SQL = "INSERT INTO singlerow(Id)"
                         "VALUES(1); ";

  currentTest(testSum, insertTable);

  for (; i < 6; ++i) {
#ifdef SQL_TESTMODE
    sprintf(sql, "INSERT INTO singlerow(Id) VALUES(%d);", i);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
    rc = GNCDB_insert(db, NULL, "singlerow", i);
#endif
    if (rc != GNCDB_SUCCESS) {
      break;
    }
  }

  test_table_count.tableName = "singlerow";
  test_table_count.fieldName = singlervalue;
  test_table_count.rowNum    = 0;
  test_table_count.rowcount  = 0;
  test_table_count.testFlag  = false;

  GNCDB_select(db, testCallBackInsertsinglerowTable, &rows, NULL, 1, 0, 0, "singlerow");

  if (rc == GNCDB_SUCCESS && rows == 5 && test_table_count.rowNum == 5) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_13(GNCDB *db, TESTSUM *testSum)
{
  char  insertTable[]    = "3.13:在无主键表插入数据";
  char *nokeyvalue[5][5] = {
      {"ZoeChen", "10", "27", "172", "female"},
      {"RyanLiu", "12", "35", "186", "male"},
      {"LilyWang", "14", "19", "165", "female"},
      {"DavidZhang", "16", "42", "178", "male"},
      {"EmmaWu", "18", "31", "170", "female"},
  };
  char str[100] = {0};
  int  rc       = 0;
  int  i        = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL       = "INSERT INTO noprimarykey(name id age high gender)"
                               "VALUES('ZoeChen' 10 27 172 'female'); ";
  test_table_count.tableName = "noprimarykey";
  currentTest(testSum, insertTable);
  // rc = GNCDB_createTable(db, "noprimarykey", 5,
  //                        "name", FIELDTYPE_VARCHAR, 0, 0, 0.0, 20.0,
  //                        "id", FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
  //                        "age", FIELDTYPE_INTEGER, 0, 0, 0.0, 50.0,
  //                        "high", FIELDTYPE_INTEGER, 0, 0, 0.0, 250.0,
  //                        "gender", FIELDTYPE_VARCHAR, 0, 0, 0.0, 10.0,
  //                        50);

#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO noprimarykey VALUES('ZoeChen', 10, 27, 172, 'female'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO noprimarykey VALUES('RyanLiu', 12, 35, 186, 'male'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO noprimarykey VALUES('LilyWang', 14, 19, 165, 'female'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO noprimarykey VALUES('DavidZhang', 16, 42, 178, 'male'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO noprimarykey VALUES('EmmaWu', 18, 31, 170, 'female'); ", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "noprimarykey", "ZoeChen", 10, 27, 172, "female");
  rc = GNCDB_insert(db, NULL, "noprimarykey", "RyanLiu", 12, 35, 186, "male");
  rc = GNCDB_insert(db, NULL, "noprimarykey", "LilyWang", 14, 19, 165, "female");
  rc = GNCDB_insert(db, NULL, "noprimarykey", "DavidZhang", 16, 42, 178, "male");
  rc = GNCDB_insert(db, NULL, "noprimarykey", "EmmaWu", 18, 31, 170, "female");
#endif
  for (i = 0; i < 5; ++i) {
    test_table_count.fieldName = nokeyvalue[i];
    test_table_count.rowNum    = 5;
    test_table_count.rowcount  = 0;
    test_table_count.testFlag  = false;

    sprintf(str, "rowId=%d", i);
    // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "noprimarykey", str);

    GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "noprimarykey", str);
  }

  /** 检查测试结果并将其添加到测试摘要中。 */
  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_14(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.14:在多主键表插入记录";
  int  rc            = 0;
  int  i             = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char  str[100]           = {0};
  char *somekeyvalue[5][5] = {
      {"ZoeChen", "10", "27", "172", "female"},
      {"RyanLiu", "12", "35", "186", "male"},
      {"LilyWang", "14", "19", "165", "female"},
      {"DavidZhang", "16", "42", "178", "male"},
      {"EmmaWu", "18", "31", "170", "female"},
  };
  test_table_count.SQL = "INSERT INTO someprimarykey(name  id  age  high  gender)"
                         "VALUES('ZoeChen'  10  27  172  'female'); ";

  currentTest(testSum, insertTable);

  /*rc = GNCDB_createTable(db, "someprimarykey", 5,
      "name", FIELDTYPE_VARCHAR, 0, 1, 0.0, 20.0,
      "id", FIELDTYPE_INTEGER, 0, 1, 0.0, 100.0,
      "age", FIELDTYPE_INTEGER, 0, 1, 0.0, 50.0,
      "high", FIELDTYPE_INTEGER, 0, 0, 0.0, 250.0,
      "gender", FIELDTYPE_VARCHAR, 0, 0, 0.0, 10.0,
      50);*/

  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "schema", "tableName=someprimarykey");
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO someprimarykey VALUES('ZoeChen', 10, 27, 172, 'female'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO someprimarykey VALUES('RyanLiu', 12, 35, 186, 'male'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO someprimarykey VALUES('LilyWang', 14, 19, 165, 'female'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO someprimarykey VALUES('DavidZhang', 16, 42, 178, 'male'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO someprimarykey VALUES('EmmaWu', 18, 31, 170, 'female'); ", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "someprimarykey", "ZoeChen", 10, 27, 172, "female");
  rc = GNCDB_insert(db, NULL, "someprimarykey", "RyanLiu", 12, 35, 186, "male");
  rc = GNCDB_insert(db, NULL, "someprimarykey", "LilyWang", 14, 19, 165, "female");
  rc = GNCDB_insert(db, NULL, "someprimarykey", "DavidZhang", 16, 42, 178, "male");
  rc = GNCDB_insert(db, NULL, "someprimarykey", "EmmaWu", 18, 31, 170, "female");
#endif
  for (i = 0; i < 5; ++i) {
    test_table_count.tableName = "someprimarykey";
    test_table_count.fieldName = somekeyvalue[i];
    test_table_count.rowNum    = 5;
    test_table_count.rowcount  = 0;
    test_table_count.testFlag  = false;
    sprintf(str, "rowId=%d", i);
    initFlag();

    GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "someprimarykey", str);
  }
  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_15(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.15:在全主键表插入记录";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *allkeyvalue[5][5] = {
      {"ZoeChen", "10", "27", "172", "female"},
      {"RyanLiu", "12", "35", "186", "male"},
      {"LilyWang", "14", "19", "165", "female"},
      {"DavidZhang", "16", "42", "178", "male"},
      {"EmmaWu", "18", "31", "170", "female"},
  };
  int  i               = 0;
  char str[100]        = {0};
  test_table_count.SQL = "INSERT INTO allprimarykey(name  id  age  high  gender)"
                         "VALUES('ZoeChen'  10  27  172  'female'); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO allprimarykey VALUES('ZoeChen', 10, 27, 172, 'female'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO allprimarykey VALUES('RyanLiu', 12, 35, 186, 'male'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO allprimarykey VALUES('LilyWang', 14, 19, 165, 'female'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO allprimarykey VALUES('DavidZhang', 16, 42, 178, 'male'); ", NULL, NULL, &errmsg);
  rc = GNCDB_exec(db, "INSERT INTO allprimarykey VALUES('EmmaWu', 18, 31, 170, 'female'); ", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "allprimarykey", "ZoeChen", 10, 27, 172, "female");
  rc = GNCDB_insert(db, NULL, "allprimarykey", "RyanLiu", 12, 35, 186, "male");
  rc = GNCDB_insert(db, NULL, "allprimarykey", "LilyWang", 14, 19, 165, "female");
  rc = GNCDB_insert(db, NULL, "allprimarykey", "DavidZhang", 16, 42, 178, "male");
  rc = GNCDB_insert(db, NULL, "allprimarykey", "EmmaWu", 18, 31, 170, "female");
#endif
  for (i = 0; i < 5; ++i) {
    test_table_count.tableName = "allprimarykey";
    test_table_count.fieldName = allkeyvalue[i];
    test_table_count.rowNum    = 5;
    test_table_count.rowcount  = 0;
    test_table_count.testFlag  = false;

    sprintf(str, "rowId=%d", i);
    initFlag();
    GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "allprimarykey", str);
  }
  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_16(GNCDB *db, TESTSUM *testSum)
{
  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char insertTable[35] = "3.16:在master表中插入记录";
  test_table_count.SQL = "INSERT INTO master(tableName  type  dependentTable  columnMaxNum  rootPageId)"
                         "VALUES('TESTTABLE'  0  'TESTTABLE'  5  100); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "INSERT INTO master"
      "(tableName, type, dependentTable, columnMaxNum, rootPageId)"
      "VALUES('TESTTABLE', 0, 'TESTTABLE', 5, 100); ",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "master", 0, "TESTTABLE", 0, "TESTTABLE", 5, 100, "TESTTABLE");
#endif
  if (rc == GNCDB_NOT_REFACTOR) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_17(GNCDB *db, TESTSUM *testSum)
{
  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char  insertTable[35] = "3.17:在schema表中插入记录";
  char *sql = "INSERT INTO schema(id columnName tableName columnIndex columnType canbeNull minValue maxValue "
              "isPrimaryKey) VALUES(300 'student_id' 'student' 0 1 0 0.0 100.0 1);";

  test_table_count.SQL = sql;

  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "schema", "tableName=someprimarykey");
  rc = GNCDB_exec(
      db, "INSERT INTO schema VALUES(300, 'student_id', 'student', 0, 1, 0, 0.0, 100.0, 1);", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "schema", 300, "student_id", "student", 0, 1, 0, 0.0, 100.0);
#endif
  if (rc == GNCDB_NOT_REFACTOR) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_18(GNCDB *db, TESTSUM *testSum)
{
  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *testvalue[3]   = {"张三", "175.000000", "53"};
  char  insertTable[]  = "3.18:在WPTTEST表中插入字符串为中文的记录";
  test_table_count.SQL = "INSERT INTO WPTTEST(WPT_ident  WPT_lon  WPT_lat)"
                         "VALUES(张三  175.0  53); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO WPTTEST VALUES('张三', 175.0, 53)", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "WPTTEST", "张三", 175.700000, 53);
#endif
  test_table_count.tableName = "WPTTEST";
  test_table_count.fieldName = testvalue;
  test_table_count.rowNum    = 3;
  test_table_count.rowcount  = 0;
  test_table_count.testFlag  = false;

  if (rc == GNCDB_SUCCESS) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_19(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.19:插入的表名不存在";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "INSERT INTO fdsdfsf(WPT_ident  WPT_lon  WPT_lat)"
                         "VALUES(1  lsi  22); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO fdsdfsf(WPT_ident, WPT_lon, WPT_lat) VALUES(1, 'lsi', 22)", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "fdsdfsf", 1, "lsi", 22);
#endif
  if (rc == GNCDB_TABLE_NOT_FOUND) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_20(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.20:WPTTEST表属性不能为空时插入空值";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "INSERT INTO WPTTEST(WPT_ident  WPT_lon  WPT_lat)"
                         "VALUES(jeck  NULL  30); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO WPTTEST VALUES('jeck', NULL, 30)", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "WPTTEST", "jeck", NULL, 30);
#endif
  if (rc == GNCDB_PARAM_INVALID || rc == -87) {
    addTest(testSum, 0);
  } else {
    addTest(testSum, rc);
  }

  return 0;
}

int test3_21(GNCDB *db, TESTSUM *testSum)
{
  char *nullvalue[4]  = {"1", "NULL", "30.000000", "abcde"};
  char  insertTable[] = "3.21:attrisnull表int属性值可以为空时插入空值";
  int   rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "INSERT INTO attrisnull(attr1  attr2  attr3  attr4)"
                         "VALUES(1  NULL  30.0  abcde); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO attrisnull VALUES(1, NULL, 30.0, 'abcde');", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "attrisnull", 1, NULL, 30.0, "abcde");
#endif

  test_table_count.tableName = "attrisnull";
  test_table_count.fieldName = nullvalue;
  test_table_count.rowNum    = 4;
  test_table_count.rowcount  = 0;
  test_table_count.testFlag  = false;

  GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "attrisnull", "attr1=1");

  if (rc == GNCDB_SUCCESS) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_22(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.22:表real属性值可以为空时插入空值";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *nullvalue[4]   = {"2", "10", "NULL", "abcde"};
  test_table_count.SQL = "INSERT INTO attrisnull(attr1 attr2 attr3 attr4)"
                         "VALUES(2 10 NULL abcde); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO attrisnull VALUES(2, 10, NULL, 'abcde');", NULL, NULL, &errmsg);
#else
  rc = -1;  // GNCDB_insert(db, NULL, "attrisnull", 2, 10, NULL, "abcde");
#endif
  test_table_count.tableName = "attrisnull";
  test_table_count.fieldName = nullvalue;
  test_table_count.rowNum    = 4;
  test_table_count.rowcount  = 0;
  test_table_count.testFlag  = false;

  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "attrisnull");
  GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "attrisnull", "attr1=2");

  if (rc == GNCDB_SUCCESS) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_23(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.23:str属性值可以为空时插入空值";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "INSERT INTO attrisnull(attr1  attr2  attr3  attr4)"
                         "VALUES(3  10  3.2  NULL); ";
  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO attrisnull VALUES(3, 10, 3.2, NULL);", NULL, NULL, &errmsg);
#else
  // rc = GNCDB_insert(db, NULL, "attrisnull", 3, 10, 3.2, NULL);
#endif
  /*char* nullvalue[4] = { "3", "10", "3.200000", "NULL" };

  test_table_count.tableName = "attrisnull";
  test_table_count.fieldName = nullvalue;
  test_table_count.rowNum = 4;
  test_table_count.rowcount = 0;
  test_table_count.testFlag = false;*/

  // GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "attrisnull", "attr1=3");
  // GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "attrisnull", "attr1=3");

  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "attrisnull");
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "attrisnull");

  if (rc == GNCDB_SUCCESS) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_24(GNCDB *db, TESTSUM *testSum)
{
  char insertTable[] = "3.24:在定长字符串的表中插入合理的字符串记录";
  int  rc            = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *strvalue[3]    = {"1", "aaaaaaaaaa", "10"};
  test_table_count.SQL = "INSERT INTO stringmaxmineq(attr1  attr2  attr3)"
                         "VALUES(1  aaaaaaaaaa  10); ";
  currentTest(testSum, insertTable);
  /*int rc = GNCDB_createTable(db, "stringmaxmineq", 3,
      "attr1", FIELDTYPE_INTEGER, 0, 1, 0.0, 100.0,
      "attr2", FIELDTYPE_VARCHAR, 0, 0, 10.0, 10.0,
      "attr3", FIELDTYPE_INTEGER, 0, 0, 0.0, 100.0,
      10);*/
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "INSERT INTO stringmaxmineq(attr1, attr2, attr3) VALUES(1, 'aaaaaaaaaa', 10);", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "stringmaxmineq", 1, "aaaaaaaaaa", 10);
#endif
  test_table_count.tableName = "stringmaxmineq";
  test_table_count.fieldName = strvalue;
  test_table_count.rowNum    = 3;
  test_table_count.rowcount  = 0;
  test_table_count.testFlag  = false;

  GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 0, "stringmaxmineq");

  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test3_25(GNCDB *db, TESTSUM *testSum)
{
  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char  insertTable[61] = "3.25:在定长字符串的表中插入短的字符串记录";
  char *sql             = "INSERT INTO stringmaxmineq(attr1 attr2 attr3) VALUES(1 'aaa' 10);";
  test_table_count.SQL  = sql;

  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "INSERT INTO stringmaxmineq(attr1, attr2, attr3) VALUES(1, 'aaa', 10);", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "stringmaxmineq", 1, "aaa", 10);
#endif
  if (rc == GNCDB_PARAM_INVALID || rc == GNCDB_INSERT_FAILED) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_26(GNCDB *db, TESTSUM *testSum)
{
  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char insertTable[61] = "3.26:在定长字符串的表中插入长的字符串记录";
  test_table_count.SQL = "INSERT INTO stringmaxmineq(attr1 attr2 attr3) VALUES(1 'aaaqqqqqqqqqq' 10);";

  currentTest(testSum, insertTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "INSERT INTO stringmaxmineq(attr1, attr2, attr3) VALUES(1, 'aaaqqqqqqqqqq', 10);", NULL, NULL, &errmsg);
#else
  rc = GNCDB_insert(db, NULL, "stringmaxmineq", 1, "aaaqqqqqqqqqq", 10);
#endif
  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test3_27(GNCDB *db, TESTSUM *testSum)
{
  int  rc              = -1;
  char insertTable[52] = "3.27:在可为空定长字符串插入空字符串";
  test_table_count.SQL = "INSERT INTO stringcannull(attr1 attr2 attr3) VALUES(1 NULL 10);";

  currentTest(testSum, insertTable);

  rc = GNCDB_createTable(db,
      "stringcannull",
      3,
      "attr1",
      FIELDTYPE_INTEGER,
      0,
      1,
      0.0,
      100.0,
      "attr2",
      FIELDTYPE_VARCHAR,
      1,
      0,
      10.0,
      10.0,
      "attr3",
      FIELDTYPE_INTEGER,
      0,
      0,
      0.0,
      100.0,
      10);

  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
  }

  // rc = GNCDB_insert(db, NULL, "stringcannull", 1, NULL, 10);

  /*char* strvalue[3] = { "1", "0", "10" };
  test_table_count.tableName = "stringcannull";
  test_table_count.fieldName = strvalue;
  test_table_count.rowNum = 3;
  test_table_count.rowcount = 0;
  test_table_count.testFlag = false;
  GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "stringcannull");*/
  GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "stringcannull");

  rc = -1;

  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int testInsertTable(GNCDB *db, TESTSUM *testSum)
{

  test_table_count.testProject = "3:插入测试";
  test3_1(db, testSum);
  test3_2(db, testSum);
  test3_3(db, testSum);
  test3_4(db, testSum);
  test3_6(db, testSum);
  test3_7(db, testSum);
  test3_8(db, testSum);
  test3_9(db, testSum);
  test3_10(db, testSum);
  test3_11(db, testSum);
  test3_12(db, testSum);
  test3_13(db, testSum);
  test3_14(db, testSum);
  test3_15(db, testSum);
  test3_16(db, testSum);
  test3_17(db, testSum);
  test3_18(db, testSum);
  test3_19(db, testSum);
  test3_20(db, testSum);
  test3_21(db, testSum);
  test3_22(db, testSum);
  test3_23(db, testSum);
  test3_24(db, testSum);
  test3_25(db, testSum);
  test3_26(db, testSum);
  test3_27(db, testSum);

  return 0;
}

bool compare_files(const char *filename1, const char *filename2)
{
  FILE *file1  = NULL;
  FILE *file2  = NULL;
  long  size1  = 0;
  long  size2  = 0;
  char *buf1   = NULL;
  char *buf2   = NULL;
  bool  result = false;

  file1 = fopen(filename1, "rb");
  if (!file1) {
    perror(filename1);
    return false;
  }

  file2 = fopen(filename2, "rb");
  if (!file2) {
    perror(filename2);
    fclose(file1);
    return false;
  }

  /* 获取文件大小 */
  fseek(file1, 0, SEEK_END);
  size1 = ftell(file1);
  fseek(file1, 0, SEEK_SET);

  fseek(file2, 0, SEEK_END);
  size2 = ftell(file2);
  fseek(file2, 0, SEEK_SET);

  if (size1 != size2) {
    fclose(file1);
    fclose(file2);
    return false;
  }

  /* 读取整个文件内容 */
  buf1 = my_malloc(size1);
  buf2 = my_malloc(size2);
  if (!buf1 || !buf2) {
    perror("my_malloc");
    fclose(file1);
    fclose(file2);
    return false;
  }

  fread(buf1, 1, size1, file1);
  fread(buf2, 1, size2, file2);

  /* 比较文件内容 */
  result = memcmp(buf1, buf2, size1) == 0;

  /* 释放内存和关闭文件 */
  my_free(buf1);
  my_free(buf2);
  fclose(file1);
  fclose(file2);

  return result;
}

int test4_1_2(GNCDB *db, TESTSUM *testSum)
{
  int    rc             = 0;
  char   blobTable1[25] = "4.1:插入图片文件";
  char   filename1[]    = "blob.png";
  char  *path1          = NULL;
  FILE  *fp             = NULL;
  size_t fileLen        = 0;
  BYTE  *buffer         = NULL;
  char  *errorMessage   = "File size exceeds limit\n";

  char  blobTable2[25] = "4.2:拿取图片文件";
  BYTE *bufferCopy     = NULL;
  char  filename2[]    = "test4_2blob.png";
  char *path2          = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.png WHERE WPT_ident=BTZCE";
  currentTest(testSum, blobTable1);
  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  if (fileLen > BLOBFILESIZE) {
    my_free(path1);
    printf("%s", errorMessage);
    return -1;
  }

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "BTZCE");
  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "BHYLY");

  addTest(testSum, rc);

  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=BTZCE";
  currentTest(testSum, blobTable2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "BTZCE");

  path2 = strJoin(resultBlobPath, filename2);

  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }

  fp = fopen(path2, "wb");

  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }

  fwrite(bufferCopy, fileLen, 1, fp);
  fclose(fp);

  my_free(buffer);

  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path1);
  my_free(path2);
  my_free(bufferCopy);

  return 0;
}

int test4_3_4(GNCDB *db, TESTSUM *testSum)
{
  int    rc             = 0;
  char   blobTable1[25] = "4.3:插入文本文件";
  char   filename1[]    = "blob.txt";
  char  *path1          = NULL;
  FILE  *fp             = NULL;
  size_t fileLen        = 0;
  BYTE  *buffer         = NULL;

  char  blobTable2[25] = "4.4:拿取文本文件";
  BYTE *bufferCopy     = NULL;
  char  filename2[]    = "test4_4blob.txt";
  char *path2          = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=APJFO";
  currentTest(testSum, blobTable1);

  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "APJFO");
  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "NLCXR");
  addTest(testSum, rc);

  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=APJFO";
  currentTest(testSum, blobTable2);
  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "APJFO");

  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }

  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }

  fwrite(bufferCopy, fileLen, 1, fp);
  fclose(fp);

  my_free(buffer);
  my_free(bufferCopy);

  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path1);
  my_free(path2);

  return 0;
}

int test4_5_6(GNCDB *db, TESTSUM *testSum)
{
  int    rc             = 0;
  char   blobTable1[22] = "4.5:插入mp3文件";
  char   filename1[]    = "blob.mp3";
  char  *path1          = NULL;
  FILE  *fp             = NULL;
  size_t fileLen        = 0;
  BYTE  *buffer         = NULL;

  char  blobTable2[22] = "4.6:拿取mp3文件";
  BYTE *bufferCopy     = NULL;
  char  filename2[]    = "test4_6blob.mp3";
  char *path2          = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.mp3 WHERE WPT_ident=AFTTM";
  currentTest(testSum, blobTable1);
  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "AFTTM");
  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "GIERW");
  addTest(testSum, rc);
  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=AFTTM";
  currentTest(testSum, blobTable2);
  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "AFTTM");

  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }

  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }

  fwrite(bufferCopy, fileLen, 1, fp);
  fclose(fp);

  my_free(buffer);
  my_free(bufferCopy);

  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path1);
  my_free(path2);

  return 0;
}

int test4_7_8(GNCDB *db, TESTSUM *testSum)
{
  int    rc             = 0;
  char   blobTable1[22] = "4.7:插入mp4文件";
  char   filename1[]    = "blob.mp4";
  char  *path1          = NULL;
  FILE  *fp             = NULL;
  size_t fileLen        = 0;
  BYTE  *buffer         = NULL;

  char  blobTable2[22] = "4.8:拿取mp4文件";
  BYTE *bufferCopy     = NULL;
  char  filename2[]    = "test4_8blob.mp4";
  char *path2          = NULL;

  path1                = strJoin(testBlobPath, filename1);
  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.mp4 WHERE WPT_ident=DZECO";
  currentTest(testSum, blobTable1);

  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "DZECO");
  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "JABRE");
  addTest(testSum, rc);

  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=DZECO";
  currentTest(testSum, blobTable2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "DZECO");

  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    return -1;
  }

  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }

  fwrite(bufferCopy, fileLen, 1, fp);

  fclose(fp);
  my_free(buffer);
  my_free(bufferCopy);

  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path1);
  my_free(path2);

  return 0;
}

int test4_9(GNCDB *db, TESTSUM *testSum)
{
  int    rc            = 0;
  char   blobTable[46] = "4.9:在已有BLOB的记录中插入文件";
  char   filename1[]   = "blob.txt";
  char  *path1         = NULL;
  FILE  *fp            = NULL;
  size_t fileLen       = 0;
  BYTE  *buffer        = NULL;

  path1                = strJoin(testBlobPath, filename1);
  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=BHYLY";
  currentTest(testSum, blobTable);

  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }

  fread(buffer, fileLen, 1, fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "BHYLY");
  if (rc == GNCDB_BLOB_EXIST) {
    addTest(testSum, 0);
  } else {
    addTest(testSum, rc);
  }

  fclose(fp);
  my_free(buffer);
  my_free(path1);

  return 0;
}

int test4_10(GNCDB *db, TESTSUM *testSum)
{
  int    rc            = 0;
  char   blobTable[45] = "4.10:在非blob类型数据中插入文件";
  char   filename[]    = "blob.mp4";
  char  *path          = NULL;
  FILE  *fp            = NULL;
  size_t fileLen       = 0;
  BYTE  *buffer        = NULL;

  path                 = strJoin(testBlobPath, filename);
  test_table_count.SQL = "UPDATE WPT SET WPT_lat=blob.mp4 WHERE WPT_ident=BHYLY";
  currentTest(testSum, blobTable);

  if (path == NULL) {
    return -1;
  }

  fp = fopen(path, "rb");
  if (fp == NULL) {
    my_free(path);
    addTest(testSum, -111);
    return -1;
  }

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path);
    addTest(testSum, -111);
    return -1;
  }

  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 2, buffer, fileLen, 1, "GIERW");

  if (rc == GNCDB_PARAM_INVALID) {
    addTest(testSum, 0);
  } else {
    addTest(testSum, rc);
  }

  my_free(buffer);
  my_free(path);

  return 0;
}

int test4_11_12(GNCDB *db, TESTSUM *testSum)
{
  char   blobTable1[32] = "4.11:插入较大的文件";
  char   blobTable2[32] = "4.12:拿取较大的文件";
  char   filename1[]    = "bigblob.mov";
  char  *path1          = NULL;
  FILE  *fp             = NULL;
  size_t fileLen        = 0;
  BYTE  *buffer         = NULL;
  int    rc             = 0;
  BYTE  *bufferCopy     = NULL;
  char   filename2[]    = "testbigblob.mov";
  char  *path2          = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_lat=blob.mov WHERE WPT_ident=LLIFH";
  currentTest(testSum, blobTable1);

  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "LLIFH");

  addTest(testSum, rc);

  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=LLIFH";

  currentTest(testSum, blobTable2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "LLIFH");
  // 打开文件，准备写入数据

  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }
  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }

  // 写入数据到文件中
  fwrite(bufferCopy, fileLen, 1, fp);

  // 关闭文件
  fclose(fp);
  // 释放内存
  my_free(buffer);
  my_free(bufferCopy);
  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path1);
  my_free(path2);

  return 0;
}

char blob_key[16] = {0};

int test4_13_14(GNCDB *db, TESTSUM *testSum)
{
  char  *path           = strJoin(testFilePath, testFileName1);
  char   blobTable1[]   = "4.13:对WPTBLOB表插入多个BLOB文件";
  char   blobTable2[32] = "4.14:拿取多个文件";
  double min            = -10000.0;
  double max            = 10000.0;
  int    rc             = 0;
  size_t fileLen        = 0;
  BYTE  *bufferCopy     = NULL;
  FILE  *fp             = NULL;
  char   filename1[]    = "blob.txt";
  char  *path1          = NULL;
  BYTE  *buffer         = NULL;
  FILE  *fileData       = NULL;
  BYTE  *ImgBuffer      = NULL;
  int    i              = 0;
  char   str[100]       = {0};
  char   blobname[128]  = {0};

  test_table_count.SQL = "UPDATE WPTBLOB SET WPT_lat=blob.txt WHERE WPT_ident";
  currentTest(testSum, blobTable1);

  rc = GNCDB_createTable(db,
      "WPTBLOB",
      4,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_blob",
      FIELDTYPE_BLOB,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    my_free(path);
    return -1;
  }

  fileData = fopen(path, "r");
  my_free(path);
  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    my_free(path);
    return -1;
  }

  for (i = 0; i < WPTBLOBROWS; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);

    rc = GNCDB_insert(db, NULL, "WPTBLOB", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat, 0, ImgBuffer);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      my_free(path);
      my_free(path1);
      return -1;
    }
    fp = fopen(path1, "rb");
    if (fp == NULL) {
      addTest(testSum, -111);
      my_free(path);
      my_free(path1);
      return -1;
    }
    fseek(fp, 0, SEEK_END);
    fileLen = ftell(fp);
    rewind(fp);
    buffer = (BYTE *)my_malloc(fileLen);
    if (buffer == NULL) {
      addTest(testSum, -111);
      my_free(path);
      my_free(path1);
      return -1;
    }
    fread(buffer, fileLen, 1, fp);
    fclose(fp);
    rc = GNCDB_setBlob(db, "WPTBLOB", 3, buffer, fileLen, 1, wpt.sc8_wpt_ident);
    my_free(buffer);
  }
  fclose(fileData);

  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  test_table_count.SQL = "SELECT WPT_blob FROM WPTBLOB WHERE WPT_ident";
  currentTest(testSum, blobTable2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    addTest(testSum, -111);
    my_free(path);
    my_free(path1);
    return -1;
  }

  for (i = 0; i < WPTBLOBROWS; ++i) {
    sprintf(str, "rowId=%d", i);
    GNCDB_select(db, testBlobCallBack, NULL, NULL, 1, 0, 1, "WPTBLOB", str);
    GNCDB_select(db, testBlobCallBack, NULL, NULL, 1, 0, 1, "WPTBLOB", str);
    memset(bufferCopy, 0, fileLen);
    rc = GNCDB_getBlob(db, "WPTBLOB", 3, bufferCopy, fileLen, 1, blob_key);

    sprintf(blobname, "%sblob_4_14/testblob%d.txt", resultBlobPath, i);
    fp = fopen(blobname, "wb");
    if (fp == NULL) {
      // my_free(path);
      // my_free(path1);
      return -1;
    }
    fwrite(bufferCopy, fileLen, 1, fp);
    fclose(fp);
    if (rc == GNCDB_SUCCESS && compare_files(path1, blobname)) {
    } else {
      if (rc == GNCDB_SUCCESS) {
        rc = -1;
      }
    }
  }

  my_free(bufferCopy);
  my_free(path1);
  addTest(testSum, rc);
  my_free(path);
  my_free(path1);
  return 0;
}

int test4_15(GNCDB *db, TESTSUM *testSum)
{
  char blobTable[47]   = "4.15:在已有数据的记录中插入空值";
  int  rc              = 0;
  test_table_count.SQL = "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=DZECO";

  currentTest(testSum, blobTable);

  rc = GNCDB_setBlob(db, "WPT", 3, NULL, 0, 1, "BTZCE");
  if (rc == -3) {
    addTest(testSum, 0);
  } else {
    addTest(testSum, rc);
  }

  return 0;
}

int test4_16(GNCDB *db, TESTSUM *testSum)
{
  char  blobTable[]  = "4.16:重复拿取txt文件";
  char  fileName[64] = {0};
  int   rc           = 0;
  int   i            = 0;
  char *path         = NULL;
  BYTE *bufferCopy   = NULL;
  FILE *fp           = NULL;
  char  fileName1[]  = "blob.txt";
  char *pathRoot     = NULL;

  currentTest(testSum, blobTable);
  for (i = 0; i < 10; ++i) {
    sprintf(fileName, "test4_16/blob4_16_%d.txt", i);
    path = strJoin(resultBlobPath, fileName);
    if (path == NULL) {
      return -1;
    }
    test_table_count.rowcount = 0;
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=APJFO");
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=APJFO");
    bufferCopy = (BYTE *)my_malloc(test_table_count.rowcount);
    if (bufferCopy == NULL) {
      my_free(path);
      addTest(testSum, -111);
      return -1;
    }
    rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, test_table_count.rowcount, 1, "APJFO");
    // 打开文件，准备写入数据
    fp = fopen(path, "wb");
    if (fp == NULL) {
      my_free(path);
      return -1;
    }

    // 写入数据到文件中
    fwrite(bufferCopy, test_table_count.rowcount, 1, fp);

    // 关闭文件
    fclose(fp);
    pathRoot = strJoin(testBlobPath, fileName1);
    if (rc == GNCDB_SUCCESS && compare_files(pathRoot, path)) {
      // addTest(testSum, rc);
    } else {
      if (rc == GNCDB_SUCCESS) {
        rc = -1;
      }
      addTest(testSum, rc);
      my_free(bufferCopy);

      my_free(path);
      my_free(pathRoot);
      return -1;
    }
    // 释放内存
    my_free(bufferCopy);

    my_free(path);
    my_free(pathRoot);
  }
  addTest(testSum, rc);
  return 0;
}

int test4_17(GNCDB *db, TESTSUM *testSum)
{
  char  blobTable[]  = "4.17:重复拿取图片文件";
  char  fileName[64] = {0};
  int   rc           = 0;
  int   i            = 0;
  char *path         = NULL;
  BYTE *bufferCopy   = NULL;
  FILE *fp           = NULL;
  char  fileName1[]  = "blob.png";
  char *pathRoot     = NULL;

  currentTest(testSum, blobTable);

  for (i = 0; i < 10; ++i) {
    sprintf(fileName, "test4_17/blob4_17_%d.png", i);
    path = strJoin(resultBlobPath, fileName);
    if (path == NULL) {
      return -1;
    }
    test_table_count.rowcount = 0;
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BTZCE");
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BTZCE");
    bufferCopy = (BYTE *)my_malloc(test_table_count.rowcount);
    if (bufferCopy == NULL) {
      my_free(path);
      addTest(testSum, -111);
      return -1;
    }
    rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, test_table_count.rowcount, 1, "BTZCE");
    // 打开文件，准备写入数据
    fp = fopen(path, "wb");
    if (fp == NULL) {
      my_free(path);
      return -1;
    }

    // 写入数据到文件中
    fwrite(bufferCopy, test_table_count.rowcount, 1, fp);

    // 关闭文件
    fclose(fp);
    pathRoot = strJoin(testBlobPath, fileName1);
    if (rc == GNCDB_SUCCESS && compare_files(pathRoot, path)) {
      // addTest(testSum, rc);
    } else {
      if (rc == GNCDB_SUCCESS) {
        rc = -1;
      }
      addTest(testSum, rc);
      my_free(bufferCopy);

      my_free(path);
      my_free(pathRoot);
      return -1;
    }
    // 释放内存
    my_free(bufferCopy);

    my_free(path);
    my_free(pathRoot);
  }
  addTest(testSum, rc);

  return 0;
}

int test4_18(GNCDB *db, TESTSUM *testSum)
{
  char blobTable[] = "4.18:重复拿取mp3文件";
  currentTest(testSum, blobTable);

  return 0;
}

int test4_19(GNCDB *db, TESTSUM *testSum)
{
  char blobTable[] = "4.19:重复拿取mp4文件";
  currentTest(testSum, blobTable);

  return 0;
}

int test4_20(GNCDB *db, TESTSUM *testSum)
{
  char blobTable[]     = "4.20:删除图片文件";
  int  rc              = 0;
  test_table_count.SQL = "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=BHYLY";
  currentTest(testSum, blobTable);
  rc                        = GNCDB_deleteBlob(db, "WPT", 3, 1, "BHYLY");
  test_table_count.rowcount = -1;
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BHYLY");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, rc);
  } else {
    if (rc == 0) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test4_21(GNCDB *db, TESTSUM *testSum)
{
  char blobTable[] = "4.21:删除txt文件";
  int  rc          = 0;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=NLCXR";
  currentTest(testSum, blobTable);
  rc                        = GNCDB_deleteBlob(db, "WPT", 3, 1, "NLCXR");
  test_table_count.rowcount = -1;
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=NLCXR");
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=NLCXR");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, rc);
  } else {
    if (rc == 0) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test4_22(GNCDB *db, TESTSUM *testSum)
{
  char blobTable[]     = "4.22:删除mp3文件";
  int  rc              = 0;
  test_table_count.SQL = "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=GIERW";
  currentTest(testSum, blobTable);

  rc                        = GNCDB_deleteBlob(db, "WPT", 3, 1, "GIERW");
  test_table_count.rowcount = -1;
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=GIERW");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, rc);
  } else {
    if (rc == 0) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test4_23(GNCDB *db, TESTSUM *testSum)
{
  char blobTable[] = "4.23:删除mp4文件";
  int  rc          = 0;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=NULL WHERE WPT_ident=JABRE";
  currentTest(testSum, blobTable);

  rc                        = GNCDB_deleteBlob(db, "WPT", 3, 1, "JABRE");
  test_table_count.rowcount = -1;
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=JABRE");
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=JABRE");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, rc);
  } else {
    if (rc == 0) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test4_24(GNCDB *db, TESTSUM *testSum)
{
  char   blobTable1[] = "4.24:删除BLOB之后再插入mp3文件";
  char   filename1[]  = "blob.mp3";
  char  *path1        = NULL;
  FILE  *fp           = NULL;
  size_t fileLen      = 0;
  BYTE  *buffer       = NULL;
  int    rc           = 0;
  BYTE  *bufferCopy   = NULL;
  char   filename2[]  = "test4_24blob.mp3";
  char  *path2        = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.mp3 WHERE WPT_ident=JABRE";
  currentTest(testSum, blobTable1);

  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc         = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "JABRE");
  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "JABRE");
  // 打开文件，准备写入数据
  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }
  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }

  // 写入数据到文件中
  fwrite(bufferCopy, fileLen, 1, fp);

  // 关闭文件
  fclose(fp);
  // 释放内存
  my_free(buffer);
  my_free(bufferCopy);
  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path1);
  my_free(path2);

  return 0;
}

int testBlobTable(GNCDB *db, TESTSUM *testSum)
{
    test_table_count.testProject = "4:BLOB测试";
    test4_1_2(db, testSum);
    test4_3_4(db, testSum);
    test4_5_6(db, testSum);
    test4_7_8(db, testSum);
    test4_9(db, testSum);
    test4_10(db, testSum);
    test4_11_12(db, testSum);
    test4_13_14(db, testSum);
    test4_15(db, testSum);
    test4_20(db, testSum);
    test4_21(db, testSum);
    test4_22(db, testSum);
    test4_23(db, testSum);
    test4_24(db, testSum);

    return 0;
}

int test5_1(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* wptvalue[3] = { "DZECO", "-180.000000", "-67.500000"};
    int rc = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
    int rows = 0;
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident=DZECO";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = wptvalue;
    test_table_count.rowNum = 1;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;

    // initFlag();
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident='DZECO'", testCallBackInsertTable, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackInsertTable, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
#endif

    if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_2(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* wptvalue[1] = { "FXXZR" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<FXXZR";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = wptvalue;
    test_table_count.rowNum = 0;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'FXXZR'", testCallBackSelectTableLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableLess, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident<FXXZR");
#endif

    if (rc == GNCDB_SUCCESS && test_table_count.rowcount == rows && test_table_count.testFlag) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_3(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* wptvalue[1] = { "FXXZR" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<=FXXZR";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = wptvalue;
    test_table_count.rowNum = 0;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<='FXXZR'", testCallBackSelectTableLessOrEqual, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableLessOrEqual, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident<=FXXZR");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.rowcount == rows && test_table_count.testFlag) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_4(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* wptvalue[1] = { "JABRE" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>JABRE";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = wptvalue;
    test_table_count.rowNum = 0;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>'JABRE'", testCallBackSelectTableGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableGreater, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident>JABRE");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.rowcount == rows && test_table_count.testFlag) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_5(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* wptvalue[1] = { "JABRE" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>=JABRE";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = wptvalue;
    test_table_count.rowNum = 0;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>='JABRE'", testCallBackSelectTableGreaterOrEqual, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableGreaterOrEqual, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident>=JABRE");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.rowcount == rows && test_table_count.testFlag) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_6(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident=cndjk";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident='cndjk'", testCallBack, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident=cndjk");
#endif
    if (rc == GNCDB_SUCCESS && rows == 0) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_7(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_id=ALRGV";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_id='ALRGV'", testCallBack, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack, &rows, NULL, 1, 0, 1, "WPT", "WPT_id=ALRGV");
#endif
    if ((rc == GNCDB_CONDITION_INVALID || rc == GNCDB_FIELD_NOT_EXIST) && rows == 0) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_8(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "ECYBG", "22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
    int rows = 0;
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident=ECYBG  ARPT_lat=22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident='ECYBG' AND ARPT_lat=22.500000", testCallBackSelectUnionEquals, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionEquals, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_ident=ECYBG", "ARPT_lat=22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_9(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident=ECYBG  ARPT_lat=67.500000";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident='ECYBG' AND ARPT_lat=67.500000", testCallBackSelectUnionEquals, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionEquals, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_ident=ECYBG", "ARPT_lat=67.500000");
#endif
    if (rc == GNCDB_SUCCESS && rows == 0) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_10(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[1] = { "90.000000" };
    int rc = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
    int rows = 0;
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_lon=90.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 1;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_lon=90.000000", testCallBackSelectTableEqual, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableEqual, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_lon=90.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_11(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[1] = { "0.000000" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    CallBack2 call = testCallBackSelectTableLess;

    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_lon<0.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 1;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_lon<0.000000", call, &rows, &errmsg);
#else  
    rc = GNCDB_select(db, call, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_lon<0.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_12(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[1] = { "-45.000000" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_lat<=-45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_lat<=-45.000000", testCallBackSelectTableFloatLessOrEqual, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableFloatLessOrEqual, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_lat<=-45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_13(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[1] = { "45.000000" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_lat>45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_lat>45.000000", testCallBackSelectTableFloatGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableFloatGreater, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_lat>45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_14(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[1] = { "45.000000" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_lat>=45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_lat>=45.000000", testCallBackSelectTableFloatGreaterOrEqual, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectTableFloatGreaterOrEqual, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_lat>=45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_15(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "DZECO", "-22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<DZECO  WPT_lat<-22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'DZECO' AND WPT_lat<-22.500000", testCallBackSelectUnionLessLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessLess, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<DZECO", "WPT_lat<-22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_16(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SWZXH", "0.000000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<SWZXH  WPT_lat<=0.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'SWZXH' AND WPT_lat<=0.000000", testCallBackSelectUnionLessLessEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessLessEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<SWZXH", "WPT_lat<=0.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_17(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SWZXH", "45.000000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<SWZXH  WPT_lat>45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'SWZXH' AND WPT_lat>45.000000", testCallBackSelectUnionLessGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessGreater, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<SWZXH", "WPT_lat>45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_18(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SWZXH", "45.000000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<SWZXH  WPT_lat>=45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'SWZXH' AND WPT_lat>=45.000000", testCallBackSelectUnionLessGreaterEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessGreaterEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<SWZXH", "WPT_lat>=45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_19(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "EBTKZ", "0.000000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<=EBTKZ  WPT_lat<0.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<='EBTKZ' AND WPT_lat<0.000000", testCallBackSelectUnionLessEqualsLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessEqualsLess, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<=EBTKZ", "WPT_lat<0.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_20(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "EBTKZ", "22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<=EBTKZ  WPT_lat<=22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<='EBTKZ' AND WPT_lat<=22.500000", testCallBackSelectUnionLessEqualsLessEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessEqualsLessEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<=EBTKZ", "WPT_lat<=22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_21(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SCSZJ", "45.000000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<=SCSZJ  WPT_lat>45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<='SCSZJ' AND WPT_lat>45.000000", testCallBackSelectUnionLessEqualsGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessEqualsGreater, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<=SCSZJ", "WPT_lat>45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_22(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SCSZJ", "45.000000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<=SCSZJ  WPT_lat>=45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<='SCSZJ' AND WPT_lat>=45.000000", testCallBackSelectUnionLessEqualsGreaterEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionLessEqualsGreaterEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<=SCSZJ", "WPT_lat>=45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_23(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SCSZJ", "-22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>SCSZJ  WPT_lat<-22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>'SCSZJ' AND WPT_lat<-22.500000", testCallBackSelectUnionGreaterLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterLess, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>SCSZJ", "WPT_lat<-22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_24(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SCSZJ", "-22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>SCSZJ  WPT_lat<=-22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>'SCSZJ' AND WPT_lat<=-22.500000", testCallBackSelectUnionGreaterLessEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterLessEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>SCSZJ", "WPT_lat<=-22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_25(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SCSZJ", "22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>SCSZJ  WPT_lat>22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>'SCSZJ' AND WPT_lat>22.500000", testCallBackSelectUnionGreaterGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterGreater, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>SCSZJ", "WPT_lat>22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_26(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "SCSZJ", "22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>SCSZJ  WPT_lat>=22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;

#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>'SCSZJ' AND WPT_lat>=22.500000", testCallBackSelectUnionGreaterGreaterEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterGreaterEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>SCSZJ", "WPT_lat>=22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_27(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "JIREO", "22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>=JIREO  WPT_lat<22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>='JIREO' AND WPT_lat<22.500000", testCallBackSelectUnionGreaterEqualsLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterEqualsLess, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>=JIREO", "WPT_lat<22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_28(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "JIREO", "22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>=JIREO  WPT_lat<22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>='JIREO' AND WPT_lat<=22.500000", testCallBackSelectUnionGreaterEqualsLessEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterEqualsLessEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>=JIREO", "WPT_lat<=22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_29(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "JIREO", "-22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>=JIREO  WPT_lat>-22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>='JIREO' AND WPT_lat<=-22.500000", testCallBackSelectUnionGreaterEqualsLessEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterEqualsGreater, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>=JIREO", "WPT_lat>-22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_30(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "JIREO", "-22.500000" };
    int index[2] = { 0, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>=JIREO  WPT_lat>=-22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>='JIREO' AND WPT_lat>=-22.500000", testCallBackSelectUnionGreaterEqualsGreaterEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectUnionGreaterEqualsGreaterEquals, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>=JIREO", "WPT_lat>=-22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_31(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<AAAAA  WPT_lat>=-22.500000";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'AAAAA' AND WPT_lat>=-22.500000", testCallBackSelectUnionLessEqualsGreaterEquals, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident<AAAAA", "WPT_lat>=-22.500000");
#endif
    if (rc == GNCDB_SUCCESS && rows == 0) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_32(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident>=JIREO  WPT_lat>=222.500000";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident>='JIREO' AND WPT_lat>=222.500000", testCallBackSelectUnionGreaterEqualsGreaterEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack, &rows, NULL, 1, 0, 2, "WPT", "WPT_ident>=JIREO", "WPT_lat>=222.500000");
#endif
    if (rc == GNCDB_SUCCESS && rows == 0) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_33(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
    int rows = 0;
#endif
    test_table_count.SQL = "SELECT * FROM WPTAAQA";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPTAAQA", testCallBack, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack, &rows, NULL, 1, 0, 0, "WPTAAQA");
#endif
    if (rc == GNCDB_TABLE_NOT_FOUND) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_34(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "JIREO", "0.000000", "-22.500000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<JIREO WPT_lon<0.000000 WPT_lat<-22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'JIREO' AND WPT_lon<0.000000 AND WPT_lat<-22.500000", testCallBackUnionLessLessLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionLessLessLess, &rows, NULL, 1, 0, 3, "WPT", "WPT_ident<JIREO", "WPT_lon<0.000000", "WPT_lat<-22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_35(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "JIREO", "0.000000", "22.500000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<JIREO WPT_lon<0.000000 WPT_lat>22.500000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'JIREO' AND WPT_lon<0.000000 AND WPT_lat>22.500000", testCallBackUnionLessLessGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionLessLessGreater, &rows, NULL, 1, 0, 3, "WPT", "WPT_ident<JIREO", "WPT_lon<0.000000", "WPT_lat>22.500000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_36(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "JIREO", "45.000000", "0.000000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident<JIREO WPT_lon>45.000000 WPT_lat<0.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident<'JIREO' AND WPT_lon>45.000000 AND WPT_lat<0.000000", testCallBackUnionLessGreaterLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionLessGreaterLess, &rows, NULL, 1, 0, 3, "WPT", "WPT_ident<JIREO", "WPT_lon>45.000000", "WPT_lat<0.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_37(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "GTEWZ", "45.000000", "45.000000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident<GTEWZ ARPT_lon>45.000000 ARPT_lat>45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident<'GTEWZ' AND ARPT_lon>45.000000 AND ARPT_lat>45.000000", testCallBackUnionLessGreaterGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionLessGreaterGreater, &rows, NULL, 1, 0, 3, "ARPT", "ARPT_ident<GTEWZ", "ARPT_lon>45.000000", "ARPT_lat>45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_38(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "KUUAX", "25.000000", "25.000000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident>KUUAX ARPT_lon<25.000000 ARPT_lat<25.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident>'KUUAX' AND ARPT_lon<25.000000 AND ARPT_lat<25.000000", testCallBackUnionGreaterLessLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionGreaterLessLess, &rows, NULL, 1, 0, 3, "ARPT", "ARPT_ident>KUUAX", "ARPT_lon<25.000000", "ARPT_lat<25.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_39(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "KUUAX", "25.000000", "45.000000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident>KUUAX ARPT_lon<25.000000 ARPT_lat>45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident>'KUUAX' AND ARPT_lon<25.000000 AND ARPT_lat>45.000000", testCallBackUnionGreaterLessGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionGreaterLessGreater, &rows, NULL, 1, 0, 3, "ARPT", "ARPT_ident>KUUAX", "ARPT_lon<25.000000", "ARPT_lat>45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_40(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "KUUAX", "25.000000", "45.000000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident>KUUAX ARPT_lon<25.000000 ARPT_lat<45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident>'KUUAX' AND ARPT_lon<25.000000 AND ARPT_lat<45.000000", testCallBackUnionGreaterLessLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionGreaterGreaterLess, &rows, NULL, 1, 0, 3, "ARPT", "ARPT_ident>KUUAX", "ARPT_lon>25.000000", "ARPT_lat<45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_41(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[3] = { "KUUAX", "25.000000", "45.000000" };
    int index[3] = { 0, 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident>KUUAX ARPT_lon>25.000000 ARPT_lat>45.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE 
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident>'KUUAX' AND ARPT_lon>25.000000 AND ARPT_lat>45.000000", testCallBackUnionGreaterGreaterGreater, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionGreaterGreaterGreater, &rows, NULL, 1, 0, 3, "ARPT", "ARPT_ident>KUUAX", "ARPT_lon>25.000000", "ARPT_lat>45.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_42(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "45.0", "0.0" };
    int index[2] = { 1, 2 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_lon<=45 WPT_lon>=0 WPT_lat<=45 WPT_lat>=0";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_lon<=45 AND WPT_lon>=0 AND WPT_lat<=45 AND WPT_lat>=0", testCallBackUnionfourUnionCondition, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionfourUnionCondition, &rows, NULL, 1, 0, 4, "WPT", "WPT_lon<=45", "WPT_lon>=0", "WPT_lat<=45", "WPT_lat>=0");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_43(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[4] = { "KUUAX", "25.000000", "45.000000", "3968.782678"};
    int index[4] = { 0, 1, 2, 4 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident<KUUAX ARPT_lon>25.000000 ARPT_lat<=45.000000 ARPT_length>=3968.782678";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident<'KUUAX' AND ARPT_lon>25.000000 AND ARPT_lat<=45.000000 AND ARPT_length>=3968.782678", testCallBackUnionfourCondition, &rows, &errmsg);
#else   
    rc = GNCDB_select(db, testCallBackUnionfourCondition, &rows, NULL, 1, 0, 4, "ARPT", "ARPT_ident<KUUAX", "ARPT_lon>25.000000", "ARPT_lat<=45.000000", "ARPT_length>=3968.782678");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_44(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[4] = { "KUUAX", "3968.782678", "45.000000", "25.000000" };
    int index[4] = { 0, 1, 2, 4 };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM ARPT WHERE ARPT_ident<KUUAX ARPT_length>=3968.782678 ARPT_lat<=45.000000 ARPT_lon>25.000000";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.index = index;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM ARPT WHERE ARPT_ident<'KUUAX' AND ARPT_length>=3968.782678 AND ARPT_lat<=45.000000 AND ARPT_lon>25.000000", testCallBackUnionfoursCondition, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackUnionfoursCondition, &rows, NULL, 1, 0, 4, "ARPT", "ARPT_ident<KUUAX", "ARPT_length>=3968.782678", "ARPT_lat<=45.000000", "ARPT_lon>25.000000");
#endif
    if (rc == GNCDB_SUCCESS && test_table_count.testFlag && test_table_count.rowcount == rows) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_45(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_ident <ZZZ";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT WHERE WPT_ident< 'ZZZ'", testCallBackSqlLimit1, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSqlLimit1, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident <ZZZ");
#endif
    if ((rc == GNCDB_SUCCESS) || rc == GNCDB_CONDITION_INVALID) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_46(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "WPT_lon", "WPT_lat" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT WPT_lon WPT_lat FROM WPT";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT WPT_lon, WPT_lat FROM WPT", testCallBackSelectAllProject, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectAllProject, &rows, NULL, 1, 2, 0, "WPT", "WPT_lon", "WPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_47(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[2] = { "WPT_lat", "WPT_lon" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT WPT_lat WPT_lon FROM WPT";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;

#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT WPT_lat, WPT_lon FROM WPT", testCallBackSelectAllProject,  &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectAllProject, &rows, NULL, 1, 2, 0, "WPT", "WPT_lat", "WPT_lon");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_48(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[4] = { "ARPT_lon", "ARPT_lat", "ARPT_length", "15.0" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT ARPT_lon ARPT_lat ARPT_length FROM ARPT WHERE ARPT_elev<15.0";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 3;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT ARPT_lon, ARPT_lat, ARPT_length FROM ARPT WHERE ARPT_elev<15.0", testCallBackSelectConditionProject, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectConditionProject, &rows, NULL, 1, 3, 1, "ARPT", "ARPT_lon", "ARPT_lat", "ARPT_length", "ARPT_elev<15.0");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_49(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[4] = { "ARPT_length", "ARPT_lon", "ARPT_lat", "15.0" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT ARPT_length ARPT_lon ARPT_lat FROM ARPT WHERE ARPT_elev<15.0";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 3;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT ARPT_length, ARPT_lon, ARPT_lat FROM ARPT WHERE ARPT_elev<15.0", testCallBackSelectConditionProject, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectConditionProject, &rows, NULL, 1, 3, 1, "ARPT", "ARPT_length", "ARPT_lon", "ARPT_lat", "ARPT_elev<15.0");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_50(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[4] = { "ARPT_lon", "ARPT_lat", "ARPT_length", "15.0" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT ARPT_lon ARPT_lat ARPT_length FROM ARPT WHERE ARPT_ident>IIFAP ARPT_lon>=32.343750 ARPT_elev<15.0";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 3;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;

#ifdef SQL_TESTMODE 
    rc = GNCDB_exec(db, "SELECT ARPT_lon, ARPT_lat, ARPT_length FROM ARPT WHERE ARPT_ident>'IIFAP' AND ARPT_lon>=32.343750 AND ARPT_elev<15.0", testCallBackSelectConditionProject, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectConditionProject, &rows, NULL, 1, 3, 3, "ARPT", "ARPT_lon", "ARPT_lat", "ARPT_length", "ARPT_ident>IIFAP", "ARPT_lon>=32.343750", "ARPT_elev<15.0");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_51(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[4] = { "ARPT_length", "ARPT_lon", "ARPT_lat", "15.0" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT ARPT_length ARPT_lon ARPT_lat FROM ARPT WHERE ARPT_ident>IIFAP ARPT_lon>=32.343750 ARPT_elev<15.0";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "ARPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 3;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT ARPT_length, ARPT_lon, ARPT_lat FROM ARPT WHERE ARPT_ident>'IIFAP' AND ARPT_lon>=32.343750 AND ARPT_elev<15.0", testCallBackSelectConditionProject, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectConditionProject, &rows, NULL, 1, 3, 3, "ARPT", "ARPT_length", "ARPT_lon", "ARPT_lat", "ARPT_ident>IIFAP", "ARPT_lon>=32.343750", "ARPT_elev<15.0");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount) {
        addTest(testSum, rc);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_52(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
    int rows = 0;
#endif
    test_table_count.SQL = "SELECT WPT_lonat  WPT_lat FROM WPT";
    currentTest(testSum, selectTable);
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT WPT_lonat, WPT_lat FROM WPT", testCallBackSelectAllProject, NULL, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackSelectAllProject, &rows, NULL, 1, 2, 0, "WPT", "WPT_lonat", "WPT_lat");
#endif
    if (rc == GNCDB_PARAMNULL || rc == -5) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_53(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT";
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT";
    currentTest(testSum, selectTable);

    test_table_count.rowcount = 0;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT, ARPT;", testCallBackAllJoin, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackAllJoin, &rows, NULL, 2, 0, 0, "WPT", "ARPT");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_54(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    char* value[1] = { "PZKDE" };
    int rc = 0;
    int rows = 0;
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT WHERE WPT_ident>=PZKDE";
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT WHERE WPT_ident>=PZKDE";
    currentTest(testSum, selectTable);

    test_table_count.tableName = "WPT";
    test_table_count.fieldName = value;
    test_table_count.rowNum = 3;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT, ARPT WHERE WPT_ident>='PZKDE'", testCallBackConditionJoin, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoin, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_ident>=PZKDE");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_55(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 0, 4 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT "
                           "JOIN ARPT"
                           "ON WPT_ident=ARPT_ident";

    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident=ARPT_ident", testCallBackConditionJoinEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinEquals, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_ident=ARPT_ident");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_56(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 0, 4 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident<ARPT_ident";
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident<ARPT_ident";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident<ARPT_ident", testCallBackConditionJoinLess, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinLess, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_ident<ARPT_ident");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_57(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 0, 4 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident<=ARPT_ident";
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident<=ARPT_ident";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident<=ARPT_ident", testCallBackConditionJoinLessEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinLessEquals, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_ident<=ARPT_ident");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_58(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 0, 4 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident>ARPT_ident";
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident>ARPT_ident";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident>ARPT_ident", testCallBackConditionJoinGreater, &rows, &errmsg);
#else   
    rc = GNCDB_select(db, testCallBackConditionJoinGreater, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_ident>ARPT_ident");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_59(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 0, 4 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident>=ARPT_ident";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident>=ARPT_ident", testCallBackConditionJoinGreaterEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinGreaterEquals, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_ident>=ARPT_ident");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_60(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;

#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat", testCallBackConditionJoinEqualsFloat, &rows, &errmsg);
#else   
    rc = GNCDB_select(db, testCallBackConditionJoinEqualsFloat, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_lat=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_61(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat<ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat<ARPT_lat", testCallBackConditionJoinLessFloat, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinLessFloat, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_lat<ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_62(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat<=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat<=ARPT_lat", testCallBackConditionJoinLessEqualsFloat, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinLessEqualsFloat, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_lat<=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_63(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat>ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat>ARPT_lat", testCallBackConditionJoinGreaterFloat, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinGreaterFloat, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_lat>ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_64(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat>=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat>=ARPT_lat", testCallBackConditionJoinGreaterEqualsFloat, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinGreaterEqualsFloat, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_lat>=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_65(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE WPT_lon=45.000000";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE WPT_lon=45.000000", testCallBack5_65, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_65, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lat=ARPT_lat", "WPT_lon=45.000000");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_66(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE WPT_lon=45.000000 AND WPT_ident<=SCXEL";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE WPT_lon=45.000000 AND WPT_ident<='SCXEL'", testCallBack5_66, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_66, &rows, NULL, 2, 0, 3, "WPT", "ARPT", "WPT_lat=ARPT_lat", "WPT_lon=45.000000", "WPT_ident<=SCXEL");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_67(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE ARPT_length<=1725.522568";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE ARPT_length<=1725.522568", testCallBack5_67, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_67, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lat=ARPT_lat", "ARPT_length<=1725.522568");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_68(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE ARPT_length<=1725.522568 AND ARPT_elev>24.959593";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE ARPT_length<=1725.522568 AND ARPT_elev>24.959593", testCallBack5_68, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_68, &rows, NULL, 2, 0, 3, "WPT", "ARPT", "WPT_lat=ARPT_lat", "ARPT_length<=1725.522568", "ARPT_elev>24.959593");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_69(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 2, 6 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE WPT_lon>=0.0 AND ARPT_elev>24.959593";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat WHERE WPT_lon>=0.0 AND ARPT_elev>24.959593", testCallBack5_69, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_69, &rows, NULL, 2, 0, 3, "WPT", "ARPT", "WPT_lat=ARPT_lat", "WPT_lon>=0.0", "ARPT_elev>24.959593");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_70(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = { 0, 4 };
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON ARPT_ident=WPT_ident";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON ARPT_ident=WPT_ident", testCallBackConditionJoinEquals, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBackConditionJoinEquals, &rows, NULL, 2, 0, 1, "WPT", "ARPT", "WPT_ident=ARPT_ident");  // Update this part with a valid test
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_71(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat AND WPT_lon=ARPT_lon";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lat=ARPT_lat AND WPT_lon=ARPT_lon", testCallBack5_71, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_71, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lat=ARPT_lat", "WPT_lon=ARPT_lon");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_72(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<ARPT_lat", testCallBack5_72, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_72, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon=ARPT_lon", "WPT_lat<ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_73(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat<=ARPT_lat", testCallBack5_73, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_73, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon=ARPT_lon", "WPT_lat<=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_74(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat>ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat>ARPT_lat", testCallBack5_74, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_74, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon=ARPT_lon", "WPT_lat>ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_75(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat>=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;

#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon=ARPT_lon AND WPT_lat>=ARPT_lat", testCallBack5_75, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_75, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon=ARPT_lon", "WPT_lat>=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_76(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;

#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat=ARPT_lat", testCallBack5_76, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_76, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<ARPT_lon", "WPT_lat=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_77(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat<ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat<ARPT_lat", testCallBack5_77, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_77, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<ARPT_lon", "WPT_lat<ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_78(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
 #ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat<=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat<=ARPT_lat", testCallBack5_78, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_78, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<ARPT_lon", "WPT_lat<=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_79(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat>ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat>ARPT_lat", testCallBack5_79, &rows, &errmsg);
#else   
    rc = GNCDB_select(db, testCallBack5_79, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<ARPT_lon", "WPT_lat>ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_80(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat>=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<ARPT_lon AND WPT_lat>=ARPT_lat", testCallBack5_80, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_80, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<ARPT_lon", "WPT_lat>=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_81(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat=ARPT_lat", testCallBack5_81, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_81, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<=ARPT_lon", "WPT_lat=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_82(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat<ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat<ARPT_lat", testCallBack5_82, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_82, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<=ARPT_lon", "WPT_lat<ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_83(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat<=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat<=ARPT_lat", testCallBack5_83, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_83, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<=ARPT_lon", "WPT_lat<=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_84(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat>ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat>ARPT_lat", testCallBack5_84, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_84, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<=ARPT_lon", "WPT_lat>ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_85(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat>=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon<=ARPT_lon AND WPT_lat>=ARPT_lat", testCallBack5_85, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_85, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon<=ARPT_lon", "WPT_lat>=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_86(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat=ARPT_lat", testCallBack5_86, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_86, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>ARPT_lon", "WPT_lat=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_87(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat<ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat<ARPT_lat", testCallBack5_87, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_87, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>ARPT_lon", "WPT_lat<ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_88(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat<=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat<=ARPT_lat", testCallBack5_88, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_88, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>ARPT_lon", "WPT_lat<=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_89(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat>ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat>ARPT_lat", testCallBack5_89, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_89, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>ARPT_lon", "WPT_lat>ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_90(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat>=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>ARPT_lon AND WPT_lat>=ARPT_lat", testCallBack5_90, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_90, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>ARPT_lon", "WPT_lat>=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int test5_91(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat=ARPT_lat", testCallBack5_91, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_91, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_92(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat<ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat<ARPT_lat", testCallBack5_92, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_92, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat<ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_93(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat<=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat<=ARPT_lat", testCallBack5_93, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_93, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat<=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_94(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>ARPT_lat", testCallBack5_94, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_94, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat>ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_95(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif

    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat", testCallBack5_95, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_95, &rows, NULL, 2, 0, 2, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat>=ARPT_lat");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_96(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat WHERE WPT_ident<=LSUWL";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat WHERE WPT_ident<='LSUWL'", testCallBack5_96, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_96, &rows, NULL, 2, 0, 3, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat>=ARPT_lat", "WPT_ident<=LSUWL");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_97(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif

    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat WHERE ARPT_ident>=LSUWL";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat WHERE ARPT_ident>='LSUWL'", testCallBack5_97, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_97, &rows, NULL, 2, 0, 3, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat>=ARPT_lat", "ARPT_ident>=LSUWL");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_98(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
 #ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif

    test_table_count.SQL = "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat WHERE ARPT_ident>=LSUWL AND WPT_ident<JDLYH AND ARPT_mag_var>5.222938";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_lon>=ARPT_lon AND WPT_lat>=ARPT_lat WHERE ARPT_ident>='LSUWL' AND WPT_ident<'JDLYH' AND ARPT_mag_var>5.222938", testCallBack5_98, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_98, &rows, NULL, 2, 0, 5, "WPT", "ARPT", "WPT_lon>=ARPT_lon", "WPT_lat>=ARPT_lat", "ARPT_ident>=LSUWL", "WPT_ident<JDLYH", "ARPT_mag_var>5.222938");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_99(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif
    char* value[6] = { "WPT.WPT_lon", "WPT.WPT_lat", "ARPT.ARPT_ident", "ARPT.ARPT_elev", "ARPT.ARPT_length", "ARPT.ARPT_mag_var" };

    test_table_count.SQL = "SELECT WPT_lon WPT_lat ARPT_ident ARPT_elev ARPT_length ARPT_mag_var FROM WPT INNER JOIN ARPT;";
    test_table_count.SQL = "SELECT WPT_lon WPT_lat ARPT_ident ARPT_elev ARPT_length ARPT_mag_var FROM WPT INNER JOIN ARPT;";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
    test_table_count.tableName = "WPT";
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT WPT_lon, WPT_lat, ARPT_ident, ARPT_elev, ARPT_length, ARPT_mag_var FROM WPT, ARPT;", testCallBack5_99, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_99, &rows, NULL, 2, 6, 0, "WPT", "ARPT", "WPT_lon", "WPT_lat", "ARPT_ident", "ARPT_elev", "ARPT_length", "ARPT_mag_var");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_100(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif

    char* value[6] = { "ARPT.ARPT_length", "ARPT.ARPT_ident", "WPT.WPT_lat", "WPT.WPT_lon", "ARPT.ARPT_mag_var", "ARPT.ARPT_elev" };

    test_table_count.SQL = "SELECT ARPT_length ARPT_ident WPT_lat WPT_lon ARPT_mag_var ARPT_elev FROM WPT INNER JOIN ARPT;";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
    test_table_count.tableName = "WPT";
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT ARPT_length, ARPT_ident, WPT_lat, WPT_lon, ARPT_mag_var, ARPT_elev FROM WPT, ARPT;", testCallBack5_99, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_99, &rows, NULL, 2, 6, 0, "WPT", "ARPT", "ARPT_length", "ARPT_ident", "WPT_lat", "WPT_lon", "ARPT_mag_var", "ARPT_elev");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_101(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif

    char* value[6] = { "WPT.WPT_lon", "WPT.WPT_lat", "ARPT.ARPT_ident", "ARPT.ARPT_elev", "ARPT.ARPT_length", "ARPT.ARPT_mag_var" };

    test_table_count.SQL = "SELECT WPT_lon, WPT_lat, ARPT_ident, ARPT_elev, ARPT_length, ARPT_mag_var FROM WPT INNER JOIN ARPT WHERE WPT_lat=45.0";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
    test_table_count.tableName = "WPT";
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT WPT_lon, WPT_lat, ARPT_ident, ARPT_elev, ARPT_length, ARPT_mag_var FROM WPT INNER JOIN ARPT ON WPT_lat=45.0", testCallBack5_99, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_99, &rows, NULL, 2, 6, 1, "WPT", "ARPT", "WPT_lon", "WPT_lat", "ARPT_ident", "ARPT_elev", "ARPT_length", "ARPT_mag_var", "WPT_lat=45.0");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}

int test5_102(GNCDB* db, TESTSUM* testSum, char* selectTable) {
    int rc = 0;
    int rows = 0;
    int index[2] = {2, 6};
#ifdef SQL_TESTMODE
    char* errmsg = NULL;
#else
#endif

    char* value[6] = { "ARPT.ARPT_length", "ARPT.ARPT_ident", "WPT.WPT_lat", "WPT.WPT_lon", "ARPT.ARPT_mag_var", "ARPT.ARPT_elev" };

    test_table_count.SQL = "SELECT ARPT_length, ARPT_ident, WPT_lat, WPT_lon, ARPT_mag_var, ARPT_elev FROM WPT INNER JOIN ARPT WHERE WPT_lat=45.0";
    currentTest(testSum, selectTable);

    test_table_count.index = index;
    test_table_count.fieldName = value;
    test_table_count.rowNum = 2;
    test_table_count.rowcount = 0;
    test_table_count.testFlag = false;
    test_table_count.tableName = "WPT";
#ifdef SQL_TESTMODE
    rc = GNCDB_exec(db, "SELECT ARPT_length, ARPT_ident, WPT_lat, WPT_lon, ARPT_mag_var, ARPT_elev FROM WPT INNER JOIN ARPT ON WPT_lat=45.0", testCallBack5_99, &rows, &errmsg);
#else
    rc = GNCDB_select(db, testCallBack5_99, &rows, NULL, 2, 6, 1, "WPT", "ARPT", "ARPT_length", "ARPT_ident", "WPT_lat", "WPT_lon", "ARPT_mag_var", "ARPT_elev", "WPT_lat=45.0");
#endif
    if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
        addTest(testSum, 0);
    } else {
        if (rc == GNCDB_SUCCESS) {
            rc = -1;
        }
        addTest(testSum, rc);
    }

    return 0;
}


int testSelectTable(GNCDB* db, TESTSUM* testSum)
{
  char selectTable1[]     = "5.1:主键查询 等于";
  char selectTable2[]     = "5.2:主键查询 小于";
  char selectTable3[]     = "5.3:主键查询 小于等于";
  char selectTable4[]     = "5.4:主键查询 大于";
  char selectTable5[]     = "5.5:主键查询 大于等于";
  char selectTable6[]     = "5.6:主键查询 主键值不存在";
  char selectTable7[]     = "5.7:属性不存在";
  char selectTable8[]     = "5.8:主键联合查询 等于+等于";
  char selectTable9[]     = "5.9:主键联合查询 等于+数据不存在";
  char selectTable10[]    = "5.10:非主键查询 等于";
  char selectTable11[]    = "5.11:非主键查询 小于";
  char selectTable12[]    = "5.12:非主键查询 小于等于";
  char selectTable13[]    = "5.13:非主键查询 大于";
  char selectTable14[]    = "5.14:非主键查询 大于等于";
  char selectTable15[]    = "5.15:主键联合查询 小于+小于";
  char selectTable16[]    = "5.16:主键联合查询 小于+小于等于";
  char selectTable17[]    = "5.17:主键联合查询 小于+大于";
  char selectTable18[]    = "5.18:主键联合查询 小于+大于等于";
  char selectTable19[]    = "5.19:主键联合查询 小于等于+小于";
  char selectTable20[]    = "5.20:主键联合查询 小于等于+小于等于";
  char selectTable21[]    = "5.21:主键联合查询 小于等于+大于";
  char selectTable22[]    = "5.22:主键联合查询 小于等于+大于等于";
  char selectTable23[]    = "5.23:主键联合查询 大于+小于";
  char selectTable24[]    = "5.24:主键联合查询 大于+小于等于";
  char selectTable25[]    = "5.25:主键联合查询 大于+大于";
  char selectTable26[]    = "5.26:主键联合查询 大于+大于等于";
  char selectTable27[]    = "5.27:主键联合查询 大于等于+小于";
  char selectTable28[]    = "5.28:主键联合查询 大于等于+小于等于";
  char selectTable29[]    = "5.29:主键联合查询 大于等于+大于";
  char selectTable30[]    = "5.30:主键联合查询 大于等于+大于等于";
  char selectTable31[]    = "5.31:主键联合查询 前置条件无数据";
  char selectTable32[]    = "5.32:主键联合查询 后置条件无数据";
  char selectTable33[]    = "5.33:表名不存在";
  char selectTable34[]    = "5.34:多条件查询 小于+小于+小于";
  char selectTable35[]    = "5.35:多条件查询 小于+小于+大于";
  char selectTable36[]    = "5.36:多条件查询 小于+大于+小于";
  char selectTable37[]    = "5.37:多条件查询 小于+大于+大于";
  char selectTable38[]    = "5.38:多条件查询 大于+小于+小于";
  char selectTable39[]    = "5.39:多条件查询 大于+小于+大于";
  char selectTable40[]    = "5.40:多条件查询 大于+大于+小于";
  char selectTable41[]    = "5.41:多条件查询 大于+大于+大于";
  char selectTable42[]    = "5.42:多条件查询 四条件范围查询";
  char selectTable43[]    = "5.43:多条件查询 四条件查询";
  char selectTable44[]    = "5.44:多条件查询 四条件乱序查询";
  char selectTable45[]    = "5.45:查询条件中存在空格";
  char selectTable46[]    = "5.46:全表查询+投影";
  char selectTable47[]    = "5.47:全表查询+乱序投影";
  char selectTable48[]    = "5.48:单条件查询+投影";
  char selectTable49[]    = "5.49:单条件查询+乱序投影";
  char selectTable50[]    = "5.50:多条件查询+投影";
  char selectTable51[]    = "5.51:多条件查询+乱序投影";
  char selectTable52[]    = "5.52:投影属性不存在";
  char selectTable53[]    = "5.53:连接查询 全连接";
  char selectTable54[]    = "5.54:连接查询 单表条件连接";
  char selectTable55[]    = "5.55:连接查询 连接条件主键连接等于";
  char selectTable56[]    = "5.56:连接查询 连接条件主键连接小于";
  char selectTable57[]    = "5.57:连接查询 连接条件主键连接小于等于";
  char selectTable58[]    = "5.58:连接查询 连接条件主键连接大于";
  char selectTable59[]    = "5.59:连接查询 连接条件主键连接大于等于";
  char selectTable60[]    = "5.60:连接查询 连接条件连接等于";
  char selectTable61[]    = "5.61:连接查询 连接条件主键连接小于";
  char selectTable62[]    = "5.62:连接查询 连接条件主键连接小于等于";
  char selectTable63[]    = "5.63:连接查询 连接条件主键连接大于";
  char selectTable64[]    = "5.64:连接查询 连接条件主键连接大于等于";
  char selectTable65[]    = "5.65:连接查询 连接条件+表1 1条件";
  char selectTable66[]    = "5.66:连接查询 连接条件+表1 多条件";
  char selectTable67[]    = "5.67:连接查询 连接条件+表2 1条件";
  char selectTable68[]    = "5.68:连接查询 连接条件+表2 多条件";
  char selectTable69[]    = "5.69:连接查询 连接条件+表1-2条件";
  char selectTable70[]    = "5.70:连接查询 连接条件属性换位";
  char selectTable71[]    = "5.71:连接查询 双连接条件等于";
  char selectTable72[]    = "5.72:连接查询 双连接条件等于小于";
  char selectTable73[]    = "5.73:连接查询 双连接条件等于小于等于";
  char selectTable74[]    = "5.74:连接查询 双连接条件等于大于";
  char selectTable75[]    = "5.75:连接查询 双连接条件等于大于";
  char selectTable76[]    = "5.76:连接查询 连接条件小于等于";
  char selectTable77[]    = "5.77:连接查询 双连接条件小于小于";
  char selectTable78[]    = "5.78:连接查询 双连接条件小于小于等于";
  char selectTable79[]    = "5.79:连接查询 双连接条件小于大于";
  char selectTable80[]    = "5.80:连接查询 双连接条件小于大于等于";
  char selectTable81[]    = "5.81:连接查询 连接条件小于等于等于";
  char selectTable82[]    = "5.82:连接查询 双连接条件小于等于小于";
  char selectTable83[]    = "5.83:连接查询 双连接条件小于等于小于等于";
  char selectTable84[]    = "5.84:连接查询 双连接条件小于等于大于";
  char selectTable85[]    = "5.85:连接查询 双连接条件小于等于大于等于";
  char selectTable86[]    = "5.86:连接查询 连接条件大于等于";
  char selectTable87[]    = "5.87:连接查询 双连接条件大于小于";
  char selectTable88[]    = "5.88:连接查询 双连接条件大于小于等于";
  char selectTable89[]    = "5.89:连接查询 双连接条件大于大于";
  char selectTable90[]    = "5.90:连接查询 双连接条件大于大于等于";
  char selectTable91[]    = "5.91:连接查询 连接条件大于等于等于";
  char selectTable92[]    = "5.92:连接查询 双连接条件大于等于小于";
  char selectTable93[]    = "5.93:连接查询 双连接条件大于等于小于等于";
  char selectTable94[]    = "5.94:连接查询 双连接条件大于等于大于";
  char selectTable95[]    = "5.95:连接查询 双连接条件大于等于大于等于";
  char selectTable96[]    = "5.96:连接查询 双连接条件＋表1条件";
  char selectTable97[]    = "5.97:连接查询 双连接条件＋表2条件";
  char selectTable98[]    = "5.98:连接查询 双连接条件＋多条件";
  char selectTable99[38]  = "5.99:连接查询 全连接＋投影";
  char selectTable100[45] = "5.100:连接查询 全连接＋乱序投影";
  char selectTable101[42] = "5.101:连接查询 条件查询＋投影";
  char selectTable102[48] = "5.102:连接查询 条件查询＋乱序投影";

  test_table_count.testProject = "5:查询测试";

  test5_1(db, testSum, selectTable1);
  test5_2(db, testSum, selectTable2);
  test5_3(db, testSum, selectTable3);
  test5_4(db, testSum, selectTable4);
  test5_5(db, testSum, selectTable5);
  test5_6(db, testSum, selectTable6);
  test5_7(db, testSum, selectTable7);
  test5_8(db, testSum, selectTable8);
  test5_9(db, testSum, selectTable9);
  test5_10(db, testSum, selectTable10);
  test5_11(db, testSum, selectTable11);
  test5_12(db, testSum, selectTable12);
  test5_13(db, testSum, selectTable13);
  test5_14(db, testSum, selectTable14);
  test5_15(db, testSum, selectTable15);
  test5_16(db, testSum, selectTable16);
  test5_17(db, testSum, selectTable17);
  test5_18(db, testSum, selectTable18);
  test5_19(db, testSum, selectTable19);
  test5_20(db, testSum, selectTable20);
  test5_21(db, testSum, selectTable21);
  test5_22(db, testSum, selectTable22);
  test5_23(db, testSum, selectTable23);
  test5_24(db, testSum, selectTable24);
  test5_25(db, testSum, selectTable25);
  test5_26(db, testSum, selectTable26);
  test5_27(db, testSum, selectTable27);
  test5_28(db, testSum, selectTable28);
  test5_29(db, testSum, selectTable29);
  test5_30(db, testSum, selectTable30);
  test5_31(db, testSum, selectTable31);
  test5_32(db, testSum, selectTable32);
  test5_33(db, testSum, selectTable33);
  test5_34(db, testSum, selectTable34);
  test5_35(db, testSum, selectTable35);
  test5_36(db, testSum, selectTable36);
  test5_37(db, testSum, selectTable37);
  test5_38(db, testSum, selectTable38);
  test5_39(db, testSum, selectTable39);
  test5_40(db, testSum, selectTable40);
  test5_41(db, testSum, selectTable41);
  test5_42(db, testSum, selectTable42);
  test5_43(db, testSum, selectTable43);
  test5_44(db, testSum, selectTable44);
  test5_45(db, testSum, selectTable45);
  test5_46(db, testSum, selectTable46);
  test5_47(db, testSum, selectTable47);
  test5_48(db, testSum, selectTable48);
  test5_49(db, testSum, selectTable49);
  test5_50(db, testSum, selectTable50);
  test5_51(db, testSum, selectTable51);
  test5_52(db, testSum, selectTable52);
  test5_53(db, testSum, selectTable53);
  test5_54(db, testSum, selectTable54);
  test5_55(db, testSum, selectTable55);
  test5_56(db, testSum, selectTable56);
  test5_57(db, testSum, selectTable57);
  test5_58(db, testSum, selectTable58);
  test5_59(db, testSum, selectTable59);
  test5_60(db, testSum, selectTable60);
  test5_61(db, testSum, selectTable61);
  test5_62(db, testSum, selectTable62);
  test5_63(db, testSum, selectTable63);
  test5_64(db, testSum, selectTable64);
  test5_65(db, testSum, selectTable65);
  test5_66(db, testSum, selectTable66);
  test5_67(db, testSum, selectTable67);
  test5_68(db, testSum, selectTable68);
  test5_69(db, testSum, selectTable69);
  test5_70(db, testSum, selectTable70);
  test5_71(db, testSum, selectTable71);
  test5_72(db, testSum, selectTable72);
  test5_73(db, testSum, selectTable73);
  test5_74(db, testSum, selectTable74);
  test5_75(db, testSum, selectTable75);
  test5_76(db, testSum, selectTable76);
  test5_77(db, testSum, selectTable77);
  test5_78(db, testSum, selectTable78);
  test5_79(db, testSum, selectTable79);
  test5_80(db, testSum, selectTable80);
  test5_81(db, testSum, selectTable81);
  test5_82(db, testSum, selectTable82);
  test5_83(db, testSum, selectTable83);
  test5_84(db, testSum, selectTable84);
  test5_85(db, testSum, selectTable85);
  test5_86(db, testSum, selectTable86);
  test5_87(db, testSum, selectTable87);
  test5_88(db, testSum, selectTable88);
  test5_89(db, testSum, selectTable89);
  test5_90(db, testSum, selectTable90);
  test5_91(db, testSum, selectTable91);
  test5_92(db, testSum, selectTable92);
  test5_93(db, testSum, selectTable93);
  test5_94(db, testSum, selectTable94);
  test5_95(db, testSum, selectTable95);
  test5_96(db, testSum, selectTable96);
  test5_97(db, testSum, selectTable97);
  test5_98(db, testSum, selectTable98);
  test5_99(db, testSum, selectTable99);
  test5_100(db, testSum, selectTable100);
  test5_101(db, testSum, selectTable101);
  test5_102(db, testSum, selectTable102);

  return 0;
}

int test6_1(GNCDB *db, TESTSUM *testSum)
{
  int  rc            = 0;
  int  rows          = 0;
  char updataTable[] = "6.1:主键更新";
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_ident=XIANBEI"
                         "WHERE ARPT_length=952.685537";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE ARPT SET ARPT_ident='XIANBEI' WHERE ARPT_length=952.685537", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 1, 1, "ARPT_ident", "XIANBEI", "ARPT_length=952.685537");
#endif
  if (rc == GNCDB_PRIMARY_KEY_IMMUTABLE) {
    addTest(testSum, 0);
  } else {
    addTest(testSum, rc);
  }

  return 0;
}

int test6_2(GNCDB *db, TESTSUM *testSum)
{
  int    rc   = 0;
  int    rows = 0;
  int    i    = 0;
  double min  = -10000.0;
  double max  = 10000.0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char  updataTable[] = "6.2:全更新";
  char *path          = strJoin(testFilePath, testFileName1);
  FILE *fileData      = fopen(path, "r");

  test_table_count.SQL = "UPDATE WPT_up1"
                         "SET WPT_lat=0";
  currentTest(testSum, updataTable);
  rc = GNCDB_createTable(db,
      "WPT_up1",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  // path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  // fileData = fopen(path, "r");
  if (fileData == NULL) {
    return -1;
  }
  for (i = 0; i < 400; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "WPT_up1", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up1 SET WPT_lat=0", NULL, &rows, &errmsg);

  GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "WPT_up1");
#else
  rc = GNCDB_update(db, &rows, "WPT_up1", 1, 0, "WPT_lat", 0.0);
#endif
  // printfPagePin(test_table_count.db->pagePool);
  test_table_count.rowcount = 0;
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "WPT_up1");
  GNCDB_select(db, testCallBackAllUpDate, &test_table_count.rowcount, NULL, 1, 0, 0, "WPT_up1");
  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  return 0;
}

int test6_3(GNCDB *db, TESTSUM *testSum)
{
  int    rc   = 0;
  int    i    = 0;
  int    rows = 0;
  double min  = -10000.0;
  double max  = 10000.0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *path           = NULL;
  FILE *fileData       = NULL;
  char  updataTable[]  = "6.3:条件更新等于";
  test_table_count.SQL = "UPDATE WPT_up2"
                         "SET WPT_lat=42.0"
                         "WHERE WPT_ident=DYINT";
  currentTest(testSum, updataTable);
  rc = GNCDB_createTable(db,
      "WPT_up2",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 200; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "WPT_up2", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lat=42.0 WHERE WPT_ident='DYINT'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_lat", 42.0, "WPT_ident=DYINT");
#endif
  test_table_count.rowcount = 0;
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "WPT_up2");
  GNCDB_select(db, testCallBack6_3, &rows, NULL, 1, 0, 1, "WPT_up2", "WPT_ident=DYINT");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path);
  return 0;
}

int test6_4(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "6.4:条件更新小于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT_up2"
                         "SET WPT_lon=52.0"
                         "WHERE WPT_ident<FTHLF";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lon=52.0 WHERE WPT_ident<'FTHLF'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_lon", 52.0, "WPT_ident<FTHLF");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_4, &rows, NULL, 1, 0, 1, "WPT_up2", "WPT_ident<FTHLF");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test6_5(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "6.5:条件更新小于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT_up2"
                         "SET WPT_lat=100.0"
                         "WHERE WPT_ident<=FTHLF";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lat=100.0 WHERE WPT_ident<='FTHLF'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_lat", 100.0, "WPT_ident<=FTHLF");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_5, &rows, NULL, 1, 0, 1, "WPT_up2", "WPT_ident<=FTHLF");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test6_6(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.6:条件更新大于";

  test_table_count.SQL = "UPDATE WPT_up2 "
                         "SET WPT_lon=-10.0 "
                         "WHERE WPT_ident>SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lon=-10.0 WHERE WPT_ident>'SRVIO'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_lon", -10.0, "WPT_ident>SRVIO");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_6, &rows, NULL, 1, 0, 1, "WPT_up2", "WPT_ident>SRVIO");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_7(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.7:条件更新大于等于";

  test_table_count.SQL = "UPDATE WPT_up2 "
                         "SET WPT_lat=10.0 "
                         "WHERE WPT_ident>=SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lat=10.0 WHERE WPT_ident>='SRVIO'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_lat", 10.0, "WPT_ident>=SRVIO");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_7, &rows, NULL, 1, 0, 1, "WPT_up2", "WPT_ident>=SRVIO");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}
int test6_8(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.8:更新表名不存在";

  test_table_count.SQL = "UPDATE table1 "
                         "SET WPT_lat=10.0 "
                         "WHERE WPT_ident>=SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE table1 SET WPT_lat=10.0 WHERE WPT_ident>='SRVIO'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "table1", 1, 1, "WPT_lat", 10.0, "WPT_ident>=SRVIO");
#endif
  if (rc == GNCDB_TABLE_NOT_FOUND) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_9(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.9:更新属性不存在";

  test_table_count.SQL = "UPDATE WPT_up2 "
                         "SET WPT_up_lat=10.0 "
                         "WHERE WPT_ident=SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_up_lat=10.0 WHERE WPT_ident='SRVIO'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_up_lat", 10.0, "WPT_ident=SRVIO");
#endif
  if (rc == GNCDB_PARAM_INVALID || rc == -19 || rc == GNCDB_NOT_FOUND) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_10(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.10:更新条件属性不存在";

  test_table_count.SQL = "UPDATE WPT_up2 "
                         "SET WPT_lat=1000.0 "
                         "WHERE WPT_up_ident=SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lat=1000.0 WHERE WPT_up_ident='SRVIO'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_lat", 1000.0, "WPT_up_ident=SRVIO");
#endif
  if ((rc == GNCDB_CONDITION_INVALID || rc == GNCDB_FIELD_NOT_EXIST) && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_11(GNCDB *db, TESTSUM *testSum)
{
  int    rc   = 0;
  int    rows = 0;
  double min  = -10000.0;
  double max  = 10000.0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  char *path;
  FILE *fileData;
  int   i        = 0;
  char *value[2] = {"100.000000", "900.000000"};
  int   index[2] = {2, 4};

  char updataTable[]   = "6.11:单条件更新多条记录等于";
  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_lat=100.0 ARPT_length=900.0 "
                         "WHERE ARPT_mag_var=7.197790";
  currentTest(testSum, updataTable);

  rc = GNCDB_createTable(db,
      "ARPT_up1",
      6,
      "ARPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "ARPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_elev",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_length",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_mag_var",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName2);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 200; ++i) {
    fscanf(fileData,
        "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
        arpt.sc8_arpt_ident,
        &arpt.f64_lon,
        &arpt.f64_lat,
        &arpt.f64_elev,
        &arpt.f64_longest_rwy_length,
        &arpt.f64_mag_var);
    rc = GNCDB_insert(db,
        NULL,
        "ARPT_up1",
        arpt.sc8_arpt_ident,
        arpt.f64_lon,
        arpt.f64_lat,
        arpt.f64_elev,
        arpt.f64_longest_rwy_length,
        arpt.f64_mag_var);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT_up1 SET ARPT_lat=100.0, ARPT_length=900.0 WHERE ARPT_mag_var=7.197790;", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT_up1", 2, 1, "ARPT_lat", 100.0, "ARPT_length", 900.0, "ARPT_mag_var=7.197790");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT_up1", "ARPT_mag_var=7.197790");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  return 0;
}

int test6_12(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char  updataTable[] = "6.12:单条件更新多条记录小于";
  char *value[2]      = {"39.990000", "-39.990000"};
  int   index[2]      = {1, 2};

  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_lon=39.99 ARPT_lat=-39.99 "
                         "WHERE ARPT_length<779.029420";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT_up1 SET ARPT_lon=39.99, ARPT_lat=-39.99 WHERE ARPT_length<779.029420", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT_up1", 2, 1, "ARPT_lon", 39.99, "ARPT_lat", -39.99, "ARPT_length<779.029420");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT_up1", "ARPT_length<779.029420");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_13(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *value[2] = {"20.000000", "5.200000"};
  int   index[2] = {3, 5};

  char updataTable[]   = "6.13:单条件更新多条记录小于等于";
  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_elev=20.0 ARPT_mag_var=5.20 "
                         "WHERE ARPT_ident<=CBMKD";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT_up1 SET ARPT_elev=20.0, ARPT_mag_var=5.20 WHERE ARPT_ident<='CBMKD'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT_up1", 2, 1, "ARPT_elev", 20.0, "ARPT_mag_var", 5.20, "ARPT_ident<=CBMKD");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;
  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT_up1", "ARPT_ident<=CBMKD");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_14(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  char *value[3] = {"20.000000", "1000.000000", "5.200000"};
  int   index[3] = {3, 4, 5};

  char updataTable[]   = "6.14:单条件更新多条记录大于";
  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_length=1000.0 ARPT_elev=20.0 ARPT_mag_var=5.20 "
                         "WHERE ARPT_ident>WCXPD";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT_up1 SET ARPT_length=1000.0, ARPT_elev=20.0, ARPT_mag_var=5.20 WHERE ARPT_ident>'WCXPD'",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_update(
      db, &rows, "ARPT_up1", 3, 1, "ARPT_length", 1000.0, "ARPT_elev", 20.0, "ARPT_mag_var", 5.20, "ARPT_ident>WCXPD");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 3;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT_up1", "ARPT_ident>WCXPD");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_15(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *value[3] = {"1000.000000", "20.000000", "6.200000"};
  int   index[3] = {1, 2, 5};

  char updataTable[] = "6.15:单条件更新多条记录大于等于";

  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_lon=1000.0 ARPT_lat=20.0 ARPT_mag_var=6.20 "
                         "WHERE ARPT_ident>=YQLUV";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT_up1 SET ARPT_lon=1000.0, ARPT_lat=20.0, ARPT_mag_var=6.20 WHERE ARPT_ident>='YQLUV'",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_update(
      db, &rows, "ARPT_up1", 3, 1, "ARPT_lon", 1000.0, "ARPT_lat", 20.0, "ARPT_mag_var", 6.20, "ARPT_ident>=YQLUV");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 3;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT_up1", "ARPT_ident>=YQLUV");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_16(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char  updataTable[] = "6.16:多条件更新单条1";
  char *value[1]      = {"500.000000"};
  int   index[1]      = {1};

  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_lon=500.0 "
                         "WHERE ARPT_ident<=ESFPT AND ARPT_length<1593.597766";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT_up1 SET ARPT_lon=500.0 WHERE ARPT_ident<='ESFPT' AND ARPT_length<1593.597766",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT_up1", 1, 2, "ARPT_lon", 500.0, "ARPT_ident<=ESFPT", "ARPT_length<1593.597766");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  GNCDB_select(
      db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT_up1", "ARPT_ident<=ESFPT", "ARPT_length<1593.597766");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_17(GNCDB *db, TESTSUM *testSum)
{
  int   rc            = 0;
  int   rows          = 0;
  char  updataTable[] = "6.17:多条件更新单条2";
  char *value[1]      = {"1.100000"};
  int   index[1]      = {2};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_lat=1.1 "
                         "WHERE ARPT_ident<=ESFPT AND ARPT_length>2535.783166";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT_up1 SET ARPT_lat=1.1 WHERE ARPT_ident<='ESFPT' AND ARPT_length>2535.783166",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT_up1", 1, 2, "ARPT_lat", 1.1, "ARPT_ident<=ESFPT", "ARPT_length>2535.783166");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  GNCDB_select(
      db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT_up1", "ARPT_ident<=ESFPT", "ARPT_length>2535.783166");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_18(GNCDB *db, TESTSUM *testSum)
{
  int   rc            = 0;
  int   rows          = 0;
  char  updataTable[] = "6.18:多条件更新单条3";
  char *value[1]      = {"1.100000"};
  int   index[1]      = {4};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_length=1.1 "
                         "WHERE ARPT_lat<=33.750000 AND ARPT_lat>-11.250000";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT_up1 SET ARPT_length=1.1 WHERE ARPT_lat<=33.750000 AND ARPT_lat>-11.250000",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT_up1", 1, 2, "ARPT_length", 1.1, "ARPT_lat<=33.750000", "ARPT_lat>-11.250000");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT_up1", "ARPT_lat<=33.750000", "ARPT_lat>-11.250000");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_19(GNCDB *db, TESTSUM *testSum)
{
  int   rc            = 0;
  int   rows          = 0;
  char  updataTable[] = "6.19:多条件更新多条1";
  char *value[2]      = {"-10.000000", "30.000000"};
  int   index[2]      = {4, 3};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_length=-10.0 ARPT_elev=30.0 "
                         "WHERE ARPT_lat<=-33.750000 AND ARPT_lat>-72.421875";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT_up1 SET ARPT_length=-10.0, ARPT_elev=30.0 WHERE ARPT_lat<=-33.750000 AND ARPT_lat>-72.421875",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_update(db,
      &rows,
      "ARPT_up1",
      2,
      2,
      "ARPT_length",
      -10.0,
      "ARPT_elev",
      30.0,
      "ARPT_lat<=-33.750000",
      "ARPT_lat>-72.421875");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT_up1", "ARPT_lat<=-33.750000", "ARPT_lat>-72.421875");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_20(GNCDB *db, TESTSUM *testSum)
{
  int   rc            = 0;
  int   rows          = 0;
  char  updataTable[] = "6.20:多条件更新多条2";
  char *value[2]      = {"-10.000000", "2000.000000"};
  int   index[2]      = {3, 4};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT_up1 "
                         "SET ARPT_elev=-10.0 ARPT_length=2000.0 "
                         "WHERE ARPT_ident>=UBKNE AND ARPT_lon>72.421875 AND ARPT_lat<=54.140625";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT_up1 SET ARPT_elev=-10.0, ARPT_length=2000.0 WHERE ARPT_ident>='UBKNE' AND ARPT_lon>72.421875 AND "
      "ARPT_lat<=54.140625",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_update(db,
      &rows,
      "ARPT_up1",
      2,
      3,
      "ARPT_elev",
      -10.0,
      "ARPT_length",
      2000.0,
      "ARPT_ident>=UBKNE",
      "ARPT_lon>72.421875",
      "ARPT_lat<=54.140625");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db,
      testCallBackUpDate,
      &rows,
      NULL,
      1,
      0,
      3,
      "ARPT_up1",
      "ARPT_ident>=UBKNE",
      "ARPT_lon>72.421875",
      "ARPT_lat<=54.140625");

  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_21(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "6.21:更新属性与条件属性相同";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT_up2"
                         "SET WPT_lat=1000.0"
                         "WHERE WPT_lat=-22.500000";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lat=1000.0 WHERE WPT_lat=-22.500000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT_up2", 1, 1, "WPT_lat", 1000.0, "WPT_lat=-22.500000");
#endif
  test_table_count.rowcount = 0;
  rows                      = 0;
  GNCDB_select(db, testCallBack, &rows, NULL, 1, 0, 1, "WPT_up2", "WPT_lat=-22.500000");
  // GNCDB_select(db, testCallBack, &rows, NULL, 1, 0, 1, "WPT_up2", "WPT_lat=1000.0");
  if (rc == GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test6_22(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.22:更新real值大于最大值";

  test_table_count.SQL = "UPDATE WPT1 "
                         "SET WPT_lon=2000.0 "
                         "WHERE WPT_ident='CHONGQING'";

  currentTest(testSum, updataTable);

  rc = GNCDB_insert(db, NULL, "WPT1", "CHONGQING", 200.0, 100.0, 0, 0);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT1 SET WPT_lon=2000.0 WHERE WPT_ident='CHONGQING'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT1", 1, 1, "WPT_lon", 2000.0, "WPT_ident=CHONGQING");
#endif
  if (rc != GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_23(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.23:更新real值小于最小值";

  test_table_count.SQL = "UPDATE WPT1 "
                         "SET WPT_lat=-2000.0 "
                         "WHERE WPT_ident='CHONGQING'";

  currentTest(testSum, updataTable);

  GNCDB_insert(db, NULL, "WPT1", "CHONGQING", 200.0, 100.0, 0, 0);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT1 SET WPT_lat=-2000.0 WHERE WPT_ident='CHONGQING'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT1", 1, 1, "WPT_lat", -2000.0, "WPT_ident=CHONGQING");
#endif
  if (rc != GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_24(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.24:更新int值大于最大值";

  test_table_count.SQL = "UPDATE WPTTEST "
                         "SET TEXT_lat=501 "
                         "WHERE TEXT_ident='TESTROW1'";

  currentTest(testSum, updataTable);

  GNCDB_insert(db, NULL, "WPTTEST", "TESTROW1", 100.0, 100);
  GNCDB_insert(db, NULL, "WPTTEST", "TESTROW2", 100.0, 100);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPTTEST SET TEXT_lat=501 WHERE TEXT_ident='TESTROW1'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPTTEST", 1, 1, "TEXT_lat", 501, "TEXT_ident=TESTROW1");
#endif
  if (rc != GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_25(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.25:更新int值小于最小值";

  test_table_count.SQL = "UPDATE WPTTEST "
                         "SET TEXT_lat=-502 "
                         "WHERE TEXT_ident='TESTROW2'";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPTTEST SET TEXT_lat=-502 WHERE TEXT_ident='TESTROW2'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPTTEST", 1, 1, "TEXT_lat", -502, "TEXT_ident=TESTROW2");
#endif
  if (rc != GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_26(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.26:不能为空值更新为空";

  test_table_count.SQL = "UPDATE WPTTEST "
                         "SET TEXT_lon=NULL "
                         "WHERE TEXT_ident='TESTROW2'";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPTTEST SET TEXT_lon=NULL WHERE TEXT_ident=TESTROW2", NULL, &rows, &errmsg);
#else
  // rc = GNCDB_update(db, &rows, "WPTTEST", 1, 1, "TEXT_lon", NULL, "TEXT_ident='TESTROW2'");
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "WPTTEST");
#endif
  if (rc != GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_27(GNCDB *db, TESTSUM *testSum)
{
  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char updataTable[] = "6.27:int值可以为空更新为空值";

  test_table_count.SQL = "UPDATE attrisnull "
                         "SET attr2=NULL "
                         "attr1=4";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE attrisnull SET attr2=NULL WHERE attr1=4", NULL, &rows, &errmsg);
#else
  // rc = GNCDB_update(db, &rows, "attrisnull", 1, 1, "attr2", NULL, "attr1=4");

  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "attrisnull");
#endif
  if (rc == GNCDB_SUCCESS && rows == 1) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_28(GNCDB *db, TESTSUM *testSum)
{
  int  rc            = 0;
  int  rows          = 0;
  char updataTable[] = "6.28:real值可以为空更新为空值";

  test_table_count.SQL = "UPDATE attrisnull "
                         "SET attr3=NULL "
                         "attr1=4";

  currentTest(testSum, updataTable);
  // rc = GNCDB_update(db, &rows, "attrisnull", 1, 1, "attr3", NULL, "attr1=4");

  if (rc == GNCDB_SUCCESS && rows == 1) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_29(GNCDB *db, TESTSUM *testSum)
{
  int  rc            = 0;
  int  rows          = 0;
  char updataTable[] = "6.29:字符串可以为空更新为空值";

  test_table_count.SQL = "UPDATE attrisnull "
                         "SET attr4=NULL "
                         "attr1=4";

  currentTest(testSum, updataTable);
  // rc = GNCDB_update(db, &rows, "attrisnull", 1, 1, "attr4", NULL, "attr1=4");

  if (rc == GNCDB_SUCCESS && rows == 1) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_30(GNCDB *db, TESTSUM *testSum)
{
  int  rc            = 0;
  int  rows          = 0;
  char updataTable[] = "6.30:定长字符串更新为小于长度的字符串";

  test_table_count.SQL = "UPDATE stringmaxmineq "
                         "SET attr2='bbbbb' "
                         "attr1=1";

  currentTest(testSum, updataTable);

  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "stringmaxmineq");
  GNCDB_update(db, &rows, "stringmaxmineq", 1, 1, "attr2", "bbbbb", "attr1=1");

  if (rc != GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test6_31(GNCDB *db, TESTSUM *testSum)
{
  int  rc            = 0;
  int  rows          = 0;
  char updataTable[] = "6.31:定长字符串更新为大于长度的字符串";

  test_table_count.SQL = "UPDATE stringmaxmineq "
                         "SET attr2='bbbbbccccccc' "
                         "attr1=1";

  currentTest(testSum, updataTable);

  // 这里的更新操作可能需要特殊处理，根据数据库库的要求进行设置
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "stringmaxmineq");
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "stringmaxmineq");
  // GNCDB_update(db, &rows, "stringmaxmineq", 1, 1, "attr2", "bbbbbccccccc", "attr1=1");

  if (rc != GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int testUpdataTable(GNCDB *db, TESTSUM *testSum)
{
  test_table_count.testProject = "6:更新测试";
  test6_1(db, testSum);
  test6_2(db, testSum);
  test6_3(db, testSum);
  test6_4(db, testSum);
  test6_5(db, testSum);
  test6_6(db, testSum);
  test6_7(db, testSum);
  test6_8(db, testSum);
  test6_9(db, testSum);
  test6_10(db, testSum);
  test6_11(db, testSum);
  test6_12(db, testSum);
  test6_13(db, testSum);
  test6_14(db, testSum);
  test6_15(db, testSum);
  test6_16(db, testSum);
  test6_17(db, testSum);
  test6_18(db, testSum);
  test6_19(db, testSum);
  test6_20(db, testSum);
  test6_21(db, testSum);
  test6_22(db, testSum);
  test6_23(db, testSum);
  test6_24(db, testSum);
  test6_25(db, testSum);
  /*test6_26(db, testSum);
  test6_27(db, testSum);
  test6_28(db, testSum);
  test6_29(db, testSum);
  test6_30(db, testSum);
  test6_31(db, testSum);*/

  return 0;
}

int test7_1(GNCDB *db, TESTSUM *testSum)
{
  char   deleteTable[] = "7.1:全删除";
  int    rc            = 0;
  double min           = -10000.0;
  double max           = 10000.0;
  char  *path          = NULL;
  FILE  *fileData      = NULL;
  int    i             = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de1";
  currentTest(testSum, deleteTable);

  rc = GNCDB_createTable(db,
      "WPT_de1",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 400; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "WPT_de1", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de1", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de1", 0);
#endif
  if (rc == GNCDB_SUCCESS) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  return 0;
}

int test7_2(GNCDB *db, TESTSUM *testSum)
{
  char   deleteTable[] = "7.2:主键删除等于";
  int    rc            = 0;
  double min           = -10000.0;
  double max           = 10000.0;
  char  *path          = NULL;
  FILE  *fileData      = NULL;
  int    i             = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2"
                         "WHERE WPT_ident=TNLKZ";
  currentTest(testSum, deleteTable);
  rc = GNCDB_createTable(db,
      "WPT_de2",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 400; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "WPT_de2", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
  my_free(path);
  rc = GNCDB_createTable(db,
      "ARPT_de1",
      6,
      "ARPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "ARPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_elev",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_length",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_mag_var",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName2);
  if (path == NULL) {
    return -1;
  }
  fileData = fopen(path, "r");
  for (i = 0; i < 500; ++i) {
    fscanf(fileData,
        "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
        arpt.sc8_arpt_ident,
        &arpt.f64_lon,
        &arpt.f64_lat,
        &arpt.f64_elev,
        &arpt.f64_longest_rwy_length,
        &arpt.f64_mag_var);
    rc = GNCDB_insert(db,
        NULL,
        "ARPT_de1",
        arpt.sc8_arpt_ident,
        arpt.f64_lon,
        arpt.f64_lat,
        arpt.f64_elev,
        arpt.f64_longest_rwy_length,
        arpt.f64_mag_var);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de2 WHERE WPT_ident='TNLKZ'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de2", 1, "WPT_ident=TNLKZ");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de2", "WPT_ident=TNLKZ");
  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  return 0;
}

int test7_3(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.3:主键删除小于";  // 更新测试用例名称

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2 "
                         "WHERE WPT_ident<EBTKZ";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de2 WHERE WPT_ident<'EBTKZ'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de2", 1, "WPT_ident<EBTKZ");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de2", "WPT_ident<EBTKZ");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_4(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.4:主键删除大于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2 "
                         "WHERE WPT_ident>SCSZJ";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de2 WHERE WPT_ident>'SCSZJ'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de2", 1, "WPT_ident>SCSZJ");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de2", "WPT_ident>SCSZJ");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_5(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.5:主键删除小于等于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM ARPT_de1 "
                         "WHERE ARPT_ident<=HILDY";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT_de1 WHERE ARPT_ident<='HILDY'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT_de1", 1, "ARPT_ident<=HILDY");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT_de1", "ARPT_ident<=HILDY");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_6(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.6:主键删除大于等于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif
  test_table_count.SQL = "DELETE FROM ARPT_de1 "
                         "WHERE ARPT_ident>=VLSTG";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT_de1 WHERE ARPT_ident>='VLSTG'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT_de1", 1, "ARPT_ident>=VLSTG");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT_de1", "ARPT_ident>=VLSTG");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_7(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.7:非主键删除等于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2 "
                         "WHERE WPT_lat=-22.500000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de2 WHERE WPT_lat=-22.500000", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de2", 1, "WPT_lat=-22.500000");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de2", "WPT_lat=-22.500000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_8(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.8:非主键删除小于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2 "
                         "WHERE WPT_lon<-26.718750";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de2 WHERE WPT_lon<-26.718750", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de2", 1, "WPT_lon<-26.718750");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de2", "WPT_lon<-26.718750");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_9(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.9:非主键删除大于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2 "
                         "WHERE WPT_lon>45.000000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de2 WHERE WPT_lon>45.000000", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de2", 1, "WPT_lon>45.000000");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de2", "WPT_lon>45.000000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_10(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.10:非主键删除小于等于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM ARPT_de1 "
                         "WHERE ARPT_elev<=5.783288";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT_de1 WHERE ARPT_elev<=5.783288", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT_de1", 1, "ARPT_elev<=5.783288");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT_de1", "ARPT_elev<=5.783288");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_11(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.11:非主键删除大于等于";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM ARPT_de1 "
                         "WHERE ARPT_lon>=90.000000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT_de1 WHERE ARPT_lon>=90.000000", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT_de1", 1, "ARPT_lon>=90.000000");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT_de1", "ARPT_lon>=90.000000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_12(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.12:多条件删除1";

  int rc = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM ARPT_de1 "
                         "WHERE ARPT_lon=-135.000000  ARPT_lat=-16.875000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT_de1 WHERE ARPT_lon=-135.000000 AND ARPT_lat=-16.875000", NULL, NULL, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT_de1", 2, "ARPT_lon=-135.000000", "ARPT_lat=-16.875000");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 2, "ARPT_de1", "ARPT_lon=-135.000000", "ARPT_lat=-16.875000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_13(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.13:多条件删除2";

  int rc         = 0;
  int rows       = 0;
  int selectrows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "DELETE FROM ARPT_de1 "
                         "WHERE ARPT_lon<=56.000000  ARPT_lat<=100.875000";

  currentTest(testSum, deleteTable);

//    GNCDB_select(db, testCallBack, &selectrows, NULL, 1, 0, 2, "ARPT_de1", "ARPT_lon<=56.000000",
//    "ARPT_lat<=100.875000");
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT_de1 WHERE ARPT_lon<=56.000000 AND ARPT_lat<=100.875000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT_de1", 2, "ARPT_lon<=56.000000", "ARPT_lat<=100.875000");
#endif
  test_table_count.rowcount = 0;

  //    initFlag();
  //    GNCDB_select(db, testCallBack, &selectrows, NULL, 1, 0, 2, "ARPT_de1", "ARPT_lon<=56.000000",
  //    "ARPT_lat<=100.875000");

  GNCDB_select(
      db, testCallBackDelete, &selectrows, NULL, 1, 0, 2, "ARPT_de1", "ARPT_lon<=56.000000", "ARPT_lat<=100.875000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_14(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.14:多条件删除3";

  int rc = 0;

#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  int rows = 0;
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2 "
                         "WHERE WPT_lon>-20.000000  WPT_lon<56.000000  WPT_lat>-10.875000  WPT_lat<100.875000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "DELETE FROM WPT_de2 WHERE WPT_lon>-20.000000 AND WPT_lon<56.000000 AND WPT_lat>-10.875000 AND "
      "WPT_lat<100.875000",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_delete(
      db, &rows, "WPT_de2", 4, "WPT_lon>-20.000000", "WPT_lon<56.000000", "WPT_lat>-10.875000", "WPT_lat<100.875000");
#endif
  test_table_count.rowcount = 0;

  GNCDB_select(db,
      testCallBackDelete,
      NULL,
      NULL,
      1,
      0,
      4,
      "WPT_de2",
      "WPT_lon>-20.000000",
      "WPT_lon<56.000000",
      "WPT_lat>-10.875000",
      "WPT_lat<100.875000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_15(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.15:删除表名不存在";

  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_noEx "
                         "WHERE WPT_lat<100.875000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_noEx WHERE WPT_lat<100.875000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_noEx", 1, "WPT_lat<100.875000");
#endif

  if (rc == GNCDB_TABLE_NOT_FOUND) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_16(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.16:删除属性不存在";

  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de2 "
                         "WHERE WPT_name=XIAN";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de2 WHERE WPT_name='XIAN'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de2", 1, "WPT_name=XIAN");
#endif

  if (rc == GNCDB_FIELD_NOT_EXIST) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_17(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.17:删除含有大对象的记录";

  int    rc      = 0;
  int    rows    = 0;
  FILE  *fp      = NULL;
  size_t fileLen = 0;
#ifdef SQL_TESTMODE
  // char* errmsg = NULL;
#else
#endif
  BYTE *buffer         = NULL;
  char  filename[]     = "blob.png";
  char *path           = strJoin(testBlobPath, filename);
  test_table_count.SQL = "DELETE FROM WPT"
                         "WHERE WPT_ident=TXPTQ";
  currentTest(testSum, deleteTable);

  if (path == NULL) {
    return -1;
  }

  fp = fopen(path, "rb");

  if (fp == NULL) {
    addTest(testSum, -111);
    return -1;
  }

  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    addTest(testSum, -111);
    return -1;
  }

  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "TXPTQ");

  rc = GNCDB_delete(db, &rows, "WPT", 1, "WPT_ident=TXPTQ");

  if (rc == GNCDB_BLOB_NO_DELETE) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  my_free(path);
  my_free(buffer);

  return 0;
}

int test7_18(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.18:对某个表执行乱序全删除";

  int rc         = 0;
  int rows       = 0;
  int rowsSelect = 0;

  double min      = -10000.0;
  double max      = 10000.0;
  char  *path     = NULL;
  FILE  *fileData = NULL;
  int    i        = 0;
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  char condition[20] = {0};
#endif

  test_table_count.SQL = "DELETE FROM ARPT_RNG"
                         "WHERE condition";
  currentTest(testSum, deleteTable);

  rc = GNCDB_createTable(db,
      "ARPT_RNG",
      6,
      "ARPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "ARPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_elev",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_length",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "ARPT_mag_var",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName2);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");

  for (i = 0; i < 8000; ++i) {
    fscanf(fileData,
        "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
        arpt.sc8_arpt_ident,
        &arpt.f64_lon,
        &arpt.f64_lat,
        &arpt.f64_elev,
        &arpt.f64_longest_rwy_length,
        &arpt.f64_mag_var);

    rc = GNCDB_insert(db,
        NULL,
        "ARPT_RNG",
        arpt.sc8_arpt_ident,
        arpt.f64_lon,
        arpt.f64_lat,
        arpt.f64_elev,
        arpt.f64_longest_rwy_length,
        arpt.f64_mag_var);

    if (rc != GNCDB_SUCCESS) {
      my_free(path);
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);

  fileData = fopen(path, "r");
  my_free(path);
  for (i = 0; i < 8000; ++i) {
    fscanf(fileData,
        "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
        arpt.sc8_arpt_ident,
        &arpt.f64_lon,
        &arpt.f64_lat,
        &arpt.f64_elev,
        &arpt.f64_longest_rwy_length,
        &arpt.f64_mag_var);
#ifdef SQL_TESTMODE
    sprintf(sql, "DELETE FROM ARPT_RNG WHERE ARPT_ident=%s", arpt.sc8_arpt_ident);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
    sprintf(condition, "ARPT_ident=%s", arpt.sc8_arpt_ident);
    rc = GNCDB_delete(db, &rows, "ARPT_RNG", 1, condition);
#endif
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      fclose(fileData);
      return -1;
    }
  }
  fclose(fileData);

  rc = GNCDB_select(db, testCallBack, &rowsSelect, NULL, 1, 0, 0, "ARPT_RNG");

  if (rc == GNCDB_SUCCESS && rows == 8000) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test7_19(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "7.19:删除大对象之后删除记录";
  int  rc            = 0;
  int  rows          = 0;

  char   filename[]    = "blob.mp3";
  char  *path          = NULL;
  FILE  *fp            = NULL;
  size_t fileLen       = 0;
  BYTE  *buffer        = NULL;
  int    rowsSelect    = 0;
  test_table_count.SQL = "DELETE FROM WPT"
                         "WHERE WPT_ident=ZWMTA";

  currentTest(testSum, deleteTable);

  path = strJoin(testBlobPath, filename);
  if (path == NULL) {
    return -1;
  }

  fp = fopen(path, "rb");
  if (fp == NULL) {
    addTest(testSum, -111);
    return -1;
  }
  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    addTest(testSum, -111);
    return -1;
  }
  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "ZWMTA");
  if (rc != GNCDB_SUCCESS) {
    my_free(path);
    my_free(buffer);
    addTest(testSum, rc);
    return -1;
  }

  rc = GNCDB_deleteBlob(db, "WPT", 3, 1, "ZWMTA");
  if (rc != GNCDB_SUCCESS) {
    my_free(path);
    my_free(buffer);
    addTest(testSum, rc);
    return -1;
  }

  test_table_count.rowcount = 1;
  GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=ZWMTA");

  rc = GNCDB_delete(db, &rows, "WPT", 1, "WPT_ident=ZWMTA");
  GNCDB_select(db, testCallBack, &rowsSelect, NULL, 1, 0, 1, "WPT", "WPT_ident=ZWMTA");
  if (rc == GNCDB_SUCCESS && rows == 1 && rowsSelect == 0 && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path);
  my_free(buffer);
  return 0;
}

int testDeleteTable(GNCDB *db, TESTSUM *testSum)
{
  test_table_count.testProject = "7:删除测试";

  test7_1(db, testSum);
  test7_2(db, testSum);
  test7_3(db, testSum);
  test7_4(db, testSum);
  test7_5(db, testSum);
  test7_6(db, testSum);
  test7_7(db, testSum);
  test7_8(db, testSum);
  test7_9(db, testSum);
  test7_10(db, testSum);
  test7_11(db, testSum);
  test7_12(db, testSum);
  test7_13(db, testSum);
  test7_14(db, testSum);
  test7_15(db, testSum);
  test7_16(db, testSum);
  test7_17(db, testSum);
  test7_18(db, testSum);
  test7_19(db, testSum);

  return 0;
}

int test8_1(GNCDB *db, TESTSUM *testSum, char *dropTable)
{
  int rc   = 0;
  int rows = 0;

  test_table_count.SQL = "DROP TABLE WPT_up1;";

  currentTest(testSum, dropTable);
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "master", "tableName=WPT_up1");
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "master", "tableName=WPT_up1");

  rc = GNCDB_dropTable(db, "WPT_up1");

  /* 查询master表中是否存在 */
  GNCDB_select(db, testCallBackMasterTable, &rows, NULL, 1, 0, 1, "master", "tableName=WPT_up1");

  if (rc == GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test8_2(GNCDB *db, TESTSUM *testSum, char *dropTable)
{
  int  rc              = 0;
  int  rows            = 0;
  int  i               = 0;
  int  num             = 0;
  char name[10]        = "WPT";
  char fileName[15]    = {0};
  char condition[32]   = {0};
  test_table_count.SQL = "DROP TABLE tableName;";

  currentTest(testSum, dropTable);
  rc = GNCDB_dropTable(db, "ARPT_de1");
  rc = GNCDB_dropTable(db, "WPT_de2");
  rc = GNCDB_dropTable(db, "WPT_de1");

  for (i = 0; i < DROP_TABLE_NUM; ++i) {
    num = i + 5;
    sprintf(fileName, "%s%d", name, num);
    rc = GNCDB_dropTable(db, fileName);
    sprintf(condition, "tableName=%s", fileName);
    /* 查询master表中是否存在 */
    GNCDB_select(db, testCallBackMasterTable, &rows, NULL, 1, 0, 1, "master", condition);
    if (rows != 0) {
      if (rc == GNCDB_SUCCESS) {
        rc = -1;
      }
      addTest(testSum, rc);
      return 0;
    }
  }

  addTest(testSum, rc);

  return 0;
}

int test8_3(GNCDB *db, TESTSUM *testSum, char *dropTable)
{
  int rc               = 0;
  test_table_count.SQL = "DROP TABLE WPT_not;";

  currentTest(testSum, dropTable);

  rc = GNCDB_dropTable(db, "WPT_not");
  if (rc == GNCDB_TABLE_NOT_FOUND) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test8_4(GNCDB *db, TESTSUM *testSum, char *dropTable)
{
  int rc               = 0;
  test_table_count.SQL = "DROP TABLE master;";

  currentTest(testSum, dropTable);
  rc = GNCDB_dropTable(db, "master");

  if (rc == GNCDB_SYSTEMTABLE_NOTREMOVE) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test8_5(GNCDB *db, TESTSUM *testSum, char *dropTable)
{
  int rc               = 0;
  test_table_count.SQL = "DROP TABLE schema;";

  currentTest(testSum, dropTable);
  rc = GNCDB_dropTable(db, "schema");

  if (rc == GNCDB_SYSTEMTABLE_NOTREMOVE) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int debugTestDrop(GNCDB *db)
{
  int    rc       = 0;
  double min      = -10000.0;
  double max      = 10000.0;
  char  *path     = NULL;
  FILE  *fileData = NULL;
  int    i        = 0;

  rc = GNCDB_createTable(db,
      "dropWpt",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      0.0,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 100; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "dropWpt", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      return -1;
    }
  }
  fclose(fileData);

  rc = GNCDB_dropTable(db, "dropWpt");

  my_free(path);
  return rc;
}

int testDropTable(GNCDB *db, TESTSUM *testSum)
{
  char dropTable1[] = "8.1:销毁存在的表";
  char dropTable2[] = "8.2:销毁多个表";
  char dropTable3[] = "8.3:销毁不存在的表";
  char dropTable4[] = "8.4:销毁master表";
  char dropTable5[] = "8.5:销毁schema表";

  test_table_count.testProject = "8:销毁表测试";
  // debugTestDrop(db);
  test8_1(db, testSum, dropTable1);
  test8_2(db, testSum, dropTable2);
  test8_3(db, testSum, dropTable3);
  test8_4(db, testSum, dropTable4);
  test8_5(db, testSum, dropTable5);

  return 0;
}

int test9_1(GNCDB **db, TESTSUM *testSum)
{
  int   rc         = GNCDB_SUCCESS;
  char  openTest[] = "9.1:打开已有数据库";
  char *filename   = "dbtest.dat";

  test_table_count.SQL = "USE dbtest.dat";

  currentTest(testSum, openTest);

  rc = GNCDB_close(db);

  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return -1;
  }
  rc                  = GNCDB_open(db, filename, 0, 0);
  test_table_count.db = *db;

  addTest(testSum, rc);

  return 0;
}

int test9_2(GNCDB **db, TESTSUM *testSum)
{
  char  createTable[] = "9.2:重新打开数据库查看表是否正常";
  int   rc            = 0;
  char *filename      = "dbtest.dat";
  int   row           = 0;
  int   rc1           = 0;
  int   rc2           = 0;
  char *WPT_field[4]  = {"WPT_ident", "WPT_lon", "WPT_lat", "WPT_blob"};

  test_table_count.SQL = "SELECT * FROM master WHERE tableName=WPT";
  currentTest(testSum, createTable);

  rc = GNCDB_close(db);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    // currentTest(testSum, "-----------------------------------------------------\n");
    return -1;
  }
  remove("log.dat");
  rc = GNCDB_open(db, filename, 0, 0);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    currentTest(testSum, "-----------------------------------------------------\n");
    return -1;
  }

  test_table_count.db        = *db;
  test_table_count.tableName = "WPT";
  test_table_count.fieldName = WPT_field;
  test_table_count.rowNum    = 4;
  test_table_count.rowcount  = 0;
  /* 查询master表中是否存在 */
  rc1 = GNCDB_select(*db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", "tableName=WPT");
  /* 查询schema表中属性是否存在 */
  rc2 = GNCDB_select(*db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", "tableName=WPT");

  if (tableExist && (rc | rc1 | rc2) == GNCDB_SUCCESS && row == 1) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  tableExist = false;
  return 0;
}

int test9_3(GNCDB **db, TESTSUM *testSum)
{
  char  openTest[]     = "9.3 :重复打开数据库";
  char *filename       = "dbtest.dat";
  int   i              = 0;
  int   rc             = 0;
  test_table_count.SQL = "USE dbtest.dat";

  currentTest(testSum, openTest);
  for (; i < REPEAT_COUNT; ++i) {
    rc = GNCDB_close(db);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
    filename = "dbtest.dat";
    remove("log_dbtest.dat");
    rc = GNCDB_open(db, filename, 0, 0);
  }

  test_table_count.db = *db;
  addTest(testSum, rc);
  return 0;
}

int test9_4(GNCDB **db, TESTSUM *testSum)
{
  char   openTest[]    = "9.4 :打开错误的文件";
  char  *filename      = "blob.png";
  GNCDB *dbtest        = NULL;
  int    rc            = 0;
  test_table_count.SQL = "USE blob.png";

  currentTest(testSum, openTest);
  rc = GNCDB_open(&dbtest, filename, 0, 0);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, 0);
  } else {
    addTest(testSum, -1);
  }
  return 0;
}

int test9_5(GNCDB **db, TESTSUM *testSum)
{
  char  createTable[]  = "9.5:重新打开数据库查看表中数据是否正常";
  int   rc             = 0;
  char *filename       = "dbtest.dat";
  char *testvalue[3]   = {"张三", "175.000000", "53"};
  test_table_count.SQL = "SELECT * FROM WPTTEST WHERE TEXT_lon=175.700000";
  currentTest(testSum, createTable);

  rc = GNCDB_close(db);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return -1;
  }
  remove("log.dat");
  rc = GNCDB_open(db, filename, 0, 0);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return -1;
  }

  test_table_count.tableName = "WPTTEST";
  test_table_count.fieldName = testvalue;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;
  test_table_count.testFlag  = false;

  // GNCDB_select(*db, testCallBack, NULL, NULL, 1, 0, 0, "WPTTEST");
  rc = GNCDB_select(*db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "WPTTEST", "TEXT_lat=53");
  // rc = -1;
  test_table_count.db = *db;
  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test9_6(GNCDB **db, TESTSUM *testSum)
{
  int  rc            = 0;
  int  row           = 0;
  int  rc1           = 0;
  int  rc2           = 0;
  char condition[20] = {0};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
  double min = -10000.0;
  double max = 10000.0;
#endif
  char *WPT_num_field[4] = {"WPT_ident", "WPT_lon", "WPT_lat", "WPT_blob"};

  char createTable[]   = "9.6:打开数据库创建表";
  test_table_count.SQL = "CREATE TABLE WPT_open("
                         "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL  "
                         "WPT_lon FLOAT  NOT NULL "
                         "WPT_lat  FLOAT	NOT NULL "
                         "WPT_blob  INT);";

  currentTest(testSum, createTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(*db,
      "CREATE TABLE WPT_open(WPT_ident CHAR(10) PRIMARY KEY NOT NULL,"
      "WPT_lon FLOAT  NOT NULL,"
      "WPT_lat  FLOAT	NOT NULL,"
      "WPT_blob  INT);",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_createTable(*db,
      "WPT_open",
      4,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      10.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      -1000.0,
      1000.0,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      -1000.0,
      max,
      "WPT_blob",
      FIELDTYPE_BLOB,
      0,
      0,
      min,
      max,
      10);
#endif
  test_table_count.tableName = "WPT_open";
  test_table_count.fieldName = WPT_num_field;
  test_table_count.rowNum    = 4;
  test_table_count.rowcount  = 0;
  sprintf(condition, "tableName=WPT_open");

  /* 查询master表中是否存在 */
  rc1 = GNCDB_select(*db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", condition);
  /* 查询schema表中属性是否存在 */
  rc2 = GNCDB_select(*db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", condition);

  // initFlag();
  // GNCDB_exec(*db, "select * from master", testCallBack, NULL, NULL);
  // initFlag();
  // GNCDB_exec(*db, "select * from schema", testCallBack, NULL, NULL);

  if ((rc | rc1 | rc2) == GNCDB_SUCCESS && tableExist == true) {
    addTest(testSum, rc);
    tableExist = false;
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    tableExist = false;
  }
  return 0;
}

int test9_7(GNCDB **db, TESTSUM *testSum)
{

  int   rc               = 0;
  int   num              = 0;
  int   row              = 0;
  char *WPT_num_field[4] = {"WPT_ident", "WPT_lon", "WPT_lat", "WPT_blob"};
  int   rc1              = 0;
  int   rc2              = 0;
  char  createTable[]    = "9.7:打开创建多张表";
  char  name[10]         = "WPT";
  char  tableName[15]    = {0};
  int   i                = 0;
  char  condition[30]    = {0};
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  double min = -10000.0;
  double max = 10000.0;
#endif
  test_table_count.SQL = "CREATE TABLE WPTi("
                         "WPT_ident  CHAR(10) PRIMARY KEY  NOT NULL  "
                         "WPT_lon FLOAT  NOT NULL  "
                         "WPT_lat  FLOAT	NOT NULL  "
                         "WPT_blob  INT);";
  currentTest(testSum, createTable);

  for (i = 10; i < DROP_TABLE_NUM; ++i) {
    num = i;
    sprintf(tableName, "%s%d", name, num);
#ifdef SQL_TESTMODE
    sprintf(sql,
        "CREATE TABLE %s(WPT_ident CHAR(10) PRIMARY KEY NOT NULL,"
        "WPT_lon FLOAT  NOT NULL,"
        "WPT_lat  FLOAT	NOT NULL, "
        "WPT_blob  INT);",
        tableName);
    rc = GNCDB_exec(*db, sql, NULL, NULL, &errmsg);
#else
    rc = GNCDB_createTable(*db,
        tableName,
        4,
        "WPT_ident",
        FIELDTYPE_VARCHAR,
        0,
        1,
        min,
        10.0,
        "WPT_lon",
        FIELDTYPE_REAL,
        0,
        0,
        -1000.0,
        1000.0,
        "WPT_lat",
        FIELDTYPE_REAL,
        0,
        0,
        -1000.0,
        max,
        "WPT_blob",
        FIELDTYPE_BLOB,
        0,
        0,
        min,
        max,
        10);
#endif
    row                        = 0;
    test_table_count.tableName = tableName;
    test_table_count.fieldName = WPT_num_field;
    test_table_count.rowNum    = 4;
    test_table_count.rowcount  = 0;
    sprintf(condition, "tableName=%s", tableName);
    /* 查询master表中是否存在 */
    rc1 = GNCDB_select(*db, testCallBackMasterTable, &row, NULL, 1, 0, 1, "master", condition);
    /* 查询schema表中属性是否存在 */
    rc2 = GNCDB_select(*db, testCallBackSchemaTable, NULL, NULL, 1, 0, 1, "schema", condition);

    if (tableExist && (rc | rc1 | rc2) == GNCDB_SUCCESS && row == 1) {

    } else {
      if (rc == GNCDB_SUCCESS) {
        rc = -1;
      }
      tableExist = false;
      break;
    }
  }
  addTest(testSum, rc);
  tableExist = false;
  return 0;
}

int testOpendb(GNCDB **db, TESTSUM *testSum)
{
  test_table_count.testProject = "9:打开数据库";
  test9_1(db, testSum);
  test9_2(db, testSum);
  test9_3(db, testSum);
  test9_5(db, testSum);
  test9_6(db, testSum);
  test9_7(db, testSum);

  return 0;
}

int test10_1(GNCDB *db, TESTSUM *testSum)
{
  char  insertTable[] = "10.1:新创建的表插入数据";
  char *path          = NULL;
  char *wptvalue[3]   = {0};
  FILE *fileData      = NULL;
  int   rc            = 0;
  int   i             = 0;
  char  lon[20]       = {0};
  char  lat[20]       = {0};
  char  str[100]      = {0};
#ifdef SQL_TESTMODE
  char  sql[1024] = {0};
  char *errmsg    = NULL;
#else
  BYTE *ImgBuffer = NULL;
#endif

  test_table_count.SQL = "INSERT INTO WPT_open(WPT_ident  WPT_lon  WPT_lat  WPT_blob)"
                         "VALUES(wpt.sc8_wpt_ident wpt.f64_lon wpt.f64_lat  NULL); ";

  currentTest(testSum, insertTable);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  if (fileData == NULL) {
    return -1;
  }

  for (i = 0; i < WPTROWS; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
#ifdef SQL_TESTMODE
    sprintf(sql,
        "INSERT INTO WPT_open(WPT_ident, WPT_lon, WPT_lat, WPT_blob)"
        "VALUES('%s', %f, %f, 0); ",
        wpt.sc8_wpt_ident,
        wpt.f64_lon,
        wpt.f64_lat);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
    rc = GNCDB_insert(db, NULL, "WPT_open", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat, 0, ImgBuffer);
#endif
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
    fileData = fopen(path, "r");
    for (i = 0; i < WPTROWS; ++i)
    {
        fscanf(fileData, "%[^,],%lf,%lf,\n",
                         wpt.sc8_wpt_ident,
                         &wpt.f64_lon,
                         &wpt.f64_lat);
        
        sprintf(lon, "%f", wpt.f64_lon);
        sprintf(lat, "%f", wpt.f64_lat);
        wptvalue[0] = wpt.sc8_wpt_ident;
        wptvalue[1] = lon;
        wptvalue[2] = lat;
        test_table_count.tableName = "WPT2";
        test_table_count.fieldName = wptvalue;
        test_table_count.rowNum = 3;
        test_table_count.rowcount = 0;
        test_table_count.testFlag = false;
        
        sprintf(str, "rowId=%d", i);
        //GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "WPT", str);
        GNCDB_select(db, testCallBackInsertTable, NULL, NULL, 1, 0, 1, "WPT_open", str);
    }
    fclose(fileData);

    if (rc == GNCDB_SUCCESS && test_table_count.testFlag)
    {
        addTest(testSum, rc);
    }
    else
    {
        if (rc == GNCDB_SUCCESS)
        {
            rc = -1;
        }
        addTest(testSum, rc);
    }
    my_free(path);

    return 0;
}

int test10_2(GNCDB* db, TESTSUM* testSum)
{
    char insertTable[] = "10.2:在已有数据的表中插入数据";
    char* path = NULL;
    FILE* fileData = NULL;
    int rc = 0;
    int rows = 0;
    // int rows = 0;
    int i = 0;
    // char lon[20] = { 0 };
    // char lat[20] = { 0 };
    // char* wptvalue[3] = {0};
    // char str[100] = { 0 };
#ifdef SQL_TESTMODE
    char sql[1024] = { 0 };
    char* errmsg = NULL;
#else
    BYTE* ImgBuffer = NULL;
#endif
    
    test_table_count.SQL = "INSERT INTO WPT(WPT_ident  WPT_lon  WPT_lat  WPT_blob)"
                           "VALUES(wpt.sc8_wpt_ident wpt.f64_lon wpt.f64_lat  NULL); ";

    currentTest(testSum, insertTable);

    path = strJoin(testFilePath, testFileName1);
    if (path == NULL)
    {
        return -1;
    }

    GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "WPT");

    fileData = fopen(path, "r");
    for (i = 0; i < WPTROWS + 200; ++i)
    {
        fscanf(fileData, "%[^,],%lf,%lf,\n",
                         wpt.sc8_wpt_ident,
                         &wpt.f64_lon,
                         &wpt.f64_lat);
        if (i < WPTROWS)
        {
            continue;
        }
#ifdef SQL_TESTMODE
        sprintf(sql, "INSERT INTO WPT VALUES('%s', %f, %f, 0); ",wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
        rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
#else
        rc = GNCDB_insert(db, NULL, "WPT",
                          wpt.sc8_wpt_ident,
                          wpt.f64_lon,
                          wpt.f64_lat,
                          0, ImgBuffer);
#endif
        if (rc == GNCDB_SUCCESS)
        {

        }
        else
        {
            addTest(testSum, rc);
            return -1;
        }
    }
    fclose(fileData);

    GNCDB_select(db, NULL, &test_table_count.rowcount, NULL, 1, 0, 0, "WPT");

    if (rc == GNCDB_SUCCESS && test_table_count.rowcount == rows + 200)
    {
        addTest(testSum, rc);
    }
    else
    {
        if (rc == GNCDB_SUCCESS)
        {
            rc = -1;
        }
        addTest(testSum, rc);
    }
    my_free(path);
    return 0;
}

int testOpendbInsert(GNCDB* db, TESTSUM* testSum)
{
    test_table_count.testProject = "10:打开数据库插入";
    test10_1(db, testSum);
    test10_2(db, testSum);

    return 0;
}

int test11_1(GNCDB* db, TESTSUM* testSum)
{
    char blobTest[] = "11.1:拿取图片文件";
    BYTE* bufferCopy = NULL;
    int rc = 0;
    char filename[] = "test11_1blob.png";
    char* path = NULL;
    FILE* fp = NULL;
    char fileName1[] = "blob.png";
    char* pathRoot = NULL;
    test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=BTZCE";
    currentTest(testSum, blobTest);

    test_table_count.rowcount = 0;
    /*GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "master");
    GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "WPT");*/
    GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BTZCE");
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=BTZCE");

    bufferCopy = (BYTE*)my_malloc(test_table_count.rowcount);
    if (bufferCopy == NULL)
    {
        addTest(testSum,  -111);
        return -1;
    }
    rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, test_table_count.rowcount, 1, "BTZCE");

    path = strJoin(resultBlobPath, filename);
    if (path == NULL)
    {
        return -1;
    }
    fp = fopen(path, "wb");
    if (fp == NULL)
    {
      my_free(path);
        return -1;
    }

    fwrite(bufferCopy, test_table_count.rowcount, 1, fp);

    fclose(fp);
    my_free(bufferCopy);

    pathRoot = strJoin(testBlobPath, fileName1);

    if (rc == GNCDB_SUCCESS && compare_files(pathRoot, path))
    {
        addTest(testSum, rc);
    }
    else
    {
        if (rc == GNCDB_SUCCESS)
        {
            rc = -1;
        }
        addTest(testSum, rc);
    }
    my_free(path);
    my_free(pathRoot);
    return 0;
}

int test11_2(GNCDB* db, TESTSUM* testSum)
{
    char blobTest[] = "11.2:拿取文本文件";
    BYTE* bufferCopy = NULL;
    int rc = 0;
    char filename[] = "test11_2blob.txt";
    char* path = NULL;
    FILE* fp = NULL;
    char fileName1[] = "blob.txt";
    char* pathRoot = NULL;
    test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=APJFO";
    currentTest(testSum, blobTest);

    test_table_count.rowcount = 0;
    //GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=APJFO");
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=APJFO");

    bufferCopy = (BYTE*)my_malloc(test_table_count.rowcount);
    if (bufferCopy == NULL)
    {
        addTest(testSum,  -111);
        return -1;
    }
    rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, test_table_count.rowcount, 1, "APJFO");
    path = strJoin(resultBlobPath, filename);
    if (path == NULL)
    {
        return -1;
    }
    fp = fopen(path, "wb");
    if (fp == NULL)
    {
        my_free(path);
        return -1;
    }

    // 写入数据到文件中
    fwrite(bufferCopy, test_table_count.rowcount, 1, fp);

    // 关闭文件
    fclose(fp);
    // 释放内存
    my_free(bufferCopy);
    pathRoot = strJoin(testBlobPath, fileName1);
    if (rc == GNCDB_SUCCESS && compare_files(pathRoot, path))
    {
        addTest(testSum, rc);
    }
    else
    {
        if (rc == GNCDB_SUCCESS)
        {
            rc = -1;
        }
        addTest(testSum, rc);
    }
    my_free(pathRoot);
    my_free(path);
    return 0;

}

int test11_3(GNCDB* db, TESTSUM* testSum)
{
    char blobTest[] = "11.3:拿取mp3文件";
    BYTE* bufferCopy = NULL;
    int rc = 0;
    char filename[] = "test11_3blob.mp3";
    char* path = NULL;
    FILE* fp = NULL;
    char fileName1[] = "blob.mp3";
    char* pathRoot = NULL;

    test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.mp3 WHERE WPT_ident=AFTTM";
    currentTest(testSum, blobTest);

    test_table_count.rowcount = 0;
    //GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=AFTTM");

    bufferCopy = (BYTE*)my_malloc(test_table_count.rowcount);
    if (bufferCopy == NULL)
    {
        addTest(testSum,  -111);
        return -1;
    }
    rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, test_table_count.rowcount, 1, "AFTTM");
    path = strJoin(resultBlobPath, filename);
    if (path == NULL)
    {
        return -1;
    }
    fp = fopen(path, "wb");
    if (fp == NULL)
    {
        my_free(path);
        return -1;
    }

    fwrite(bufferCopy, test_table_count.rowcount, 1, fp);

    fclose(fp);
    my_free(bufferCopy);
    pathRoot = strJoin(testBlobPath, fileName1);
    if (rc == GNCDB_SUCCESS && compare_files(pathRoot, path))
    {
        addTest(testSum, rc);
    }
    else
    {
        if (rc == GNCDB_SUCCESS)
        {
            rc = -1;
        }
        addTest(testSum, rc);
    }
    my_free(pathRoot);
    my_free(path);
    return 0;
}

int test11_4(GNCDB* db, TESTSUM* testSum)
{
    char blobTest[] = "11.4:拿取mp4文件";
    BYTE* bufferCopy = NULL;
    int rc = 0;
    char filename[] = "test11_4blob.mp4";
    char* path = NULL;
    FILE* fp = NULL;
    char fileName1[] = "blob.mp4";
    char* pathRoot = NULL;

    test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.mp4 WHERE WPT_ident=DZECO";
    currentTest(testSum, blobTest);

    test_table_count.rowcount = 0;
    //GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");
    GNCDB_select(db, testCallBackBlobSize, NULL, NULL, 1, 0, 1, "WPT", "WPT_ident=DZECO");

    bufferCopy = (BYTE*)my_malloc(test_table_count.rowcount);
    if (bufferCopy == NULL)
    {
        addTest(testSum,  -111);
        return -1;
    }
    rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, test_table_count.rowcount, 1, "DZECO");
    path = strJoin(resultBlobPath, filename);
    if (path == NULL)
    {
        return -1;
    }
    fp = fopen(path, "wb");
    if (fp == NULL)
    {
        my_free(path);
        return -1;
    }

    fwrite(bufferCopy, test_table_count.rowcount, 1, fp);

    fclose(fp);
    my_free(bufferCopy);
    pathRoot = strJoin(testBlobPath, fileName1);
    if (rc == GNCDB_SUCCESS && compare_files(pathRoot, path))
    {
        addTest(testSum, rc);
    }
    else
    {
        if (rc == GNCDB_SUCCESS)
        {
            rc = -1;
        }
        addTest(testSum, rc);
    }
    my_free(pathRoot);
    my_free(path);
    return 0;
}

int test11_5_6(GNCDB* db, TESTSUM* testSum)
{
    char blobTest1[] = "11.5:插入图片文件";
    char blobTest2[] = "11.6:拿取图片文件";
    char filename1[] = "blob.png";
    char* path1 = NULL;
    char filename2[] = "test11_5blob.png";
    char* path2 = NULL;
    FILE* fp = NULL;
    size_t fileLen = 0;
    BYTE* buffer = NULL;
    BYTE* bufferCopy = NULL;
    int rc = 0;

    test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.png WHERE WPT_ident=CQRCT";
    currentTest(testSum, blobTest1);
    path1 = strJoin(testBlobPath, filename1);
    if (path1 == NULL)
    {
        return -1;
    }
    fp = fopen(path1, "rb");
    if (fp == NULL)
    {
        my_free(path1);
        addTest(testSum,  -111);
        return -1;
    }
    fseek(fp, 0, SEEK_END);
    fileLen = ftell(fp);
    rewind(fp);

    if (fileLen > BLOBFILESIZE)
    {
        printf("File size exceeds limit\n");
        my_free(path1);
        return -1;
    }
    buffer = (BYTE*)my_malloc(fileLen);
    if (buffer == NULL)
    {
        my_free(path1);
        addTest(testSum,  -111);
        return -1;
    }
    fread(buffer, fileLen, 1, fp);
    fclose(fp);

    rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "CQRCT");
  addTest(testSum, rc);
  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=CQRCT";
  currentTest(testSum, blobTest2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  rc    = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "CQRCT");
  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }
  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }
  fwrite(bufferCopy, 1, fileLen, fp);
  fclose(fp);
  my_free(buffer);
  my_free(bufferCopy);
  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path1);
  my_free(path2);
  return 0;
}

int test11_7_8(GNCDB *db, TESTSUM *testSum)
{
  char   blobTest1[] = "11.7:插入文本文件";
  char   filename1[] = "blob.txt";
  char   blobTest2[] = "11.8:拿取文本文件";
  char  *path1       = NULL;
  BYTE  *bufferCopy  = NULL;
  FILE  *fp          = NULL;
  size_t fileLen     = 0;
  BYTE  *buffer      = NULL;
  int    rc          = 0;
  char   filename2[] = "test11_7blob.txt";
  char  *path2       = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.txt WHERE WPT_ident=HMSAN";
  currentTest(testSum, blobTest1);
  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "HMSAN");

  addTest(testSum, rc);

  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=HMSAN";

  currentTest(testSum, blobTest2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  rc    = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "HMSAN");
  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }
  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }
  fwrite(bufferCopy, fileLen, 1, fp);
  fclose(fp);
  my_free(buffer);
  my_free(bufferCopy);
  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path1);
  my_free(path2);
  return 0;
}

int test11_9_10(GNCDB *db, TESTSUM *testSum)
{
  char   blobTest1[] = "11.9:插入mp3文件";
  char   filename1[] = "blob.mp3";
  char  *path1       = NULL;
  FILE  *fp          = NULL;
  size_t fileLen     = 0;
  BYTE  *buffer      = NULL;
  int    rc          = 0;
  char   blobTest2[] = "11.10:拿取mp3文件";
  BYTE  *bufferCopy  = NULL;
  char   filename2[] = "test11_9blob.mp3";
  char  *path2       = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.mp3 WHERE WPT_ident=SVAUL";
  currentTest(testSum, blobTest1);
  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }
  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "SVAUL");

  addTest(testSum, rc);

  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=SVAUL";
  currentTest(testSum, blobTest2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  rc    = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "SVAUL");
  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }
  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }
  fwrite(bufferCopy, fileLen, 1, fp);
  fclose(fp);
  my_free(buffer);
  my_free(bufferCopy);
  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path1);
  my_free(path2);
  return 0;
}

int test11_11_12(GNCDB *db, TESTSUM *testSum)
{
  char   blobTest1[] = "11.11:插入mp4文件";
  char   blobTest2[] = "11.12:拿取mp4文件";
  char   filename1[] = "blob.mp4";
  char  *path1       = NULL;
  FILE  *fp          = NULL;
  size_t fileLen     = 0;
  BYTE  *buffer      = NULL;
  int    rc          = 0;
  BYTE  *bufferCopy  = NULL;
  char   filename2[] = "test11_11blob.mp4";
  char  *path2       = NULL;

  test_table_count.SQL = "UPDATE WPT SET WPT_blob=blob.mp4 WHERE WPT_ident=IBKKN";
  currentTest(testSum, blobTest1);
  path1 = strJoin(testBlobPath, filename1);
  if (path1 == NULL) {
    return -1;
  }

  fp = fopen(path1, "rb");
  if (fp == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fseek(fp, 0, SEEK_END);
  fileLen = ftell(fp);
  rewind(fp);

  buffer = (BYTE *)my_malloc(fileLen);
  if (buffer == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  fread(buffer, fileLen, 1, fp);
  fclose(fp);

  rc = GNCDB_setBlob(db, "WPT", 3, buffer, fileLen, 1, "IBKKN");

  addTest(testSum, rc);

  test_table_count.SQL = "SELECT WPT_blob FROM WPT WHERE WPT_ident=IBKKN";
  currentTest(testSum, blobTest2);

  bufferCopy = (BYTE *)my_malloc(fileLen);
  if (bufferCopy == NULL) {
    my_free(path1);
    addTest(testSum, -111);
    return -1;
  }
  rc = GNCDB_getBlob(db, "WPT", 3, bufferCopy, fileLen, 1, "IBKKN");

  path2 = strJoin(resultBlobPath, filename2);
  if (path2 == NULL) {
    my_free(path1);
    return -1;
  }
  fp = fopen(path2, "wb");
  if (fp == NULL) {
    my_free(path1);
    my_free(path2);
    return -1;
  }
  fwrite(bufferCopy, fileLen, 1, fp);
  fclose(fp);
  my_free(buffer);
  my_free(bufferCopy);
  if (rc == GNCDB_SUCCESS && compare_files(path1, path2)) {
    addTest(testSum, rc);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path1);
  my_free(path2);

  return 0;
}

int testOpendbBlob(GNCDB *db, TESTSUM *testSum)
{
  test_table_count.testProject = "11:打开数据库测blob";
  test11_1(db, testSum);
  test11_2(db, testSum);
  test11_3(db, testSum);
  test11_4(db, testSum);
  test11_5_6(db, testSum);
  test11_7_8(db, testSum);
  test11_9_10(db, testSum);
  test11_11_12(db, testSum);

  return 0;
}

int testOpendbSelect(GNCDB *db, TESTSUM *testSum)
{
  int  rc = 0;
  char selectTable1[100];
  char selectTable2[100];
  char selectTable3[100];
  char selectTable4[100];
  char selectTable5[100];
  char selectTable6[100];
  char selectTable7[100];
  char selectTable8[100];
  char selectTable9[100];
  char selectTable10[100];
  char selectTable11[100];
  char selectTable12[100];
  char selectTable13[100];
  char selectTable14[100];
  char selectTable15[100];
  char selectTable16[100];
  char selectTable17[100];
  char selectTable18[100];
  char selectTable19[100];
  char selectTable20[100];
  char selectTable21[100];
  char selectTable22[100];
  char selectTable23[100];
  char selectTable24[100];
  char selectTable25[100];
  char selectTable26[100];
  char selectTable27[100];
  char selectTable28[100];
  char selectTable29[100];
  char selectTable30[100];
  char selectTable31[100];
  char selectTable32[100];
  char selectTable33[100];
  char selectTable34[100];
  char selectTable35[100];
  char selectTable36[100];
  char selectTable37[100];
  char selectTable38[100];
  char selectTable39[100];
  char selectTable40[100];
  char selectTable41[100];
  char selectTable42[100];
  char selectTable43[100];
  char selectTable44[100];
  char selectTable45[100];
  char selectTable46[100];
  char selectTable47[100];
  char selectTable48[100];
  char selectTable49[100];
  char selectTable50[100];
  char selectTable51[100];
  char selectTable52[100];
  char selectTable53[100];
  char selectTable54[100];
  char selectTable55[100];
  char selectTable56[100];
  char selectTable57[100];
  char selectTable58[100];
  char selectTable59[100];
  char selectTable60[100];
  char selectTable61[100];
  char selectTable62[100];
  char selectTable63[100];
  char selectTable64[100];
  char selectTable65[100];
  char selectTable66[100];
  char selectTable67[100];
  char selectTable68[100];
  char selectTable69[100];
  char selectTable70[100];
  char selectTable71[100];
  char selectTable72[100];
  char selectTable73[100];
  char selectTable74[100];
  char selectTable75[100];
  char selectTable76[100];
  char selectTable77[100];
  char selectTable78[100];
  char selectTable79[100];
  char selectTable80[100];
  char selectTable81[100];
  char selectTable82[100];
  char selectTable83[100];
  char selectTable84[100];
  char selectTable85[100];
  char selectTable86[100];
  char selectTable87[100];
  char selectTable88[100];
  char selectTable89[100];
  char selectTable90[100];
  char selectTable91[100];
  char selectTable92[100];
  char selectTable93[100];
  char selectTable94[100];
  char selectTable95[100];
  char selectTable96[100];
  char selectTable97[100];
  char selectTable98[100];
  char selectTable99[100];
  char selectTable100[100];
  char selectTable101[100];
  char selectTable102[100];

  test_table_count.testProject = "12:打开数据库查询";
  strcpy(selectTable1, "12.1:主键查询 等于");
  test5_1(db, testSum, selectTable1);
  strcpy(selectTable2, "12.2:主键查询 小于");
  test5_2(db, testSum, selectTable2);
  strcpy(selectTable3, "12.3:主键查询 小于等于");
  test5_3(db, testSum, selectTable3);
  strcpy(selectTable4, "12.4:主键查询 大于");
  test5_4(db, testSum, selectTable4);
  strcpy(selectTable5, "12.5:主键查询 大于等于");
  test5_5(db, testSum, selectTable5);
  strcpy(selectTable6, "12.6:主键查询 主键值不存在");
  test5_6(db, testSum, selectTable6);
  strcpy(selectTable7, "12.7:属性不存在");
  test5_7(db, testSum, selectTable7);
  strcpy(selectTable8, "12.8:主键联合查询 等于+等于");
  test5_8(db, testSum, selectTable8);
  strcpy(selectTable9, "12.9:主键联合查询 等于+数据不存在");
  test5_9(db, testSum, selectTable9);
  strcpy(selectTable10, "12.10:非主键查询 等于");
  test5_10(db, testSum, selectTable10);
  strcpy(selectTable11, "12.11:非主键查询 小于");
  test5_11(db, testSum, selectTable11);
  strcpy(selectTable12, "12.12:非主键查询 小于等于");
  test5_12(db, testSum, selectTable12);
  strcpy(selectTable13, "12.13:非主键查询 大于");
  test5_13(db, testSum, selectTable13);
  strcpy(selectTable14, "12.14:非主键查询 大于等于");
  test5_14(db, testSum, selectTable14);
  strcpy(selectTable15, "12.15:主键联合查询 小于+小于");
  test5_15(db, testSum, selectTable15);
  strcpy(selectTable16, "12.16:主键联合查询 小于+小于等于");
  test5_16(db, testSum, selectTable16);
  strcpy(selectTable17, "12.17:主键联合查询 小于+大于");
  test5_17(db, testSum, selectTable17);
  strcpy(selectTable18, "12.18:主键联合查询 小于+大于等于");
  test5_18(db, testSum, selectTable18);
  strcpy(selectTable19, "12.19:主键联合查询 小于等于+小于");
  test5_19(db, testSum, selectTable19);
  strcpy(selectTable20, "12.20:主键联合查询 小于等于+小于等于");
  test5_20(db, testSum, selectTable20);
  strcpy(selectTable21, "12.21:主键联合查询 小于等于+大于");
  test5_21(db, testSum, selectTable21);
  strcpy(selectTable22, "12.22:主键联合查询 小于等于+大于等于");
  test5_22(db, testSum, selectTable22);
  strcpy(selectTable23, "12.23:主键联合查询 大于+小于");
  test5_23(db, testSum, selectTable23);
  strcpy(selectTable24, "12.24:主键联合查询 大于+小于等于");
  test5_24(db, testSum, selectTable24);
  strcpy(selectTable25, "12.25:主键联合查询 大于+大于");
  test5_25(db, testSum, selectTable25);
  strcpy(selectTable26, "12.26:主键联合查询 大于+大于等于");
  test5_26(db, testSum, selectTable26);
  strcpy(selectTable27, "12.27:主键联合查询 大于等于+小于");
  test5_27(db, testSum, selectTable27);
  strcpy(selectTable28, "12.28:主键联合查询 大于等于+小于等于");
  test5_28(db, testSum, selectTable28);
  strcpy(selectTable29, "12.29:主键联合查询 大于等于+大于");
  test5_29(db, testSum, selectTable29);
  strcpy(selectTable30, "12.30:主键联合查询 大于等于+大于等于");
  test5_30(db, testSum, selectTable30);
  strcpy(selectTable31, "12.31:主键联合查询 前置条件无数据");
  test5_31(db, testSum, selectTable31);
  strcpy(selectTable32, "12.32:主键联合查询 后置条件无数据");
  test5_32(db, testSum, selectTable32);
  strcpy(selectTable33, "12.33:表名不存在");
  test5_33(db, testSum, selectTable33);
  strcpy(selectTable34, "12.34:多条件查询 小于+小于+小于");
  test5_34(db, testSum, selectTable34);
  strcpy(selectTable35, "12.35:多条件查询 小于+小于+大于");
  test5_35(db, testSum, selectTable35);
  strcpy(selectTable36, "12.36:多条件查询 小于+大于+小于");
  test5_36(db, testSum, selectTable36);
  strcpy(selectTable37, "12.37:多条件查询 小于+大于+大于");
  test5_37(db, testSum, selectTable37);
  strcpy(selectTable38, "12.38:多条件查询 大于+小于+小于");
  test5_38(db, testSum, selectTable38);
  strcpy(selectTable39, "12.39:多条件查询 大于+小于+大于");
  test5_39(db, testSum, selectTable39);
  strcpy(selectTable40, "12.40:多条件查询 大于+大于+小于");
  test5_40(db, testSum, selectTable40);
  strcpy(selectTable41, "12.41:多条件查询 大于+大于+大于");
  test5_41(db, testSum, selectTable41);
  strcpy(selectTable42, "12.42:多条件查询 四条件范围查询");
  test5_42(db, testSum, selectTable42);
  strcpy(selectTable43, "12.43:多条件查询 四条件查询");
  test5_43(db, testSum, selectTable43);
  strcpy(selectTable44, "12.44:多条件查询 四条件乱序查询");
  test5_44(db, testSum, selectTable44);
  strcpy(selectTable45, "12.45:查询条件中存在空格");
  test5_45(db, testSum, selectTable45);
  strcpy(selectTable46, "12.46:全表查询+投影");
  test5_46(db, testSum, selectTable46);
  strcpy(selectTable47, "12.47:全表查询+乱序投影");
  test5_47(db, testSum, selectTable47);
  strcpy(selectTable48, "12.48:单条件查询+投影");
  test5_48(db, testSum, selectTable48);
  strcpy(selectTable49, "12.49:单条件查询+乱序投影");
  test5_49(db, testSum, selectTable49);
  strcpy(selectTable50, "12.50:多条件查询+投影");
  test5_50(db, testSum, selectTable50);
  strcpy(selectTable51, "12.51:多条件查询+乱序投影");
  test5_51(db, testSum, selectTable51);
  strcpy(selectTable52, "12.52:投影属性不存在");
  test5_52(db, testSum, selectTable52);
  strcpy(selectTable53, "12.53:连接查询 全连接");
  test5_53(db, testSum, selectTable53);
  strcpy(selectTable54, "12.54:连接查询 单表条件连接");
  test5_54(db, testSum, selectTable54);
  strcpy(selectTable55, "12.55:连接查询 连接条件主键连接等于");
  test5_55(db, testSum, selectTable55);
  strcpy(selectTable56, "12.56:连接查询 连接条件主键连接小于");
  test5_56(db, testSum, selectTable56);
  strcpy(selectTable57, "12.57:连接查询 连接条件主键连接小于等于");
  test5_57(db, testSum, selectTable57);
  strcpy(selectTable58, "12.58:连接查询 连接条件主键连接大于");
  test5_58(db, testSum, selectTable58);
  strcpy(selectTable59, "12.59:连接查询 连接条件主键连接大于等于");
  test5_59(db, testSum, selectTable59);
  strcpy(selectTable60, "12.60:连接查询 连接条件连接等于");
  test5_60(db, testSum, selectTable60);
  strcpy(selectTable61, "12.61:连接查询 连接条件主键连接小于");
  test5_61(db, testSum, selectTable61);
  strcpy(selectTable62, "12.62:连接查询 连接条件主键连接小于等于");
  test5_62(db, testSum, selectTable62);
  strcpy(selectTable63, "12.63:连接查询 连接条件主键连接大于");
  test5_63(db, testSum, selectTable63);
  strcpy(selectTable64, "12.64:连接查询 连接条件主键连接大于等于");
  test5_64(db, testSum, selectTable64);
  strcpy(selectTable65, "12.65:连接查询 连接条件+表1 1条件");
  test5_65(db, testSum, selectTable65);
  strcpy(selectTable66, "12.66:连接查询 连接条件+表1 多条件");
  test5_66(db, testSum, selectTable66);
  strcpy(selectTable67, "12.67:连接查询 连接条件+表2 1条件");
  test5_67(db, testSum, selectTable67);
  strcpy(selectTable68, "12.68:连接查询 连接条件+表2 多条件");
  test5_68(db, testSum, selectTable68);
  strcpy(selectTable69, "12.69:连接查询 连接条件+表1-2条件");
  test5_69(db, testSum, selectTable69);
  strcpy(selectTable70, "12.70:连接查询 连接条件属性换位");
  test5_70(db, testSum, selectTable70);
  strcpy(selectTable71, "12.71:连接查询 双连接条件等于");
  test5_71(db, testSum, selectTable71);
  strcpy(selectTable72, "12.72:连接查询 双连接条件等于小于");
  test5_72(db, testSum, selectTable72);
  strcpy(selectTable73, "12.73:连接查询 双连接条件等于小于等于");
  test5_73(db, testSum, selectTable73);
  strcpy(selectTable74, "12.74:连接查询 双连接条件等于大于");
  test5_74(db, testSum, selectTable74);
  strcpy(selectTable75, "12.75:连接查询 双连接条件等于大于");
  test5_75(db, testSum, selectTable75);
  strcpy(selectTable76, "12.76:连接查询 连接条件小于等于");
  test5_76(db, testSum, selectTable76);
  strcpy(selectTable77, "12.77:连接查询 双连接条件小于小于");
  test5_77(db, testSum, selectTable77);
  strcpy(selectTable78, "12.78:连接查询 双连接条件小于小于等于");
  test5_78(db, testSum, selectTable78);
  strcpy(selectTable79, "12.79:连接查询 双连接条件小于大于");
  test5_79(db, testSum, selectTable79);
  strcpy(selectTable80, "12.80:连接查询 双连接条件小于大于等于");
  test5_80(db, testSum, selectTable80);
  strcpy(selectTable81, "12.81:连接查询 连接条件小于等于");
  test5_81(db, testSum, selectTable81);
  strcpy(selectTable82, "12.82:连接查询 双连接条件大于小于");
  test5_82(db, testSum, selectTable82);
  strcpy(selectTable83, "12.83:连接查询 双连接条件大于小于等于");
  test5_83(db, testSum, selectTable83);
  strcpy(selectTable84, "12.84:连接查询 双连接条件大于大于");
  test5_84(db, testSum, selectTable84);
  strcpy(selectTable85, "12.85:连接查询 双连接条件大于大于等于");
  test5_85(db, testSum, selectTable85);
  strcpy(selectTable86, "12.86:连接查询 连接条件小于等于");
  test5_86(db, testSum, selectTable86);
  strcpy(selectTable87, "12.87:连接查询 双连接条件小于等于小于");
  test5_87(db, testSum, selectTable87);
  strcpy(selectTable88, "12.88:连接查询 双连接条件小于等于小于等于");
  test5_88(db, testSum, selectTable88);
  strcpy(selectTable89, "12.89:连接查询 双连接条件小于等于大于");
  test5_89(db, testSum, selectTable89);
  strcpy(selectTable90, "12.90:连接查询 双连接条件小于等于大于等于");
  test5_90(db, testSum, selectTable90);
  strcpy(selectTable91, "12.91:连接查询 双连接条件大于等于小于");
  test5_91(db, testSum, selectTable91);
  strcpy(selectTable92, "12.92:连接查询 双连接条件大于等于小于等于");
  test5_92(db, testSum, selectTable92);
  strcpy(selectTable93, "12.93:连接查询 双连接条件大于等于大于");
  test5_93(db, testSum, selectTable93);
  strcpy(selectTable94, "12.94:连接查询 双连接条件大于等于大于等于");
  test5_94(db, testSum, selectTable94);
  strcpy(selectTable95, "12.95:连接查询 双连接条件大于等于大于等于");
  test5_95(db, testSum, selectTable95);
  strcpy(selectTable96, "12.96:连接查询 双连接条件大于等于大于等于");
  test5_96(db, testSum, selectTable96);
  strcpy(selectTable97, "12.97:连接查询 等于+等于+小于+小于");
  test5_97(db, testSum, selectTable97);
  strcpy(selectTable98, "12.98:连接查询 等于+等于+小于+大于");
  test5_98(db, testSum, selectTable98);
  strcpy(selectTable99, "12.99:连接查询 等于+等于+大于+大于");
  test5_99(db, testSum, selectTable99);
  strcpy(selectTable100, "12.100:连接查询 等于+等于+小于+小于+小于+小于+小于+小于+小于+小于");
  test5_100(db, testSum, selectTable100);
  strcpy(selectTable101, "12.101:连接查询 等于+等于+小于+小于+小于+小于+小于+小于+小于+大于");
  test5_101(db, testSum, selectTable101);
  strcpy(selectTable102, "12.102:连接查询 等于+等于+小于+小于+小于+小于+小于+小于+小于+大于+大于");
  test5_102(db, testSum, selectTable102);
  strcpy(selectTable101, "12.101:连接查询 等于+等于+小于+小于+小于+小于+小于+小于+小于+大于");
  test5_101(db, testSum, selectTable101);
  strcpy(selectTable102, "12.102:连接查询 等于+等于+小于+小于+小于+小于+小于+小于+小于+大于+大于");
  test5_102(db, testSum, selectTable102);

  return rc;
}

int test13_1(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.1:主键更新";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_ident=XIANBEI"
                         "WHERE ARPT_length=952.685537";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE ARPT SET ARPT_ident='XIANBEI' WHERE ARPT_length=952.685537", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 1, 1, "ARPT_ident", "XIANBEI", "ARPT_length=952.685537");
#endif
  if (rc == GNCDB_PRIMARY_KEY_IMMUTABLE) {
    addTest(testSum, 0);
  } else {
    addTest(testSum, rc);
  }

  return 0;
}

int test13_2(GNCDB *db, TESTSUM *testSum)
{
  char   updataTable[] = "13.2:全更新";
  int    rc            = 0;
  int    rows          = 0;
  double min           = -10000.0;
  double max           = 10000.0;
  char  *path          = NULL;
  FILE  *fileData      = NULL;
  int    i             = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE WPT_up10"
                         "SET WPT_lat=0";
  currentTest(testSum, updataTable);

  rc = GNCDB_createTable(db,
      "AAWPT_up10",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 400; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "AAWPT_up10", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE AAWPT_up10 SET WPT_lat=0", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "AAWPT_up10", 1, 0, "WPT_lat", 0.0);
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackAllUpDate, &rows, NULL, 1, 0, 0, "AAWPT_up10");
  if (rc == GNCDB_SUCCESS && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path);
  return 0;
}

int test13_3(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.3:条件更新等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE WPT"
                         "SET WPT_lat=42.0"
                         "WHERE WPT_ident=DYINT";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT SET WPT_lat=42.0 WHERE WPT_ident='DYINT'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_lat", 42.0, "WPT_ident=DYINT");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_3, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident=DYINT");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test13_4(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.4:条件更新小于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT"
                         "SET WPT_lon=52.0"
                         "WHERE WPT_ident<FTHLF";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT SET WPT_lon=52.0 WHERE WPT_ident<'FTHLF'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_lon", 52.0, "WPT_ident<FTHLF");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_4, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident<FTHLF");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test13_5(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.5:条件更新小于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT"
                         "SET WPT_lat=100.0"
                         "WHERE WPT_ident<=FTHLF";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT SET WPT_lat=100.0 WHERE WPT_ident<='FTHLF'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_lat", 100.0, "WPT_ident<=FTHLF");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_5, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident<=FTHLF");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test13_6(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.6:条件更新大于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT"
                         "SET WPT_lon=-10.0"
                         "WHERE WPT_ident>SRVIO";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT SET WPT_lon=-10.0 WHERE WPT_ident>'SRVIO'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_lon", -10.0, "WPT_ident>SRVIO");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_6, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident>SRVIO");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test13_7(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.7:条件更新大于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT"
                         "SET WPT_lat=10.0"
                         "WHERE WPT_ident>=SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT SET WPT_lat=10.0 WHERE WPT_ident>='SRVIO'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_lat", 10.0, "WPT_ident>=SRVIO");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBack6_7, &rows, NULL, 1, 0, 1, "WPT", "WPT_ident>=SRVIO");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int test13_8(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.8:更新表名不存在";

  int rc   = 0;
  int rows = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE table1 "
                         "SET WPT_lat = 10.0 "
                         "WHERE WPT_ident >= SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE table1 SET WPT_lat = 10.0 WHERE WPT_ident>='SRVIO'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "table1", 1, 1, "WPT_lat", 10.0, "WPT_ident >= SRVIO");
#endif
  if (rc == GNCDB_TABLE_NOT_FOUND) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_9(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.9:更新属性不存在";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE WPT "
                         "SET WPT_up_lat = 10.0 "
                         "WHERE WPT_ident = SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT SET WPT_up_lat = 10.0 WHERE WPT_ident='SRVIO'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_up_lat", 10.0, "WPT_ident=SRVIO");
#endif
  if (rc == GNCDB_PARAM_INVALID || rc == -19 || rc == GNCDB_NOT_FOUND) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_10(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.10:更新条件属性不存在";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE WPT "
                         "SET WPT_lat = 1000.0 "
                         "WHERE WPT_up_ident = SRVIO";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT SET WPT_lat = 1000.0 WHERE WPT_up_ident='SRVIO'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_lat", 1000.0, "WPT_up_ident = SRVIO");
#endif
  if ((rc == GNCDB_CONDITION_INVALID || rc == GNCDB_FIELD_NOT_EXIST) && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_11(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.11:单条件更新多条记录等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *value[2]       = {"100.000000", "900.000000"};
  int   index[2]       = {2, 4};
  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_lat=100.0  ARPT_length=900.0"
                         "WHERE ARPT_mag_var=7.197790";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT SET ARPT_lat=100.0, ARPT_length=900.0 WHERE ARPT_mag_var=7.197790", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 2, 1, "ARPT_lat", 100.0, "ARPT_length", 900.0, "ARPT_mag_var=7.197790");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_mag_var=7.197790");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_12(GNCDB *db, TESTSUM *testSum)
{
  char  updataTable[] = "13.12:单条件更新多条记录小于";
  int   rc            = 0;
  int   rows          = 0;
  char *value[2]      = {"39.990000", "-39.990000"};
  int   index[2]      = {1, 2};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_lon=39.99  ARPT_lat=-39.99"
                         "WHERE ARPT_length<779.029420";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT SET ARPT_lon=39.99, ARPT_lat=-39.99 WHERE ARPT_length<779.029420", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 2, 1, "ARPT_lon", 39.99, "ARPT_lat", -39.99, "ARPT_length<779.029420");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_length<779.029420");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_13(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.13:单条件更新多条记录小于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *value[2] = {"20.000000", "5.200000"};
  int   index[2] = {3, 5};

  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_elev=20.0  ARPT_mag_var=5.20"
                         "WHERE ARPT_ident<=CBMKD";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT SET ARPT_elev=20.0, ARPT_mag_var=5.20 WHERE ARPT_ident<='CBMKD'", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 2, 1, "ARPT_elev", 20.0, "ARPT_mag_var", 5.20, "ARPT_ident<=CBMKD");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident<=CBMKD");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_14(GNCDB *db, TESTSUM *testSum)
{
  char  updataTable[] = "13.14:单条件更新多条记录大于";
  int   rc            = 0;
  int   rows          = 0;
  char *value[3]      = {"20.000000", "1000.000000", "5.200000"};
  int   index[3]      = {3, 4, 5};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_length=1000.0  ARPT_elev=20.0  ARPT_mag_var=5.20"
                         "WHERE ARPT_ident>WCXPD";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT SET ARPT_length=1000.0, ARPT_elev=20.0, ARPT_mag_var=5.20 WHERE ARPT_ident>'WCXPD'",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_update(
      db, &rows, "ARPT", 3, 1, "ARPT_length", 1000.0, "ARPT_elev", 20.0, "ARPT_mag_var", 5.20, "ARPT_ident>WCXPD");
#endif

  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 3;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident>WCXPD");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_15(GNCDB *db, TESTSUM *testSum)
{
  char  updataTable[] = "13.15:单条件更新多条记录大于等于";
  int   rc            = 0;
  int   rows          = 0;
  char *value[3]      = {"1000.000000", "20.000000", "6.200000"};
  int   index[3]      = {1, 2, 5};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_lon=1000.0  ARPT_lat=20.0  ARPT_mag_var=6.20"
                         "WHERE ARPT_ident>=YQLUV";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT SET ARPT_lon=1000.0, ARPT_lat=20.0, ARPT_mag_var=6.20 WHERE ARPT_ident>='YQLUV'",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_update(
      db, &rows, "ARPT", 3, 1, "ARPT_lon", 1000.0, "ARPT_lat", 20.0, "ARPT_mag_var", 6.20, "ARPT_ident>=YQLUV");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 3;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=YQLUV");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_16(GNCDB *db, TESTSUM *testSum)
{
  char  updataTable[] = "13.16:多条件更新单条1";
  int   rc            = 0;
  int   rows          = 0;
  char *value[1]      = {"500.000000"};
  int   index[1]      = {1};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_lon=500.0"
                         "WHERE ARPT_ident<=ESFPT  ARPT_length<1593.597766";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT SET ARPT_lon=500.0 WHERE ARPT_ident<='ESFPT' AND ARPT_length<1593.597766", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 1, 2, "ARPT_lon", 500.0, "ARPT_ident<=ESFPT", "ARPT_length<1593.597766");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_ident<=ESFPT", "ARPT_length<1593.597766");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_17(GNCDB *db, TESTSUM *testSum)
{
  char  updataTable[] = "13.17:多条件更新单条2";
  int   rc            = 0;
  int   rows          = 0;
  char *value[1]      = {"1.100000"};
  int   index[1]      = {2};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_lat=1.1"
                         "WHERE ARPT_ident<=ESFPT  ARPT_length>2535.783166";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT SET ARPT_lat=1.1 WHERE ARPT_ident<='ESFPT' AND ARPT_length>2535.783166", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 1, 2, "ARPT_lat", 1.1, "ARPT_ident<=ESFPT", "ARPT_length>2535.783166");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_ident<=ESFPT", "ARPT_length>2535.783166");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_18(GNCDB *db, TESTSUM *testSum)
{
  char  updataTable[] = "13.18:多条件更新单条3";
  int   rc            = 0;
  int   rows          = 0;
  char *value[1]      = {"1.100000"};
  int   index[1]      = {4};
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_length=1.1"
                         "WHERE ARPT_lat<=33.750000  ARPT_lat>-11.250000";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(
      db, "UPDATE ARPT SET ARPT_length=1.1 WHERE ARPT_lat<=33.750000 AND ARPT_lat>-11.250000", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "ARPT", 1, 2, "ARPT_length", 1.1, "ARPT_lat<=33.750000", "ARPT_lat>-11.250000");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 1;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_lat<=33.750000", "ARPT_lat>-11.250000");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_19(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.19:多条件更新多条1";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *value[2]       = {"-10.000000", "30.000000"};
  int   index[2]       = {4, 3};
  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_length=10.0  ARPT_elev=30.0"
                         "WHERE ARPT_lat<=-33.750000  ARPT_lat>-72.421875";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT SET ARPT_length=-10.0, ARPT_elev=30.0 WHERE ARPT_lat<=-33.750000 AND ARPT_lat>-72.421875",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_update(
      db, &rows, "ARPT", 2, 2, "ARPT_length", -10.0, "ARPT_elev", 30.0, "ARPT_lat<=-33.750000", "ARPT_lat>-72.421875");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db, testCallBackUpDate, &rows, NULL, 1, 0, 2, "ARPT", "ARPT_lat<=-33.750000", "ARPT_lat>-72.421875");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_20(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.20:多条件更新多条2";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  char *value[2]       = {"-10.000000", "2000.000000"};
  int   index[2]       = {3, 4};
  test_table_count.SQL = "UPDATE ARPT"
                         "SET ARPT_elev=-10.0  ARPT_length=2000.0"
                         "WHERE ARPT_ident>=UBKNE  ARPT_lon>72.421875  ARPT_lat<=54.140625";
  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "UPDATE ARPT SET ARPT_elev=-10.0, ARPT_length=2000.0 WHERE ARPT_ident>='UBKNE' AND ARPT_lon>72.421875 AND "
      "ARPT_lat<=54.140625",
      NULL,
      NULL,
      &errmsg);
#else
  rc = GNCDB_update(db,
      &rows,
      "ARPT",
      2,
      3,
      "ARPT_elev",
      -10.0,
      "ARPT_length",
      2000.0,
      "ARPT_ident>=UBKNE",
      "ARPT_lon>72.421875",
      "ARPT_lat<=54.140625");
#endif
  test_table_count.fieldName = value;
  test_table_count.index     = index;
  test_table_count.rowNum    = 2;
  test_table_count.rowcount  = 0;

  GNCDB_select(db,
      testCallBackUpDate,
      &rows,
      NULL,
      1,
      0,
      3,
      "ARPT",
      "ARPT_ident>=UBKNE",
      "ARPT_lon>72.421875",
      "ARPT_lat<=54.140625");
  if (rc == GNCDB_SUCCESS && rows == test_table_count.rowcount && test_table_count.testFlag) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test13_21(GNCDB *db, TESTSUM *testSum)
{
  char updataTable[] = "13.21:更新属性与条件属性相同";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL = "UPDATE WPT_up2"
                         "SET WPT_lat=1000.0"
                         "WHERE WPT_lat=-22.500000";

  currentTest(testSum, updataTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "UPDATE WPT_up2 SET WPT_lat=1000.0 WHERE WPT_lat=-22.500000", NULL, NULL, &errmsg);
#else
  rc = GNCDB_update(db, &rows, "WPT", 1, 1, "WPT_lat", 1000.0, "WPT_lat=-22.500000");
#endif
  test_table_count.rowcount = 0;
  rows                      = 0;
  GNCDB_select(db, NULL, &rows, NULL, 1, 0, 1, "WPT", "WPT_lat=-22.500000");
  if (rc == GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int testOpendbUpdate(GNCDB *db, TESTSUM *testSum)
{
  test_table_count.testProject = "13:打开数据库更新";
  // test13_1(db, testSum);
  test13_2(db, testSum);
  test13_3(db, testSum);
  test13_4(db, testSum);
  test13_5(db, testSum);
  test13_6(db, testSum);
  test13_7(db, testSum);
  test13_8(db, testSum);
  test13_9(db, testSum);
  test13_10(db, testSum);
  test13_11(db, testSum);
  test13_12(db, testSum);
  test13_13(db, testSum);
  test13_14(db, testSum);
  test13_15(db, testSum);
  test13_16(db, testSum);
  test13_17(db, testSum);
  test13_18(db, testSum);
  test13_19(db, testSum);
  test13_20(db, testSum);
  test13_21(db, testSum);

  return 0;
}

int test14_1(GNCDB *db, TESTSUM *testSum)
{
  char   deleteTable[] = "14.1:全删除";
  int    rc            = 0;
  int    rows          = 0;
  double min           = -10000.0;
  double max           = 10000.0;
  char  *path          = NULL;
  FILE  *fileData      = NULL;
  int    i             = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de1";
  currentTest(testSum, deleteTable);

  rc = GNCDB_createTable(db,
      "WPT_de1",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 400; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "WPT_de1", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de1", callBackSQLGetRows, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de1", 0);
#endif
  if (rc == GNCDB_SUCCESS && rows == 400) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path);
  return 0;
}

int test14_2(GNCDB *db, TESTSUM *testSum)
{
  char   deleteTable[36] = "14.2:主键删除等于";
  int    rc              = 0;
  int    rows            = 0;
  double min             = -10000.0;
  double max             = 10000.0;
  char  *path            = NULL;
  FILE  *fileData        = NULL;
  int    i               = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de10"
                         "WHERE WPT_ident=TNLKZ";
  currentTest(testSum, deleteTable);

  rc = GNCDB_createTable(db,
      "WPT_de10",
      3,
      "WPT_ident",
      FIELDTYPE_VARCHAR,
      0,
      1,
      min,
      100.0,
      "WPT_lon",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      "WPT_lat",
      FIELDTYPE_REAL,
      0,
      0,
      min,
      max,
      TABLEMAXROWS);

  path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  }

  fileData = fopen(path, "r");
  for (i = 0; i < 400; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    rc = GNCDB_insert(db, NULL, "WPT_de10", wpt.sc8_wpt_ident, wpt.f64_lon, wpt.f64_lat);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  fclose(fileData);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de10 WHERE WPT_ident='TNLKZ'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de10", 1, "WPT_ident=TNLKZ");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de10", "WPT_ident=TNLKZ");
  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  my_free(path);
  return 0;
}

int test14_3(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.3:主键删除小于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de10 "
                         "WHERE WPT_ident < EBTKZ";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de10 WHERE WPT_ident<'EBTKZ'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de10", 1, "WPT_ident<EBTKZ");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de10", "WPT_ident<EBTKZ");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_4(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.4:主键删除大于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de10 "
                         "WHERE WPT_ident > SCSZJ";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de10 WHERE WPT_ident>'SCSZJ'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de10", 1, "WPT_ident>SCSZJ");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de10", "WPT_ident>SCSZJ");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_5(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.5:主键删除小于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM ARPT "
                         "WHERE ARPT_ident <= HILDY";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT WHERE ARPT_ident<='HILDY'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT", 1, "ARPT_ident<=HILDY");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident<=HILDY");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_6(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.6:主键删除大于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM ARPT "
                         "WHERE ARPT_ident >= VLSTG";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT WHERE ARPT_ident>='VLSTG'", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT", 1, "ARPT_ident>=VLSTG");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_ident>=VLSTG");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_7(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.7:非主键删除等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de10 "
                         "WHERE WPT_lat = -22.500000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de10 WHERE WPT_lat=-22.500000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de10", 1, "WPT_lat=-22.500000");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de10", "WPT_lat=-22.500000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_8(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.8:非主键删除小于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de10 "
                         "WHERE WPT_lon < -26.718750";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de10 WHERE WPT_lon<-26.718750", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de10", 1, "WPT_lon<-26.718750");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de10", "WPT_lon<-26.718750");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_9(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.9:非主键删除大于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM WPT_de10 "
                         "WHERE WPT_lon > 45.000000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM WPT_de10 WHERE WPT_lon>45.000000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "WPT_de10", 1, "WPT_lon>45.000000");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "WPT_de10", "WPT_lon>45.000000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_10(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.10:非主键删除小于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM ARPT "
                         "WHERE ARPT_elev <= 5.783288";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT WHERE ARPT_elev<=5.783288", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT", 1, "ARPT_elev<=5.783288");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_elev<=5.783288");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_11(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.11:非主键删除大于等于";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM ARPT "
                         "WHERE ARPT_lon >= 90.000000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT WHERE ARPT_lon>=90.000000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT", 1, "ARPT_lon>=90.000000");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 1, "ARPT", "ARPT_lon>=90.000000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_12(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.12:多条件删除1";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM ARPT "
                         "WHERE ARPT_lon = -135.000000 AND ARPT_lat=-16.875000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT WHERE ARPT_lon=-135.000000 AND ARPT_lat=-16.875000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT", 2, "ARPT_lon=-135.000000", "ARPT_lat=-16.875000");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_lon=-135.000000", "ARPT_lat=-16.875000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_13(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.13:多条件删除2";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif

  test_table_count.SQL = "DELETE FROM ARPT "
                         "WHERE ARPT_lon <= 56.000000 AND ARPT_lat <= 100.875000";

  currentTest(testSum, deleteTable);
#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db, "DELETE FROM ARPT WHERE ARPT_lon<=56.000000 AND ARPT_lat<=100.875000", NULL, &rows, &errmsg);
#else
  rc = GNCDB_delete(db, &rows, "ARPT", 2, "ARPT_lon<=56.000000", "ARPT_lat<=100.875000");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackDelete, NULL, NULL, 1, 0, 2, "ARPT", "ARPT_lon<=56.000000", "ARPT_lat<=100.875000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int test14_14(GNCDB *db, TESTSUM *testSum)
{
  char deleteTable[] = "14.14:多条件删除3";
  int  rc            = 0;
  int  rows          = 0;
#ifdef SQL_TESTMODE
  char *errmsg = NULL;
#else
#endif
  test_table_count.SQL =
      "DELETE FROM WPT_de10 "
      "WHERE WPT_lon > -20.000000 AND WPT_lon < 56.000000 AND WPT_lat > -10.875000 AND WPT_lat < 100.875000";

  currentTest(testSum, deleteTable);

#ifdef SQL_TESTMODE
  rc = GNCDB_exec(db,
      "DELETE FROM WPT_de10 WHERE WPT_lon>-20.000000 AND WPT_lon<56.000000 AND WPT_lat>-10.875000 AND "
      "WPT_lat<100.875000",
      NULL,
      &rows,
      &errmsg);
#else
  rc = GNCDB_delete(
      db, &rows, "WPT_de10", 4, "WPT_lon>-20.000000", "WPT_lon<56.000000", "WPT_lat>-10.875000", "WPT_lat<100.875000");
#endif
  test_table_count.rowcount = 0;
  GNCDB_select(db,
      testCallBackDelete,
      NULL,
      NULL,
      1,
      0,
      4,
      "WPT_de10",
      "WPT_lon>-20.000000",
      "WPT_lon<56.000000",
      "WPT_lat>-10.875000",
      "WPT_lat<100.875000");

  if (rc == GNCDB_SUCCESS && test_table_count.rowcount == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }

  return 0;
}

int testOpendbDelete(GNCDB *db, TESTSUM *testSum)
{
  test_table_count.testProject = "14:打开数据库删除";
  test14_1(db, testSum);
  test14_2(db, testSum);
  test14_3(db, testSum);
  test14_4(db, testSum);
  test14_5(db, testSum);
  test14_6(db, testSum);
  test14_7(db, testSum);
  test14_8(db, testSum);
  test14_9(db, testSum);
  test14_10(db, testSum);
  test14_11(db, testSum);
  test14_12(db, testSum);
  test14_13(db, testSum);
  test14_14(db, testSum);

  return 0;
}

int testOpendbDrop(GNCDB *db, TESTSUM *testSum)
{
  char openTest1[36]           = "15.1:销毁存在的表";
  char openTest2[36]           = "15.2:销毁多个表";
  char openTest3[36]           = "15.3:销毁不存在的表";
  test_table_count.testProject = "15:打开数据库销毁表";
  test8_1(db, testSum, openTest1);
  test8_2(db, testSum, openTest2);
  test8_3(db, testSum, openTest3);

  return 0;
}

int test16_1(GNCDB **db, TESTSUM *testSum)
{
  int  rc              = 0;
  char closeTest[42]   = "16.1:关闭打开的数据库";
  test_table_count.SQL = "EXIT";

  currentTest(testSum, closeTest);
  rc                  = GNCDB_close(db);
  test_table_count.db = NULL;
  test_table_count.db = NULL;
  addTest(testSum, rc);
  return 0;
}

int test16_2(GNCDB **db, TESTSUM *testSum)
{
  int  rc              = -1;
  char closeTest[39]   = "16.2:重复关闭数据库";
  test_table_count.SQL = "EXIT";
  currentTest(testSum, closeTest);
  rc = GNCDB_close(db);
  if (rc == GNCDB_WAS_CLOSED) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return 0;
}

int testClosedb(GNCDB *db, TESTSUM *testSum)
{
  test_table_count.testProject = "16:关闭数据库";
  // currentTest(testSum, closeTest);
  test16_1(&db, testSum);
  // test16_2(&db, testSum);

  return 0;
}

TESTSUM *initTest()
{
  char     pathcwd[1024] = {0};
  TESTSUM *testSum       = NULL;

  testSum = my_malloc(sizeof(TESTSUM));
  if (testSum == NULL) {
    return NULL;
  }

  getcwd(pathcwd, 1024);
  printf("%s\n", pathcwd);
  if (TEST_OUTPUT_SELECT) {
#if defined _WIN32
    testSum->fp = fopen(".\\testfile\\result\\test.csv", "w+");
#else
    testSum->fp = fopen("./testfile/result/test.csv", "w+");
#endif

    if (testSum->fp == NULL) {
      perror("Error");
      printf("test.csv 文件打开失败\n");
    }
  }
  testSum->allTest     = 0;
  testSum->failTest    = 0;
  testSum->successTest = 0;

  return testSum;
}

void speaceTab(int rowLen, int len, TESTSUM *testSum)
{
  int i = 0;
  for (i = 0; i < rowLen - len; ++i) {
    printf(" ");
  }
}

void printfTableAttr(TESTSUM *testSum)
{
  char str[] = "功能,测试项,测试内容,SQL语句,结果,状态码,\n";
  printf("%s", str);
}

void writeToFile(TESTSUM *testSum)
{
  char str[] = "功能,测试项,测试内容,SQL语句,结果,状态码,\n";
  fwrite(str, strlen(str), 1, testSum->fp);
  fflush(testSum->fp);
}

void outputTestAttr(TESTSUM *testSum)
{
  if (TEST_OUTPUT_SELECT == 0) {
    printfTableAttr(testSum);
  } else if (TEST_OUTPUT_SELECT == 1) {
    writeToFile(testSum);
  } else {
    printfTableAttr(testSum);
    writeToFile(testSum);
  }
}

void printfTestMessage(TESTSUM *testSum, char *str)
{
  char *cutStr        = NULL;
  char  cut[2]        = ":";
  char  strCopy[1024] = {0};
  strcpy(strCopy, str);
  if (test_table_count.testProject != NULL) {
    printf("%s,", test_table_count.testProject);
  } else {
    printf(",");
  }

  cutStr = strtok(strCopy, cut);
  printf("%s,", cutStr);
  cutStr = strtok(NULL, cut);
  printf("%s,", cutStr);
  printf("%s,", test_table_count.SQL);
}

void outputMessageToFile(TESTSUM *testSum, char *str)
{
  char  strCopy[1024] = {0};
  char *cutStr        = NULL;
  char  cut[2]        = ":";
  strcpy(strCopy, str);
  if (test_table_count.testProject != NULL) {
    fwrite(test_table_count.testProject, 1, strlen(test_table_count.testProject), testSum->fp);
    fwrite(",", 1, 1, testSum->fp);
  } else {
    fwrite(",", 1, 1, testSum->fp);
  }
  cutStr = strtok(strCopy, cut);
  fwrite(cutStr, 1, strlen(cutStr), testSum->fp);
  fwrite(",", 1, 1, testSum->fp);
  cutStr = strtok(NULL, cut);
  fwrite(cutStr, 1, strlen(cutStr), testSum->fp);
  fwrite(",", 1, 1, testSum->fp);
  if(test_table_count.SQL != NULL)
  {
    fwrite(test_table_count.SQL, 1, strlen(test_table_count.SQL), testSum->fp);
  }
  fwrite(",", 1, 1, testSum->fp);
  fflush(testSum->fp);
}

void currentTest(TESTSUM *testSum, char *str)
{
  if (TEST_OUTPUT_SELECT == 0) {
    printfTestMessage(testSum, str);
  } else if (TEST_OUTPUT_SELECT == 1) {
    outputMessageToFile(testSum, str);
  } else {
    printfTestMessage(testSum, str);
    outputMessageToFile(testSum, str);
  }
  test_table_count.SQL = NULL;
}

void printfAddTest(TESTSUM *testSum, int rc)
{
  char str[1024] = {0};
  if (rc == GNCDB_SUCCESS) {
    sprintf(str, "测试通过, ,\n");
  } else {
    sprintf(str, "测试未通过,%d,\n", rc);
  }
  printf("%s", str);
}

void writeAddTest(TESTSUM *testSum, int rc)
{
  char str[1024] = {0};
  if (rc == GNCDB_SUCCESS) {
    sprintf(str, "测试通过, ,\n");
  } else {
    sprintf(str, "测试未通过,%d,\n", rc);
  }
  fwrite(str, strlen(str), 1, testSum->fp);
  fflush(testSum->fp);
}

void addTest(TESTSUM *testSum, int rc)
{
  test_table_count.testProject = NULL;
  if (TEST_OUTPUT_SELECT == 0) {
    printfAddTest(testSum, rc);
  } else if (TEST_OUTPUT_SELECT == 1) {
    writeAddTest(testSum, rc);
  } else {
    printfAddTest(testSum, rc);
    writeAddTest(testSum, rc);
  }
  if (test_table_count.db != NULL) {
    // printfPagePin(test_table_count.db->pagePool);
  }
  if (rc == 0) {
    testSum->successTest++;
  } else {
    testSum->failTest++;
  }
  testSum->allTest++;
}

void printfDisaplay(TESTSUM *testSum)
{
  char str[1024] = {0};
  sprintf(str,
      "------------------------------\n总的测试项 :%d\n成功测试项 :%d\n失败/未实现测试项 "
      ":%d\n------------------------------\n",
      testSum->allTest,
      testSum->successTest,
      testSum->failTest);
  printf("%s", str);
}

void writeDisplay(TESTSUM *testSum)
{
  char str[120] = {0};
  sprintf(str,
      "总的测试项,%d,\n成功测试项,%d,\n失败/未实现测试项,%d,\n",
      testSum->allTest,
      testSum->successTest,
      testSum->failTest);
  fwrite(str, strlen(str), 1, testSum->fp);
  fflush(testSum->fp);
}

void displayTest(TESTSUM *testSum)
{
  if (TEST_OUTPUT_SELECT == 0) {
    printfDisaplay(testSum);
  } else if (TEST_OUTPUT_SELECT == 1) {
    writeDisplay(testSum);
    fclose(testSum->fp);
  } else {
    printfDisaplay(testSum);
    writeDisplay(testSum);
    fclose(testSum->fp);
  }
  my_free(testSum);
}

char *strJoin(char *s1, char *s2)
{
  char *result = my_malloc(strlen(s1) + strlen(s2) + 1);
  if (result == NULL)
    return NULL;

  strcpy(result, s1);
  strcat(result, s2);

  return result;
}

int sql_AggregateFun(TESTSUM *testSum, GNCDB *db);
int sql_AggregateTest1(TESTSUM *testSum, GNCDB *db);
int sql_AggregateTest2(TESTSUM *testSum, GNCDB *db);
int sql_AggregateTest3(TESTSUM *testSum, GNCDB *db);
int sql_AggregateTest4(TESTSUM *testSum, GNCDB *db);
int sql_AggregateTest5(TESTSUM *testSum, GNCDB *db);

int sql_SubqueryFun(TESTSUM *testSum, GNCDB *db);
int sql_SubqueryTest1(TESTSUM *testSum, GNCDB *db);
int sql_SubqueryTest2(TESTSUM *testSum, GNCDB *db);
int sql_SubqueryTest3(TESTSUM *testSum, GNCDB *db);

int sql_GroupByFun(TESTSUM *testSum, GNCDB *db);
int sql_GroupByTest1(TESTSUM *testSum, GNCDB *db);

int sql_OrderByFun(TESTSUM *testSum, GNCDB *db);
int sql_OrderByTest1(TESTSUM *testSum, GNCDB *db);
int sql_OrderByTest2(TESTSUM *testSum, GNCDB *db);
int sql_OrderByTest3(TESTSUM *testSum, GNCDB *db);

int sql_LimitFun(TESTSUM *testSum, GNCDB *db);
int sql_LimitTest1(TESTSUM *testSum, GNCDB *db);

int createClassTable(GNCDB *db)
{
  int rc = 0;
  // 创建一个年级学生表
  rc = GNCDB_exec(db,
      "CREATE TABLE students ("
      "student_id INT PRIMARY KEY,"
      "name CHAR(20),"
      "class INT,"
      "age INT,"
      "gender char(10),"
      "score FLOAT);",
      NULL,
      NULL,
      NULL);

  // 插入50条学生数据

  rc = GNCDB_exec(db, "INSERT INTO students VALUES(1, 'John Doe', 1, 15, 'Male', 85.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(2, 'Jane Smith', 1, 16, 'Female', 92.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(3, 'Alice Johnson', 1, 15, 'Female', 88.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(4, 'Bob Brown', 1, 16, 'Male', 76.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(5, 'Emily Davis', 1, 15, 'Female', 90.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(6, 'Michael Miller', 2, 15, 'Male', 85.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(7, 'Jessica Wilson', 2, 16, 'Female', 92.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(8, 'David Moore', 2, 15, 'Male', 88.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(9, 'Sarah Taylor', 2, 16, 'Female', 86.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(10, 'Daniel Anderson', 2, 15, 'Male', 80.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(11, 'Jennifer Thomas', 3, 15, 'Female', 95.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(12, 'Matthew Jackson', 3, 16, 'Male', 92.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(13, 'Amanda Harris', 3, 15, 'Female', 88.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(14, 'Donald White', 3, 16, 'Male', 76.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(15, 'Ashley Martin', 3, 15, 'Female', 90.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(16, 'Brian Thompson', 4, 15, 'Male', 55.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(17, 'Heather Garcia', 4, 16, 'Female', 32.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(18, 'Samuel Martinez', 4, 15, 'Male', 88.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(19, 'Melissa Robinson', 4, 16, 'Female', 76.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(20, 'Nicholas Clark', 4, 15, 'Male', 90.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(21, 'Sara Lewis', 5, 15, 'Female', 85.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(22, 'Andrew Hall', 5, 16, 'Male', 92.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(23, 'Stephanie Allen', 5, 15, 'Female', 88.5);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(24, 'Joshua Young', 5, 16, 'Male', 76.0);", NULL, NULL, NULL);
  rc = GNCDB_exec(db, "INSERT INTO students VALUES(25, 'Lauren King', 5, 15, 'Female', 40.0);", NULL, NULL, NULL);

  // 查询数据
  // rc = GNCDB_exec(db, "SELECT * FROM students;", testCallBack, NULL, NULL);
  // initFlag();

  return rc;
}

int sql_SpecialTest(TESTSUM *testSum)
{
  GNCDB *db        = NULL;
  int    i         = 0;
  int    rc        = 0;
  char  *errmsg    = NULL;
  FILE  *fileData  = NULL;
  char   sql[1024] = {0};

  char *path = strJoin(testFilePath, testFileName1);
  if (path == NULL) {
    return -1;
  };
  // 创建数据库
  remove("sqltest.dat");
  rc = GNCDB_open(&db, "sqltest.dat", 0, 0);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  test_table_count.db = db;
  // 创建表
  rc = GNCDB_exec(db,
      "CREATE TABLE WPT(WPT_ident CHAR(10) PRIMARY KEY NOT NULL, "
      "WPT_lon FLOAT  NOT NULL,"
      "WPT_lat  FLOAT	NOT NULL, "
      "WPT_blob  INT);",
      NULL,
      NULL,
      &errmsg);
  if (rc != GNCDB_SUCCESS) {
    GNCDB_close(&db);
    return rc;
  }
  rc = GNCDB_exec(db,
      "CREATE TABLE ARPT("
      "ARPT_ident CHAR(10) PRIMARY KEY NOT NULL,"
      "ARPT_lon FLOAT  NOT NULL,"
      "ARPT_lat FLOAT NOT NULL,"
      "ARPT_elev FLOAT NOT NULL,"
      "ARPT_length FLOAT NOT NULL,"
      "ARPT_mag_var FLOAT  NOT NULL);",
      NULL,
      NULL,
      &errmsg);
  // 插入数据

  fileData = fopen(path, "r");
  if (fileData == NULL) {
    return -1;
  }
  for (i = 0; i < 10; ++i) {
    fscanf(fileData, "%[^,],%lf,%lf,\n", wpt.sc8_wpt_ident, &wpt.f64_lon, &wpt.f64_lat);
    sprintf(sql,
        "INSERT INTO WPT(WPT_ident,WPT_lon,WPT_lat,WPT_blob) VALUES('%s',%f,%f,NULL);",
        wpt.sc8_wpt_ident,
        wpt.f64_lon,
        wpt.f64_lat);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);

    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  my_free(path);
  fclose(fileData);
  path = strJoin(testFilePath, testFileName2);
  if (path == NULL) {
    return -1;
  };
  fileData = fopen(path, "r");

  if (fileData == NULL) {
    return -1;
  }

  for (i = 0; i < 10; ++i) {
    fscanf(fileData,
        "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
        arpt.sc8_arpt_ident,
        &arpt.f64_lon,
        &arpt.f64_lat,
        &arpt.f64_elev,
        &arpt.f64_longest_rwy_length,
        &arpt.f64_mag_var);
    sprintf(sql,
        "INSERT INTO ARPT (ARPT_ident, ARPT_lon, ARPT_lat, ARPT_elev, ARPT_length, ARPT_mag_var)"
        "VALUES('%s', %lf, %lf, %lf, %lf, %lf);",
        arpt.sc8_arpt_ident,
        arpt.f64_lon,
        arpt.f64_lat,
        arpt.f64_elev,
        arpt.f64_longest_rwy_length,
        arpt.f64_mag_var);
    rc = GNCDB_exec(db, sql, NULL, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS) {
      addTest(testSum, rc);
      return -1;
    }
  }
  my_free(path);
  fclose(fileData);

  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "WPT");
  // initFlag();
  // GNCDB_select(db, testCallBack, NULL, NULL, 1, 0, 0, "ARPT");

  // 进行测试
  sql_AggregateFun(testSum, db);
  sql_SubqueryFun(testSum, db);
  sql_GroupByFun(testSum, db);
  sql_OrderByFun(testSum, db);
  sql_LimitFun(testSum, db);

  // 关闭数据库
  rc = GNCDB_close(&db);

  return rc;
}

/* 聚合函数 */
int sql_AggregateFun(TESTSUM *testSum, GNCDB *db)
{
  int rc                       = GNCDB_SUCCESS;
  test_table_count.testProject = "SQL查询测试 聚合函数";

  sql_AggregateTest1(testSum, db);
  sql_AggregateTest2(testSum, db);
  sql_AggregateTest3(testSum, db);
  sql_AggregateTest4(testSum, db);
  sql_AggregateTest5(testSum, db);

  return rc;
}

int sql_AggregateTest1(TESTSUM *testSum, GNCDB *db)
{
  /* 求和 */
  int    rc             = 0;
  double rows           = 0;
  double rows1          = 0;
  char  *errmsg         = NULL;
  char   aggregateStr[] = "1:sum";

  test_table_count.SQL = "SELECT SUM(WPT_lon) FROM WPT;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db, "SELECT SUM(WPT_lon) FROM WPT;", testCallBackSqlAggregate1, &rows, &errmsg);
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackAggregate1, NULL, &rows1, 1, 0, 0, "WPT");

  if (rc == GNCDB_SUCCESS && !compare_float(rows, rows1, 3)) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_AggregateTest2(TESTSUM *testSum, GNCDB *db)
{
  /* 平均 */
  int    rc             = 0;
  double rows           = 0;
  double rows1          = 0;
  int    rows2          = 0;
  char  *errmsg         = NULL;
  char   aggregateStr[] = "2:avg";

  test_table_count.SQL = "SELECT AVG(WPT_lon) FROM WPT;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db, "SELECT AVG(WPT_lon) FROM WPT;", testCallBackSqlAggregate2, &rows, &errmsg);
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackAggregate2, &rows2, &rows1, 1, 0, 0, "WPT");

  if (rc == GNCDB_SUCCESS && !compare_float(rows, rows1 / rows2, 3)) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_AggregateTest3(TESTSUM *testSum, GNCDB *db)
{
  /* 最小值 */
  int    rc             = 0;
  double rows           = 0;
  double rows1          = 0;
  int    rows2          = 0;
  char  *errmsg         = NULL;
  char   aggregateStr[] = "3:min";

  test_table_count.SQL = "SELECT MIN(WPT_lon) FROM WPT;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db, "SELECT MIN(WPT_lon) FROM WPT;", testCallBackSqlAggregate3, &rows, &errmsg);
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackAggregate3, &rows2, &rows1, 1, 0, 0, "WPT");

  if (rc == GNCDB_SUCCESS && !compare_float(rows, rows1, 3)) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_AggregateTest4(TESTSUM *testSum, GNCDB *db)
{
  /* 最大值 */
  int    rc             = 0;
  double rows           = 0;
  double rows1          = 0;
  int    rows2          = 0;
  char  *errmsg         = NULL;
  char   aggregateStr[] = "4:max";

  test_table_count.SQL = "SELECT MAX(WPT_lon) FROM WPT;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db, "SELECT MAX(WPT_lon) FROM WPT;", testCallBackSqlAggregate4, &rows, &errmsg);
  test_table_count.rowcount = 0;
  GNCDB_select(db, testCallBackAggregate4, &rows2, &rows1, 1, 0, 0, "WPT");

  if (rc == GNCDB_SUCCESS && rows == rows1) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_AggregateTest5(TESTSUM *testSum, GNCDB *db)
{
  /* 行数 */
  int    rc    = 0;
  double rows  = 0;
  int    rows1 = 0;
  // int rows2 = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "5:count";

  test_table_count.SQL = "SELECT COUNT(*) FROM WPT;";

  currentTest(testSum, aggregateStr);
  rc                        = GNCDB_exec(db, "SELECT COUNT(*) FROM WPT;", testCallBackSqlAggregate5, &rows, &errmsg);
  test_table_count.rowcount = 0;
  GNCDB_select(db, NULL, &rows1, NULL, 1, 0, 0, "WPT");

  if (rc == GNCDB_SUCCESS && rows == rows1) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

/* 子查询 */
int sql_SubqueryFun(TESTSUM *testSum, GNCDB *db)
{
  int rc                       = GNCDB_SUCCESS;
  test_table_count.testProject = "SQL查询测试 子查询";

  sql_SubqueryTest1(testSum, db);
  // sql_SubqueryTest2(testSum, db);
  // sql_SubqueryTest3(testSum, db);

  return rc;
}

int sql_SubqueryTest1(TESTSUM *testSum, GNCDB *db)
{
  /* 作为 WHERE 子句的条件 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "1:作为 WHERE 子句的条件";

  test_table_count.SQL = "SELECT * FROM WPT WHERE WPT_lon=(SELECT ARPT_lon FROM ARPT WHERE ARPT_ident='KDWTO');";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db,
      "SELECT * FROM WPT WHERE WPT_lon=(SELECT ARPT_lon FROM ARPT WHERE ARPT_ident='KDWTO');",
      testCallBackSqlSubquery1,
      &rows,
      &errmsg);
  if (rc == GNCDB_SUCCESS && rows) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_SubqueryTest2(TESTSUM *testSum, GNCDB *db)
{
  /* 作为 FROM 子句的一部分 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "2:作为 FROM 子句的一部分";

  test_table_count.SQL = "SELECT * FROM WPT INNER JOIN (SELECT ARPT_ident, ARPT_lon FROM ARPT) ON WPT_lon=ARPT_lon;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db,
      "SELECT * FROM WPT INNER JOIN (SELECT ARPT_ident, ARPT_lon FROM ARPT) ON WPT_lon=ARPT_lon;",
      testCallBackSqlSubquery2,
      &rows,
      &errmsg);
  if (rc == GNCDB_SUCCESS && rows != 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_SubqueryTest3(TESTSUM *testSum, GNCDB *db)
{
  /* 作为 SELECT 子句的一部分 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "3:作为 SELECT 子句的一部分";

  test_table_count.SQL = "SELECT WPT_ident, (SELECT ARPT_ident FROM ARPT WHERE ARPT_lon = WPT_lon) FROM WPT;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db,
      "SELECT WPT_ident, (SELECT ARPT_ident FROM ARPT WHERE ARPT_lon = WPT_lon) FROM WPT;",
      testCallBackSqlSubquery2,
      &rows,
      &errmsg);
  if (rc == GNCDB_SUCCESS && rows != 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

/* group by */
int sql_GroupByFun(TESTSUM *testSum, GNCDB *db)
{
  int rc                       = GNCDB_SUCCESS;
  test_table_count.testProject = "SQL查询测试 GroupBy";
  createClassTable(db);
  sql_GroupByTest1(testSum, db);

  return rc;
}

int sql_GroupByTest1(TESTSUM *testSum, GNCDB *db)
{
  /* 作为 WHERE 子句的条件 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "1:GroupBy";

  test_table_count.SQL = "SELECT class, AVG(score) FROM students GROUP BY class;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(
      db, "SELECT class, AVG(score) FROM students GROUP BY class;", testCallBackSqlSubquery2, &rows, &errmsg);
  rc = GNCDB_exec(db, "SELECT class, AVG(score) FROM students GROUP BY class;", testCallBackGroud, &rows, &errmsg);
  if (rc == GNCDB_SUCCESS && rows) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

/* order by */
int sql_OrderByFun(TESTSUM *testSum, GNCDB *db)
{
  int rc                       = GNCDB_SUCCESS;
  test_table_count.testProject = "SQL查询测试 OrderBy";

  sql_OrderByTest1(testSum, db);
  sql_OrderByTest2(testSum, db);
  sql_OrderByTest3(testSum, db);

  return rc;
}

int sql_OrderByTest1(TESTSUM *testSum, GNCDB *db)
{
  /* 按单个列升序排序 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "1:按单个列升序排序";

  test_table_count.SQL = "SELECT * FROM ARPT ORDER BY ARPT_lon ASC;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db, "SELECT * FROM ARPT ORDER BY ARPT_lon;", testCallBackSqlOrderBy1, &rows, &errmsg);
  // rc = GNCDB_exec(db, "SELECT * FROM ARPT ORDER BY ARPT_lon ASC;", testCallBack, NULL, &errmsg);
  if (rc == GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_OrderByTest2(TESTSUM *testSum, GNCDB *db)
{
  /* 按单个列降序排序 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "2:按单个列降序排序";

  test_table_count.SQL = "SELECT * FROM ARPT ORDER BY ARPT_lon DESC;";

  currentTest(testSum, aggregateStr);
  value111 = 222.0;
  rc       = GNCDB_exec(db, "SELECT * FROM ARPT ORDER BY ARPT_lon DESC;", testCallBackSqlOrderBy2, &rows, &errmsg);
  // rc = GNCDB_exec(db, "SELECT * FROM ARPT ORDER BY ARPT_lon DESC;", testCallBack, &rows, &errmsg);
  if (rc == GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

int sql_OrderByTest3(TESTSUM *testSum, GNCDB *db)
{
  /* 混合升序和降序排序 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "3:混合升序和降序排序";

  test_table_count.SQL = "SELECT * FROM ARPT ORDER BY ARPT_lon ASC, ARPT_lat DESC;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(
      db, "SELECT * FROM ARPT ORDER BY ARPT_lon ASC, ARPT_lat DESC;", testCallBackSqlOrderBy3, &rows, &errmsg);
  // rc = GNCDB_exec(db, "SELECT * FROM ARPT ORDER BY ARPT_lon ASC, ARPT_lat DESC;", testCallBack, &rows, &errmsg);
  if (rc == GNCDB_SUCCESS && rows == 0) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

/* limit */
int sql_LimitFun(TESTSUM *testSum, GNCDB *db)
{
  int rc                       = GNCDB_SUCCESS;
  test_table_count.testProject = "SQL查询测试 Limit";

  sql_LimitTest1(testSum, db);

  return rc;
}

int sql_LimitTest1(TESTSUM *testSum, GNCDB *db)
{
  /* 返回前 10 行 */
  int   rc             = 0;
  int   rows           = 0;
  char *errmsg         = NULL;
  char  aggregateStr[] = "1:Limit";

  test_table_count.SQL = "SELECT * FROM ARPT "
                         " ORDER BY ARPT_lon ASC "
                         " LIMIT 5;";

  currentTest(testSum, aggregateStr);
  rc = GNCDB_exec(db,
      " SELECT * FROM ARPT "
      " ORDER BY ARPT_lon ASC "
      " LIMIT 5; ",
      testCallBackSqlLimit1,
      &rows,
      &errmsg);
  if (rc == GNCDB_SUCCESS && rows == 5) {
    addTest(testSum, 0);
  } else {
    if (rc == GNCDB_SUCCESS) {
      rc = -1;
    }
    addTest(testSum, rc);
  }
  return rc;
}

/* ---------------------------------------
 * 以下是哈希索引测试的内容
 */

int  testEntryCnt        = 0;
char TEST_DB_FILE_NAME[] = "test_hash_index.dat";
char TEST_TABLE_NAME[]   = "combined_table";
char TEST_INDEX_NAME1[]  = "hash_int";
char TEST_INDEX_NAME2[]  = "hash_double";
char TEST_INDEX_NAME3[]  = "hash_char";
int  printHeader1        = 1;  // 静态变量，用于跟踪是否需要打印列名
int  myCallBack(void *data, int argc, char **azColName, char **argv)
{
  int i;
  // 如果printHeader为1，则打印列名
  if (printHeader1) {
    for (i = 0; i < argc; i++) {
      printf("%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
    }
    printf("\n");
    printHeader1 = 0;  // 设置为0，下次调用时不再打印列名
  }
  for (i = 0; i < argc; i++) {
    // printf("%da ",strlen(argv[i]));
    printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");
  }
  printf("\n");
  return 0;
}

int myCallBack_cnt(void *data, int argc, char **azColName, char **argv)
{
  testEntryCnt++;
  return GNCDB_SUCCESS;
}

int init_hash_index_test()
{
  int    rc = GNCDB_SUCCESS;
  GNCDB *db = NULL;

  // 删除原有文件（如果存在）
  if (remove(TEST_DB_FILE_NAME) == 0) {
    printf("文件 test_hash_index.dat 已成功删除\n");
  } else {
    printf("文件 test_hash_index.dat 不存在或删除失败\n");
  }

  rc = GNCDB_open(&db, TEST_DB_FILE_NAME, 0, 0);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = executeSQLFile(db, "./test/hash_index_test/test_sql_file/init.sql");
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = GNCDB_close(&db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return rc;
}

int createIntTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc        = 0;
  HashIndex *hashIndex = NULL;

  test_table_count.SQL = "create index hash_int on combined_table(int_value) using hash;";
  currentTest(testSum, "2:创建整数类型哈希索引");
  rc = GNCDB_exec(db, "create index hash_int on combined_table(int_value) using hash;", NULL, NULL, NULL);
  //* 检查master和schema表
  testEntryCnt = 0;
  rc           = GNCDB_exec(db,
      "select * from master where tableName = 'hash_int' and dependentTable = 'combined_table';",
      myCallBack_cnt,
      NULL,
      NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 1) {
    addTest(testSum, -1);
    return rc;
  }
  testEntryCnt = 0;
  rc           = GNCDB_exec(db,
      "select * from schema where tableName = 'hash_int' and columnName = 'int_value';",
      myCallBack_cnt,
      NULL,
      NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 1) {
    addTest(testSum, -1);
    return rc;
  }

  //* 获取索引并检查索引的键值对数: 10w条数据 */
  rc           = catalogGetHashIndex(db->catalog, &hashIndex, "combined_table.hash_int");
  testEntryCnt = getHashIndexKVCnt(hashIndex, db);
  if (testEntryCnt != 100000) {
    addTest(testSum, -1);
    return rc;
  }

  addTest(testSum, rc);
  return rc;
}

int createDoubleTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc        = 0;
  HashIndex *hashIndex = NULL;

  test_table_count.SQL = "create index hash_double on combined_table(double_value) using hash;";
  currentTest(testSum, "3:创建浮点数类型哈希索引");
  rc = GNCDB_exec(db, "create index hash_double on combined_table(double_value) using hash;", NULL, NULL, NULL);
  //* 检查master和schema表
  testEntryCnt = 0;
  rc           = GNCDB_exec(db,
      "select * from master where tableName = 'hash_double' and dependentTable = 'combined_table';",
      myCallBack_cnt,
      NULL,
      NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 1) {
    addTest(testSum, -1);
    return rc;
  }
  testEntryCnt = 0;
  rc           = GNCDB_exec(db,
      "select * from schema where tableName = 'hash_double' and columnName = 'double_value';",
      myCallBack_cnt,
      NULL,
      NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 1) {
    addTest(testSum, -1);
    return rc;
  }

  //* 获取索引并检查索引的键值对数: 10w条数据 */
  rc           = catalogGetHashIndex(db->catalog, &hashIndex, "combined_table.hash_double");
  testEntryCnt = getHashIndexKVCnt(hashIndex, db);
  if (testEntryCnt != 100000) {
    addTest(testSum, -1);
    return rc;
  }

  addTest(testSum, rc);
  return rc;
}

int createVarcharTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc        = 0;
  HashIndex *hashIndex = NULL;

  test_table_count.SQL = "create index hash_varchar on combined_table(varchar_value) using hash;";
  currentTest(testSum, "4:创建字符串类型哈希索引");
  rc = GNCDB_exec(db, "create index hash_varchar on combined_table(varchar_value) using hash;", NULL, NULL, NULL);
  //* 检查master和schema表
  testEntryCnt = 0;
  rc           = GNCDB_exec(db,
      "select * from master where tableName = 'hash_varchar' and dependentTable = 'combined_table';",
      myCallBack_cnt,
      NULL,
      NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 1) {
    addTest(testSum, -1);
    return rc;
  }
  testEntryCnt = 0;
  rc           = GNCDB_exec(db,
      "select * from schema where tableName = 'hash_varchar' and columnName = 'varchar_value';",
      myCallBack_cnt,
      NULL,
      NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 1) {
    addTest(testSum, -1);
    return rc;
  }

  //* 获取索引并检查索引的键值对数: 10w条数据 */
  rc           = catalogGetHashIndex(db->catalog, &hashIndex, "combined_table.hash_varchar");
  testEntryCnt = getHashIndexKVCnt(hashIndex, db);
  if (testEntryCnt != 100000) {
    addTest(testSum, -1);
    return rc;
  }

  addTest(testSum, rc);
  return rc;
}

int normalQueryTest(GNCDB *db, TESTSUM *testSum, double *time)
{
  char            sql[1024] = {0};
  int             rc        = 0;
  struct timespec start, end;

  test_table_count.SQL = "select id from combined_table where value = ?(no index)";
  currentTest(testSum, "1:无索引查询");
  clock_gettime(CLOCK_MONOTONIC, &start);
  /* 执行查询：分别100条数据 */
  for (int i = 1; i <= 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where int_value = %d;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (double i = 1.5; i <= 1000.5; i = i + 1.0) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where double_value = %lf;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (int i = 0; i < 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where varchar_value = 'value_%d';", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  clock_gettime(CLOCK_MONOTONIC, &end);
  *time = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  addTest(testSum, rc);
  return rc;
}

int indexQueryTest(GNCDB *db, TESTSUM *testSum, double *time)
{
  char            sql[1024] = {0};
  int             rc        = 0;
  struct timespec start, end;

  test_table_count.SQL = "select id from combined_table where value = ?(with index)";
  currentTest(testSum, "5:有索引查询");
  clock_gettime(CLOCK_MONOTONIC, &start);
  /* 执行查询：分别100条数据 */
  for (int i = 1; i <= 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where int_value = %d;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (double i = 1.5; i <= 1000.5; i = i + 1.0) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where double_value = %lf;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (int i = 0; i < 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where varchar_value = 'value_%d';", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  clock_gettime(CLOCK_MONOTONIC, &end);
  *time = (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
  addTest(testSum, rc);
  return rc;
}

int indexJudgeTest(TESTSUM *testSum, double time_no_index, double time_index)
{
  int    rc        = 0;
  double threshold = 0.2; /* 设置的判断阈值为索引查询时间应为无索引时的20% */
  char   text[256] = {0};

  sprintf(text, "time_indx <= time_no_index * %.2lf %% ?", threshold * 100);
  test_table_count.SQL = text;
  currentTest(testSum, "6:索引加速比较");
  addTest(testSum, rc);
  printf("total time_no_index: %.2lf ms, time_index: %.2lf ms, ratio: %.2lf %%\n",
      time_no_index,
      time_index,
      (time_index / time_no_index) * 100);
  return rc;
}

int createAndSelectIndexTest(GNCDB *db, TESTSUM *testSum)
{
  double time_no_index = 0;
  double time_index    = 0;

  test_table_count.testProject = "创建和查询哈希索引";

  /* 先获取创建索引之前的查询时间 */
  normalQueryTest(db, testSum, &time_no_index);

  /* 创建索引测试 */
  createIntTest(db, testSum);
  createDoubleTest(db, testSum);
  createVarcharTest(db, testSum);

  /* 再获取创建索引之后的查询时间 */
  indexQueryTest(db, testSum, &time_index);

  /* 比较判断索引是否加速查询 */
  indexJudgeTest(testSum, time_no_index, time_index);

  return 0;
}

int insertIndexTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  HashIndex *hashIndex3 = NULL;
  int        entryCnt1  = 0;
  int        entryCnt2  = 0;
  int        entryCnt3  = 0;
  char       sql[1024]  = {0};

  test_table_count.testProject = "哈希索引插入";

  test_table_count.SQL = "insert into combined_table values(?, ?, '?');";
  currentTest(testSum, "1:在建立哈希索引的表上插入1w条数据");

  /* 每个索引分别10W条entry */
  rc = catalogGetHashIndex(db->catalog, &hashIndex1, "combined_table.hash_int");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex2, "combined_table.hash_double");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex3, "combined_table.hash_varchar");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  entryCnt1 = getHashIndexKVCnt(hashIndex1, db);
  entryCnt2 = getHashIndexKVCnt(hashIndex2, db);
  entryCnt3 = getHashIndexKVCnt(hashIndex3, db);
  if (entryCnt1 != 100000 && entryCnt2 != 100000 && entryCnt3 != 100000) {
    addTest(testSum, -1);
    return -1;
  }

  /* 执行查询：分别100条数据 */
  for (int i = 1; i <= 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where int_value = %d;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (double i = 1.5; i <= 1000.5; i = i + 1.0) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where double_value = %lf;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (int i = 0; i < 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where varchar_value = 'value_%d';", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 100) {
      addTest(testSum, -1);
      return -1;
    }
  }

  /* 再次插入10000条数据 */
  rc = executeSQLFile(db, "./test/hash_index_test/test_sql_file/insert.sql");

  /* 每个索引分别11w条entry */
  entryCnt1 = getHashIndexKVCnt(hashIndex1, db);
  entryCnt2 = getHashIndexKVCnt(hashIndex2, db);
  entryCnt3 = getHashIndexKVCnt(hashIndex3, db);
  if (entryCnt1 != 110000 && entryCnt2 != 110000 && entryCnt3 != 110000) {
    addTest(testSum, -1);
    return -1;
  }

  /* 执行查询：分别110条数据 */
  for (int i = 1; i <= 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where int_value = %d;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (double i = 1.5; i <= 1000.5; i = i + 1.0) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where double_value = %lf;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (int i = 0; i < 1000; i++) {
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where varchar_value = 'value_%d';", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }

  addTest(testSum, rc);
  return rc;
}

int deleteIndexTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  HashIndex *hashIndex3 = NULL;
  int        entryCnt1  = 0;
  int        entryCnt2  = 0;
  int        entryCnt3  = 0;
  char       sql[1024]  = {0};

  test_table_count.testProject = "哈希索引删除";

  test_table_count.SQL = "delete from combined_table where int_value = 700;";
  currentTest(testSum, "1:在建立哈希索引的表上删除数据");

  /* 删除110条数据 */
  rc = GNCDB_exec(db, "delete from combined_table where int_value = 700;", NULL, NULL, NULL);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, -1);
    return rc;
  }

  /* 检查索引entry数，分别应为109890条 */
  rc = catalogGetHashIndex(db->catalog, &hashIndex1, "combined_table.hash_int");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex2, "combined_table.hash_double");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex3, "combined_table.hash_varchar");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  entryCnt1 = getHashIndexKVCnt(hashIndex1, db);
  entryCnt2 = getHashIndexKVCnt(hashIndex2, db);
  entryCnt3 = getHashIndexKVCnt(hashIndex3, db);
  if (entryCnt1 != 109890 && entryCnt2 != 109890 && entryCnt3 != 109890) {
    addTest(testSum, -1);
    return -1;
  }

  /* 这些值被删除，应为空 */
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where int_value = 700;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where double_value = 700.5;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }
  testEntryCnt = 0;
  rc = GNCDB_exec(db, "select * from combined_table where varchar_value = 'value_699';", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }

  /* 其余各110条entry */
  for (int i = 1; i <= 1000; i++) {
    if (i == 700) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where int_value = %d;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (double i = 1.5; i <= 1000.5; i = i + 1.0) {
    if (i == 700.5) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where double_value = %lf;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (int i = 0; i < 1000; i++) {
    if (i == 699) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where varchar_value = 'value_%d';", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }

  addTest(testSum, rc);
  return rc;
}

int reopenDeleteIndexTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  HashIndex *hashIndex3 = NULL;
  int        entryCnt1  = 0;
  int        entryCnt2  = 0;
  int        entryCnt3  = 0;
  char       sql[1024]  = {0};

  test_table_count.testProject = "哈希索引删除";

  test_table_count.SQL = "delete from combined_table where id >= 100000;";
  currentTest(testSum, "1:重新打开数据库并在建立哈希索引的表上删除数据");

  /* 删除110条数据 */
  rc = GNCDB_exec(db, "delete from combined_table where id >= 100000;", NULL, NULL, NULL);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, -1);
    return rc;
  }

  /* 检查索引entry数，分别应为109890条 */
  rc = catalogGetHashIndex(db->catalog, &hashIndex1, "combined_table.hash_int");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex2, "combined_table.hash_double");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex3, "combined_table.hash_varchar");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  entryCnt1 = getHashIndexKVCnt(hashIndex1, db);
  entryCnt2 = getHashIndexKVCnt(hashIndex2, db);
  entryCnt3 = getHashIndexKVCnt(hashIndex3, db);
  if (entryCnt1 != 109890 && entryCnt2 != 109890 && entryCnt3 != 109890) {
    addTest(testSum, -1);
    return -1;
  }

  /* 这些值被删除，应为空 */
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where int_value = 700;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where double_value = 700.5;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }
  testEntryCnt = 0;
  rc = GNCDB_exec(db, "select * from combined_table where varchar_value = 'value_699';", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }

  /* 其余各110条entry */
  for (int i = 1; i <= 1000; i++) {
    if (i == 700) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where int_value = %d;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (double i = 1.5; i <= 1000.5; i = i + 1.0) {
    if (i == 700.5) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where double_value = %lf;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  for (int i = 0; i < 1000; i++) {
    if (i == 699) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where varchar_value = 'value_%d';", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }

  addTest(testSum, rc);
  return rc;
}

int updateIntTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  HashIndex *hashIndex3 = NULL;
  int        entryCnt1  = 0;
  int        entryCnt2  = 0;
  int        entryCnt3  = 0;
  char       sql[1024]  = {0};

  test_table_count.SQL = "update combined_table set int_value = 900 where int_value = 100;";
  currentTest(testSum, "1:在建立哈希索引的表上更新int列数据");

  /* 更新110条数据 */
  rc = GNCDB_exec(db, "update combined_table set int_value = 900 where int_value = 100;", NULL, NULL, NULL);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, -1);
    return rc;
  }

  /* 检查索引entry数，分别应为109890条 */
  rc = catalogGetHashIndex(db->catalog, &hashIndex1, "combined_table.hash_int");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex2, "combined_table.hash_double");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex3, "combined_table.hash_varchar");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  entryCnt1 = getHashIndexKVCnt(hashIndex1, db);
  entryCnt2 = getHashIndexKVCnt(hashIndex2, db);
  entryCnt3 = getHashIndexKVCnt(hashIndex3, db);
  if (entryCnt1 != 109890 && entryCnt2 != 109890 && entryCnt3 != 109890) {
    addTest(testSum, -1);
    return -1;
  }

  /* 这些值被更新，应该为空 */
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where int_value = 100;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }
  /* 这些值被更新，应该翻倍为220 */
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where int_value = 900;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 220) {
    addTest(testSum, -1);
    return -1;
  }

  /* 除了被删除和更新的数据，其余各110条entry */
  for (int i = 1; i <= 1000; i++) {
    if (i == 100 || i == 900 || i == 700) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where int_value = %d;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  addTest(testSum, rc);
  return rc;
}

int updateDoubleTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  HashIndex *hashIndex3 = NULL;
  int        entryCnt1  = 0;
  int        entryCnt2  = 0;
  int        entryCnt3  = 0;
  char       sql[1024]  = {0};

  test_table_count.SQL = "update combined_table set double_value = 800.5 where double_value = 200.5;";
  currentTest(testSum, "2:在建立哈希索引的表上更新double列数据");

  /* 更新110条数据 */
  rc = GNCDB_exec(db, "update combined_table set double_value = 800.5 where double_value = 200.5;", NULL, NULL, NULL);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, -1);
    return rc;
  }

  /* 检查索引entry数，分别应为109890条 */
  rc = catalogGetHashIndex(db->catalog, &hashIndex1, "combined_table.hash_int");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex2, "combined_table.hash_double");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex3, "combined_table.hash_varchar");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  entryCnt1 = getHashIndexKVCnt(hashIndex1, db);
  entryCnt2 = getHashIndexKVCnt(hashIndex2, db);
  entryCnt3 = getHashIndexKVCnt(hashIndex3, db);
  if (entryCnt1 != 109890 && entryCnt2 != 109890 && entryCnt3 != 109890) {
    addTest(testSum, -1);
    return -1;
  }

  /* 这些值被更新，应该为空 */
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where double_value = 200.5;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }
  /* 这些值被更新，应该翻倍为220 */
  testEntryCnt = 0;
  rc           = GNCDB_exec(db, "select * from combined_table where double_value = 800.5;", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 220) {
    addTest(testSum, -1);
    return -1;
  }

  /* 除了被删除和更新的数据，其余各110条entry */
  for (double i = 1.5; i <= 1000.5; i = i + 1.0) {
    if (i == 200.5 || i == 800.5 || i == 700.5) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where double_value = %lf;", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  addTest(testSum, rc);
  return rc;
}

int updateVarcharTest(GNCDB *db, TESTSUM *testSum)
{
  int        rc         = 0;
  HashIndex *hashIndex1 = NULL;
  HashIndex *hashIndex2 = NULL;
  HashIndex *hashIndex3 = NULL;
  int        entryCnt1  = 0;
  int        entryCnt2  = 0;
  int        entryCnt3  = 0;
  char       sql[1024]  = {0};

  test_table_count.SQL = "update combined_table set varchar_value = 'value_700' where varchar_value = 'value_300';";
  currentTest(testSum, "3:在建立哈希索引的表上更新varchar列数据");

  /* 更新110条数据 */
  rc = GNCDB_exec(
      db, "update combined_table set varchar_value = 'value_700' where varchar_value = 'value_300';", NULL, NULL, NULL);
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, -1);
    return rc;
  }

  /* 检查索引entry数，分别应为109890条 */
  rc = catalogGetHashIndex(db->catalog, &hashIndex1, "combined_table.hash_int");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex2, "combined_table.hash_double");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  rc = catalogGetHashIndex(db->catalog, &hashIndex3, "combined_table.hash_varchar");
  if (rc != GNCDB_SUCCESS) {
    addTest(testSum, rc);
    return rc;
  }
  entryCnt1 = getHashIndexKVCnt(hashIndex1, db);
  entryCnt2 = getHashIndexKVCnt(hashIndex2, db);
  entryCnt3 = getHashIndexKVCnt(hashIndex3, db);
  if (entryCnt1 != 109890 && entryCnt2 != 109890 && entryCnt3 != 109890) {
    addTest(testSum, -1);
    return -1;
  }

  /* 这些值被更新，应该为空 */
  testEntryCnt = 0;
  rc = GNCDB_exec(db, "select * from combined_table where varchar_value = 'value_300';", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 0) {
    addTest(testSum, -1);
    return -1;
  }
  /* 这些值被更新，应该翻倍为220 */
  testEntryCnt = 0;
  rc = GNCDB_exec(db, "select * from combined_table where varchar_value = 'value_700';", myCallBack_cnt, NULL, NULL);
  if (rc != GNCDB_SUCCESS || testEntryCnt != 220) {
    addTest(testSum, -1);
    return -1;
  }

  /* 除了被删除和更新的数据，其余各110条entry */
  for (int i = 0; i < 1000; i++) {
    if (i == 700 || i == 300 || i == 699) {
      continue;
    }
    memset(sql, 0, 1024);
    sprintf(sql, "select id from combined_table where varchar_value = 'value_%d';", i);
    testEntryCnt = 0;
    rc           = GNCDB_exec(db, sql, myCallBack_cnt, NULL, NULL);
    if (rc != GNCDB_SUCCESS || testEntryCnt != 110) {
      addTest(testSum, -1);
      return -1;
    }
  }
  addTest(testSum, rc);
  return rc;
}

int updateIndexTest(GNCDB *db, TESTSUM *testSum)
{
  int rc = 0;

  test_table_count.testProject = "哈希索引更新";

  updateIntTest(db, testSum);
  updateDoubleTest(db, testSum);
  updateVarcharTest(db, testSum);

  return rc;
}

int hash_index_test(TESTSUM *testSum)
{
  GNCDB *db = NULL;
  int    rc = GNCDB_SUCCESS;

  rc = init_hash_index_test();
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  rc = GNCDB_open(&db, TEST_DB_FILE_NAME, 0, 0);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 创建哈希索引和查询测试 */
  createAndSelectIndexTest(db, testSum);
  /* 插入索引测试 */
  insertIndexTest(db, testSum);
  /* 索引上删除数据测试 */
  deleteIndexTest(db, testSum);
  /* 更新索引测试 */
  updateIndexTest(db, testSum);

  rc = GNCDB_close(&db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return 0;
}

/* 故障恢复测试 */
long get_file_size(const char *filename) {
    FILE *fp = fopen(filename, "rb");
    long size = 0;
    if (!fp) {
        perror("fopen");
        return -1;
    }

    if (fseek(fp, 0, SEEK_END) != 0) {
        perror("fseek");
        fclose(fp);
        return -1;
    }

    size = ftell(fp);
    if (size == -1L) {
        perror("ftell");
    }

    fclose(fp);
    return size;
}

/* 创建数据库创建表插入数据后恢复 */

/* 创建多个表后恢复 */
int createTableAddThread(GNCDB* db){
    int rc = 0;
	char* errmsg = NULL;
    rc = GNCDB_exec(db, "CREATE TABLE NAV(NAV_ident CHAR(8) PRIMARY KEY NOT NULL, "
        "NAV_type CHAR(2) NOT NULL,"
        "NAV_lon FLOAT  NOT NULL,"
        "NAV_lat  FLOAT	NOT NULL, "
        "NAV_vor FLOAT NOT NULL,"
        "NAV_dme FLOAT  NOT NULL,"
        "NAV_tacan FLOAT  NOT NULL,"
        "NAV_ndb FLOAT  NOT NULL);", NULL, NULL, &errmsg);
    if( rc != GNCDB_SUCCESS){
        return rc;
    }
    rc = GNCDB_exec(db, "CREATE TABLE SID(SID_arptident CHAR(8) NOT NULL, "
		"SID_sidident CHAR(8) PRIMARY KEY NOT NULL, "
		"SID_no CHAR(2) NOT NULL, "
		"SID_type CHAR(2) NOT NULL, "
		"SID_track FLOAT NOT NULL, "
		"SID_fix CHAR(8) NOT NULL, "
		"SID_cat CHAR(2) NOT NULL, "
		"SID_lon FLOAT  NOT NULL,"
		"SID_lat  FLOAT	NOT NULL, "
		"SID_rnp FLOAT NOT NULL,"
		"SID_alt FLOAT  NOT NULL);", NULL, NULL, &errmsg);
    if( rc != GNCDB_SUCCESS){
        return rc;
    }
	return rc;
}

int createTableAddThreadCheck(GNCDB* db){
    int rc = 0;
    char *name = my_malloc(20);
    memset(name, 0, 20);
    strcpy(name, "NAV");
    rc = GNCDB_exec(db, "SELECT * FROM master WHERE tableName='NAV';", testCallBackcheckTableNameMaster, name, NULL);
    if( rc != GNCDB_SUCCESS){
        my_free(name);
        return rc;
    }
    if(name[0] == '0'){
        my_free(name);
        return rc;
    }
    memset(name, 0, 20);
    strcpy(name, "SID");
    rc = GNCDB_exec(db, "SELECT * FROM master WHERE tableName='SID';", testCallBackcheckTableNameMaster, name, NULL);
    if( rc != GNCDB_SUCCESS){
        my_free(name);
        return rc;
    }
    if(name[0] == '0'){
        my_free(name);
        return rc;
    }
        my_free(name);
    return rc;
}

/* 插入多个表 */
int insertTableAddThread(GNCDB* db){

  return 0;
}

int insertTableAddThreadCheck(GNCDB* db){
    
  return 0;
}

/* 更新后恢复 */
int dbtast_updaterows = 0;
int updateTableAddThread(GNCDB* db){
    int rc = 0;
    int row = 0;
    rc = GNCDB_update(db, &row, "ARPT", 1, 1, "ARPT_lon", 10.0, "ARPT_lat=25.0");
    if( rc != GNCDB_SUCCESS){
        return rc;
    }
    dbtast_updaterows = row;
    return rc;
}

int updateTableAddThreadCheck(GNCDB* db){
  int rc = 0;
  int row = 0;
  rc = GNCDB_exec(db, "SELECT * FROM ARPT;", testCallBackcheckRecoverUpdate, &row, NULL);
  if( rc != GNCDB_SUCCESS){
        return rc;
  }
  if(row != dbtast_updaterows){
      return -1;
  }
  return 0;
}

int dbtast_deleterows = 0;
int deleteTableAddThread(GNCDB* db){
    int rc = 0;
    int row = 0;
    rc = GNCDB_delete(db, &row, "ARPT", 1, "ARPT_lon=0.0");
    if( rc != GNCDB_SUCCESS){
        return rc;
    }
    dbtast_deleterows = row;
    return rc;
}

int deleteTableAddThreadCheck(GNCDB* db){
  int rc = 0;
  int row = 0;
  rc = GNCDB_select(db, NULL, &row, NULL, 1, 0, 0, "ARPT");
  if( rc != GNCDB_SUCCESS){
        return rc;
  }
  if(row + dbtast_deleterows != 500){
      return -1;
  }
  return 0;
}


int pthread_count = 0;

void* sql_RecoveryThread(void* arg){
    int rc = 0;
    GNCDB* db = NULL;
    char sql[1024] = {0};
    int rows = 0;
    int* p_rc = (int*)arg;
    int i = 0;
    // char insertTable[] = "故障恢复功能测试";

    char* path = strJoin(testFilePath, testFileName2);
    FILE* fileData = NULL;


    // currentTest(testSum, insertTable);

    fileData = fopen(path, "r");

    if (fileData == NULL)
    {
        * p_rc = -1;
        // addTest(testSum, rc);
        return NULL;
    }

    remove("recover.dat");
    remove("log_recover.dat");

    rc = GNCDB_open(&db, "recover.dat", 0, 0);
    if (rc != GNCDB_SUCCESS){
        printf("Open database error!\n");
        // addTest(testSum, rc);
        * p_rc = rc;
        return NULL;
    }
    rc = GNCDB_exec(db, "CREATE TABLE ARPT("
        "ARPT_ident CHAR(8) PRIMARY KEY NOT NULL,"
        "ARPT_lon FLOAT  NOT NULL,"
        "ARPT_lat FLOAT NOT NULL,"
        "ARPT_elev FLOAT NOT NULL,"
        "ARPT_length FLOAT NOT NULL,"
        "ARPT_mag_var FLOAT  NOT NULL);", NULL, NULL, NULL);
    if (rc != GNCDB_SUCCESS){
        printf("Create table error!\n");
        // addTest(testSum, rc);
        * p_rc = rc;
        return NULL;
    }

    for (i = 0; i < 500; ++i)
    {
        fscanf(fileData, "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
                         arpt.sc8_arpt_ident, 
                         &arpt.f64_lon, 
                         &arpt.f64_lat, 
                         &arpt.f64_elev, 
                         &arpt.f64_longest_rwy_length, 
                         &arpt.f64_mag_var);
        sprintf(sql, "INSERT INTO ARPT (ARPT_ident, ARPT_lon, ARPT_lat, ARPT_elev, ARPT_length, ARPT_mag_var)"
        "VALUES('%s', %lf, %lf, %lf, %lf, %lf);", arpt.sc8_arpt_ident, 
        arpt.f64_lon, 
        arpt.f64_lat, 
        arpt.f64_elev, 
        arpt.f64_longest_rwy_length, 
        arpt.f64_mag_var);
        rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
        
        if (rc != GNCDB_SUCCESS)
        {
            // addTest(testSum, rc);
            * p_rc = rc;
            return NULL;
        }
    }
    fclose(fileData);

    rc = GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "ARPT");
    if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
    }
    if(rows != 500){
        // addTest(testSum, -1);
        * p_rc = -1;
        return NULL;
    }

    if(pthread_count == 0)
    {

    } else if(pthread_count == 1)
    {
      rc = createTableAddThread(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = createTableAddThreadCheck(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
    } else if(pthread_count == 2)
    {
      rc = updateTableAddThread(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = updateTableAddThreadCheck(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
    } else if(pthread_count == 3)
    {
      rc = deleteTableAddThread(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = deleteTableAddThreadCheck(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
    } else if(pthread_count == 4)
    {
      rc = createTableAddThread(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = createTableAddThreadCheck(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = updateTableAddThread(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = updateTableAddThreadCheck(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
    } else if(pthread_count == 5)
    {
      rc = deleteTableAddThread(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = deleteTableAddThreadCheck(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = createTableAddThread(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
      rc = createTableAddThreadCheck(db);
      if(rc != GNCDB_SUCCESS){
        * p_rc = rc;
        return NULL;
      }
    } else if(pthread_count == 6)
    {

    } else if(pthread_count == 7)
    {

    } else if(pthread_count == 8)
    {
      
    }


    * p_rc = 0;
    return NULL;
}

int sql_RecoveryFun(TESTSUM* testSum){
    int rc = 0;
    int p_rc = 0;
    pthread_t thread;
    long size = 0;

    GNCDB* db = NULL;
    int rows = 0;
    int arpt_row = 500;

    test_table_count.testProject = "0.故障恢复测试";

    for(pthread_count = 0; pthread_count < 6; ++pthread_count)
    {
      arpt_row = 500;
      if(pthread_count == 0)
      {
        char insertTable[] = "0.00:故障恢复功能测试";
        currentTest(testSum, insertTable);
      } else if(pthread_count == 1)
      {
        char insertTable[] = "0.01:创建表后故障恢复功能测试";
        currentTest(testSum, insertTable);
      } else if(pthread_count == 2)
      {
        char insertTable[] = "0.02:更新表后故障恢复功能测试";
        currentTest(testSum, insertTable);
      } else if(pthread_count == 3)
      {
        char insertTable[] = "0.03:删除表后故障恢复功能测试";
        currentTest(testSum, insertTable);
      } else if(pthread_count == 4)
      {
        char insertTable[] = "0.04:创建更新故障恢复功能测试";
        currentTest(testSum, insertTable);
      } else if(pthread_count == 5)
      {
        char insertTable[] = "0.05:删除创建故障恢复功能测试";
        currentTest(testSum, insertTable);
      }

      rc = pthread_create(&thread, NULL, sql_RecoveryThread, &p_rc);
      if (rc != 0){
          printf("Create thread error!\n");
      }
      pthread_join(thread, NULL);
      if(p_rc != 0){
          addTest(testSum, p_rc);
          return -1;
      }

      if(pthread_count == 3 || pthread_count == 5)
      {
        arpt_row -= dbtast_deleterows;
      }
      
      rc = GNCDB_open(&db, "recover.dat", 0, 0);
      if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
      }
      if (db == NULL){
          addTest(testSum, -1);
          return -1;
      }

      // 获取文件大小
      size = get_file_size("recover.dat");
      
      if(size != db->totalPageNum * db->pageCurrentSize){
          addTest(testSum, -1);
          return -1;
      }

      rc = GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "ARPT");
      if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
      }
      if(rows != arpt_row){
          addTest(testSum, -1);
          return -1;
      }
      if(pthread_count == 0)
      {

      } else if(pthread_count == 1)
      {
        rc = createTableAddThreadCheck(db);
        if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
        }
      } else if(pthread_count == 2)
      {
        rc = updateTableAddThreadCheck(db);
        if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
        }
      } else if(pthread_count == 3)
      {
        rc = deleteTableAddThreadCheck(db);
        if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
        }
      } else if(pthread_count == 4)
      {
        rc = createTableAddThreadCheck(db);
        if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
        }
        rc = updateTableAddThreadCheck(db);
        if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
        }
      } else if(pthread_count == 5)
      {
        rc = deleteTableAddThreadCheck(db);
        if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
        }
        rc = createTableAddThreadCheck(db);
        if(rc != GNCDB_SUCCESS){
          addTest(testSum, rc);
          return -1;
        }
      }
      addTest(testSum, 0);

      testCreateAfterRecovery(db, testSum);
      testInsertAfterRecovery(db, testSum);
      testSelectAfterRecovery(db, testSum, arpt_row);
      testUpdateAfterRecovery(db, testSum);
      testDeleteAfterRecovery(db, testSum, arpt_row); 
      GNCDB_close(&db);

    }


    return rc;
}


int testCreateAfterRecovery(GNCDB* db, TESTSUM* testSum){
    int rc = 0;
	  char* errmsg = NULL;
    char name[20] = "APCH";

    char insertTable[] = "0.1:故障恢复后创建";
    currentTest(testSum, insertTable);
  
    rc = GNCDB_exec(db, "CREATE TABLE APCH(APCH_arptident CHAR(8) NOT NULL, "
		"APCH_apchident CHAR(8) PRIMARY KEY NOT NULL, "
		"APCH_no CHAR(2) NOT NULL, "
		"APCH_type CHAR(2) NOT NULL, "
		"APCH_track FLOAT NOT NULL, "
		"APCH_fixident CHAR(8) NOT NULL, "
		"APCH_fixcat CHAR(2) NOT NULL, "
		"APCH_stage CHAR(2) NOT NULL, "
		"APCH_lon FLOAT  NOT NULL,"
		"APCH_lat  FLOAT	NOT NULL, "
		"APCH_rnp FLOAT NOT NULL,"
		"APCH_alt FLOAT  NOT NULL);", NULL, NULL, &errmsg);
    if( rc != GNCDB_SUCCESS){
      addTest(testSum, rc);
        return rc;
    }

    rc = GNCDB_exec(db, "SELECT * FROM master WHERE tableName='APCH';", testCallBackcheckTableNameMaster, name, NULL);
    if( rc != GNCDB_SUCCESS){
      addTest(testSum, rc);
        return rc;
    }

  addTest(testSum, rc);
	return rc;
}

// 故障恢复后插入
int testInsertAfterRecovery(GNCDB* db, TESTSUM* testSum){
    int rc = 0;
    char* path = strJoin(testFilePath, testFileName2);
    FILE* fileData = NULL;
    char sql[1024] = {0};
    int i = 0;

    char insertTable[] = "0.2:故障恢复后插入";
    currentTest(testSum, insertTable);

    fileData = fopen(path, "r");
    if (fileData == NULL)
    {
        rc = -1;
        addTest(testSum, rc);
        return rc;
    }
    for (i = 0; i < 500; ++i)
    {
        fscanf(fileData, "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
                         arpt.sc8_arpt_ident, 
                         &arpt.f64_lon, 
                         &arpt.f64_lat, 
                         &arpt.f64_elev, 
                         &arpt.f64_longest_rwy_length, 
                         &arpt.f64_mag_var);
    }
    for (i = 0; i < 200; ++i)
    {
        fscanf(fileData, "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
                         arpt.sc8_arpt_ident, 
                         &arpt.f64_lon, 
                         &arpt.f64_lat, 
                         &arpt.f64_elev, 
                         &arpt.f64_longest_rwy_length, 
                         &arpt.f64_mag_var);
        sprintf(sql, "INSERT INTO ARPT (ARPT_ident, ARPT_lon, ARPT_lat, ARPT_elev, ARPT_length, ARPT_mag_var)"
        "VALUES('%s', %lf, %lf, %lf, %lf, %lf);", arpt.sc8_arpt_ident, 
        arpt.f64_lon, 
        arpt.f64_lat, 
        arpt.f64_elev, 
        arpt.f64_longest_rwy_length, 
        arpt.f64_mag_var);
        rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
        if (rc != GNCDB_SUCCESS)
        {
            addTest(testSum, rc);
            return rc;
        }
    }
    fclose(fileData);
    addTest(testSum, rc);
    return 0;
}

// 故障恢复后查询
int testSelectAfterRecovery(GNCDB* db, TESTSUM* testSum, int arpt_row){
    int rc = 0;
    // char sql[1024] = {0};
    int rows = 0;
    char selectTable[] = "0.3:故障恢复后查询";
    currentTest(testSum, selectTable);

    rc = GNCDB_select(db, NULL, &rows, NULL, 1, 0, 0, "ARPT");
    if(rc != GNCDB_SUCCESS){
        addTest(testSum, rc);
        return rc;
    }
    if(rows != arpt_row + 200){
        rc = -1;
        addTest(testSum, -1);
        return rc;
    }
    addTest(testSum, 0);
    return rc;
}
// 故障恢复后更新
int testUpdateAfterRecovery(GNCDB* db, TESTSUM* testSum){
    int rc = 0;
    int rows = 0;
    int rowSelect = 0;

    char updataTable[] = "0.4:故障恢复后更新";
    currentTest(testSum, updataTable);

    rc = GNCDB_update(db, &rows, "ARPT", 1, 1, "ARPT_lat", 0.0, "ARPT_lon=45.0");
    if(rc != GNCDB_SUCCESS){
        addTest(testSum, rc);
        return rc;
    }
    rc = GNCDB_exec(db, "SELECT * FROM ARPT;", testCallBackUpdateAfterRecovery, &rowSelect, NULL);
    if(rc != GNCDB_SUCCESS){
        addTest(testSum, rc);
        return rc;
    }
    if(rowSelect != rows){
        addTest(testSum, -1);
        return -1;
    }
    addTest(testSum, 0);
    return rc;
}

// 故障恢复后删除
int testDeleteAfterRecovery(GNCDB* db, TESTSUM* testSum, int arpt_row){
    int rc = 0;
    int rows = 0;
    int rowSelect = 0;

    char updataTable[] = "0.5:故障恢复后删除";
    currentTest(testSum, updataTable);

    rc = GNCDB_delete(db, &rows, "ARPT", 1, "ARPT_lat=45.0");
    if(rc != GNCDB_SUCCESS){
        addTest(testSum, rc);
        return rc;
    }
    rc = GNCDB_select(db, NULL, &rowSelect, NULL, 1, 0, 0, "ARPT");
    if(rc != GNCDB_SUCCESS){
        addTest(testSum, rc);
        return rc;
    }
    if(rowSelect + rows != arpt_row + 200){
        addTest(testSum, -1);
        return -1;
    }
    addTest(testSum, 0);
    return rc;
}