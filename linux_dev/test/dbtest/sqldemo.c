#include "sqldemo.h"
#include "singletest.h"


#if defined(_WIN32)

char sqlFilePath[60] = "..\\testfile\\datafile\\";

#else
char sqlFilePath[60] = "./testfile/datafile/";
#endif

/* 文件名 */
char sqlFileName2[30] = "ARPT.txt";
char sqlFileName1[30] = "WPT.txt";

WPT_ROW sqlwpt;
ARPT_ROW sqlarpt;

int Sql_DemoFun()
{
    int rc = 0;
    GNCDB* sql_db = NULL;
    char* filename = "SQL_db.dat";
    remove(filename);
    remove("log_SQL_db.dat");
    /* 创建数据库 */
    rc = GNCDB_open(&sql_db, filename, 0, 0);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 创建表 */
    rc = SQLdemoCreateTable(sql_db);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 插入数据 */
    rc = SQLdemoInsertTable(sql_db);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 查询数据*/
    rc = SQLdemoSelectTable(sql_db);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 更新数据 */
    rc = SQLdemoUpdateTable(sql_db);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 删除数据 */
    rc = SQLdemoDeleteTable(sql_db);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 聚集函数 */
    rc = SQLdemoAggregateTable(sql_db);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 关闭数据库 */
    rc = GNCDB_close(&sql_db);
    return rc;
}


int SQLdemoCreateTable(GNCDB* sql_db)
{
    int rc = 0;
    char* errmsg = NULL;
    printf("******************创建表******************\n");
    /* 创建WPT表 */
    rc = GNCDB_exec(sql_db, "CREATE TABLE WPT"
    "(WPT_ident CHAR(10) PRIMARY KEY NOT NULL, "
    "WPT_lon FLOAT, "
    "WPT_lat FLOAT, "
    "WPT_blob INT);", NULL, NULL, &errmsg);
    if(rc != GNCDB_SUCCESS)
    {
        printf("创建WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("WPT表创建成功\n");
    /* 创建ARPT表 */
    rc = GNCDB_exec(sql_db, "CREATE TABLE ARPT("
        "ARPT_ident  CHAR(10) PRIMARY KEY NOT NULL,"
        "ARPT_lon FLOAT  NOT NULL,"
        "ARPT_lat FLOAT NOT NULL,"
        "ARPT_elev FLOAT NOT NULL,"
        "ARPT_length FLOAT NOT NULL,"
        "ARPT_mag_var FLOAT  NOT NULL);", NULL, NULL, &errmsg);
    if(rc != GNCDB_SUCCESS)
    {
        printf("创建ARPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("ARPT表创建成功\n");

    return rc;
}

int SQLdemoInsertTable(GNCDB* sql_db)
{
    int rc = 0;
    int i = 0;
    char* errmsg = NULL;
    char sql[1024] = {0};
    char path[200] = {0};
    FILE* fp = NULL;
    /* 插入数据 */
    printf("******************给WPT表插入100条数据******************\n");
    sprintf(path, "%s%s", sqlFilePath, sqlFileName1);
    fp = fopen(path, "r");
    if(fp == NULL)
    {
        printf("打开文件失败\n");
        return -1;
    }
    for (i = 0; i < 100; ++i)
    {
        fscanf(fp, "%[^,],%lf,%lf,\n", 
        sqlwpt.sc8_wpt_ident, 
        &sqlwpt.f64_lon, 
        &sqlwpt.f64_lat);
        sprintf(sql, "INSERT INTO WPT(WPT_ident,WPT_lon,WPT_lat,WPT_blob) VALUES('%s',%f,%f,0);", 
        sqlwpt.sc8_wpt_ident, 
        sqlwpt.f64_lon, 
        sqlwpt.f64_lat);
        rc = GNCDB_exec(sql_db, sql, NULL, NULL, &errmsg);
        if (rc != GNCDB_SUCCESS)
        {
            printf("插入WPT表失败, 状态码 rc = %d\n", rc);
            return rc;
        }
    }
    fclose(fp);
    printf("插入WPT表成功\n");
    printf("******************给ARPT表插入100条数据******************\n");
    sprintf(path, "%s%s", sqlFilePath, sqlFileName2);
    fp = fopen(path, "r");
    if(fp == NULL)
    {
        printf("打开文件失败\n");
        return -1;
    }
    for (i = 0; i < 100; ++i)
    {
        fscanf(fp, "%[^,],%lf,%lf,%lf,%lf,%lf,\n",
                         sqlarpt.sc8_arpt_ident, 
                        &sqlarpt.f64_lon, 
                        &sqlarpt.f64_lat, 
                        &sqlarpt.f64_elev, 
                        &sqlarpt.f64_longest_rwy_length, 
                        &sqlarpt.f64_mag_var);
        sprintf(sql, "INSERT INTO ARPT (ARPT_ident, ARPT_lon, ARPT_lat, ARPT_elev, ARPT_length, ARPT_mag_var)"
        "VALUES('%s', %lf, %lf, %lf, %lf, %lf);", 
        sqlarpt.sc8_arpt_ident, 
        sqlarpt.f64_lon, 
        sqlarpt.f64_lat, 
        sqlarpt.f64_elev, 
        sqlarpt.f64_longest_rwy_length, 
        sqlarpt.f64_mag_var);
        rc = GNCDB_exec(sql_db, sql, NULL, NULL, &errmsg);
        if (rc != GNCDB_SUCCESS)
        {
            printf("插入ARPT表失败, 状态码 rc = %d\n", rc);
            return rc;
        }
    }
    fclose(fp);
    printf("插入ARPT表成功\n");
    return rc;
}

int SQLdemoSelectTable(GNCDB* sql_db)
{
    int rc = 0;
    char sql[1024] = {0};
    char* errmsg = NULL;
    /* 查询WPT表 */
    printf("******************查询演示******************\n");
    printf("查询演示1: 主键查询, 查询条件: WPT_ident='SRORR'\n");
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_ident='SRORR';");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("查询WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    memset(sql, 0, sizeof(sql));
    printf("查询演示2: 范围查询, 查询条件: WPT_ident>'AFTTM' AND WPT_ident<'CQRCT'\n");
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_ident>'AFTTM' AND WPT_ident<'CQRCT';");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("查询WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    memset(sql, 0, sizeof(sql));
    printf("查询演示3: 非主键查询, 查询条件: WPT_lon=45.0\n");
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_lon=45.0;");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("查询WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    memset(sql, 0, sizeof(sql));
    printf("查询演示4: 非主键范围查询, 查询条件: WPT_lon>100.0 AND WPT_lat<0.0\n");
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_lon>100.0 AND WPT_lat<0.0;");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("查询WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    memset(sql, 0, sizeof(sql));
    printf("查询演示5: 连接查询, 查询条件: WPT_ident=ARPT_ident\n");
    sprintf(sql, "SELECT * FROM WPT INNER JOIN ARPT ON WPT_ident=ARPT_ident;");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("查询WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    return rc;
}

int SQLdemoUpdateTable(GNCDB* sql_db)
{
    int rc = 0;
    char sql[1024] = {0};
    char* errmsg = NULL;
    /* 更新WPT表 */
    printf("******************更新演示******************\n");
    printf("更新演示1: 主键更新, 更新条件: WPT_ident='SRORR', 更新内容: WPT_lon=3.14\n");
    printf("更新前:\n");
    initFlag();
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_ident='SRORR';");
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "UPDATE WPT SET WPT_lon=3.14 WHERE WPT_ident='SRORR';");
    rc = GNCDB_exec(sql_db, sql, NULL, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("更新WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("更新后:\n");
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_ident='SRORR';");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    
    printf("更新演示2: 非主键更新, 更新条件: WPT_lon=45.0, 更新内容: WPT_lat=-3.14\n");
    printf("更新前:\n");
    initFlag();
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_lon=45.0;");
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "UPDATE WPT SET WPT_lat=-3.14 WHERE WPT_lon=45.0;");
    rc = GNCDB_exec(sql_db, sql, NULL, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("更新WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("更新后:\n");
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_lon=45.0;");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    return rc;
}

int SQLdemoDeleteTable(GNCDB* sql_db)
{
    int rc = 0;
    char sql[1024] = {0};
    char* errmsg = NULL;
    /* 删除WPT表 */
    printf("******************删除演示******************\n");
    printf("删除演示1: 主键删除, 删除条件: WPT_ident='BHYLY'\n");
    printf("删除前:\n");
    initFlag();
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_ident>='B' AND WPT_ident<'C';");
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "DELETE FROM WPT WHERE WPT_ident='BHYLY';");
    rc= GNCDB_exec(sql_db, sql, NULL, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("删除WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("删除后:\n");
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_ident>='B' AND WPT_ident<'C';");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    printf("删除演示2: 范围删除, 删除条件: WPT_lat>0.0 AND WPT_lat<25.0\n");
    printf("删除前:\n");
    initFlag();
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_lat>=0.0 AND WPT_lat<=25.0;");
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "DELETE FROM WPT WHERE WPT_lat>0.0 AND WPT_lat<25.0;");
    rc = GNCDB_exec(sql_db, sql, NULL, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("删除WPT表失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("删除后:\n");
    memset(sql, 0, sizeof(sql));
    sprintf(sql, "SELECT * FROM WPT WHERE WPT_lat>=0.0 AND WPT_lat<=25.0;");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);

    return rc;
}

int SQLdemoAggregateTable(GNCDB* sql_db)
{
    int rc = 0;
    char sql[1024] = {0};
    char* errmsg = NULL;
    /* 求和 */
    printf("******************聚合演示******************\n");
    printf("聚合演示1: 求和 求和内容: ARPT_mag_var\n");
    sprintf(sql, "SELECT SUM(ARPT_mag_var) FROM ARPT;");
    rc = GNCDB_exec(sql_db, sql, SQLdemoCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("求和失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("聚合演示2: 平均 平均内容: ARPT_elev\n");
    sprintf(sql, "SELECT AVG(ARPT_elev) FROM ARPT;");
    rc = GNCDB_exec(sql_db, sql, SQLdemoCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("平均失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("聚合演示3: 最大 最大内容: ARPT_lon\n");
    sprintf(sql, "SELECT MAX(ARPT_lon) FROM ARPT WHERE ARPT_lon<=90.0;");
    rc = GNCDB_exec(sql_db, sql, SQLdemoCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("最大失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("聚合演示4: 最小 最小内容: ARPT_lon\n");
    sprintf(sql, "SELECT MIN(ARPT_lon) FROM ARPT WHERE ARPT_lon>=90.0;");
    rc = GNCDB_exec(sql_db, sql, SQLdemoCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("最小失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("聚合演示5: 统计 统计内容: ARPT\n");
    sprintf(sql, "SELECT COUNT(*) FROM ARPT;");
    rc = GNCDB_exec(sql_db, sql, SQLdemoCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("统计失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("聚合演示6: 排序 排序内容: ARPT_lon\n");
    sprintf(sql, "SELECT * FROM ARPT WHERE ARPT_ident<='CIRKL' ORDER BY ARPT_lon ASC;");
    initFlag();
    rc = GNCDB_exec(sql_db, sql, testCallBack, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("排序失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    printf("聚合演示7: 分组 分组内容: class\n");
    printf("创建学生表并插入数据");
    createClassTable(sql_db);
    sprintf(sql, "SELECT class, AVG(score) FROM students GROUP BY class;");
    rc = GNCDB_exec(sql_db, sql, testCallBackGroud, NULL, &errmsg);
    if (rc != GNCDB_SUCCESS)
    {
        printf("分组失败, 状态码 rc = %d\n", rc);
        return rc;
    }
    return rc;
}



/* callback函数 */
int SQLdemoCallBack(void *NotUsed, int argc, char** azColName, char** argv)
{
    int i;
    for (i = 0; i < argc; i++)
    {
        printf("%s = %s\n", azColName[i], argv[i] ? argv[i] : "NULL");
    }
    printf("\n");
    return 0;
}