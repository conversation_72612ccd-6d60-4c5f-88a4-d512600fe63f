#ifndef _DBTEST_H_
#define _DBTEST_H_

#include <stdio.h>
#include "gncdb.h"

// #define SQL_TESTMODE			1

typedef struct TESTSUM TESTSUM;

int parserTest();
int dbtest();




int testCreatedb(GNCDB** db, void** dbVoid, TESTSUM* testSum);
int testCreateTable(GNCDB* db, TESTSUM* testSum);
int testInsertTable(GNCDB* db, TESTSUM* testSum);
int testBlobTable(GNCDB* db, TESTSUM* testSum);
int testSelectTable(GNCDB* db, TESTSUM* testSum);
int testUpdataTable(GNCDB* db, TESTSUM* testSum);
int testDeleteTable(GNCDB* db, TESTSUM* testSum);
int testDropTable(GNCDB* db, TESTSUM* testSum);
int testOpendb(GNCDB** db, TESTSUM* testSum);
int testOpendbInsert(GNCDB* db, TESTSUM* testSum);
int testOpendbBlob(GNCDB* db, TESTSUM* testSum);
int testOpendbSelect(GNCDB* db, TESTSUM* testSum);
int testOpendbUpdate(GNCDB* db, TESTSUM* testSum);
int testOpendbDelete(GNCDB* db, TESTSUM* testSum);
int testOpendbDrop(GNCDB* db, TESTSUM* testSum);
int testClosedb(GNCDB* db, TESTSUM* testSum);
int sql_test(GNCDB* db, TESTSUM* testSum);

extern int tpccCallBack(void* data, int argc, char** azColName, char** argv);

/* 测试记录结构体 */
typedef struct TESTSUM
{
	FILE* fp;
	int allTest;
	int successTest;
	int failTest;

}TESTSUM;

typedef struct CREATETABLE
{
	GNCDB* db;
	char* testProject;
	char* SQL;
	int rowNum;
	int rowcount;
	int* index;
	char* tableName;
	char** fieldName;
	bool testFlag;
}CREATETABLE;

extern CREATETABLE test_table_count;
extern char blob_key[16];
extern double value111;

#define TEST_OUTPUT_SELECT	2				/* 0 控制台 1 文件 2 控制台+文件 */
#define REPEAT_COUNT 100
#define TABLEMAXROWS 10000
#define CREATE_TABLE_NUM 10
#define DROP_TABLE_NUM 4
#define WPTROWS  1000
#define ARPRROWS  1000
#define BLOBFILESIZE 1048576
#define WPTBLOBROWS  20

TESTSUM* initTest();
void outputTestAttr(TESTSUM* testSum);
void currentTest(TESTSUM* testSum, char* str);
void addTest(TESTSUM* testSum, int rc);
void displayTest(TESTSUM* testSum);
char* strJoin(char* s1, char* s2);

/* 回调函数相关参数 */
extern bool tableExist;
void initFlag();
int testCallBack(void *NotUsed, int argc, char** azColName, char** argv);

int testCallBackGroud(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackMasterTable(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackSchemaTable(void *NotUsed, int argc, char** azColName, char** argv);
/* 在对表进行插入时检查结果的回调函数 */
int testCallBackInsertTable(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackInsertsinglerowTable(void *NotUsed, int argc, char** azColName, char** argv);
/* 进行查询时不需要查询到的数据的回调函数 */
int testNoNeedDataCallBack(void *NotUsed, int argc, char** azColName, char** argv);
/* 大对象测试获取主键值的回调函数 */
int testBlobCallBack(void *NotUsed, int argc, char** azColName, char** argv);
/* 查询判断等于 */
int testCallBackSelectTableEqual(void *NotUsed, int argc, char** azColName, char** argv);
/* 查询判断小于 */
int testCallBackSelectTableLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 查询判断小于或等于 */
int testCallBackSelectTableLessOrEqual(void *NotUsed, int argc, char** azColName, char** argv);
/* 查询判断大于 */
int testCallBackSelectTableGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 查询判断大于或等于 */
int testCallBackSelectTableGreaterOrEqual(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询2等于 */
int testCallBackSelectUnionEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 查询时比较float类型数据的回调函数 */
int testCallBackSelectTableFloatLessOrEqual(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackSelectTableFloatGreater(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackSelectTableFloatGreaterOrEqual(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于＋小于 */
int testCallBackSelectUnionLessLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于＋小于等于 */
int testCallBackSelectUnionLessLessEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于＋大于 */
int testCallBackSelectUnionLessGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于＋大于等于 */
int testCallBackSelectUnionLessGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于等于＋小于 */
int testCallBackSelectUnionLessEqualsLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于等于＋小于等于 */
int testCallBackSelectUnionLessEqualsLessEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于等于＋大于 */
int testCallBackSelectUnionLessEqualsGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询小于等于＋大于等于 */
int testCallBackSelectUnionLessEqualsGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于＋小于 */
int testCallBackSelectUnionGreaterLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于＋小于等于 */
int testCallBackSelectUnionGreaterLessEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于＋大于 */
int testCallBackSelectUnionGreaterGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于＋大于等于 */
int testCallBackSelectUnionGreaterGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于等于＋小于 */
int testCallBackSelectUnionGreaterEqualsLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于等于＋小于等于 */
int testCallBackSelectUnionGreaterEqualsLessEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于等于＋大于 */
int testCallBackSelectUnionGreaterEqualsGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 联合查询大于等于＋大于等于 */
int testCallBackSelectUnionGreaterEqualsGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询小于+小于+小于 */
int testCallBackUnionLessLessLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询小于+小于+大于 */
int testCallBackUnionLessLessGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询小于+大于+小于 */
int testCallBackUnionLessGreaterLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询小于+大于+大于 */
int testCallBackUnionLessGreaterGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询大于+小于+小于 */
int testCallBackUnionGreaterLessLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询大于+小于+大于 */
int testCallBackUnionGreaterLessGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询大于+大于+小于 */
int testCallBackUnionGreaterGreaterLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 多条件查询大于+大于+大于 */
int testCallBackUnionGreaterGreaterGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 四条件范围查询 */
int testCallBackUnionfourUnionCondition(void *NotUsed, int argc, char** azColName, char** argv);
/* 四条件查询 */
int testCallBackUnionfourCondition(void *NotUsed, int argc, char** azColName, char** argv);
/* 四条件乱序查询 */
int testCallBackUnionfoursCondition(void *NotUsed, int argc, char** azColName, char** argv);
/* 全表查询＋投影 */
int testCallBackSelectAllProject(void *NotUsed, int argc, char** azColName, char** argv);
/* 单条件查询＋投影 */
int testCallBackSelectConditionProject(void *NotUsed, int argc, char** azColName, char** argv);
/* 全连接 */
int testCallBackAllJoin(void *NotUsed, int argc, char** azColName, char** argv);
/* 单表条件连接 */
int testCallBackConditionJoin(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接等于 */
int testCallBackConditionJoinEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接小于 */
int testCallBackConditionJoinLess(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接小于等于 */
int testCallBackConditionJoinLessEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接大于 */
int testCallBackConditionJoinGreater(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接大于等于 */
int testCallBackConditionJoinGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接等于 */
int testCallBackConditionJoinEqualsFloat(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接小于 */
int testCallBackConditionJoinLessFloat(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接小于等于 */
int testCallBackConditionJoinLessEqualsFloat(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接大于 */
int testCallBackConditionJoinGreaterFloat(void *NotUsed, int argc, char** azColName, char** argv);
/* 连接条件连接大于等于 */
int testCallBackConditionJoinGreaterEqualsFloat(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_65(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_66(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_67(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_68(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_69(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_71(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_72(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_73(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_74(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_75(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_76(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_77(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_78(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_79(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_80(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_81(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_82(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_83(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_84(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_85(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_86(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_87(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_88(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_89(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_90(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_91(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_92(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_93(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_94(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_95(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_96(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_97(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_98(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack5_99(void *NotUsed, int argc, char** azColName, char** argv);

int testCallBack6_2(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack6_3(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack6_4(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack6_5(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack6_6(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBack6_7(void *NotUsed, int argc, char** azColName, char** argv);

int testCallBackUpDate(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackAllUpDate(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackDelete(void *NotUsed, int argc, char** azColName, char** argv);

int testCallBackBlobSize(void *NotUsed, int argc, char** azColName, char** argv);


int callBackSQLGetRows(void *NotUsed, int argc, char** azColName, char** argv);

// =======================航路点数据WPT============================
// 数据表行结构体
typedef struct {
	char  sc8_wpt_ident[8];							// 航路点标识 
	double f64_lon;                                 // 航路点经度 
	double f64_lat;                                 // 航路点纬度 
}WPT_ROW;
// ========================机场数据ARPT=========================

// 数据表行结构体
typedef struct {
	char  sc8_arpt_ident[8];						// 机场标识       
	double f64_lon;									// 机场参考点经度 
	double f64_lat;									// 机场参考点纬度 
	double f64_elev;								// 机场标高       
	double f64_longest_rwy_length;					// 最长跑道长度   
	double f64_mag_var;								// 磁差 
}ARPT_ROW;

/***************************单元测试****************************************************/

int testInterEntryOrder();
int compare_float(float a, float b, int decimal_places);
int createClassTable(GNCDB* db);


int sql_RecoveryFun(TESTSUM* testSum);
int testCreateAfterRecovery(GNCDB* db, TESTSUM* testSum);
int testInsertAfterRecovery(GNCDB* db, TESTSUM* testSum);
int testSelectAfterRecovery(GNCDB* db, TESTSUM* testSum, int arpt_row);
int testUpdateAfterRecovery(GNCDB* db, TESTSUM* testSum);
int testDeleteAfterRecovery(GNCDB* db, TESTSUM* testSum, int arpt_row);



#endif // !_DBTEST_H_

