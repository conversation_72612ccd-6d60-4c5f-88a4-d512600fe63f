#include "btreepage.h"
#include "singletest.h"

int testInterEntryOrder()
{
    varArrayList* entryArray = NULL;
    varArrayList* primaryTypeArray = NULL;
    char str[][6] = {
            "ABCDN",
            "FREDS",
            "EWDSA",
            "AEEDC",
            "BGTR<PERSON>",
            "MIHJG",
            "GTGHG",
            "MVDFG",
            "ZXCVF",
            "REFDS"
    };
    int type = 2;
    int i = 0;
    entryArray = varArrayListCreate(ORDER, BYTES_POINTER, 0, internalEntryCompareFun, internalEntryDestroy);
	if (entryArray == NULL) {
		return GNCDB_SPACE_LACK;
	}

	primaryTypeArray = varArrayListCreate(DISORDER, 4, 5, intCompare, NULL);
	type = 2;
	varArrayListAdd(primaryTypeArray, &type);
	entryArray->reserve = primaryTypeArray;
	for (i = 0; i < 10; ++i)
	{
		InternalEntry* inter = internalEntryConstruct(i);
		if (inter == NULL)
		{
			return GNCDB_SPACE_LACK;
		}
		
		varArrayListAddPointer(inter->keyValueArray, str[i]);
		// p = p + 1;
		varArrayListAddPointer(entryArray, inter);

	}

	for (i = 0; i < entryArray->elementCount; ++i)
	{
		// InternalEntry* inter = varArrayListGetPointer(entryArray, i);
		// char* key = varArrayListGetPointer(inter->keyValueArray, 0);
		// printf("%s\n", key);
	}

	return 0;
}