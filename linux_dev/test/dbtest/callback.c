#include "singletest.h"
#include "callback.h"
#include <math.h>

int flag = 0;

void initFlag()
{
    flag = 0;
}

int testCallBack(void *NotUsed, int argc, char** azColName, char** argv)
{
    int i = 0;
    if (flag == 0)
    {
        //printf("查询出的记录：\n");
        printf("\n");
        printf("|");
        for (i = 0; i < argc; i++)
        {
            printf("%16s|", azColName[i]);
        }
        printf("\n");
        for (i = 0; i <= argc * 17; ++i)
        {
            printf("-");
        }
        printf("\n");
    }

    printf("|");
    for (i = 0; i < argc; i++)
    {
        printf("%16s|", argv[i]);
    }
    printf("\n");

    flag = 1;

    return 0;
}

int testCallBackGroud(void *NotUsed, int argc, char** azColName, char** argv)
{
    int i = 0;
    for (i = 0; i < argc; i++)
    {
        printf("%s : %s\n", azColName[i], argv[i]);
    }
    return 0;
}

int testCallBackMasterTable(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[1], test_table_count.tableName) == 0)
    {
        tableExist = true;
    }
    return 0;
}

int testCallBackSchemaTable(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    char* azColName1 = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (test_table_count.rowcount < test_table_count.rowNum)
    {
        azColName1 = test_table_count.fieldName[test_table_count.rowcount];
        if (tableExist && strcmp(argv[1], azColName1) == 0)
        {
            tableExist = true;
        }
        else
        {
            tableExist = false;
        }

    }
    test_table_count.rowcount++;
    return 0;
}

int testCallBackInsertTable(void *NotUsed, int argc, char** azColName, char** argv)
{
    int i = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }

    test_table_count.rowcount++;

    for (i = 0; i < test_table_count.rowNum; ++i)
    {
        // printf("%s  %s\n",argv[i], test_table_count.fieldName[i]);
        if (strncmp(argv[i], test_table_count.fieldName[i], 4) == 0)
        {
            test_table_count.testFlag = true;
        }
        else
        {
            test_table_count.testFlag = false;
            return -1;
        }
    }
    return 0;
}

int testCallBackInsertsinglerowTable(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[0], test_table_count.fieldName[test_table_count.rowcount]) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowNum++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    test_table_count.rowcount++;
    return 0;
}

int testNoNeedDataCallBack(void *NotUsed, int argc, char** azColName, char** argv)
{
    return 0;
}

int testBlobCallBack(void *NotUsed, int argc, char** azColName, char** argv)
{
    memset(blob_key, 0, 16);
    strcpy(blob_key, argv[0]);
    return 0;
}

int testCallBackSelectTableEqual(void *NotUsed, int argc, char** azColName, char** argv)
{
    if (strncmp(argv[test_table_count.rowNum], test_table_count.fieldName[0], 2) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectTableLess(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[test_table_count.rowNum], test_table_count.fieldName[0]) < 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectTableLessOrEqual(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[test_table_count.rowNum], test_table_count.fieldName[0]) <= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

float string_to_float(char* str) {
    float result = 0.0f;
    float frac = 0.1f;
    int sign = 1;
    int decimal_seen = 0;

    if (*str == '-') {
        sign = -1;
        str++;
    }
    else if (*str == '+') {
        str++;
    }

    while (*str != '\0') {
        if (*str == '.') {
            decimal_seen = 1;
            str++;
            continue;
        }
        if (*str >= '0' && *str <= '9') {
            if (decimal_seen) {
                result = result + frac * (*str - '0');
                frac /= 10.0f;
            }
            else {
                result = result * 10.0f + (*str - '0');
            }
        }
        else {
            return sign * result;
        }
        str++;
    }
    return sign * result;
}

int testCallBackSelectTableFloatLessOrEqual(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    // 测试5.12
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[test_table_count.rowNum]);
    num2 = string_to_float(test_table_count.fieldName[0]);

    if (num1 <= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectTableGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[test_table_count.rowNum], test_table_count.fieldName[0]) > 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectTableFloatGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    
    // 测试5.13
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[test_table_count.rowNum]);
    num2 = string_to_float(test_table_count.fieldName[0]);

    if (num1 > num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectTableGreaterOrEqual(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[test_table_count.rowNum], test_table_count.fieldName[0]) >= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectTableFloatGreaterOrEqual(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    // 测试5.14
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[test_table_count.rowNum]);
    num2 = string_to_float(test_table_count.fieldName[0]);

    if (num1 >= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    int index1 = 0;
    int index2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    if (strcmp(argv[index1], test_table_count.fieldName[0]) == 0 && strncmp(argv[index2], test_table_count.fieldName[1], 4) == 0)
    {
        test_table_count.testFlag = true;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessLess(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.15
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 < num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessLessEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.16
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 <= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.17
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 > num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.18
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 >= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessEqualsLess(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.19
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) <= 0 && num1 < num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessEqualsLessEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.20
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) <= 0 && num1 <= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessEqualsGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.21
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) <= 0 && num1 > num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionLessEqualsGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.22
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) <= 0 && num1 >= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterLess(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.23
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 < num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterLessEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.24
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 <= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    
    //5.25
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 > num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    
    //5.26
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 >= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterEqualsLess(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    //5.27
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) >= 0 && num1 < num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterEqualsLessEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
   int* p = NULL;
    
    //5.28
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) >= 0 && num1 <= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterEqualsGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.29
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) >= 0 && num1 > num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectUnionGreaterEqualsGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    
    //5.30
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) >= 0 && num1 >= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionLessLessLess(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.34
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 < num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionLessLessGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    
    //5.35
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 < num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionLessGreaterLess(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.36
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 > num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionLessGreaterGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.37
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 > num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionGreaterLessLess(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.38
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 < num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionGreaterLessGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.39
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 < num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionGreaterGreaterLess(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.40
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 > num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionGreaterGreaterGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.41
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) > 0 && num1 > num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionfourUnionCondition(void *NotUsed, int argc, char** azColName, char** argv) {
    int* p = NULL;
    //5.42
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);
    num3 = string_to_float(test_table_count.fieldName[0]);
    num4 = string_to_float(test_table_count.fieldName[1]);

    if (num1 <= num3 && num1 >= num4 && num2 <= num3 && num2 >= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionfourCondition(void *NotUsed, int argc, char** azColName, char** argv)
{
    
    //5.43
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    int index4 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    float num5 = 0;
    float num6 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];
    index4 = test_table_count.index[3];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);
    num5 = string_to_float(argv[index4]);
    num6 = string_to_float(test_table_count.fieldName[3]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num1 > num2 && num3 <= num4 && num5 >= num6)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackUnionfoursCondition(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    //5.44
    int index1 = 0;
    int index2 = 0;
    int index3 = 0;
    int index4 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    float num5 = 0;
    float num6 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    index3 = test_table_count.index[2];
    index4 = test_table_count.index[3];

    num1 = string_to_float(argv[index2]);
    num2 = string_to_float(test_table_count.fieldName[1]);
    num3 = string_to_float(argv[index3]);
    num4 = string_to_float(test_table_count.fieldName[2]);
    num5 = string_to_float(argv[index4]);
    num6 = string_to_float(test_table_count.fieldName[3]);

    if (strcmp(argv[index1], test_table_count.fieldName[0]) < 0 && num5 >= num2 && num3 <= num4 && num1 > num6)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectAllProject(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    //5.45
    if (strcmp(azColName[0], test_table_count.fieldName[0]) == 0 && strcmp(azColName[1], test_table_count.fieldName[1]) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackSelectConditionProject(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    //5.47
    if (strcmp(azColName[0], test_table_count.fieldName[0]) == 0 && strcmp(azColName[1], test_table_count.fieldName[1]) == 0
        && strcmp(azColName[2], test_table_count.fieldName[2]) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackAllJoin(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    test_table_count.rowcount++;
    return 0;
}

int testCallBackConditionJoin(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[0], test_table_count.fieldName[0]) >= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    if (strcmp(argv[index1], argv[index2]) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinLess(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    if (strcmp(argv[index1], argv[index2]) < 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinLessEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    if (strcmp(argv[index1], argv[index2]) <= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinGreater(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    if (strcmp(argv[index1], argv[index2]) > 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinGreaterEquals(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    if (strcmp(argv[index1], argv[index2]) >= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinEqualsFloat(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);

    if (num1 == num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinLessFloat(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);

    if (num1 < num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinLessEqualsFloat(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];
    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);

    if (num1 <= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinGreaterFloat(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);

    if (num1 > num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackConditionJoinGreaterEqualsFloat(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);

    if (num1 >= num2)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_65(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    float num3 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);
    num3 = string_to_float(argv[1]);

    if (num1 == num2 && num3 == 45.0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_66(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    float num3 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);
    num3 = string_to_float(argv[1]);

    if (num1 == num2 && num3 == 45.0 && strcmp(argv[0], "SCXEL") <= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_67(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 =0;
    float num2 =0;
    float num3 =0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);
    num3 = string_to_float(argv[8]);

    if (num1 == num2 && num3 <= 1725.522568)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_68(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    float num1 = 0;
    float num2 = 0;
    float num3 = 0;
    float num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);
    num3 = string_to_float(argv[8]);
    num4 = string_to_float(argv[7]);

    if (num1 == num2 && num3 <= 1725.522568 && num4 > 24.959593)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_69(void *NotUsed, int argc, char** azColName, char** argv)
{
    int index1 = 0;
    int index2 = 0;
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    index1 = test_table_count.index[0];
    index2 = test_table_count.index[1];

    num1 = string_to_float(argv[index1]);
    num2 = string_to_float(argv[index2]);
    num3 = string_to_float(argv[1]);
    num4 = string_to_float(argv[7]);

    if (num1 == num2 && num3 >= 0.0 && num4 > 24.959593)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_71(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 == num2 && num3 == num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_72(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 == num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_73(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    int* p = NULL;
    double num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 == num2 && num3 <= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_74(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 == num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_75(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 == num2 && num3 >= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_76(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 < num2 && num3 == num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_77(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 < num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_78(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 < num2 && num3 <= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_79(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 < num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_80(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 < num2 && num3 >= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_81(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 <= num2 && num3 == num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_82(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 <= num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_83(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 <= num2 && num3 <= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_84(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 <= num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_85(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 <= num2 && num3 >= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_86(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 > num2 && num3 == num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_87(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 > num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_88(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 > num2 && num3 <= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_89(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 > num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_90(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 > num2 && num3 >= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_91(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 >= num2 && num3 == num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_92(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 >= num2 && num3 < num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_93(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 >= num2 && num3 <= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_94(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    int* p = NULL;
    double num4 = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 >= num2 && num3 > num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_95(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 >= num2 && num3 >= num4)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_96(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 >= num2 && num3 >= num4 && strcmp(argv[0], "LSUWL") <= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_97(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);

    if (num1 >= num2 && num3 >= num4 && strcmp(argv[4], "LSUWL") >= 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_98(void *NotUsed, int argc, char** azColName, char** argv)
{
    double num1 = 0;
    double num2 = 0;
    double num3 = 0;
    double num4 = 0;
    double num5 = 0;
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    num1 = string_to_float(argv[1]);
    num2 = string_to_float(argv[5]);
    num3 = string_to_float(argv[2]);
    num4 = string_to_float(argv[6]);
    num5 = string_to_float(argv[9]);

    if (num1 >= num2 && num3 >= num4 && strcmp(argv[4], "LSUWL") >= 0 && strcmp(argv[0], "JDLYH") < 0 && num5 > 5.222)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack5_99(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(azColName[0], test_table_count.fieldName[0]) == 0
        && strcmp(azColName[1], test_table_count.fieldName[1]) == 0
        && strcmp(azColName[2], test_table_count.fieldName[2]) == 0
        && strcmp(azColName[3], test_table_count.fieldName[3]) == 0
        && strcmp(azColName[4], test_table_count.fieldName[4]) == 0
        && strcmp(azColName[5], test_table_count.fieldName[5]) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack6_2(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strcmp(argv[2], "0.000000") == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack6_3(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strncmp(argv[2], "42.000000", 2) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack6_4(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strncmp(argv[1], "52.000000", 2) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack6_5(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strncmp(argv[2], "100.000000", 3) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack6_6(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strncmp(argv[1], "-10.000000", 3) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBack6_7(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    if (strncmp(argv[2], "10.000000", 2) == 0)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    else
    {
        test_table_count.testFlag = false;
    }
    return 0;
}

int testCallBackAllUpDate(void *NotUsed, int argc, char** azColName, char** argv)
{
    // int* p = NULL;
    // int i = 0;
    if (strncmp(argv[2], "0.000000", 1) == 0)
    {
        test_table_count.testFlag = true;
    }
    else
    {
        test_table_count.testFlag = false;
        return -1;
    }
    return 0;
}

int testCallBackUpDate(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    int flag1 = 0;
    int i = 0;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    // for(i = 0; i < argc; ++i)
    // {
    //     printf("%s = %s\n", azColName[i], argv[i]);
    // }
    for (i = 0; i < test_table_count.rowNum; ++i)
    {
        if (strncmp(argv[test_table_count.index[i]], test_table_count.fieldName[i], 2) == 0)
        {
            flag1++;
        }
        else
        {
            test_table_count.testFlag = false;
            return -1;
        }
    }
    if (flag1 == test_table_count.rowNum)
    {
        test_table_count.testFlag = true;
        test_table_count.rowcount++;
    }
    return 0;
}

int testCallBackDelete(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    test_table_count.rowcount++;
    return 0;
}

int testCallBackBlobSize(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p = NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) += 1;
    }
    test_table_count.rowcount = atoi(argv[3]);

    return 0;
}

int compare_float(float a, float b, int decimal_places) {
    // 计算比较的精度
    float precision = pow(10, -decimal_places);
    
    // 计算两个数的差值
    float diff = fabs(a - b);
    
    // 如果差值小于精度，则认为两个数相等
    if (diff < precision) {
        return 0; // 相等
    } else if (a < b) {
        return -1; // a 小于 b
    } else {
        return 1; // a 大于 b
    }
}

int testCallBackSqlAggregate1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum;
    }
    return 0;
}

int testCallBackAggregate1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) += sum;
    }
    return 0;
}

int testCallBackSqlAggregate2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum;
    }
    return 0;
}

int testCallBackAggregate2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) += sum;
    }
    return 0;
}

int testCallBackSqlAggregate3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum;
    }
    return 0;
}

int testCallBackAggregate3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum < (*p) ? sum : (*p);
    }
    return 0;
}

int testCallBackSqlAggregate4(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum;
    }
    return 0;
}

int testCallBackAggregate4(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum > (*p) ? sum : (*p);
    }
    return 0;
}

int testCallBackSqlAggregate5(void *NotUsed, int argc, char** azColName, char** argv)
{
    // todo
    double sum = 0;
    double* p= NULL;
    sum = string_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        (*p) = sum;
    }
    return 0;
}

int testCallBackSqlSubquery1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    int* p= NULL;
    sum = string_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        if(sum == 0.000000)
        {
            (*p) = 1;
        }
        else
        {
            (*p) = 0;
        }
    }
    
    return 0;
}

int testCallBackSqlSubquery2(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p= NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p) = 1;

    }
    
    return 0;
}

double value111 = -200.0;
int testCallBackSqlOrderBy1(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    double* p = NULL;
    sum = string_to_float(argv[0]);
    if(NotUsed != NULL)
    {
        p = (double*)NotUsed;
        if(sum >= value111)
        {
            value111 = sum;
        }
        else{
            *p = *p + 1;
        }  
    }
    
    return 0;
}

int testCallBackSqlOrderBy2(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum = 0;
    int* p= NULL;
    sum = string_to_float(argv[1]);
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        if(sum <= value111)
        {
            value111 = sum;
        }
        else{
            *p = *p + 1;
        }  
    }
    
    return 0;
}

double value1 = -200.0;
double value2 = 200.0;
int testCallBackSqlOrderBy3(void *NotUsed, int argc, char** azColName, char** argv)
{
    double sum1 = 0;
    double sum2 = 0;
    int* p= NULL;
    sum1 = string_to_float(argv[1]);
    sum2 = string_to_float(argv[2]);
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        if(sum1 >= value1)
        {
            if(value1 == sum1){
                if(sum2 <= value2){
                    value2 = sum2;
                }
                else{
                    *p = *p + 1;
                }  
                value2 = sum2;
            }
            value1 = sum1;
        }
        else{
            *p = *p + 1;
        }  
    }
    
    return 0;
}

int testCallBackSqlLimit1(void *NotUsed, int argc, char** azColName, char** argv)
{
    int* p= NULL;
    if(NotUsed != NULL)
    {
        p = (int*)NotUsed;
        (*p)++;

    }
    return 0;
}

int callBackSQLGetRows(void *NotUsed, int argc, char** azColName, char** argv)
{
    if(NotUsed != NULL)
    {
        int* num = (int*)NotUsed;
        (*num) = atoi(argv[0]);
    }

    return 0;
}
int testCallBackUpdateAfterRecovery(void *NotUsed, int argc, char** azColName, char** argv){
    int* num = (int*)NotUsed;
    double value = atof(argv[1]);
    double value2 = atof(argv[2]);
    if(value == 45.0){
        if(value2 == 0.0){
            (*num)++;
        }
    }
    return 0;
}

int testCallBackcheckTableNameMaster(void *NotUsed, int argc, char** azColName, char** argv){
    char* num = (char*)NotUsed;
    if(strcmp(argv[1], num) == 0){
        num[0] = '1';
    }
    else{
        num[0] = '0'; 
    }
    return 0;
}

int testCallBackcheckTableNameSchema(void *NotUsed, int argc, char** azColName, char** argv){
    char* num = (char*)NotUsed;
    if(strcmp(argv[1], num) == 0){
        num[0] = '1';
    }
    else{
        num[0] = '0'; 
    }
    return 0;
}


int testCallBackcheckRecoverUpdate(void *NotUsed, int argc, char** azColName, char** argv){
    int* num = (int*)NotUsed;
    double valueTab1 = atof(argv[1]);
    double valueTab2 = atof(argv[2]);
    if(valueTab2 == 25.0){
        if(valueTab1 == 10.0){
            (*num)++;
        }
    }
    return 0;
}