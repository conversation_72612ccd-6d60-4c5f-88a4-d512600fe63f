#ifndef _SQLDEMO_H_
#define _SQLDEMO_H_

#include <stdio.h>
#include <string.h>
#include "gncdb.h"

int Sql_DemoFun();

int SQLdemoCreateTable(GNCDB* sql_db);
int SQLdemoInsertTable(GNCDB* sql_db);
int SQLdemoSelectTable(GNCDB* sql_db);
int SQLdemoUpdateTable(GNCDB* sql_db);
int SQLdemoDeleteTable(GNCDB* sql_db);
int SQLdemoAggregateTable(GNCDB* sql_db);

int SQLdemoCallBack(void *NotUsed, int argc, char** azColName, char** argv);

#endif