#ifndef CALLBACK_H
#define CALLBACK_H


extern int testCallBackSqlAggregate1(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackAggregate1(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlAggregate2(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackAggregate2(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlAggregate3(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackAggregate3(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlAggregate4(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackAggregate4(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlAggregate5(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlSubquery1(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlSubquery2(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlOrderBy1(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlOrderBy2(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlOrderBy3(void *NotUsed, int argc, char** azColName, char** argv);
extern int testCallBackSqlLimit1(void *NotUsed, int argc, char** azColName, char** argv);

int testCallBackUpdateAfterRecovery(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackcheckTableNameMaster(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackcheckTableNameSchema(void *NotUsed, int argc, char** azColName, char** argv);
int testCallBackcheckRecoverUpdate(void *NotUsed, int argc, char** azColName, char** argv);
#endif