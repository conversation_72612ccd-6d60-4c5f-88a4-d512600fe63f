cmake_minimum_required(VERSION 3.10)

# 设置可执行文件输出目录
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/build)


add_subdirectory(dbtest)
add_subdirectory(concurrence)
add_subdirectory(demo)

if(WIN32)
    
else()

    add_subdirectory(tpc-h)
    add_subdirectory(tpc-c)
    add_subdirectory(performance)
    add_subdirectory(multicon)
    add_subdirectory(complexquery)
    add_subdirectory(dbperformancetest)

endif()

aux_source_directory(. SRC_LIST)
add_executable(gncdb ${SRC_LIST})

# find_program(iwyu_path NAMES include-what-you-use iwyu)
# if(NOT iwyu_path)
#   message(FATAL_ERROR "Could not find the program include-what-you-use")
# else()
#   set_property(TARGET gncdb PROPERTY C_INCLUDE_WHAT_YOU_USE ${iwyu_path})
# endif()
# target_link_options(gncdb PRIVATE -no-pie)

# find_package(Thread REQUIRED)

if(WIN32)

target_link_libraries(gncdb
    
    gncdb_dbtest
    gncdb_concurrence
    gncdb_demo

    gncdb_common
    gncdb_qp
    gncdb_cachemanager
    gncdb_tranmanager
    gncdb_index
    gncdb_storage
    gncdb_system
    gncdb_utils
    
    )


else()


target_link_libraries(gncdb
    
    gncdb_dbtest
    gncdb_complexquery
    gncdb_multicon
    gncdb_concurrence
    gncdb_dbpertest
    gncdb_tpcctest
    gncdb_tpch1
    gncdb_demo
    gncdb_performance

    
    gncdb_common
    gncdb_qp
    gncdb_cachemanager
    gncdb_tranmanager
    gncdb_index
    gncdb_storage
    gncdb_system
    gncdb_utils
    
    )

endif()

# find_library(ZLIB_LIB z PATHS /usr/lib/x86_64-linux-gnu)
# target_link_libraries(gncdb ZLIB_LIB)

# 链接第三方库
# target_link_libraries(gncdb profiler unwind)

if(WIN32)
    target_link_libraries(gncdb C:/msys64/mingw64/lib/libregex.dll.a)
    target_link_libraries(gncdb C:/msys64/mingw32/lib/libregex.dll.a)
else()
    target_link_libraries(gncdb ${CMAKE_SOURCE_DIR}/test/testdependlib/sqlitelib/libsqlite3.a)
    target_link_libraries(gncdb ${CMAKE_SOURCE_DIR}/test/testdependlib/sqlitelib/libsqlite3.a dl)
    target_link_libraries(gncdb ${CMAKE_SOURCE_DIR}/malloc/mallocLib.a)
    target_link_libraries(gncdb ${CMAKE_SOURCE_DIR}/test/testdependlib/xlsxwriter/libxlsxwriter.a)
    target_link_libraries(gncdb  /usr/lib/x86_64-linux-gnu/libz.a)
    target_link_libraries(gncdb m)
    # target_link_libraries(gncdb profiler unwind)
endif()

target_link_libraries(gncdb -lpthread)
