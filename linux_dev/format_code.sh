#!/bin/bash

# 脚本功能：自动格式化所有 .c 和 .h 文件，排除指定文件和文件夹，并使用当前目录下的 .clang-format 配置文件

# 检查 clang-format 是否已安装
if ! command -v clang-format &> /dev/null; then
    echo "错误: clang-format 未安装，请先安装 clang-format。"
    exit 1
fi

# 检查 .clang-format 文件是否存在
if [ ! -f ".clang-format" ]; then
    echo "错误: 当前目录下未找到 .clang-format 文件，请确保配置文件存在。"
    exit 1
fi

# 定义格式化文件的函数
format_files() {
    # 查找 .c 和 .h 文件，排除指定的文件或目录
    find ./query_processing -type f \( -name "*.c" -o -name "*.h" \) \
        ! -path "./.cache/*" \
        ! -path "./*ccls-cache*/*" \
        ! -name "compile_commands.json" \
        ! -name "*.db" \
        ! -name "yacc_sql.*" \
        ! -name "lex_sql.*" | while read -r file; do
        echo "格式化文件: $file"
        # 使用当前目录下的 .clang-format 配置文件
        clang-format -i -style=file "$file"
    done
}

# 输出脚本说明
echo "开始格式化所有 .c 和 .h 文件（使用当前目录下的 .clang-format 配置文件）..."
format_files
echo "格式化完成！"
