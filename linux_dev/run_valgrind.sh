#!/bin/bash
###
 # @Author: zql <EMAIL>
 # @Date: 2025-07-15 10:32:39
 # @LastEditors: zql <EMAIL>
 # @LastEditTime: 2025-07-24 11:41:34
 # @FilePath: /gncdbflr/linux_dev/run_valgrind.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

# 定义可执行文件路径
EXECUTABLE="./bin/gncdb"

# 定义 valgrind 选项
VALGRIND_OPTIONS="--leak-check=full --show-leak-kinds=all -s  --track-origins=yes --log-file=valgrind_output.log"
# VALGRIND_OPTIONS="--leak-check=full --log-file=valgrind_output.txt --track-origins=yes"

# 检查可执行文件是否存在
if [ ! -f "$EXECUTABLE" ]; then
    echo "Error: Executable $EXECUTABLE not found."
    exit 1
fi

# 运行 valgrind 并输出到日志文件
echo "Running valgrind with $EXECUTABLE..."
valgrind $VALGRIND_OPTIONS $EXECUTABLE

# 检查 valgrind 是否成功运行
if [ $? -eq 0 ]; then
    echo "Valgrind finished successfully. Log saved to valgrind_output.log."
else
    echo "Valgrind encountered an error."
    exit 1
fi
