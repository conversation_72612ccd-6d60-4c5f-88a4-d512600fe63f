#!/bin/bash

rm -rf ./build
rm -rf ./bin
rm -rf ./output
rm -rf ./log
rm -f ./testAP
rm -f ./tpcc.db
rm -f ./log_tpcc.db

# Find and remove directories named "CMakeFiles"
find . -type d -name "CMakeFiles" -exec rm -rf {} +

# Find and remove files named "cmake_install.cmake"
find . -type f -name "cmake_install.cmake" -exec rm -f {} +

# Find and remove files named "Makefile"
find . -type f -name "Makefile" -exec rm -f {} +

echo "Specified files and directories have been removed."