#!/bin/bash
###
 # @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 # @Date: 2024-09-02 16:09:39
 # @LastEditors: zql <EMAIL>
 # @LastEditTime: 2025-07-29 10:35:19
 # @FilePath: /gncdbflr/linux_dev/build.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 

if [ ! -d "./bin" ];then
    mkdir bin
fi

if [ ! -d "./build" ];then
    mkdir build
fi

if [ ! -d "./log" ];then
    mkdir log
fi

if [ ! -d "./testfile/blob/blobresult" ];then
    mkdir ./testfile/blob/blobresult
fi

if [ ! -d "./testfile/result" ];then
    mkdir ./testfile/result
fi

cd ./build
cmake -DCMAKE_C_COMPILER=gcc -DCAMKE_BUILD_TYPE=Debug -DCMAKE_C_FLAGS="-m64" ..
# cmake -DCMAKE_C_COMPILER=gcc -DCAMKE_BUILD_TYPE=Debug ..
# cmake -DCMAKE_C_COMPILER=gcc-11 -DCAMKE_BUILD_TYPE=Release ..
# 赋值${TOPDIR}/build/compile_commands.json 到${TOPDIR}/compile_commands.json
# cp compile_commands.json ../
# make -j ${nproc}cd ./build
# cmake -DCMAKE_C_COMPILER=gcc -DCAMKE_BUILD_TYPE=Debug -DCMAKE_C_FLAGS="-m32" ..
cp compile_commands.json ../
make -j ${nproc}