# 删除顶层 bin 文件夹（如果存在）
$binPath = Join-Path -Path (Get-Location) -ChildPath "bin"
if (Test-Path $binPath) {
    Remove-Item -Path $binPath -Recurse -Force
    Write-Host "Deleted bin directory."
}

# 删除顶层 build 文件夹
$buildPath = Join-Path -Path (Get-Location) -ChildPath "build"
if (Test-Path $buildPath) {
    Remove-Item -Path $buildPath -Recurse -Force
    Write-Host "Deleted build directory."
}

# 删除所有名为 "CMakeFiles" 的目录
Get-ChildItem -Recurse -Directory -Filter "CMakeFiles" | Remove-Item -Recurse -Force

# 删除所有名为 "cmake_install.cmake" 的文件
Get-ChildItem -Recurse -File -Filter "cmake_install.cmake" | Remove-Item -Force

# 删除所有名为 "Makefile" 的文件
Get-ChildItem -Recurse -File -Filter "Makefile" | Remove-Item -Force

Write-Host "Specified files and directories have been removed."