cmake_minimum_required(VERSION 3.10)
project(PROJECT_GNCDB C)

# 生成的可执行文件拥有符号表，可以gdb调试
add_definitions("-Wall -g")
add_compile_definitions(_GNU_SOURCE)
add_compile_definitions(__USE_XOPEN2K)

add_compile_options("-Werror=declaration-after-statement")

# set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -m32")

# option(ENABLE_ASAN "Enable AddressSanitizer" OFF)					# 是否开启地址检测
# option(ENABLE_TSAN "Enable ThreadSanitizer" OFF)					# 是否开启线程检测
# option(ENABLE_COMPILE_WARNINGS "Enable all compiler warnings" ON) 	# 是否开启编译器警告
# option(ENABLE_DAS "Enable DAS" ON)									# 是否开启声明必须在语句之后
# option(ENABLE_O2 "Enable O2 optimization" OFF)		 				# 是否开启O2优化
option(ENABLE_CMAKE_EXPORT "Enable CMake export" ON) 				# 是否输出compile_commands.json
# option(ENABLE_WARNINGS_AS_ERRORS "Enable warnings as errors" OFF) 	# 是否开启警告视为错误

# if (ENABLE_ASAN)
# 	message(STATUS "Enable AddressSanitizer")
# 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=address")
# 	set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=address")
# endif()

# if (ENABLE_TSAN)
# 	message(STATUS "Enable ThreadSanitizer")
# 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fsanitize=thread")
# 	set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fsanitize=thread")
# endif()

# if (ENABLE_COMPILE_WARNINGS)
# 	message(STATUS "Enable compiler warnings")
# else() 
# 	message(STATUS "Disable compiler warnings")
# 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -w")
# endif()

# if (ENABLE_DAS)
# 	message(STATUS "Enable declaration after statement")
# 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Werror=declaration-after-statement")
# endif()

# if (ENABLE_O2)
# 	message(STATUS "Enable O2 optimization")
# 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -O2")
# endif()

if(ENABLE_CMAKE_EXPORT)
	message(STATUS "Enable CMake export")
	set(CMAKE_EXPORT_COMPILE_COMMANDS TRUE)
endif()

# if (ENABLE_WARNINGS_AS_ERRORS)
# 	message(STATUS "Enable warnings as errors")
# 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Werror")
# else()
# 	message(STATUS "Disable warnings as errors")
# 	set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -w")
# endif()
	

# 设置可执行文件输出目录
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)
set(CMAKE_BINARY_DIR ${CMAKE_SOURCE_DIR}/build)


include_directories(${CMAKE_SOURCE_DIR}/src/queryprocess/stmt)
include_directories(${CMAKE_SOURCE_DIR}/src/queryprocess/parser)
include_directories(${CMAKE_SOURCE_DIR}/src/queryprocess/expr)
include_directories(${CMAKE_SOURCE_DIR}/src/queryprocess/operator)
include_directories(${CMAKE_SOURCE_DIR}/src/optimizer)
include_directories(${CMAKE_SOURCE_DIR}/src/optimizer/RBO)
include_directories(${CMAKE_SOURCE_DIR}/src/optimizer/CBO)
include_directories(${CMAKE_SOURCE_DIR}/src/queryprocess/executor)
include_directories(${CMAKE_SOURCE_DIR}/src/queryprocess/event)
include_directories(${CMAKE_SOURCE_DIR}/src/queryprocess)


include_directories(${CMAKE_SOURCE_DIR}/malloc)
include_directories(${CMAKE_SOURCE_DIR}/src/cachemanager)
include_directories(${CMAKE_SOURCE_DIR}/src/common)
include_directories(${CMAKE_SOURCE_DIR}/src/index)
include_directories(${CMAKE_SOURCE_DIR}/src/system)
include_directories(${CMAKE_SOURCE_DIR}/src/storage)
include_directories(${CMAKE_SOURCE_DIR}/src/tranmanager)
include_directories(${CMAKE_SOURCE_DIR}/src/utils)
include_directories(${CMAKE_SOURCE_DIR}/test/concurrence)
include_directories(${CMAKE_SOURCE_DIR}/test/dbtest)
include_directories(${CMAKE_SOURCE_DIR}/test/demo)
if (WIN32)

else()

include_directories(${CMAKE_SOURCE_DIR}/test/multicon)
include_directories(${CMAKE_SOURCE_DIR}/test/dbperformancetest)
include_directories(${CMAKE_SOURCE_DIR}/test/tpc-c)
include_directories(${CMAKE_SOURCE_DIR}/test/tpc-h)
include_directories(${CMAKE_SOURCE_DIR}/test/performance)
include_directories(${CMAKE_SOURCE_DIR}/test/complexquery)
include_directories(${CMAKE_SOURCE_DIR}/test/testdependlib/xlsxwriter)

endif()

# src/utils/uthash
# include_directories(${CMAKE_SOURCE_DIR}/src/utils/uthash)


# 编译子文件夹的CMakeLists.txt
add_subdirectory(${CMAKE_SOURCE_DIR}/src)
add_subdirectory(${CMAKE_SOURCE_DIR}/test)




