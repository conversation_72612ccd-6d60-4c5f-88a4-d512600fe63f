$gccPath = "C:\msys64\mingw64\bin\gcc.exe"

# 创建必要的目录（如果不存在）
$dirs = @("bin", "build", "log", "testfile/blob/blobresult", "testfile/result")
foreach ($dir in $dirs) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Force -Path $dir | Out-Null
    }
}

# 进入 build 目录
Set-Location .\build

# 生成构建文件，确保生成 compile_commands.json
cmake -G "MinGW Makefiles" -DCMAKE_C_COMPILER="$gccPath" -DCMAKE_BUILD_TYPE=Debug -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..

# 如果 compile_commands.json 存在，则复制到上级目录
if (Test-Path "compile_commands.json") {
    Copy-Item -Path "compile_commands.json" -Destination "..\" -Force
}

# 构建项目
cmake --build . --parallel ([Environment]::ProcessorCount)

Set-Location ..