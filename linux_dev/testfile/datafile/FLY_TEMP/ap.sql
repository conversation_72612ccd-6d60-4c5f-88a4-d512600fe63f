-- 表：机场数据 (ARPT)
drop table if exists ARPT;
CREATE TABLE ARPT (
    sc8_arpt_ident CHAR(8) NOT NULL,          -- 机场标识
    f64_lon FLOAT,                           -- 机场参考点经度
    f64_lat FLOAT,                           -- 机场参考点纬度
    f64_elev FLOAT,                          -- 机场标高
    f64_LR_length FLOAT,            -- 最长跑道长度
    f64_mag_var FLOAT                       -- 磁差
);

-- 表：航路点数据 (WPT)
drop table if exists WPT;
CREATE TABLE WPT (
    sc8_wpt_ident CHAR(8) NOT NULL,          -- 航路点标识
    f64_lon FLOAT,                          -- 航路点经度
    f64_lat FLOAT                          -- 航路点纬度
);

-- 表：导航数据 (NAV)
drop table if exists NAV;
CREATE TABLE NAV (
    sc8_nav_ident CHAR(8) NOT NULL,          -- 导航台标识
    sc8_nav_type CHAR(2),                    -- 导航台类型
    f64_lon FLOAT,                          -- 导航台经度
    f64_lat FLOAT,                          -- 导航台纬度
    f64_vor FLOAT,                          -- VOR频率
    f64_dme FLOAT,                          -- DME频率
    f64_tacan FLOAT,                        -- TANCAN频率
    f64_ndb FLOAT                          -- NDB频率
);

-- 表：离场程序数据 (SID)
drop table if exists SID;
CREATE TABLE SID (
    sc8_arpt_ident CHAR(8) NOT NULL,         -- 机场标识
    sc8_sid_ident CHAR(8),                   -- 离场程序标识
    sc8_seq_no CHAR(2),                      -- 序号
    sc8_FS_type CHAR(2),             -- 航段类型
    f64_track FLOAT,                        -- 航迹角
    sc8_fix_ident CHAR(8),                   -- 定位点标识
    sc8_fix_cat CHAR(2),                     -- 定位点属性
    f64_lon FLOAT,                          -- 定位点经度
    f64_lat FLOAT,                          -- 定位点纬度
    f64_rnp FLOAT,                          -- 水平半宽
    f64_alt FLOAT                          -- 飞行高度
);

-- 表：进近程序数据 (APCH)
drop table if exists APCH;
CREATE TABLE APCH (
    sc8_arpt_ident CHAR(8) NOT NULL,         -- 机场标识
    sc8_apch_ident CHAR(8),                  -- 进近程序标识
    sc8_seq_no CHAR(2),                      -- 序号
    sc8_FS_type CHAR(2),             -- 航段类型
    f64_track FLOAT,                        -- 航迹角
    sc8_fix_ident CHAR(8),                   -- 定位点标识
    sc8_fix_cat CHAR(2),                     -- 定位点属性
    sc8_flight_stage CHAR(2),                -- 飞行阶段
    f64_lon FLOAT,                          -- 定位点经度
    f64_lat FLOAT,                          -- 定位点纬度
    f64_rnp FLOAT,                          -- 水平半宽
    f64_alt FLOAT                          -- 飞行高度
);

-- 表：自定义航路点数据 (DEFINE_WPT)
drop table if exists DEFINE_WPT;
CREATE TABLE DEFINE_WPT (
    sc8_wpt_ident CHAR(8) NOT NULL,          -- 航路点标识
    f64_lon FLOAT,                          -- 航路点经度
    f64_lat FLOAT                          -- 航路点纬度
);

-- 表：飞行计划数据 (FPLN)
drop table if exists FPLN;
CREATE TABLE FPLN (
    sc8_chs_eng CHAR(2),                     -- 中英文标识
    sc8_fpln_ident CHAR(16) NOT NULL,        -- 飞行计划标识
    sc8_seq_no CHAR(3),                      -- 序号
    sc8_FS_type CHAR(2),             -- 航段类型
    f64_track FLOAT,                        -- 航迹角
    sc8_fix_ident CHAR(8),                   -- 定位点标识
    sc8_fix_cat CHAR(2),                     -- 定位点属性
    sc8_fight_stage1 CHAR(2),                -- 飞行阶段1
    sc8_fight_stage2 CHAR(2),                -- 飞行阶段2
    f64_lon FLOAT,                          -- 定位点经度
    f64_lat FLOAT,                          -- 定位点纬度
    f64_rnp FLOAT,                          -- 水平半宽
    f64_alt FLOAT                          -- 飞行高度
);
