CREATE TABLE Drop_table_1(id int, t_name char);
SUCCESS

DROP TABLE Drop_table_1;
SUCCESS

CREATE TABLE Drop_table_2(id int, t_name char);
SUCCESS

INSERT INTO Drop_table_2 VALUES (1,'OB');
SUCCESS

DROP TABLE Drop_table_2;
SUCCESS

CREATE TABLE Drop_table_3(id int, t_name char);
SUCCESS

INSERT INTO Drop_table_3 VALUES (1,'OB');
SUCCESS

SELECT * FROM Drop_table_3;
id| t_name| rowId| createTime| updateTime
1| OB| 0| 1740125618| 1740125618
SUCCESS

DROP TABLE Drop_table_3;
SUCCESS

INSERT INTO Drop_table_3 VALUES (1,'OB');
FAILED

SELECT * FROM Drop_table_3;
FAILED

DELETE FROM Drop_table_3 WHERE id = 3;
FAILED

CREATE TABLE Drop_table_3(id int, t_name char);
SUCCESS

SELECT * FROM Drop_table_3;
SUCCESS

CREATE TABLE Drop_table_4(id int, t_name char);
SUCCESS

DROP TABLE Drop_table_4;
SUCCESS

DROP TABLE Drop_table_4;
FAILED

DROP TABLE Drop_table_4_1;
FAILED

CREATE TABLE Drop_table_5(id int, t_name char);
SUCCESS

DROP TABLE Drop_table_5;
SUCCESS

CREATE TABLE Drop_table_5(id int, t_name char);
SUCCESS

SELECT * FROM Drop_table_5;
SUCCESS

CREATE TABLE Drop_table_6(id int, t_name char);
SUCCESS

CREATE INDEX index_id on Drop_table_6(id);
SUCCESS

INSERT INTO Drop_table_6 VALUES (1,'OB');
SUCCESS

SELECT * FROM Drop_table_6;
id| t_name| rowId| createTime| updateTime
1| OB| 0| 1740125618| 1740125618
SUCCESS

DROP TABLE Drop_table_6;
SUCCESS

SELECT * FROM Drop_table_6;
FAILED

