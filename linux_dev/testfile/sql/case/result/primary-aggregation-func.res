CREATE TABLE aggregation_func(id int, num int, price float, addr char, birthday date);
SUCCESS

INSERT INTO aggregation_func VALUES (1, 18, 10.0, 'abc', '2020-01-01');
SUCCESS

INSERT INTO aggregation_func VALUES (2, 15, 20.0, 'abc', '2010-01-11');
SUCCESS

INSERT INTO aggregation_func VALUES (3, 12, 30.0, 'def', '2021-01-21');
SUCCESS

INSERT INTO aggregation_func VALUES (4, 15, 30.0, 'dei', '2021-01-31');
SUCCESS

SELECT count(*) FROM aggregation_func;
count(*)
4
SUCCESS

SELECT count(num) FROM aggregation_func;
count(num)
4
SUCCESS

SELECT min(num) FROM aggregation_func;
min(num)
12
SUCCESS

SELECT min(price) FROM aggregation_func;
min(price)
10.000000
SUCCESS

SELECT min(addr) FROM aggregation_func;
min(addr)
abc
SUCCESS

SELECT max(num) FROM aggregation_func;
max(num)
18
SUCCESS

SELECT max(price) FROM aggregation_func;
max(price)
30.000000
SUCCESS

SELECT max(addr) FROM aggregation_func;
max(addr)
dei
SUCCESS

SELECT avg(num) FROM aggregation_func;
avg(num)
15.000000
SUCCESS

SELECT avg(price) FROM aggregation_func;
avg(price)
22.500000
SUCCESS

SELECT min(*) FROM aggregation_func;
FAILED

SELECT max(*) FROM aggregation_func;
FAILED

SELECT avg(*) FROM aggregation_func;
FAILED

SELECT count(*,num) FROM aggregation_func;
FAILED

SELECT min(num,price) FROM aggregation_func;
FAILED

SELECT max(num,price) FROM aggregation_func;
FAILED

SELECT avg(num,price) FROM aggregation_func;
FAILED

SELECT count() FROM aggregation_func;
FAILED

SELECT min() FROM aggregation_func;
FAILED

SELECT max() FROM aggregation_func;
FAILED

SELECT avg() FROM aggregation_func;
FAILED

SELECT count(id2) FROM aggregation_func;
FAILED

SELECT min(id2) FROM aggregation_func;
FAILED

SELECT max(id2) FROM aggregation_func;
FAILED

SELECT avg(id2) FROM aggregation_func;
FAILED

SELECT min(num),max(num),avg(num) FROM aggregation_func;
min(num)| max(num)| avg(num)
12| 18| 15.000000
SUCCESS

