create table t_basic(id int, age int, name char, score float);
SUCCESS

insert into t_basic values(1,1, 'a', 1.0);
SUCCESS

insert into t_basic values(2,2, 'b', 2.0);
SUCCESS

insert into t_basic values(4,4, 'c', 3.0);
SUCCESS

insert into t_basic values(3,3, 'd', 4.0);
SUCCESS

insert into t_basic values(5,5, 'e', 5.5);
SUCCESS

insert into t_basic values(6,6, 'f', 6.6);
SUCCESS

insert into t_basic values(7,7, 'g', 7.7);
SUCCESS

delete from t_basic where id=3;
SUCCESS

select * from t_basic;
id| age| name| score| rowId| createTime| updateTime
1| 1| a| 1.000000| 0| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
2| 2| b| 2.000000| 1| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
4| 4| c| 3.000000| 2| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
5| 5| e| 5.500000| 4| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
6| 6| f| 6.600000| 5| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
7| 7| g| 7.700000| 6| 1740125617| 1740125617
SUCCESS

select * from t_basic where id=1;
id| age| name| score| rowId| createTime| updateTime
1| 1| a| 1.000000| 0| 1740125617| 1740125617
SUCCESS

select * from t_basic where id>=5;
id| age| name| score| rowId| createTime| updateTime
5| 5| e| 5.500000| 4| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
6| 6| f| 6.600000| 5| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
7| 7| g| 7.700000| 6| 1740125617| 1740125617
SUCCESS

select * from t_basic where age>1 and age<3;
id| age| name| score| rowId| createTime| updateTime
2| 2| b| 2.000000| 1| 1740125617| 1740125617
SUCCESS

select * from t_basic where t_basic.id=1 and t_basic.age=1;
id| age| name| score| rowId| createTime| updateTime
1| 1| a| 1.000000| 0| 1740125617| 1740125617
SUCCESS

select * from t_basic where id=1 and age=1;
id| age| name| score| rowId| createTime| updateTime
1| 1| a| 1.000000| 0| 1740125617| 1740125617
SUCCESS

select id, age, name, score from t_basic;
id| age| name| score
1| 1| a| 1.000000
id| age| name| score
2| 2| b| 2.000000
id| age| name| score
4| 4| c| 3.000000
id| age| name| score
5| 5| e| 5.500000
id| age| name| score
6| 6| f| 6.600000
id| age| name| score
7| 7| g| 7.700000
SUCCESS

select t_basic.id, t_basic.age, t_basic.name, t_basic.score from t_basic;
id| age| name| score
1| 1| a| 1.000000
id| age| name| score
2| 2| b| 2.000000
id| age| name| score
4| 4| c| 3.000000
id| age| name| score
5| 5| e| 5.500000
id| age| name| score
6| 6| f| 6.600000
id| age| name| score
7| 7| g| 7.700000
SUCCESS

select t_basic.id, t_basic.age, name from t_basic;
id| age| name
1| 1| a
id| age| name
2| 2| b
id| age| name
4| 4| c
id| age| name
5| 5| e
id| age| name
6| 6| f
id| age| name
7| 7| g
SUCCESS

create index i_id on t_basic (id);
SUCCESS

select * from t_basic;
id| age| name| score| rowId| createTime| updateTime
1| 1| a| 1.000000| 0| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
2| 2| b| 2.000000| 1| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
4| 4| c| 3.000000| 2| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
5| 5| e| 5.500000| 4| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
6| 6| f| 6.600000| 5| 1740125617| 1740125617
id| age| name| score| rowId| createTime| updateTime
7| 7| g| 7.700000| 6| 1740125617| 1740125617
SUCCESS

calc 1, 2, 3;
FAILED

calc 1 + 2 - 3+4;
FAILED

calc 2* 4;
FAILED

calc 3/2;
FAILED

calc 2* (1+3);
FAILED

calc (1+3) *2;
FAILED

calc 2 *-(10+2);
FAILED

calc (1+2) * (2 * (20+-(5*1)));
FAILED

calc 20/0;
FAILED

calc 13.2/0.0;
FAILED

calc "123" + 4;
FAILED

calc "abc" + 4;
FAILED

