create table exp_table(id int, col1 int, col2 int, col3 float, col4 float);
SUCCESS

insert into exp_table VALUES (1, 1, 1, 1.0, 1.5);
SUCCESS

insert into exp_table VALUES (2, 2, -2, 5.5, 1.0);
SUCCESS

insert into exp_table VALUES (3, 3, 4, 5.0, 4.0);
SUCCESS

select * from exp_table where 1 = 5/4;
SUCCESS

select * from exp_table where col1-2 > 0;
FAILED

select * from exp_table where 2+col2 < 1;
id| col1| col2| col3| col4| rowId| createTime| updateTime
2| 2| -2| 5.500000| 1.000000| 1| 1740125618| 1740125618
SUCCESS

select * from exp_table where col1*col2 < 0;
id| col1| col2| col3| col4| rowId| createTime| updateTime
2| 2| -2| 5.500000| 1.000000| 1| 1740125618| 1740125618
SUCCESS

select * from exp_table where 5/4 = 1;
SUCCESS

select * from exp_table where 0 < col1-2;
FAILED

select * from exp_table where 1.0 > 2+col2;
id| col1| col2| col3| col4| rowId| createTime| updateTime
2| 2| -2| 5.500000| 1.000000| 1| 1740125618| 1740125618
SUCCESS

select * from exp_table where -0 < col1-col2;
id| col1| col2| col3| col4| rowId| createTime| updateTime
2| 2| -2| 5.500000| 1.000000| 1| 1740125618| 1740125618
SUCCESS

select * from exp_table where 0 < -2+col1;
id| col1| col2| col3| col4| rowId| createTime| updateTime
3| 3| 4| 5.000000| 4.000000| 2| 1740125618| 1740125618
SUCCESS

select * from exp_table where 1+1 = 2*1.0;
id| col1| col2| col3| col4| rowId| createTime| updateTime
1| 1| 1| 1.000000| 1.500000| 0| 1740125618| 1740125618
id| col1| col2| col3| col4| rowId| createTime| updateTime
2| 2| -2| 5.500000| 1.000000| 1| 1740125618| 1740125618
id| col1| col2| col3| col4| rowId| createTime| updateTime
3| 3| 4| 5.000000| 4.000000| 2| 1740125618| 1740125618
SUCCESS

select * from exp_table where 5/4*8 < 4+col2*col3/2;
id| col1| col2| col3| col4| rowId| createTime| updateTime
3| 3| 4| 5.000000| 4.000000| 2| 1740125618| 1740125618
SUCCESS

select * from exp_table where 5/4*8 < (4+col2)*col3/2;
id| col1| col2| col3| col4| rowId| createTime| updateTime
3| 3| 4| 5.000000| 4.000000| 2| 1740125618| 1740125618
SUCCESS

select id,-(col2*(-1)+1)+(col4+2)*(col1+col3*2),(4+col2)*col3/2 from exp_table where -(col2*(-1)+1)+(col4+2)*(col1+col3*2) > (4+col2)*col3/2;
id| -(col2*(-1)+1)+(col4+2)*(col1+col3*2)| (4+col2)*col3/2
1| 10.500000| 2.500000
id| -(col2*(-1)+1)+(col4+2)*(col1+col3*2)| (4+col2)*col3/2
2| 36.000000| 5.500000
id| -(col2*(-1)+1)+(col4+2)*(col1+col3*2)| (4+col2)*col3/2
3| 81.000000| 20.000000
SUCCESS

select id,col1,col2,col3,col4,6-(col2*(1+col1))+(col4+2)/(1+col1*4+col3*2) from exp_table where 6-(col2*(1+col1))+(col4+2)/(1+col1*4+col3*2) > 5;
id| col1| col2| col3| col4| 6-(col2*(1+col1))+(col4+2)/(1+col1*4+col3*2)
2| 2| -2| 5.500000| 1.000000| 12.150000
SUCCESS

select id,col1,col2,col3,col4,3*col1/(col2+2) from exp_table where 3*col1/(col2+2) > 1;
id| col1| col2| col3| col4| 3*col1/(col2+2)
2| 2| -2| 5.500000| 1.000000| inf
id| col1| col2| col3| col4| 3*col1/(col2+2)
3| 3| 4| 5.000000| 4.000000| 1.500000
SUCCESS

select id,3*col1/(col2+2) from exp_table where 3*col1/(col2+2)+1/0 > 1;
id| 3*col1/(col2+2)
1| 1.000000
id| 3*col1/(col2+2)
2| inf
id| 3*col1/(col2+2)
3| 1.500000
SUCCESS

select * from exp_table where 1/0 = 1/0;
id| col1| col2| col3| col4| rowId| createTime| updateTime
1| 1| 1| 1.000000| 1.500000| 0| 1740125618| 1740125618
id| col1| col2| col3| col4| rowId| createTime| updateTime
2| 2| -2| 5.500000| 1.000000| 1| 1740125618| 1740125618
id| col1| col2| col3| col4| rowId| createTime| updateTime
3| 3| 4| 5.000000| 4.000000| 2| 1740125618| 1740125618
SUCCESS

create table exp_table2(id int, col1 int);
SUCCESS

insert into exp_table2 VALUES (1, 1);
SUCCESS

insert into exp_table2 VALUES (2, 3);
SUCCESS

select exp_table.id,3*exp_table2.col1/(exp_table.col2+2) from exp_table,exp_table2 where 3*exp_table2.col1/(exp_table.col2+2)>1;
exp_table.id| 3*exp_table2.col1/(exp_table.col2+2)
1| 3.000000
exp_table.id| 3*exp_table2.col1/(exp_table.col2+2)
2| inf
exp_table.id| 3*exp_table2.col1/(exp_table.col2+2)
2| inf
exp_table.id| 3*exp_table2.col1/(exp_table.col2+2)
3| 1.500000
SUCCESS

