CREATE TABLE insert_table(id int, t_name char, col1 int, col2 int);
SUCCESS

INSERT INTO insert_table VALUES (1,'N1',1,1);
SUCCESS

INSERT INTO insert_table VALUES (2,'N2',1,1),(3,'N3',2,1);
SUCCE<PERSON>

INSERT INTO insert_table VALUES (4,'N4',1,1),(1,1,1);
FAILED

INSERT INTO insert_table VALUES (4,'N4',1,1),(1,1,1,1);
FAILED

SELECT * FROM insert_table;
id| t_name| col1| col2| rowId| createTime| updateTime
1| N1| 1| 1| 0| 1740125618| 1740125618
id| t_name| col1| col2| rowId| createTime| updateTime
2| N2| 1| 1| 1| 1740125618| 1740125618
id| t_name| col1| col2| rowId| createTime| updateTime
3| N3| 2| 1| 2| 1740125618| 1740125618
SUCCESS

