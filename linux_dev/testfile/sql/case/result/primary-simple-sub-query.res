CREATE TABLE ssq_1(id int, col1 int, feat1 float);
SUCCESS

CREATE TABLE ssq_2(id int, col2 int, feat2 float);
SUCCESS

CREATE TABLE ssq_3(id int, col3 int, feat3 float);
SUCCE<PERSON>

INSERT INTO ssq_1 VALUES (1, 4, 11.2);
SUCCESS

INSERT INTO ssq_1 VALUES (2, 2, 12.0);
SUCCESS

INSERT INTO ssq_1 VALUES (3, 3, 13.5);
SUCCESS

INSERT INTO ssq_2 VALUES (1, 2, 13.0);
SUCCESS

INSERT INTO ssq_2 VALUES (2, 7, 10.5);
SUCCESS

INSERT INTO ssq_2 VALUES (5, 3, 12.6);
SUCCESS

select * from ssq_1 where id in (select ssq_2.id from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where col1 not in (select ssq_2.col2 from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where col1 = (select avg(ssq_2.col2) from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where (select avg(ssq_2.col2) from ssq_2) = col1;
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where feat1 >= (select min(ssq_2.feat2) from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
3| 3| 13.500000| 2| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where (select min(ssq_2.feat2) from ssq_2) <= feat1;
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
3| 3| 13.500000| 2| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where feat1 <= (select max(ssq_2.feat2) from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where (select max(ssq_2.feat2) from ssq_2) >= feat1;
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where feat1 > (select min(ssq_2.feat2) from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
3| 3| 13.500000| 2| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where (select min(ssq_2.feat2) from ssq_2) < feat1;
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
3| 3| 13.500000| 2| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where feat1 < (select max(ssq_2.feat2) from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where (select max(ssq_2.feat2) from ssq_2) > feat1;
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where feat1 <> (select avg(ssq_2.feat2) from ssq_2);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
3| 3| 13.500000| 2| 1740125621| 1740125621
SUCCESS

select * from ssq_1 where feat1 < (select max(ssq_2.feat2) from ssq_2 where 1=0);
SUCCESS

select * from ssq_1 where id in (select ssq_2.id from ssq_2 where 1=0);
SUCCESS

select * from ssq_1 where id not in (select ssq_2.id from ssq_2 where 1=0);
id| col1| feat1| rowId| createTime| updateTime
1| 4| 11.200000| 0| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
2| 2| 12.000000| 1| 1740125621| 1740125621
id| col1| feat1| rowId| createTime| updateTime
3| 3| 13.500000| 2| 1740125621| 1740125621
SUCCESS

select * from ssq_3 where feat3 < (select max(ssq_2.feat2) from ssq_2);
SUCCESS

select * from ssq_3 where id in (select ssq_2.id from ssq_2);
SUCCESS

select * from ssq_3 where id not in (select ssq_2.id from ssq_2);
SUCCESS

select * from ssq_1 where col1 = (select ssq_2.col2 from ssq_2);
FAILED

select * from ssq_1 where col1 = (select * from ssq_2);
FAILED

select * from ssq_1 where col1 in (select * from ssq_2);
FAILED

select * from ssq_1 where col1 not in (select * from ssq_2);
FAILED

