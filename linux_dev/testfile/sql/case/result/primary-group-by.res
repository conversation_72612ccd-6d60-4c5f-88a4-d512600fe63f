create table t_group_by (id int, score float, name char);
SUCCESS

create table t_group_by_2 (id int, age int);
SUCCESS

insert into t_group_by values(3, 1.0, 'a');
SUCCESS

insert into t_group_by values(1, 2.0, 'b');
SUCCESS

insert into t_group_by values(4, 3.0, 'c');
SUCCESS

insert into t_group_by values(3, 2.0, 'c');
SUCCESS

insert into t_group_by values(3, 4.0, 'c');
SUCCESS

insert into t_group_by values(3, 3.0, 'd');
SUCCESS

insert into t_group_by values(3, 2.0, 'f');
SUCCESS

insert into t_group_by_2 values(1, 10);
SUCCESS

insert into t_group_by_2 values(2, 20);
SUCCESS

insert into t_group_by_2 values(3, 10);
SUCCESS

insert into t_group_by_2 values(3, 20);
SUCCESS

insert into t_group_by_2 values(3, 40);
SUCCESS

insert into t_group_by_2 values(4, 20);
SUCCESS

select id, avg(score) from t_group_by group by id;
id| avg(score)
1| 2.000000
id| avg(score)
3| 2.400000
id| avg(score)
4| 3.000000
SUCCESS

select name, min(id), max(score) from t_group_by group by name;
name| min(id)| max(score)
a| 3| 1.000000
name| min(id)| max(score)
b| 1| 2.000000
name| min(id)| max(score)
c| 3| 4.000000
name| min(id)| max(score)
d| 3| 3.000000
name| min(id)| max(score)
f| 3| 2.000000
SUCCESS

select id, name, avg(score) from t_group_by group by id, name;
id| name| avg(score)
1| b| 2.000000
id| name| avg(score)
3| a| 1.000000
id| name| avg(score)
3| c| 3.000000
id| name| avg(score)
3| d| 3.000000
id| name| avg(score)
3| f| 2.000000
id| name| avg(score)
4| c| 3.000000
SUCCESS

select id, avg(score) from t_group_by where id>2 group by id;
id| avg(score)
3| 2.400000
id| avg(score)
4| 3.000000
SUCCESS

select name, count(id), max(score) from t_group_by where name > 'a' and id>=0 group by name;
name| count(id)| max(score)
b| 1| 2.000000
name| count(id)| max(score)
c| 3| 4.000000
name| count(id)| max(score)
d| 1| 3.000000
name| count(id)| max(score)
f| 1| 2.000000
SUCCESS

select t_group_by.id, t_group_by.name, avg(t_group_by.score), avg(t_group_by_2.age) from t_group_by, t_group_by_2 where t_group_by.id=t_group_by_2.id group by t_group_by.id, t_group_by.name;
t_group_by.id| t_group_by.name| avg(t_group_by.score)| avg(t_group_by_2.age)
1| b| 2.000000| 10.000000
t_group_by.id| t_group_by.name| avg(t_group_by.score)| avg(t_group_by_2.age)
3| a| 1.000000| 23.333334
t_group_by.id| t_group_by.name| avg(t_group_by.score)| avg(t_group_by_2.age)
3| c| 3.000000| 23.333334
t_group_by.id| t_group_by.name| avg(t_group_by.score)| avg(t_group_by_2.age)
3| d| 3.000000| 23.333334
t_group_by.id| t_group_by.name| avg(t_group_by.score)| avg(t_group_by_2.age)
3| f| 2.000000| 23.333334
t_group_by.id| t_group_by.name| avg(t_group_by.score)| avg(t_group_by_2.age)
4| c| 3.000000| 20.000000
SUCCESS

