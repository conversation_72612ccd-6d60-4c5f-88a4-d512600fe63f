insert into t_basic values(51, 51, 'Y', 51.6);
insert into t_basic values(52, 52, 'Z', 52.7);
insert into t_basic values(53, 53, 'a', 53.8);
insert into t_basic values(54, 54, 'b', 54.9);
insert into t_basic values(55, 55, 'c', 55.1);
insert into t_basic values(56, 56, 'd', 56.2);
insert into t_basic values(57, 57, 'e', 57.3);
insert into t_basic values(58, 58, 'f', 58.4);
insert into t_basic values(59, 59, 'g', 59.5);
insert into t_basic values(60, 60, 'h', 60.6);
insert into t_basic values(61, 61, 'i', 61.7);
insert into t_basic values(62, 62, 'j', 62.8);
insert into t_basic values(63, 63, 'k', 63.9);
insert into t_basic values(64, 64, 'l', 64.1);
insert into t_basic values(65, 65, 'm', 65.2);
insert into t_basic values(66, 66, 'n', 66.3);
insert into t_basic values(67, 67, 'o', 67.4);
insert into t_basic values(68, 68, 'p', 68.5);
insert into t_basic values(69, 69, 'q', 69.6);
insert into t_basic values(70, 70, 'r', 70.7);
insert into t_basic values(71, 71, 's', 71.8);
insert into t_basic values(72, 72, 't', 72.9);
insert into t_basic values(73, 73, 'u', 73.1);
insert into t_basic values(74, 74, 'v', 74.2);
insert into t_basic values(75, 75, 'w', 75.3);
insert into t_basic values(76, 76, 'x', 76.4);
insert into t_basic values(77, 77, 'y', 77.5);
insert into t_basic values(78, 78, 'z', 78.6);
insert into t_basic values(79, 79, 'A', 79.7);
insert into t_basic values(80, 80, 'B', 80.8);
insert into t_basic values(81, 81, 'C', 81.9);
insert into t_basic values(82, 82, 'D', 82.1);
insert into t_basic values(83, 83, 'E', 83.2);
insert into t_basic values(84, 84, 'F', 84.3);
insert into t_basic values(85, 85, 'G', 85.4);
insert into t_basic values(86, 86, 'H', 86.5);
insert into t_basic values(87, 87, 'I', 87.6);
insert into t_basic values(88, 88, 'J', 88.7);
insert into t_basic values(89, 89, 'K', 89.8);
insert into t_basic values(90, 90, 'L', 89.9);
insert into t_basic values(91, 91, 'M', 91.1);
insert into t_basic values(92, 92, 'N', 92.2);
insert into t_basic values(93, 93, 'O', 93.3);
insert into t_basic values(94, 94, 'P', 94.4);
insert into t_basic values(95, 95, 'Q', 95.5);
insert into t_basic values(96, 96, 'R', 96.6);
insert into t_basic values(97, 97, 'S', 97.7);
insert into t_basic values(98, 98, 'T', 98.8);
insert into t_basic values(99, 99, 'U', 99.9);
insert into t_basic values(100, 100, 'V', 100.1);
insert into t_basic values(101, 101, 'W', 101.1);
insert into t_basic values(102, 102, 'X', 102.2);
insert into t_basic values(103, 103, 'Y', 103.3);
insert into t_basic values(104, 104, 'Z', 104.4);
insert into t_basic values(105, 105, 'a', 105.5);
insert into t_basic values(106, 106, 'b', 106.6);
insert into t_basic values(107, 107, 'c', 107.7);
insert into t_basic values(108, 108, 'd', 108.8);
insert into t_basic values(109, 109, 'e', 109.9);
insert into t_basic values(110, 110, 'f', 110.1);
insert into t_basic values(111, 111, 'g', 111.2);
insert into t_basic values(112, 112, 'h', 112.3);
insert into t_basic values(113, 113, 'i', 113.4);
insert into t_basic values(114, 114, 'j', 114.5);
insert into t_basic values(115, 115, 'k', 115.6);
insert into t_basic values(116, 116, 'l', 116.7);
insert into t_basic values(117, 117, 'm', 117.8);
insert into t_basic values(118, 118, 'n', 118.9);
insert into t_basic values(119, 119, 'o', 119.1);
insert into t_basic values(120, 120, 'p', 120.2);
insert into t_basic values(121, 121, 'q', 121.3);
insert into t_basic values(122, 122, 'r', 122.4);
insert into t_basic values(123, 123, 's', 123.5);
insert into t_basic values(124, 124, 't', 124.6);
insert into t_basic values(125, 125, 'u', 125.7);
insert into t_basic values(126, 126, 'v', 126.8);
insert into t_basic values(127, 127, 'w', 127.9);
insert into t_basic values(128, 128, 'x', 128.1);
insert into t_basic values(129, 129, 'y', 129.2);
insert into t_basic values(130, 130, 'z', 130.3);
insert into t_basic values(131, 131, 'A', 131.4);
insert into t_basic values(132, 132, 'B', 132.5);
insert into t_basic values(133, 133, 'C', 133.6);
insert into t_basic values(134, 134, 'D', 134.7);
insert into t_basic values(135, 135, 'E', 135.8);
insert into t_basic values(136, 136, 'F', 136.9);
insert into t_basic values(137, 137, 'G', 137.1);
insert into t_basic values(138, 138, 'H', 138.2);
insert into t_basic values(139, 139, 'I', 139.3);
insert into t_basic values(140, 140, 'J', 140.4);
insert into t_basic values(141, 141, 'K', 141.5);
insert into t_basic values(142, 142, 'L', 142.6);
insert into t_basic values(143, 143, 'M', 143.7);
insert into t_basic values(144, 144, 'N', 144.8);
insert into t_basic values(145, 145, 'O', 145.9);
insert into t_basic values(146, 146, 'P', 146.1);
insert into t_basic values(147, 147, 'Q', 147.2);
insert into t_basic values(148, 148, 'R', 148.3);
insert into t_basic values(149, 149, 'S', 149.4);
insert into t_basic values(150, 150, 'T', 150.5);
insert into t_basic values(151, 151, 'U', 151.6);
insert into t_basic values(152, 152, 'V', 152.7);
insert into t_basic values(153, 153, 'W', 153.8);
insert into t_basic values(154, 154, 'X', 154.9);
insert into t_basic values(155, 155, 'Y', 155.1);
insert into t_basic values(156, 156, 'Z', 156.2);
insert into t_basic values(157, 157, 'a', 157.3);
insert into t_basic values(158, 158, 'b', 158.4);
insert into t_basic values(159, 159, 'c', 159.5);
insert into t_basic values(160, 160, 'd', 160.6);
insert into t_basic values(161, 161, 'e', 161.7);
insert into t_basic values(162, 162, 'f', 162.8);
insert into t_basic values(163, 163, 'g', 163.9);
insert into t_basic values(164, 164, 'h', 164.1);
insert into t_basic values(165, 165, 'i', 165.2);
insert into t_basic values(166, 166, 'j', 166.3);
insert into t_basic values(167, 167, 'k', 167.4);
insert into t_basic values(168, 168, 'l', 168.5);
insert into t_basic values(169, 169, 'm', 169.6);
insert into t_basic values(170, 170, 'n', 170.7);
insert into t_basic values(171, 171, 'o', 171.8);
insert into t_basic values(172, 172, 'p', 172.9);
insert into t_basic values(173, 173, 'q', 173.1);
insert into t_basic values(174, 174, 'r', 174.2);
insert into t_basic values(175, 175, 's', 175.3);
insert into t_basic values(176, 176, 't', 176.4);
insert into t_basic values(177, 177, 'u', 177.5);
insert into t_basic values(178, 178, 'v', 178.6);
insert into t_basic values(179, 179, 'w', 179.7);
insert into t_basic values(180, 180, 'x', 180.8);
insert into t_basic values(181, 181, 'y', 181.9);
insert into t_basic values(182, 182, 'z', 182.1);
insert into t_basic values(183, 183, 'A', 183.2);
insert into t_basic values(184, 184, 'B', 184.3);
insert into t_basic values(185, 185, 'C', 185.4);
insert into t_basic values(186, 186, 'D', 186.5);
insert into t_basic values(187, 187, 'E', 187.6);
insert into t_basic values(188, 188, 'F', 188.7);
insert into t_basic values(189, 189, 'G', 189.8);
insert into t_basic values(190, 190, 'H', 190.9);
insert into t_basic values(191, 191, 'I', 191.1);
insert into t_basic values(192, 192, 'J', 192.2);
insert into t_basic values(193, 193, 'K', 193.3);
insert into t_basic values(194, 194, 'L', 194.4);
insert into t_basic values(195, 195, 'M', 195.5);
insert into t_basic values(196, 196, 'N', 196.6);
insert into t_basic values(197, 197, 'O', 197.7);
insert into t_basic values(198, 198, 'P', 198.8);
insert into t_basic values(199, 199, 'Q', 199.9);
insert into t_basic values(200, 200, 'R', 200.1);
insert into t_basic values(201, 201, 'S', 201.2);
insert into t_basic values(202, 202, 'T', 202.3);
insert into t_basic values(203, 203, 'U', 203.4);
insert into t_basic values(204, 204, 'V', 204.5);
insert into t_basic values(205, 205, 'W', 205.6);
insert into t_basic values(206, 206, 'X', 206.7);
insert into t_basic values(207, 207, 'Y', 207.8);
insert into t_basic values(208, 208, 'Z', 208.9);
insert into t_basic values(209, 209, 'a', 209.1);
insert into t_basic values(210, 210, 'b', 210.2);
insert into t_basic values(211, 211, 'c', 211.3);
insert into t_basic values(212, 212, 'd', 212.4);
insert into t_basic values(213, 213, 'e', 213.5);
insert into t_basic values(214, 214, 'f', 214.6);
insert into t_basic values(215, 215, 'g', 215.7);
insert into t_basic values(216, 216, 'h', 216.8);
insert into t_basic values(217, 217, 'i', 217.9);
insert into t_basic values(218, 218, 'j', 218.1);
insert into t_basic values(219, 219, 'k', 219.2);
insert into t_basic values(220, 220, 'l', 220.3);
insert into t_basic values(221, 221, 'm', 221.4);
insert into t_basic values(222, 222, 'n', 222.5);
insert into t_basic values(223, 223, 'o', 223.6);
insert into t_basic values(224, 224, 'p', 224.7);
insert into t_basic values(225, 225, 'q', 225.8);
insert into t_basic values(226, 226, 'r', 226.9);
insert into t_basic values(227, 227, 's', 227.1);
insert into t_basic values(228, 228, 't', 228.2);
insert into t_basic values(229, 229, 'u', 229.3);
insert into t_basic values(230, 230, 'v', 230.4);
insert into t_basic values(231, 231, 'w', 231.5);
insert into t_basic values(232, 232, 'x', 232.6);
insert into t_basic values(233, 233, 'y', 233.7);
insert into t_basic values(234, 234, 'z', 234.8);
insert into t_basic values(235, 235, 'A', 235.9);
insert into t_basic values(236, 236, 'B', 236.1);
insert into t_basic values(237, 237, 'C', 237.2);
insert into t_basic values(238, 238, 'D', 238.3);
insert into t_basic values(239, 239, 'E', 239.4);
insert into t_basic values(240, 240, 'F', 240.5);
insert into t_basic values(241, 241, 'G', 241.6);
insert into t_basic values(242, 242, 'H', 242.7);
insert into t_basic values(243, 243, 'I', 243.8);
insert into t_basic values(244, 244, 'J', 244.9);
insert into t_basic values(245, 245, 'K', 245.1);
insert into t_basic values(246, 246, 'L', 246.2);
insert into t_basic values(247, 247, 'M', 247.3);
insert into t_basic values(248, 248, 'N', 248.4);
insert into t_basic values(249, 249, 'O', 249.5);
insert into t_basic values(250, 250, 'P', 250.6);
insert into t_basic values(251, 251, 'Q', 251.7);
insert into t_basic values(252, 252, 'R', 252.8);
insert into t_basic values(253, 253, 'S', 253.9);
insert into t_basic values(254, 254, 'T', 254.1);
insert into t_basic values(255, 255, 'U', 255.2);
insert into t_basic values(256, 256, 'V', 256.3);
insert into t_basic values(257, 257, 'W', 257.4);
insert into t_basic values(258, 258, 'X', 258.5);
insert into t_basic values(259, 259, 'Y', 259.6);
insert into t_basic values(260, 260, 'Z', 260.7);
insert into t_basic values(261, 261, 'a', 261.8);
insert into t_basic values(262, 262, 'b', 262.9);
insert into t_basic values(263, 263, 'c', 263.1);
insert into t_basic values(264, 264, 'd', 264.2);
insert into t_basic values(265, 265, 'e', 265.3);
insert into t_basic values(266, 266, 'f', 266.4);
insert into t_basic values(267, 267, 'g', 267.5);
insert into t_basic values(268, 268, 'h', 268.6);
insert into t_basic values(269, 269, 'i', 269.7);
insert into t_basic values(270, 270, 'j', 270.8);
insert into t_basic values(271, 271, 'k', 271.9);
insert into t_basic values(272, 272, 'l', 272.1);
insert into t_basic values(273, 273, 'm', 273.2);
insert into t_basic values(274, 274, 'n', 274.3);
insert into t_basic values(275, 275, 'o', 275.4);
insert into t_basic values(276, 276, 'p', 276.5);
insert into t_basic values(277, 277, 'q', 277.6);
insert into t_basic values(278, 278, 'r', 278.7);
