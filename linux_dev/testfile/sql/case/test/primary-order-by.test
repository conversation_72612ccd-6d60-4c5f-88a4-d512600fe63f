-- echo 1. create table
create table t_order_by(id int, score float, name1 char(1), name2 char(1));
create table t_order_by_2(id int, age int);

-- echo 2. insert records
insert into t_order_by values(3, 1.0, "a", "b");
insert into t_order_by values(1, 2.0, "c", "d");
insert into t_order_by values(2, 3.0, "e", "f");
insert into t_order_by values(4, 4.0, "g", "h");
insert into t_order_by values(5, 5.0, "i", "j");
insert into t_order_by values(6, 6.0, "k", "l");
insert into t_order_by values(7, 7.0, "m", "n");
insert into t_order_by values(8, 8.0, "o", "p");
insert into t_order_by values(9, 9.0, "q", "r");

insert into t_order_by values(3, 1.0, "a", "a");

insert into t_order_by values(3, 1.0, "a", "c");
insert into t_order_by values(1, 2.0, "d", "e");
insert into t_order_by values(2, 3.0, "f", "g");
insert into t_order_by values(4, 4.0, "h", "i");
insert into t_order_by values(5, 5.0, "j", "k");
insert into t_order_by values(6, 6.0, "l", "m");
insert into t_order_by values(7, 7.0, "n", "o");
insert into t_order_by values(8, 8.0, "p", "q");




-- echo 3. primary order by

 select name1, name2 from t_order_by 
 group by name1, name2
 order by name1 asc, name2 asc;

