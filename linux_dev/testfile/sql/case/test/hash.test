-- echo basic insert

create table t_basic(id int, age int, name char, score float);
insert into t_basic values(1, 1, 'a', 1.0);
insert into t_basic values(2, 2, 'b', 2.0);
insert into t_basic values(3, 3, 'c', 3.0);
insert into t_basic values(4, 4, 'd', 4.0);
insert into t_basic values(5, 5, 'e', 5.5);
insert into t_basic values(6, 6, 'f', 6.6);
insert into t_basic values(7, 7, 'g', 7.7);
insert into t_basic values(8, 8, 'h', 8.8);
insert into t_basic values(9, 9, 'i', 9.9);
insert into t_basic values(10, 10, 'j', 10.1);
insert into t_basic values(11, 11, 'k', 11.2);
insert into t_basic values(12, 12, 'l', 12.3);
insert into t_basic values(13, 13, 'm', 13.4);
insert into t_basic values(14, 14, 'n', 14.5);
insert into t_basic values(15, 15, 'o', 15.6);
insert into t_basic values(16, 16, 'p', 16.7);
insert into t_basic values(17, 17, 'q', 17.8);
insert into t_basic values(18, 18, 'r', 18.9);
insert into t_basic values(19, 19, 's', 19.1);
insert into t_basic values(20, 20, 't', 20.2);
insert into t_basic values(21, 21, 'u', 21.3);
insert into t_basic values(22, 22, 'v', 22.4);
insert into t_basic values(23, 23, 'w', 23.5);
insert into t_basic values(24, 24, 'x', 24.6);
insert into t_basic values(25, 25, 'y', 25.7);
insert into t_basic values(26, 26, 'z', 26.8);
insert into t_basic values(27, 27, 'A', 27.9);
insert into t_basic values(28, 28, 'B', 28.1);
insert into t_basic values(29, 29, 'C', 29.2);
insert into t_basic values(30, 30, 'D', 30.3);
insert into t_basic values(31, 31, 'E', 31.4);
insert into t_basic values(32, 32, 'F', 32.5);
insert into t_basic values(33, 33, 'G', 33.6);
insert into t_basic values(34, 34, 'H', 34.7);
insert into t_basic values(35, 35, 'I', 35.8);
insert into t_basic values(36, 36, 'J', 36.9);
insert into t_basic values(37, 37, 'K', 37.1);
insert into t_basic values(38, 38, 'L', 38.2);
insert into t_basic values(39, 39, 'M', 39.3);
insert into t_basic values(40, 40, 'N', 40.4);
insert into t_basic values(41, 41, 'O', 41.5);
insert into t_basic values(42, 42, 'P', 42.6);
insert into t_basic values(43, 43, 'Q', 43.7);
insert into t_basic values(44, 44, 'R', 44.8);
insert into t_basic values(45, 45, 'S', 45.9);
insert into t_basic values(46, 46, 'T', 46.1);
insert into t_basic values(47, 47, 'U', 47.2);
insert into t_basic values(48, 48, 'V', 48.3);
insert into t_basic values(49, 49, 'W', 49.4);
insert into t_basic values(50, 50, 'X', 50.5);
select * from t_basic;

select * from master;