'''
Author: zql <EMAIL>
Date: 2025-05-08 17:24:07
LastEditors: zql <EMAIL>
LastEditTime: 2025-05-20 20:10:01
FilePath: /gncdbflr/linux_dev/testfile/sql/case/test/gen_sql.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import os

# 配置
table_name = "combined_table"
num_records = 10000
output_file = "linux_dev/insert.sql"

# 生成 INSERT 语句
with open(output_file, "w") as f:
    for i in range(100000, 100000+num_records):
        int_val = (i % 1000) + 1
        double_val = (i % 1000) + 1.5
        varchar_val = f"'value_{i % 1000}'"
        insert_stmt = f"INSERT INTO {table_name} (id, int_value, double_value, varchar_value) VALUES ({i}, {int_val}, {double_val}, {varchar_val});\n"
        f.write(insert_stmt)

print(f"Generated {num_records} INSERT statements in {output_file}")