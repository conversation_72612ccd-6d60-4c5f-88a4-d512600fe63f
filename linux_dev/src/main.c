//#include "gncdb.h"
//#include "demo.h"
//#include "gncdb.h"
//#include "gncdbconstant.h"
#include "gncdb.h"
#include "runninglog.h"
#include "gncdbconstant.h"
// #include "singletest.h"
// #include "concurrence.h"
#include "raster2axdb.h"
#include <stdio.h>
#include <time.h>
#include <dirent.h>
//#include "sql_event.h"
//#include "tpcc.h"
//#include "transaction.h"
// extern int testCallBack(int columnNum, char** fieldName, char** fieldValue);
// 0. 静态分配的空间
#ifdef SELF_DEFINE_MEMORY
#define M_TEST_MEM_SIZE (1024 * 1024 * 1000)
DT_UCHAR8 gp_uc8_mem_buffer[M_TEST_MEM_SIZE];
#endif
struct mydata
{
  int a;
  int b;
};

int printHeader1 = 1; // 静态变量，用于跟踪是否需要打印列名
int strm1(char *filenam);
int strm3(char *filenam);
int RGB(char *filenam);
int importest(char *filenam);
int RGB_2(char *filenam);
int RGB_4(char *filenam);
int RGB_8(char *filenam);
int getRgbBlob(char *filenam,int id);
int testsetBlob1(char *filenam);
int testsetBlob2(char *filenam);
int testgetBlob(char *filenam);
// 生成随机字符串的函数
void generate_random_string(char *str, const int length) {
    static const char charset[] = 
        "abcdefghijklmnopqrstuvwxyz"
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        "0123456789";
    if (length > 1) { // 确保至少有一个字符加上终止符
        srand(time(NULL)); // 初始化随机数生成器
        for (int n = 0; n < length - 1; ++n) {
            int key = rand() % (int)(sizeof(charset) - 1);
            str[n] = charset[key];
        }
        str[length - 1] = '\0'; // 字符串结束符
    }
}
int myCallBack(void*data, int argc, char **azColName, char **argv) {
  int i;
  // 如果printHeader为1，则打印列名
  if (printHeader1) {
    for (i = 0; i < argc; i++) {
      printf("%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
    }
    printf("\n");
    printHeader1 = 0; // 设置为0，下次调用时不再打印列名
  }
  for (i = 0; i < argc; i++) {
    // printf("%da ",strlen(argv[i]));
    printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");
  }
  printf("\n");
  return 0;
}

void showResult(char *result,int result_size)
{
  int len = 0;
  int count = result_size/QUERY_POINT_SIZE;
  // printf("********************圆查询**************\n");
  // printf("共计%d个点\n",count);
  printf("lon       lat      height     \n");
  for(int i = 0;i < count;i++)
  {
    double lon,lat;
    short num;
    readDouble(&lon,(BYTE *)result,&len);
    result += 8;
    readDouble(&lat,(BYTE *)result,&len);
    result += 8;
    readShort(&num,(BYTE *)result,&len);
    result += 2;
    printf("%6f  ",lon);
    printf("%6f  ",lat);
    printf("%d\n",num);
  }
  
}

int main() {
  // 内存初始化
#ifdef SELF_DEFINE_MEMORY
  my_malloc_init(gp_uc8_mem_buffer, M_TEST_MEM_SIZE);
#endif
    // demo();
    //todo 金字塔
    // strm1();
    // clock_t start, end;
    // double cpu_time_used;
    struct timespec start, end;
    double diff;
    char *filename = "mygisdbx_cetxgs";
    char *dirpath = "/root/npp/gncdbflr/linux_dev/";
    char filepath[128];
    int len = strlen(dirpath) + strlen(filename);
    strcpy(filepath,dirpath);
    strcat(filepath,filename);
    filepath[len] = '\0';
    remove(filepath);
    clock_gettime(CLOCK_MONOTONIC, &start);
    // importest(filename);
    
    strm3(filename);
    // strm1(filename);
    // RGB_2(filename);
    // RGB_4(filename);
    // RGB_8(filename);
    // testgetBlob(filename);

    // start = clock();

    // testsetBlob1(filename);
    //记录结束时间
    // clock_gettime(CLOCK_MONOTONIC, &end);
    // // 计算时间差
    // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    // printf(">> finish time: %f secs\n", diff);
    // testsetBlob2(filename);

    // // 记录结束时间
    // end = clock();
    // // 计算 CPU 时间
    // cpu_time_used = ((double) (end - start)) / CLOCKS_PER_SEC;
    // printf("程序执行时间: %f 秒\n", cpu_time_used);

    // getRgbBlob(filename,358);
    // getRgbBlob(filename,2);
    // getRgbBlob(filename,3);
    // getRgbBlob(filename,4);
    // getRgbBlob(filename,9);
    // getRgbBlob(filename,10);
    // getRgbBlob(filename,11);
    // getRgbBlob(filename,12);

    //记录结束时间
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> final finish time: %f secs\n", diff);
    return 0;
}

int strm1(char *filenam) {
    int rc = 0;
    GNCDB* db = NULL;
    char* sql = NULL;
    char *result  = NULL;
    int result_size = 0;
    struct timespec start, end;
    double diff;
    char *filename = filenam;
    const char *filePath = "/root/gdata/HGT/strm1/N28E098.hgt";
    const char *folderPath = "/root/gdata/HGT/strm1";
    rc = GNCDB_open(&db, filename, 1024, 65536);
    if(rc != 0)
    {
      printf("ddd\n");
    }
    /*创建覆盖层*/
    printf("/*********创建地形数据覆盖层*********/\n");
    rc = GNCDB_CreateCoverage(db,"strm1",SAMPLE_16BIT,PIXEL_DEM,1,256,256,4326,1.0/3600,1.0/3600);
    GNCDB_exec(db,"show tables",myCallBack,NULL,NULL);
    sql = "select * from raster_coverages";
    rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    printf("\n");
    
    /*导入raster*/
    printf("/*********创建HGT文件到数据库*********/\n");
    rc = GNCDB_ImportRaster(db,filePath,"strm1",4326,false);
    // rc = GNCDB_ImportRaster(db,folderPath,"strm1",4326,false);
    printf("\n");
    
    // sql = "select * from strm1_tiles";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("\n");

    // sql = "select * from strm1_sections";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("\n");

    // sql = "select * from strm1_levels";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("\n");
    /*查询API*/
    // printf("/*********查询高程值*********/\n");
    // printf("********************点查询**************\n");
    // result_size = getPointQueryMemorySize();
    // result = my_malloc(result_size);
    GNCDB_RasterPointQuery(db,"strm1",result,result_size,78.5,28.5);
    // showResult(result,result_size);
    // my_free(result);
    // printf("\n");
    clock_gettime(CLOCK_MONOTONIC, &start);
    printf("********************线查询**************\n");
    getLineQueryMemorySize(db,"strm1",&result_size,98.5,28.5,98.55,28.55,0);
    //记录结束时间
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> final finish time: %f secs\n", diff);
    result = my_malloc(result_size);
    GNCDB_RasterLineQuery(db,"strm1",result,result_size,98.5,28.5,98.55,28.55,0);
    // showResult(result,result_size);
    my_free(result);
    printf("\n");
    //记录结束时间
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> final finish time: %f secs\n", diff);

    printf("********************矩形查询**************\n");
    getRectangleQueryMemorySize(db,"strm1",&result_size,80.50,28.50,80.502,28.502,true);
    result = my_malloc(result_size);
    GNCDB_RasterRectangleQuery(db,"strm1",result,result_size,80.50,28.50,80.502,28.502,true);
    showResult(result,result_size);
    my_free(result);
    printf("\n");

    printf("********************圆查询**************\n");
    getCircleleQueryMemorySize(db,"strm1",&result_size,80.5,28.5,0.001,true);
    result = my_malloc(result_size);
    GNCDB_RasterCircleleQuery(db,"strm1",result,result_size,80.5,28.5,0.001,true);
    showResult(result,result_size);
    my_free(result);
    printf("\n");

    // /*更新Raster*/
    // printf("/*********更新Raster*********/\n");
    // printf("********************更新前**************\n");
    // result_size = getPointQueryMemorySize();
    // result = my_malloc(result_size);
    // GNCDB_RasterPointQuery(db,"strm1",result,result_size,80.5,28.5);
    // showResult(result,result_size);
    // my_free(result);

    // GNCDB_UpdateRaster(db,"strm1",NULL,80.5,28.5,4096,4326);

    // printf("********************更新后**************\n");
    // result_size = getPointQueryMemorySize();
    // result = my_malloc(result_size);
    // GNCDB_RasterPointQuery(db,"strm1",result,result_size,80.5,28.5);
    // showResult(result,result_size);
    // my_free(result);
    // printf("\n");


    /*删除Raster*/
    // printf("/*********删除Raster中的单个HGT文件*********/\n");
    // GNCDB_DeleteRaster(db,"strm1","N28E080");
    
    // printf("********************tiles表**************\n");
    // sql = "select * from strm1_tiles";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");
    
    // printf("********************raster表**************\n");
    // sql = "select * from raster_coverages";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");

    // printf("********************sections表**************\n");
    // sql = "select * from strm1_sections";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");

    // printf("********************levels表**************\n");
    // sql = "select * from strm1_levels";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");

    GNCDB_close(&db);
    return 0;
}

int strm3(char *filenam)
{
    int rc = 0;
    GNCDB* db = NULL;
    char* sql = NULL;
    char *result  = NULL;
    int result_size = 0;
    char *filename = filenam;
    clock_t start, end;
    double cpu_time_used;
    const char *filePath = "/root/gdata/HGT/strm3/N28E096.hgt";
    const char *folderPath = "/root/gdata/HGT/strm3";
    rc = GNCDB_open(&db, filename, 1024,65536);

    /*创建覆盖层*/
    printf("/*********创建地形数据覆盖层*********/\n");
    rc = GNCDB_CreateCoverage(db,"strm3",SAMPLE_16BIT,PIXEL_DEM,1,256,256,4326,1.0/1200,1.0/1200);
    GNCDB_exec(db,"show tables",myCallBack,NULL,NULL);
    sql = "select * from raster_coverages";
    rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    printf("\n");
    
    //GNCDB_exec(db,"select * from schema",myCallBack,NULL,NULL);

    /*导入raster*/
    printf("/*********创建HGT文件到数据库*********/\n");
    rc = GNCDB_ImportRaster(db,filePath,"strm3",4326,false);
    if(rc != 0)
    {
      printf("dddd\n");
    }
    // sql = "select * from strm3_tiles where tile_id = 30";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    printf("\n");

    // sql = "select * from strm3_sections";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("\n");

    // start = clock();
    // sql = "select * from strm3_levels";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("\n");
    // /*查询API*/
    // printf("/*********查询高程值*********/\n");
    printf("********************点查询**************\n");
    result_size = getPointQueryMemorySize();
    result = my_malloc(result_size);
    GNCDB_RasterPointQuery(db,"strm3",result,result_size,96.5,28.5);
    showResult(result,result_size);
    my_free(result);
    printf("\n");

    printf("********************线查询**************\n");
    getLineQueryMemorySize(db,"strm3",&result_size,96.5,28.9,96.55,29.95,15);
    result = my_malloc(result_size);
    printf("%d\n",result_size/18);
    GNCDB_RasterLineQuery(db,"strm3",result,result_size,96.5,28.9,96.55,29.95,15);
    showResult(result,result_size);
    my_free(result);
    printf("\n");

    printf("********************矩形查询**************\n");
    getRectangleQueryMemorySize(db,"strm3",&result_size,96.50,28.50,96.51,28.51,true);
    result = my_malloc(result_size);
    printf("%d\n",result_size/18);
    GNCDB_RasterRectangleQuery(db,"strm3",result,result_size,96.50,28.50,96.51,28.51,true);
    showResult(result,result_size);
    my_free(result);
    printf("\n");


    printf("********************矩形查询**************\n");
    getRectangleQueryMemorySize(db,"strm3",&result_size,96.50,28.50,96.51,28.51,false);
    result = my_malloc(result_size);
    printf("%d\n",result_size/18);
    GNCDB_RasterRectangleQuery(db,"strm3",result,result_size,96.50,28.50,96.51,28.51,false);
    showResult(result,result_size);
    my_free(result);
    printf("\n");

    printf("********************圆查询**************\n");
    getCircleleQueryMemorySize(db,"strm3",&result_size,96.99,28.99,0.005,true);
    result = my_malloc(result_size);
    printf("%d\n",result_size/18);
    GNCDB_RasterCircleleQuery(db,"strm3",result,result_size,96.99,28.99,0.005,true);
    showResult(result,result_size);
    my_free(result);
    printf("\n");

    printf("********************圆查询**************\n");
    getCircleleQueryMemorySize(db,"strm3",&result_size,96.5,28.5,0.005,false);
    result = my_malloc(result_size);
    printf("%d\n",result_size/18);
    GNCDB_RasterCircleleQuery(db,"strm3",result,result_size,96.5,28.5,0.005,false);
    showResult(result,result_size);
    my_free(result);
    printf("\n");


    // 记录结束时间
    end = clock();

    // 计算 CPU 时间
    // cpu_time_used = ((double) (end - start)) / CLOCKS_PER_SEC;
    printf("查询时间: %f 秒\n", cpu_time_used);
    /*更新Raster*/
    printf("/*********更新Raster*********/\n");
    printf("********************更新前**************\n");
    result_size = getPointQueryMemorySize();
    result = my_malloc(result_size);
    GNCDB_RasterPointQuery(db,"strm3",result,result_size,96.5,28.5);
    showResult(result,result_size);
    my_free(result);

    GNCDB_UpdateRaster(db,"strm3",NULL,96.5,28.5,1234,4326);

    printf("********************更新后**************\n");
    result_size = getPointQueryMemorySize();
    result = my_malloc(result_size);
    GNCDB_RasterPointQuery(db,"strm3",result,result_size,96.5,28.5);
    showResult(result,result_size);
    my_free(result);
    printf("\n");

    printf("/*********删除前*********/\n");
    printf("\n");
    printf("********************sections表**************\n");
    sql = "select * from strm3_sections where section_name = 'N28E096'";
    rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    printf("\n");

    printf("********************tiles表**************\n");
    sql = "select count(*) from strm3_tiles where section_id = 15";
    rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");
    
    // printf("********************raster表**************\n");
    // sql = "select * from raster_coverages";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");


    
    /*删除Raster*/
    printf("/*********删除Raster后*********/\n");
    GNCDB_DeleteRaster(db,"strm3","N28E096");
    
    printf("********************tiles表**************\n");
    sql = "select count(*) from strm3_tiles where section_id = 15";
    rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    printf("\n");
    
    // printf("********************raster表**************\n");
    // sql = "select * from raster_coverages";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");

    printf("********************sections表**************\n");
    sql = "select count(*) from strm3_sections where section_name = 'N28E096'";
    rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("%d\n",rc);
    printf("\n");

    // printf("********************levels表**************\n");
    // sql = "select * from strm3_levels";
    // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
    // printf("GNCDB%d\n",rc);
    // printf("\n");

    GNCDB_close(&db);
    return 0;
}

int RGB(char *filenam)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char* sql = NULL;
  char *result  = NULL;
  int result_size = 0;
  char *filename = filenam;
  const char *jpg_filePath = "/root/gdata/a.jpg";
  rc = GNCDB_open(&db, filename, 1024,65536);
  GNCDB_CreateCoverage(db,"ww",8,18,3,256,256,4326,0.0000089831528410,0.0000089831528410);
  GNCDB_ImportRaster(db,jpg_filePath,"ww",4326,true);
  printf("********************tiles表**************\n");
  sql = "select * from ww_tiles";
  rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  printf("GNCDB%d\n",rc);
  printf("\n");
  
  printf("********************raster表**************\n");
  sql = "select * from raster_coverages";
  rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  printf("GNCDB%d\n",rc);
  printf("\n");

  printf("********************sections表**************\n");
  sql = "select * from ww_sections";
  rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  printf("GNCDB%d\n",rc);
  printf("\n");

  printf("********************levels表**************\n");
  sql = "select * from ww_levels";
  rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  printf("GNCDB%d\n",rc);
  GNCDB_close(&db);
  return 0;
}

int importest(char *filenam)
{
  GNCDB* db = NULL;
  int rc;
  char *filename = filenam;
  char *coverage_tiles = NULL;
  char strbuffer[1000][20];
  BYTE *blob_data = NULL;

const char *jpg_filePath[] = {
        "/root/gdata/RGB/256.jpg",
        "/root/gdata/RGB/512.jpg",
        "/root/gdata/RGB/1024.jpg",
        "/root/gdata/RGB/2048.jpg",
        "/root/gdata/RGB/4096.jpg",
        "/root/gdata/RGB/8192.jpg"
    };
  struct timespec start, end;
  double diff;
  double days;
  double hours;
  double mins;
  double secs;
  int num = 0;
      Transaction* tx = NULL;
      double min = -2147483647.0;
    double max = 2147483647.0;
    /*2.开启一个事务*/
	// tx = transcationConstrcut(db);
  rc = GNCDB_open(&db, filename, 1024 ,65536);
  GNCDB_CreateCoverage(db,"ww",SAMPLE_8BIT,18,3,256,256,4326,0.000008983152841,0.000008983152841);
  for(int i = 5;i<6;i++)
  {
    clock_gettime(CLOCK_MONOTONIC, &start);
    printf("Importing %d*%d\n",256*(int)(pow(2,i)),256*(int)(pow(2,i)));
    rc = GNCDB_ImportRaster(db,jpg_filePath[i],"ww",4326,true);
    if(rc == GNCDB_SUCCESS)
    {
      clock_gettime(CLOCK_MONOTONIC, &end);
      // 计算时间差
      diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
      printf(">> Finish import total time: %f secs\n", diff);
    }
    else{
      printf("Import failed,%d\n",rc);
    }
    // rc = GNCDB_exec(db,"select * from ww_tiles",myCallBack,NULL,NULL);
  }



  // printf("\n");
  // printf("1\n");
  // clock_gettime(CLOCK_MONOTONIC, &start);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/1.0_res.jpg",8192,8192,139.6208604999999920,35.3287347880734686,0.000008983152841,0.000008983152841);
  rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/1.0_res.jpg",2048,2048,139.6508604999999920,35.3087347880734686,0.000008983152841,0.000008983152841);
  rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/0.5_res.jpg",4096,4096,139.6208604999999920,35.3287347880734686,0.000017966305682,0.000017966305682);
  rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/0.25_res.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/0.125_res.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/0.0625_res.jpg",512,512,139.6208604999999920,35.3287347880734686,0.000143730445416,0.000143730445416);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/0.03125_res.jpg",256,256,139.6208604999999920,35.3287347880734686,0.000287460890831,0.000287460890831);
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> finish time: %f secs\n", diff);
  
  // printf("\n");
  // printf("1\n");
  // clock_gettime(CLOCK_MONOTONIC, &start);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview344.jpg",8192,8192,139.6208604999999920,35.3287347880734686,0.000008983152841,0.000008983152841);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview4.jpg",4096,4096,139.6208604999999920,35.3287347880734686,0.000017966305682,0.000017966305682);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview5.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview6.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview7.jpg",512,512,139.6208604999999920,35.3287347880734686,0.000143730445416,0.000143730445416);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview8.jpg",256,256,139.6208604999999920,35.3287347880734686,0.000287460890831,0.000287460890831);
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> finish time: %f secs\n", diff);
  
  // printf("\n");
  // printf("1\n");
  // clock_gettime(CLOCK_MONOTONIC, &start);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview344.jpg",8192,8192,139.6208604999999920,35.3287347880734686,0.000008983152841,0.000008983152841);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview4.jpg",4096,4096,139.6208604999999920,35.3287347880734686,0.000017966305682,0.000017966305682);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview5.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview6.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview7.jpg",512,512,139.6208604999999920,35.3287347880734686,0.000143730445416,0.000143730445416);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview8.jpg",256,256,139.6208604999999920,35.3287347880734686,0.000287460890831,0.000287460890831);
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> finish time: %f secs\n", diff);
  
  // printf("\n");
  // printf("1\n");
  // clock_gettime(CLOCK_MONOTONIC, &start);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview344.jpg",8192,8192,139.6208604999999920,35.3287347880734686,0.000008983152841,0.000008983152841);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview4.jpg",4096,4096,139.6208604999999920,35.3287347880734686,0.000017966305682,0.000017966305682);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview5.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview6.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview7.jpg",512,512,139.6208604999999920,35.3287347880734686,0.000143730445416,0.000143730445416);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview8.jpg",256,256,139.6208604999999920,35.3287347880734686,0.000287460890831,0.000287460890831);
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> finish time: %f secs\n", diff);
  
  // printf("\n");
  // printf("1\n");
  // clock_gettime(CLOCK_MONOTONIC, &start);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview344.jpg",8192,8192,139.6208604999999920,35.3287347880734686,0.000008983152841,0.000008983152841);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview4.jpg",4096,4096,139.6208604999999920,35.3287347880734686,0.000017966305682,0.000017966305682);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview5.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview6.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview7.jpg",512,512,139.6208604999999920,35.3287347880734686,0.000143730445416,0.000143730445416);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview8.jpg",256,256,139.6208604999999920,35.3287347880734686,0.000287460890831,0.000287460890831);
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> finish time: %f secs\n", diff);
  
  // printf("\n");
  // printf("1\n");
  // clock_gettime(CLOCK_MONOTONIC, &start);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview344.jpg",8192,8192,139.6208604999999920,35.3287347880734686,0.000008983152841,0.000008983152841);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview4.jpg",4096,4096,139.6208604999999920,35.3287347880734686,0.000017966305682,0.000017966305682);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview5.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview6.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  // // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview7.jpg",512,512,139.6208604999999920,35.3287347880734686,0.000143730445416,0.000143730445416);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview8.jpg",256,256,139.6208604999999920,35.3287347880734686,0.000287460890831,0.000287460890831);
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> finish time: %f secs\n", diff);



  // transactionCommit(tx, db);

    
  //   /*2.开启一个事务*/
	// tx = transcationConstrcut(db);

		


  // coverage_tiles = "q2q";
  // rc = GNCDB_createTable(db,coverage_tiles,9,
  //           "tile_id",FIELDTYPE_INTEGER,0,1,min,max,
  //           "pyramid_level",FIELDTYPE_INTEGER,0,0,min,max,
  //           "section_id",FIELDTYPE_INTEGER,0,0,min,max,
  //           "tile_data",FIELDTYPE_BLOB,1,0,min,max,
  //           "x_min",FIELDTYPE_REAL,0,0,min,max,
  //           "x_max",FIELDTYPE_REAL,0,0,min,max,
  //           "y_min",FIELDTYPE_REAL,0,0,min,max,
  //           "y_max",FIELDTYPE_REAL,0,0,min,max,
  //           "blob_size",FIELDTYPE_INTEGER,0,0,min,max,
  //   M_MAX_ROWS);

  // clock_gettime(CLOCK_MONOTONIC, &start);
  // coverage_tiles = "q2q";
  // for(int i=0;i<1200;i++)
  // {
  //   int rows = 0;
  //   rc = GNCDB_insert(db, &rows, coverage_tiles,
  //   i,rows,3,
  //   256*256*3,1,1,
  //   1,1,256*256*3);
    
  // }
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> Finish inisert time: %f secs\n", diff);


  //         blob_data = my_malloc(256*256*3);
  //   if(blob_data == NULL)
  //   {
  //     return GNCDB_SPACE_LACK;
  //   }
  // num = 32;
  // for(int i=0;i<1024;i+=num)
  // {

  //   for(int j = 0;j<num;j++)
  //   {
  //     sprintf(strbuffer[j], "%d", i+j);
  //   }

  //   for(int j = 0;j<num;j++)
  //   {
  //     rc = GNCDB_setBlob(db,coverage_tiles,3,blob_data,256*256*3,1,strbuffer[j]);
  //     if(rc != 0)
  //     {
  //       printf("%d\n",rc);
  //       printf("%d\n",i);
  //     }
  //   }
  //   my_free(blob_data);
  // }



  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> Finish test time: %f secs\n", diff);
  // transactionCommit(tx, db);
  // rc = GNCDB_exec(db,"select * from qq",myCallBack,NULL,NULL);
  // clock_gettime(CLOCK_MONOTONIC, &start);
  // coverage_tiles = "qq";
  // for(int i=1025;i<=2048;i++)
  // {
  //   int rows = 0;
  //   rc = GNCDB_insert(db, &rows, coverage_tiles,
  //   i,rows,3,
  //   256*256*3,1,1,
  //   1,1,256*256*3);
    
  // }
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> Finish inisert1 time: %f secs\n", diff);
  // for(int i=1025;i<=2048;i++)
  // {
  //   blob_data = my_malloc(256*256*3);
  //   if(blob_data == NULL)
  //   {
  //     return GNCDB_SPACE_LACK;
  //   }
  //   sprintf(strbuffer, "%d", i);
  //   rc = GNCDB_setBlob(db,coverage_tiles,3,blob_data,256*256*3,1,strbuffer);
  //   // printf("%d\n",rc);
  //   my_free(blob_data);
  // }
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> Finish test time1: %f secs\n", diff);

  // clock_gettime(CLOCK_MONOTONIC, &start);
  // coverage_tiles = "qq";
  // for(int i=2049;i<=3072;i++)
  // {
  //   int rows = 0;
  //   rc = GNCDB_insert(db, &rows, coverage_tiles,
  //   i,rows,3,
  //   256*256*3,1,1,
  //   1,1,256*256*3);
    
  // }
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> Finish inisert1 time: %f secs\n", diff);
  // for(int i=2049;i<=3072;i++)
  // {
  //   blob_data = my_malloc(256*256*3);
  //   if(blob_data == NULL)
  //   {
  //     return GNCDB_SPACE_LACK;
  //   }
  //   sprintf(strbuffer, "%d", i);
  //   rc = GNCDB_setBlob(db,coverage_tiles,3,blob_data,256*256*3,1,strbuffer);
  //   // printf("%d\n",rc);
  //   my_free(blob_data);
  // }
  // clock_gettime(CLOCK_MONOTONIC, &end);
  // // 计算时间差
  // diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
  // printf(">> Finish test time1: %f secs\n", diff);

  // GNCDB_ImportRaster(db,jpg_filePath,"ww",4326,false);
  // GNCDB_ImportRaster(db,jpg_filePath,"ww",4326,true);
  // printf("********************tiles表**************\n");
  // sql = "select * from ww_tiles";
  // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  // printf("GNCDB%d\n",rc);
  // printf("\n");
  
  // printf("********************raster表**************\n");
  // sql = "select * from raster_coverages";
  // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  // printf("GNCDB%d\n",rc);
  // printf("\n");

  // printf("********************sections表**************\n");
  // sql = "select * from ww_sections";
  // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  // printf("GNCDB%d\n",rc);
  // printf("\n");

  // printf("********************levels表**************\n");
  // sql = "select * from ww_levels";
  // rc = GNCDB_exec(db, sql, myCallBack,NULL, NULL);
  printf("GNCDB%d\n",rc);
  GNCDB_close(&db);
  return 0;
}

int RGB_2(char *filenam)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char* sql = NULL;
  char *result  = NULL;
  int result_size = 0;
  char *filename = filenam;
  const char *jpg_filePath = "/root/gdata/a.jpg";
  rc = GNCDB_open(&db, filename, 1024,65536);

  // rc = GNCDB_exec(db,"select tile_data from ww_tiles where tile_id = 1"
  // ,myCallBack,NULL,NULL);
  // printf("%d\n",rc);

  rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview344.jpg",8192,8192,139.6208604999999920,35.3287347880734686,0.000008983152841,0.000008983152841);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview4.jpg",4096,4096,139.6208604999999920,35.3287347880734686,0.000017966305682,0.000017966305682);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview5.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview6.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview7.jpg",512,512,139.6208604999999920,35.3287347880734686,0.000143730445416,0.000143730445416);
  // rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview8.jpg",256,256,139.6208604999999920,35.3287347880734686,0.000287460890831,0.000287460890831);
  GNCDB_close(&db);
  return 0;
}

int RGB_4(char *filenam)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char* sql = NULL;
  char *result  = NULL;
  int result_size = 0;
  char *filename = filenam;
  char *test = NULL;
  const char *jpg_filePath = "/root/gdata/c.jpg";
  test = my_malloc(123);
  rc = GNCDB_open(&db, filename, 1024, 65536);
  rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview5.jpg",2048,2048,139.6208604999999920,35.3287347880734686,0.000035932611364,0.000035932611364);

  my_free(test);
  GNCDB_close(&db);
  return 0;
}

int RGB_8(char *filenam)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char* sql = NULL;
  char *result  = NULL;
  int result_size = 0;
  char *filename = filenam;
  char *test = NULL;
  const char *jpg_filePath = "/root/gdata/c.jpg";
  test = my_malloc(123);
  rc = GNCDB_open(&db, filename, 1024,65535);
  rc = GNCDB_previewRaster(db,"ww","/root/gdata/output/preview6.jpg",1024,1024,139.6208604999999920,35.3287347880734686,0.000071865222728,0.000071865222728);
  my_free(test);
  GNCDB_close(&db);
  return 0;
}

int getRgbBlob(char *filenam,int id)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char* sql = NULL;
  char *result  = NULL;
  int tile_id = id;
  int result_size = 0;
  char *filename = filenam;
  const char *jpg_filePath = "/root/gdata/c.jpg";
  char strbuffer[20];
  BYTE *blob_data = NULL;
  rt_raster raster = NULL;
  char path[123];
	int blob_data_sz = 0;
	int blob_data_szs = 0;
  char *coverage_tiles = NULL;
  rc = GNCDB_open(&db, filename, 1024,65535);
  //获取瓦片的blob的size
  blob_data_sz = getBlobSize(db,"strm3",tile_id);
  blob_data = my_malloc(sizeof(char)*blob_data_sz);
  if(blob_data == NULL)
  {
      return GNCDB_SPACE_LACK;
  }
  sprintf(strbuffer, "%d", tile_id);
  coverage_tiles = strcatetablename("strm3","_tiles");
  rc = GNCDB_getBlob(db,coverage_tiles,3,blob_data,blob_data_sz,1,strbuffer);
  raster = raster_decode(0,blob_data,blob_data_sz,&rc);
  // blob_data_szs = blob_data_sz-35;
  // rc = raster_encode(raster,&blob_data,&blob_data_szs,100,isLittleEnsian());
  // printf("%d\n",rc);
  // rc = GNCDB_deleteBlob(db,coverage_tiles,3,1,strbuffer);
  // printf("%d\n",rc);
  // rc = GNCDB_setBlob(db,coverage_tiles,3,blob_data,blob_data_sz,1,strbuffer);
  // printf("%d\n",rc);
  sprintf(path,"/tmp/%s.bin",strbuffer);
  write_blob_tofile(raster->rasterBuffer,path,blob_data_sz-35);
}

int testgetBlob(char *filenam)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char* sql = NULL;
  char *result  = NULL;
  int tile_id = 0;
  int result_size = 0;
  char *filename = filenam;
  const char *jpg_filePath = "/root/gdata/c.jpg";
  char strbuffer[20];
  BYTE *blob_data = NULL;
  rt_raster raster = NULL;
  char path[123];
	int blob_data_sz = 0;
  char *coverage_tiles = NULL;
  rc = GNCDB_open(&db, filename, 1024,65535);
  //获取瓦片的blob的size

  coverage_tiles = strcatetablename("ww","_tiles");
  printf("1\n");
  for(int i=1;i<=1024;i++)
  {
    blob_data_sz = getBlobSize(db,"ww",i);
    blob_data = my_malloc(sizeof(char)*blob_data_sz);
    if(blob_data == NULL)
    {
      return GNCDB_SPACE_LACK;
    }
    sprintf(strbuffer, "%d", i);
    rc = GNCDB_getBlob(db,coverage_tiles,3,blob_data,blob_data_sz,1,strbuffer);
    raster = raster_decode(0,blob_data,blob_data_sz,&rc);
    rasterDestory(raster);
  }
  rc = GNCDB_close(&db);
  // sprintf(path,"/tmp/%s.rgb",strbuffer);
  // write_blob_tofile(raster->rasterBuffer,path,blob_data_sz-35);
}

int testsetBlob1(char *filenam)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char *filename = filenam;
  char *coverage_tiles = NULL;
  rc = GNCDB_open(&db, filename, 1024,65535);
  //获取瓦片的blob的size

  coverage_tiles = strcatetablename("ww","_tiles");
  for(int i=1070+1024;i<=2094+1024;i++)
  {
    int rows = 0;
    rc = GNCDB_insert(db, &rows, coverage_tiles,
    i,rows,3,
    256*256*3,1,1,
    1,1,256*256*3);
    
  }
  printf("ss%d\n",rc);
  rc = GNCDB_close(&db);
  // sprintf(path,"/tmp/%s.rgb",strbuffer);
  // write_blob_tofile(raster->rasterBuffer,path,blob_data_sz-35);
}

int testsetBlob2(char *filenam)
{
  GNCDB* db = NULL;
  int rc = GNCDB_SUCCESS;
  char* sql = NULL;
  char *result  = NULL;
  int tile_id = 0;
  int result_size = 0;
  char *filename = filenam;
  const char *jpg_filePath = "/root/gdata/c.jpg";
  char strbuffer[20];
  BYTE *blob_data = NULL;
  rt_raster raster = NULL;
  char path[123];
	int blob_data_sz = 0;
  char *coverage_tiles = NULL;
  rc = GNCDB_open(&db, filename, 1024,65535);
  //获取瓦片的blob的size

  coverage_tiles = strcatetablename("ww","_tiles");
  for(int i=1070+1024;i<=2094+1024;i++)
  {
    blob_data = my_malloc(256*256*3);
    if(blob_data == NULL)
    {
      return GNCDB_SPACE_LACK;
    }
    sprintf(strbuffer, "%d", i);
    rc = GNCDB_setBlob(db,coverage_tiles,3,blob_data,256*256*3,1,strbuffer);
    // printf("%d\n",rc);
    my_free(blob_data);
  }
  printf("dffc%d\n",rc);
  rc = GNCDB_close(&db);
  // sprintf(path,"/tmp/%s.rgb",strbuffer);
  // write_blob_tofile(raster->rasterBuffer,path,blob_data_sz-35);
}