#include "catalog.h"
#include "gncdbconstant.h"
#include "btreecursor.h"
#include "btreepage.h"
#include "gncdbconstant.h"
#include "hash.h"
#include "hashmap.h"
#include "parse_defs.h"
#include "table_stats.h"
#include "typedefine.h"
#include "utils.h"
#include "gncdb.h"
#include "table_stats.h"
#include "oss_type_def.h"
#include <stdio.h>
#include <string.h>
#include <time.h>
#include <limits.h>
#include "transaction.h"
#include "vararraylist.h"

int setTableSchemaColMetas(TableSchema *tableSchema, char *tableName);

//* 代价模型的可修改配置项
OPT_COST_CONFIG optCostConfig = {
    .TUPLE_COST = 1, .CPU_OPERATOR_COST = 0.25, .MEM_PAGE_COST = 50, .RANDOM_PAGE_COST = 400};

//* BNLJ逻辑块的元组数量
int JOIN_BLOCK_TNUM = 50;

char *catalogGetCreateIndexSql(varArrayList *indexCols, char *indexName, char *tableName, IndexType indexType);
int   createHashIndexToMasterRecord(BYTE *masterRecord, int autoId, HashIndex *hashIndex);
int   setTableSchemaColMetas(TableSchema *tableSchema, char *tableName);
/// <summary>
/// 获取主键的下标array
/// </summary>
/// <param name="catalog"></param>
/// <param name="tableName"></param>
/// <returns></returns>
varArrayList *getPrimaryIndexArray(struct Catalog *catalog, char *tableName)
{
    varArrayList *array = NULL;
    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
    array = hashMapGet(catalog->tablePrimaryKeyIndexMap, tableName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteUnLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");
    if (array == NULL)
    {
        return NULL;
    }
    return array;
}

varArrayList *getPrimaryOffsetArray(struct Catalog *catalog, char *tableName)
{
    varArrayList *array = NULL;
    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyOffsetMap");
    WriteLock(&(catalog->keyOffsetLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyOffsetMap");
    array = hashMapGet(catalog->tablePrimaryKeyOffsetMap, tableName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyOffsetMap");
    WriteUnLock(&(catalog->keyOffsetLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyOffsetMap");
    if (array == NULL)
    {
        return NULL;
    }
    return array;
}

varArrayList *getPrimaryVarcharLenArray(struct Catalog *catalog, char *tableName)
{
    varArrayList *array = NULL;

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
    WriteLock(&(catalog->keyVarcharLenLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyVarcharLenMap");
    array = hashMapGet(catalog->tablePrimaryKeyVarcharLenMap, tableName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
    WriteUnLock(&(catalog->keyVarcharLenLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyVarcharLenMap");
    if (array == NULL)
    {
        return NULL;
    }

    return array;
}
/// <summary>
/// 获取主键的类型array
/// </summary>
/// <param name="catalog"></param>
/// <param name="tableName"></param>
/// <returns></returns>
varArrayList *getPrimaryTypeArray(struct Catalog *catalog, char *tableName)
{
    varArrayList *array = NULL;
    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
    array = hashMapGet(catalog->tablePrimaryKeyTypeMap, tableName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteUnLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");
    if (array == NULL)
    {
        return NULL;
    }
    return array;
}
/// <summary>
/// 获取一个表的TableSchema
/// </summary>
/// <param name="catalog"></param>
/// <param name="tableName"></param>
/// <returns></returns>
TableSchema *getTableSchema(struct Catalog *catalog, char *tableName)
{
    TableSchema *tableSchema = NULL;

    if (strcmp(tableName, SCHEMANAME) == 0)
    {
        tableSchema = catalog->schemaTableSchema;
    }
    else if (strcmp(tableName, MASTERNAME) == 0)
    {
        tableSchema = catalog->masterTableSchema;
    }
    else
    {
        LOG(LOG_TRACE, "SLOCKing:%s", "tableSchemaMap");
        WriteLock(&(catalog->tableSchemaLatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "tableSchemaMap");
        tableSchema = hashMapGet(catalog->tableSchemaMap, tableName);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "tableSchemaMap");
        WriteUnLock(&(catalog->tableSchemaLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");
    }
    if (tableSchema == NULL)
    {
        return NULL;
    }
    else
    {
        return tableSchema;
    }
}

TableSchema *getTableSchemaUnLock(struct Catalog *catalog, char *tableName)
{
    TableSchema *tableSchema = NULL;

    if (strcmp(tableName, SCHEMANAME) == 0)
    {
        tableSchema = catalog->schemaTableSchema;
    }
    else if (strcmp(tableName, MASTERNAME) == 0)
    {
        tableSchema = catalog->masterTableSchema;
    }
    else
    {
        tableSchema = hashMapGet(catalog->tableSchemaMap, tableName);
    }
    if (tableSchema == NULL)
    {
        return NULL;
    }
    else
    {
        return tableSchema;
    }
}

varArrayList *getPrimaryKeyIndex(struct Catalog *catalog, char *tableName)
{
    varArrayList *array = NULL;
    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
    array = hashMapGet(catalog->tablePrimaryKeyIndexMap, tableName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteUnLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");
    if (array == NULL)
    {
        return NULL;
    }
    return array;
}

varArrayList *getPrimaryKeyType(struct Catalog *catalog, char *tableName)
{
    varArrayList *array = NULL;
    LOG(LOG_TRACE, "SLOCKing	:%s", "tablePrimaryKeyTypeMap");
    WriteLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SLOCKend	:%s", "tablePrimaryKeyTypeMap");
    array = hashMapGet(catalog->tablePrimaryKeyTypeMap, tableName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteUnLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");
    if (array == NULL)
    {
        return NULL;
    }
    return array;
}

/// <summary>
/// 获取一个索引的TableSchema
/// </summary>
/// <param name="catalog"></param>
/// <param name="tableName"></param>
/// <returns></returns>
TableSchema *getIndexSchema(struct Catalog *catalog, char *fullIdxName)
{
    TableSchema *tableSchema = NULL;

    LOG(LOG_TRACE, "SLOCKing:%s", "indexSchema");
    WriteLock(&(catalog->indexSchemaLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "indexSchema");
    tableSchema = hashMapGet(catalog->indexSchemaMap, fullIdxName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "indexSchema");
    WriteUnLock(&(catalog->indexSchemaLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "indexSchema");

    if (tableSchema == NULL)
    {
        return NULL;
    }
    else
    {
        return tableSchema;
    }
}
/// <summary>
/// 创建一个主键下标array
/// </summary>
/// <param name="primaryIndexArray"></param>
/// <param name="tableSchema"></param>
/// <returns></returns>
int createPrimaryIndex(struct varArrayList *primaryIndexArray, struct TableSchema *tableSchema)
{

    Column *column       = NULL;
    bool    isPrimaryKey = false;
    int     rc = 0, i = 0;

    if (primaryIndexArray == NULL || tableSchema == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    /* 遍历tableSchema，判断哪些列是主键，并记录index */
    for (i = 0; i < tableSchema->columnNum; i++)
    {
        column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
        if (column == NULL)
        {
            return GNCDB_COLUMN_NOT_FOUND;
        }

        isPrimaryKey = column->columnConstraint->isPrimaryKey;
        if (isPrimaryKey)
        {
            rc = varArrayListAdd(primaryIndexArray, &i);
            if (rc != GNCDB_SUCCESS)
            {
                return GNCDB_ARRAY_ADD_FALSE;
            }
        }
    }

    return GNCDB_SUCCESS;
}

/// <summary>
/// 创建一个主键类型array
/// </summary>
/// <param name="primaryTypeArray"></param>
/// <param name="tableSchema"></param>
/// <returns></returns>
int createPrimaryType(struct varArrayList *primaryTypeArray, struct TableSchema *tableSchema)
{

    int     i = 0, rc = 0, type = 0;
    bool    isPrimaryKey = false;
    Column *column       = NULL;
    if (primaryTypeArray == NULL || tableSchema == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    /* 遍历tableSchema，判断哪些列是主键，并记录数据类型type */
    for (i = 0; i < tableSchema->columnNum; i++)
    {
        column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
        if (column == NULL)
        {
            return GNCDB_COLUMN_NOT_FOUND;
        }

        isPrimaryKey = column->columnConstraint->isPrimaryKey;
        if (isPrimaryKey)
        {
            type = column->fieldType;
            rc   = varArrayListAdd(primaryTypeArray, &type);
            if (rc != GNCDB_SUCCESS)
            {
                return GNCDB_ARRAY_ADD_FALSE;
            }
        }
    }

    return GNCDB_SUCCESS;
}

/// <summary>
/// 列约束构造函数
/// </summary>
/// <param name="minValue">最小值</param>
/// <param name="maxValue">最大值</param>
/// <param name="canBeNull">是否可以为空</param>
/// <param name="isPrimaryKey">是否为主键</param>
/// <returns></returns>
ColumnConstraint *columnConstraintConstruct(double minValue, double maxValue, int canBeNull, int isPrimaryKey)
{
    ColumnConstraint *columnConstraint = NULL;
    columnConstraint                   = my_malloc(sizeof(ColumnConstraint));
    if (columnConstraint == NULL)
    {
        return NULL;
    }

    columnConstraint->maxValue     = maxValue;
    columnConstraint->minValue     = minValue;
    columnConstraint->canBeNull    = canBeNull;
    columnConstraint->isPrimaryKey = isPrimaryKey;

    return columnConstraint;
}

/// <summary>
/// 列约束销毁函数
/// </summary>
/// <param name="columnConstraint"></param>
void columnConstraintDestroy(ColumnConstraint *columnConstraint)
{
    if (columnConstraint == NULL)
    {
        return;
    }
    my_free(columnConstraint);
    return;
}
/// <summary>
/// 列信息构造函数
/// </summary>
/// <param name="fieldType">数据类型</param>
/// <param name="fieldName">属性名</param>
/// <param name="columnConstraint"></param>
/// <returns></returns>
Column *columnConstruct(FieldType fieldType, char *fieldName, ColumnConstraint *columnConstraint, int offset)
{
    Column *column = NULL;
    int     len    = 0;
    column         = my_malloc(sizeof(Column));
    if (column == NULL)
    {
        return NULL;
    }

    column->columnConstraint = columnConstraint;
    column->fieldType        = fieldType;
    column->offset           = offset;
    len                      = strlen(fieldName);
    column->fieldName        = my_malloc(len + 1);
    if (column->fieldName == NULL)
    {
        return NULL;
    }
    memset(column->fieldName, '0', len + 1);
    strcpy(column->fieldName, fieldName);

    return column;
}

Column *columnDeepCopy(Column *column)
{
    Column *          newColumn        = NULL;
    ColumnConstraint *newColConstraint = NULL;
    newColumn                          = my_new0(Column);
    if (newColumn == NULL)
    {
        return NULL;
    }
    newColConstraint = columnConstraintConstruct(column->columnConstraint->minValue,
        column->columnConstraint->maxValue,
        column->columnConstraint->canBeNull,
        column->columnConstraint->isPrimaryKey);
    if (newColConstraint == NULL)
    {
        my_free(newColumn);
        return NULL;
    }
    newColumn->fieldType        = column->fieldType;
    newColumn->tableName        = strdup(column->tableName);
    newColumn->fieldName        = strdup(column->fieldName);
    newColumn->columnConstraint = newColConstraint;
    newColumn->offset           = column->offset;
    return newColumn;
}

/// <summary>
/// 列销毁函数
/// </summary>
/// <param name="column"></param>
void columnDestroy(Column *column)
{
    if (column == NULL)
    {
        return;
    }
    if (column->columnConstraint != NULL)
    {
        columnConstraintDestroy(column->columnConstraint);
    }
    if (column->fieldName != NULL)
    {
        my_free(column->fieldName);
    }
    if (column->tableName != NULL)
    {
        my_free(column->tableName);
    }
    my_free(column);
    return;
}

void arrayColumnDestroy(void *arg)
{
    Column * column  = NULL;
    Column **pColumn = NULL;
    pColumn          = (Column **)arg;
    column           = *pColumn;
    columnDestroy(column);
}

/// <summary>
/// 增加masterid
/// </summary>
/// <param name="catalog"></param>
/// <returns></returns>
int masterCurrentMaxIdAdd(struct Catalog *catalog)
{
    catalog->masterCurrentMaxId++;
    return catalog->masterCurrentMaxId;
}

/// <summary>
/// 总页数增加
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
void totalPageNumAdd(struct GNCDB *db)
{
    WriteLock(&(db->latch));

    db->totalPageNum++;

    WriteUnLock(&(db->latch));
}

/// <summary>
/// 增加schemaid
/// </summary>
/// <param name="catalog"></param>
/// <returns></returns>
int schemaCurrentMaxIncIdAdd(struct Catalog *catalog)
{
    catalog->schemaCurrentMaxIncId++;
    return catalog->schemaCurrentMaxIncId;
}
/// <summary>
/// columnList需要在外部进行创建
/// </summary>
/// <param name="maxRowNum">最大行数</param>
/// <param name="columnNum">列数</param>
/// <param name="columnList">列信息array</param>
/// <returns></returns>
TableSchema *tableSchemaConstruct(int maxRowNum, int columnNum, varArrayList *columnList)
{

    TableSchema *tableSchema = NULL;
    tableSchema              = my_malloc0(sizeof(TableSchema));
    if (tableSchema == NULL)
    {
        return NULL;
    }
    tableSchema->maxRowNum  = maxRowNum;
    tableSchema->columnNum  = columnNum;
    tableSchema->rowId      = 0;
    tableSchema->tableParam = 2; /* 构造时只能是2 */
    tableSchema->columnList = columnList;
    return tableSchema;
}
/// <summary>
/// TableSchema销毁函数
/// </summary>
/// <param name="tableSchema"></param>
void tableSchemaDestroy(TableSchema *tableSchema)
{
    if (tableSchema == NULL)
    {
        return;
    }
    if (tableSchema->columnList != NULL)
    {
        varArrayListDestroy(&(tableSchema->columnList));
    }
    if (tableSchema->tablecolMetas != NULL)
    {
        varArrayListDestroy(&(tableSchema->tablecolMetas));
    }

    my_free(tableSchema);
    return;
}
/// <summary>
/// catalog构造函数
/// </summary>
/// <param name="catalog"></param>
/// <returns></returns>
int catalogConstruct(struct GNCDB *db)
{
    int             rc      = 0;
    struct Catalog *catalog = NULL;
    catalog                 = (Catalog *)my_malloc(sizeof(Catalog));
    if (catalog == NULL)
    {
        return GNCDB_MEM;
    }
    catalog->masterCurrentMaxId           = 0;
    catalog->schemaCurrentMaxIncId        = 0;
    catalog->tablePrimaryKeyIndexMap      = NULL;
    catalog->tablePrimaryKeyTypeMap       = NULL;
    catalog->tablePrimaryKeyOffsetMap     = NULL;
    catalog->tablePrimaryKeyVarcharLenMap = NULL;
    catalog->tableSchemaMap               = NULL;
    catalog->masterTable                  = NULL;
    catalog->schemaTable                  = NULL;
    catalog->tableMap                     = NULL;
    catalog->indexMap                     = NULL;
    catalog->indexSchemaMap               = NULL;
    db->catalog                           = NULL;
    /* 初始化锁 */
    ReadWriteLockInit(&(catalog->latch));
    ReadWriteLockInit(&(catalog->indexMapLatch));
    ReadWriteLockInit(&(catalog->indexSchemaLatch));
    ReadWriteLockInit(&(catalog->keyIndexLatch));
    ReadWriteLockInit(&(catalog->keyOffsetLatch));
    ReadWriteLockInit(&(catalog->keyVarcharLenLatch));
    ReadWriteLockInit(&(catalog->keyTypeLatch));
    ReadWriteLockInit(&(catalog->tableMapLatch));
    ReadWriteLockInit(&(catalog->tableSchemaLatch));
    /* 主键下标的map */
    catalog->tablePrimaryKeyIndexMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->tablePrimaryKeyIndexMap == NULL)
    {
        my_free(catalog);
        return GNCDB_PRIMARYKEYINDEXMAP_CREATE_FALSE;
    }
    /* 主键偏移的map */
    catalog->tablePrimaryKeyOffsetMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->tablePrimaryKeyOffsetMap == NULL)
    {
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        my_free(catalog);
        return GNCDB_PRIMARYKEYOFFSETMAP_CREATE_FALSE;
    }
    /* 主键varchar长度的map */
    catalog->tablePrimaryKeyVarcharLenMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->tablePrimaryKeyVarcharLenMap == NULL)
    {
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        my_free(catalog);
        return GNCDB_PRIMARYKEYVARCHARLENMAP_CREATE_FALSE;
    }
    /* 主键类型的map */
    catalog->tablePrimaryKeyTypeMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->tablePrimaryKeyTypeMap == NULL)
    {
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_PRIMARYKEYTYPEMAP_CREATE_FALSE;
    }
    /* tableSchemaMap */
    catalog->tableSchemaMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->tableSchemaMap == NULL)
    {
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_TABLESCHEMAMAP_CREATE_FALSE;
    }
    /* 获取master和schema的TableSchema */
    rc = catalogGetMasterTableSchema(catalog);
    if (rc != GNCDB_SUCCESS)
    {
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_MASTER_TS_CREATE_FALSE;
    }
    rc = setTableSchemaColMetas(catalog->masterTableSchema, MASTERNAME);
    if (rc != GNCDB_SUCCESS)
    {
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_SCHEMA_TS_CREATE_FALSE;
    }
    rc = catalogGetSchemaTableSchema(catalog);
    if (rc != GNCDB_SUCCESS)
    {
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_SCHEMA_TS_CREATE_FALSE;
    }
    rc = setTableSchemaColMetas(catalog->schemaTableSchema, SCHEMANAME);
    if (rc != GNCDB_SUCCESS)
    {
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_SCHEMA_TS_CREATE_FALSE;
    }
    /* 获取master和schema的btree */
    catalog->masterTable = btreeTableConstruct(MASTERNAME, MASTER_ROOT_PID, catalog->masterTableSchema);
    if (catalog->masterTable == NULL)
    {
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_MASTERTABLE_CREATE_FALSE;
    }
    catalog->schemaTable = btreeTableConstruct(SCHEMANAME, SCHEMA_ROOT_PID, catalog->schemaTableSchema);
    if (catalog->schemaTable == NULL)
    {
        btreeTableDestroy(&(catalog->masterTable));
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_SCHEMATABLE_CREATE_FALSE;
    }

    /* 创建表的map */
    catalog->tableMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->tableMap == NULL)
    {
        btreeTableDestroy(&(catalog->schemaTable));
        btreeTableDestroy(&(catalog->masterTable));
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_TABLEMAP_CREATE_FALSE;
    }
    /* 创建表的map */
    catalog->indexMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->indexMap == NULL)
    {
        btreeTableDestroy(&(catalog->schemaTable));
        btreeTableDestroy(&(catalog->masterTable));
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tableMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_TABLEMAP_CREATE_FALSE;
    }
    /* indexSchemaMap */
    catalog->indexSchemaMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->indexSchemaMap == NULL)
    {
        btreeTableDestroy(&(catalog->schemaTable));
        btreeTableDestroy(&(catalog->masterTable));
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tableMap));
        hashMapDestroy(&(catalog->indexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));
        my_free(catalog);
        return GNCDB_TABLESCHEMAMAP_CREATE_FALSE;
    }

    /* tableStatusMap */
    catalog->tableStatsMap = hashMapCreate(STRKEY, 0, NULL);
    if (catalog->tableStatsMap == NULL)
    {
        btreeTableDestroy(&(catalog->schemaTable));
        btreeTableDestroy(&(catalog->masterTable));
        hashMapDestroy(&(catalog->tableSchemaMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));
        hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));
        hashMapDestroy(&(catalog->tableMap));
        hashMapDestroy(&(catalog->indexMap));
        hashMapDestroy(&(catalog->indexSchemaMap));
        my_free(catalog);
        return GNCDB_TABLEMAP_CREATE_FALSE;
    }

    db->catalog = catalog;

    // totalPageNumAdd(db);
    // totalPageNumAdd(db);
    return GNCDB_SUCCESS;
}

/// <summary>
///  析构方法
/// </summary>
/// <param name="catalog"></param>
void catalogDestroy(Catalog *catalog)
{
    HashMapIterator *tableIterator = NULL;
    BtreeTable *     table         = NULL;
    TableSchema *    tableSchema   = NULL;
    // TableStats* tableStats = NULL;
    varArrayList *array = NULL;
    if (catalog == NULL)
    {
        return;
    }
    tableIterator = createHashMapIterator(catalog->tableMap);
    if (tableIterator == NULL)
    {
        return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        table         = tableIterator->entry->value;
        btreeTableDestroy(&table);
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->tableMap));

    tableIterator = createHashMapIterator(catalog->tableSchemaMap);
    if (tableIterator == NULL)
    {
        return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        tableSchema   = tableIterator->entry->value;
        tableSchemaDestroy(tableSchema);
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->tableSchemaMap));

    tableIterator = createHashMapIterator(catalog->tablePrimaryKeyIndexMap);
    if (tableIterator == NULL)
    {
        return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        array         = tableIterator->entry->value;
        varArrayListDestroy(&array);
        if (strcmp(tableIterator->entry->key, MASTERNAME) == 0)
        {
            // printf("release master PrimaryKeyIndex\n");
            continue;
        }
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->tablePrimaryKeyIndexMap));

    tableIterator = createHashMapIterator(catalog->tablePrimaryKeyOffsetMap);
    if (tableIterator == NULL)
    {
        return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        array         = tableIterator->entry->value;
        varArrayListDestroy(&array);
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->tablePrimaryKeyOffsetMap));

    tableIterator = createHashMapIterator(catalog->tablePrimaryKeyVarcharLenMap);
    if (tableIterator == NULL)
    {
        return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        array         = tableIterator->entry->value;
        varArrayListDestroy(&array);
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->tablePrimaryKeyVarcharLenMap));

    tableIterator = createHashMapIterator(catalog->tablePrimaryKeyTypeMap);
    if (tableIterator == NULL)
    {
        return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        array         = tableIterator->entry->value;
        varArrayListDestroy(&array);
        if (strcmp(tableIterator->entry->key, MASTERNAME) == 0)
        {
            // printf("release master PrimaryKeyTypeMap\n");
            continue;
        }
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->tablePrimaryKeyTypeMap));

    /* 遍历销毁每个索引 */
    tableIterator = createHashMapIterator(catalog->indexMap);
    if (tableIterator == NULL)
    {
        // return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        hashIndexDestroy((HashIndex **)&(tableIterator->entry->value));
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->indexMap));

    /* 遍历销毁每个索引schema */
    tableIterator = createHashMapIterator(catalog->indexSchemaMap);
    if (tableIterator == NULL)
    {
        // return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        tableSchemaDestroy(tableIterator->entry->value);
    }
    freeHashMapIterator(&tableIterator);
    hashMapDestroy(&(catalog->indexSchemaMap));

    // free tableStatsMap
    tableIterator = createHashMapIterator(catalog->tableStatsMap);
    if (tableIterator == NULL)
    {
        // return;
    }
    while (hasNextHashMapIterator(tableIterator))
    {
        tableIterator = nextHashMapIterator(tableIterator);
        // printf("\ndestroy table stats: %s\n", (char *)tableIterator->entry->key);
        /* 调用具体的函数 */
        destroy_table_stats((TableStats **)&tableIterator->entry->value);
    }
    hashMapDestroy(&(catalog->tableStatsMap));

    freeHashMapIterator(&tableIterator);
    // hashMapDestroy(&(catalog->tableStatsMap));

    tableSchemaDestroy(catalog->schemaTableSchema);
    tableSchemaDestroy(catalog->masterTableSchema);
    btreeTableDestroy(&(catalog->schemaTable));
    btreeTableDestroy(&(catalog->masterTable));
    ReadWriteLockDestroy(&(catalog->latch));
    ReadWriteLockDestroy(&(catalog->indexMapLatch));
    ReadWriteLockDestroy(&(catalog->indexSchemaLatch));
    ReadWriteLockDestroy(&(catalog->keyIndexLatch));
    ReadWriteLockDestroy(&(catalog->keyOffsetLatch));
    ReadWriteLockDestroy(&(catalog->keyVarcharLenLatch));
    ReadWriteLockDestroy(&(catalog->keyTypeLatch));
    ReadWriteLockDestroy(&(catalog->tableMapLatch));
    ReadWriteLockDestroy(&(catalog->tableSchemaLatch));
    my_free(catalog);

    return;
}

/// <summary>
/// 解析schema表
/// </summary>
/// <param name="db"></param>
/// <param name="schemaCursor"></param>
/// <param name="columnArray"></param>
/// <param name="indexArray"></param>
/// <param name="typeArray"></param>
/// <param name="tableField"></param>
/// <param name="colNumField"></param>
/// <returns></returns>
// int parseSchema(GNCDB* db, BtreeCursor* schemaCursor, varArrayList* columnArray, varArrayList* indexArray,
// varArrayList* typeArray, VarCharField* tableField, IntField* colNumField)
int parseSchema(GNCDB *db, BtreeCursor *schemaCursor, varArrayList *columnArray, varArrayList *indexArray,
    varArrayList *typeArray, varArrayList *keyOffsetArray, varArrayList *keyVarcharLenArray, char *tableName,
    int colNum)
{
    int               rc      = 0;
    int               idValue = 0;
    char              columnNameValue[16];
    char              tableNameValue[16];
    int               columnIndexValue  = 0;
    int               columnTypeValue   = 0;
    int               canbeNullValue    = 0;
    double            minValue          = 0;
    double            maxValue          = 0;
    int               isPrimaryKeyValue = 0;
    ColumnConstraint *columnConstraint  = NULL;
    Column *          column            = NULL;
    Catalog *         catalog           = NULL;
    BYTE *            schemaRecord      = NULL;
    int               offset            = 0;
    int               colOffset         = 0;
    int               len               = 0;

    catalog   = db->catalog;
    colOffset = GET_BITMAP_LENGTH(colNum);
    /* 拿取表 */

    while (btreeTableHasNextTuple(schemaCursor))
    {
        schemaRecord = btreeTableGetNextRecord(catalog->schemaTable, schemaCursor, db);
        /* 拿取schema表的记录构成tableschema */
        offset = GET_BITMAP_LENGTH(SCHEMACOLNUM);
        memcpy(&idValue, schemaRecord + offset, INT_SIZE);
        offset += INT_SIZE;
        if (idValue < 0)
        {
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        if (db->catalog->schemaCurrentMaxIncId < idValue)
        {
            db->catalog->schemaCurrentMaxIncId = idValue;
        }
        /* 列名 */
        memcpy(columnNameValue, schemaRecord + offset, TABLENAME_FIELD_MAXLEN);
        offset += TABLENAME_FIELD_MAXLEN;
        /* 表名 */
        memcpy(tableNameValue, schemaRecord + offset, TABLENAME_FIELD_MAXLEN);
        offset += TABLENAME_FIELD_MAXLEN;
        /* 列号 */
        memcpy(&columnIndexValue, schemaRecord + offset, sizeof(int));
        offset += INT_SIZE;
        if (columnIndexValue < 0)
        {
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        /* 列的数据类型 */
        memcpy(&columnTypeValue, schemaRecord + offset, sizeof(int));
        offset += INT_SIZE;
        if (columnTypeValue < 0)
        {
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        /* 是否为空 */
        memcpy(&canbeNullValue, schemaRecord + offset, sizeof(int));
        offset += INT_SIZE;
        if (canbeNullValue < 0)
        {
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        /* 最小值 */
        memcpy(&minValue, schemaRecord + offset, sizeof(double));
        offset += DOUBLE_SIZE;
        /* 最大值 */
        memcpy(&maxValue, schemaRecord + offset, sizeof(double));
        offset += DOUBLE_SIZE;
        /* 是否为主键 */
        memcpy(&isPrimaryKeyValue, schemaRecord + offset, sizeof(int));
        offset += INT_SIZE;
        if (isPrimaryKeyValue < 0)
        {
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }

        if (strcmp(tableNameValue, tableName) == 0)
        {
            columnConstraint = columnConstraintConstruct(minValue, maxValue, canbeNullValue, isPrimaryKeyValue);
            if (columnConstraint == NULL)
            {
                return GNCDB_COLUMNCONSTRAINT_CREATE_FALSE;
            }
            column = columnConstruct(columnTypeValue, columnNameValue, columnConstraint, colOffset);
            if (column == NULL)
            {
                columnConstraintDestroy(columnConstraint);
                return GNCDB_COLUMN_CREATE_FALSE;
            }
            column->tableName = my_malloc(strlen(tableNameValue) + 1);
            if (column->tableName == NULL)
            {
                columnDestroy(column);
                return GNCDB_MEM;
            }
            strcpy(column->tableName, tableNameValue);
            rc = varArrayListAddPointer(columnArray, column);
            if (column->fieldType == FIELDTYPE_INTEGER)
            {
                colOffset += INT_SIZE;
            }
            else if (column->fieldType == FIELDTYPE_REAL)
            {
                colOffset += DOUBLE_SIZE;
            }
            else if (column->fieldType == FIELDTYPE_VARCHAR)
            {
                colOffset += column->columnConstraint->maxValue;
            }
            if (rc != GNCDB_SUCCESS)
            {
                columnDestroy(column);
                return GNCDB_ARRAY_ADD_FALSE;
            }
            if (isPrimaryKeyValue == 1)
            {
                varArrayListAdd(indexArray, &(columnIndexValue));
                varArrayListAdd(typeArray, &(columnTypeValue));
                varArrayListAdd(keyOffsetArray, &(column->offset));
                if (column->fieldType == FIELDTYPE_VARCHAR)
                {
                    len = columnConstraint->maxValue;
                    varArrayListAdd(keyVarcharLenArray, &(len));
                }
            }
        }
        if (columnArray->elementCount == colNum)
        {
            break;
        }
    }
    return GNCDB_SUCCESS;
}

int setTableSchemaColMetas(TableSchema *tableSchema, char *tableName)
{
    int      rc      = 0;
    ColMeta *colMeta = NULL;
    Column * column  = NULL;
    int      offset  = 0;
    char *   memptr  = NULL;

    if (tableSchema == NULL || tableName == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    if (tableSchema->tablecolMetas != NULL)
    {
        return GNCDB_SUCCESS;  // 如果已经存在ColMeta，则不需要重新创建
    }

    /* 创建表的ColMeta */
    tableSchema->tablecolMetas = varArrayListCreate(0, BYTES_POINTER, 0, NULL, MetaPtrDestroy);
    if (tableSchema->tablecolMetas == NULL)
    {
        return GNCDB_MEM;
    }

    colMeta = (ColMeta *)my_malloc0(sizeof(ColMeta));
    if (colMeta == NULL)
    {
        return GNCDB_MEM;
    }
    colMeta->type = NormalField;

    memptr = my_malloc0(strlen(tableName) + 1);
    if (memptr == NULL)
    {
        return GNCDB_MEM;
    }
    strcpy(memptr, tableName);
    colMeta->tableName = memptr;
    memptr             = my_malloc0(strlen("__bitMap") + 1);
    if (memptr == NULL)
    {
        return GNCDB_MEM;
    }
    strcpy(memptr, "__bitMap");
    colMeta->name     = memptr;
    colMeta->len      = GET_BITMAP_LENGTH(tableSchema->columnList->elementCount);
    colMeta->offset   = offset;
    colMeta->isBitMap = true;
    colMeta->owned    = true;
    colMeta->index    = (-1);  // 该字段在ColMeta数组中的位置，便于后续处理
    offset += colMeta->len;
    varArrayListAddPointer(tableSchema->tablecolMetas, colMeta);

    for (int i = 0; i < tableSchema->columnList->elementCount; i++)
    {
        column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
        if (column == NULL)
        {
            return GNCDB_ARRAY_GET_FALSE;
        }
        colMeta = (ColMeta *)my_malloc0(sizeof(ColMeta));
        if (colMeta == NULL)
        {
            return GNCDB_MEM;
        }
        colMeta->tableName = my_strdup(tableName);
        colMeta->name      = my_strdup(column->fieldName);
        colMeta->fieldType = column->fieldType;
        if (column->fieldType == FIELDTYPE_INTEGER)
        {
            colMeta->len = sizeof(int);
        }
        else if (column->fieldType == FIELDTYPE_REAL)
        {
            colMeta->len = sizeof(double);
        }
        else if (column->fieldType == FIELDTYPE_VARCHAR)
        {
            colMeta->len = (int)column->columnConstraint->maxValue;
        }
        else if (column->fieldType == FIELDTYPE_BLOB)
        {
            // 暂时不支持其他类型
            // ColMetaDestroy(&colMeta);
            // return GNCDB_INTERNAL;
            colMeta->len = INT_SIZE * 2;  // BLOB类型的长度暂时设为INT_SIZE * 2
        }
        else
        {
            ColMetaDestroy(&colMeta);
            return GNCDB_INTERNAL;
        }
        colMeta->offset = offset;
        colMeta->index  = (i);
        offset += colMeta->len;
        colMeta->owned = true;
        varArrayListAddPointer(tableSchema->tablecolMetas, colMeta);
    }

    return rc;
}

/**
 * @description: 解析完master表得到表的所有信息之后，对索引内部信息进行二次处理，添加索引列信息和表主键信息
 * @param {Catalog*} catalog
 * @return {*}
 */
int postIndexProcess(GNCDB *db)
{
    Catalog *        catalog       = db->catalog;
    int              pkIndex       = 0;
    varArrayList *   pkIdxArray    = NULL;
    varArrayList *   pkCols        = NULL;
    TableSchema *    tableSchema   = NULL;
    TableSchema *    indexSchema   = NULL;
    Column *         column        = NULL;
    Column *         idxColumn     = NULL;
    Column *         idxColumnCopy = NULL;
    Column *         pkColumn      = NULL;
    int              indexColIdx   = 0;
    char *           fullIdxName   = NULL;
    HashIndex *      hashIndex     = NULL;
    HashMapIterator *iter          = NULL;

    LOG(LOG_TRACE, "SLOCKing:%s", "indexMap");
    WriteLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "indexMap");
    //* 遍历所有的索引 */
    iter = createHashMapIterator(catalog->indexMap);
    while (hasNextHashMapIterator(iter))
    {
        iter = nextHashMapIterator(iter);
        //* 获取索引和索引信息表信息 */
        fullIdxName = (char *)iter->entry->key;
        hashIndex   = (HashIndex *)iter->entry->value;
        indexSchema = getIndexSchema(catalog, fullIdxName);
        tableSchema = getTableSchema(catalog, hashIndex->tableName);

        //* 解析真正的indexSchema */
        for (int i = 0; i < indexSchema->columnList->elementCount; i++)
        {
            column        = (Column *)varArrayListGetPointer(indexSchema->columnList, i);
            indexColIdx   = tableSchemaGetIndex(tableSchema, column->fieldName);
            idxColumn     = varArrayListGetPointer(tableSchema->columnList, indexColIdx);
            idxColumnCopy = columnDeepCopy(idxColumn);
            varArrayListSetByIndexPointer(indexSchema->columnList, i, idxColumnCopy);
            columnDestroy(column);
        }
        hashIndex->index_columns = indexSchema->columnList;

        //* 解析索引的主键信息 */
        pkIdxArray = getPrimaryKeyIndex(catalog, hashIndex->tableName);
        pkCols     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        for (int i = 0; i < pkIdxArray->elementCount; i++)
        {
            pkIndex  = *(int *)varArrayListGet(pkIdxArray, i);
            pkColumn = (Column *)varArrayListGetPointer(tableSchema->columnList, pkIndex);
            varArrayListAddPointer(pkCols, pkColumn);
            switch (pkColumn->fieldType)
            {
                case FIELDTYPE_INTEGER:
                    hashIndex->primaryKeyLenth += INT_SIZE;
                    break;
                case FIELDTYPE_REAL:
                    hashIndex->primaryKeyLenth += DOUBLE_SIZE;
                    break;
                case FIELDTYPE_VARCHAR:
                    hashIndex->primaryKeyLenth += pkColumn->columnConstraint->maxValue;
                    break;
                default:
                    return GNCDB_FIELD_NOT_EXIST;
            }
        }
        hashIndex->pkCols = pkCols;
    }
    freeHashMapIterator(&iter);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "indexMap");
    WriteUnLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "indexMap");
    return GNCDB_SUCCESS;
}

/// <summary>
/// 解析master表
/// </summary>
/// <param name="db"></param>
/// <param name="masterCursor"></param>
/// <param name="masterTuple"></param>
/// <returns></returns>
// int parseMaster(GNCDB* db, BtreeCursor* masterCursor, Tuple* masterTuple, struct Transaction* tx)
int parseMaster(GNCDB *db, BtreeCursor *masterCursor, BYTE *masterRecord, struct Transaction *tx)
{
    struct Catalog *catalog     = NULL;
    int             rc          = 0;
    varArrayList *  columnArray = NULL;
    char            tableName[TABLENAME_FIELD_MAXLEN];
    int             type = 0;  // 0表示表，1表示索引
    char            dependTableName[TABLENAME_FIELD_MAXLEN];
    int             colNum                                      = 0;
    int             rootPageId                                  = 0;
    BtreeCursor *   schemaCursor                                = NULL;
    varArrayList *  indexArray                                  = NULL;
    varArrayList *  typeArray                                   = NULL;
    varArrayList *  keyOffsetArray                              = NULL;
    varArrayList *  keyVarcharLenArray                          = NULL;
    TableSchema *   tableSchema                                 = NULL;
    BtreeTable *    table                                       = NULL;
    HashIndex *     hashIndex                                   = NULL;
    int             rowNum                                      = 0;
    int             offset                                      = 0;
    int             masterId                                    = 0;
    char            fullIdxName[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};  // tabName.idxName + '\0'
    catalog                                                     = db->catalog;

    for (; masterRecord != NULL; masterRecord = btreeTableGetNextRecord(catalog->masterTable, masterCursor, db))
    {
        rowNum = 0;
        /* 遍历master为每个表构造tableschema */
        offset = GET_BITMAP_LENGTH(MASTERCOLNUM);
        /* 构造tableschema结构体的columnarray */
        columnArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, arrayColumnDestroy);
        if (columnArray == NULL)
        {
            return GNCDB_ARRAY_CREATE_FALSE;
        }
        /* master的tuple id */
        memcpy(&masterId, masterRecord + offset, INT_SIZE);
        offset += INT_SIZE;
        if (db->catalog->masterCurrentMaxId < masterId)
        {
            db->catalog->masterCurrentMaxId = masterId;  // 设置当前最大的id
        }

        /* 1 master的第二列 表名 */
        memcpy(tableName, masterRecord + offset, TABLENAME_FIELD_MAXLEN);
        offset += TABLENAME_FIELD_MAXLEN;

        /* 2 master的第三列 表类型 */
        memcpy(&type, masterRecord + offset, INT_SIZE);
        offset += INT_SIZE;

        /* 3 master的第四列 从属表 */
        memcpy(dependTableName, masterRecord + offset, TABLENAME_FIELD_MAXLEN);
        offset += TABLENAME_FIELD_MAXLEN;

        /* 4 master的第五列 列数 */
        memcpy(&colNum, masterRecord + offset, sizeof(int));
        offset += INT_SIZE;
        if (colNum < 0)
        {
            varArrayListDestroy(&columnArray);
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        /* 5 master的第六列 rootpageid */
        memcpy(&rootPageId, masterRecord + offset, sizeof(int));
        offset += INT_SIZE;
        if (rootPageId < 0)
        {
            varArrayListDestroy(&columnArray);
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }

        /* 构造遍历schema的cursor */
        schemaCursor = btreeCursorConstruct(SCHEMANAME, db, NULL, tx);
        if (schemaCursor == NULL)
        {
            varArrayListDestroy(&columnArray);
            return GNCDB_BTC_CREATE_FALSE;
        }
        indexArray = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
        if (indexArray == NULL)
        {
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return GNCDB_ARRAY_CREATE_FALSE;
        }
        typeArray = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
        if (typeArray == NULL)
        {
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return GNCDB_ARRAY_CREATE_FALSE;
        }
        keyOffsetArray = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
        if (keyOffsetArray == NULL)
        {
            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return GNCDB_ARRAY_CREATE_FALSE;
        }
        keyVarcharLenArray = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
        if (keyVarcharLenArray == NULL)
        {
            varArrayListDestroy(&keyOffsetArray);
            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return GNCDB_ARRAY_CREATE_FALSE;
        }
        /* 解析schema表获取column */
        rc = parseSchema(db,
            schemaCursor,
            columnArray,
            indexArray,
            typeArray,
            keyOffsetArray,
            keyVarcharLenArray,
            tableName,
            colNum);
        if (rc != GNCDB_SUCCESS)
        {
            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return rc;
        }

        if (rc != GNCDB_SUCCESS)
        {
            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return GNCDB_GET_TABLE_ROWNUM_FAILED;
        }

        /* 构造tableschema */
        tableSchema = tableSchemaConstruct(MAXCOLNUM * MAXINDEX * MAXTABLE, colNum, columnArray);
        // tableSchema->rowId = rowNum;
        if (tableSchema == NULL)
        {

            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return GNCDB_TABLESCHEMA_CREATE_FALSE;
        }

        // 索引特殊处理
        if (type == 1)
        {
            /* 生成完全索引名：tabName.idxName  */
            sprintf(fullIdxName, "%s.%s", dependTableName, tableName);
            /* indexschema存入map */
            LOG(LOG_TRACE, "SLOCKing:%s", "indexSchemaMap");
            WriteLock(&(catalog->indexSchemaLatch));
            LOG(LOG_TRACE, "SLOCKend:%s", "indexSchemaMap");
            hashMapPut(catalog->indexSchemaMap, fullIdxName, tableSchema);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "indexSchemaMap");
            WriteUnLock(&(catalog->indexSchemaLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "indexSchemaMap");

            /* 构造索引结构体 */
            hashIndex = hashIndexConstruct(rootPageId, NULL, dependTableName, tableName, catalog);
            if (hashIndex == NULL)
            {
                varArrayListDestroy(&typeArray);
                varArrayListDestroy(&indexArray);
                btreeCursorDestroy(&schemaCursor);
                varArrayListDestroy(&columnArray);
                tableSchemaDestroy(tableSchema);
                return GNCDB_INTERNAL;
            }
            /* index存入map */
            LOG(LOG_TRACE, "SLOCKing:%s", "indexMap");
            WriteLock(&(catalog->indexMapLatch));
            LOG(LOG_TRACE, "SLOCKend:%s", "indexMap");
            hashMapPut(catalog->indexMap, fullIdxName, hashIndex);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "indexMap");
            WriteUnLock(&(catalog->indexMapLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "indexMap");

            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&indexArray);
            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&keyOffsetArray);
            varArrayListDestroy(&keyVarcharLenArray);
            continue;
        }

        /* 将tableschema存入map */
        LOG(LOG_TRACE, "SLOCKing:%s", "tableSchemaMap");
        WriteLock(&(catalog->tableSchemaLatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "tableSchemaMap");
        hashMapPut(catalog->tableSchemaMap, tableName, tableSchema);
        rc = setTableSchemaColMetas(tableSchema, tableName);
        if (rc != GNCDB_SUCCESS)
        {
            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            tableSchemaDestroy(tableSchema);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "tableSchemaMap");
            WriteUnLock(&(catalog->tableSchemaLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");
            return rc;
        }
        LOG(LOG_TRACE, "SUNLOCKing:%s", "tableSchemaMap");
        WriteUnLock(&(catalog->tableSchemaLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");
        /* 构造B+树 */
        table = btreeTableConstruct(tableName, rootPageId, tableSchema);
        if (table == NULL)
        {
            varArrayListDestroy(&typeArray);
            varArrayListDestroy(&indexArray);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&columnArray);
            return GNCDB_BTREETABLE_CREATE_FALSE;
        }
        /* 保存B+树 */
        LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
        WriteLock(&(catalog->tableMapLatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
        hashMapPut(catalog->tableMap, table->tableName, table);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
        WriteUnLock(&(catalog->tableMapLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");
        /* 保存主键下标 */
        LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
        WriteLock(&(catalog->keyIndexLatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
        hashMapPut(catalog->tablePrimaryKeyIndexMap, table->tableName, indexArray);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
        WriteUnLock(&(catalog->keyIndexLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");
        /* 保存主键类型 */
        LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
        WriteLock(&(catalog->keyTypeLatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
        hashMapPut(catalog->tablePrimaryKeyTypeMap, table->tableName, typeArray);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
        WriteUnLock(&(catalog->keyTypeLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");
        /* 保存主键偏移 */
        LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyOffsetMap");
        WriteLock(&(catalog->keyOffsetLatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyOffsetMap");
        hashMapPut(catalog->tablePrimaryKeyOffsetMap, table->tableName, keyOffsetArray);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyOffsetMap");
        WriteUnLock(&(catalog->keyOffsetLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyOffsetMap");
        /* 保存主键varchar长度 */
        LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
        WriteLock(&(catalog->keyVarcharLenLatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyVarcharLenMap");
        hashMapPut(catalog->tablePrimaryKeyVarcharLenMap, table->tableName, keyVarcharLenArray);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
        WriteUnLock(&(catalog->keyVarcharLenLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyVarcharLenMap");

        rc = btreeTableDfsTraversal(table, db);
        if (rc != GNCDB_SUCCESS)
        {
            return GNCDB_BTREEDFS_FALSE;
        }
        btreeCursorDestroy(&schemaCursor);
        // 获取表的行数
        rc = btreeTableGetRowNum(tableName, db, &rowNum);

        tableSchema->rowId = rowNum;
    }

    rc = postIndexProcess(db);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_BTREEDFS_FALSE;
    }

    return rc;
}
/// <summary>
/// 解析master和schema，为所有表生成对应TableSchema
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
int catalogParseMasterAndSchema(struct GNCDB *db, struct Transaction *tx)
{
    BtreeCursor *masterCursor = NULL;
    // Tuple* masterTuple = NULL;
    BYTE *masterRecord = NULL;
    int   total        = 0;
    int   rc           = 0;

    struct Catalog *catalog = db->catalog;
    total                   = db->totalPageNum;
    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");
    /* 从schema和master表中得到所有的tuple->根据tuple形成tableSchema */
    /* 构建master表的cursor遍历 */
    masterCursor = btreeCursorConstruct(MASTERNAME, db, NULL, tx);
    if (masterCursor == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_BTC_CREATE_FALSE;
    }
    // masterTuple = btreeTab/leGetNextTuple(catalog->masterTable, masterCursor, db);
    masterRecord = btreeTableGetNextRecord(catalog->masterTable, masterCursor, db);
    if (masterRecord == NULL)
    {
        /* 新建数据库没有系统表 */
        btreeCursorDestroy(&masterCursor);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_SUCCESS;
    }
    /* 解析master表 */
    rc = parseMaster(db, masterCursor, masterRecord, tx);

    btreeCursorDestroy(&masterCursor);
    db->totalPageNum = total;
    LOG(LOG_TRACE, "SUNLOCKing:%s", "catalog");
    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
    return rc;
}

int createSchemaRecord(Catalog *catalog, BYTE *record, Column *column, char *name, int columnNum)
{
    int    rc = 0;
    time_t SchemaCreateTime;
    time_t SchemaUpdateTime;
    int    SchemaCreateTimeInt   = 0;
    int    SchemaUpdateTimeInt   = 0;
    int    schemaCurrentMaxIncId = schemaCurrentMaxIncIdAdd(catalog);
    int    offset                = GET_BITMAP_LENGTH(SCHEMACOLNUM);

    /* 自增ID */
    rc = leafRecordAddIntField(record, &offset, schemaCurrentMaxIncId, 0);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列的属性名 */
    rc = leafRecordAddVarCharField(record, &offset, column->fieldName, TABLENAME_FIELD_MAXLEN, 1);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列的表名 */
    rc = leafRecordAddVarCharField(record, &offset, name, TABLENAME_FIELD_MAXLEN, 2);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列号 */
    rc = leafRecordAddIntField(record, &offset, columnNum, 3);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列的类型 int类型 占4字节 */
    rc = leafRecordAddIntField(record, &offset, column->fieldType, 4);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列是否可以为空 */
    rc = leafRecordAddIntField(record, &offset, column->columnConstraint->canBeNull, 5);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列允许的最小值 */
    rc = leafRecordAddRealField(record, &offset, &column->columnConstraint->minValue, 6);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列允许的最大值 */
    rc = leafRecordAddRealField(record, &offset, &column->columnConstraint->maxValue, 7);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 列是否为主键 */
    rc = leafRecordAddIntField(record, &offset, column->columnConstraint->isPrimaryKey, 8);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // columnIsPrimaryKeyIntField = intFieldConstruct(column->columnConstraint->isPrimaryKey);
    // if (columnIsPrimaryKeyIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(schemaTuple, (Field*)columnIsPrimaryKeyIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&columnIsPrimaryKeyIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    rc = leafRecordAddIntField(record, &offset, schemaCurrentMaxIncId, 9);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 创建表时的时间 */
    time(&SchemaCreateTime);
    SchemaCreateTimeInt = (int)SchemaCreateTime;
    rc                  = leafRecordAddIntField(record, &offset, SchemaCreateTimeInt, 10);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 修改表的时间 */
    time(&SchemaUpdateTime);
    SchemaUpdateTimeInt = (int)SchemaUpdateTime;
    rc                  = leafRecordAddIntField(record, &offset, SchemaUpdateTimeInt, 11);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    return rc;
}

/// <summary>
/// 创建表时将各个fieldname记录在schema中
/// </summary>
/// <param name="tableSchema"></param>
/// <param name="name"></param>
/// <param name="tid"></param>
/// <param name="db"></param>
/// <returns></returns>
int createTableToSchema(TableSchema *tableSchema, char *name, Transaction *tx, GNCDB *db)
{
    int rc = 0;
    int i  = 0;
    // Tuple* schemaTuple = NULL;
    Column *      column       = NULL;
    Catalog *     catalog      = NULL;
    varArrayList *columnList   = NULL;
    BYTE *        schemaRecord = NULL;
    int           bitmapLength = 0;

    catalog = db->catalog;
    /* 通过该表的tableSchema 向schema表中加入信息  */
    columnList   = tableSchema->columnList;
    schemaRecord = (BYTE *)my_malloc(catalog->schemaTable->leafRecordLength);

    if (schemaRecord == NULL)
    {
        return GNCDB_LEAFTUPLE_CREATE_FALSE;
    }

    bitmapLength = GET_BITMAP_LENGTH(SCHEMACOLNUM);

    for (i = 0; i < tableSchema->columnNum; i++)
    {
        memset(schemaRecord, 0, catalog->schemaTable->leafRecordLength);
        memset(schemaRecord, 0xff, bitmapLength);
        /* 创建一条记录 */
        // schemaTuple = leafTupleConstruct(SCHEMACOLNUM);
        // if (schemaTuple == NULL)
        // {

        // 	return GNCDB_LEAFTUPLE_CREATE_FALSE;
        // }

        column = (Column *)varArrayListGetPointer(columnList, i);
        if (column == NULL)
        {
            if (schemaRecord != NULL)
                my_free(schemaRecord);
            // leafTupleDestroy(&schemaTuple);
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        /* 通过循环构造schema的每一条记录 */
        // rc = createSchemaTuple(catalog, schemaTuple, column, name, i);
        rc = createSchemaRecord(catalog, schemaRecord, column, name, i);
        if (rc != GNCDB_SUCCESS)
        {
            // leafTupleDestroy(&schemaTuple);
            if (schemaRecord != NULL)
                my_free(schemaRecord);
            return rc;
        }
        // 使用btreeTable的insertTuple接口向schema表插入一条tuple
        rc = btreeTableInsertTuple(catalog->schemaTable, schemaRecord, catalog->schemaTableSchema, db, tx);
        if (rc != GNCDB_SUCCESS)
        {
            // leafTupleDestroy(&schemaTuple);
            if (schemaRecord != NULL)
                my_free(schemaRecord);
            return rc;
        }
    }
    if (schemaRecord != NULL)
        my_free(schemaRecord);
    return GNCDB_SUCCESS;
}

int createIndexToSchema(varArrayList *indexCols, char *indexName, Transaction *tx, GNCDB *db)
{
    int      rc           = 0;
    int      i            = 0;
    Column * column       = NULL;
    Catalog *catalog      = NULL;
    BYTE *   schemaRecord = NULL;
    int      bitmapLength = 0;

    catalog = db->catalog;
    /* 通过该表的tableSchema 向schema表中加入信息  */
    schemaRecord = (BYTE *)my_malloc(catalog->schemaTable->leafRecordLength);

    if (schemaRecord == NULL)
    {
        return GNCDB_LEAFTUPLE_CREATE_FALSE;
    }

    bitmapLength = GET_BITMAP_LENGTH(SCHEMACOLNUM);

    for (i = 0; i < indexCols->elementCount; i++)
    {
        memset(schemaRecord, 0, catalog->schemaTable->leafRecordLength);
        memset(schemaRecord, 0xff, bitmapLength);

        column = (Column *)varArrayListGetPointer(indexCols, i);
        if (column == NULL)
        {
            if (schemaRecord != NULL)
                my_free(schemaRecord);
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        /* 通过循环构造schema的每一条记录 */
        rc = createSchemaRecord(catalog, schemaRecord, column, indexName, i);
        if (rc != GNCDB_SUCCESS)
        {
            if (schemaRecord != NULL)
                my_free(schemaRecord);
            return rc;
        }
        // 使用btreeTable的insertTuple接口向schema表插入一条tuple
        rc = btreeTableInsertTuple(catalog->schemaTable, schemaRecord, catalog->schemaTableSchema, db, tx);
        if (rc != GNCDB_SUCCESS)
        {
            if (schemaRecord != NULL)
                my_free(schemaRecord);
            return rc;
        }
    }
    if (schemaRecord != NULL)
        my_free(schemaRecord);
    return GNCDB_SUCCESS;
}

/// <summary>
/// 创建一条mastertuple
/// </summary>
/// <param name="tableSchema"></param>
/// <param name="masterTuple"></param>
/// <param name="name"></param>
/// <param name="autoId"></param>
/// <param name="rootPageId"></param>
/// <returns></returns>
int createTableToMasterRecord(TableSchema *tableSchema, BYTE *masterRecord, char *name, int autoId, int rootPageId)
{
    int rc = 0;
    // VarCharField* tableNameVarCharField = NULL;
    // IntField* masterCurrentMaxIdIntField = NULL;
    // IntField* typeIntField = NULL;
    // VarCharField* dependentTableVarCharField = NULL;
    // IntField* columnNumIntField = NULL;
    // IntField* rootPageIdIntField = NULL;
    // IntField* deleteFlagIntField = NULL;
    // IntField* tableParamIntField = NULL;
    // VarCharField* sqlVarCharField = NULL;
    time_t    createTime;
    DT_UINT64 createTimeInt;
    // IntField* createTimeIntField = NULL;
    time_t updateTime;
    // IntField* updateTimeIntField = NULL;
    int   updateTimeInt                              = 0;
    int   offset                                     = GET_BITMAP_LENGTH(MASTERCOLNUM);
    char  tableName[TABLENAME_FIELD_MAXLEN + 1]      = {0};
    char  dependentTable[TABLENAME_FIELD_MAXLEN + 1] = {0};
    char  sqlname[TABLENAME_FIELD_MAXLEN + 1]        = {0};
    char *sql                                        = NULL;

    /* 自增Id */
    rc = leafRecordAddIntField(masterRecord, &offset, autoId, 0);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // masterCurrentMaxIdIntField = intFieldConstruct(autoId);
    // if (masterCurrentMaxIdIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)masterCurrentMaxIdIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&masterCurrentMaxIdIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 表名 */
    memcpy(tableName, name, strlen(name));
    rc = leafRecordAddVarCharField(masterRecord, &offset, tableName, TABLENAME_FIELD_MAXLEN, 1);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // tableNameVarCharField = varCharFieldConstruct(name);
    // if (tableNameVarCharField == NULL)
    // {
    // 	return GNCDB_VARCHARFIELD_CREATE_FALSE;
    // }

    // rc = leafTupleAddField(masterTuple, (Field*)tableNameVarCharField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	varCharFieldDestroy(&tableNameVarCharField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 表的类型 0代表是表，1代表是索引 */
    rc = leafRecordAddIntField(masterRecord, &offset, 0, 2);
    // typeIntField = intFieldConstruct(0);
    // if (typeIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)typeIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&typeIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 表的从属表 */
    memcpy(dependentTable, name, strlen(name));
    rc = leafRecordAddVarCharField(masterRecord, &offset, dependentTable, TABLENAME_FIELD_MAXLEN, 3);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // dependentTableVarCharField = varCharFieldConstruct(name);
    // if (dependentTableVarCharField == NULL)
    // {
    // 	return GNCDB_VARCHARFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)dependentTableVarCharField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	varCharFieldDestroy(&dependentTableVarCharField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 表共有多少列  */
    rc = leafRecordAddIntField(masterRecord, &offset, tableSchema->columnNum, 4);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // columnNumIntField = intFieldConstruct(tableSchema->columnNum);
    // if (columnNumIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)columnNumIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&columnNumIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 表的rootPage页号 */
    rc = leafRecordAddIntField(masterRecord, &offset, rootPageId, 5);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // rootPageIdIntField = intFieldConstruct(rootPageId);
    // if (rootPageIdIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)rootPageIdIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&rootPageIdIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 删除表的标记, 0表示正常, 1表示该表被设置为删除状态 */
    rc = leafRecordAddIntField(masterRecord, &offset, 0, 6);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // deleteFlagIntField = intFieldConstruct(0);
    // if (deleteFlagIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)deleteFlagIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&deleteFlagIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 表的类型 如果是index则为-1 */
    rc = leafRecordAddIntField(masterRecord, &offset, tableSchema->tableParam, 7);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // tableParamIntField = intFieldConstruct(tableSchema->tableParam);
    // if (rootPageIdIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)tableParamIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&tableParamIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 创建表的sql语句 */
    sql = catalogGetCreateTableSql(tableSchema, name);
    if (sql == NULL)
    {
        memcpy(sqlname, name, strlen(name));
        rc = leafRecordAddVarCharField(masterRecord, &offset, sqlname, MAX_CHARLEN_NUM, 8);
    }
    else
    {
        rc = leafRecordAddVarCharField(masterRecord, &offset, sql, MAX_CHARLEN_NUM, 8);
        my_free(sql);
    }

    // sqlVarCharField = varCharFieldConstruct(name);
    // if (sqlVarCharField == NULL)
    // {
    // 	return GNCDB_VARCHARFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)sqlVarCharField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	varCharFieldDestroy(&sqlVarCharField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    rc = leafRecordAddIntField(masterRecord, &offset, autoId, 0);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 创建表时的时间 */
    time(&createTime);
    createTimeInt = (DT_UINT64)createTime;
    rc            = leafRecordAddIntField(masterRecord, &offset, createTimeInt, 9);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // createTimeIntField = intFieldConstruct((int)createTimeInt);
    // if (createTimeIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)createTimeIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&createTimeIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    /* 修改表的时间 */
    time(&updateTime);
    // todo???
    updateTimeInt = (int)updateTime;
    rc            = leafRecordAddIntField(masterRecord, &offset, updateTimeInt, 10);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    // updateTimeIntField = intFieldConstruct(updateTimeInt);
    // if (updateTimeIntField == NULL)
    // {
    // 	return GNCDB_INTFIELD_CREATE_FALSE;
    // }
    // rc = leafTupleAddField(masterTuple, (Field*)updateTimeIntField);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	intFieldDestroy(&updateTimeIntField);
    // 	return GNCDB_LEAFTUPLE_ADD_FALSE;
    // }

    return rc;
}

/// <summary>
/// Catalog中添加一张表
/// </summary>
/// <param name="catalog"></param>
/// <param name="name">表名</param>
/// <param name="table">表的树</param>
/// <returns></returns>
int catalogAddTable(char *name, struct BtreeTable *table, struct Transaction *tx, struct GNCDB *db)
{
    int             rc           = 0;
    TableSchema *   tableSchema  = NULL;
    Tuple *         masterTuple  = NULL;
    struct Catalog *catalog      = NULL;
    BYTE *          masterRecord = NULL;
    int             bitmapLength = 0;
    catalog                      = db->catalog;

    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");

    /* 获取tableSchema */
    LOG(LOG_TRACE, "SLOCKing:%s", "tableSchemaMap");
    WriteLock(&(catalog->tableSchemaLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tableSchemaMap");
    tableSchema = (TableSchema *)hashMapGet(catalog->tableSchemaMap, name);
    if (tableSchema == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        WriteUnLock(&(catalog->tableSchemaLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");
        return GNCDB_TABLESCHEMA_CREATE_FALSE;
    }

    rc = setTableSchemaColMetas(tableSchema, name);
    if (rc != GNCDB_SUCCESS)
    {
        WriteUnLock(&(catalog->tableSchemaLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "tableSchemaMap");
    WriteUnLock(&(catalog->tableSchemaLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");

    masterRecord = (BYTE *)my_malloc(catalog->masterTable->leafRecordLength);
    if (masterRecord == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_SPACE_LACK;
    }

    bitmapLength = GET_BITMAP_LENGTH(MASTERCOLNUM);

    // 将0-bitmapLength-1位都置为1
    memset(masterRecord, 0xFF, bitmapLength);

    /* 创建一条mastertuple */
    // masterTuple = leafTupleConstruct(MASTERCOLNUM);
    // if (masterTuple == NULL)
    // {
    //     WriteUnLock(&(catalog->latch));
    //     LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
    // 	return GNCDB_LEAFTUPLE_CREATE_FALSE;
    // }

    /* 创建mastertuple中的数据 */
    rc = createTableToMasterRecord(tableSchema, masterRecord, name, masterCurrentMaxIdAdd(catalog), table->rootPageId);
    if (rc != GNCDB_SUCCESS)
    {
        leafTupleDestroy(&masterTuple);
        WriteUnLock(&(catalog->latch));
        my_free(masterRecord);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }
    rc = btreeTableInsertTuple(catalog->masterTable, masterRecord, catalog->masterTableSchema, db, tx);
    if (rc != GNCDB_SUCCESS)
    {
        // leafTupleDestroy(&masterTuple);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        my_free(masterRecord);
        return GNCDB_INSERTTTUPLE_FALSE;
    }
    my_free(masterRecord);

    /* 创建tableSchema中的tuple */
    rc = createTableToSchema(tableSchema, name, tx, db);
    if (rc != GNCDB_SUCCESS)
    {
        // leafTupleDestroy(&masterTuple);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }
    LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
    WriteLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
    hashMapPut(catalog->tableMap, table->tableName, table);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
    WriteUnLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "catalog");
    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");

    /* 增加 */
    return GNCDB_SUCCESS;
}

/// <summary>
/// Catalog中删除一张表
/// </summary>
/// <param name="name">表名</param>
/// <param name="tid"></param>
/// <param name="db"></param>
/// <returns></returns>
int catalogDeleteTable(char *name, struct Transaction *tx, struct GNCDB *db)
{
    int             rc           = 0;
    BtreeCursor *   schemaCursor = NULL;
    struct Catalog *catalog      = NULL;
    // Tuple* schemaTuple = NULL;
    BYTE *schemaRecord = NULL;
    // VarCharField* schemaField = NULL;
    varArrayList *schemaKeyValueArray = NULL;
    BtreeCursor * masterCursor        = NULL;
    // Tuple* masterTuple = NULL;
    BYTE *masterRecord = NULL;
    // VarCharField* field = NULL;
    varArrayList *keyValueArray = NULL;
    int *         updatedPageId = NULL;
    char          tableName[TABLENAME_FIELD_MAXLEN];

    catalog = db->catalog;
    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");

    /* 构造schema表的cursor */
    schemaCursor = btreeCursorConstruct(SCHEMANAME, db, NULL, tx);
    if (schemaCursor == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_BTC_CREATE_FALSE;
    }

    /* 用于更新cursor */
    updatedPageId = (int *)my_malloc(sizeof(int));
    if (updatedPageId == NULL)
    {
        btreeCursorDestroy(&schemaCursor);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_SPACE_LACK;
    }

    while (btreeTableHasNextTuple(schemaCursor))
    {
        /* debug */
        // printBtreeToFile(db->catalog->schemaTable, db, tx->id);
        // schemaTuple = btreeTableGetNextTuple(catalog->schemaTable, schemaCursor, db);
        schemaRecord = btreeTableGetNextRecord(catalog->schemaTable, schemaCursor, db);
        /* 循环遍历schema删除表的属性记录 */
        // schemaField = varArrayListGetPointer(schemaTuple->fieldArray, 2);
        memcpy(tableName, schemaRecord + INT_SIZE + 16, 16);
        // if (schemaField == NULL)
        // {
        // 	my_free(updatedPageId);
        // 	btreeCursorDestroy(&schemaCursor);
        //     WriteUnLock(&(catalog->latch));
        //     LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        // 	return GNCDB_NOT_FOUND;
        // }
        if (strcmp(name, tableName) != 0)
        {
            continue;
        }

        schemaKeyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (schemaKeyValueArray == NULL)
        {
            my_free(updatedPageId);
            btreeCursorDestroy(&schemaCursor);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_ARRAY_CREATE_FALSE;
        }
        rc = leafTupleGetKeyValue(
            schemaKeyValueArray, schemaRecord, catalog, catalog->schemaTableSchema, catalog->schemaTable->tableName);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(updatedPageId);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&schemaKeyValueArray);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return rc;
        }

        /* 删除操作 */
        *updatedPageId = -1;
        rc             = btreeTableDeleteTuple(
            catalog->schemaTable, schemaKeyValueArray, catalog->schemaTableSchema, db, tx, updatedPageId, NULL, -1);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(updatedPageId);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&schemaKeyValueArray);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            // my_free(updatedPageId);
            return rc;
        }

        /* cursor的更新 */
        if (*updatedPageId != -1 && schemaCursor->currentLeafPageId != *updatedPageId)
        {
            rc = updateCursor(schemaCursor, db, catalog->schemaTable->tableName, *updatedPageId);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(updatedPageId);
                btreeCursorDestroy(&schemaCursor);
                varArrayListDestroy(&schemaKeyValueArray);
                WriteUnLock(&(catalog->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
                return rc;
            }
        }
        schemaCursor->currentTupleIndex -= 1;
        varArrayListDestroy(&schemaKeyValueArray);
    }
    my_free(updatedPageId);
    btreeCursorDestroy(&schemaCursor);

    /* 构造master的cursor */
    masterCursor = btreeCursorConstruct(MASTERNAME, db, NULL, tx);
    if (masterCursor == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_BTC_CREATE_FALSE;
    }
    while (true)
    {
        masterRecord = btreeTableGetNextRecord(catalog->masterTable, masterCursor, db);
        // masterTuple = btreeTableGetNextTuple(catalog->masterTable, masterCursor, db);
        if (masterRecord == NULL)
        {
            btreeCursorDestroy(&masterCursor);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_MASTERTUPLE_NOT_FOUND;
        }
        memcpy(tableName, masterRecord + GET_BITMAP_LENGTH(MASTERCOLNUM) + INT_SIZE, 16);
        // field = varArrayListGetPointer(masterTuple->fieldArray, MASTERTWOCOLUMN);
        // if (field == NULL)
        // {
        // 	btreeCursorDestroy(&masterCursor);
        //     WriteUnLock(&(catalog->latch));
        //     LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        // 	return GNCDB_MASTERTUPLE_NOT_FOUND;
        // }
        if (strcmp(name, tableName) == 0)
        {
            break;
        }
    }

    keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    if (keyValueArray == NULL)
    {
        btreeCursorDestroy(&masterCursor);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    rc = leafTupleGetKeyValue(
        keyValueArray, masterRecord, catalog, catalog->masterTableSchema, catalog->masterTable->tableName);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&keyValueArray);
        btreeCursorDestroy(&masterCursor);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }
    /* 删除操作 */
    rc = btreeTableDeleteTuple(catalog->masterTable, keyValueArray, catalog->masterTableSchema, db, tx, NULL, NULL, -1);

    varArrayListDestroy(&keyValueArray);
    btreeCursorDestroy(&masterCursor);

    if (rc != GNCDB_SUCCESS)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
    WriteLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
    hashMapRemove(catalog->tableMap, name);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
    WriteUnLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");

    LOG(LOG_TRACE, "SLOCKing:%s", "tableSchemaMap");
    WriteLock(&(catalog->tableSchemaLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tableSchemaMap");
    hashMapRemove(catalog->tableSchemaMap, name);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tableSchemaMap");
    WriteUnLock(&(catalog->tableSchemaLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");

    LOG(LOG_TRACE, "SUNLOCKing:%s", "catalog");
    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
    return GNCDB_SUCCESS;
}

/// <summary>
/// 判断名为tablename的表是否存在
/// </summary>
/// <param name="catalog"></param>
/// <param name="tableName"></param>
/// <returns></returns>
int catalogIsTableExist(struct Catalog *catalog, char *tableName)
{
    bool rc = true;
    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");

    LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
    WriteLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
    rc = hashMapExists(catalog->tableMap, tableName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
    WriteUnLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");

    LOG(LOG_TRACE, "SUNLOCKing:%s", "catalog");
    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
    if (rc)
    {
        return GNCDB_SUCCESS;
    }
    else
    {
        return GNCDB_TABLE_NOT_FOUND;
    }
}

/// <summary>
/// INT       STRING           byte           STRING                INT           INT             INT         STRING
/// STRING      STRING "id"    "tableName"    "masterType"  "dependentTable"     "columnMaxNum"  "rootPageId"
/// "deleteFlag"      "SQL"    "createTime"   "updataTime"
/// </summary>
/// <param name="catalog"></param>
/// <returns></returns>
int catalogGetMasterTableSchema(struct Catalog *catalog)
{
    int               rc                       = 0;
    varArrayList *    columnList               = NULL;
    int               i                        = 0;
    int               index                    = 1;
    bool              masterExist              = false;
    ColumnConstraint *constraint               = NULL;
    Column *          column                   = NULL;
    varArrayList *    primaryKeyTypeArraylist  = NULL;
    varArrayList *    primaryKeyIndexlist      = NULL;
    varArrayList *    primaryKeyOffsetlist     = NULL;
    varArrayList *    primaryKeyVarcharLenlist = NULL;

    int colOffset = GET_BITMAP_LENGTH(MASTERCOLNUM);
    int offset    = colOffset + INT_SIZE;
    int length    = TABLENAME_FIELD_MAXLEN;

    int type = (int)FIELDTYPE_VARCHAR;
    struct ColumnInfo
    {
        char *    name;
        FieldType type;
        int       minVal;
        int       maxVal;
        int       isCanBeNull;
        int       isPrimaryKey;
    };
    struct ColumnInfo columnInfos[] = {
        {"id", FIELDTYPE_INTEGER, 0, MAXTABLE * MAXINDEX, 0, 0},
        {"tableName", FIELDTYPE_VARCHAR, 1, 16, 0, 1},
        {"type", FIELDTYPE_INTEGER, 0, 4, 0},
        {"dependentTable", FIELDTYPE_VARCHAR, 1, 16, 0, 0},
        {"columnMaxNum", FIELDTYPE_INTEGER, 1, 32, 0, 0},
        {"rootPageId", FIELDTYPE_INTEGER, 0, INT_MAX, 0, 0},
        {"deleteFlag", FIELDTYPE_INTEGER, 0, 1, 0, 0},
        {"tableParam", FIELDTYPE_INTEGER, 0, 3, 0, 0},
        {"sql", FIELDTYPE_VARCHAR, 0, 256, 0, 0},
        {"rowId", FIELDTYPE_INTEGER, 0, MAXTABLE * MAXINDEX, 0, 0},
        {"createTime", FIELDTYPE_INTEGER, INT_MIN, INT_MAX, 0, 0},
        {"updataTime", FIELDTYPE_INTEGER, INT_MIN, INT_MAX, 0, 0},
    };

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
    masterExist = hashMapExists(catalog->tablePrimaryKeyTypeMap, MASTERNAME);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteUnLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");

    if (masterExist)
    {
        return GNCDB_SUCCESS;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");

    /* 构造columnarray */
    columnList = varArrayListCreate(DISORDER, BYTES_POINTER, MASTERCOLNUM, NULL, arrayColumnDestroy);
    if (columnList == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_MEM;
    }

    for (i = 0; i < MASTERCOLNUM; i++)
    {
        /* 循环构造每个列的约束和内容 */
        constraint = columnConstraintConstruct(
            columnInfos[i].minVal, columnInfos[i].maxVal, columnInfos[i].isCanBeNull, columnInfos[i].isPrimaryKey);
        if (constraint == NULL)
        {
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            varArrayListDestroy(&columnList);
            return GNCDB_MEM;
        }
        column            = columnConstruct(columnInfos[i].type, columnInfos[i].name, constraint, colOffset);
        column->tableName = my_malloc0(strlen(MASTERNAME) + 1);
        strcpy(column->tableName, MASTERNAME);
        if (column->fieldType == FIELDTYPE_INTEGER)
            colOffset += INT_SIZE;
        else if (column->fieldType == FIELDTYPE_REAL)
            colOffset += DOUBLE_SIZE;
        else if (column->fieldType == FIELDTYPE_VARCHAR)
            colOffset += column->columnConstraint->maxValue;
        if (column == NULL)
        {
            columnConstraintDestroy(constraint);
            varArrayListDestroy(&columnList);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_COLUMN_CREATE_FALSE;
        }

        rc = varArrayListAddPointer(columnList, column);
        if (rc != GNCDB_SUCCESS)
        {
            columnDestroy(column);
            varArrayListDestroy(&columnList);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_ARRAY_ADD_FALSE;
        }
    }
    /* 构造master的tableSchema */
    catalog->masterTableSchema = tableSchemaConstruct(MAXTABLE * MAXINDEX, MASTERCOLNUM, columnList);
    if (catalog->masterTableSchema == NULL)
    {
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_TABLESCHEMA_CREATE_FALSE;
    }
    /* 构造主键类型array */
    primaryKeyTypeArraylist = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (primaryKeyTypeArraylist == NULL)
    {
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    /* 构造主键下标array */
    primaryKeyIndexlist = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (primaryKeyIndexlist == NULL)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    /* 构造主键偏移array */
    primaryKeyOffsetlist = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (primaryKeyOffsetlist == NULL)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    /* 构造主键varchar长度array */
    primaryKeyVarcharLenlist = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (primaryKeyVarcharLenlist == NULL)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarcharLenlist);
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }

    /* 第二列  表名为主键 */
    rc = varArrayListAdd(primaryKeyIndexlist, &index);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarcharLenlist);
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_ADD_FALSE;
    }
    rc = varArrayListAdd(primaryKeyOffsetlist, &offset);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarcharLenlist);
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_ADD_FALSE;
    }

    rc = varArrayListAdd(primaryKeyVarcharLenlist, &length);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarcharLenlist);
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_ADD_FALSE;
    }

    rc = varArrayListAdd(primaryKeyTypeArraylist, &type);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarcharLenlist);
        tableSchemaDestroy(catalog->masterTableSchema);
        varArrayListDestroy(&columnList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_ADD_FALSE;
    }
    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
    hashMapPut(catalog->tablePrimaryKeyIndexMap, MASTERNAME, primaryKeyIndexlist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteUnLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyOffsetMap");
    WriteLock(&(catalog->keyOffsetLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyOffsetMap");
    hashMapPut(catalog->tablePrimaryKeyOffsetMap, MASTERNAME, primaryKeyOffsetlist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyOffsetMap");
    WriteUnLock(&(catalog->keyOffsetLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyOffsetMap");

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
    WriteLock(&(catalog->keyVarcharLenLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyVarcharLenMap");
    hashMapPut(catalog->tablePrimaryKeyVarcharLenMap, MASTERNAME, primaryKeyVarcharLenlist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
    WriteUnLock(&(catalog->keyVarcharLenLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyVarcharLenMap");

    /* 保存主键类型 */
    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
    hashMapPut(catalog->tablePrimaryKeyTypeMap, MASTERNAME, primaryKeyTypeArraylist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteUnLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "catalog");
    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");

    return GNCDB_SUCCESS;
}

/// <summary>
/// INT    STRING        STRING         INT              INT             int         double       double          int
/// int       int
///"id"   "columnName"   "tableName"   "columnIndex"   "columnType"   "canbeNull"   "minValue"   "maxValue"
///"isPrimaryKEY"  "createTime"     "updataTime"
/// </summary>
/// <param name="catalog"></param>
/// <returns></returns>
int catalogGetSchemaTableSchema(struct Catalog *catalog)
{
    int               rc                       = 0;
    int               i                        = 0;
    int               index                    = 0;
    bool              schemaExist              = false;
    varArrayList *    arrayList                = NULL;
    ColumnConstraint *columnConstraint         = NULL;
    Column *          column                   = NULL;
    varArrayList *    primaryKeyTypeArraylist  = NULL;
    varArrayList *    primaryKeyIndexlist      = NULL;
    varArrayList *    primaryKeyOffsetlist     = NULL;
    varArrayList *    primaryKeyVarCharLenlist = NULL;
    int               type                     = (int)FIELDTYPE_INTEGER;
    int               offset                   = GET_BITMAP_LENGTH(SCHEMACOLNUM);
    int               colOffset                = GET_BITMAP_LENGTH(SCHEMACOLNUM);

    char *columnNames[]                      = {"id",
        "columnName",
        "tableName",
        "columnIndex",
        "columnType",
        "canbeNull",
        "minValue",
        "maxValue",
        "isPrimaryKey",
        "rowId",
        "createTime",
        "updateTime"};
    int   columnTypes[]                      = {FIELDTYPE_INTEGER,
        FIELDTYPE_VARCHAR,
        FIELDTYPE_VARCHAR,
        FIELDTYPE_INTEGER,
        FIELDTYPE_INTEGER,
        FIELDTYPE_INTEGER,
        FIELDTYPE_REAL,
        FIELDTYPE_REAL,
        FIELDTYPE_INTEGER,
        FIELDTYPE_INTEGER,
        FIELDTYPE_INTEGER,
        FIELDTYPE_INTEGER};
    int   columnConstraints[SCHEMACOLNUM][4] = {{0, MAXTABLE * MAXINDEX, 0, 1},
        {0, 16, 0, 0},
        {0, 16, 0, 0},
        {0, 32, 0, 0},
        {0, 4, 0, 0},
        {0, 1, 0, 0},
        {INT_MIN, INT_MAX, 0, 0},
        {INT_MIN, INT_MAX, 0, 0},
        {0, 1, 0, 0},
        {0, MAXTABLE * MAXINDEX, 0, 0},
        {INT_MIN, INT_MAX, 0, 0},
        {INT_MIN, INT_MAX, 0, 0}};

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
    schemaExist = hashMapExists(catalog->tablePrimaryKeyTypeMap, SCHEMANAME);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteUnLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");
    if (schemaExist)
    {
        return GNCDB_SUCCESS;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");
    /* 使用构造函数构建schemaTableSchema */
    /* 构造columnarray */
    arrayList = varArrayListCreate(DISORDER, BYTES_POINTER, SCHEMACOLNUM, NULL, arrayColumnDestroy);
    if (arrayList == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }

    for (i = 0; i < SCHEMACOLNUM; i++)
    {
        /* 循环构造列约束 */
        columnConstraint = columnConstraintConstruct(
            columnConstraints[i][0], columnConstraints[i][1], columnConstraints[i][2], columnConstraints[i][3]);
        if (columnConstraint == NULL)
        {
            varArrayListDestroy(&arrayList);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_COLUMNCONSTRAINT_CREATE_FALSE;
        }
        /* 循环构造列数据 */
        column = columnConstruct(columnTypes[i], columnNames[i], columnConstraint, colOffset);
        if (column == NULL)
        {

            columnConstraintDestroy(columnConstraint);
            varArrayListDestroy(&arrayList);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_COLUMN_CREATE_FALSE;
        }
        column->tableName = my_malloc0(strlen(SCHEMANAME) + 1);
        strcpy(column->tableName, SCHEMANAME);
        rc = varArrayListAddPointer(arrayList, column);
        if (column->fieldType == FIELDTYPE_INTEGER)
            colOffset += INT_SIZE;
        else if (column->fieldType == FIELDTYPE_REAL)
            colOffset += DOUBLE_SIZE;
        else if (column->fieldType == FIELDTYPE_VARCHAR)
            colOffset += column->columnConstraint->maxValue;
        if (rc != GNCDB_SUCCESS)
        {
            columnDestroy(column);
            varArrayListDestroy(&arrayList);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_ARRAY_ADD_FALSE;
        }
    }
    /* 构造tableSchema */
    catalog->schemaTableSchema =
        tableSchemaConstruct(MAXTABLE * MAXINDEX + MAXTABLE * MAXCOLNUM, SCHEMACOLNUM, arrayList);
    if (catalog->schemaTableSchema == NULL)
    {
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_TABLESCHEMA_CREATE_FALSE;
    }
    /* 构造主键类型array */
    primaryKeyTypeArraylist = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
    if (primaryKeyTypeArraylist == NULL)
    {
        tableSchemaDestroy(catalog->schemaTableSchema);
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    /* 构造主键下标索引 */
    primaryKeyIndexlist = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
    if (primaryKeyIndexlist == NULL)
    {
        varArrayListDestroy(&primaryKeyTypeArraylist);
        tableSchemaDestroy(catalog->schemaTableSchema);
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    /*构造主键偏移*/
    primaryKeyOffsetlist = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
    if (primaryKeyOffsetlist == NULL)
    {
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyIndexlist);
        tableSchemaDestroy(catalog->schemaTableSchema);
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    /*构造主键varchar长度*/
    primaryKeyVarCharLenlist = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
    if (primaryKeyVarCharLenlist == NULL)
    {
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        tableSchemaDestroy(catalog->schemaTableSchema);
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_CREATE_FALSE;
    }

    rc = varArrayListAdd(primaryKeyIndexlist, &index);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarCharLenlist);
        tableSchemaDestroy(catalog->schemaTableSchema);
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_ADD_FALSE;
    }

    rc = varArrayListAdd(primaryKeyTypeArraylist, &type);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarCharLenlist);
        tableSchemaDestroy(catalog->schemaTableSchema);
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_ADD_FALSE;
    }
    rc = varArrayListAdd(primaryKeyOffsetlist, &offset);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&primaryKeyIndexlist);
        varArrayListDestroy(&primaryKeyTypeArraylist);
        varArrayListDestroy(&primaryKeyOffsetlist);
        varArrayListDestroy(&primaryKeyVarCharLenlist);
        tableSchemaDestroy(catalog->schemaTableSchema);
        varArrayListDestroy(&arrayList);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_ARRAY_ADD_FALSE;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
    hashMapPut(catalog->tablePrimaryKeyIndexMap, SCHEMANAME, primaryKeyIndexlist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
    WriteUnLock(&(catalog->keyIndexLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyOffsetMap");
    WriteLock(&(catalog->keyOffsetLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyOffsetMap");
    hashMapPut(catalog->tablePrimaryKeyOffsetMap, SCHEMANAME, primaryKeyOffsetlist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyOffsetMap");
    WriteUnLock(&(catalog->keyOffsetLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyOffsetMap");

    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyVarCharLenMap");
    WriteLock(&(catalog->keyVarcharLenLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyVarCharLenMap");
    hashMapPut(catalog->tablePrimaryKeyVarcharLenMap, SCHEMANAME, primaryKeyVarCharLenlist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyVarCharLenMap");
    WriteUnLock(&(catalog->keyVarcharLenLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyVarCharLenMap");

    /* 保存主键类型 */
    LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
    hashMapPut(catalog->tablePrimaryKeyTypeMap, SCHEMANAME, primaryKeyTypeArraylist);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
    WriteUnLock(&(catalog->keyTypeLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "catalog");
    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
    return GNCDB_SUCCESS;
}
/// <summary>
/// 获取一张表
/// </summary>
/// <param name="catalog"></param>
/// <param name="btreeTable">保存获取到的表</param>
/// <param name="name">表名</param>
/// <returns></returns>
int catalogGetTable(struct Catalog *catalog, struct BtreeTable **btreeTable, char *name)
{
    if (strcmp(name, MASTERNAME) == 0)
    {
        *btreeTable = catalog->masterTable;
        return GNCDB_SUCCESS;
    }
    else if (strcmp(name, SCHEMANAME) == 0)
    {
        *btreeTable = catalog->schemaTable;
        return GNCDB_SUCCESS;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
    WriteLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
    *btreeTable = (BtreeTable *)hashMapGet(catalog->tableMap, name);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
    WriteUnLock(&(catalog->tableMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");

    if (*btreeTable == NULL)
    {
        return GNCDB_TABLE_NOT_FOUND;
    }
    else
    {
        return GNCDB_SUCCESS;
    }
}
/// <summary>
/// 获取一张索引
/// </summary>
/// <param name="catalog"></param>
/// <param name="btreeTable">保存获取到的表</param>
/// <param name="name">表名</param>
/// <returns></returns>
// int catalogGetRtreeTable(struct Catalog* catalog, struct RtreeTable** rtreeTable, char* name)
// {

//     LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
//     WriteLock(&(catalog->indexMapLatch));
//     LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
//     *rtreeTable = (RtreeTable*)hashMapGet(catalog->indexMap, name);
//     LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
//     WriteUnLock(&(catalog->indexMapLatch));
//     LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");

// 	if (*rtreeTable == NULL)
// 	{
// 		return GNCDB_TABLE_NOT_FOUND;
// 	}
// 	else
// 	{
// 		return GNCDB_SUCCESS;
// 	}
// }
/// <summary>
/// 合并两个表的TableSchema
/// </summary>
/// <param name="ts1"></param>
/// <param name="ts2"></param>
/// <param name="ts3"></param>
/// <returns></returns>
int tableSchemaMerge(
    TableSchema **ts1, TableSchema *ts2, TableSchema *ts3, BtreeTable *btreeTable, varArrayList *joinColumOffsetArray)
{

    int           rc         = 0;
    int           i          = 0;
    varArrayList *columnList = NULL;
    Column *      column2    = NULL;
    Column *      column3    = NULL;
    int           columNum   = ts2->columnNum + ts3->columnNum;
    int           offset     = btreeTable->leafRecordLength;

    columnList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    if (columnList == NULL)
    {
        return GNCDB_ARRAY_CREATE_FALSE;
    }
    *ts1 = tableSchemaConstruct(ts2->maxRowNum + ts3->maxRowNum, columNum, columnList);
    if (rc != GNCDB_SUCCESS)
    {
        varArrayListDestroy(&columnList);
        return GNCDB_TABLESCHEMA_CREATE_FALSE;
    }
    for (i = 0; i < ts2->columnList->elementCount; i++)
    {
        column2 = (Column *)varArrayListGetPointer(ts2->columnList, i);
        if (column2 == NULL)
        {
            tableSchemaDestroy((*ts1));
            return GNCDB_COLUMN_CREATE_FALSE;
        }
        rc = varArrayListAddPointer((*ts1)->columnList, column2);
        if (rc != GNCDB_SUCCESS)
        {
            tableSchemaDestroy((*ts1));
            return GNCDB_ARRAY_ADD_FALSE;
        }
        // char** name = ArrayList_Get((*ts1)->columnList, i);
        rc = varArrayListAdd(joinColumOffsetArray, &column2->offset);
        if (rc != GNCDB_SUCCESS)
        {
            tableSchemaDestroy((*ts1));
            return GNCDB_ARRAY_ADD_FALSE;
        }
    }
    for (i = 0; i < ts3->columnList->elementCount; i++)
    {
        column3 = (Column *)varArrayListGetPointer(ts3->columnList, i);
        if (column3 == NULL)
        {
            tableSchemaDestroy((*ts1));
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        rc = varArrayListAddPointer((*ts1)->columnList, column3);
        if (rc != GNCDB_SUCCESS)
        {
            tableSchemaDestroy((*ts1));
            return GNCDB_ARRAY_ADD_FALSE;
        }
        offset = column3->offset + btreeTable->leafRecordLength;
        rc     = varArrayListAdd(joinColumOffsetArray, &offset);
        if (rc != GNCDB_SUCCESS)
        {
            tableSchemaDestroy((*ts1));
            return GNCDB_ARRAY_ADD_FALSE;
        }
    }
    return GNCDB_SUCCESS;
}

/// <summary>
/// 判断属性是否存在
/// </summary>
/// <param name="array">columnList</param>
/// <param name="attrName">属性名</param>
/// <returns>成功返回GNCDB_SUCCESS，否则返回错误代码</returns>
int columnFindFieldExist(varArrayList *array, char *fieldName)
{
    int     i      = 0;
    Column *column = NULL;

    for (i = 0; i < array->elementCount; ++i)
    {
        column = varArrayListGetPointer(array, i);
        if (column == NULL)
        {
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        if (!strcmp(column->fieldName, fieldName))
        {
            return GNCDB_SUCCESS;
        }
    }
    return GNCDB_FIELD_NOT_EXIST;
}

/// <summary>
/// 通过属性在array中寻找属性的下标
/// </summary>
/// <param name="array">columnList</param>
/// <param name="attrName">属性名</param>
/// <returns>找到返回下标，否则返回状态码</returns>
int columnFindFieldGet(varArrayList *array, char *fieldName)
{
    int     i      = 0;
    Column *column = NULL;
    for (i = 0; i < array->elementCount; ++i)
    {
        column = varArrayListGetPointer(array, i);
        if (column == NULL)
        {
            return GNCDB_ARRAY_GETPOINTER_FALSE;
        }
        if (!strcmp(column->fieldName, fieldName))
        {
            return i;
        }
    }
    return GNCDB_FIELD_NOT_EXIST;
}
/// <summary>
/// 获取属性的下标
/// </summary>
/// <param name="tableSchema">表的schema</param>
/// <param name="fieldName">属性名</param>
/// <returns>找到返回下标，否则返回状态码</returns>
int tableSchemaGetIndex(struct TableSchema *tableSchema, char *fieldName)
{
    int     i      = 0;
    Column *column = NULL;
    int     flag   = 0;

    for (i = 0; i < tableSchema->columnList->elementCount; ++i)
    {
        column = varArrayListGetPointer(tableSchema->columnList, i);
        flag   = strcmp(fieldName, column->fieldName);
        if (!flag)
        {
            return i;
        }
    }
    return GNCDB_FIELD_NOT_EXIST;
}

int tableSchemaGetFieldType(struct TableSchema *tableSchema, char *fieldName)
{
    int     i      = 0;
    Column *column = NULL;
    int     flag   = 0;

    for (i = 0; i < tableSchema->columnList->elementCount; ++i)
    {
        column = varArrayListGetPointer(tableSchema->columnList, i);
        flag   = strcmp(fieldName, column->fieldName);
        if (!flag)
        {
            return column->fieldType;
        }
    }
    return FIELDTYPE_INVALID;
}

/// <summary>
/// 从master表中获得指定表所拥有的索引名list
/// </summary>
/// <param name="db"></param>
/// <param name="indexNameArray"></param>
/// <param name="tableName"></param>
/// <param name="tx"></param>
/// <returns></returns>
int catalogGetIndexName(struct GNCDB *db, struct varArrayList *indexNameArray, char *tableName, struct Transaction *tx)
{

    /* 变量声明 */
    int rc = 0;

    BtreeCursor *masterCursor = NULL;
    // Tuple* masterTuple = NULL;
    BYTE *masterRecord = NULL;
    // VarCharField** tableNameField = NULL;
    char tableNameValue[16];
    // VarCharField** indexNameField = NULL;
    char indexName[16];
    int  offset = GET_BITMAP_LENGTH(MASTERCOLNUM);

    /* 构造BtreeCursor对象 */
    masterCursor = btreeCursorConstruct(MASTERNAME, db, NULL, tx);
    if (masterCursor == NULL)
    {
        return GNCDB_MEM;
    }

    /* 循环遍历匹配条件的Tuple */
    while (btreeTableHasNextTuple(masterCursor))
    {
        // masterTuple = btreeTableGetNextTuple(db->catalog->masterTable, masterCursor, db);
        masterRecord = btreeTableGetNextRecord(db->catalog->masterTable, masterCursor, db);
        if (masterRecord == NULL)
        {
            btreeCursorDestroy(&masterCursor);
            return GNCDB_MEM;
        }

        /* 获取tuple的第2列表名 */
        memcpy(tableNameValue, masterRecord + offset + INT_SIZE, TABLENAME_FIELD_MAXLEN);
        // tableNameField = (VarCharField**)varArrayListGet(masterTuple->fieldArray, 1);
        // if (tableNameField == NULL) {
        // 	btreeCursorDestroy(&masterCursor);
        // 	return GNCDB_MEM;
        // }
        /* 获取tuple的第3列dependTable */
        memcpy(indexName, masterRecord + offset + 2 * INT_SIZE + TABLENAME_FIELD_MAXLEN, TABLENAME_FIELD_MAXLEN);
        // indexNameField = (VarCharField**)varArrayListGet(masterTuple->fieldArray, 3);
        // if (indexNameField == NULL) {
        // 	btreeCursorDestroy(&masterCursor);
        // 	return GNCDB_MEM;
        // }
        /* 判断是否为指定表 */
        if (strcmp(tableNameValue, tableName) != 0)
            continue;
        /* 若相等则为表不是索引 */
        if (strcmp(tableNameValue, indexName) == 0)
            continue;
        rc = varArrayListAdd(indexNameArray, &(tableNameValue));
        if (rc != GNCDB_SUCCESS)
        {
            btreeCursorDestroy(&masterCursor);
            return rc;
        }
    }

    /* 释放资源 */
    btreeCursorDestroy(&masterCursor);

    return GNCDB_SUCCESS;
}

/// <summary>
/// 从master表中获得索引依赖的表名
/// </summary>
/// <param name="catalog"></param>
/// <param name="indexName"></param>
/// <param name="tid"></param>
/// <returns></returns>
int catalogGetDependentTableName(struct GNCDB *db, char *indexName, char **tableName, struct Transaction *tx)
{
    int          rc           = 0;
    BtreeCursor *masterCursor = NULL;
    BYTE *       masterRecord = NULL;
    char *       tableNameValue;

    masterCursor = btreeCursorConstruct(MASTERNAME, db, NULL, tx);
    while (btreeTableHasNextTuple(masterCursor))
    {
        masterRecord = btreeTableGetNextRecord(db->catalog->masterTable, masterCursor, db);
        if (masterRecord == NULL)
        {
            btreeCursorDestroy(&masterCursor);
            return GNCDB_MEM;
        }
        /* 获取tuple的第1列表名 */
        memcpy(indexName, masterRecord + GET_BITMAP_LENGTH(MASTERCOLNUM) + INT_SIZE, 16);
        if (strcmp(indexName, indexName) == 0)
        {
            /* 获取tuple的第4列表名 */
            tableNameValue = (char *)my_malloc(16);
            memcpy(tableNameValue, masterRecord + GET_BITMAP_LENGTH(MASTERCOLNUM) + 2 * INT_SIZE + 16, 16);
            *tableName = tableNameValue;
            if (*tableName == NULL)
            {
                btreeCursorDestroy(&masterCursor);
                return rc;
            }
        }
    }
    btreeCursorDestroy(&masterCursor);
    return GNCDB_SUCCESS;
}

/// <summary>
/// 获取哈希索引
/// </summary>
/// <param name="catalog">目录结构</param>
/// <param name="hashIndex">返回的哈希索引</param>
/// <param name="name">索引名称</param>
/// <returns>成功返回GNCDB_SUCCESS</returns>
int catalogGetHashIndex(struct Catalog *catalog, struct HashIndex **hashIndex, char *fullIdxName)
{
    bool exist = false;

    /* 参数检查 */
    if (catalog == NULL || hashIndex == NULL || fullIdxName == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "indexMap");
    WriteLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "indexMap");

    exist = hashMapExists(catalog->indexMap, fullIdxName);
    if (!exist)
    {
        WriteUnLock(&(catalog->indexMapLatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "indexMap");
        return GNCDB_INDEX_NOT_FOUND;
    }

    (*hashIndex) = (HashIndex *)hashMapGet(catalog->indexMap, fullIdxName);

    LOG(LOG_TRACE, "SUNLOCKing:%s", "indexMap");
    WriteUnLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "indexMap");

    if (*hashIndex == NULL)
    {
        return GNCDB_INDEX_NOT_FOUND;
    }

    return GNCDB_SUCCESS;
}

/**
 * @description: 获取指定表名上的哈希索引示例指针列表
 * @param {GNCDB*} db 数据库指针
 * @param {varArrayList*} indexList 索引列表List<HashIndex *>，需调用者预先分配内存
 * @param {char*} tableName 表名
 * @param {Transaction*} tx
 * @return {*}
 */
int catalogGetHashIndexList(GNCDB *db, varArrayList *indexList, char *tableName, Transaction *tx)
{
    // int rc = 0;
    Catalog *        catalog   = db->catalog;
    HashMapIterator *iter      = NULL;
    HashIndex *      hashIndex = NULL;

    //* 参数检查
    if (db == NULL || indexList == NULL || tableName == NULL || tx == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "indexMap");
    WriteLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "indexMap");

    iter = createHashMapIterator(catalog->indexMap);
    while (hasNextHashMapIterator(iter))
    {
        iter      = nextHashMapIterator(iter);
        hashIndex = iter->entry->value;
        if (strcmp(tableName, hashIndex->tableName) == 0)
        {
            varArrayListAddPointer(indexList, iter->entry->value);
        }
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "indexMap");
    WriteUnLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "indexMap");
    freeHashMapIterator(&iter);
    return GNCDB_SUCCESS;
}

/**
 * @description: 为索引创建master和schema记录
 * @param {char*} tableName
 * @param {char*} indexName
 * @param {HashIndex*} hashIndex
 * @param {Transaction*} tx
 * @param {GNCDB*} db
 * @return {*}
 */
int catalogAddHashIndex(char *fullIdxName, HashIndex *hashIndex, struct Transaction *tx, struct GNCDB *db)
{
    int      rc           = 0;
    Catalog *catalog      = NULL;
    BYTE *   masterRecord = NULL;
    int      bitmapLength = 0;

    catalog = db->catalog;
    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");

    masterRecord = (BYTE *)my_malloc0(catalog->masterTable->leafRecordLength);
    if (masterRecord == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_SPACE_LACK;
    }

    bitmapLength = GET_BITMAP_LENGTH(MASTERCOLNUM);
    // 将0-bitmapLength-1位都置为1
    memset(masterRecord, 0xFF, bitmapLength);

    /* 创建master记录 */
    rc = createHashIndexToMasterRecord(masterRecord, masterCurrentMaxIdAdd(catalog), hashIndex);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(masterRecord);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }

    /* 插入master表中 */
    rc = btreeTableInsertTuple(catalog->masterTable, masterRecord, catalog->masterTableSchema, db, tx);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(masterRecord);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }
    my_free(masterRecord);

    /* 创建schema中的tuple并插入 */
    rc = createIndexToSchema(hashIndex->index_columns, hashIndex->indexName, tx, db);
    if (rc != GNCDB_SUCCESS)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return rc;
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "indexSchemaMap");
    WriteLock(&(db->catalog->indexMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "indexSchemaMap");
    rc = hashMapPut(db->catalog->indexMap, fullIdxName, hashIndex);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "indexSchemaMap");
    WriteUnLock(&(db->catalog->indexMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "indexSchemaMap");

    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");

    return GNCDB_SUCCESS;
}

/// <summary>
/// 从目录中删除哈希索引
/// </summary>
/// <param name="indexName">索引名</param>
/// <param name="tableName">表名</param>
/// <param name="tx">事务</param>
/// <param name="db">数据库实例</param>
/// <returns>成功返回GNCDB_SUCCESS，失败返回错误码</returns>
int catalogDeleteHashIndex(char *fullIdxName, HashIndex *hashIndex, struct Transaction *tx, struct GNCDB *db)
{
    int           rc                  = 0;
    int           type                = 0;
    int           offset              = 0;
    BtreeCursor * schemaCursor        = NULL;
    Catalog *     catalog             = NULL;
    BYTE *        schemaRecord        = NULL;
    varArrayList *schemaKeyValueArray = NULL;
    BtreeCursor * masterCursor        = NULL;
    BYTE *        masterRecord        = NULL;
    varArrayList *keyValueArray       = NULL;
    int *         updatedPageId       = NULL;
    char *        idxName;
    char *        tblName;

    /* 参数检查 */
    if (hashIndex == NULL || tx == NULL || db == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    catalog = db->catalog;
    LOG(LOG_TRACE, "SLOCKing:%s", "catalog");
    WriteLock(&(catalog->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "catalog");

    /* 构造schema表的cursor */
    schemaCursor = btreeCursorConstruct(SCHEMANAME, db, NULL, tx);
    if (schemaCursor == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_BTC_CREATE_FALSE;
    }

    /* 用于更新cursor */
    updatedPageId = (int *)my_malloc(sizeof(int));
    if (updatedPageId == NULL)
    {
        btreeCursorDestroy(&schemaCursor);
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_SPACE_LACK;
    }

    /* 遍历schema表，删除索引相关记录 */
    while (btreeTableHasNextTuple(schemaCursor))
    {
        schemaRecord = btreeTableGetNextRecord(catalog->schemaTable, schemaCursor, db);
        idxName      = (char *)schemaRecord + GET_BITMAP_LENGTH(SCHEMACOLNUM) + INT_SIZE + 16;
        if (strcmp(idxName, hashIndex->indexName) != 0)
        {
            continue;
        }
        schemaKeyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (schemaKeyValueArray == NULL)
        {
            my_free(updatedPageId);
            btreeCursorDestroy(&schemaCursor);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_ARRAY_CREATE_FALSE;
        }
        rc = leafTupleGetKeyValue(schemaKeyValueArray, schemaRecord, catalog, catalog->schemaTableSchema, SCHEMANAME);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(updatedPageId);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&schemaKeyValueArray);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return rc;
        }

        /* 删除操作 */
        *updatedPageId = -1;
        rc             = btreeTableDeleteTuple(
            catalog->schemaTable, schemaKeyValueArray, catalog->schemaTableSchema, db, tx, updatedPageId, NULL, -1);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(updatedPageId);
            btreeCursorDestroy(&schemaCursor);
            varArrayListDestroy(&schemaKeyValueArray);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return rc;
        }

        /* cursor的更新 */
        if (*updatedPageId != -1 && schemaCursor->currentLeafPageId != *updatedPageId)
        {
            rc = updateCursor(schemaCursor, db, catalog->schemaTable->tableName, *updatedPageId);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(updatedPageId);
                btreeCursorDestroy(&schemaCursor);
                varArrayListDestroy(&schemaKeyValueArray);
                WriteUnLock(&(catalog->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
                return rc;
            }
        }
        schemaCursor->currentTupleIndex -= 1;
        varArrayListDestroy(&schemaKeyValueArray);
    }
    my_free(updatedPageId);
    btreeCursorDestroy(&schemaCursor);

    /* 构造master的cursor */
    masterCursor = btreeCursorConstruct(MASTERNAME, db, NULL, tx);
    if (masterCursor == NULL)
    {
        WriteUnLock(&(catalog->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
        return GNCDB_BTC_CREATE_FALSE;
    }
    /* 遍历master表，查找并删除索引记录 */
    while (btreeTableHasNextTuple(masterCursor))
    {
        offset       = GET_BITMAP_LENGTH(MASTERCOLNUM);
        masterRecord = btreeTableGetNextRecord(catalog->masterTable, masterCursor, db);
        if (masterRecord == NULL)
        {
            btreeCursorDestroy(&masterCursor);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_MASTERTUPLE_NOT_FOUND;
        }
        offset += INT_SIZE;
        /* 获取tuple的第2列表名 */
        idxName = (char *)(masterRecord + offset);
        offset += TABLENAME_FIELD_MAXLEN;
        /* 获取tuple的第3列type */
        type = *(int *)(masterRecord + offset);
        offset += INT_SIZE;
        /* 获取tuple的第4列dependTable */
        tblName = (char *)(masterRecord + offset);
        /* 检查是否为索引记录 */
        if (type != 1)
        {
            continue;
        }

        if (strcmp(idxName, hashIndex->indexName) != 0)
        {
            continue;
        }
        if (strcmp(tblName, hashIndex->tableName) != 0)
        {
            continue;
        }
        keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (keyValueArray == NULL)
        {
            btreeCursorDestroy(&masterCursor);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return GNCDB_ARRAY_CREATE_FALSE;
        }

        rc = leafTupleGetKeyValue(keyValueArray, masterRecord, catalog, catalog->masterTableSchema, MASTERNAME);
        if (rc != GNCDB_SUCCESS)
        {
            varArrayListDestroy(&keyValueArray);
            btreeCursorDestroy(&masterCursor);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return rc;
        }

        /* 删除操作 */
        rc = btreeTableDeleteTuple(
            catalog->masterTable, keyValueArray, catalog->masterTableSchema, db, tx, NULL, NULL, -1);
        varArrayListDestroy(&keyValueArray);

        if (rc != GNCDB_SUCCESS)
        {
            btreeCursorDestroy(&masterCursor);
            WriteUnLock(&(catalog->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");
            return rc;
        }

        break;
    }
    btreeCursorDestroy(&masterCursor);

    /* 从内存数据结构中删除哈希索引 */
    LOG(LOG_TRACE, "SLOCKing:%s", "indexMap");
    WriteLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "hashIndexMap");
    hashMapRemove(catalog->indexMap, fullIdxName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "hashIndexMap");
    WriteUnLock(&(catalog->indexMapLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "indexMapp");

    LOG(LOG_TRACE, "SLOCKing:%s", "indexSchemaMap");
    WriteLock(&(catalog->indexSchemaLatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "indexSchemaMap");
    hashMapRemove(catalog->indexSchemaMap, fullIdxName);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "indexSchemaMap");
    WriteUnLock(&(catalog->indexSchemaLatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "indexSchemaMap");

    /* 释放哈希索引资源 */
    if (hashIndex != NULL)
    {
        hashIndexDestroy(&hashIndex);
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "catalog");
    WriteUnLock(&(catalog->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "catalog");

    return GNCDB_SUCCESS;
}
/**
 * @brief 创建哈希索引的master记录
 * @param masterRecord master记录缓冲区
 * @param indexName 索引名称
 * @param tableName 表名
 * @param autoId 自增ID
 * @param metaPageId 元数据页ID
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int createHashIndexToMasterRecord(BYTE *masterRecord, int autoId, HashIndex *hashIndex)
{
    int       rc = 0;
    time_t    createTime;
    DT_UINT64 createTimeInt;
    time_t    updateTime;
    int       updateTimeInt = 0;
    int       offset        = GET_BITMAP_LENGTH(MASTERCOLNUM);

    char *sql = NULL;

    /* 自增Id */
    rc = leafRecordAddIntField(masterRecord, &offset, autoId, 0);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 表名 对于索引实际是索引名 */
    rc = leafRecordAddVarCharField(masterRecord, &offset, hashIndex->indexName, TABLENAME_FIELD_MAXLEN, 1);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 表的类型 0代表是表，1代表是索引 */
    rc = leafRecordAddIntField(masterRecord, &offset, 1, 2);

    /* 表的从属表 */
    rc = leafRecordAddVarCharField(masterRecord, &offset, hashIndex->tableName, TABLENAME_FIELD_MAXLEN, 3);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 表共有多少列  目前哈希索引只支持单列索引*/
    rc = leafRecordAddIntField(masterRecord, &offset, 1, 4);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 哈希索引元页页号 */
    rc = leafRecordAddIntField(masterRecord, &offset, hashIndex->meta_page_id, 5);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 删除表的标记, 0表示正常, 1表示该表被设置为删除状态 */
    rc = leafRecordAddIntField(masterRecord, &offset, 0, 6);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 表的类型 如果是index则为-1 */
    rc = leafRecordAddIntField(masterRecord, &offset, -1, 7);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 创建表的sql语句 */
    sql =
        catalogGetCreateIndexSql(hashIndex->index_columns, hashIndex->indexName, hashIndex->tableName, INDEX_TYPE_HASH);
    if (sql == NULL)
    {
        rc = leafRecordAddVarCharField(masterRecord, &offset, hashIndex->indexName, MAX_CHARLEN_NUM, 8);
    }
    else
    {
        rc = leafRecordAddVarCharField(masterRecord, &offset, sql, MAX_CHARLEN_NUM, 8);
        my_free(sql);
    }
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }
    /* 创建表时的时间 */
    time(&createTime);
    createTimeInt = (DT_UINT64)createTime;
    rc            = leafRecordAddIntField(masterRecord, &offset, createTimeInt, 9);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    /* 修改表的时间 */
    time(&updateTime);
    // todo???
    updateTimeInt = (int)updateTime;
    rc            = leafRecordAddIntField(masterRecord, &offset, updateTimeInt, 10);
    if (rc != GNCDB_SUCCESS)
    {
        return GNCDB_LEAFTUPLE_ADD_FALSE;
    }

    return rc;
}

void printColumnConstraint(const ColumnConstraint *cc)
{
    // printf("最小值: %f\n", cc->minValue);
    // printf("最大值: %f\n", cc->maxValue);
    // printf("可否为NULL: %s\n", cc->canBeNull ? "是" : "否");
    // printf("是否为主键: %s\n", cc->isPrimaryKey ? "是" : "否");
}

char *catalogIntToString(int num)
{
    char  buffer[20];  // 分配足够的缓冲区
    char *str = NULL;
    sprintf(buffer, "%d", num);  // 将整数格式化为字符串
    str = my_malloc(strlen(buffer) + 1);
    if (str == NULL)
    {
        return NULL;
    }
    strcpy(str, buffer);  // 将字符串复制到新分配的内存中
    return str;
}

char *catalogGetCreateTableSql(TableSchema *tableSchema, char *tableName)
{
    Column *column = NULL;
    int     length = 0;
    int     i      = 0;
    char *  sql    = NULL;
    char *  str    = NULL;
    char *  sqlLen = NULL;

    for (i = 0; i < tableSchema->columnNum - 3; ++i)
    {
        column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
        length += strlen(column->fieldName);
        length += 3;  // 空格 逗号 空格
        if (column->fieldType == FIELDTYPE_INTEGER)
        {
            length += 3;  // "int"
        }
        else if (column->fieldType == FIELDTYPE_REAL)
        {
            length += 5;  // "float"
        }
        else if (column->fieldType == FIELDTYPE_VARCHAR)
        {
            length += 6;  // "char()"
            length += snprintf(NULL, 0, "%d", (int)column->columnConstraint->maxValue);
        }
        else
        {
            length += 6;  // "unknown"
        }
    }
    length += 13;                 // "create table "
    length += strlen(tableName);  // tableName
    length += 1;                  // ;
    length += 3;

    sql = (char *)my_malloc0(length + 1);
    if (sql == NULL)
    {
        return NULL;
    }
    sql[0]      = '\0';
    sql[length] = '\0';

    strcat(sql, "create table ");
    strcat(sql, tableName);
    strcat(sql, "(");

    for (i = 0; i < tableSchema->columnNum - 3; ++i)
    {
        column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
        strcat(sql, column->fieldName);
        strcat(sql, " ");
        if (column->fieldType == FIELDTYPE_INTEGER)
        {
            strcat(sql, "int");
        }
        else if (column->fieldType == FIELDTYPE_REAL)
        {
            strcat(sql, "float");
        }
        else if (column->fieldType == FIELDTYPE_VARCHAR)
        {
            str = catalogIntToString(column->columnConstraint->maxValue);
            strcat(sql, "char(");
            strcat(sql, str);
            strcat(sql, ")");
            my_free(str);
        }
        else
        {
            strcat(sql, "noknown");
        }
        if (i != tableSchema->columnNum - 4)
        {
            strcat(sql, ", ");
        }
    }
    strcat(sql, ");");

    if (length >= MAX_CHARLEN_NUM)
    {
        sqlLen = my_malloc0(MAX_CHARLEN_NUM);
        if (sqlLen == NULL)
        {
            my_free(sql);
            return NULL;
        }
        memcpy(sqlLen, sql, MAX_CHARLEN_NUM - 1);
        sqlLen[MAX_CHARLEN_NUM - 1] = '\0';
        my_free(sql);
        return sqlLen;
    }

    return sql;
}

char *catalogGetCreateIndexSql(varArrayList *indexCols, char *indexName, char *tableName, IndexType indexType)
{
    Column *column = NULL;
    int     length = 0;
    int     i      = 0;
    char *  sqlStr = NULL;

    length += strlen("create index ");  // "create index "
    length += strlen(indexName) + 1;    // "indexName "
    length += strlen("on ");            // "on "
    length += strlen(tableName);        // "tableName"
    for (i = 0; i < indexCols->elementCount; ++i)
    {
        column = (Column *)varArrayListGetPointer(indexCols, i);
        length += strlen(column->fieldName) + 2;  // "fieldName, "
    }
    length += 2;                       // "()"
    length += strlen(" using hash;");  // " using hash;"
    length += 3;                       // 额外留的 防止意外

    sqlStr = (char *)my_malloc0(length + 1);
    if (sqlStr == NULL)
    {
        return NULL;
    }

    // create index idx_name on table_name(column1, column2, ...);
    strcat(sqlStr, "create index ");
    strcat(sqlStr, indexName);
    strcat(sqlStr, " on ");
    strcat(sqlStr, tableName);
    strcat(sqlStr, "(");

    for (i = 0; i < indexCols->elementCount; ++i)
    {
        column = (Column *)varArrayListGetPointer(indexCols, i);
        strcat(sqlStr, column->fieldName);

        if (i != indexCols->elementCount - 1)
        {
            strcat(sqlStr, ", ");
        }
    }
    if (indexType == INDEX_TYPE_HASH)
    {
        strcat(sqlStr, ") using hash;");
    }
    else if (indexType == INDEX_TYPE_BTREE)
    {
        strcat(sqlStr, ") using btree;");
    }
    else
    {
        strcat(sqlStr, ");");
    }
    return sqlStr;
}