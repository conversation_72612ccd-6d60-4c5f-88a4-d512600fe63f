/*
 * @Descripttion:
 * @version:
 * @Author: Alan
 * @Date: 2023-02-05 13:03:39
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-27 16:24:19
 */
#include "pagepool.h"
#include "btreepage.h"
#include "gncdbconstant.h"
#include "gncdb.h"
#include "typedefine.h"
#include "utils.h"
#include "lockmanager.h"
#include "list.h"
#include "hash.h"
#include <stdio.h>
#include <string.h>
#include "hashpage.h"

int pagePoolAddPageUnlock(Page *page, int pageId, struct GNCDB *db);
int pagePoolRemovePageUnlock(struct PagePool *pagePool, int pageId, struct Page *btreePage);
int setPageStatusDirtyUnlock(struct PagePool *pagePool, int pageId, PageStatus *pageStatus);
int pagePoolConstructNewBtreePageUnlock(struct BtreePage **page, struct PagePool *pagePool, struct GNCDB *db,
    PageType pageType, int pageId, char *tableName, struct Catalog *catalog);
int pagePoolConstructNewOverflowPageUnlock(
    struct OverflowPage **page, struct PagePool *pagePool, struct GNCDB *db, int pageId, int nextPageId);
int pagePoolConstructNewFreePageUnlock(
    struct FreePage **page, struct PagePool *pagePool, struct GNCDB *db, int pageId, int nextPageId);
int pagePoolAllocateNewPageUnlock(struct Page **page, struct PagePool *pagePool, struct GNCDB *db);
int pagePoolConstructNewMetaPageUnlock(
    struct MetaPage **page, struct PagePool *pagePool, struct GNCDB *db, int pageId, struct Catalog *catalog);
int pagePoolConstructNewBucketPageUnlock(struct BucketPage **page, struct PagePool *pagePool, struct GNCDB *db,
    int pageId, struct HashIndex *hashIndex, int bucketNumber);
int pagePoolConstructNewHashOverflowPageUnlock(struct HashOverflowPage **page, struct PagePool *pagePool,
    struct GNCDB *db, int pageId, struct HashIndex *hashIndex, int bucketNumber);

// static int pagePoolHashCode(HashMap *hashMap, void *key) {
// 	int x= *(int *)key;
// 	return (int)(x % hashMap->bucketNum);
// }

/// <summary>
/// 页状态的构造函数
/// </summary>
/// <returns></returns>
PageStatus *pageStatusConstruct(int pageID)
{
    PageStatus *pageStatus = NULL;
    pageStatus             = my_malloc(sizeof(PageStatus));
    if (pageStatus == NULL)
    {
        return NULL;
    }
    /* 初始化，由于页读入时一定不为脏页且不会被pin */
    pageStatus->dirty        = false;
    pageStatus->pin          = 0;
    pageStatus->accessRecord = 0;
    pageStatus->pageID       = pageID;
    pageStatus->pre          = NULL;
    pageStatus->next         = NULL;

    return pageStatus;
}

/**
 * @brief 修改页的 pre 指针
 * @param status
 * @param pre
 */
void setPageStatusPre(struct PageStatus *status, struct PageStatus *pre)
{
    status->pre = pre;
}

/**
 * @brief 修改页的 next 指针
 * @param status
 * @param next
 */
void setPageStatusNext(struct PageStatus *status, struct PageStatus *next)
{
    status->next = next;
}

/// <summary>
/// 页状态的销毁函数
/// </summary>
/// <param name="status">页状态指针</param>
void pageStatusDestroy(struct PageStatus *status)
{
    if (status == NULL)
    {
        return;
    }
    my_free(status);
}
/// <summary>
/// pagepoolFreeList的初始化函数，申请缓冲池空间以及初始化FreeList链表
/// </summary>
/// <param name="pStart">pagepool申请空间的起始地址</param>
/// <param name="pFree">pagepoolFreeList链表的起始页</param>
/// <param name="nFreeSlot">剩余freePage的个数</param>
/// <returns>状态码</returns>
int pagePoolFreeListInit(BYTE **pStart, struct Page **pFree, int *nFreeSlot, struct GNCDB *db)
{
    Page * pCur   = NULL;
    size_t offset = sizeof(allKindsPage) * (db->pagePoolCount);

    *pStart = (BYTE *)my_malloc(((db->pageCurrentSize) + sizeof(allKindsPage)) *
                                (db->pagePoolCount));  //缓冲池总空间为（每页大小+每个totalPage大小）*最大缓冲页数
    if (*pStart == NULL)
    {
        return GNCDB_MEM;
    }
    *nFreeSlot = (db->pagePoolCount);

    //初始化freeList
    for (int i = 0; i < (db->pagePoolCount); i++)
    {
        pCur        = (Page *)(*pStart + i * sizeof(allKindsPage));
        pCur->pData = *pStart + offset + i * (db->pageCurrentSize);

        pCur->pNext = *pFree;
        *pFree      = pCur;
    }
    return GNCDB_SUCCESS;
}
/// <summary>
/// pagepool的构造函数，初始化结构体中变量
/// </summary>
/// <param name="db">数据库句柄</param>
/// <returns>状态码</returns>
int pagePoolConstruct(struct GNCDB *db)
{
    int       rc       = GNCDB_SUCCESS;
    PagePool *pagePool = NULL;
    pagePool           = my_malloc(sizeof(PagePool));
    if (pagePool == NULL)
    {
        return GNCDB_MEM;
    }

    //初始化FreeList
    pagePool->pFree     = NULL;
    pagePool->nFreeSlot = 0;
    pagePool->pStart    = NULL;
    rc                  = pagePoolFreeListInit(&pagePool->pStart, &pagePool->pFree, &(pagePool->nFreeSlot), db);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolDestroy(pagePool);
        return rc;
    }

    /* 初始化锁 */
    ReadWriteLockInit(&(pagePool->latch));
    ReadWriteLockInit(&(pagePool->latchFlushMap));
    ReadWriteLockInit(&(pagePool->pageControlMaplatch));
    ReadWriteLockInit(&(pagePool->latchStatusMap));

    /* 缓冲池页管理map */
    pagePool->pageMap = hashMapCreate(INTKEY, 6000, NULL);
    if (pagePool->pageMap == NULL)
    {
        ReadWriteLockDestroy(&(pagePool->latch));
        ReadWriteLockDestroy(&(pagePool->latchFlushMap));
        ReadWriteLockDestroy(&(pagePool->pageControlMaplatch));
        ReadWriteLockDestroy(&(pagePool->latchStatusMap));
        my_free(pagePool);
        return GNCDB_MAP_CREATE_FALSE;
    }
    /* 缓冲池页状态map */
    pagePool->pageStatus = hashMapCreate(INTKEY, 6000, NULL);
    if (pagePool->pageStatus == NULL)
    {
        ReadWriteLockDestroy(&(pagePool->latch));
        ReadWriteLockDestroy(&(pagePool->latchFlushMap));
        ReadWriteLockDestroy(&(pagePool->pageControlMaplatch));
        ReadWriteLockDestroy(&(pagePool->latchStatusMap));
        hashMapDestroy(&(pagePool->pageMap));
        my_free(pagePool);
        return GNCDB_MAP_CREATE_FALSE;
    }
    pagePool->historyList      = dlist_create(NULL, NULL);
    pagePool->cacheList        = dlist_create(NULL, NULL);
    pagePool->cacheListUnpin   = dlist_create(NULL, NULL);
    pagePool->historyListUnpin = dlist_create(NULL, NULL);
    pagePool->pageControlMap   = hashMapCreate(INTKEY, 0, NULL);
    pagePool->flushMap         = hashMapCreate(INTKEY, 0, NULL);

    db->pagePool = pagePool;
    return GNCDB_SUCCESS;
}

/// <summary>
/// 销毁函数
/// </summary>
/// <param name="pagePool">pagepool</param>
void pagePoolDestroy(struct PagePool *pagePool)
{
    HashMapIterator *iterator        = NULL;
    PageControlNode *pageControlNode = NULL;
    Page *           page            = NULL;

    if (pagePool == NULL)
    {
        return;
    }

    ReadWriteLockDestroy(&(pagePool->latch));
    ReadWriteLockDestroy(&(pagePool->latchFlushMap));
    ReadWriteLockDestroy(&(pagePool->pageControlMaplatch));
    ReadWriteLockDestroy(&(pagePool->latchStatusMap));
    // printf("%lf\n", (double)pagePool->pageMap->sum /pagePool->pageMap->cnt);
    if (pagePool->pageMap != NULL)
    {
        iterator = createHashMapIterator(pagePool->pageMap);
        if (iterator == NULL)
        {
            return;
        }
        /* 循环遍历将page都free */
        while (hasNextHashMapIterator(iterator))
        {
            iterator = nextHashMapIterator(iterator);
            if (iterator == NULL)
            {
                return;
            }
            page = (Page *)iterator->entry->value;
            if (page == NULL)
                continue;
            if (page->pageType == LEAF_PAGE || page->pageType == INTERNAL_PAGE)
            {
                btreePageDestroy((BtreePage **)&page);
            }
            else if (page->pageType == FREE_PAGE)
            {
                freePageDestroy((FreePage **)&page);
            }
            else if (page->pageType == OVERFLOW_PAGE)
            {
                overflowPageDestroy((OverflowPage **)&page);
            }
        }
        freeHashMapIterator(&iterator);
        hashMapDestroy(&(pagePool->pageMap));
    }
    if (pagePool->pageStatus != NULL)
    {
        iterator = createHashMapIterator(pagePool->pageStatus);
        if (iterator == NULL)
        {
            return;
        }
        /* 循环遍历将pageStatus都free */
        while (hasNextHashMapIterator(iterator))
        {
            iterator = nextHashMapIterator(iterator);
            if (iterator == NULL)
            {
                return;
            }
            if (iterator->entry->value != NULL)
                pageStatusDestroy(iterator->entry->value);
        }
        freeHashMapIterator(&iterator);
        hashMapDestroy(&(pagePool->pageStatus));
    }
    if (pagePool->historyList != NULL)
    {
        dlist_destroy(pagePool->historyList);
    }
    if (pagePool->cacheList != NULL)
    {
        dlist_destroy(pagePool->cacheList);
    }
    if (pagePool->cacheListUnpin != NULL)
    {
        dlist_destroy(pagePool->cacheListUnpin);
    }
    if (pagePool->historyListUnpin != NULL)
    {
        dlist_destroy(pagePool->historyListUnpin);
    }

    LOG(LOG_TRACE, "SLOCKing:%s", "pageControlMap");
    WriteLock(&(pagePool->pageControlMaplatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pageControlMap");
    if (pagePool->pageControlMap->entryCount != 0)
    {
        iterator = createHashMapIterator(pagePool->pageControlMap);
        if (iterator == NULL)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pageControlMap");
            WriteUnLock(&(pagePool->pageControlMaplatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pageControlMap");
            return;
        }
        /* 循环遍历将node都free */
        while (hasNextHashMapIterator(iterator))
        {
            iterator = nextHashMapIterator(iterator);
            if (iterator == NULL)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pageControlMap");
                WriteUnLock(&(pagePool->pageControlMaplatch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pageControlMap");
                return;
            }
            pageControlNode = iterator->entry->value;
            ReadWriteLockDestroy(&(pageControlNode->latch));
            my_free(pageControlNode);
        }
        freeHashMapIterator(&iterator);
    }
    LOG(LOG_TRACE, "SUNLOCKing:%s", "pageControlMap");
    WriteUnLock(&(pagePool->pageControlMaplatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pageControlMap");

    if (pagePool->pageControlMap != NULL)
    {
        hashMapDestroy(&(pagePool->pageControlMap));
    }
    if (pagePool->flushMap != NULL)
    {
        hashMapDestroy(&(pagePool->flushMap));
    }
    my_free(pagePool->pStart);
    my_free(pagePool);
    return;
}

/// <summary>
/// 交换两个页的信息
/// </summary>
/// <param name="pagePool">缓冲池</param>
/// <param name="pageId1">页号1</param>
/// <param name="page1">页1</param>
/// <param name="status1">状态1</param>
/// <param name="pageId2">页号2</param>
/// <param name="page2">页2</param>
/// <param name="status2">状态2</param>
/// <returns></returns>
int pageExchange(struct PagePool *pagePool, int pageId1, struct Page *page1, int pageId2, struct Page *page2)
{
    Page *tpage = NULL;
    // PageStatus* status1 = NULL;
    // PageStatus* status2 = NULL;

    // printf("pageExchange:pageId1 : %d, pageId2 : %d\n", pageId1, pageId2);
    tpage = page1;
    page1 = page2;
    page2 = tpage;

    // status1 = getPageStatus(pagePool, pageId1);
    // status2 = getPageStatus(pagePool, pageId2);

    WriteLock(&pagePool->latch);
    // status1->pageID = pageId2;
    // status2->pageID = pageId1;

    hashMapPut(pagePool->pageMap, &pageId1, page1);
    // hashMapPut(pagePool->pageStatus, &pageId1, status2);

    hashMapPut(pagePool->pageMap, &pageId2, page2);
    // hashMapPut(pagePool->pageStatus, &pageId2, status1);
    WriteUnLock(&pagePool->latch);
    return GNCDB_SUCCESS;
}

/// <summary>
/// 获取pageStatus
/// </summary>
/// <param name="pagePool">pagePool</param>
/// <param name="pageId">页号</param>
/// <returns></returns>
PageStatus *getPageStatus(struct PagePool *pagePool, int pageId)
{
    PageStatus *status = NULL;
    if (pagePool == NULL)
    {
        return NULL;
    }
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    /* 从pagePool中获取对应页号的pageStatus */
    status = hashMapGet(pagePool->pageStatus, &pageId);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return status;
}

PageStatus *getPageStatusUnLock(struct PagePool *pagePool, int pageId)
{
    PageStatus *status = NULL;
    if (pagePool == NULL)
    {
        return NULL;
    }
    /* 从pagePool中获取对应页号的pageStatus */
    status = hashMapGet(pagePool->pageStatus, &pageId);
    return status;
}

/// <summary>
/// 将pageId对应页修改为脏页
/// </summary>
/// <param name="pagePool">pagePool</param>
/// <param name="pageId">页号</param>
/// <returns></returns>
int setPageStatusDirty(struct PagePool *pagePool, int pageId, PageStatus *pageStatus)
{
    PageStatus *status = NULL;
    if (pagePool == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    /* 从pagePool中获取对应页号的pageStatus */
    if (pageStatus == NULL)
        status = hashMapGet(pagePool->pageStatus, &pageId);
    else
        status = pageStatus;
    if (status == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_STATUS_NOT_FOUND;
    }
    /* 设置脏页 */
    status->dirty = true;

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

    return GNCDB_SUCCESS;
}

int setPageStatusDirtyUnlock(struct PagePool *pagePool, int pageId, PageStatus *pageStatus)
{
    PageStatus *status = NULL;
    if (pagePool == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    /* 从pagePool中获取对应页号的pageStatus */
    if (pageStatus == NULL)
        status = hashMapGet(pagePool->pageStatus, &pageId);
    else
        status = pageStatus;
    if (status == NULL)
    {
        return GNCDB_STATUS_NOT_FOUND;
    }
    /* 设置脏页 */
    status->dirty = true;
    return GNCDB_SUCCESS;
}

int setPageStatusClear(struct PagePool *pagePool, int pageId)
{
    PageStatus *status = NULL;
    if (pagePool == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    /* 从pagePool中获取对应页号的pageStatus */
    status = hashMapGet(pagePool->pageStatus, &pageId);
    if (status == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_STATUS_NOT_FOUND;
    }
    /* 设置脏页 */
    status->dirty = false;

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

    return GNCDB_SUCCESS;
}

/// <summary>
/// 将pageId对应的pin改成相应的状态，对于master表或者schema表必须被盯住，普通表的页根据需要自行设置
/// </summary>
/// <param name="pagePool">pagePool</param>
/// <param name="pageId">页号</param>
/// <returns></returns>
int setPageStatusPinUp(struct PagePool *pagePool, int pageId, PageStatus *status)
{
    PageStatus *pageStatus = NULL;

    if (pagePool == NULL || pageId <= 0)
    {
        return GNCDB_PARAMNULL;
    }
    /* 从pagePool中获取对应页号的pageStatus */
    if (status == NULL)
        pageStatus = hashMapGet(pagePool->pageStatus, &pageId);
    else
        pageStatus = status;
    if (pageStatus == NULL)
    {
        return GNCDB_STATUS_NOT_FOUND;
    }
    /* pin住 */
    pageStatus->pin++;
    if (pageStatus->pin == 1)
    {
        adjustLRU_Kpinset(pagePool, pageStatus->pageID, pageStatus);
    }

    return GNCDB_SUCCESS;
}
/// <summary>
/// 解pin
/// </summary>
/// <param name="pagePool"></param>
/// <param name="pageId"></param>
/// <returns></returns>
int setPageStatusPinDown(struct PagePool *pagePool, int pageId, PageStatus *status)
{
    PageStatus *pageStatus = NULL;
    if (pagePool == NULL || pageId == 0)
    {
        return GNCDB_PARAMNULL;
    }
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    if (status == NULL)
        pageStatus = hashMapGet(pagePool->pageStatus, &pageId);
    else
        pageStatus = status;
    if (pageStatus == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_STATUS_NOT_FOUND;
    }
    /* 解pin */
    pageStatus->pin--;
    if (pageStatus->pin == 0)
    {
        adjustLRU_Kunpinset(pagePool, pageStatus->pageID, pageStatus);
    }
    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return GNCDB_SUCCESS;
}

int adjustLRU_Kpinset(PagePool *pagePool, int pageId, PageStatus *pageStatus)
{
    int         rc     = 0;
    PageStatus *status = NULL;
    int         access = 0;
#if defined(DEBUG) && (DEBUG == 1)
    int num1 = 0;
    int num2 = 0;
#endif

    if (pageStatus == NULL)
        status = hashMapGet(pagePool->pageStatus, &pageId);
    else
        status = pageStatus;
    access = status->accessRecord;

    /*需保证如果页的pin为0时在对应的unpin队列，因此这里pin增加到1时需要移动到对应的pin队列*/
    if (access >= LRU_K)
    {
#if defined(DEBUG) && (DEBUG == 1)
        num1 = dlist_node_num(pagePool->cacheListUnpin);
        dlist_delete(pagePool->cacheListUnpin, status);
        num2 = dlist_node_num(pagePool->cacheListUnpin);
        if (num1 != num2 + 1)
        {
            printf("cacheListUnpin num not match\n");
        }
        if (dlist_node_num(pagePool->cacheListUnpin) != pagePool->cacheListUnpin->length)
        {
            printf("cacheListUnpin num not match\n");
        }

        num1 = dlist_node_num(pagePool->cacheList);
        dlist_append(pagePool->cacheList, status);
        num2 = dlist_node_num(pagePool->cacheList);
        if (num1 != num2 - 1)
        {
            printf("cacheList num not match\n");
        }
        if (dlist_node_num(pagePool->cacheList) != pagePool->cacheList->length)
        {
            printf("cacheList num not match\n");
        }
#else
        dlist_delete(pagePool->cacheListUnpin, status);
        dlist_append(pagePool->cacheList, status);
#endif
    }
    else
    {
#if defined(DEBUG) && (DEBUG == 1)
        num1 = dlist_node_num(pagePool->historyListUnpin);
        dlist_delete(pagePool->historyListUnpin, status);
        num2 = dlist_node_num(pagePool->historyListUnpin);
        if (num1 != num2 + 1)
        {
            printf("historyListUnpin num not match\n");
        }
        if (dlist_node_num(pagePool->historyListUnpin) != pagePool->historyListUnpin->length)
        {
            printf("historyListUnpin num not match\n");
        }

        num1 = dlist_node_num(pagePool->historyList);
        dlist_append(pagePool->historyList, status);
        num2 = dlist_node_num(pagePool->historyList);
        if (num1 != num2 - 1)
        {
            printf("historyList num not match\n");
        }
        if (dlist_node_num(pagePool->historyList) != pagePool->historyList->length)
        {
            printf("historyList num not match\n");
        }
#else
        dlist_delete(pagePool->historyListUnpin, status);
        dlist_append(pagePool->historyList, status);
#endif
    }
    return rc;
}

int adjustLRU_Kunpinset(PagePool *pagePool, int pageId, PageStatus *pageStatus)
{
    int         rc     = 0;
    PageStatus *status = NULL;
    int         access = 0;
#if defined(DEBUG) && (DEBUG == 1)
    int num1 = 0;
    int num2 = 0;
#endif

    if (pageStatus == NULL)
        status = hashMapGet(pagePool->pageStatus, &pageId);
    else
        status = pageStatus;
    access = status->accessRecord;

    /*需保证如果页的pin为0时在对应的unpin队列，因此这里pin增加到1时需要移动到对应的pin队列*/
    if (access >= LRU_K)
    {
#if defined(DEBUG) && (DEBUG == 1)
        num1 = dlist_node_num(pagePool->cacheList);
        dlist_delete(pagePool->cacheList, status);
        num2 = dlist_node_num(pagePool->cacheList);
        if (num1 != num2 + 1)
        {
            printf("cacheList num not match\n");
        }
        if (dlist_node_num(pagePool->cacheList) != pagePool->cacheList->length)
        {
            printf("cacheList num not match\n");
        }

        num1 = dlist_node_num(pagePool->cacheListUnpin);
        dlist_append(pagePool->cacheListUnpin, status);
        num2 = dlist_node_num(pagePool->cacheListUnpin);
        if (num1 != num2 - 1)
        {
            printf("cacheListUnpin num not match\n");
        }
        if (dlist_node_num(pagePool->cacheListUnpin) != pagePool->cacheListUnpin->length)
        {
            printf("cacheListUnpin num not match\n");
        }
#else
        dlist_delete(pagePool->cacheList, status);
        dlist_append(pagePool->cacheListUnpin, status);
#endif
    }
    else
    {
#if defined(DEBUG) && (DEBUG == 1)
        num1 = dlist_node_num(pagePool->historyList);
        dlist_delete(pagePool->historyList, status);
        num2 = dlist_node_num(pagePool->historyList);
        if (num1 != num2 + 1)
        {
            printf("historyList num not match\n");
        }
        if (dlist_node_num(pagePool->historyListUnpin) != pagePool->historyListUnpin->length)
        {
            printf("historyListUnpin num not match\n");
        }

        num1 = dlist_node_num(pagePool->historyListUnpin);
        dlist_append(pagePool->historyListUnpin, status);
        num2 = dlist_node_num(pagePool->historyListUnpin);
        if (num1 != num2 - 1)
        {
            printf("historyListUnpin num not match\n");
        }
        if (dlist_node_num(pagePool->historyList) != pagePool->historyList->length)
        {
            printf("historyList num not match\n");
        }
#else
        dlist_delete(pagePool->historyList, status);
        dlist_append(pagePool->historyListUnpin, status);
#endif
    }
    return rc;
}

/// <summary>
/// 撤销一个事务的页
/// </summary>
/// <param name="db"></param>
/// <param name="tid"></param>
/// <returns></returns>
int cancelTargetedPageofTransaction(struct GNCDB *db, Transaction *tx)
{
    int              rc       = 0;
    int *            pageId   = NULL;
    HashMapIterator *iterator = NULL;

    if (db == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    //根据oldPageData解pin
    if (tx->oldPageData->entryCount != 0)
    {
        iterator = createHashMapIterator(tx->oldPageData);
        if (iterator == NULL)
        {
            return GNCDB_MAP_ITERATOR_FALSE;
        }
        /* 循环遍历将每个页都解pin */
        while (hasNextHashMapIterator(iterator))
        {
            iterator = nextHashMapIterator(iterator);
            if (iterator == NULL)
            {
                freeHashMapIterator(&iterator);
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }
            pageId = (int *)iterator->entry->key;
            rc     = setPageStatusPinDown(db->pagePool, *pageId, NULL);
            if (rc != GNCDB_SUCCESS)
            {
                freeHashMapIterator(&iterator);
                return rc;
            }
        }
        freeHashMapIterator(&iterator);
    }

    return GNCDB_SUCCESS;
}

/// <summary>
/// freePage减少一个页
/// </summary>
/// <param name="db"></param>
/// <param name="pageId"></param>
/// <returns></returns>
int freePageNumReduce(struct GNCDB *db, int pageId)
{
    db->firstFreePid = pageId;
    return GNCDB_SUCCESS;
}

/// <summary>
/// 获取第一个freepage页
/// </summary>
/// <param name="db"></param>
/// <param name="firstFreePageId"></param>
/// <returns></returns>
int getFirstFreePageId(struct GNCDB *db, int *firstFreePageId, struct Transaction *tx, struct Page **tpage)
{
    int         pageId       = 0;
    int         prevPageId   = -1;
    Page *      page         = NULL;
    PageStatus *pageStatus   = NULL;
    Page *      prevPage     = NULL;
    FreePage *  freePage     = NULL;
    FreePage *  prevFreePage = NULL;
    int         rc           = -1;
    int         flag         = 0;
    pageId                   = db->firstFreePid;
    while (1)
    {
        if (pageId < 1)
        {
            /* 当前没有freepage或者所有freePage均被占有 */
            flag = 1;
            break;
        }
        pageStatus = getPageStatus(db->pagePool, pageId);
        if (pageStatus == NULL)
        {
            /* 当前页不在内存，则一定没有被其他事务使用 */
            break;
        }
        else if (pageStatus->pin <= 0)
        {
            /* 当前页没有被其他事务使用 */
            break;
        }
        /* 否则获取下一个freepage */
        rc = pagePoolGetPage(&page, pageId, NULL, db);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        freePage   = (FreePage *)page;
        prevPageId = pageId;
        pageId     = freePage->nextPageId;
        setPageStatusPinDown(db->pagePool, page->id, pageStatus);
    }
    if (flag)
    {
        /* 表明当前没有freepage或者所有freePage均被占有 不用更新freepage链表 */
        *firstFreePageId = pageId;
        return GNCDB_SUCCESS;
    }
    if (prevPageId == -1)
    {
        /* 表明第一个freepage获取成功 更新当前db firstFreePid 为下一个 */
        rc = pagePoolGetPage(&page, pageId, NULL, db);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        *tpage           = page;
        freePage         = (FreePage *)page;
        db->firstFreePid = freePage->nextPageId;
        setPageStatusPinDown(db->pagePool, page->id, pageStatus);
    }
    else
    {
        /* 拿取了链表中间节点的freepage 此时需要更新前一个freepage的next为当前获取的page的next */
        /* todo 当前的前一个freePage必定是已经删除但事务未提交的情况，此时修改是否导致其他问题未知 */
        rc = pagePoolGetPage(&page, pageId, NULL, db);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        rc = pagePoolGetPage(&prevPage, prevPageId, NULL, db);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        *tpage                   = page;
        freePage                 = (FreePage *)page;
        prevFreePage             = (FreePage *)prevPage;
        prevFreePage->nextPageId = freePage->nextPageId;
        setPageStatusPinDown(db->pagePool, page->id, NULL);
        setPageStatusPinDown(db->pagePool, prevPage->id, NULL);
    }

    *firstFreePageId = pageId;

    // printf("pagePoolGetPage  firstfreepage :%d\n", pageId);

    return GNCDB_SUCCESS;
}

/// <summary>
/// 获取一个页
/// </summary>
/// <param name="pagePool"></param>
/// <param name="page">保存获取到的页</param>
/// <param name="pageId">页号</param>
/// <param name="tableName"></param>
/// <param name="db"></param>
/// <returns></returns>
int pagePoolGetPage(struct Page **page, int pageId, char *tableName, struct GNCDB *db)
{
    int rc     = 0;
    int offset = 0;
    // int access = 0;
    char  pageType;
    Page *tpage     = NULL;
    BYTE *tpageData = NULL;
    // FreePage* freePage = NULL;
    // BtreePage* btreePage = NULL;
    // OverflowPage* overflowPage = NULL;
    struct TableSchema *tableSchema = NULL;
    PageStatus *        pageStatus  = NULL;
    struct PagePool *   pagePool    = NULL;
    pagePool                        = db->pagePool;
    if (db == NULL || pageId < 1)
    {
        return GNCDB_PARAMNULL;
    }
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    /* 从缓冲池中读取 */
    tpage = (Page *)hashMapGet(pagePool->pageMap, &pageId);
    if (tpage == NULL)
    {
        /* 从磁盘中读 */
        rc = pagePoolAllocateNewPageUnlock(&tpage, pagePool, db);
        if (tpage == NULL)
        {
            pagePoolWithdrawPage(pagePool, *page);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
        if (pageId == 1)
        {
            /* master特殊处理 */
            rc = osRead(db->dbFile, (db->pageCurrentSize), (db->pageCurrentSize), &(tpage->pData));
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_READ_FAILED;
            }
        }
        else
        {
            rc = osRead(db->dbFile, (long)(pageId - 1) * (db->pageCurrentSize), (db->pageCurrentSize), &(tpage->pData));
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_READ_FAILED;
            }
        }
        // if (tpageData == NULL) {
        // 	my_free(tpageData);
        // 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        //     WriteUnLock(&(pagePool->latch));
        // 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        // 	return GNCDB_READ_FAILED;
        // }
        /* 获取页的类型 */
        rc = readChar(&pageType, tpage->pData, &offset);
        if (rc != GNCDB_SUCCESS)
        {
            // my_free(tpageData);
            pagePoolWithdrawPage(pagePool, *page);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
        if (pageType == LEAF_PAGE || pageType == INTERNAL_PAGE)
        {
            /* 该页为Btree页 利用数据构造一个btree页 */
            tableSchema = getTableSchema(db->catalog, tableName);
            if (tableSchema == NULL)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_TABLESCHEMA_NOT_FOUND;
            }
            rc = btreePageConstruct((BtreePage *)tpage, pageId, tableName);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_BTG_CREATE_FALSE;
            }
            /* 将该页添加至缓冲池 */
            rc = pagePoolAddPageUnlock(tpage, pageId, db);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return rc;
            }

            *page = tpage;
        }
        else if (pageType == FREE_PAGE)
        {
            /* 该页为freepage */
            rc = freePageConstruct((FreePage *)tpage, pageId);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_FREEPAGE_CREATE_FALSE;
            }
            /* 将该页添加至缓冲池 */
            // rc = pagePoolAddPageUnlock((Page*)freePage, freePage->page.id, db);
            rc = pagePoolAddPageUnlock((Page *)tpage, tpage->id, db);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return rc;
            }
            *page = tpage;
        }
        else if (pageType == OVERFLOW_PAGE)
        {
            /* 该页为溢出页 */
            rc = overflowPageConstruct((OverflowPage *)tpage, pageId);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_OVERFLOWPAGE_CREATE_FALSE;
            }
            /* 将该页添加至缓冲池 */
            rc = pagePoolAddPageUnlock((Page *)tpage, tpage->id, db);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return rc;
            }
            *page = (Page *)tpage;
        }
        else if (pageType == META_PAGE)
        {
            rc = MetaPageConstruct((MetaPage *)tpage, pageId);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_BTG_CREATE_FALSE;
            }
            /* 将该页添加至缓冲池 */
            rc = pagePoolAddPageUnlock(tpage, pageId, db);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return rc;
            }
            *page = tpage;
        }
        else if (pageType == BUCKET_PAGE)
        {
            rc = BucketPageConstruct((BucketPage *)tpage, pageId);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_BTG_CREATE_FALSE;
            }
            /* 将该页添加至缓冲池 */
            rc = pagePoolAddPageUnlock(tpage, pageId, db);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return rc;
            }
            *page = tpage;
        }
        else if (pageType == HASH_OVERFLOW_PAGE)
        {
            rc = HashOverflowPageConstruct((HashOverflowPage *)tpage, pageId);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_BTG_CREATE_FALSE;
            }
            /* 将该页添加至缓冲池 */
            rc = pagePoolAddPageUnlock(tpage, pageId, db);
            if (rc != GNCDB_SUCCESS)
            {
                // my_free(tpageData);
                pagePoolWithdrawPage(pagePool, *page);
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return rc;
            }
            *page = tpage;
        }
        else
        {
            my_free(tpageData);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return GNCDB_NOT_FOUND_PAGE;
        }
    }
    else
    {
        *page = tpage;
        /* 钉住该页 */
        rc = setPageStatusPinUp(pagePool, pageId, NULL);
    }
    // my_free(tpageData);
    /* 获取该页的status */
    pageStatus = hashMapGet(pagePool->pageStatus, &pageId);
    /* 增加该页的访问次数 */
    rc = accessPage(pagePool, pageStatus);
    // pageStatus->accessRecord++;
    // access = pageStatus->accessRecord;
    // rc = LRU_Kset(pagePool, pageId, access);

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return rc;
}

int accessPage(PagePool *pagePool, PageStatus *pageStatus)
{
    int rc = GNCDB_SUCCESS;

    pageStatus->accessRecord++;
    rc = LRU_Kset(pagePool, pageStatus);

    return rc;
}

/* LRU_K 调整 */
int LRU_Kset(PagePool *pagePool, PageStatus *status)
{
    int rc = 0;
#if defined(DEBUG) && (DEBUG == 1)
    int num1 = 0;
    int num2 = 0;
#endif
    // int pageId = status->pageID;
    int access = status->accessRecord;
    /*需保证如果页的pin为0时在对应的unpin队列，因此这里pin增加到1时需要移动到对应的pin队列*/
    if (access > LRU_K)
    {
        /* 缓冲队列重新入队 */
        // dlist_delete(pagePool->cacheList, status);
        // dlist_append(pagePool->cacheList, status);
    }
    else if (access == LRU_K)
    {
        /* 从历史队列中移除 加入到缓冲队列*/
#if defined(DEBUG) && (DEBUG == 1)
        if (dlist_node_num(pagePool->historyList) != pagePool->historyList->length)
        {
            printf("historyList num not match\n");
        }
        if (dlist_node_num(pagePool->cacheList) != pagePool->cacheList->length)
        {
            printf("cacheList num not match\n");
        }
        num1 = dlist_node_num(pagePool->historyList);
        dlist_delete(pagePool->historyList, status);
        num2 = dlist_node_num(pagePool->historyList);
        if (num1 != num2 + 1)
        {
            printf("historyList num not match\n");
        }

        num1 = dlist_node_num(pagePool->cacheList);
        dlist_append(pagePool->cacheList, status);
        num2 = dlist_node_num(pagePool->cacheList);
        if (num1 != num2 - 1)
        {
            printf("cacheList num not match\n");
        }
#else
        dlist_delete(pagePool->historyList, status);
        dlist_append(pagePool->cacheList, status);
#endif
    }
    else if (access < LRU_K)
    {
        /* 历史队列重新入队 */
        // dlist_delete(pagePool->historyList, status);
        // dlist_append(pagePool->historyList, status);
    }
    return rc;
}

int pagePoolConstructNewBtreePage(struct BtreePage **page, struct PagePool *pagePool, struct GNCDB *db,
    PageType pageType, int pageId, char *tableName, struct Catalog *catalog)
{
    int rc = GNCDB_SUCCESS;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    if (pagePool->nFreeSlot > 0)
    {
        *page           = (struct BtreePage *)pagePool->pFree;
        pagePool->pFree = (*page)->page.pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = (struct BtreePage *)pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    memset((*page)->page.pData, 0, (db->pageCurrentSize));
    rc = btreePageConstructWithOutData(*page, pageType, pageId, tableName, catalog);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolWithdrawPage(pagePool, (struct Page *)*page);
        return rc;
    }

    rc = pagePoolAddPageUnlock((Page *)*page, (*page)->page.id, db);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolWithdrawPage(pagePool, (struct Page *)*page);
        return rc;
    }
    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return rc;
}

int pagePoolConstructNewBtreePageUnlock(struct BtreePage **page, struct PagePool *pagePool, struct GNCDB *db,
    PageType pageType, int pageId, char *tableName, struct Catalog *catalog)
{
    int rc = GNCDB_SUCCESS;

    if (pagePool->nFreeSlot > 0)
    {
        *page           = (struct BtreePage *)pagePool->pFree;
        pagePool->pFree = (*page)->page.pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = (struct BtreePage *)pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    memset((*page)->page.pData, 0x00, (db->pageCurrentSize));
    rc = btreePageConstructWithOutData(*page, pageType, pageId, tableName, catalog);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = pagePoolAddPageUnlock((Page *)*page, (*page)->page.id, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return rc;
}

void pagePoolWithdrawPage(struct PagePool *pagePool, struct Page *page)
{
    if (page == NULL)
        return;
    else
    {
        page->pNext     = pagePool->pFree;
        pagePool->pFree = page;
        pagePool->nFreeSlot++;
    }
}

int pagePoolConstructNewOverflowPageUnlock(
    struct OverflowPage **page, struct PagePool *pagePool, struct GNCDB *db, int pageId, int nextPageId)
{
    int rc = GNCDB_SUCCESS;

    if (pagePool->nFreeSlot > 0)
    {
        *page           = (struct OverflowPage *)pagePool->pFree;
        pagePool->pFree = (*page)->page.pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = (struct OverflowPage *)pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    memset((*page)->page.pData, 0x00, (db->pageCurrentSize));
    rc = overflowPageConstructWithOutData(*page, pageId, nextPageId);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = pagePoolAddPageUnlock((Page *)*page, (*page)->page.id, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return rc;
}

int pagePoolConstructNewFreePageUnlock(
    struct FreePage **page, struct PagePool *pagePool, struct GNCDB *db, int pageId, int nextPageId)
{
    int rc = GNCDB_SUCCESS;

    if (pagePool->nFreeSlot > 0)
    {
        *page           = (struct FreePage *)pagePool->pFree;
        pagePool->pFree = (*page)->page.pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = (struct FreePage *)pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    memset((*page)->page.pData, 0x00, (db->pageCurrentSize));
    rc = freePageConstructWithOutData(*page, pageId, nextPageId);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = pagePoolAddPageUnlock((Page *)*page, (*page)->page.id, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    db->firstFreePid = (*page)->page.id;
    return rc;
}

// 只分配页空间，不进行页构造
int pagePoolAllocateNewPageUnlock(struct Page **page, struct PagePool *pagePool, struct GNCDB *db)
{
    int rc = GNCDB_SUCCESS;

    if (pagePool->nFreeSlot > 0)
    {
        *page           = pagePool->pFree;
        pagePool->pFree = (*page)->pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    // 缓冲池页替换调用该函数，data部分会被磁盘中数据覆盖，因此不需要清空
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return rc;
}

/// <summary>
/// 创建一个BtreePage页
/// </summary>
/// <param name="pagePool"></param>
/// <param name="page">返回得到的BtreePage</param>
/// <param name="pageType">页类型  叶子页还是内部页</param>
/// <param name="tableName"></param>
/// <param name="parentPageId">父页页号</param>
/// <param name="db"></param>
/// <returns></returns>
int pagePoolCreateBtreePage(
    struct BtreePage **page, PageType pageType, char *tableName, struct GNCDB *db, struct Transaction *tx)
{

    int              rc         = 0;
    int              pageId     = 0;
    int              flag       = 0;
    struct PagePool *pagePool   = NULL;
    struct Page *    tpage      = NULL;
    PageStatus *     pageStatus = NULL;

    pagePool = db->pagePool;

    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");

    /* 访问db共享资源 */
    // 待修改函数
    rc = getFirstFreePageId(db, &pageId, tx, &tpage);
    if (rc != GNCDB_SUCCESS)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    if (pageId <= 1)
    {
        /* 数据库中没有空页，拿取下一个新页 */
        db->totalPageNum++;
        /*  获取新的页页号 */
        pageId = db->totalPageNum;
        flag   = 1;
    }
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    if (flag == 0)
    {
        rc = pagePoolRemovePageUnlock(db->pagePool, pageId, tpage);
        if (rc != GNCDB_SUCCESS)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
    }
    /* 已有页数据构造btreepage */
    rc = pagePoolConstructNewBtreePageUnlock(page, pagePool, db, pageType, pageId, tableName, db->catalog);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolWithdrawPage(pagePool, (struct Page *)*page);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return rc;
    }
    if (*page == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_FREEPAGE_CREATE_FALSE;
    }
    pageStatus = hashMapGet(pagePool->pageStatus, &(*page)->page.id);
    rc         = accessPage(pagePool, pageStatus);

    // btreePageConstructWithOutData(*page,pageType, pageId, tableName, db->catalog);
    /* 将该页添加至缓冲池 */
    // rc = pagePoolAddPageUnlock((Page*)*page, pageId, db);
    // if (rc)
    // {
    // 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    //     WriteUnLock(&(pagePool->latch));
    // 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    // 	return rc;
    // }
    // setPageStatusPinUp(db->pagePool, pageId);

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return GNCDB_SUCCESS;
}
// int pagePoolCreateBtreePage(struct BtreePage** page, PageType pageType, char* tableName, struct GNCDB* db,struct
// Transaction* tx) {

// 	int rc = 0;
// 	int pageId = 0;
//     int flag = 1;
// 	struct PagePool* pagePool = NULL;
//     pagePool = db->pagePool;

//     LOG(LOG_TRACE, "SLOCKing:%s", "db");
//     WriteLock(&(db->latch));
//     LOG(LOG_TRACE, "SLOCKend:%s", "db");
// 	/* 访问db共享资源 */
// 	rc = getFirstFreePageId(db, &pageId, tx);
//     if(rc != GNCDB_SUCCESS)
//     {
//         LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
//         WriteUnLock(&(db->latch));
//         LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
//         return rc;
//     }
// 	if (pageId  <= 1)
// 	{
// 		/* 数据库中没有空页，拿取下一个新页 */
// 		db->totalPageNum++;
// 		/*  获取新的页页号 */
// 		pageId = db->totalPageNum;
//         flag = 0;
// 	}
//     LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
//     WriteUnLock(&(db->latch));
//     LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
// 	LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
//     WriteLock(&(pagePool->latch));
// 	LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

// 	/* 已有页数据构造btreepage */
// 	*page = btreePageConstructWithOutData(pageType, pageId, tableName, db->catalog);
// 	if (*page == NULL)
// 	{
// 		LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//         WriteUnLock(&(pagePool->latch));
// 		LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 		return GNCDB_FREEPAGE_CREATE_FALSE;
// 	}
// 	/* 将该页添加至缓冲池 */
//     if(flag)
//     {
//         rc = pagePoolRemovePageUnlock(db->pagePool, pageId);
//         if(rc != GNCDB_SUCCESS)
//         {
//             LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//             WriteUnLock(&(pagePool->latch));
//             LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
//             return rc;
//         }
//     }
// 	rc = pagePoolAddPageUnlock((Page*)*page, pageId, db);
// 	if (rc)
// 	{
// 		LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//         WriteUnLock(&(pagePool->latch));
// 		LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 		return rc;
// 	}
// 	//setPageStatusPinUp(db->pagePool, pageId);

// 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//     WriteUnLock(&(pagePool->latch));
// 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 	return GNCDB_SUCCESS;
// }

int pagePoolCreateOverflowPage(struct OverflowPage **page, struct GNCDB *db, struct Transaction *tx)
{

    int              rc       = 0;
    int              pageId   = 0;
    int              flag     = 0;
    struct PagePool *pagePool = NULL;
    struct Page *    tpage    = NULL;
    pagePool                  = db->pagePool;

    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    /* 访问共享资源 */
    rc = getFirstFreePageId(db, &pageId, tx, &tpage);
    if (rc != GNCDB_SUCCESS)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    if (pageId <= 0)
    {
        /* 数据库中没有空页，拿取下一个新页 */
        db->totalPageNum++;
        /*  获取新的页页号 */
        pageId = db->totalPageNum;
        flag   = 1;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    if (flag == 0)
    {
        rc = pagePoolRemovePageUnlock(db->pagePool, pageId, tpage);
        if (rc != GNCDB_SUCCESS)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
    }
    rc = pagePoolConstructNewOverflowPageUnlock(page, pagePool, db, pageId, 0);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolWithdrawPage(pagePool, (struct Page *)*page);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return rc;
    }
    // *page = overflowPageConstructWithOutData(pageId, 0);
    // if (*page == NULL)
    // {
    // 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    //     WriteUnLock(&(pagePool->latch));
    // 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    // 	return GNCDB_OVERFLOWPAGE_CREATE_FALSE;
    // }
    /* 将该页添加至缓冲池 */
    // if(flag)
    // {
    //     rc = pagePoolRemovePageUnlock(db->pagePool, pageId);
    //     if(rc != GNCDB_SUCCESS)
    //     {
    //         LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    //         WriteUnLock(&(pagePool->latch));
    //         LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    //         return rc;
    //     }
    // }
    // rc = pagePoolAddPageUnlock((Page*)*page, pageId, db);
    // if (rc)
    // {
    // 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    //     WriteUnLock(&(pagePool->latch));
    // 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    // 	return rc;
    // }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

    return GNCDB_SUCCESS;
}
/// <summary>
/// 创建一个OverflowPage页
/// </summary>
/// <param name="pagePool"></param>
/// <param name="page">返回得到的OverflowPage</param>
/// <param name="db"></param>
/// <returns></returns>
// int pagePoolCreateOverflowPage(struct OverflowPage** page, struct GNCDB* db, struct Transaction* tx) {

// 	int rc = 0;
// 	int pageId = 0;
//     int flag = 1;
// 	struct PagePool* pagePool = NULL;
//     pagePool = db->pagePool;

//     LOG(LOG_TRACE, "SLOCKing:%s", "db");
//     WriteLock(&(db->latch));
//     LOG(LOG_TRACE, "SLOCKend:%s", "db");
// 	/* 访问共享资源 */
// 	rc = getFirstFreePageId(db, &pageId, tx);
//     if(rc != GNCDB_SUCCESS)
//     {
//         LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
//         WriteUnLock(&(db->latch));
//         LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
//         return rc;
//     }
// 	if (pageId <= 0)
// 	{
// 		/* 数据库中没有空页，拿取下一个新页 */
// 		db->totalPageNum++;
// 		/*  获取新的页页号 */
// 		pageId = db->totalPageNum;
//         flag = 0;
// 	}

//     LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
//     WriteUnLock(&(db->latch));
//     LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
// 	LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
//     WriteLock(&(pagePool->latch));
// 	LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

// 	*page = overflowPageConstructWithOutData(pageId, 0);
// 	if (*page == NULL)
// 	{
// 		LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//         WriteUnLock(&(pagePool->latch));
// 		LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 		return GNCDB_OVERFLOWPAGE_CREATE_FALSE;
// 	}
// 	/* 将该页添加至缓冲池 */
//     if(flag)
//     {
//         rc = pagePoolRemovePageUnlock(db->pagePool, pageId);
//         if(rc != GNCDB_SUCCESS)
//         {
//             LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//             WriteUnLock(&(pagePool->latch));
//             LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
//             return rc;
//         }
//     }
// 	rc = pagePoolAddPageUnlock((Page*)*page, pageId, db);
// 	if (rc)
// 	{
// 		LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//         WriteUnLock(&(pagePool->latch));
// 		LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 		return rc;
// 	}

// 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//     WriteUnLock(&(pagePool->latch));
// 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

// 	return GNCDB_SUCCESS;
// }
/// <summary>
/// 创建一个BtreePage页
/// </summary>
/// <param name="pagePool"></param>
/// <param name="page">返回得到的BtreePage</param>
/// <param name="pageType">页类型  叶子页还是内部页</param>
/// <param name="tableName"></param>
/// <param name="parentPageId">父页页号</param>
/// <param name="db"></param>
/// <returns></returns>
// int pagePoolCreateRtreePage(struct RtreePage** page, PageType pageType, char* tableName, int parentPageId, struct
// GNCDB* db,struct Transaction* tx) {

// 	int rc = 0;
// 	int pageId = 0;
//     int flag = 1;
// 	struct PagePool* pagePool = NULL;
//     pagePool = db->pagePool;

//     LOG(LOG_TRACE, "SLOCKing:%s", "db");
//     WriteLock(&(db->latch));
//     LOG(LOG_TRACE, "SLOCKend:%s", "db");
// 	/* 访问db共享资源 */
// 	rc = getFirstFreePageId(db, &pageId, tx);
//     if(rc != GNCDB_SUCCESS)
//     {
//         LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
//         WriteUnLock(&(db->latch));
//         LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
//         return rc;
//     }
// 	if (pageId  <= 1)
// 	{
// 		/* 数据库中没有空页，拿取下一个新页 */
// 		db->totalPageNum++;
// 		/*  获取新的页页号 */
// 		pageId = db->totalPageNum;
//         flag = 0;
// 	}
//     LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
//     WriteUnLock(&(db->latch));
//     LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
// 	LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
//     WriteLock(&(pagePool->latch));
// 	LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

// 	/* 已有页数据构造btreepage */
// 	*page = rtreePageConstructWithOutData(pageType, pageId, tableName, parentPageId, db);
// 	if (*page == NULL)
// 	{
// 		LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//         WriteUnLock(&(pagePool->latch));
// 		LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 		return GNCDB_FREEPAGE_CREATE_FALSE;
// 	}
// 	/* 将该页添加至缓冲池 */
//     if(flag)
//     {
//         rc = pagePoolRemovePageUnlock(db->pagePool, pageId);
//         if(rc != GNCDB_SUCCESS)
//         {
//             LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//             WriteUnLock(&(pagePool->latch));
//             LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
//             return rc;
//         }
//     }
// 	rc = pagePoolAddPageUnlock((Page*)*page, pageId, db);
// 	if (rc)
// 	{
// 		LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//         WriteUnLock(&(pagePool->latch));
// 		LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 		return rc;
// 	}
// 	//setPageStatusPinUp(db->pagePool, pageId);

// 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
//     WriteUnLock(&(pagePool->latch));
// 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
// 	return GNCDB_SUCCESS;
// }
int pagePoolConstructNewMetaPageUnlock(
    struct MetaPage **page, struct PagePool *pagePool, struct GNCDB *db, int pageId, struct Catalog *catalog)
{
    int rc = GNCDB_SUCCESS;

    if (pagePool->nFreeSlot > 0)
    {
        *page           = (struct MetaPage *)pagePool->pFree;
        pagePool->pFree = (*page)->page.pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = (struct MetaPage *)pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    memset((*page)->page.pData, 0x00, db->pageCurrentSize);

    rc = HashMetaPageInit(*page, pageId);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = pagePoolAddPageUnlock((Page *)*page, (*page)->page.id, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    return rc;
}
/// <summary>
/// 创建一个MetaPage页
/// </summary>
/// <param name="pagePool"></param>
/// <param name="page">返回得到的MetaPage</param>
/// <param name="tableName"></param>
/// <param name="db"></param>
/// <returns></returns>
int pagePoolCreateMetaPage(struct MetaPage **page, struct GNCDB *db, struct Transaction *tx)
{
    int              rc       = GNCDB_SUCCESS;
    int              pageId   = 0;
    int              flag     = 1;
    struct PagePool *pagePool = NULL;
    struct Page *    tpage    = NULL;

    pagePool = db->pagePool;

    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    /* 访问共享资源 */
    rc = getFirstFreePageId(db, &pageId, tx, &tpage);
    if (rc != GNCDB_SUCCESS)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    if (pageId <= 1)
    {
        /* 数据库中没有空页，拿取下一个新页 */
        db->totalPageNum++;
        /*  获取新的页页号 */
        pageId = db->totalPageNum;
        flag   = 1;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    /* 将该页添加至缓冲池 */
    if (flag == 0)
    {
        rc = pagePoolRemovePageUnlock(db->pagePool, pageId, tpage);
        if (rc != GNCDB_SUCCESS)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
    }

    /* 调用元数据页构造函数 */
    rc = pagePoolConstructNewMetaPageUnlock(page, pagePool, db, pageId, db->catalog);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolWithdrawPage(pagePool, (struct Page *)*page);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return rc;
    }
    if (*page == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_FREEPAGE_CREATE_FALSE;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

    return GNCDB_SUCCESS;
}

int pagePoolConstructNewBucketPageUnlock(struct BucketPage **page, struct PagePool *pagePool, struct GNCDB *db,
    int pageId, struct HashIndex *hashIndex, int bucketNumber)
{
    int rc = GNCDB_SUCCESS;

    if (pagePool->nFreeSlot > 0)
    {
        *page           = (struct BucketPage *)pagePool->pFree;
        pagePool->pFree = (*page)->page.pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = (struct BucketPage *)pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    memset((*page)->page.pData, 0x00, db->pageCurrentSize);
    rc = HashBucketPageInit(*page, pageId, bucketNumber, hashIndex);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = pagePoolAddPageUnlock((Page *)*page, (*page)->page.id, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return rc;
}
/// <summary>
/// 创建BucketPage并加入页池
/// </summary>
/// <param name="page">返回创建的BucketPage指针</param>
/// <param name="bucketNumber">桶编号</param>
/// <param name="hashIndex">所属哈希索引</param>
/// <param name="db">数据库实例</param>
/// <param name="tx">事务对象</param>
/// <returns>操作状态码</returns>
int pagePoolCreateBucketPage(
    struct BucketPage **page, struct HashIndex *hashIndex, int bucketNumber, struct GNCDB *db, struct Transaction *tx)
{
    /* 1.变量定义 */
    int              rc       = GNCDB_SUCCESS;
    int              pageId   = 0;
    int              flag     = 1;
    struct PagePool *pagePool = NULL;
    struct Page *    tpage    = NULL;
    pagePool                  = db->pagePool;

    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    /* 访问db共享资源 */
    rc = getFirstFreePageId(db, &pageId, tx, &tpage);
    if (rc != GNCDB_SUCCESS)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    if (pageId <= 1)
    {
        /* 数据库中没有空页，拿取下一个新页 */
        db->totalPageNum++;
        /*  获取新的页页号 */
        pageId = db->totalPageNum;
        flag   = 1;
    }
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    if (flag == 0)
    {
        rc = pagePoolRemovePageUnlock(db->pagePool, pageId, tpage);
        if (rc != GNCDB_SUCCESS)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
    }
    /* 已有页数据构造bucketpage */
    rc = pagePoolConstructNewBucketPageUnlock(page, pagePool, db, pageId, hashIndex, bucketNumber);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolWithdrawPage(pagePool, (struct Page *)*page);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return rc;
    }
    if (*page == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_FREEPAGE_CREATE_FALSE;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return GNCDB_SUCCESS;
}

int pagePoolConstructNewHashOverflowPageUnlock(struct HashOverflowPage **page, struct PagePool *pagePool,
    struct GNCDB *db, int pageId, struct HashIndex *hashIndex, int bucketNumber)
{
    int rc = GNCDB_SUCCESS;

    if (pagePool->nFreeSlot > 0)
    {
        *page           = (struct HashOverflowPage *)pagePool->pFree;
        pagePool->pFree = (*page)->page.pNext;
        pagePool->nFreeSlot--;
    }
    else
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
            return rc;
        *page = (struct HashOverflowPage *)pagePool->pFree;
        pagePool->nFreeSlot--;
    }
    if (*page == NULL)
        return GNCDB_MEM;
    memset((*page)->page.pData, 0x00, db->pageCurrentSize);
    rc = HashOverflowPageInit(*page, pageId, bucketNumber, hashIndex);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = pagePoolAddPageUnlock((Page *)*page, (*page)->page.id, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return rc;
}
/// <summary>
/// 创建HashOverflowPage并加入页池
/// </summary>
/// <param name="page">返回创建的OverflowPage指针</param>
/// <param name="bucketNumber">桶编号</param>
/// <param name="tableName">所属表名</param>
/// <param name="db">数据库实例</param>
/// <param name="tx">事务对象</param>
/// <returns>操作状态码</returns>
int pagePoolcreateHashOverflowPage(struct HashOverflowPage **page, struct HashIndex *hashIndex, int bucketNumber,
    struct GNCDB *db, struct Transaction *tx)
{
    /* 1.变量定义 */
    int              rc       = GNCDB_SUCCESS;
    int              pageId   = 0;
    int              flag     = 1;
    struct PagePool *pagePool = NULL;
    struct Page *    tpage    = NULL;
    pagePool                  = db->pagePool;

    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    /* 访问db共享资源 */
    rc = getFirstFreePageId(db, &pageId, tx, &tpage);
    if (rc != GNCDB_SUCCESS)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    if (pageId <= 1)
    {
        /* 数据库中没有空页，拿取下一个新页 */
        db->totalPageNum++;
        /*  获取新的页页号 */
        pageId = db->totalPageNum;
        flag   = 1;
    }
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    if (flag == 0)
    {
        rc = pagePoolRemovePageUnlock(db->pagePool, pageId, tpage);
        if (rc != GNCDB_SUCCESS)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
    }
    /* 已有页数据构造hashoverflowtpage */
    rc = pagePoolConstructNewHashOverflowPageUnlock(page, pagePool, db, pageId, hashIndex, bucketNumber);
    if (rc != GNCDB_SUCCESS)
    {
        pagePoolWithdrawPage(pagePool, (struct Page *)*page);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return rc;
    }
    if (*page == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_FREEPAGE_CREATE_FALSE;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return GNCDB_SUCCESS;
}

/// <summary>
/// 淘汰一个页
/// </summary>
/// <param name="pagePool">pagePool</param>
/// <param name="tid">tid</param>
/// <returns>状态码</returns>
int pagePoolReplacePage(struct GNCDB *db)
{
    DListNode *iter          = NULL;
    int        rc            = 0;
    int        replacePageId = -1;
    // int flag = 0;
    int *            pid      = NULL;
    Page *           pp       = NULL;
    struct PagePool *pagePool = NULL;
    pagePool                  = db->pagePool;

    /* 先遍历历史队列，再遍历缓冲队列，找出pin为0的页然后淘汰*/
    // iter = pagePool->historyList->first;
    iter = pagePool->historyListUnpin->first;

    while (iter != NULL)
    {
        pid = &iter->pageID;

        if (iter->pin == 0)
        {
            replacePageId = *pid;
            break;
        }

        iter = iter->next;
        if (iter == pagePool->historyListUnpin->first)
        {
            break;
        }
    }

    if (replacePageId == -1)
    {
        /* 遍历缓冲队列 */
        // iter = pagePool->cacheList->first;
        iter = pagePool->cacheListUnpin->first;

        while (iter != NULL)
        {
            pid = &iter->pageID;
            // status = hashMapGet(pagePool->pageStatus, pid);
            // if (status == NULL)
            // {
            // 	return GNCDB_STATUS_NOT_FOUND;
            // }

            if (iter->pin == 0)
            {
                replacePageId = *pid;
                break;
            }

            iter = iter->next;
            if (iter == pagePool->cacheListUnpin->first)
            {
                break;
            }
        }
    }
    // for (i = 0; i < pagePool->historyArray->elementCount; i++)
    // {
    // 	pid = varArrayListGet(pagePool->historyArray, i);
    // 	if (pid == NULL)
    // 	{
    // 		return GNCDB_ARRAY_GET_FALSE;
    // 	}
    // 	status = hashMapGet(pagePool->pageStatus, pid);
    // 	if (status == NULL)
    // 	{
    // 		return GNCDB_STATUS_NOT_FOUND;
    // 	}
    // 	if (status->pin == 0)
    // 	{
    // 		replacePageId = *pid;
    // 		break;
    // 	}
    // }
    // if (replacePageId == -1)
    // {
    // 	/* 遍历缓冲队列 */
    // 	for (i = 0; i < pagePool->cacheArray->elementCount; i++)
    // 	{
    // 		pid = varArrayListGet(pagePool->cacheArray, i);
    // 		if (pid == NULL)
    // 		{
    // 			return GNCDB_ARRAY_GET_FALSE;
    // 		}
    // 		status = hashMapGet(pagePool->pageStatus, pid);
    // 		if (status == NULL)
    // 		{
    // 			return GNCDB_STATUS_NOT_FOUND;
    // 		}
    // 		if (status->pin == 0)
    // 		{
    // 			replacePageId = *pid;
    // 			break;
    // 		}
    // 	}
    // }
    if (replacePageId == -1)
    {
        /* 所有的页都被钉住 */
        return GNCDB_AIIPAGE_IS_PIN;
    }

    pp = hashMapGet(pagePool->pageMap, &replacePageId);
    if (pp == NULL)
    {
        return GNCDB_MAP_GET_FALSE;
    }
    if (iter->dirty == true)
    {
        rc = pagePoolFlushPage(pp->id, db);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }
    rc = pagePoolRemovePageUnlock(pagePool, replacePageId, pp);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // lockManagerDestroyTableEntryByPageID(db->transactionManager->lockManager, replacePageId);
    rc = pagePoolRemovePCN(pagePool, replacePageId);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    // printf("replace id : %d\n", replacePageId);
    //    printf("page:");
    //    printfHashKey(db->pagePool->pageMap);
    //    printf("status:");
    //    printfHashKey(db->pagePool->pageStatus);
    //    printf("history:");
    //    printfArrayIntValue(db->pagePool->historyArray);
    //    printf("cache:");
    //    printfArrayIntValue(db->pagePool->cacheArray);

    return GNCDB_SUCCESS;
}

/// <summary>
/// 添加一个页
/// </summary>
/// <param name="pagePool"></param>
/// <param name="page">需要添加的页</param>
/// <param name="pageId">页号</param>
/// <param name="db"></param>
/// <returns></returns>
int pagePoolAddPage(Page *page, int pageId, struct GNCDB *db)
{
    int              rc       = 0;
    PageStatus *     status   = NULL;
    struct PagePool *pagePool = NULL;
    pagePool                  = db->pagePool;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    /* 缓冲区不够 淘汰页 */
    if (pagePool->pageMap->entryCount >= (db->pagePoolCount))
    {
        rc = pagePoolReplacePage(db);
        if (rc != GNCDB_SUCCESS)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
    }

    status = pageStatusConstruct(pageId);
    if (status == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_STATUS_CREATE_FALSE;
    }

    rc = hashMapPut(pagePool->pageStatus, &page->id, status);
    if (rc != GNCDB_SUCCESS)
    {
        pageStatusDestroy(status);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_MAP_PUT_FALSE;
    }
    rc = hashMapPut(pagePool->pageMap, &page->id, page);
    if (rc != GNCDB_SUCCESS)
    {
        pageStatusDestroy(status);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_MAP_PUT_FALSE;
    }
    /* 第一次加入只能加入历史unpin队列 */
    dlist_append(pagePool->historyListUnpin, status);

    setPageStatusPinUp(pagePool, pageId, status);
    if (rc != GNCDB_SUCCESS)
    {
        pageStatusDestroy(status);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_ARRAY_ADD_FALSE;
    }
    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return GNCDB_SUCCESS;
}

/// <summary>
/// 添加一个页(在pagepool中用于已经上锁的函数中)
/// </summary>
/// <param name="pagePool"></param>
/// <param name="page"></param>
/// <param name="pageId"></param>
/// <param name="tid"></param>
/// <param name="db"></param>
/// <returns></returns>
int pagePoolAddPageUnlock(Page *page, int pageId, struct GNCDB *db)
{
    int              rc       = 0;
    PageStatus *     status   = NULL;
    struct PagePool *pagePool = NULL;
#if defined(DEBUG) && (DEBUG == 1)
    int num1 = 0;
    int num2 = 0;
#endif

    pagePool = db->pagePool;

    /* 缓冲区不够 淘汰页 */
    status = pageStatusConstruct(pageId);
    if (status == NULL)
    {
        return GNCDB_STATUS_CREATE_FALSE;
    }

    rc = hashMapPut(pagePool->pageStatus, &page->id, status);
    if (rc != GNCDB_SUCCESS)
    {
        pageStatusDestroy(status);
        return GNCDB_MAP_PUT_FALSE;
    }
    rc = hashMapPut(pagePool->pageMap, &page->id, page);
    if (rc != GNCDB_SUCCESS)
    {
        pageStatusDestroy(status);
        return GNCDB_MAP_PUT_FALSE;
    }
#if defined(DEBUG) && (DEBUG == 1)
    /* 第一次加入只能加入历史unpin队列 */
    num1 = dlist_node_num(pagePool->historyListUnpin);
    dlist_append(pagePool->historyListUnpin, status);
    num2 = dlist_node_num(pagePool->historyListUnpin);
    if (num1 != num2 - 1)
    {
        printf("historyListUnpin num not match\n");
    }
#else
    dlist_append(pagePool->historyListUnpin, status);
#endif

    setPageStatusPinUp(pagePool, pageId, status);

    if (rc != GNCDB_SUCCESS)
    {
        pageStatusDestroy(status);
        return GNCDB_ARRAY_ADD_FALSE;
    }
    return GNCDB_SUCCESS;
}

/// <summary>
/// 从缓冲池中移除一个页
/// </summary>
/// <param name="pagePool"></param>
/// <param name="pageId"></param>
/// <returns></returns>
int pagePoolRemovePage(struct PagePool *pagePool, int pageId)
{
    int         rc         = 0;
    Page *      tpage      = NULL;
    PageStatus *pageStatus = NULL;
    int         access     = 0;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    /* 获取页 */
    tpage = hashMapGet(pagePool->pageMap, &pageId);
    if (tpage == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_MAP_GET_FALSE;
    }
    /* 获取页状态 */
    pageStatus = hashMapGet(pagePool->pageStatus, &pageId);
    if (pageStatus == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_MAP_GET_FALSE;
    }

    access = pageStatus->accessRecord;
    if (tpage != NULL && pageStatus != NULL)
    {
        /* 删除页 */
        rc = hashMapRemove(pagePool->pageMap, &pageId);
        if (rc)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return GNCDB_MAP_REMOVE_FALSE;
        }
        if (access >= LRU_K)
        {
            /* 缓冲队列 */
            dlist_delete(pagePool->cacheList, pageStatus);
            // rc = varArrayListRemove(pagePool->cacheArray, &pageId);
            if (rc)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_MAP_REMOVE_FALSE;
            }
        }
        else
        {
            /* 历史队列 */
            dlist_delete(pagePool->historyList, pageStatus);
            // rc = varArrayListRemove(pagePool->historyArray, &pageId);
            if (rc)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_ARRAY_REMOVE_FALSE;
            }
        }
        /* 移除页状态 */
        rc = hashMapRemove(pagePool->pageStatus, &pageId);
        if (rc)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return GNCDB_MAP_REMOVE_FALSE;
        }
        /* 释放page和status的空间的 */
        if (tpage->pageType == LEAF_PAGE || tpage->pageType == INTERNAL_PAGE)
        {
            btreePageDestroy((BtreePage **)&tpage);
        }
        else if (tpage->pageType == FREE_PAGE)
        {
            freePageDestroy((FreePage **)&tpage);
        }
        else if (tpage->pageType == OVERFLOW_PAGE)
        {
            overflowPageDestroy((OverflowPage **)&tpage);
        }
        else if (tpage->pageType == META_PAGE)
        {
            MetaPageDestroy((MetaPage **)&tpage);
        }
        else if (tpage->pageType == BUCKET_PAGE)
        {
            BucketPageDestroy((BucketPage **)&tpage);
        }
        else if (tpage->pageType == HASH_OVERFLOW_PAGE)
        {
            HashoverflowPageDestroy((HashOverflowPage **)&tpage);
        }
        pageStatusDestroy(pageStatus);

        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_SUCCESS;
    }
    else
    {
        WriteUnLock(&(pagePool->latch));
        return GNCDB_NO_PAGE;
    }
}

/// <summary>
/// 从缓冲池中移除一个页(无锁状态,用于已经获取锁的函数中)
/// </summary>
/// <param name="pagePool"></param>
/// <param name="pageId"></param>
/// <returns></returns>
int pagePoolRemovePageUnlock(struct PagePool *pagePool, int pageId, struct Page *page)
{
    int         rc         = 0;
    Page *      tpage      = NULL;
    PageStatus *pageStatus = NULL;
    int         access     = 0;
    // int num1 = 0;
    // int num2 = 0;

    /* 获取页 */
    tpage = hashMapGet(pagePool->pageMap, &pageId);
    if (tpage == NULL)
    {
        return GNCDB_MAP_GET_FALSE;
    }
    /* 获取页状态 */
    pageStatus = hashMapGet(pagePool->pageStatus, &pageId);
    if (pageStatus == NULL)
    {
        return GNCDB_MAP_GET_FALSE;
    }
    // if (pageStatus->pin > 0)
    // {
    // 	/* 页被钉住不能删除 */
    // 	return GNCDB_NO_REMOVE;
    // }
    if (pageStatus->dirty == true)
    {
        /* todo 刷盘需要db 该删除主要用于页转换时使用,不考虑刷盘操作 */
        /*pagePoolFlushPage(pagePool, pageId, db);*/
    }
    access = pageStatus->accessRecord;

    if (tpage != NULL && pageStatus != NULL)
    {
        /* 删除页 */
        rc = hashMapRemove(pagePool->pageMap, &pageId);
        if (rc)
        {
            return GNCDB_MAP_REMOVE_FALSE;
        }
        if (access >= LRU_K)
        {
            /* 缓冲队列 */
            if (pageStatus->pin == 0)
            {
#if defined(DEBUG) && (DEBUG == 1)
                num1 = dlist_node_num(pagePool->cacheListUnpin);
                dlist_delete(pagePool->cacheListUnpin, pageStatus);
                num2 = dlist_node_num(pagePool->cacheListUnpin);
                if (num1 != num2 - 1)
                {
                    printf("cacheListUnpin num not match\n");
                }
#else
                dlist_delete(pagePool->cacheListUnpin, pageStatus);
#endif
            }
            else
            {
#if defined(DEBUG) && (DEBUG == 1)
                num1 = dlist_node_num(pagePool->cacheList);
                dlist_delete(pagePool->cacheList, pageStatus);
                num2 = dlist_node_num(pagePool->cacheList);
                if (num1 != num2 - 1)
                {
                    printf("cacheList num not match\n");
                }
#else
                dlist_delete(pagePool->cacheList, pageStatus);
#endif
            }
            // rc = varArrayListRemove(pagePool->cacheArray, &pageId);
            if (rc)
            {
                return GNCDB_MAP_REMOVE_FALSE;
            }
        }
        else
        {
            if (pageStatus->pin == 0)
            {
#if defined(DEBUG) && (DEBUG == 1)
                num1 = dlist_node_num(pagePool->historyListUnpin);
                dlist_delete(pagePool->historyListUnpin, pageStatus);
                num2 = dlist_node_num(pagePool->historyListUnpin);
                if (num1 != num2 - 1)
                {
                    printf("historyListUnpin num not match\n");
                }
#else
                dlist_delete(pagePool->historyListUnpin, pageStatus);
#endif
            }
            else
            {
#if defined(DEBUG) && (DEBUG == 1)
                num1 = dlist_node_num(pagePool->historyList);
                dlist_delete(pagePool->historyList, pageStatus);
                num2 = dlist_node_num(pagePool->historyList);
                if (num1 != num2 - 1)
                {
                    printf("historyList num not match\n");
                }
#else
                dlist_delete(pagePool->historyList, pageStatus);
#endif
            }
            // rc = varArrayListRemove(pagePool->historyArray, &pageId);
            if (rc)
            {
                return GNCDB_ARRAY_REMOVE_FALSE;
            }
        }
        /* 移除页状态 */
        rc = hashMapRemove(pagePool->pageStatus, &pageId);
        if (rc)
        {
            return GNCDB_MAP_REMOVE_FALSE;
        }
        page->pNext     = pagePool->pFree;
        pagePool->pFree = page;
        pagePool->nFreeSlot++;
        /* 释放page和status的空间的 */
        if (tpage->pageType == LEAF_PAGE || tpage->pageType == INTERNAL_PAGE)
        {
            btreePageDestroy((BtreePage **)&tpage);
        }
        else if (tpage->pageType == FREE_PAGE)
        {
            freePageDestroy((FreePage **)&tpage);
        }
        else if (tpage->pageType == OVERFLOW_PAGE)
        {
            overflowPageDestroy((OverflowPage **)&tpage);
        }
        else if (tpage->pageType == META_PAGE)
        {
            MetaPageDestroy((MetaPage **)&tpage);
        }
        else if (tpage->pageType == BUCKET_PAGE)
        {
            BucketPageDestroy((BucketPage **)&tpage);
        }
        else if (tpage->pageType == HASH_OVERFLOW_PAGE)
        {
            HashoverflowPageDestroy((HashOverflowPage **)&tpage);
        }
        pageStatusDestroy(pageStatus);

        return GNCDB_SUCCESS;
    }
    else
    {
        return GNCDB_NO_PAGE;
    }
}

/// <summary>
/// btreePage刷入磁盘，freepage和overflowpage的刷盘在close中才会执行
/// </summary>
/// <param name="pagePool">pagePool</param>
/// <param name="pageId">pageId页号</param>
/// <param name="tid">tid</param>
/// <returns>状态码</returns>
int pagePoolFlushPage(int pageId, struct GNCDB *db)
{
    int                 rc          = 0;
    Page *              page        = NULL;
    PageStatus *        status      = NULL;
    BYTE *              uchar       = NULL;
    BtreePage *         btPage      = NULL;
    struct TableSchema *tableSchema = NULL;
    struct PagePool *   pagePool    = NULL;
    pagePool                        = db->pagePool;

    /* 获取page和status */
    page = (Page *)hashMapGet(pagePool->pageMap, &pageId);
    if (page == NULL)
    {
        return GNCDB_MAP_GET_FALSE;
    }
    status = hashMapGet(pagePool->pageStatus, &pageId);
    if (status == NULL)
    {
        return GNCDB_MAP_GET_FALSE;
    }

    if (status->dirty == true)
    {
        if (page->pageType == FREE_PAGE)
        {
            /* freepage转化为字节流并写磁盘 */
            uchar = freePageToByte(db, (FreePage *)page);
            if (uchar == NULL)
            {
                return GNCDB_TOBYTE_FALSE;
            }
            rc = osWrite(db->dbFile, uchar, (long)(pageId - 1) * (db->pageCurrentSize), (db->pageCurrentSize));
            if (rc != GNCDB_SUCCESS)
            {
                my_free(uchar);
                return rc;
            }
            // my_free(uchar);
        }
        else if (page->pageType == OVERFLOW_PAGE)
        {
            /* overflowpage转化为字节流并写磁盘 */
            uchar = overflowPageToByte(db, (OverflowPage *)page);
            if (uchar == NULL)
            {
                return GNCDB_TOBYTE_FALSE;
            }
            rc = osWrite(db->dbFile, uchar, (long)(pageId - 1) * (db->pageCurrentSize), (db->pageCurrentSize));
            if (rc != GNCDB_SUCCESS)
            {
                my_free(uchar);
                return rc;
            }
            // my_free(uchar);
        }
        else if (page->pageType == LEAF_PAGE || page->pageType == INTERNAL_PAGE)
        {
            /* BtreePage转化为字节流并写磁盘 */
            btPage      = (BtreePage *)page;
            tableSchema = getTableSchema(db->catalog, btPage->tableName);
            if (tableSchema == NULL)
            {
                return GNCDB_TABLESCHEMA_NOT_FOUND;
            }
            uchar = btreePageToByte(btPage);
            if (uchar == NULL)
            {
                return GNCDB_TOBYTE_FALSE;
            }
            rc = osWrite(db->dbFile, uchar, (long)(pageId - 1) * (db->pageCurrentSize), (db->pageCurrentSize));
            if (rc != GNCDB_SUCCESS)
            {
                my_free(uchar);
                return rc;
            }
            // my_free(uchar);
        }
        else if (page->pageType == META_PAGE)
        {
            /* 元数据页转换字节流*/
            uchar = MetaPageToByte((MetaPage *)page);
            if (uchar == NULL)
            {
                return GNCDB_TOBYTE_FALSE;
            }
            rc = osWrite(db->dbFile, uchar, (long)(pageId - 1) * db->pageCurrentSize, db->pageCurrentSize);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(uchar);
                return rc;
            }
        }
        else if (page->pageType == BUCKET_PAGE)
        {
            /* 桶页转换字节流 */

            uchar = BucketPageToByte((BucketPage *)page);
            if (uchar == NULL)
            {
                return GNCDB_TOBYTE_FALSE;
            }
            rc = osWrite(db->dbFile, uchar, (long)(pageId - 1) * db->pageCurrentSize, db->pageCurrentSize);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(uchar);
                return rc;
            }
        }
        else if (page->pageType == HASH_OVERFLOW_PAGE)
        {
            /* 哈希溢出页转换字节流 */
            uchar = HashOverflowPageToByte((HashOverflowPage *)page);
            if (uchar == NULL)
            {
                return GNCDB_TOBYTE_FALSE;
            }
            rc = osWrite(db->dbFile, uchar, (long)(pageId - 1) * db->pageCurrentSize, db->pageCurrentSize);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(uchar);
                return rc;
            }
        }
        else
        {
            return GNCDB_PAGETYPE_NOT_FOUND;
        }
    }
    else
    {
        return GNCDB_FLUSH_FAILED;
    }

    LOG(LOG_INFO, "LRU FLUSH PAGE  Pageid = %d", pageId);
    return GNCDB_SUCCESS;
}

/// <summary>
/// 在close时，把缓存区中的page全部刷新到磁盘中
/// </summary>
/// <param name="pagePool">pagePool</param>
/// <returns>状态码</returns>
int pagePoolFlushAllDirtyPages(struct GNCDB *db)
{
    int rc = 0;
    // int i = 0;
    // int* pageId = NULL;
    HashMapIterator *iteratorBtree = NULL;
    Page *           page          = NULL;
    PageStatus *     status        = NULL;
    BtreePage *      btPage        = NULL;
    TableSchema *    tableSchema   = NULL;
    BYTE *           uchar         = NULL;
    varArrayList *   pageIdArray   = NULL;
    struct PagePool *pagePool      = NULL;
    pagePool                       = db->pagePool;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

    pageIdArray = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (pageIdArray == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_SPACE_LACK;
    }

    iteratorBtree = createHashMapIterator(pagePool->pageMap);
    if (iteratorBtree == NULL)
    {
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return GNCDB_MAP_ITERATOR_FALSE;
    }
    if (pagePool->pageMap->entryCount != 0)
    {
        /* 循环遍历将每个页都刷入磁盘 */
        while (hasNextHashMapIterator(iteratorBtree))
        {
            iteratorBtree = nextHashMapIterator(iteratorBtree);
            if (iteratorBtree == NULL)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }

            page = (Page *)iteratorBtree->entry->value;
            if (page == NULL)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }
            status = (PageStatus *)hashMapGet(pagePool->pageStatus, &(page->id));
            if (status == NULL)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                WriteUnLock(&(pagePool->latch));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                return GNCDB_STATUS_NOT_FOUND;
            }
            if (status->dirty == true)
            {
                rc = varArrayListAdd(pageIdArray, &(page->id));
                if (rc != GNCDB_SUCCESS)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                    WriteUnLock(&(pagePool->latch));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                    freeHashMapIterator(&iteratorBtree);
                    varArrayListDestroy(&pageIdArray);
                    return GNCDB_ARRAY_ADD_FALSE;
                }
            }
            if (page->pageType == LEAF_PAGE || page->pageType == INTERNAL_PAGE)
            {
                if (status->dirty == true)
                {
                    btPage      = (BtreePage *)page;
                    tableSchema = getTableSchema(db->catalog, btPage->tableName);
                    if (tableSchema == NULL)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return GNCDB_TABLESCHEMA_NOT_FOUND;
                    }
                    uchar = btreePageToByte(btPage);
                    if (uchar == NULL)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return GNCDB_TOBYTE_FALSE;
                    }
                    rc =
                        osWrite(db->dbFile, uchar, (long)(page->id - 1) * (db->pageCurrentSize), (db->pageCurrentSize));
                    if (rc != GNCDB_SUCCESS)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return rc;
                    }
                    // my_free(uchar);
                    // uchar = NULL;
                }
            }
            else if (page->pageType == FREE_PAGE)
            {
                uchar = freePageToByte(db, (FreePage *)page);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                    WriteUnLock(&(pagePool->latch));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                    freeHashMapIterator(&iteratorBtree);
                    varArrayListDestroy(&pageIdArray);
                    return GNCDB_TOBYTE_FALSE;
                }
                if (status->dirty == true)
                {
                    rc =
                        osWrite(db->dbFile, uchar, (long)(page->id - 1) * (db->pageCurrentSize), (db->pageCurrentSize));
                    if (rc != GNCDB_SUCCESS)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return rc;
                    }
                }
                // my_free(uchar);
                // uchar = NULL;
            }
            else if (page->pageType == OVERFLOW_PAGE)
            {
                uchar = overflowPageToByte(db, (OverflowPage *)page);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                    WriteUnLock(&(pagePool->latch));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                    freeHashMapIterator(&iteratorBtree);
                    varArrayListDestroy(&pageIdArray);
                    return GNCDB_TOBYTE_FALSE;
                }
                if (status->dirty == true)
                {
                    rc =
                        osWrite(db->dbFile, uchar, (long)(page->id - 1) * (db->pageCurrentSize), (db->pageCurrentSize));
                    if (rc != GNCDB_SUCCESS)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return rc;
                    }
                }
                // my_free(uchar);
                // uchar = NULL;
            }
            else if (page->pageType == META_PAGE)
            {
                if (status->dirty == true)
                {
                    uchar = MetaPageToByte((MetaPage *)page);
                    if (uchar == NULL)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return GNCDB_TOBYTE_FALSE;
                    }
                    rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                    if (rc != GNCDB_SUCCESS)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return rc;
                    }
                }
                // my_free(uchar);
                // uchar = NULL;
            }
            else if (page->pageType == BUCKET_PAGE)
            {
                if (status->dirty == true)
                {
                    uchar = BucketPageToByte((BucketPage *)page);
                    if (uchar == NULL)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return GNCDB_TOBYTE_FALSE;
                    }
                    rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                    if (rc != GNCDB_SUCCESS)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return rc;
                    }
                }
                // my_free(uchar);
                // uchar = NULL;
            }
            else if (page->pageType == HASH_OVERFLOW_PAGE)
            {
                if (status->dirty == true)
                {
                    uchar = HashOverflowPageToByte((HashOverflowPage *)page);
                    if (uchar == NULL)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return GNCDB_TOBYTE_FALSE;
                    }
                    rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                    if (rc != GNCDB_SUCCESS)
                    {
                        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
                        WriteUnLock(&(pagePool->latch));
                        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
                        freeHashMapIterator(&iteratorBtree);
                        varArrayListDestroy(&pageIdArray);
                        return rc;
                    }
                }
                // my_free(uchar);
                // uchar = NULL;
            }
        }
    }
    freeHashMapIterator(&iteratorBtree);

    // for (i = 0; i < pageIdArray->elementCount; ++i)
    // {
    // 	pageId = varArrayListGet(pageIdArray, i);
    // 	if (pageId == NULL)
    // 	{
    // 		break;
    // 	}
    // 	rc = pagePoolRemovePageUnlock(pagePool, *pageId);
    // 	if (rc != GNCDB_SUCCESS && rc != GNCDB_NO_REMOVE)
    // 	{
    // 		varArrayListDestroy(&pageIdArray);
    //         WriteUnLock(&(pagePool->latch));
    // 		return rc;
    // 	}
    // }

    varArrayListDestroy(&pageIdArray);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    return GNCDB_SUCCESS;
}

/// <summary>
/// btreePage转化FreePage
/// </summary>
/// <param name="btreePage"></param>
/// <param name="db"></param>
/// <returns></returns>
int btreePageToFreePage(struct BtreePage *btreePage, struct GNCDB *db)
{
    int         setPin            = 0;
    int         rc                = 0;
    FreePage *  freePage          = NULL;
    int         currentFreePageId = db->firstFreePid;
    PageStatus *status            = NULL;
    int         pageId            = 0;

    if (btreePage == NULL || db == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    pageId = btreePage->page.id;
    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    status = getPageStatus(db->pagePool, btreePage->page.id);
    setPin = status->pin;
    /* 构建freepage */
    // freePage = freePageConstructWithOutData(btreePage->page.id, db->firstFreePid);
    // if (freePage == NULL) {

    // 	LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    //     WriteUnLock(&(db->latch));
    // 	LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    // 	return GNCDB_SPACE_LACK;
    // }
    // /* 增加freepage */
    // db->firstFreePid = freePage->page.id;
    // setPageStatusPinDown(db->pagePool, btreePage->page.id);

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    rc = pagePoolRemovePageUnlock(db->pagePool, btreePage->page.id, (struct Page *)btreePage);
    if (rc != GNCDB_SUCCESS)
    {
        freePageDestroy(&freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    rc = pagePoolConstructNewFreePageUnlock(&freePage, db->pagePool, db, btreePage->page.id, db->firstFreePid);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    /* 增加freepage */
    // db->firstFreePid = freePage->page.id;

    // rc = pagePoolAddPageUnlock((Page*)freePage, freePage->page.id, db);
    // if (rc != GNCDB_SUCCESS) {
    // 	db->firstFreePid = currentFreePageId;
    // 	freePageDestroy(&freePage);

    //     LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    //     WriteUnLock(&(db->pagePool->latch));
    //     LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    // 	LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    //     WriteUnLock(&(db->latch));
    // 	LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    // 	return rc;
    // }
    status      = hashMapGet(db->pagePool->pageStatus, &pageId);
    status->pin = setPin;
    /* 设置脏页 */
    rc = setPageStatusDirtyUnlock(db->pagePool, freePage->page.id, status);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

        return rc;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    return GNCDB_SUCCESS;
}

/// <summary>
/// overflowPage转化为freePage
/// </summary>
/// <param name="overflowPage"></param>
/// <param name="db"></param>
/// <returns></returns>
int overflowPageToFreePage(OverflowPage *overflowPage, struct GNCDB *db)
{
    int         rc                = 0;
    int         setPin            = 0;
    FreePage *  freePage          = NULL;
    int         currentFreePageId = 0;
    PageStatus *status            = NULL;
    int         pageId            = 0;
    if (overflowPage == NULL || db == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    pageId = overflowPage->page.id;
    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");

    status = getPageStatus(db->pagePool, overflowPage->page.id);
    setPin = status->pin;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    rc = pagePoolRemovePageUnlock(db->pagePool, overflowPage->page.id, (struct Page *)overflowPage);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);

        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&db->pagePool->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&db->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    /* 记录当前的freepage */
    currentFreePageId = db->firstFreePid;

    rc = pagePoolConstructNewFreePageUnlock(&freePage, db->pagePool, db, overflowPage->page.id, db->firstFreePid);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&db->pagePool->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&db->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    // freePage = freePageConstructWithOutData(overflowPage->page.id, db->firstFreePid);
    // if (freePage == NULL) {
    // 	return GNCDB_SPACE_LACK;
    // }

    // db->firstFreePid = freePage->page.id;

    // rc = pagePoolAddPageUnlock((Page*)freePage, freePage->page.id, db);
    // if (rc != GNCDB_SUCCESS) {
    //     db->firstFreePid = currentFreePageId;
    //     freePageDestroy(&freePage);

    //     LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    //     WriteUnLock(&(db->pagePool->latch));
    //     LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    //     LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    //     WriteUnLock(&(db->latch));
    //     LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    //     return rc;
    // }
    status      = hashMapGet(db->pagePool->pageStatus, &pageId);
    status->pin = setPin;
    /* 设置脏页 */
    rc = setPageStatusDirtyUnlock(db->pagePool, freePage->page.id, status);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);

        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&db->pagePool->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&db->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }

    rc = setPageStatusDirtyUnlock(db->pagePool, pageId, status);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);

        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&db->pagePool->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&db->latch);
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }

    status      = hashMapGet(db->pagePool->pageStatus, &pageId);
    status->pin = setPin;

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&db->pagePool->latch);
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&db->latch);
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    return GNCDB_SUCCESS;
}

/// <summary>
/// metaPage转化FreePage
/// </summary>
/// <param name="metaPage"></param>
/// <param name="db"></param>
/// <returns></returns>
int metaPageToFreePage(struct MetaPage *metaPage, struct GNCDB *db)
{
    int         setPin            = 0;
    int         rc                = 0;
    FreePage *  freePage          = NULL;
    int         currentFreePageId = db->firstFreePid;
    PageStatus *status            = NULL;
    int         pageId            = 0;

    if (metaPage == NULL || db == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    pageId = metaPage->page.id;
    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    status = getPageStatus(db->pagePool, metaPage->page.id);
    setPin = status->pin;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    rc = pagePoolRemovePageUnlock(db->pagePool, metaPage->page.id, (struct Page *)metaPage);
    if (rc != GNCDB_SUCCESS)
    {
        freePageDestroy(&freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    rc = pagePoolConstructNewFreePageUnlock(&freePage, db->pagePool, db, metaPage->page.id, db->firstFreePid);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }

    status      = hashMapGet(db->pagePool->pageStatus, &pageId);
    status->pin = setPin;
    /* 设置脏页 */
    rc = setPageStatusDirtyUnlock(db->pagePool, freePage->page.id, status);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

        return rc;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    return GNCDB_SUCCESS;
}

/// <summary>
/// bucketPage转化FreePage
/// </summary>
/// <param name="bucketPage"></param>
/// <param name="db"></param>
/// <returns></returns>
int bucketPageToFreePage(struct BucketPage *bucketPage, struct GNCDB *db)
{
    int         setPin            = 0;
    int         rc                = 0;
    FreePage *  freePage          = NULL;
    int         currentFreePageId = db->firstFreePid;
    PageStatus *status            = NULL;
    int         pageId            = 0;

    if (bucketPage == NULL || db == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    pageId = bucketPage->page.id;
    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    status = getPageStatus(db->pagePool, bucketPage->page.id);
    setPin = status->pin;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    rc = pagePoolRemovePageUnlock(db->pagePool, bucketPage->page.id, (struct Page *)bucketPage);
    if (rc != GNCDB_SUCCESS)
    {
        freePageDestroy(&freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    rc = pagePoolConstructNewFreePageUnlock(&freePage, db->pagePool, db, bucketPage->page.id, db->firstFreePid);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }

    status      = hashMapGet(db->pagePool->pageStatus, &pageId);
    status->pin = setPin;
    /* 设置脏页 */
    rc = setPageStatusDirtyUnlock(db->pagePool, freePage->page.id, status);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

        return rc;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    return GNCDB_SUCCESS;
}
/// <summary>
/// hashOverflowtPage转化FreePage
/// </summary>
/// <param name="hashOverflowtPage"></param>
/// <param name="db"></param>
/// <returns></returns>
int hashOverflowPageToFreePage(struct HashOverflowPage *hashOverflowtPage, struct GNCDB *db)
{
    int         setPin            = 0;
    int         rc                = 0;
    FreePage *  freePage          = NULL;
    int         currentFreePageId = db->firstFreePid;
    PageStatus *status            = NULL;
    int         pageId            = 0;

    if (hashOverflowtPage == NULL || db == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    pageId = hashOverflowtPage->page.id;
    LOG(LOG_TRACE, "SLOCKing:%s", "db");
    WriteLock(&(db->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "db");
    status = getPageStatus(db->pagePool, hashOverflowtPage->page.id);
    setPin = status->pin;

    LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
    WriteLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
    rc = pagePoolRemovePageUnlock(db->pagePool, hashOverflowtPage->page.id, (struct Page *)hashOverflowtPage);
    if (rc != GNCDB_SUCCESS)
    {
        freePageDestroy(&freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }
    rc = pagePoolConstructNewFreePageUnlock(&freePage, db->pagePool, db, hashOverflowtPage->page.id, db->firstFreePid);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");
        return rc;
    }

    status      = hashMapGet(db->pagePool->pageStatus, &pageId);
    status->pin = setPin;
    /* 设置脏页 */
    rc = setPageStatusDirtyUnlock(db->pagePool, freePage->page.id, status);
    if (rc != GNCDB_SUCCESS)
    {
        db->firstFreePid = currentFreePageId;
        freePageDestroy(&freePage);
        pagePoolWithdrawPage(db->pagePool, (Page *)freePage);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
        WriteUnLock(&(db->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

        return rc;
    }

    LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
    WriteUnLock(&(db->pagePool->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
    LOG(LOG_TRACE, "SUNLOCKing:%s", "db");
    WriteUnLock(&(db->latch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "db");

    return GNCDB_SUCCESS;
}

/// <summary>
/// 深拷贝oldpage后，修改页之前调用
/// </summary>
/// <param name="db"></param>
/// <param name="tx"></param>
/// <param name="pid"></param>
/// <param name="oldPage"></param>
/// <returns></returns>
int pagePoolAddFlushOld(struct GNCDB *db, struct Transaction *tx, int pid, void *oldPage)
{
    PageControlNode *pageControlNodeInMap = NULL;
    PageControlNode *pageControlNode      = NULL;

    if (oldPage == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    WriteLock(&db->pagePool->latch);
    if (!pageControlMapExists(db->pagePool, pid))
    {
        // LOG(LOG_TRACE, "pageControlMap LOCKing");
        // WriteLock(&(db->pagePool->pageControlMaplatch));
        // LOG(LOG_TRACE, "pageControlMap LOCKend");

        /* 新建节点 */
        pageControlNode = my_malloc(sizeof(PageControlNode));
        if (pageControlNode == NULL)
        {
            // WriteUnLock(&(db->pagePool->pageControlMaplatch));
            LOG(LOG_TRACE, "pageControlMap UNLOCKend");
            WriteUnLock(&db->pagePool->latch);
            return GNCDB_MEM;
        }
        /* 初始化锁 */
        ReadWriteLockInit(&(pageControlNode->latch));
        pageControlNode->tid     = tx->id;
        pageControlNode->oldPage = oldPage;
        pageControlNode->newPage = hashMapGet(db->pagePool->pageMap, &pid);
        if (pageControlNode->newPage == NULL)
        {
            // WriteUnLock(&(db->pagePool->pageControlMaplatch));
            LOG(LOG_TRACE, "pageControlMap UNLOCKend");
            WriteUnLock(&db->pagePool->latch);
            return GNCDB_NOT_FOUND;
        }
        LOG(LOG_TRACE, "SLOCKing:%s", "pageControlMap");
        WriteLock(&(db->pagePool->pageControlMaplatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "pageControlMap");
        hashMapPut(db->pagePool->pageControlMap, &pid, pageControlNode);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pageControlMap");
        WriteUnLock(&(db->pagePool->pageControlMaplatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pageControlMap");
        hashMapPut(tx->txFlushMap, &pid, pageControlNode);

        LOG(LOG_TRACE, "SLOCKing:%s", "flushMap");
        WriteLock(&(db->pagePool->latchFlushMap));
        LOG(LOG_TRACE, "SLOCKend:%s", "flushMap");
        hashMapPut(db->pagePool->flushMap, &pid, pageControlNode);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
        WriteUnLock(&(db->pagePool->latchFlushMap));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");

        LOG(LOG_DEBUG, "NEW CONTROLNODE pageid = %d", pid);
    }
    else
    {
        LOG(LOG_TRACE, "SLOCKing:%s", "pageControlMap");
        WriteLock(&(db->pagePool->pageControlMaplatch));
        LOG(LOG_TRACE, "SLOCKend:%s", "pageControlMap");
        pageControlNodeInMap = (PageControlNode *)hashMapGet(db->pagePool->pageControlMap, &pid);
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pageControlMap");
        WriteUnLock(&(db->pagePool->pageControlMaplatch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pageControlMap");
        /* 在pageControlMap有了节点，不再进行添加 */
        if (pageControlNodeInMap == NULL)
        {
            WriteUnLock(&db->pagePool->latch);
            return GNCDB_NOT_FOUND;
        }
        LOG(LOG_TRACE, "flushPageNode LOCKing:pageId = %d", pid);
        WriteLock(&(pageControlNodeInMap->latch));
        LOG(LOG_TRACE, "flushPageNode LOCKend:pageId = %d", pid);
        // pageControlNodeInMap->tid != tx->id：可能之前别的页修改过该页，但不是当前事务
        // pageControlNodeInMap->oldPage == NULL: 之前的事务已经提交/回滚，将PCN中的oldPage设置为null了
        if (pageControlNodeInMap->tid != tx->id)
        {
            if (pageControlNodeInMap->oldPage == NULL)
            {
                pageControlNodeInMap->tid     = tx->id;
                pageControlNodeInMap->oldPage = oldPage;
                pageControlNodeInMap->newPage = hashMapGet(db->pagePool->pageMap, &pid);
                if (pageControlNodeInMap->newPage == NULL)
                {
                    WriteUnLock(&(pageControlNodeInMap->latch));
                    WriteUnLock(&db->pagePool->latch);
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pid);
                    return GNCDB_NOT_FOUND;
                }
            }
            else
            {
                // 当前事务修改的pcn之前别的事务已经修改过了，但是未提交/回滚，即未经历pagePoolAddFlushNew函数将old置空
                printf("another transaction: %d is modify this page: %d\n", pageControlNodeInMap->tid, pid);
            }
        }
        WriteUnLock(&(pageControlNodeInMap->latch));
        LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pid);

        hashMapPut(tx->txFlushMap, &pid, pageControlNodeInMap);

        LOG(LOG_DEBUG, "UPDATE CONTROLNODE oldpageId = %d newpageId = %d", ((Page *)oldPage)->id, pid);
    }
    WriteUnLock(&db->pagePool->latch);

    return GNCDB_SUCCESS;
}
/// <summary>
/// 修改页之后调用
/// </summary>
/// <param name="db"></param>
/// <param name="tx"></param>
/// <param name="pid"></param>
/// <returns></returns>


int pagePoolAddFlushNew(struct GNCDB *db, struct Transaction *tx)
{
    int              pageId             = 0;
    PageControlNode *flushPageNode      = NULL;
    HashMapIterator *iteratorTxFlushMap = NULL;
    Page *           newPageTemp        = NULL;

    iteratorTxFlushMap = createHashMapIterator(tx->txFlushMap);
    if (iteratorTxFlushMap == NULL)
    {
        return GNCDB_MAP_ITERATOR_FALSE;
    }
    if (tx->txFlushMap->entryCount != 0)
    {
        while (hasNextHashMapIterator(iteratorTxFlushMap))
        {
            iteratorTxFlushMap = nextHashMapIterator(iteratorTxFlushMap);
            if (iteratorTxFlushMap == NULL)
            {
                my_free(iteratorTxFlushMap);
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }
            pageId        = *(int *)iteratorTxFlushMap->entry->key;
            flushPageNode = (PageControlNode *)iteratorTxFlushMap->entry->value;

            if (flushPageNode == NULL)
            {
                my_free(iteratorTxFlushMap);
                return GNCDB_NOT_FOUND;
            }

            LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
            WriteLock(&(db->pagePool->latch));
            LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");
            newPageTemp = hashMapGet(db->pagePool->pageMap, &pageId);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(db->pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

            LOG(LOG_TRACE, "flushPageNode LOCKing:pageId = %d", pageId);

            WriteLock(&(flushPageNode->latch));
            LOG(LOG_TRACE, "flushPageNode LOCKend:pageId = %d", pageId);

            if (flushPageNode->tid != tx->id)
            {
                WriteUnLock(&(flushPageNode->latch));
                LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pageId);
                my_free(iteratorTxFlushMap);
                return GNCDB_NOT_FOUND;
            }

            // * 这个函数主要的作用就是这部分:oldPage置空，newPage替换为最新页 */
            flushPageNode->oldPage = NULL;
            flushPageNode->newPage = newPageTemp;
            if (flushPageNode->newPage == NULL)
            {
                WriteUnLock(&(flushPageNode->latch));
                LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pageId);
                my_free(iteratorTxFlushMap);
                return GNCDB_NOT_FOUND;
            }
            /* 提交事务加入待刷新列表 */
            WriteUnLock(&(flushPageNode->latch));
            LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pageId);
        }
    }

    my_free(iteratorTxFlushMap);

    return GNCDB_SUCCESS;
}

int pageControlMapExists(struct PagePool *pagePool, int pid)
{
    bool rc = 0;

    LOG(LOG_TRACE, "SLOCKing:%s", "pageControlMap");
    WriteLock(&(pagePool->pageControlMaplatch));
    LOG(LOG_TRACE, "SLOCKend:%s", "pageControlMap");
    rc = hashMapExists(pagePool->pageControlMap, &pid);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "pageControlMap");
    WriteUnLock(&(pagePool->pageControlMaplatch));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "pageControlMap");

    return rc;
}

int flushMapExists(struct PagePool *pagePool, int pid)
{
    bool rc = 0;
    LOG(LOG_TRACE, "SLOCKing:%s", "flushMap");
    WriteLock(&(pagePool->latchFlushMap));
    LOG(LOG_TRACE, "SLOCKend:%s", "flushMap");
    rc = hashMapExists(pagePool->flushMap, &pid);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
    WriteUnLock(&(pagePool->latchFlushMap));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
    return rc;
}

/**
 * @description: 在创建checkpoint后，把内存缓冲池中flushMap中的需要flush的page全部刷盘（不移除内存）
 * @return {*} 结果
 */
int FlushCheckPoint(struct GNCDB *db)
{
    int              rc               = 0;
    HashMapIterator *iteratorflushMap = NULL;
    Page *           page             = NULL;
    BtreePage *      btPage           = NULL;
    TableSchema *    tableSchema      = NULL;
    BYTE *           uchar            = NULL;
    PageControlNode *flushPageNode    = NULL;
    BYTE *           intBuf           = NULL;
    int              offset           = 0;
    int *            pageId           = NULL;

    LOG(LOG_TRACE, "SLOCKing:%s", "flushMap");
    WriteLock(&(db->pagePool->latchFlushMap));
    LOG(LOG_TRACE, "SLOCKend:%s", "flushMap");
    iteratorflushMap = createHashMapIterator(db->pagePool->flushMap);
    if (iteratorflushMap == NULL)
    {
        return GNCDB_MAP_ITERATOR_FALSE;
    }
    if (db->pagePool->flushMap->entryCount != 0)
    {
        /* 循环遍历将每个页都刷入磁盘 */
        while (hasNextHashMapIterator(iteratorflushMap))
        {
            iteratorflushMap = nextHashMapIterator(iteratorflushMap);
            if (iteratorflushMap == NULL)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                WriteUnLock(&(db->pagePool->latchFlushMap));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }
            pageId        = (int *)iteratorflushMap->entry->key;
            flushPageNode = (PageControlNode *)iteratorflushMap->entry->value;

            if (flushPageNode == NULL)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                WriteUnLock(&(db->pagePool->latchFlushMap));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                return GNCDB_NOT_FOUND;
            }

            LOG(LOG_TRACE, "flushPageNode LOCKing:pageId = %d", *pageId);
            WriteLock(&(flushPageNode->latch));
            LOG(LOG_TRACE, "flushPageNode LOCKend:pageId = %d", *pageId);

            if (flushPageNode->oldPage == NULL)
            {
                page = (Page *)(flushPageNode->newPage);
            }
            /* 存在oldpage说明修改该页的事务在进行中并未提交，故需要刷旧页 */
            else
            {
                page = (Page *)(flushPageNode->oldPage);
            }

            if (page == NULL)
            {
                LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                WriteUnLock(&(db->pagePool->latchFlushMap));
                LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                WriteUnLock(&(flushPageNode->latch));
                LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }
            LOG(LOG_INFO, "CKP FLUSH PAGE  Pageid = %d", *pageId);
            if (page->pageType == LEAF_PAGE || page->pageType == INTERNAL_PAGE)
            {
                btPage      = (BtreePage *)page;
                tableSchema = getTableSchema(db->catalog, btPage->tableName);
                if (tableSchema == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return GNCDB_TABLESCHEMA_NOT_FOUND;
                }
                uchar = btreePageToByte(btPage);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return GNCDB_TOBYTE_FALSE;
                }
                rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                if (rc != GNCDB_SUCCESS)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return rc;
                }
                // my_free(uchar);
                // uchar = NULL;
            }
            else if (page->pageType == FREE_PAGE)
            {
                uchar = freePageToByte(db, (FreePage *)page);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return GNCDB_TOBYTE_FALSE;
                }
                rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                if (rc != GNCDB_SUCCESS)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return rc;
                }
                // my_free(uchar);
                uchar = NULL;
            }
            else if (page->pageType == OVERFLOW_PAGE)
            {
                uchar = overflowPageToByte(db, (OverflowPage *)page);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return GNCDB_TOBYTE_FALSE;
                }
                rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                if (rc != GNCDB_SUCCESS)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return rc;
                }
                // my_free(uchar);
                uchar = NULL;
            }
            else if (page->pageType == META_PAGE)
            {
                uchar = MetaPageToByte((MetaPage *)page);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return GNCDB_TOBYTE_FALSE;
                }
                rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                if (rc != GNCDB_SUCCESS)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return rc;
                }
                // my_free(uchar);
                uchar = NULL;
            }
            else if (page->pageType == BUCKET_PAGE)
            {
                uchar = BucketPageToByte((BucketPage *)page);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return GNCDB_TOBYTE_FALSE;
                }
                rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                if (rc != GNCDB_SUCCESS)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return rc;
                }
                // my_free(uchar);
                uchar = NULL;
            }
            else if (page->pageType == HASH_OVERFLOW_PAGE)
            {
                uchar = HashOverflowPageToByte((HashOverflowPage *)page);
                if (uchar == NULL)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return GNCDB_TOBYTE_FALSE;
                }
                rc = osWrite(db->dbFile, uchar, (long)(page->id - 1) * db->pageCurrentSize, db->pageCurrentSize);
                if (rc != GNCDB_SUCCESS)
                {
                    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
                    WriteUnLock(&(db->pagePool->latchFlushMap));
                    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");
                    WriteUnLock(&(flushPageNode->latch));
                    LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
                    freeHashMapIterator(&iteratorflushMap);
                    return rc;
                }
                // my_free(uchar);
                uchar = NULL;
            }
            /* 刷页完成， flushMap移除该entry并且释放该节点的锁*/
            WriteUnLock(&(flushPageNode->latch));
            LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", *pageId);
        }
    }

    hashMapClear(db->pagePool->flushMap);
    LOG(LOG_TRACE, "SUNLOCKing:%s", "flushMap");
    WriteUnLock(&(db->pagePool->latchFlushMap));
    LOG(LOG_TRACE, "SUNLOCKend:%s", "flushMap");

    freeHashMapIterator(&iteratorflushMap);

    //下面刷totalpagenum和freepageid
    intBuf = (BYTE *)my_malloc(INT_SIZE * 2);
    if (intBuf == NULL)
    {
        return GNCDB_MEM;
    }
    rc = writeInt(db->totalPageNum, &intBuf[0], &offset);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(intBuf);
        return rc;
    }
    rc = writeInt(db->firstFreePid, &intBuf[INT_SIZE], &offset);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(intBuf);
        return rc;
    }
    rc = osWrite(db->dbFile, intBuf, BYTES64, INT_SIZE * 2);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(intBuf);
        return rc;
    }
    my_free(intBuf);
    (void)pageId;
    return GNCDB_SUCCESS;
}
/// <summary>
/// 回滚页
/// </summary>
/// <param name="db"></param>
/// <param name="tx"></param>
/// <param name="pid"></param>
/// <returns></returns>
int pagePoolRollBackFlush(struct GNCDB *db, struct Transaction *tx)
{
    int              pageId             = 0;
    PageControlNode *flushPageNode      = NULL;
    HashMapIterator *iteratorTxFlushMap = NULL;

    iteratorTxFlushMap = createHashMapIterator(tx->txFlushMap);
    if (iteratorTxFlushMap == NULL)
    {
        return GNCDB_MAP_ITERATOR_FALSE;
    }
    if (tx->txFlushMap->entryCount != 0)
    {
        while (hasNextHashMapIterator(iteratorTxFlushMap))
        {
            iteratorTxFlushMap = nextHashMapIterator(iteratorTxFlushMap);
            if (iteratorTxFlushMap == NULL)
            {
                my_free(iteratorTxFlushMap);
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }
            pageId        = *(int *)iteratorTxFlushMap->entry->key;
            flushPageNode = (PageControlNode *)iteratorTxFlushMap->entry->value;

            if (flushPageNode == NULL)
            {
                my_free(iteratorTxFlushMap);
                return GNCDB_NOT_FOUND;
            }

            LOG(LOG_TRACE, "flushPageNode LOCKing:pageId = %d", pageId);
            WriteLock(&(flushPageNode->latch));
            LOG(LOG_TRACE, "flushPageNode LOCKend:pageId = %d", pageId);

            if (flushPageNode->tid != tx->id)
            {
                WriteUnLock(&(flushPageNode->latch));
                LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pageId);
                my_free(iteratorTxFlushMap);
                return GNCDB_NOT_FOUND;
            }

            /* oldPage为空代表该页没有被事务正在修改 */
            flushPageNode->oldPage = NULL;
            WriteLock(&db->pagePool->latch);
            flushPageNode->newPage = hashMapGet(db->pagePool->pageMap, &pageId);
            WriteUnLock(&db->pagePool->latch);
            if (flushPageNode->newPage == NULL)
            {
                WriteUnLock(&(flushPageNode->latch));
                LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pageId);
                my_free(iteratorTxFlushMap);
                return GNCDB_NOT_FOUND;
            }
            WriteUnLock(&(flushPageNode->latch));
            LOG(LOG_TRACE, "flushPageNode UNLOCKend:pageId = %d", pageId);
        }
    }

    my_free(iteratorTxFlushMap);

    return GNCDB_SUCCESS;
}

/// <summary>
/// PCN移出并销毁
/// </summary>
/// <param name="pagepool"></param>
/// <param name="tx"></param>
/// <param name="pid"></param>
/// <returns></returns>
int pagePoolRemovePCN(PagePool *pagepool, int pid)
{
    PageControlNode *PCN = NULL;
    if (!pageControlMapExists(pagepool, pid))
    {
        return GNCDB_SUCCESS;
    }
    WriteLock(&pagepool->pageControlMaplatch);
    // 移出PCNmap
    PCN = hashMapGet(pagepool->pageControlMap, &pid);
    if (PCN == NULL)
    {
        WriteUnLock(&pagepool->pageControlMaplatch);
        return GNCDB_SUCCESS;
    }
    hashMapRemove(pagepool->pageControlMap, &pid);
    WriteUnLock(&pagepool->pageControlMaplatch);
    // 移出flushmap
    if (flushMapExists(pagepool, pid))
    {
        WriteLock(&pagepool->latchFlushMap);
        hashMapRemove(pagepool->flushMap, &pid);
        WriteUnLock(&pagepool->latchFlushMap);
    }
    // 销毁
    ReadWriteLockDestroy(&PCN->latch);
    my_free(PCN);
    return GNCDB_SUCCESS;
}

/* 输出页的pin */
int printfPagePin(PagePool *pagePool)
{
    HashMapIterator *iterator = NULL;
    PageStatus *     status   = NULL;
    int              id       = 0;
    iterator                  = createHashMapIterator(pagePool->pageStatus);
    if (iterator == NULL)
    {
        return GNCDB_MAP_ITERATOR_FALSE;
    }
    printf("PagePin:");
    while (hasNextHashMapIterator(iterator))
    {
        iterator = nextHashMapIterator(iterator);
        status   = iterator->entry->value;
        id       = *(int *)(iterator->entry->key);
        printf("id %d: %d   ", id, status->pin);
        if (status->pin != 0)
        {
            printf("error");
        }
        fflush(stdout);
    }
    printf("\n");
    fflush(stdout);
    freeHashMapIterator(&iterator);
    return GNCDB_SUCCESS;
}

BYTE *pageToBytes(GNCDB *db, Page *page)
{
    BYTE *bytes = NULL;

    if (page == NULL)
    {
        return NULL;
    }
    switch (page->pageType)
    {
        case LEAF_PAGE:
        case INTERNAL_PAGE:
            bytes = btreePageToByte((BtreePage *)page);
            break;
        case BUCKET_PAGE:
            bytes = BucketPageToByte((BucketPage *)page);
            break;
        case META_PAGE:
            bytes = MetaPageToByte((MetaPage *)page);
            break;
        case FREE_PAGE:
            bytes = freePageToByte(db, (FreePage *)page);
            break;
        case OVERFLOW_PAGE:
            bytes = overflowPageToByte(db, (OverflowPage *)page);
            break;
        case HASH_OVERFLOW_PAGE:
            bytes = HashOverflowPageToByte((HashOverflowPage *)page);
            break;
        default:
            return NULL;
            break;
    }

    return bytes;
}
