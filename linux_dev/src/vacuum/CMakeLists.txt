cmake_minimum_required(VERSION 3.10)
project(PROJECT_GNCDB C)

# 生成的可执行文件拥有符号表，可以gdb调试
add_definitions("-Wall -g")

# 添加编译选项，要求变量定义限定在作用域的开头位置
add_compile_options("-Werror=declaration-after-statement")

# 设置头文件搜索路径
include_directories(${CMAKE_SOURCE_DIR}/../../malloc)
include_directories(${CMAKE_SOURCE_DIR}/../cachemanager)
include_directories(${CMAKE_SOURCE_DIR}/../common)
include_directories(${CMAKE_SOURCE_DIR}/../executor)
include_directories(${CMAKE_SOURCE_DIR}/../system)
include_directories(${CMAKE_SOURCE_DIR}/../storage)
include_directories(${CMAKE_SOURCE_DIR}/../transaction)
include_directories(${CMAKE_SOURCE_DIR}/../utils)
include_directories(${CMAKE_SOURCE_DIR}/../gncdb_api)

# 设置可执行文件输出目录
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/build/bin)
set(CMAKE_BINARY_DIR ${CMAKE_SOURCE_DIR}/build)

# 设置默认构建类型为Release
if(NOT CMAKE_BUILD_TYPE)
  set(CMAKE_BUILD_TYPE Debug)
endif()

# 为Release模式添加-O2优化选项
# set(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} -O2")
# set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2")

# 添加可执行文件
aux_source_directory(${CMAKE_SOURCE_DIR}/ SRC_LIST)
add_executable(vacuum ${SRC_LIST} ${CMAKE_SOURCE_DIR}/../utils/vararraylist.c  ${CMAKE_SOURCE_DIR}/../utils/hashmap.c ${CMAKE_SOURCE_DIR}/../system/os.c)

target_link_libraries(vacuum m)
