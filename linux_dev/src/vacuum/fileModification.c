#include "fileModification.h"
#include "dbInfo.h"
#include "gncdbconstant.h"
#include "typedefine.h"
#include "vararraylist.h"
#include "os.h"
#include <stdbool.h>
#include <stdio.h>
#include "gncdbconstant.h"
#include "adaptiveSort.h"

int findFirstGreaterThan(int *arr, int size, int value)
{
  int low  = 0;
  int high = size - 1;
  int mid  = 0;

  while (low <= high) {
    mid = low + (high - low) / 2;  // 防止溢出
    if (arr[mid] > value) {
      high = mid - 1;
    } else {
      low = mid + 1;
    }
  }
  return low;
}

int bfsTraverseMater(FILE *fp, int *free_list, DBInfo *dbInfo)
{
  int           rc;
  int           pid = 2;  // 初始化为master的根结点
  int          *pid_ptr;
  int           entryNum;
  int           keyLen = 0;
  int           nextBtreePid;
  int           internalEntryPid;  // 内部页entry的pid
  int           leafRootPageId;    // master中的rootPageId
  int           offset = 0;
  int           subNum;
  char          pageType;
  bool          findLeaf = false;
  int           currentNodeCount;
  int           LEAF_HEAD_SIZE        = 13;                           // 叶子页头部大小
  int           MASTER_TUPLE_LEN      = 8 * INT_SIZE + 2 * 16 + 256;  // master表中的tuple长度固定
  int           MASTER_BITMAP_LEN     = (MASTERCOLNUM - 1) / 8 + 1;
  int           MASTER_LEAF_ENTRY_LEN = MASTER_TUPLE_LEN + MASTER_BITMAP_LEN;  // master叶子页中的entry=bitmap+tuple
  BYTE         *page                  = my_malloc(dbInfo->page_size);
  varArrayList *pidArray              = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);  // 存储结点

  // 将master表的根结点（2）加入队列中
  rc = varArrayListAdd(pidArray, &pid);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&pidArray);
    my_free(page);
    return rc;
  }

  while (pidArray->elementCount > 0 && !findLeaf) {
    currentNodeCount = pidArray->elementCount;
    for (int i = 0; i < currentNodeCount; i++) {
      pid_ptr = (int *)varArrayListGet(pidArray, i);
      pid     = *pid_ptr;

      // 获取页面
      rc = osRead(fp, (pid - 1) * dbInfo->page_size, dbInfo->page_size, &page);  // 读取页面
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&pidArray);
        my_free(page);
        return rc;
      }
      offset = 0;
      rc     = readChar(&pageType, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&pidArray);
        my_free(page);
        return rc;
      }

      if (pageType == (char)LEAF_PAGE) {  // 搜索到了最下层的叶子页了，停止bfs。此时pidArray中全是leafPage的pid
        findLeaf = true;
        break;
      }

      // 内部页header：type(char) + keyLength(int) + entryNum(int) + nextPageId(int)
      if (pageType == (char)INTERNAL_PAGE) {

        // 读取keyLength
        rc = readInt(&keyLen, page + offset, &offset);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pidArray);
          my_free(page);
          return rc;
        }

        // 读取entryNum
        rc = readInt(&entryNum, page + offset, &offset);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pidArray);
          my_free(page);
          return rc;
        }

        // 读取nextPageId
        rc = readInt(&nextBtreePid, page + offset, &offset);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pidArray);
          my_free(page);
          return rc;
        }

        //* 将当前内部结点的所有entryPid加入队列 */
        offset = LEAF_HEAD_SIZE;
        for (int entryIndex = 0; entryIndex < entryNum; entryIndex++) {
          rc = readInt(&internalEntryPid, page + offset, &offset);  // 读取entry的pageID
          if (rc != GNCDB_SUCCESS) {
            varArrayListDestroy(&pidArray);
            my_free(page);
            return rc;
          }
          rc = varArrayListAdd(pidArray, &internalEntryPid);
          if (rc != GNCDB_SUCCESS) {
            varArrayListDestroy(&pidArray);
            my_free(page);
            return rc;
          }
          offset += keyLen;
        }

        //* 将nextBtreePid加入队列 */
        rc = varArrayListAdd(pidArray, &nextBtreePid);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pidArray);
          my_free(page);
          return rc;
        }
      } else {
        // master B+树中不可能存在除了内部页和叶子页之外的其他类型的页面
        varArrayListDestroy(&pidArray);
        my_free(page);
        return GNCDB_NO_PAGE;
      }

      rc = varArrayListRemove(pidArray, pid_ptr);  // 将当前结点的所有子结点全部加入之后再删除该结点
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&pidArray);
        my_free(page);
        return rc;
      }
    }
  }

  // 将所有的leafPage中所有tuple的rootPid修改
  currentNodeCount = pidArray->elementCount;
  for (int i = 0; i < currentNodeCount; i++) {
    // 读取页面
    pid = *(int *)varArrayListGet(pidArray, i);
    rc  = osRead(fp, (pid - 1) * dbInfo->page_size, dbInfo->page_size, &page);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pidArray);
      my_free(page);
      return rc;
    }
    offset = 0;  // 页内偏移量初始化为0

    rc = readChar(&pageType, page + offset, &offset);  // 读取pageType
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pidArray);
      my_free(page);
      return GNCDB_NO_PAGE;
    }
    if (pageType != (char)LEAF_PAGE) {  // 非叶子页，发生错误
      varArrayListDestroy(&pidArray);
      my_free(page);
      return GNCDB_NO_PAGE;
    }

    // 跳过keyLength
    offset += sizeof(int);
    // 读取entryNum
    rc = readInt(&entryNum, page + offset, &offset);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pidArray);
      my_free(page);
      return rc;
    }

    // 遍历当前叶子页的tuple
    offset = LEAF_HEAD_SIZE;
    for (int j = 1; j <= entryNum; j++) {
      offset = offset + MASTER_BITMAP_LEN + 3 * INT_SIZE + 2 * 16;  // 跳过tuple前的bitmap和前5列

      //* 修改rootPageId */
      // 先读取rootPageId
      rc = readInt(&leafRootPageId, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&pidArray);
        my_free(page);
        return rc;
      }

      // 计算新页号并重新写入
      subNum = findFirstGreaterThan(free_list, dbInfo->free_page_count, leafRootPageId);  // 计算页号减少的数量
      if (subNum > 0) {                                                                   // 判断是否需要变化页号
        leafRootPageId -= subNum;                                                         // 计算变换后的新页号
        offset -= sizeof(int);                                                            // 偏移量矫正
        rc = writeInt(leafRootPageId, page + offset, &offset);                            // 修改entry的pageID
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pidArray);
          my_free(page);
          return rc;
        }
      }
      // offset定位到下一个entry处
      offset = LEAF_HEAD_SIZE + j * MASTER_LEAF_ENTRY_LEN;
    }

    // 将修改后的叶子页写入文件
    rc = osWrite(fp, page, (pid - 1) * dbInfo->page_size, dbInfo->page_size);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pidArray);
      my_free(page);
      return rc;
    }
  }

  varArrayListDestroy(&pidArray);
  my_free(page);
  return GNCDB_SUCCESS;
}

int modifyPageID(BYTE *page, int *free_list, int freePageNum)
{
  int  keyLen;  // 内部页entry长度
  int  rc;
  char pageType;
  int  nextOverflowPid;
  int  nextBtreePid, newPid;  // 用以读取和写入hashMap的页号
  int  offset = 0;            // 偏移量
  int  entryNum;              // 内部/叶子页entry数量
  int  internalEntryPid;      // 内部页entry的页号
  int  subNum = 0;

  offset = 0;                                            // 页内偏移量，初始化为0
  rc     = readChar(&pageType, page + offset, &offset);  // 获取页面类型
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  switch (pageType) {

    //* 叶子页：修改nextPageId */
    case (char)LEAF_PAGE:  // 叶子页header：type(char) + keyLength(int) + entryNum(int) +  nextPageId(int)
    {
      // 跳过keyLen
      offset += sizeof(int);

      // 读取entryNum
      rc = readInt(&entryNum, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }

      // 读取nextPageId并修改
      rc = readInt(&nextBtreePid, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      subNum = findFirstGreaterThan(free_list, freePageNum, nextBtreePid);  // 计算页号减少的数量
      if (subNum > 0) {                                                     // 判断是否需要变化页号
        newPid = nextBtreePid - subNum;                                     // 计算变换后的新页号
        offset -= INT_SIZE;                                                 // 偏移量矫正
        rc = writeInt(newPid, page + offset, &offset);                      // 修改nextPageId页号
      }
      break;
    }

    //* 内部页：修改nextPageId、entry中的pageId */
    case (char)INTERNAL_PAGE:  // 内部页header：type(char) + keyLength(int) + entryNum(int) +  nextPageId(int)
    {

      // 读取keyLength
      rc = readInt(&keyLen, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }

      // 读取entryNum
      rc = readInt(&entryNum, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }

      // 读取nextPageId并修改
      rc = readInt(&nextBtreePid, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      subNum = findFirstGreaterThan(free_list, freePageNum, nextBtreePid);  // 计算页号减少的数量
      if (subNum > 0) {                                                     // 判断是否需要变化页号
        newPid = nextBtreePid - subNum;                                     // 计算变换后的新页号
        offset -= INT_SIZE;                                                 // 偏移量矫正
        rc = writeInt(newPid, page + offset, &offset);                      // 修改nextPageId页号
      }

      //* 接下来开始遍历内部页内容，修改entry的pageID */
      offset = 13;  // type + keyLength + entryNum + nextPageId
      for (int i = 0; i < entryNum; i++) {
        rc = readInt(&internalEntryPid, page + offset, &offset);  // 读取entry的pageID
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        subNum = findFirstGreaterThan(free_list, freePageNum, internalEntryPid);  // 计算页号减少的数量
        if (subNum > 0) {                                                         // 判断是否需要变化页号
          internalEntryPid -= subNum;                                             // 计算变换后的新页号
          offset -= sizeof(int);                                                  // 偏移量矫正
          rc = writeInt(internalEntryPid, page + offset, &offset);                // 修改entry的pageID
        }
        offset += keyLen;  // 跳过entry中key的内容
      }
      break;
    }

    //* 溢出页：修改nextOverflowPage */
    case (char)OVERFLOW_PAGE:  // 溢出页header：type（char） + nextOverflowPage（int）+ dataSize (int)
    {

      // 获取nextOverflowPage页号并修改
      rc = readInt(&nextOverflowPid, page + offset, &offset);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      subNum = findFirstGreaterThan(free_list, freePageNum, nextOverflowPid);  // 计算页号减少的数量
      if (subNum > 0) {                                                        // 判断是否需要变化页号
        newPid = nextOverflowPid - subNum;                                     // 计算变换后的新页号
        offset -= sizeof(int);                                                 // 偏移量矫正
        rc = writeInt(newPid, page + offset, &offset);                         // 修改nextPageId页号
      }
      break;
    }

    default: return GNCDB_NOT_FOUND; break;
  }

  return GNCDB_SUCCESS;
}

int modifyFileHeader(FILE *fp, FILE *new_fp, BYTE *page, int page_size, int newPageNum)
{
  int rc     = 0;
  int offset = 0;

  /* 读取文件头 */
  rc = osRead(fp, 0, page_size, &page);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 修改文件头信息 */
  offset = BYTES32 + 3 * INT_SIZE;                       /* overview + 版本号（主+次）+ pageSize */
  rc     = writeInt(newPageNum, page + offset, &offset); /* 重新写入文件pageNum */
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = writeInt(0, page + offset, &offset); /* 重新写入文件freePid */
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  rc = osWrite(new_fp, page, -1, page_size); /* 将文件头追加写入新文件 */
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

int getFreeList(FILE *fp, BYTE *page, int *freeList, DBInfo *dbInfo)
{
  int  rc      = 0;
  int  offset  = 0;
  int  i       = 0;
  int  freePid = dbInfo->first_free_pid;  // 获取第一个空闲页号
  char pageType;
  while (freePid > 0 && freePid <= dbInfo->page_count) {
    rc = osRead(fp, (freePid - 1) * dbInfo->page_size, dbInfo->page_size, &page);  // 读取页面
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    offset = 0;                                            // 页内偏移量初始化为0
    rc     = readChar(&pageType, page + offset, &offset);  // 获取页面类型
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (pageType != (char)0x03) {  // 检查页面类型，若不是空闲页则出错
      return GNCDB_MEM;
    }
    freeList[i] = freePid;
    i++;
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    // printf("get freePid: %d\n", freePid);
    rc = readInt(&freePid, page + offset, &offset);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  adaptiveSort(freeList, i);
  return GNCDB_SUCCESS;
}

int modifyAllPid(FILE *fp, FILE *new_fp, BYTE *page, int *freeList, DBInfo *dbInfo)
{
  int  rc;
  int  offset   = 0;
  char pageType = 0;
  for (int i = 2; i <= dbInfo->page_count; i++) {  // 页号为1是文件头，页号为2是master表，从2号页开始修改
    offset = 0;
    rc     = osRead(fp, (i - 1) * dbInfo->page_size, dbInfo->page_size, &page);  // 读取页面
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    rc = readChar(&pageType, page + offset, &offset);  // 获取页面类型
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    if (pageType != (char)FREE_PAGE) {  // 检查页面类型:若不是空闲页，需要修改页号后复制到新文件中
      rc = modifyPageID(page, freeList, dbInfo->free_page_count);  // 修改缓冲区中页号
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }

      rc = osWrite(new_fp, page, -1, dbInfo->page_size);  // 将页面追加写入新文件
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  }
  return GNCDB_SUCCESS;
}
