/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-28 21:00:22
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-14 21:51:29
 * @FilePath: /gncdbflr/linux_dev/src/fragmentation/pidModification.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef FILE_MODIFICATION_H
#define FILE_MODIFICATION_H
#include "dbInfo.h"
#include "typedefine.h"
int bfsTraverseMater(FILE *fp, int *free_list, DBInfo *dbInfo);
int modifyFileHeader(FILE *fp, FILE *new_fp, BYTE *page,int page_size, int newPageNum);
int getFreeList(FILE *fp, BYTE *page, int *freeList, DBInfo * dbInfo);
int modifyAllPid(FILE *fp, FILE *new_fp, BYTE *page, int *freeList, DBInfo* dbInfo);
#endif // FILE_MODIFICATION_H