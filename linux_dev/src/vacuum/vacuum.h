/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-08 15:14:58
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-14 15:51:52
 * @FilePath: /gncdbflr/linux_dev/src/fragmentation/vacuum.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef VACUUM_H
#define VACUUM_H
int vacuum(char *file_path, char *new_file_path, long *contract_size);
#endif // VACUUM_H