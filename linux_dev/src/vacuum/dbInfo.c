/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-26 09:42:28
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-14 19:58:05
 * @FilePath: /gncdbflr/linux_dev/src/vacuum/dbInfo.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "dbInfo.h"
#include "gncdbconstant.h"
#include "os.h"
#include "typedefine.h"
#include <string.h>
void double_to_percentage_string(double, char *);

/**
 * @description: 创建DBInfo结构体
 * @return {*} DBInfo结构体指针
 */
DBInfo *createDBInfo()
{
  DBInfo *db_info          = (DBInfo *)my_malloc(sizeof(DBInfo));
  db_info->file_len        = 0;
  db_info->fragmentation   = 0;
  db_info->free_page_count = 0;
  db_info->page_count      = 0;
  db_info->page_size       = 0;
  db_info->payload         = 0;
  return db_info;
}

/**
 * @description: 销毁DBInfo结构体
 * @param {DBInfo} *db_info DBInfo结构体指针
 * @return {*} void
 */
void destroyDBInfo(DBInfo *db_info) { my_free(db_info); }

/**
 * @description: 扫描文件，构建dbInfo
 * @param {char} *file_path 数据库文件路径
 * @param {DBInfo} *db_info 文件信息结构体，外部已分配内存
 * @return {*} 执行结果
 */
int parseDBInfo(char *file_path, DBInfo *db_info)
{
  long  file_size    = 0;
  BYTE *page         = my_malloc(PAGE_SIZE);
  char  pageType     = 0;
  int   offset       = 0;
  int   pageNum      = 0;
  int   freePid      = 0;
  int   firstFreePid = 0;
  int   pageSize     = 0;
  int   rc           = GNCDB_SUCCESS;
  FILE *fp           = NULL;

  /* 打开原始数据库文件 */
  rc = osOpenFile(file_path, &fp);
  if (rc != GNCDB_SUCCESS) {
    my_free(page);
    printf("No such file or dictionary: %s\n", file_path);
    return rc;
  }

  /* 读取原始数据库文件大小 */
  rc = getFileLength(&file_size, fp);
  if (rc != GNCDB_SUCCESS) {
    my_free(page);
    fclose(fp);
    return rc;
  }
  db_info->file_len = file_size;

  // 读取文件头
  // todo 这里假定读取的文件头会在pageSize范围内
  rc = osRead(fp, 0, PAGE_SIZE, &page);
  if (rc != GNCDB_SUCCESS) {
    my_free(page);
    fclose(fp);
    return rc;
  }

  //* 读取文件头中的pageSiz、pageNum和firstFreePid */
  offset = BYTES32 + 2 * INT_SIZE;                      // overview + 版本号（主+次）
  rc     = readInt(&pageSize, page + offset, &offset);  // 读取页面大小
  if (rc != GNCDB_SUCCESS) {
    my_free(page);
    fclose(fp);
    return rc;
  }
  // 计算文件大小
  db_info->page_size  = pageSize;
  db_info->page_count = (file_size) / pageSize;

  rc = readInt(&pageNum, page + offset, &offset);  // 读取文件pageNum
  if (rc != GNCDB_SUCCESS) {
    my_free(page);
    fclose(fp);
    return rc;
  }
  if (pageNum != db_info->page_count) {  // 读取页面数量与计算数量不符，文件出错
    my_free(page);
    fclose(fp);
    return GNCDB_FILE_NOT_FOUND;
  }
  
  rc = readInt(&firstFreePid, page + offset, &offset);  // 读取文件firstFreePid
  if (rc != GNCDB_SUCCESS) {
    my_free(page);
    fclose(fp);
    return rc;
  }
  db_info->first_free_pid = firstFreePid;

  //* 重新page为正确的pageSize大小
  if (pageSize != PAGE_SIZE) {
    page = my_realloc(page, pageSize);
    memset(page, 0x00, pageSize);  // 清空内存
    if (page == NULL) {
      fclose(fp);
      return GNCDB_MEM;
    }
  }

  //* 通过空闲页链表遍历 */
  freePid = firstFreePid;
  while (freePid > 0 && freePid <= db_info->page_count) {
    rc = osRead(fp, (freePid - 1) * pageSize, pageSize, &page);
    if (rc != GNCDB_SUCCESS) {
      my_free(page);
      fclose(fp);
      return rc;
    }
    offset = 0;  // 页内偏移量初始化为0
    rc     = readChar(&pageType, page + offset, &offset);
    if (rc != GNCDB_SUCCESS) {
      my_free(page);
      fclose(fp);
      return rc;
    }
    if (pageType != (char)FREE_PAGE) {  // 检查页面类型
      my_free(page);
      fclose(fp);
      return GNCDB_MEM;
    }
    db_info->free_page_count++;
    if (rc != GNCDB_SUCCESS) {
      my_free(page);
      fclose(fp);
      return rc;
    }
    rc = readInt(&freePid, page + offset, &offset);
    if (rc != GNCDB_SUCCESS) {
      my_free(page);
      fclose(fp);
      return rc;
    }
  }
  if (freePid == 0) {
    // printf("%d\n", freePid);
  } else {  // 空闲链表最后一个不是0，空闲链表出错
    my_free(page);
    fclose(fp);
    return GNCDB_NO_PAGE;
  }

  // 计算payload
  db_info->payload = 1 - ((double)db_info->free_page_count / db_info->page_count);
  // 计算fragmentation
  db_info->fragmentation = ((double)db_info->free_page_count * pageSize) / (db_info->file_len);

  my_free(page);
  fclose(fp);
  return GNCDB_SUCCESS;
}

/**
 * @description: 格式化打印dbInfo
 * @param {DBInfo} *db_info 文件信息结构体
 * @return {*} 执行结果
 */
int formatPrintDBinfo(DBInfo *db_info)
{
  int         col_cnt       = 2;
  int         row_cnt       = 6;
  int         col_widths[2] = {0};
  const char *keys[]        = {
      "file_len",
      "page_size",
      "page_num",
      "free_page_num",
      "payload",
      "fragmentation",
  };
  char values[6][15] = {0};
  sprintf(values[0], "%ld", db_info->file_len);
  sprintf(values[1], "%d", db_info->page_size);
  sprintf(values[2], "%d", db_info->page_count);
  sprintf(values[3], "%d", db_info->free_page_count);
  double_to_percentage_string(db_info->payload, values[4]);
  double_to_percentage_string(db_info->fragmentation, values[5]);

  //* 开始计算打印的列宽 */
  col_widths[0] = strlen(keys[3]);
  for (int i = 0; i < row_cnt; i++) {
    if (strlen(values[i]) > col_widths[1]) {
      col_widths[1] = strlen(values[i]);
    }
  }

  //* 开始打印输出 */
  // 间隔行
  printf("+");
  for (int i = 0; i < col_cnt; i++) {
    for (int j = 0; j < col_widths[i] + 2; j++) {
      printf("-");
    }
    printf("+");
  }
  printf("\n");

  for (int i = 0; i < row_cnt; i++) {
    printf("| %*s ", col_widths[0], keys[i]);
    printf("| %*s ", col_widths[1], values[i]);
    printf("|\n");

    printf("+");
    for (int i = 0; i < col_cnt; i++) {
      for (int j = 0; j < col_widths[i] + 2; j++) {
        printf("-");
      }
      printf("+");
    }
    printf("\n");
  }

  return GNCDB_SUCCESS;
}

int outputDBInfo(char *file_path)
{
  DBInfo *db_info = NULL;
  int     rc;

  //* 创建DBInfo结构体 */
  db_info = createDBInfo();
  if (db_info == NULL) {
    return GNCDB_MEM;
  }

  //* 读取文件信息 */
  rc = parseDBInfo(file_path, db_info);
  if (rc != GNCDB_SUCCESS) {
    destroyDBInfo(db_info);
    return rc;
  }

  //* 格式化打印输出 */
  rc = formatPrintDBinfo(db_info);
  if (rc != GNCDB_SUCCESS) {
    destroyDBInfo(db_info);
    return rc;
  }

  destroyDBInfo(db_info);
  return rc;
}

// 辅助函数：将double转换为百分比形式的字符串
inline void double_to_percentage_string(double value, char *output)
{
  double percentage = value * 100;
  sprintf(output, "%.2f%%", percentage);
}
