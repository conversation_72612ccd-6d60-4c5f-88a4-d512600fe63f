/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-25 15:53:47
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-14 15:56:26
 * @FilePath: /gncdbflr/linux_dev/src/fragmentation/mapping.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef MAPPING_H
#define MAPPING_H
#include <stdio.h>
#include <stdlib.h>

#ifdef _WIN32
#include <fcntl.h>
#include <io.h>
#include <windows.h>
#define OPEN_FILE(path) _open(path, _O_RDWR)
#define CLOSE_FILE(fd) _close(fd)
#define CREATE_MAP(fd, size) CreateFileMapping((HANDLE)_get_osfhandle(fd), NULL, PAGE_READWRITE, 0, size, NULL)
#define MAP_VIEW(hMap, size) MapViewOfFile(hMap, FILE_MAP_ALL_ACCESS, 0, 0, size)
#define UNMAP_VIEW(ptr, size) \
    do {                      \
        (void)(size);         \
        UnmapViewOfFile(ptr); \
    } while (0)
#define CLOSE_MAPPING(hMap) CloseHandle(hMap)
#define GET_FILE_SIZE(fd) _filelength(fd) // Get file size for Windows

#elif defined(__linux__)
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#define OPEN_FILE(path) open(path, O_RDWR)
#define CLOSE_FILE(fd) close(fd)
#define CREATE_MAP(fd, size) mmap(NULL, size, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0)
#define MAP_VIEW(hMap, size) \
    do {                     \
        (void)(hMap);        \
        (void)(size);        \
    } while (0)
#define UNMAP_VIEW(ptr, size) munmap(ptr, size)
#define CLOSE_MAPPING(hMap) \
    do {                    \
        (void)(hMap);       \
    } while (0) // No need to close mapping handle in Linux
#define GET_FILE_SIZE(fd) ({ \
    struct stat st;          \
    fstat(fd, &st);          \
    st.st_size;              \
}) // Get file size for Linux

#else
// Other platforms (if needed)
#endif

void *create_memory_mapping(const char *path, size_t *size);

void destroy_memory_mapping(void *ptr, size_t *size);
#endif // MAPPING_H