###
 # @Author: zql <EMAIL>
 # @Date: 2025-05-14 20:52:14
 # @LastEditors: zql <EMAIL>
 # @LastEditTime: 2025-05-14 20:53:32
 # @FilePath: /gncdbflr/linux_dev/src/vacuum/build_vacuum.sh
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
if [ ! -d "./build" ]; then
  mkdir ./build
fi
cd ./build
cmake ..
make vacuum
if [ $? -eq 0 ]; then
  echo "Build successful!"
else
  echo "Build failed!"
fi