/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-30 19:44:15
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-14 15:33:54
 * @FilePath: /gncdbflr/linux_dev/src/fragmentation/adaptiveSort.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "adaptiveSort.h"

#define THRESHOLD 10

// 插入排序
void insertionSort(int *arr, int left, int right) {
    int i, j;
    int tmp;
    for (i = left + 1; i <= right; i++) {
        tmp = arr[i];
        j = i - 1;
        while (j >= left && arr[j] > tmp) {
            arr[j + 1] = arr[j];
            j--;
        }
        arr[j + 1] = tmp;
    }
}

// 分区函数
int partition(int *arr, int left, int right) {
    int i = left - 1;
    int j;
    int pivot = arr[right];
    int temp;
    for (j = left; j < right; j++) {
        if (arr[j] <= pivot) {
            i++;
            temp = arr[i];
            arr[i] = arr[j];
            arr[j] = temp;
        }
    }
    temp = arr[i + 1];
    arr[i + 1] = pivot;
    arr[right] = temp;
    return i + 1;
}

// 三向切分快速排序
void quickSort3Way(int *arr, int left, int right) {
    int lt, gt, i;
    int pivot, current;

    if (left < right) {
        if (right - left < THRESHOLD) {
            insertionSort(arr, left, right);
            return;
        }
        lt = left;
        gt = right;
        pivot = arr[left];
        i = left + 1;
        while (i <= gt) {
            current = arr[i];
            if (current < pivot) {
                arr[i] = arr[lt];
                arr[lt] = current;
                i++;
                lt++;
            } else if (current > pivot) {
                arr[i] = arr[gt];
                arr[gt] = current;
                gt--;
            } else {
                i++;
            }
        }
        quickSort3Way(arr, left, lt - 1);
        quickSort3Way(arr, gt + 1, right);
    }
}

// 自适应排序
void adaptiveSort(int *arr, int size) {
    quickSort3Way(arr, 0, size - 1);
}
