/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-25 16:06:33
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-14 15:32:45
 * @FilePath: /gncdbflr/linux_dev/src/fragmentation/mapping.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

#include "mapping.h"

void *create_memory_mapping(const char *path, size_t *size)
{
    int fd = OPEN_FILE(path);
    void *mapped;
    if (fd == -1) {
        perror("Failed to open file");
        return NULL;
    }

    // 获取文件大小
    *size = GET_FILE_SIZE(fd);
    if (*size == -1) {
        perror("Failed to get file size");
        CLOSE_FILE(fd);
        return NULL;
    }

    mapped = CREATE_MAP(fd, *size);
    if (mapped == MAP_FAILED) {
        perror("Memory mapping failed");
        CLOSE_FILE(fd);
        return NULL;
    }

    CLOSE_FILE(fd);
    return mapped;
}

void destroy_memory_mapping(void *ptr, size_t *size)
{
    // 解除映射
    UNMAP_VIEW(ptr, *size);
    // 关闭映射
    CLOSE_MAPPING(ptr);
}
