#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include "vacuum.h"
#include "gncdbconstant.h"
#include "typedefine.h"
#include "os.h"
#include "fileModification.h"
#include "dbInfo.h"

void copy_file(const char *source, const char *destination)
{
  FILE  *src, *dest;
  char   buffer[BUFSIZ];  // 定义缓冲区
  size_t bytes;
  src = fopen(source, "rb");  // 以二进制模式打开源文件
  if (src == NULL) {
    perror("Error opening source file");
    return;
  }

  dest = fopen(destination, "wb");  // 以二进制模式打开目标文件
  if (dest == NULL) {
    perror("Error opening destination file");
    fclose(src);
    return;
  }

  // 读取源文件并写入目标文件
  while ((bytes = fread(buffer, sizeof(char), BUFSIZ, src)) > 0) {
    fwrite(buffer, sizeof(char), bytes, dest);
  }

  // 关闭文件
  fclose(src);
  fclose(dest);
}

//* 新方案：直接计算页号减少数 */
int vacuum(char *file_path, char *new_file_path, long *contract_size)
{
  int     rc             = GNCDB_SUCCESS;
  long    new_file_len   = 0;
  long    old_file_len   = 0;
  bool    hasNewFilePath = false;
  char   *dot            = NULL;
  BYTE   *page           = NULL;
  int    *freeList       = NULL;
  FILE   *new_fp         = NULL;
  FILE   *fp             = NULL;
  DBInfo *db_info        = NULL;

  *contract_size = 0;
  hasNewFilePath = ((new_file_path == NULL) ? false : true);

  //* 检验输入文件路径的有效性 */
  if (access(file_path, F_OK) == -1) {
    printf("No such file or dictionary: %s\n", file_path);
    return GNCDB_FILE_NOT_FOUND;
  }

  //* 处理输出文件名，若不存在，就在当前目录下创建以"_new"后缀的同名文件 */
  if (!hasNewFilePath) {
    new_file_path = my_malloc(strlen(file_path) + 5);    // +5是为了添加"_new"后缀与'\0'
    dot           = strrchr(file_path, '.');             // 查找最后一个点
    strncpy(new_file_path, file_path, dot - file_path);  // 复制文件名部分
    strcpy(new_file_path + (dot - file_path), "_new");   // 添加"_new"后缀
    strcpy(new_file_path + (dot - file_path) + 4, dot);  // 复制扩展名部分
  }

  //* 创建DBInfo结构体 */
  db_info = createDBInfo();
  if (db_info == NULL) {
    if (!hasNewFilePath)
      my_free(new_file_path);
    return GNCDB_MEM;
  }

  //* 读取文件信息 */
  rc = parseDBInfo(file_path, db_info);
  if (rc != GNCDB_SUCCESS) {
    if (!hasNewFilePath)
      my_free(new_file_path);
    destroyDBInfo(db_info);
    return rc;
  }

  //* 打开原始数据库文件 */
  rc = osOpenFile(file_path, &fp);
  if (rc != GNCDB_SUCCESS) {
    if (!hasNewFilePath)
      my_free(new_file_path);
    destroyDBInfo(db_info);
    return rc;
  }
  page = my_malloc(db_info->page_size);

  //* 获取freeList */
  freeList = my_malloc(sizeof(int) * db_info->free_page_count);
  rc       = getFreeList(fp, page, freeList, db_info);
  if (rc != GNCDB_SUCCESS) {
    my_free(freeList);
    my_free(page);
    if (!hasNewFilePath)
      my_free(new_file_path);
    fclose(fp);
    destroyDBInfo(db_info);
    return rc;
  }
  for (int i = 0; i < db_info->free_page_count; i++) {
    if (i > 0 && freeList[i] < freeList[i - 1]) {
      printf("freeList not sorted!!!\n");
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return GNCDB_FILE_NOT_FOUND;
    }
  }
  fflush(stdout);

  //* 复制并删除空闲页面 */
  if (db_info->free_page_count == 0) {
    printf("there is no free space needed to be vacuumed in DB file!\n");
    copy_file(file_path, new_file_path);  /* 直接将文件复制一份作为新文件 */
  } else {
    //* 直接以wb+形式打开新文件，避免文件不存在报错 */
    rc = createFile(new_file_path, &new_fp);
    if (rc != GNCDB_SUCCESS) {
      printf("Create file: %s failed!\n", new_file_path);
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return rc;
    }

    //* 修改文件头信息后将文件头写入新文件 */
    rc = modifyFileHeader(fp, new_fp, page, db_info->page_size, db_info->page_count - db_info->free_page_count);
    if (rc != GNCDB_SUCCESS) {
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return rc;
    }

    //* 遍历修改页号后将页面写入新文件 */
    rc = modifyAllPid(fp, new_fp, page, freeList, db_info);
    if (rc != GNCDB_SUCCESS) {
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return rc;
    }

    //* BFS修改master表的rootPageId */
    rc = bfsTraverseMater(new_fp, freeList, db_info);
    if (rc != GNCDB_SUCCESS) {
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return rc;
    }

    rc = getFileLength(&new_file_len, new_fp);  // 获取新文件长度
    if (rc != GNCDB_SUCCESS) {
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return rc;
    }
    rc = getFileLength(&old_file_len, fp);  // 获取旧文件长度
    if (rc != GNCDB_SUCCESS) {
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return rc;
    }
    *contract_size = old_file_len - new_file_len;
    if (*contract_size != db_info->page_size * db_info->free_page_count) {
      printf("file size not equal!!!");
      my_free(freeList);
      my_free(page);
      if (!hasNewFilePath)
        my_free(new_file_path);
      fclose(fp);
      destroyDBInfo(db_info);
      return GNCDB_FILE_NOT_FOUND;
    }
    printf("vacuum success!\nnew db file has saved in: %s\n", new_file_path);
    printf("old_file_size: %ld bytes\nnew_file_size: %ld bytes\n", old_file_len, new_file_len);
    printf("contract_size: %ld bytes\n", *contract_size);
    fclose(new_fp);
  }

  my_free(freeList);
  my_free(page);
  if (!hasNewFilePath)
    my_free(new_file_path);
  fclose(fp);
  destroyDBInfo(db_info);
  return GNCDB_SUCCESS;
}
