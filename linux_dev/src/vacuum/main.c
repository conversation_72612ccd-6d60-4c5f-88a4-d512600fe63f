/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-09-25 15:04:34
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-15 17:37:41
 * @FilePath: /gncdbflr/linux_dev/src/vacuum/main.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置
 * 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "dbInfo.h"
#include "gncdbconstant.h"
#include "vacuum.h"
#include <stdio.h>
#include <stdlib.h>
#include <sys/mman.h>

int printCommands()
{
  printf("commands:\n");
  printf("    vacuum [option] <file path> [-o <new file path>]\n");
  printf("options:\n");
  printf("    -h, --help                    显示帮助信息\n");
  printf("    -c, --contract                回收碎片\n");
  printf("    -v, --view                    查询碎片信息\n");
  printf("    -o, --output  <new file path> 新数据库文件路径\n");
  return GNCDB_SUCCESS;
}

int   c_flag = 0, v_flag = 0, h_flag = 0, o_flag = 0;
char *input_path  = NULL;  // 用于存储输出文件路径参数
char *output_path = NULL;  // 用于存储输出文件路径参数

#ifdef __linux__
#include <getopt.h>

//* 使用Linux getopt解析命令行参数 */
int linux_parse_param(int argc, char **argv)
{
  int opt       = 0;
  int longindex = 0;
  // 定义长选项和短选项
  struct option long_options[] = {
      {"help", no_argument, NULL, 'h'},
      {"contract", required_argument, NULL, 'c'},
      {"view", required_argument, NULL, 'v'},
      {"output", required_argument, NULL, 'o'},  // -o 选项需要一个参数
      {NULL, 0, NULL, 0}                         // 结束标志
  };

  while (1) {
    opt = getopt_long(argc, argv, "hc:v:o:", long_options, &longindex);
    if (opt == -1) {
      break;  // 处理完所有选项
    }
    switch (opt) {

      case 'h':  // 接收help参数
        h_flag = 1;
        break;

      case 'c':  // 接收contract参数，后跟输入文件路径
        c_flag     = 1;
        input_path = optarg;
        break;

      case 'v':  // 接受view参数，后跟输入文件路径
        v_flag     = 1;
        input_path = optarg;
        break;

      case 'o':  // 接受output参数，后跟输出文件路径
        o_flag      = 1;
        output_path = optarg;
        break;

      case '?':
        fprintf(stderr, "Error option: %c\n", optopt);
        printCommands();
        return GNCDB_PARAM_INVALID;
    }
  }
  return GNCDB_SUCCESS;
}

#elif defined(_WIN32)
#include <shellapi.h>
#include <windows.h>
//* 使用 Windows API 解析命令行参数 */
void windows_parse_param(int argc, char **argv)
{
  int     nArgs;
  LPWSTR *szArglist = CommandLineToArgvW(GetCommandLineW(), &nArgs);
  if (szArglist == NULL) {
    fprintf(stderr, "CommandLineToArgvW failed\n");
    return EXIT_FAILURE;
  }

  for (int i = 1; i < nArgs; i++) {  // 从 1 开始，跳过程序名
    wchar_t *arg = szArglist[i];
    if (wcscmp(arg, L"-h") == 0 || wcscmp(arg, L"--help") == 0) {
      h_flag = 1;
    } else if (wcscmp(arg, L"-c") == 0 || wcscmp(arg, L"--contract") == 0) {
      if (i + 1 < nArgs) {
        input_path = argv[i + 1];
        c_flag     = 1;
        i++;  // 跳过下一个参数
      } else {
        fprintf(stderr, "Error: Option -c requires an input file path.\n");
        return EXIT_FAILURE;
      }
    } else if (wcscmp(arg, L"-v") == 0 || wcscmp(arg, L"--view") == 0) {
      if (i + 1 < nArgs) {
        input_path = argv[i + 1];
        v_flag     = 1;
        i++;  // 跳过下一个参数
      } else {
        fprintf(stderr, "Error: Option -v requires an input file path.\n");
        return EXIT_FAILURE;
      }
      return GNCDB_SUCCESS;
    } else if ((strcmp(argv[1], "-q") == 0 || strcmp(argv[1], "--query") == 0)) {
      return outputDBInfo(argv[2]);
    } else {
      printf("Command foramt error!\n");
      print_usage();
      return GNCDB_SUCCESS;
    }
  }
  else if (argc == 5)
  {  // 设置新文件路径，仅回收碎片有效
    if ((strcmp(argv[1], "-r") == 0 || strcmp(argv[1], "--recycle") == 0) && (strcmp(argv[3], "-o") == 0)) {
      //* 碎片回收 */
      rc = vacuum(argv[2], argv[4]);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      } else if (wcscmp(arg, L"-o") == 0 || wcscmp(arg, L"--output") == 0) {
        if (i + 1 < nArgs) {
          output_path = argv[i + 1];
          i++;  // 跳过下一个参数
        } else {
          fprintf(stderr, "Error: Option -o requires an output file path.\n");
          return EXIT_FAILURE;
        }
      } else {
        fprintf(stderr, "Unknown option: %S\n", arg);
      }
    }
  }
#endif

int main(int argc, char *argv[])
{
  long contract_size = 0;
  int  rc            = 0;

#ifdef __linux__
  rc = linux_parse_param(argc, argv);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
#elif defined(_WIN32)
    windows_parse_param(argc, argv);
#else
  printf("Unsupported platform.\n");
  return 0;
#endif

  if ((h_flag + c_flag + v_flag) == 0) {  // 没有参数
    printCommands();
    return GNCDB_PARAM_INVALID;
  }

  if ((h_flag + c_flag + v_flag) > 1) {  // 不能多于一个参数
    fprintf(stderr, "Error: Options -h, -c, and -v cannot be used simultaneously.\n");
    printCommands();
    return GNCDB_PARAM_INVALID;
  }

  if (h_flag) {
    return printCommands();
  }

  if (c_flag) {  // 回收碎片
    rc = vacuum(input_path, output_path, &contract_size);
    return rc;
  }

  if (v_flag) {
    if (o_flag) {  // -v不能和-o同时使用
      fprintf(stderr, "Error: Option -o is not valid with -v.\n");
      printCommands();
      return GNCDB_PARAM_INVALID;
    }
    return outputDBInfo(input_path);
  }
}
