#include "vararraylist.h"
#include "gncdbconstant.h"
//#include "value.h"

int intCompare(varArrayList *v_st_array, void *vp_void_data1, void *vp_void_data2)
{
    int *lp_si32_num1 = (int *)vp_void_data1;
    int *lp_si32_num2 = (int *)vp_void_data2;
    if (*lp_si32_num1 == *lp_si32_num2)
    {
        return 0;
    }
    else if (*lp_si32_num1 > *lp_si32_num2)
    {
        return 1;
    }
    return -1;
}

/// <summary>
/// double比较函数
/// </summary>
/// <param name="data1"></param>
/// <param name="data2"></param>
/// <returns></returns>
int doubleCompare(varArrayList *v_st_array, void *vp_void_data1, void *vp_void_data2)
{
    double *lp_f64_num1 = (double *)vp_void_data1;
    double *lp_f64_num2 = (double *)vp_void_data2;
    if (*lp_f64_num1 == *lp_f64_num2)
    {
        return 0;
    }
    else if (*lp_f64_num1 > *lp_f64_num2)
    {
        return 1;
    }
    return -1;
}

/// <summary>
/// 字符串比较函数
/// </summary>
/// <param name="data1"></param>
/// <param name="data2"></param>
/// <returns></returns>
int stringCompare(varArrayList *v_st_array, void *vp_void_data1, void *vp_void_data2)
{
    {
        char **lp_sc8_str1 = (char **)vp_void_data1;
        char * lp_sc8_str2 = (char *)vp_void_data2;
        if (strcmp(*lp_sc8_str1, lp_sc8_str2) == 0)
        {
            return 0;
        }
        else if (strcmp(*lp_sc8_str1, lp_sc8_str2) > 0)
        {
            return 1;
        }
        return -1;
    }
}

varArrayList *varArrayListCreate(
    int v_si32_flag, int v_si32_elementSize, int v_si32_capacity, Compare vp_fun_compare, Destroy vp_fun_destroy)
{
    varArrayList *lp_st_newarray = NULL;

    if (v_si32_elementSize <= 0 || v_si32_capacity < 0)
    {
        return NULL;
    }

    /* 为结构体申请空间 */
    lp_st_newarray = my_malloc(sizeof(struct varArrayList));
    if (lp_st_newarray == NULL)
    {
        return NULL;
    }
    /* 初始化 */
    if (v_si32_flag == ORDER)
    {
        lp_st_newarray->orderly = true;
    }
    else if (v_si32_flag == DISORDER)
    {
        lp_st_newarray->orderly = false;
    }
    else
    {
        my_free(lp_st_newarray);
        return NULL;
    }
    lp_st_newarray->capacity     = v_si32_capacity == 0 ? ARRDEFAULTCAPACITY : v_si32_capacity;
    lp_st_newarray->fiexdCap     = v_si32_capacity == 0 ? false : true;
    lp_st_newarray->elementSize  = v_si32_elementSize;
    lp_st_newarray->elementCount = 0;
    lp_st_newarray->compare      = vp_fun_compare;
    lp_st_newarray->destroy      = vp_fun_destroy;
    lp_st_newarray->dataBuffer   = NULL;
    /* 为结构体内的指针申请空间 */
    lp_st_newarray->dataBuffer = my_malloc((size_t)(lp_st_newarray->capacity) * lp_st_newarray->elementSize);
    if (lp_st_newarray->dataBuffer == NULL)
    {
        /* 若没有申请到，则释放结构体空间并返回NULL */
        my_free(lp_st_newarray);
        return NULL;
    }
    /* 将申请的空间清空为0 */
    memset(lp_st_newarray->dataBuffer, 0, (size_t)(lp_st_newarray->capacity) * lp_st_newarray->elementSize);

    return lp_st_newarray;
}

/// <summary>
/// 获取array保存数据的数量
/// </summary>
/// <param name="vp_st_array"></param>
/// <returns></returns>
int varArrayListGetCount(varArrayList *vp_st_array)
{
    if (vp_st_array == NULL)
    {
        return GNCDB_NOT_FOUND;
    }
    return vp_st_array->elementCount;
}

/// <summary>
/// 内存调整
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="flag">决定操作为扩大还是缩小,定义为EXPAND  REDUCE</param>
/// <returns></returns>
int arrayResize(varArrayList *vp_st_array, int v_si32_flag)
{
    int   l_si32_capacity = vp_st_array->capacity;
    int   l_si32_num      = 0;
    void *lp_void_newdata = NULL;

    if (v_si32_flag == EXPAND)
    {
        /* 容量不够, 将容量扩大 */
        l_si32_capacity = (int)MEM_EXPAND(l_si32_capacity);
    }
    else if (v_si32_flag == REDUCE)
    {
        /* 容量多余, 将容量缩小 */
        l_si32_num      = (int)MEM_REDUCE(l_si32_capacity);
        l_si32_capacity = l_si32_num >= ARRMINCAPACITY ? l_si32_num : ARRMINCAPACITY;
    }
    /* 根据容量修改内存大小 */
    // lp_void_newdata = my_realloc(vp_st_array->dataBuffer, (size_t)capacity * (vp_st_array->elementSize));
    lp_void_newdata = my_realloc(vp_st_array->dataBuffer, l_si32_capacity * (vp_st_array->elementSize));
    if (lp_void_newdata == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    vp_st_array->dataBuffer = lp_void_newdata;
    vp_st_array->capacity   = l_si32_capacity;

    return GNCDB_SUCCESS;
}

/// <summary>
/// 在有序的array中寻找数据插入的位置
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="data"></param>
/// <returns></returns>
int varArrayListGetPosition(varArrayList *vp_st_array, void *vp_void_data)
{
    /* 寻找data合适的位置 */
    long l_si64_offet = 0;
    int  i            = 0;

    for (i = 0; i < vp_st_array->elementCount; i++)
    {
        l_si64_offet = i * vp_st_array->elementSize;
        if (vp_st_array->compare(vp_st_array, (char *)(vp_st_array->dataBuffer) + l_si64_offet, vp_void_data) > 0)
        {
            return i;
        }
    }
    return vp_st_array->elementCount;
}

/// <summary>
///
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="data"></param>
/// <returns></returns>
int varArrayListAdd(varArrayList *vp_st_array, void *vp_void_data)
{
    int   l_si32_rc     = 0;
    int   l_si32_sub    = 0;
    int   l_si32_offset = 0;
    char *lp_sc8_data   = NULL;

    /* 判断空间是否足够 */
    if (vp_st_array->elementCount >= vp_st_array->capacity)
    {
        if (vp_st_array->fiexdCap)
        {
            return GNCDB_CAPOVERFLOW;
        }
        else
        {
            /* 内存不够, 将内存扩大 */
            l_si32_rc = arrayResize(vp_st_array, EXPAND);
            if (l_si32_rc != GNCDB_SUCCESS)
            {
                return GNCDB_SPACE_LACK;
            }
        }
    }
    if (vp_st_array->orderly)
    {
        l_si32_sub = varArrayListGetPosition(vp_st_array, vp_void_data);
        /* 先将别的数据往后挪一位 */
        // for (i = vp_st_array->elementCount - 1; i >= sub; i--)
        // {
        // 	offset = i * vp_st_array->elementSize;
        // 	memcpy((char*)(vp_st_array->dataBuffer) + offset + vp_st_array->elementSize,
        // (char*)(vp_st_array->dataBuffer) + offset, vp_st_array->elementSize);
        // }
        memmove((char *)(vp_st_array->dataBuffer) + (l_si32_sub + 1) * vp_st_array->elementSize,
            (char *)(vp_st_array->dataBuffer) + l_si32_sub * vp_st_array->elementSize,
            (vp_st_array->elementCount - l_si32_sub) * vp_st_array->elementSize);

        /* 插入数据 */
        l_si32_offset = l_si32_sub * vp_st_array->elementSize;
        memcpy((char *)(vp_st_array->dataBuffer) + l_si32_offset, vp_void_data, vp_st_array->elementSize);

        vp_st_array->elementCount++;
    }
    else
    {
        lp_sc8_data = (char *)vp_st_array->dataBuffer +
                      vp_st_array->elementCount * vp_st_array->elementSize;  //计算内存中的数据的位置
        memcpy(lp_sc8_data, vp_void_data, vp_st_array->elementSize);

        vp_st_array->elementCount++;
    }

    return GNCDB_SUCCESS;
}

int varArrayListInsert(varArrayList *vp_st_array, int v_si32_idx, void *vp_void_data)
{
    int l_si32_rc     = 0;
    int l_si32_offset = 0;

    if (vp_st_array->orderly)
    {
        return GNCDB_INSERT_FAILED;
    }

    /* 判断空间是否足够 */
    if (vp_st_array->elementCount >= vp_st_array->capacity)
    {
        if (vp_st_array->fiexdCap)
        {
            return GNCDB_CAPOVERFLOW;
        }
        else
        {
            /* 内存不够, 将内存扩大 */
            l_si32_rc = arrayResize(vp_st_array, EXPAND);
            if (l_si32_rc != GNCDB_SUCCESS)
            {
                return GNCDB_SPACE_LACK;
            }
        }
    }

    /* idx后面的数据向后移动一位 */
    memmove((char *)(vp_st_array->dataBuffer) + (v_si32_idx + 1) * vp_st_array->elementSize,
        (char *)(vp_st_array->dataBuffer) + v_si32_idx * vp_st_array->elementSize,
        (vp_st_array->elementCount - v_si32_idx) * vp_st_array->elementSize);

    /* 插入数据 */
    l_si32_offset = v_si32_idx * vp_st_array->elementSize;
    memcpy(vp_st_array->dataBuffer + l_si32_offset, vp_void_data, vp_st_array->elementSize);
    vp_st_array->elementCount++;

    return GNCDB_SUCCESS;
}

/// @brief
/// @param vp_st_array
/// @param data
/// @return
int varArrayListAddTail(varArrayList *vp_st_array, void *vp_void_data)
{
    int   l_si32_rc   = 0;
    char *lp_sc8_data = NULL;

    /* 判断空间是否足够 */
    if (vp_st_array->elementCount >= vp_st_array->capacity)
    {
        if (vp_st_array->fiexdCap)
        {
            return GNCDB_CAPOVERFLOW;
        }
        else
        {
            /* 内存不够, 将内存扩大 */
            l_si32_rc = arrayResize(vp_st_array, EXPAND);
            if (l_si32_rc != GNCDB_SUCCESS)
            {
                return GNCDB_SPACE_LACK;
            }
        }
    }
    lp_sc8_data = (char *)vp_st_array->dataBuffer +
                  vp_st_array->elementCount * vp_st_array->elementSize;  //计算内存中的数据的位置
    memcpy(lp_sc8_data, vp_void_data, vp_st_array->elementSize);

    vp_st_array->elementCount++;

    return GNCDB_SUCCESS;
}

int varArrayListAddHead(varArrayList *vp_st_array, void *vp_void_data)
{
    int l_si32_rc     = 0;
    int i             = 0;
    int l_si32_offset = 0;
    // char* pData = NULL;

    /* 判断空间是否足够 */
    if (vp_st_array->elementCount >= vp_st_array->capacity)
    {
        if (vp_st_array->fiexdCap)
        {
            return GNCDB_CAPOVERFLOW;
        }
        else
        {
            /* 内存不够, 将内存扩大 */
            l_si32_rc = arrayResize(vp_st_array, EXPAND);
            if (l_si32_rc != GNCDB_SUCCESS)
            {
                return GNCDB_SPACE_LACK;
            }
        }
    }

    /* 先将别的数据往后挪一位 */
    for (i = vp_st_array->elementCount - 1; i >= 0; i--)
    {
        l_si32_offset = i * vp_st_array->elementSize;
        memcpy((char *)(vp_st_array->dataBuffer) + l_si32_offset + vp_st_array->elementSize,
            (char *)(vp_st_array->dataBuffer) + l_si32_offset,
            vp_st_array->elementSize);
    }
    memmove((char *)(vp_st_array->dataBuffer) + vp_st_array->elementSize,
        (char *)(vp_st_array->dataBuffer),
        (vp_st_array->elementCount) * vp_st_array->elementSize);
    /* 插入数据 */
    l_si32_offset = 0;
    memcpy((char *)(vp_st_array->dataBuffer) + l_si32_offset, vp_void_data, vp_st_array->elementSize);

    vp_st_array->elementCount++;

    return GNCDB_SUCCESS;
}

/// <summary>
///
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="low"></param>
/// <param name="high"></param>
/// <param name="data"></param>
/// <returns></returns>
static int binarySearch(varArrayList *vp_st_array, int v_si32_low, int v_si32_high, void *vp_void_data)
{
    int l_si32_mid    = 0;
    int l_si32_offset = 0;

    if (v_si32_low <= v_si32_high)
    {
        l_si32_mid    = (v_si32_low + v_si32_high) / 2;
        l_si32_offset = l_si32_mid * vp_st_array->elementSize;
        if (vp_st_array->compare(vp_st_array, (char *)(vp_st_array->dataBuffer) + l_si32_offset, vp_void_data) == 0)
        {
            return l_si32_mid;
        }
        else if (vp_st_array->compare(vp_st_array, (char *)(vp_st_array->dataBuffer) + l_si32_offset, vp_void_data) > 0)
        {
            return binarySearch(vp_st_array, v_si32_low, l_si32_mid - 1, vp_void_data);
        }
        else
        {
            return binarySearch(vp_st_array, l_si32_mid + 1, v_si32_high, vp_void_data);
        }
    }
    return GNCDB_NOT_FOUND;
}

/// <summary>
/// 查找数据下标
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="data"></param>
/// <returns></returns>
int varArrayListIndexOf(varArrayList *vp_st_array, void *vp_void_data)
{
    int l_si32_index = GNCDB_NOT_FOUND;
    // int offset = 0;
    void *l_void_data = NULL;
    int   i           = 0;
    if (vp_st_array->orderly)
    {
        /* 有序的数据可以通过二分查找 */
        l_si32_index = binarySearch(vp_st_array, 0, vp_st_array->elementCount - 1, vp_void_data);
    }
    else
    {
        /* 无序的数据进行遍历查找 */
        for (i = 0; i < vp_st_array->elementCount; i++)
        {
            // offset = i * vp_st_array->elementSize;
            l_void_data = varArrayListGet(vp_st_array, i);
            if (!vp_st_array->compare(vp_st_array, l_void_data, vp_void_data))
            {
                l_si32_index = i;
                break;
            }
        }
    }

    return l_si32_index;
}

/// <summary>
/// 根据下标获取数据
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="index"></param>
/// <returns></returns>
void *varArrayListGet(varArrayList *vp_st_array, int v_si32_index)
{
    void *lp_void_data = NULL;
    if (v_si32_index >= vp_st_array->elementCount)
    {
        return NULL;
    }
    lp_void_data = (char *)vp_st_array->dataBuffer + v_si32_index * vp_st_array->elementSize;

    return lp_void_data;
}

/// <summary>
/// 将first中下标index至尾部的数据移动至second的头部
/// </summary>
/// <param name="arrayFirst"></param>
/// <param name="arraySecond"></param>
/// <param name="index"></param>
/// <returns></returns>
int varArrayListRemoveTail(varArrayList *vp_st_arrayfirst, varArrayList *vp_st_arraysecond, int v_si32_index)
{
    int   l_si32_count  = vp_st_arrayfirst->elementCount - v_si32_index;
    int   l_si32_rc     = 0;
    char *lp_sc8_buffer = NULL;
    char *lp_sc8_data   = NULL;
    /* size为first中需要移动的数据的字节大小 */
    int   l_si32_size       = (vp_st_arrayfirst->elementCount - v_si32_index) * vp_st_arrayfirst->elementSize;
    char *lp_sc8_seconddata = NULL;

    /* 首先判断arraySecond剩余空间是否足够 */
    while (vp_st_arraysecond->capacity - vp_st_arraysecond->elementCount < l_si32_count)
    {
        if (vp_st_arraysecond->fiexdCap)
        {
            return GNCDB_CAPOVERFLOW;
        }
        else
        {
            /* 内存不够, 将内存扩大 */
            l_si32_rc = arrayResize(vp_st_arraysecond, EXPAND);
            if (l_si32_rc != GNCDB_SUCCESS)
            {
                return GNCDB_SPACE_LACK;
            }
        }
    }

    /* 申请临时空间保存第二个array的数据 */
    if (vp_st_arraysecond->elementCount > 0)
    {
        lp_sc8_buffer = my_malloc(vp_st_arraysecond->elementCount * vp_st_arraysecond->elementSize);
        if (lp_sc8_buffer == NULL)
        {
            return GNCDB_SPACE_LACK;
        }
        memmove(lp_sc8_buffer,
            vp_st_arraysecond->dataBuffer,
            vp_st_arraysecond->elementCount * vp_st_arraysecond->elementSize);

        /* 将原本第二个array的数据复制回来并释放临时空间 */
        lp_sc8_seconddata = (char *)vp_st_arraysecond->dataBuffer + l_si32_size;
        memmove(lp_sc8_seconddata, lp_sc8_buffer, vp_st_arraysecond->elementCount * vp_st_arraysecond->elementSize);
        my_free(lp_sc8_buffer);
    }

    /* 将第一个array中index下标开始的数据复制到第二个array的开头 */
    lp_sc8_data = (char *)(vp_st_arrayfirst->dataBuffer) + v_si32_index * vp_st_arrayfirst->elementSize;
    memmove(vp_st_arraysecond->dataBuffer, lp_sc8_data, l_si32_size);

    vp_st_arrayfirst->elementCount = v_si32_index;
    vp_st_arraysecond->elementCount += l_si32_count;

    /* 处理冗余内存 */
    while (vp_st_arrayfirst->elementCount < (vp_st_arrayfirst->capacity) / 3 && vp_st_arrayfirst->capacity > 5 &&
           !(vp_st_arrayfirst->fiexdCap))
    {
        /* 由于此处为多层循环且执行将内存缩小，暂不做返回值处理 */
        l_si32_rc = arrayResize(vp_st_arrayfirst, REDUCE);
    }
    return GNCDB_SUCCESS;
}

int varArrayListRemoveHead(varArrayList *vp_st_arrayfirst, varArrayList *vp_st_arraysecond, int v_si32_index)
{
    int   l_si32_count  = v_si32_index + 1;
    int   l_si32_rc     = 0;
    char *lp_sc8_buffer = NULL;
    char *lp_sc8_data   = NULL;
    int   l_si32_size   = 0;
    /* 首先判断arraySecond剩余空间是否足够 */
    while (vp_st_arraysecond->capacity - vp_st_arraysecond->elementCount < l_si32_count)
    {
        if (vp_st_arraysecond->fiexdCap)
        {
            return GNCDB_CAPOVERFLOW;
        }
        else
        {
            /* 内存不够, 将内存扩大 */
            l_si32_rc = arrayResize(vp_st_arraysecond, EXPAND);
            if (l_si32_rc != GNCDB_SUCCESS)
            {
                return GNCDB_SPACE_LACK;
            }
        }
    }
    lp_sc8_data =
        (char *)(vp_st_arraysecond->dataBuffer) + vp_st_arraysecond->elementCount * vp_st_arraysecond->elementSize;
    l_si32_size = l_si32_count * vp_st_arrayfirst->elementSize;
    memmove(lp_sc8_data, vp_st_arrayfirst->dataBuffer, l_si32_size);

    lp_sc8_data = (char *)(vp_st_arrayfirst->dataBuffer) + l_si32_size;
    memmove(vp_st_arrayfirst->dataBuffer,
        lp_sc8_data,
        (vp_st_arrayfirst->elementCount - l_si32_count) * vp_st_arrayfirst->elementSize);
    my_free(lp_sc8_buffer);

    vp_st_arrayfirst->elementCount  = vp_st_arrayfirst->elementCount - l_si32_count;
    vp_st_arraysecond->elementCount = vp_st_arraysecond->elementCount + l_si32_count;

    /* 处理冗余内存 */
    while (vp_st_arrayfirst->elementCount < (vp_st_arrayfirst->capacity) / 3 && vp_st_arrayfirst->capacity > 5 &&
           !(vp_st_arrayfirst->fiexdCap))
    {
        /* 由于此处为多层循环且执行将内存缩小，暂不做返回值处理 */
        l_si32_rc = arrayResize(vp_st_arrayfirst, REDUCE);
    }

    return GNCDB_SUCCESS;
}

int varArrayListRemove(varArrayList *vp_st_array, void *vp_void_data)
{
    char *lp_sc8_data  = NULL;
    int   l_si32_size  = 0;
    int   l_si32_index = varArrayListIndexOf(vp_st_array, vp_void_data);

    if (l_si32_index == GNCDB_NOT_FOUND)
    {
        return GNCDB_NOT_FOUND;
    }

    lp_sc8_data = (char *)vp_st_array->dataBuffer + l_si32_index * vp_st_array->elementSize;  //计算内存中的数据的位置

    /* 调整各个元素的位置 */
    l_si32_size =
        (vp_st_array->elementCount * vp_st_array->elementSize) - ((l_si32_index + 1) * vp_st_array->elementSize);
    memmove(lp_sc8_data, lp_sc8_data + vp_st_array->elementSize, l_si32_size);
    vp_st_array->elementCount--;
    /* 处理冗余内存 */
    while (vp_st_array->elementCount < (vp_st_array->capacity) / 3 && vp_st_array->capacity > 5 &&
           !(vp_st_array->fiexdCap))
    {
        arrayResize(vp_st_array, REDUCE);
    }

    return GNCDB_SUCCESS;
}

/// <summary>
/// 指定的数据全部删除
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="data"></param>
/// <returns></returns>
int varArrayListRemoveAll(varArrayList *vp_st_array, void *vp_void_data)
{
    int   l_si32_flag  = 0;
    char *lp_sc8_pfast = vp_st_array->dataBuffer;
    char *lp_sc8_pslow = vp_st_array->dataBuffer;
    int   l_si32_count = vp_st_array->elementCount;
    int   i            = 0;

    for (i = 0; i < l_si32_count; ++i)
    {
        if (!vp_st_array->compare(vp_st_array, lp_sc8_pfast, vp_void_data))
        {
            vp_st_array->elementCount--;
            if (l_si32_flag == 0)
            {
                lp_sc8_pslow = lp_sc8_pfast;
            }
            l_si32_flag = 1;
        }
        else
        {
            if (l_si32_flag)
            {
                memmove(lp_sc8_pslow, lp_sc8_pfast, vp_st_array->elementSize);
                lp_sc8_pslow += vp_st_array->elementSize;
            }
        }
        lp_sc8_pfast += vp_st_array->elementSize;
    }
    /* 处理冗余内存 */
    while (vp_st_array->elementCount < (vp_st_array->capacity) / 3 && vp_st_array->capacity > 5 &&
           !(vp_st_array->fiexdCap))
    {
        arrayResize(vp_st_array, REDUCE);
    }

    return GNCDB_SUCCESS;
}

int varArrayListRemoveByIndex(varArrayList *vp_st_array, int v_si32_index)
{
    int   l_si32_size = 0;
    char *lp_sc8_data = varArrayListGet(vp_st_array, v_si32_index);
    if (lp_sc8_data == NULL)
    {
        return GNCDB_NOT_FOUND;
    }
    /* 调整各个元素的位置 */
    l_si32_size =
        (vp_st_array->elementCount * vp_st_array->elementSize) - ((v_si32_index + 1) * vp_st_array->elementSize);
    memmove(lp_sc8_data, lp_sc8_data + vp_st_array->elementSize, l_si32_size);
    vp_st_array->elementCount--;
    /* 处理冗余内存 */
    while (vp_st_array->elementCount < (vp_st_array->capacity) / 3 && vp_st_array->capacity > 5 &&
           !(vp_st_array->fiexdCap))
    {
        arrayResize(vp_st_array, REDUCE);
    }

    return GNCDB_SUCCESS;
}

int varArrayListSet(varArrayList *vp_st_array, void *vp_void_data1, void *vp_void_data2)
{
    int   l_si32_index = 0;
    int   rc           = 0;
    char *lp_sc8_data  = NULL;
    if (vp_st_array->orderly)
    {
        rc = varArrayListRemove(vp_st_array, vp_void_data1);
        if (rc == GNCDB_NOT_FOUND)
        {
            return GNCDB_NOT_FOUND;
        }
        rc = varArrayListAdd(vp_st_array, vp_void_data2);
    }
    else
    {
        l_si32_index = varArrayListIndexOf(vp_st_array, vp_void_data1);
        if (l_si32_index == GNCDB_NOT_FOUND)
        {
            return GNCDB_NOT_FOUND;
        }
        lp_sc8_data =
            (char *)vp_st_array->dataBuffer + l_si32_index * vp_st_array->elementSize;  //计算内存中的数据的位置
        memcpy(lp_sc8_data, vp_void_data2, vp_st_array->elementSize);
    }

    return GNCDB_SUCCESS;
}

int varArrayListSetAll(varArrayList *vp_st_array, void *vp_void_data1, void *vp_void_data2)
{
    int   l_si32_sub   = 0;
    int   i            = 0;
    char *lp_sc8_data  = NULL;
    int   l_si32_count = 0;
    if (vp_st_array->orderly)
    {
        l_si32_count = vp_st_array->elementCount;
        l_si32_sub   = varArrayListRemove(vp_st_array, vp_void_data1);
        if (l_si32_sub == GNCDB_NOT_FOUND)
        {
            return GNCDB_NOT_FOUND;
        }
        for (i = 0; i < l_si32_count - vp_st_array->elementCount; ++i)
        {
            varArrayListAdd(vp_st_array, vp_void_data2);
        }
    }
    else
    {
        lp_sc8_data = (char *)vp_st_array->dataBuffer;
        for (i = 0; i < vp_st_array->elementCount; ++i)
        {
            if (!vp_st_array->compare(vp_st_array, lp_sc8_data, vp_void_data1))
            {
                memcpy(lp_sc8_data, vp_void_data2, vp_st_array->elementSize);
                l_si32_count = 1;
            }
            lp_sc8_data = lp_sc8_data + vp_st_array->elementSize;
        }
        if (l_si32_count == 0)
        {
            return GNCDB_NOT_FOUND;
        }
    }

    return GNCDB_SUCCESS;
}

/// <summary>
/// 通过下标更新数据
/// </summary>
/// <param name="vp_st_array"></param>
/// <param name="index">更新数据的下标</param>
/// <param name="data">需要更新的数据</param>
/// <returns></returns>
int varArrayListSetByIndex(varArrayList *vp_st_array, int v_si32_index, void *vp_void_data)
{
    int   l_si32_rc   = 0;
    char *lp_sc8_data = NULL;
    if (vp_st_array->orderly)
    {
        l_si32_rc = varArrayListRemoveByIndex(vp_st_array, v_si32_index);
        if (l_si32_rc == GNCDB_NOT_FOUND)
        {
            return GNCDB_NOT_FOUND;
        }
        l_si32_rc = varArrayListAdd(vp_st_array, vp_void_data);
    }
    else
    {
        lp_sc8_data = varArrayListGet(vp_st_array, v_si32_index);
        if (lp_sc8_data == NULL)
        {
            return GNCDB_NOT_FOUND;
        }
        memcpy(lp_sc8_data, vp_void_data, vp_st_array->elementSize);
    }
    return GNCDB_SUCCESS;
}

/// <summary>
/// 清空一个array
/// </summary>
/// <param name="vp_st_array"></param>
/// <returns></returns>
int varArrayListClear(varArrayList *vp_st_array)
{
    char *lp_sc8_data = NULL;
    int   i           = 0;
    if (vp_st_array == NULL)
    {
        return GNCDB_SUCCESS;
    }
    if (vp_st_array->destroy != NULL)
    {
        lp_sc8_data = (char *)vp_st_array->dataBuffer;
        for (i = 0; i < vp_st_array->elementCount; ++i)
        {
            vp_st_array->destroy(lp_sc8_data);
            lp_sc8_data = lp_sc8_data + vp_st_array->elementSize;
        }
    }
    memset(vp_st_array->dataBuffer, 0, (size_t)(vp_st_array->capacity) * vp_st_array->elementSize);
    vp_st_array->elementCount = 0;

    return GNCDB_SUCCESS;
}

/// <summary>
/// 销毁一个array
/// </summary>
/// <param name="vp_st_array"></param>
void varArrayListDestroy(varArrayList **vp_st_array)
{
    char *lp_sc8_data = NULL;
    int   i           = 0;
    if (*vp_st_array == NULL)
    {
        return;
    }
    if ((*vp_st_array)->destroy != NULL)
    {
        lp_sc8_data = (char *)(*vp_st_array)->dataBuffer;
        for (i = 0; i < (*vp_st_array)->elementCount; ++i)
        {
            (*vp_st_array)->destroy(lp_sc8_data);
            lp_sc8_data = lp_sc8_data + (*vp_st_array)->elementSize;
        }
    }

    my_free((*vp_st_array)->dataBuffer);
    (*vp_st_array)->dataBuffer = NULL;
    my_free(*vp_st_array);
    *vp_st_array = NULL;
}

/************************************************
使用array保存指针的函数接口
************************************************/

int varArrayListAddPointer(varArrayList *vp_st_array, void *vp_void_pointer)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListAdd(vp_st_array, &vp_void_pointer);
    return l_si32_rc;
}

int varArrayListInsertPointer(varArrayList *vp_st_array, int v_si32_idx, void *vp_void_pointer)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListInsert(vp_st_array, v_si32_idx, &vp_void_pointer);
    return l_si32_rc;
}

int varArrayListAddPointerTail(varArrayList *vp_st_array, void *vp_void_pointer)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListAddTail(vp_st_array, &vp_void_pointer);
    return l_si32_rc;
}

int varArrayListAddPointerHead(varArrayList *vp_st_array, void *vp_void_pointer)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListAddHead(vp_st_array, &vp_void_pointer);
    return l_si32_rc;
}

int varArrayListRemovePointer(varArrayList *vp_st_array, void *vp_void_pointer)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListRemove(vp_st_array, &vp_void_pointer);
    return l_si32_rc;
}

int varArrayListRemoveByIndexPointer(varArrayList *vp_st_array, int v_si32_index)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListRemoveByIndex(vp_st_array, v_si32_index);
    return l_si32_rc;
}

void *varArrayListGetPointer(varArrayList *vp_st_array, int v_si32_index)
{
    void **lp_void_data = varArrayListGet(vp_st_array, v_si32_index);
    if (lp_void_data == NULL)
    {
        return NULL;
    }
    return *lp_void_data;
}
/**
 * @brief  获取array指定位置的元素，并remove掉
 * @param  vp_st_array:
 * @param  index:
 * @return void*:
 */
void *varArrayListMovePointer(varArrayList *vp_st_array, int v_si32_index)
{
    void *lp_void_data = varArrayListGetPointer(vp_st_array, v_si32_index);
    varArrayListRemoveByIndexPointer(vp_st_array, v_si32_index);
    return lp_void_data;
}

int varArrayListIndexOfPointer(varArrayList *vp_st_array, void *vp_void_pointer)
{
    int l_si32_index = 0;
    l_si32_index     = varArrayListIndexOf(vp_st_array, &vp_void_pointer);

    return l_si32_index;
}

int varArrListSetPointer(varArrayList *vp_st_array, void *vp_void_pointer1, void *vp_void_pointer2)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListSet(vp_st_array, &vp_void_pointer1, &vp_void_pointer2);

    return l_si32_rc;
}

int varArrayListSetByIndexPointer(varArrayList *vp_st_array, int v_si32_index, void *vp_void_pointer)
{
    int l_si32_rc = 0;
    l_si32_rc     = varArrayListSetByIndex(vp_st_array, v_si32_index, &vp_void_pointer);
    return l_si32_rc;
}

int varArrayListExistPointer(varArrayList *vp_st_array, void *vp_void_pointer)
{
    int l_si32_index = varArrayListIndexOf(vp_st_array, &vp_void_pointer);
    if (l_si32_index == GNCDB_NOT_FOUND)
    {
        return false;
    }
    return true;
}

void *varArrayListGetTailPointer(varArrayList *vp_st_array)
{
    return varArrayListGetPointer(vp_st_array, vp_st_array->elementCount - 1);
}

void printfArrayIntValue(varArrayList *vp_st_array)
{
    int  i             = 0;
    int *lp_si32_value = 0;
    for (i = 0; i < vp_st_array->elementCount; ++i)
    {
        lp_si32_value = varArrayListGet(vp_st_array, i);
        printf("%d--", *lp_si32_value);
    }
    printf("\n");
}

// 对数据进行交换
static void swap(void *vp_void_data1, void *vp_void_data2, size_t v_ui64_size)
{
    void *lp_void_temp = (void *)my_malloc(v_ui64_size);
    if (lp_void_temp == NULL)
    {
        return;
    }

    memcpy(lp_void_temp, vp_void_data1, v_ui64_size);
    memcpy(vp_void_data1, vp_void_data2, v_ui64_size);
    memcpy(vp_void_data2, lp_void_temp, v_ui64_size);
    my_free(lp_void_temp);
}

// 反转 varArrayList 中的元素
void varArrayListReverse(varArrayList *vp_st_array)
{
    int   l_si32_left          = 0;
    int   l_si32_right         = vp_st_array->elementCount - 1;
    void *lp_void_leftelement  = NULL;
    void *lp_void_rightelement = NULL;
    if (vp_st_array == NULL || vp_st_array->elementCount <= 1)
    {
        return;
    }
    while (l_si32_left < l_si32_right)
    {
        lp_void_leftelement  = (char *)vp_st_array->dataBuffer + l_si32_left * vp_st_array->elementSize;
        lp_void_rightelement = (char *)vp_st_array->dataBuffer + l_si32_right * vp_st_array->elementSize;

        swap(lp_void_leftelement, lp_void_rightelement, vp_st_array->elementSize);

        ++l_si32_left;
        --l_si32_right;
    }
}

/**
 * 复制一个 varArrayList 类型的数组列表。
 * @param vp_st_array 指向要复制的 varArrayList 的指针。
 * @return 指向新的 varArrayList 的指针，如果无法创建则返回 NULL。
 */
varArrayList *varArrayListCopy(varArrayList *vp_st_array)
{
    varArrayList *lp_st_newarray = NULL;
    int           i              = 0;
    void *        lp_void_data   = NULL;

    // 如果原数组为空，则直接返回 NULL。
    if (vp_st_array == NULL)
    {
        return NULL;
    }

    // 创建一个新的 varArrayList，属性与原数组相同。
    lp_st_newarray = varArrayListCreate(vp_st_array->orderly == true ? ORDER : DISORDER,
        vp_st_array->elementSize,
        vp_st_array->capacity,
        vp_st_array->compare,
        vp_st_array->destroy);
    // 如果新数组创建失败，则返回 NULL。
    if (lp_st_newarray == NULL)
    {
        return NULL;
    }

    // 遍历原数组，复制每个元素到新数组。
    for (i = 0; i < vp_st_array->elementCount; ++i)
    {
        lp_void_data = varArrayListGet(vp_st_array, i);
        if (lp_void_data != NULL)
        {
            // 如果获取元素成功，则添加到新数组。
            varArrayListAdd(lp_st_newarray, lp_void_data);
        }
        else
        {
            // 如果获取元素失败，释放新数组并返回 NULL。
            varArrayListDestroy(&lp_st_newarray);
            return NULL;
        }
    }

    // 返回新的数组列表指针。
    return lp_st_newarray;
}

void varArrayListPointerDestroy(void *vp_void_data)
{
    varArrayList **lp_st_list = (varArrayList **)vp_void_data;
    if (lp_st_list == NULL || *lp_st_list == NULL)
    {
        return;
    }
    varArrayListDestroy(lp_st_list);
}