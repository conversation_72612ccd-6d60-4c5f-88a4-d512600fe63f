#include <limits.h>
#include <math.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <stdio.h>
#include <ctype.h>
#include "utils.h"
#include "gncdbconstant.h"

/* 分割字符串 */
int split_string(char *str, char **tokens)
{
  char *token;
  int   i = 0;
  token   = strtok(str, ".");
  while (token != NULL) {
    tokens[i++] = token;
    token       = strtok(NULL, ".");
  }
  return i;
}
/// <summary>
/// 判断字符串中是否有两个点
/// </summary>
/// <param name="str"></param>
/// <returns></returns>
int hasOneDots(char *str)
{
  int dotCount = 0;
  int i        = 0;
  for (i = 0; str[i] != '\0'; i++) {
    if (str[i] == '.') {
      dotCount++;
    }
    if (dotCount == 1) {
      return GNCDB_SUCCESS;
    }
  }
  return GNCDB_NOT_FOUND;
}

//找出字符串在原字符串中的起始位置
int find_str(char *str, char *sub_str)
{
  char *str_temp1 = NULL;
  char *str_temp2 = NULL;
  int   count_i, count_j;
  int   flg_find = 0;
  for (count_i = 1; *str != '\0'; count_i++) {
    if (*str == *sub_str) {
      str_temp1 = str;
      str_temp2 = sub_str;
      for (count_j = 1; *str_temp2 != '\0'; count_j++) {  //以要查找的字符串长度为循环回数
        str_temp1++;
        if (*str_temp1 == '\0') {  //判断是否到目标字符串的结尾
          break;
        }
        str_temp2++;
        if (*str_temp2 == '\0') {  //判断是否到查找字符串的结尾
          flg_find = 1;
          break;
        }
        if (*str_temp1 != *str_temp2) {
          break;                                //出现不相等的情况即返回上层循环重新查找
        } else if (*(str_temp2 + 1) == '\0') {  //如果下一个字符为结尾，那么证明已经找到
          flg_find = 1;
          break;
        }
      }
    }
    if (flg_find == 1) {
      return count_i;
    }
    str++;
  }
  return -1;
}

//从指定位置开始制子字符串，要求从指定位置开始，并具有指定的长度。如果没有指定长度_Count或_Count+_Off超出了源字符串的长度，则子字符串将延续到源字符串的结尾
char *sub_str(char *str, int pos, int len)
{
  int   i = 0;
  int   j = 0;
  char *r = NULL;
  r       = my_malloc(len + 1);
  if (r == NULL) {
    return NULL;
  }
  while (str[i] != '\0') {
    if (i >= pos && i < pos + len) {
      r[j] = str[i];
      j++;
    }
    i++;
  }
  r[j] = '\0';
  return r;
}
//只负责截取字符串，不负责分配内存
// utils

// swap str
void swap_str(char *str1, char *str2)
{
  char *temp = str1;
  str1       = str2;
  str2       = temp;
}

/// <summary>
/// 默认判断键是否相等（键值为int类型）
/// </summary>
/// <param name="key1">key1</param>
/// <param name="key2">key2</param>
/// <returns>true 或 false</returns>
bool isEqualOfInt(void *key1, void *key2)
{
  if (*((int *)key1) == *((int *)key2)) {
    return true;
  }
  return false;
}

/// <summary>
/// 默认判断键是否相等（键值为字符串类型）
/// </summary>
/// <param name="key1">key1</param>
/// <param name="key2">key2</param>
/// <returns>true 或 false</returns>
bool isEqualOfString(void *key1, void *key2)
{
  if (strcmp((char *)key1, (char *)key2) == 0) {
    return true;
  }
  return false;
}

/**
 * @brief 判断字符串是否为空。例如NULL、 ""、 "  "、 "\n"等
 *
 * @param s
 * @return true
 * @return false
 */
int isBlank(const char *s)
{
  if (s == NULL) {
    return 1;
  }
  while (*s != '\0') {
    if (!isspace(*s)) {
      return 0;
    }
    s++;
  }
  return 1;
}

/**
 * @brief 字符串拼接
 *
 * @param str1
 * @param str2
 * @return char*
 */
char *strConcat(const char *str1, const char *str2)
{
  char *result = (char *)my_malloc(strlen(str1) + strlen(str2) + 1);
  strcpy(result, str1);
  strcat(result, str2);
  return result;
}

char **deepCopy2DArray(char **source, int rows)
{
  /* 分配指针数组的内存 */
  int    i           = 0;
  int    len         = 0;
  char **destination = my_malloc(rows * sizeof(char *));
  for (i = 0; i < rows; i++) {
    if (source[i]) {
      /* 计算每一行的长度（不包括结尾的'\0'） */
      len = strlen(source[i]);
      /* 为每一行分配内存 */
      destination[i] = my_malloc((len + 1) * sizeof(char));
      /* 复制每一行的内容 */
      memcpy(destination[i], source[i], len * sizeof(char));
      destination[i][len] = '\0';
    } else {
      destination[i] = NULL;
    }
  }
  return destination;
}

void init2DArray(char **array, int num)
{
  int i = 0;
  for (i = 0; i < num; i++) {
    array[i] = NULL;
  }
}

void reset2DArray(char **array, int num)
{
  int i = 0;
  if (array == NULL) {
    return;
  }
  for (i = 0; i < num; i++) {
    if (array[i]) {
      my_free(array[i]);
      array[i] = NULL;
    }
  }
}

void free2DArray(char ***array, int num)
{
  int i = 0;

  if (array == NULL || *array == NULL) {
    return;
  }

  for (i = 0; i < num; i++) {
    if ((*array)[i]) {
      my_free((*array)[i]);
    }
  }
  my_free(*array);
  *array = NULL;
}

/**
 * @brief  如果字段名为a.b(表名.字段)的形式，提取b(字段)
 * @param  input:
 * @return const char*:
 */
const char *extractFieldName(const char *input)
{
  // 查找 '.' 字符的位置
  const char *dotPosition = strchr(input, '.');

  if (dotPosition != NULL) {
    // 如果找到了 '.', 返回 '.' 后面的部分
    return dotPosition + 1;
  } else {
    // 如果没有找到 '.', 返回整个输入字符串
    return input;
  }
}

/**
 * @brief  检查字符串原类型
 * @param  str:
 * @return int: 1: 整数 2: 浮点数 3: 字符串
 */
int checkStringType(const char *str)
{
  int hasDecimalPoint = 0;
  int hasDigit        = 0;
  int i               = 0;
  // 检查空字符串
  if (str == NULL || *str == '\0') {
    return 3;
  }
  // 如果字符串以正号或负号开头
  if (str[i] == '+' || str[i] == '-') {
    i++;
  }

  for (; str[i] != '\0'; i++) {
    if (isdigit(str[i])) {
      hasDigit = 1;
    } else if (str[i] == '.') {
      if (hasDecimalPoint) {
        // 如果已经有一个小数点了，返回字符串类型
        return 3;
      }
      hasDecimalPoint = 1;
    } else {
      // 任何非数字或非小数点的字符表示这是一个普通字符串
      return 3;
    }
  }

  if (hasDigit) {
    if (hasDecimalPoint) {
      return 2;
    } else {
      return 1;
    }
  } else {
    return 3;
  }
}

/**
 * @brief 转移指针的所有权
 *
 * @param data
 * @return void*
 */
void *ptrMove(void **data)
{
  void *temp = NULL;
  if (data == NULL || *data == NULL) {
    return NULL;
  }
  temp  = *data;
  *data = NULL;
  return temp;
}

int ptrSwap(void **data1, void **data2)
{
  void *temp = NULL;
  if (data1 == NULL || data2 == NULL)
    return GNCDB_PTR_SWAP_FAILED;  // 仅检查二级指针本身是否为NULL
  temp   = *data1;
  *data1 = *data2;
  *data2 = temp;
  return GNCDB_SUCCESS;
}

/**
 * @brief  将整数转换为字符串
 * 
 * @param val  要要转换的整数
 * @param buf  转换后的字符串缓冲区
 * @return int 转换后的字符符串的长度
 */
int def_itoa(int val, char *buf)
{
  char        *p = NULL;
  char        *min_str;
  char        *digit_start;
  unsigned int u;
  int          len;
  char        *q;
  char         temp;

  /* 特殊情况：处理0值 */
  if (val == 0) {
    buf[0] = '0';
    buf[1] = '\0';
    return 1;
  }

  p = buf;

  /* 处理负数 */
  if (val < 0) {
    *p++ = '-';

    /* 处理INT_MIN的特殊情况，避免-val溢出 */
    if (val == INT_MIN) {
      /* INT_MIN = -2147483648 */
      min_str = "2147483648";
      q       = p;
      while ((*q++ = *min_str++)) {}
      return 11; /* "-" + 10位数字 */
    }

    val = -val;
  }

  /* 保存数字开始的位置，以便后续反转 */
  digit_start = p;

  /* 转换为无符号类型，避免有符号整数除法带来的额外开销 */
  u = (unsigned int)val;

  /* 从低位到高位生成数字 */
  do {
    /* 使用单步计算替代两步计算 */
    *p++ = (u % 10) + '0';
    u /= 10;
  } while (u);

  /* 计算长度并添加字符串结束符 */
  len  = p - buf;
  *p-- = '\0';

  /* 反转数字部分 */
  q = digit_start;
  while (q < p) {
    temp = *q;
    *q++ = *p;
    *p-- = temp;
  }

  return len;
}

int Int64ToText(int64_t v, char *zOut)
{
  int      i;
  uint64_t x;
  char     zTemp[22];
  if (v < 0) {
    x = (v == SMALLEST_INT64) ? ((uint64_t)1) << 63 : (uint64_t)-v;
  } else {
    x = v;
  }
  i                        = sizeof(zTemp) - 2;
  zTemp[sizeof(zTemp) - 1] = 0;
  while (1) {
    zTemp[i] = (x % 10) + '0';
    x        = x / 10;
    if (x == 0)
      break;
    i--;
  }
  if (v < 0)
    zTemp[--i] = '-';
  memcpy(zOut, &zTemp[i], sizeof(zTemp) - i);
  return sizeof(zTemp) - 1 - i;
}

/**
 * @brief 使用 dtoa_r 将双精度浮点数转换为字符串，模拟 sprintf("%.15g", ...) 的行为。
 *        当指数超出范围时，自动切换到科学计数法。
 *
 * @param value 要转换的双精度浮点数。
 * @param str   用于存储结果字符串的缓冲区。
 * @param str_len 结果缓冲区的最大长度 (包括 null 终止符)。
 * @return int 0 表示成功，非 0 表示失败 (例如缓冲区太小或内部错误)。
 */
int format_double_dtoa(double value, char *str, size_t str_len)
{
  int    decpt, sign;
  char  *end;
  char   temp_buf[40];  // 稍微增大临时缓冲区以防万一
  size_t temp_buf_len = sizeof(temp_buf);
  size_t required_len = 0;
  size_t current_pos  = 0;
  size_t digits_len;
  char  *dtoa_result;
  bool   use_scientific;
  int    exponent;
  size_t copy_len;
  int    exp_val;
  char   exp_buf[5];  // 指数最多约308，需要3位 + 符号 + '\0'
  int    exp_idx = 0;
  int    leading_zeros;
  int    i;
  size_t trailing_zeros;
  size_t remaining_digits;
  size_t inf_len;

  // 检查输入参数
  if (str == NULL || str_len == 0) {
    return GNCDB_DTOA_FAILED;  // 无效参数
  }

  // 处理 NaN 和 Infinity
  if (isnan(value)) {
    if (str_len < 4)
      return GNCDB_DTOA_FAILED;  // 缓冲区太小
    strcpy(str, "nan");
    return GNCDB_SUCCESS;
  }
  if (isinf(value)) {
    inf_len = (value < 0) ? 4 : 3;
    if (str_len < inf_len + 1)
      return GNCDB_DTOA_FAILED;  // 缓冲区太小
    strcpy(str, (value < 0) ? "-inf" : "inf");
    return GNCDB_SUCCESS;
  }

  // 调用 dtoa_r 获取数字串、小数点位置和符号
  // 仍然使用 mode=2, ndigits=15 获取足够精度的数字和 decpt
  dtoa_result = dtoa_r(value, 2, 15, &decpt, &sign, &end, temp_buf, temp_buf_len);

  if (dtoa_result == NULL) {
    if (str_len > 0)
      str[0] = '\0';
    return GNCDB_DTOA_FAILED;  // dtoa_r 失败
  }

  digits_len = end - temp_buf;  // 获取到的数字串长度

  // --- 决定使用定点还是科学计数法 ---
  // %.15g 通常在 exponent < -4 或 exponent >= precision (15) 时切换
  // decpt 大约是 exponent + 1
  use_scientific = (decpt < -3 || decpt >= 15);

  // --- 预估所需总长度 ---
  required_len = 0;
  if (sign) {
    required_len++;  // '-'
  }

  if (use_scientific) {
    // 格式: -d.dddd...e[+/-]eee
    required_len += 1;  // 第一位数字
    if (digits_len > 1) {
      required_len += 1;                 // '.'
      required_len += (digits_len - 1);  // 小数点后的数字
    }
    required_len += 1;  // 'e'
    required_len += 1;  // 指数符号 '+' or '-'
    exponent = decpt - 1;
    if (exponent == 0) {
      required_len += 1;  // "0"
    } else {
      int exp_val = abs(exponent);
      do {
        required_len++;
        exp_val /= 10;
      } while (exp_val > 0);
    }
  } else {
    // 定点格式的长度计算 (之前的逻辑)
    if (decpt <= 0) {
      required_len += 2;         // "0."
      required_len += (-decpt);  // 前导零
      required_len += digits_len;
    } else {                      // decpt > 0
      if (decpt >= digits_len) {  // 整数
        required_len += digits_len;
        required_len += (decpt - digits_len);  // 尾随零
      } else {                                 // 混合
        required_len += decpt;                 // 点前
        required_len += 1;                     // '.'
        required_len += (digits_len - decpt);  // 点后
      }
    }
  }
  required_len++;  // '\0'

  // --- 检查缓冲区大小 ---
  if (required_len == 0 || required_len > str_len) {  // required_len == 0 可能表示溢出
    if (str_len > 0)
      str[0] = '\0';
    return GNCDB_DTOA_FAILED;  // 缓冲区太小或长度计算溢出
  }

  // --- 构造最终字符串 ---
  current_pos = 0;
  if (sign) {
    str[current_pos++] = '-';
  }

  if (use_scientific) {
    exponent = decpt - 1;
    // 写入第一位数字
    if (current_pos >= str_len - 1)
      return GNCDB_DTOA_FAILED;  // 边界检查
    str[current_pos++] = temp_buf[0];
    // 写入小数点和后面的数字 (如果存在)
    if (digits_len > 1) {
      if (current_pos >= str_len - 1)
        return GNCDB_DTOA_FAILED;  // 边界检查
      str[current_pos++] = '.';
      copy_len           = digits_len - 1;
      if (current_pos + copy_len >= str_len)
        return GNCDB_DTOA_FAILED;  // 边界检查
      memcpy(str + current_pos, temp_buf + 1, copy_len);
      current_pos += copy_len;
    }
    // 写入 'e' 和指数符号
    if (current_pos + 1 >= str_len)
      return GNCDB_DTOA_FAILED;  // 边界检查 (需要空间放 'e' 和符号)
    str[current_pos++] = 'e';
    str[current_pos++] = (exponent < 0) ? '-' : '+';
    // 写入指数值
    exp_val = abs(exponent);
    exp_idx = 0;
    if (exp_val == 0) {
      exp_buf[exp_idx++] = '0';
    } else {
      while (exp_val > 0 && exp_idx < sizeof(exp_buf) - 1) {  // 留一位给'\0'
        exp_buf[exp_idx++] = (exp_val % 10) + '0';
        exp_val /= 10;
      }
      if (exp_val > 0)
        return GNCDB_DTOA_FAILED;  // 指数太大无法放入 exp_buf
    }
    // 反向复制指数数字
    if (current_pos + exp_idx >= str_len)
      return GNCDB_DTOA_FAILED;  // 边界检查
    while (exp_idx > 0) {
      str[current_pos++] = exp_buf[--exp_idx];
    }
  } else {
    // 定点格式的构造 (之前的逻辑，增加边界检查)
    if (decpt <= 0) {
      if (current_pos + 1 >= str_len)
        return GNCDB_DTOA_FAILED;  // "0."
      str[current_pos++] = '0';
      str[current_pos++] = '.';
      leading_zeros      = -decpt;
      if (current_pos + leading_zeros >= str_len)
        return GNCDB_DTOA_FAILED;
      for (int i = 0; i < leading_zeros; ++i) {
        str[current_pos++] = '0';
      }
      if (current_pos + digits_len >= str_len)
        return GNCDB_DTOA_FAILED;
      memcpy(str + current_pos, temp_buf, digits_len);
      current_pos += digits_len;
    } else {                      // decpt > 0
      if (decpt >= digits_len) {  // 整数
        trailing_zeros = (size_t)decpt - digits_len;
        if (current_pos + digits_len + trailing_zeros >= str_len)
          return GNCDB_DTOA_FAILED;
        memcpy(str + current_pos, temp_buf, digits_len);
        current_pos += digits_len;
        for (i = 0; i < trailing_zeros; ++i) {
          str[current_pos++] = '0';
        }
        str[current_pos++] = '.';
        str[current_pos++] = '0';
      } else {  // 混合
        remaining_digits = digits_len - decpt;
        if (current_pos + decpt + 1 + remaining_digits >= str_len)
          return GNCDB_DTOA_FAILED;
        memcpy(str + current_pos, temp_buf, decpt);
        current_pos += decpt;
        str[current_pos++] = '.';
        memcpy(str + current_pos, temp_buf + decpt, remaining_digits);
        current_pos += remaining_digits;
      }
    }
  }

  // 添加 null 终止符
  if (current_pos >= str_len)
    return GNCDB_DTOA_FAILED;  // 最终边界检查
  str[current_pos] = '\0';

  return GNCDB_SUCCESS;  // 成功
}