// #ifndef _UTHASHMAP_H_
// #define _UTHASHMAP_H_

// #include "uthash.h"
// #include "gncdbconstant.h"

// #define INTKEY 1
// #define STRKEY 2

// typedef int (*HashCode)(struct HashMap *hashMap, void *key);

// typedef struct IntHashMap {
//     int keyInt;
//     void* data;
//     UT_hash_handle hh;
// }IntHashMap;

// typedef struct StrHashMap {
//     char* keyStr;
//     void* data;
//     UT_hash_handle hh;
// }StrHashMap;

// typedef struct HashMap
// {
// 	int           entryCount; /* 当前大小 */
// 	int           keyType;    /* key值类型 */

// 	union {
// 		struct IntHashMap *intHashMap;
// 		struct StrHashMap *strHashMap;
// 	}
// } HashMap;


// typedef struct Entry
// {
// 	void         *key;   /* 键 */
// 	void         *value; /* 值 */
// } Entry;

// typedef struct HashMapIterator
// {
// 	struct Entry   entry; /* 迭代器当前指向 */
// 	int             count; /* 迭代次数 */
// 	union {
// 		struct IntHashMap *intHashMap;
// 		struct StrHashMap *strHashMap;
// 	}
// 	struct HashMap *hashMap;
// } HashMapIterator;

// // 创建一个哈希结构
// HashMap *hashMapCreate(int keyType, int fixBuck, HashCode hashCode);
// // 销毁map
// void hashMapDestroy(HashMap **hashMap);
// // 判断键是否存在
// bool hashMapExists(HashMap *hashMap, void *key);
// // 添加键值对
// int hashMapPut(HashMap *hashMap, void *key, void *value);
// // 获取键对应值
// void *hashMapGet(HashMap *hashMap, void *key);
// // 删除键
// int hashMapRemove(HashMap *hashMap, void *key);
// // 清空Map
// void hashMapClear(HashMap *hashMap);


// // 创建哈希结构迭代器
// HashMapIterator *createHashMapIterator(HashMap *hashMap);
// // 迭代器是否有下一个
// bool hasNextHashMapIterator(HashMapIterator *iterator);
// // 迭代到下一次
// HashMapIterator *nextHashMapIterator(HashMapIterator *iterator);
// // 释放迭代器内存
// void freeHashMapIterator(HashMapIterator **iterator);


// #endif