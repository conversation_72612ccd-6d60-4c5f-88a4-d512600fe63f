// #include "uthashmap.h"

// HashMap *hashMapCreate(int keyType, int fixBuckNum, HashCode hashCode){
//     HashMap *map = NULL;
//     map = (HashMap *)my_malloc(sizeof(HashMap));
//     if(map == NULL){
//         return NULL;
//     }
//     map->keyType = keyType;
//     if(keyType == INTKEY){
//         map->intHashMap = NULL;
//     } else {
//         map->strHashMap = NULL;
//     }
//     map->entryCount = 0;
//     return map;
// }

// bool hashMapExists(HashMap *hashMap, void *key){
// 	int intKey = 0;
// 	char* strKey = NULL;
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	if(hashMap->keyType == INTKEY){
// 	    intKey = *(int *)key;
// 		HASH_FIND_INT(hashMap->intHashMap, &intKey, intEntry);
// 		if(intEntry == NULL){
// 		    return false;
// 		} else {
// 		    return true;
// 		}
// 	} else if(hashMap->keyType == STRKEY){
// 	    strKey = (char *)key;
// 		HASH_FIND_STR(hashMap->strHashMap, strKey, strEntry);
// 		if(strEntry == NULL){
// 		    return false;
// 		} else {
// 		    return true;
// 		}
// 	}
// }

// int hashMapPut(HashMap *hashMap, void *key, void *value){
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	char* strKey = NULL;
// 	if(hashMap->keyType == INTKEY) {
// 		HASH_FIND_INT(hashMap->intHashMap, key, intEntry);
// 		if(intEntry == NULL){
// 		    intEntry = (IntHashMap *)my_malloc(sizeof(IntHashMap));
// 			if(intEntry == NULL){
// 			    return GNCDB_SPACE_LACK;
// 			}
// 			intEntry->key = *(int *)key;
// 			intEntry->value = value;
// 			HASH_ADD_INT(hashMap->intHashMap, key, intEntry);
// 		}
// 		hashMap->entryCount = HASH_COUNT(hashMap->intHashMap);
// 	} else if(hashMap->keyType == STRKEY) {
// 		HASH_FIND_STR(hashMap->strHashMap, key, strEntry);
// 		if(strEntry == NULL){
// 		    strEntry = (StrHashMap *)my_malloc(sizeof(StrHashMap));
// 			if(strEntry == NULL){
// 			    return GNCDB_SPACE_LACK;
// 			}
// 			strKey = (char *)key;
// 			strEntry->key = my_malloc(strlen(strKey) + 1);
// 			if(strEntry->key == NULL){
// 			    return GNCDB_SPACE_LACK;
// 			}
// 			strcpy(strEntry->key, strKey);

// 			strEntry->value = value;
// 			HASH_ADD_STR(hashMap->strHashMap, key, strEntry);
// 		}
// 		hashMap->entryCount = HASH_COUNT(hashMap->strHashMap);
// 	}
// 	return GNCDB_SUCCESS;
// }


// void *hashMapGet(HashMap *hashMap, void *key){
// 	int intKey = 0;
// 	char* strKey = NULL;
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	if(hashMap->keyType == INTKEY){
// 	    intKey = *(int *)key;
// 		HASH_FIND_INT(hashMap->intHashMap, &intKey, intEntry);
// 		if(intEntry == NULL){
// 		    return NULL;
// 		} else {
// 		    return intEntry->value;
// 		}
// 	} else if(hashMap->keyType == STRKEY){
// 	    strKey = (char *)key;
// 		HASH_FIND_STR(hashMap->strHashMap, strKey, strEntry);
// 		if(strEntry == NULL){
// 		    return NULL;
// 		} else {
// 		    return strEntry->value;
// 		}
// 	}
// }

// int hashMapRemove(HashMap *hashMap, void *key){
// 	int intKey = 0;
// 	char* strKey = NULL;
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	if(hashMap->keyType == INTKEY){
// 	    intKey = *(int *)key;
// 		HASH_FIND_INT(hashMap->intHashMap, &intKey, intEntry);
// 		if(intEntry == NULL){
// 		    return GNCDB_PARAMNULL;
// 		}
// 		HASH_DEL(hashMap->intHashMap, intEntry);
// 		my_free(intEntry);
// 		hashMap->entryCount = HASH_COUNT(hashMap->intHashMap);
// 	} else if(hashMap->keyType == STRKEY){
// 	    strKey = (char *)key;
// 		HASH_FIND_STR(hashMap->strHashMap, strKey, strEntry);
// 		if(strEntry == NULL){
// 		    return GNCDB_PARAMNULL;
// 		}
// 		HASH_DEL(hashMap->strHashMap, strEntry);
// 		my_free(strEntry->key);
// 		my_free(strEntry);
// 		hashMap->entryCount = HASH_COUNT(hashMap->strHashMap);
// 	}
// 	return GNCDB_SUCCESS;
// }

// void hashMapClear(HashMap *hashMap){
//     IntHashMap *intEntry = NULL;
//     StrHashMap *strEntry = NULL;
//     IntHashMap *intEntryTmp = NULL;
//     StrHashMap *strEntryTmp = NULL;
//     if(hashMap->keyType == INTKEY){
//         HASH_ITER(hh, hashMap->intHashMap, intEntry, intEntryTmp){
//             HASH_DEL(hashMap->intHashMap, intEntry);
//             my_free(intEntry);
//     	}
// 		hashMap->entryCount = HASH_COUNT(hashMap->intHashMap);
// 	} else if(hashMap->keyType == STRKEY){
//         HASH_ITER(hh, hashMap->strHashMap, strEntry, strEntryTmp){
//             HASH_DEL(hashMap->strHashMap, strEntry);
//             my_free(strEntry->key);
//             my_free(strEntry);
//     	}
// 		hashMap->entryCount = HASH_COUNT(hashMap->strHashMap);
// 	}	
// }

// void hashMapDestroy(HashMap **hashMap){
//     hashMapClear(*hashMap);
//     my_free(*hashMap);
//     *hashMap = NULL;
// }

// HashMapIterator *createHashMapIterator(HashMap *hashMap)
// {
// 	HashMapIterator *iterator = my_malloc(sizeof(struct HashMapIterator));
// 	if (iterator == NULL) {
// 		return NULL;
// 	}
// 	iterator->hashMap = hashMap;
// 	iterator->count   = 0;
// 	if(hashMap->keyType == INTKEY){
// 		iterator->intHashMap = hashMap->intHashMap;
// 	} else if(hashMap->keyType == STRKEY){
// 		iterator->strHashMap = hashMap->strHashMap;
// 	}
// 	return iterator;
// }

// bool hasNextHashMapIterator(HashMapIterator *iterator)
// {
// 	if (iterator->intHashMap != NULL && iterator->count < iterator->hashMap->entryCount) {
// 		return true;
// 	}
// 	return false;
// }

// HashMapIterator *nextHashMapIterator(HashMapIterator *iterator)
// {
// 	if(iterator->hashMap->keyType == INTKEY){
// 		iterator->entry.key = &(iterator->intHashMap->key);
// 		iterator->entry.value = iterator->intHashMap->value;
// 		iterator->intHashMap = iterator->intHashMap->hh.next;
// 	} else if(iterator->hashMap->keyType == STRKEY){
// 		iterator->entry.key = iterator->strHashMap->key;
// 		iterator->entry.value = iterator->strHashMap->value;
// 		iterator->strHashMap = iterator->strHashMap->hh.next;
// 	}
// 	iterator->count++;
// 	return iterator;
// }

// void freeHashMapIterator(HashMapIterator **iterator)
// {
// 	my_free(*iterator);
// 	*iterator = NULL;
// }