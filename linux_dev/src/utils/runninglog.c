#include <sys/time.h>
#include <unistd.h>
#include "runninglog.h"
#include "gncdbconstant.h"
#include "gncdb.h"
#include "os.h"
#include<sys/timeb.h>
#include "stdarg.h"

static struct DBLog* dbLog_sta;


/// <summary>
/// dbLog的构造函数
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
DBLog* DBLogConstruct(struct GNCDB* db) {
    FILE* file = NULL;
    char logFileName[64] = { 0 };
    DBLog* dbLog = my_malloc(sizeof(DBLog));
	if (dbLog == NULL)
	{
		return NULL;
	}
	memset(dbLog->dbName, 0, 16);
	getFileName(db->fileName, dbLog->dbName, false);

#if defined _WIN32
	sprintf(logFileName, "%s\\log\\DBlog_%s.log",LOCAL_PATH, dbLog->dbName);
#else
	sprintf(logFileName, "%s/log/DBlog_%s.log",LOCAL_PATH, dbLog->dbName);
#endif
	file = fopen(logFileName, "w+");
	if (file == NULL)
	{
		perror("Error opening file");
		my_free(dbLog);
		return NULL;
	}
	dbLog->logFile = file;
	memset(dbLog->logFileName, 0, 32);
	strcpy(dbLog->logFileName, logFileName);

	dbLog_sta = dbLog;
	return dbLog;
}
int logType2str(int logType, char* type) {
	switch (logType)
	{
	case LOG_TRACE:
		strcpy(type, "TRACE");
		break;
    case LOG_DEBUG:
        strcpy(type, "DEBUG");
        break;
	case LOG_INFO:
		strcpy(type, "INFO");
		break;
	case LOG_WARNING:
		strcpy(type, "WARNING");
		break;
	case LOG_ERROR:
		strcpy(type, "ERROR");
		break;
	default:
		strcpy(type, "TYPENULL");
		break;
	}
	return GNCDB_SUCCESS;
}
// void getTime(char* nowtime) {
// 	struct timeb t_tmb;
// 	struct tm* t_tm;
// 	ftime(&t_tmb);
// 	t_tm = localtime(&t_tmb.time);
// 	sprintf(nowtime, "%02d-%02d  %02d:%02d:%02d:%3d",
// 		t_tm->tm_mon + 1, t_tm->tm_mday, t_tm->tm_hour, t_tm->tm_min, t_tm->tm_sec, t_tmb.millitm);
// }
void getTime(char* nowtime) {
    struct timeval tv;
	time_t now;
	struct tm * t_tm;
    gettimeofday(&tv, NULL);
    now = tv.tv_sec;
    t_tm = localtime(&now);

    sprintf(nowtime, "%02d-%02d  %02d:%02d:%02d:%06ld",
        t_tm->tm_mon + 1, t_tm->tm_mday, t_tm->tm_hour, t_tm->tm_min, t_tm->tm_sec, tv.tv_usec);
}
/// <summary>
/// 打印日志
/// </summary>
/// <param name="logType"></param>
/// <param name="logStr"></param>
/// <param name="fileName"></param>
/// <param name="func"></param>
/// <param name="rowNum"></param>
/// <returns></returns>
void DBLoggerPrint(int logType, const char* constFileName, const char* constFunc, int line, const char* format, ...) {
    BYTE* buf = NULL;
    int len = 0;
    int rc = GNCDB_SUCCESS;
    char time[100] = { 0 };
    char pidStr[20];
    char rowStr[10] = { 0 };
    char type[10] = { 0 };
    char name[100] = { 0 };
	char fileName[100] = { 0 };
	char logStr[100] = {0};
	char func[100] = { 0 };
    pthread_t ptid = 0;

	
	va_list args;
    va_start(args, format);
    vsnprintf(logStr, sizeof(logStr), format, args);
    va_end(args);
	
	if(!(logType == LOG_ERROR || logType == LOG_TRACE)){
		return;
	}
	buf = my_malloc(sizeof(BYTE) * 500); 
	memset(buf, 0, sizeof(BYTE) * 500);
	// <time>
	getTime(time);
	rc = writeString(time, &buf[len], &len) || writeString("   ", &buf[len], &len);
	if (rc)
	{
		rc = writeString("<-?", &buf[len], &len);
		rc = GNCDB_SUCCESS;
	}
	// <pid>
    ptid = pthread_self();
	sprintf(pidStr, "PID=%lu", ptid);  // 将long类型转换为字符串
	rc = writeChar(' ', &buf[len], &len) || writeString(pidStr, &buf[len], &len) || writeChar(' ', &buf[len], &len);
	if (rc)
	{
		rc = writeString("<-?", &buf[len], &len);
		rc = GNCDB_SUCCESS;
	}
	//<logType>
	rc = logType2str(logType, type);
	rc = rc || writeChar(' ', &buf[len], &len) || writeString(type, &buf[len], &len) || writeChar(' ', &buf[len], &len);
	if (rc)
	{
		rc = writeString("<-?", &buf[len], &len);
		rc = GNCDB_SUCCESS;
	}
	//<logStr>
	if (strlen(logStr) == 0) {
		rc = writeString(" [", &buf[len], &len) || writeString("NULL", &buf[len], &len) || writeString("] ", &buf[len], &len);
	}
	else rc = writeString(" [", &buf[len], &len) || writeString(logStr, &buf[len], &len) || writeString("] ", &buf[len], &len);
	if (rc)
	{
		rc = writeString("<-?", &buf[len], &len);
		rc = GNCDB_SUCCESS;
	}
	//<fileName:line>
	strcpy(fileName, constFileName);
	getFileName(fileName, name, 1);
//    if(strcmp(name, "pagepool.c") == 0 || strcmp(name, "catalog.c") == 0)
//    {
//        my_free(buf);
//        return ;
//    }
	sprintf(rowStr, ":%d", line);
	rc =  writeChar(' ', &buf[len], &len) ||  writeString(name, &buf[len], &len) ||writeString(rowStr, &buf[len], &len)  || writeChar(' ', &buf[len], &len);
	if (rc)
	{
		rc = writeString("<-?", &buf[len], &len);
		rc = GNCDB_SUCCESS;
	}
	//<func>
	strcpy(func, constFunc);
	rc = writeString(func, &buf[len], &len) || writeString("()", &buf[len], &len);
	if (rc)
	{
		rc = writeString("<-?", &buf[len], &len);
		rc = GNCDB_SUCCESS;
	}
	//<rownum>
	//itoa(rowNum, rowStr, 10);

	rc = writeString("\n", &buf[len], &len);//换行
	if (rc)
	{
		rc = writeString("<-?", &buf[len], &len);
		rc = GNCDB_SUCCESS;
	}
	rc = osWrite(dbLog_sta->logFile, buf, -1, len);
	if (rc != GNCDB_SUCCESS)
	{
		my_free(buf);
		return;
	}
	my_free(buf);
	return;
}
/// <summary>
/// 打印日志模块销毁
/// </summary>
/// <param name="dbLog"></param>
/// <returns></returns>
void DBLoggerDestory(DBLog** dbLog) {
	/* 1.判断参数是否为空 */
	if (dbLog == NULL || *dbLog == NULL) {
		return;
	}
	fclose((*dbLog)->logFile);
	/* 2.释放内存空间 */
	my_free(*dbLog);
	*dbLog = NULL;
}

