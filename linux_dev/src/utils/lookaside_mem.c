#include "lookaside_mem.h"
#include "hashmap.h"
#include "gncdbconstant.h"
#include "typedefine.h"
#include <string.h>

extern bool hashMapIntEqual(void *key1, void *key2);
extern bool hashMapStrEqual(void *key1, void *key2);
extern int  hashInt(HashMap *hashMap, void *key);
extern int  hashStr(HashMap *hashMap, void *key);
void        hashMapResetLookaside(GNCDB *db, HashMap *hashMap, int bucketNum);
void        hashMapDuplicateReset(GNCDB *db, HashMap *hashMap, int bucketNum);

/*
** Return the number of LookasideSlot elements on the linked list
*/
static unsigned int countLookasideSlots(LookasideSlot *p)
{
  unsigned int cnt = 0;
  while (p) {
    p = p->pNext;
    cnt++;
  }
  return cnt;
}

/*
** Count the number of slots of lookaside memory that are outstanding
*/
int lookasideUsed(GNCDB *db, int *pHighwater)
{
  unsigned int nInit = countLookasideSlots(db->lookaside->pInit);
  unsigned int nFree = countLookasideSlots(db->lookaside->pFree);
  nInit += countLookasideSlots(db->lookaside->pSmallInit);
  nFree += countLookasideSlots(db->lookaside->pSmallFree);
  assert(db->lookaside->nSlot >= nInit + nFree);
  if (pHighwater)
    *pHighwater = (int)(db->lookaside->nSlot - nInit);
  return (int)(db->lookaside->nSlot - (nInit + nFree));
}

/*
** Set up the lookaside buffers for a database connection.
** Return SQLITE_OK on success.
** If lookaside is already active, return SQLITE_BUSY.
**
** The sz parameter is the number of bytes in each lookaside slot.
** The cnt parameter is the number of slots.  If pBuf is NULL the
** space for the lookaside memory is obtained from sqlite3_malloc()
** or similar.  If pBuf is not NULL then it is sz*cnt bytes of memory
** to use for the lookaside memory.
*/
int setupLookaside(GNCDB *db,   /* Database connection being configured */
    void                 *pBuf, /* Memory to use for lookaside.  May be NULL */
    int                   sz,   /* Desired size of each lookaside memory slot */
    int                   cnt   /* Number of slots to allocate */
)
{
  void          *pStart;  /* Start of the lookaside buffer */
  long           szAlloc; /* Total space set aside for lookaside memory */
  int            nBig;    /* Number of full-size slots */
  int            nSm;     /* Number smaller LOOKASIDE_SMALL-byte slots */
  int            i;
  LookasideSlot *p;

  if (!db->lookaside) {
    /* If lookaside is not already active, allocate a new lookaside
    ** structure and initialize it.
    */
    db->lookaside = (Lookaside *)my_malloc(sizeof(Lookaside));
  }

  if (lookasideUsed(db, NULL) > 0) {
    return GNCDB_MEM;
  }
  /* Free any existing lookaside buffer for this handle before
  ** allocating a new one so we don't have to have space for
  ** both at the same time.
  */
  if (db->lookaside->bMalloced) {
    my_free(db->lookaside->pStart);
  }
  /* The size of a lookaside slot after ROUNDDOWN8 needs to be larger
  ** than a pointer and small enough to fit in a u16.
  */
  sz = ROUNDDOWN8(sz);
  if (sz <= (int)sizeof(LookasideSlot *))
    sz = 0;
  if (sz > 65528)
    sz = 65528;
  /* Count must be at least 1 to be useful, but not so large as to use
  ** more than 0x7fff0000 total bytes for lookaside. */
  if (cnt < 1)
    cnt = 0;
  if (sz > 0 && cnt > (0x7fff0000 / sz))
    cnt = 0x7fff0000 / sz;
  szAlloc = (long)sz * (long)cnt;
  if (szAlloc == 0) {
    sz     = 0;
    pStart = 0;
  } else if (pBuf == 0) {
    pStart = my_malloc(szAlloc);
  } else {
    pStart = pBuf;
  }
  if (sz >= LOOKASIDE_SMALL * 3) {
    nBig = szAlloc / (3 * LOOKASIDE_SMALL + sz);
    nSm  = (szAlloc - (long)sz * (long)nBig) / LOOKASIDE_SMALL;
  } else if (sz >= LOOKASIDE_SMALL * 2) {
    nBig = szAlloc / (LOOKASIDE_SMALL + sz);
    nSm  = (szAlloc - (long)sz * (long)nBig) / LOOKASIDE_SMALL;
  } else if (sz > 0) {
    nBig = szAlloc / sz;
    nSm  = 0;
  } else {
    nBig = nSm = 0;
  }
  db->lookaside->pStart = pStart;
  db->lookaside->pInit  = 0;
  db->lookaside->pFree  = 0;
  db->lookaside->sz     = (unsigned short)sz;
  db->lookaside->szTrue = (unsigned short)sz;
  if (pStart) {
    assert(sz > (int)sizeof(LookasideSlot *));
    p = (LookasideSlot *)pStart;
    for (i = 0; i < nBig; i++) {
      p->pNext             = db->lookaside->pInit;
      db->lookaside->pInit = p;
      // p                   = p + sz;
      p = (LookasideSlot *)&((BYTE *)p)[sz];
    }
    db->lookaside->pSmallInit = 0;
    db->lookaside->pSmallFree = 0;
    db->lookaside->pMiddle    = p;
    for (i = 0; i < nSm; i++) {
      p->pNext                  = db->lookaside->pSmallInit;
      db->lookaside->pSmallInit = p;
      // p                        = p + LOOKASIDE_SMALL;
      p = (LookasideSlot *)&((BYTE *)p)[LOOKASIDE_SMALL];
    }
    db->lookaside->pEnd       = p;
    db->lookaside->bDisable   = 0;
    db->lookaside->bMalloced  = pBuf == 0 ? 1 : 0;
    db->lookaside->nSlot      = nBig + nSm;
    db->lookaside->nBFreeSlot = nBig;
    db->lookaside->nSFreeSlot = nSm;
  } else {
    db->lookaside->pStart     = 0;
    db->lookaside->pSmallInit = 0;
    db->lookaside->pSmallFree = 0;
    db->lookaside->pMiddle    = 0;
    db->lookaside->pEnd       = 0;
    db->lookaside->bDisable   = 1;
    db->lookaside->sz         = 0;
    db->lookaside->bMalloced  = 0;
    db->lookaside->nSlot      = 0;
  }
  db->lookaside->pTrueEnd = db->lookaside->pEnd;
  assert(lookasideUsed(db, 0) == 0);
  return GNCDB_SUCCESS;
}

int destroyLookaside(GNCDB *db)
{
  if (db->lookaside) {
    if (db->lookaside->bMalloced) {
      my_free(db->lookaside->pStart);
    }
    my_free(db->lookaside);
    db->lookaside = NULL;
  }
  return GNCDB_SUCCESS;
}

void *lookasideMalloc(GNCDB *db, int n)
{
  LookasideSlot *pBuf;

  /* 大于大槽大小，直接malloc */
  if (n > db->lookaside->sz) {
    if (!db->lookaside->bDisable) {
      db->lookaside->anStat[1]++;
    }
    return my_malloc(n);
  }

  /* 小于小槽大小，优先分配小槽 */
  if (n <= LOOKASIDE_SMALL) {
    if ((pBuf = db->lookaside->pSmallFree) != 0) {
      db->lookaside->pSmallFree = pBuf->pNext;
      db->lookaside->nSFreeSlot--;
      db->lookaside->anStat[0]++;
      return (void *)pBuf;
    } else if ((pBuf = db->lookaside->pSmallInit) != 0) {
      db->lookaside->pSmallInit = pBuf->pNext;
      db->lookaside->nSFreeSlot--;
      db->lookaside->anStat[0]++;
      return (void *)pBuf;
    }
  }

  /* 剩余情况从大槽分配 */
  if ((pBuf = db->lookaside->pFree) != 0) {
    db->lookaside->pFree = pBuf->pNext;
    db->lookaside->nBFreeSlot--;
    db->lookaside->anStat[0]++;
    return (void *)pBuf;
  } else if ((pBuf = db->lookaside->pInit) != 0) {
    db->lookaside->pInit = pBuf->pNext;
    db->lookaside->nBFreeSlot--;
    db->lookaside->anStat[0]++;
    return (void *)pBuf;
  } else {
    db->lookaside->anStat[2]++;
  }

  return my_malloc(n);
}

char *lookasideStrDup(GNCDB *db, const char *z)
{
  char  *zNew;
  size_t n;
  if (z == 0) {
    return 0;
  }
  n    = strlen(z) + 1;
  zNew = lookasideMalloc(db, n);
  if (zNew) {
    memcpy(zNew, z, n);
  }
  return zNew;
}

int lookasideFree(GNCDB *db, void *p)
{
  LookasideSlot *pBuf = NULL;
  if (!WITHIN_LOOKASIDE(db, p)) {
    my_free(p);
    return GNCDB_SUCCESS;
  }
  if (p >= db->lookaside->pMiddle) {
    /* This is a small buffer */
    pBuf                      = (LookasideSlot *)p;
    pBuf->pNext               = db->lookaside->pSmallFree;
    db->lookaside->pSmallFree = pBuf;
    db->lookaside->nSFreeSlot++;
  } else {
    /* This is a full-size buffer */
    pBuf                 = (LookasideSlot *)p;
    pBuf->pNext          = db->lookaside->pFree;
    db->lookaside->pFree = pBuf;
    db->lookaside->nBFreeSlot++;
  }
  return GNCDB_SUCCESS;
}

void *lookasideRealloc(GNCDB *db, void *p, int n)
{
  void *pNew = NULL;
  /* p是空的，直接分配 */
  if (!p) {
    return lookasideMalloc(db, n);
  }

  if (p < db->lookaside->pEnd) {
    if (p > db->lookaside->pMiddle) {
      if (n <= LOOKASIDE_SMALL) {
        /* p已经是小槽，直接返回 */
        return p;
      }
    } else if (p >= db->lookaside->pStart) {
      if (n <= db->lookaside->sz) {
        /* p已经是大槽，直接返回 */
        return p;
      }
    }
  }

  /* 重新分配 */
  pNew = lookasideMalloc(db, n);
  if (pNew) {
    /* 重新分配成功，释放原来的内存 */
    memcpy(pNew, p, LOOKASIDE_SLOT_SIZE(db, p));
    lookasideFree(db, p);
  }

  return pNew;
}

/* 下面是使用lookaside内存池进行内存分配的hashMap的一些接口的实现，与实际hashMap逻辑一致，仅仅替换内存分配和释放的函数
 */

HashMap *hashMapCreateLookaside(GNCDB *db, int keyType, int fixBuckNum, HashCode hashCode)
{
  Entry   *p       = NULL;
  int      i       = 0;
  HashMap *hashMap = lookasideMalloc(db, sizeof(struct HashMap));
  if (hashMap == NULL) {
    return NULL;
  }
  // 初始化
  hashMap->entryCount = 0;
  hashMap->keyType    = keyType;
  switch (keyType) {
    case INTKEY: hashMap->equal = hashMapIntEqual; break;
    case STRKEY: hashMap->equal = hashMapStrEqual; break;
    default: lookasideFree(db, hashMap); return NULL;
  }
  if (hashCode != NULL) {
    hashMap->hashCode = hashCode;
  } else {
    switch (keyType) {
      case INTKEY: hashMap->hashCode = hashInt; break;
      case STRKEY: hashMap->hashCode = hashStr; break;
      default: hashMap->hashCode = defaultHashCode;
    }
  }

  if (fixBuckNum != 0) {
    hashMap->bucketNum  = fixBuckNum;
    hashMap->autoAssign = false;
  } else {
    hashMap->bucketNum  = DEFAULTCAPACITY;
    hashMap->autoAssign = true;
  }
  // 为结构体内的指针申请空间
  hashMap->bucketList = lookasideMalloc(db, hashMap->bucketNum * sizeof(struct Entry));
  if (hashMap->bucketList == NULL) {
    // 若没有申请到，则释放结构体空间并返回NULL
    lookasideFree(db, hashMap);
    return NULL;
  }
  // 初始化申请的空间
  p = hashMap->bucketList;
  for (i = 0; i < hashMap->bucketNum; i++) {
    p[i].key = p[i].value = p[i].next = NULL;
  }
  return hashMap;
}

void hashMapClearLookaside(GNCDB *db, HashMap *hashMap)
{
  int    i     = 0;
  Entry *entry = NULL;
  Entry *next  = NULL;
  for (i = 0; i < hashMap->bucketNum; i++) {
    // 释放冲突值内存
    entry = hashMap->bucketList[i].next;
    while (entry != NULL) {
      next = entry->next;
      lookasideFree(db, entry->key);
      entry->key = NULL;
      lookasideFree(db, entry);
      entry = next;
    }
    lookasideFree(db, hashMap->bucketList[i].key);
    hashMap->bucketList[i].next  = NULL;
    hashMap->bucketList[i].key   = NULL;
    hashMap->bucketList[i].value = NULL;
  }

  hashMap->entryCount = 0;
}

void hashMapDestroyLookaside(GNCDB *db, HashMap **hashMap)
{
  if (*hashMap == NULL) {
    return;
  }
  if ((*hashMap)->bucketNum > 0) {
    hashMapClearLookaside(db, *hashMap);
  }
  lookasideFree(db, (*hashMap)->bucketList);
  lookasideFree(db, *hashMap);
  *hashMap = NULL;
  return;
}

int hashMapPutLookaside(GNCDB *db, HashMap *hashMap, void *key, void *value)
{
  // 获取哈希值
  int   *pInt     = NULL;
  char  *pKey     = NULL;
  char  *pStr     = NULL;
  Entry *newEntry = NULL;
  Entry *entry    = NULL;

  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    if (hashMap->keyType == INTKEY) {
      pInt = lookasideMalloc(db, sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt                          = *(int *)key;
      hashMap->bucketList[index].key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = lookasideMalloc(db, strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      hashMap->bucketList[index].key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->keyType == INTKEY) {
        if (*(int *)entry->key == *(int *)key) {
          entry->value = value;
          return GNCDB_SUCCESS;
        }
      } else {
        if (hashMap->equal(entry->key, key)) {
          entry->value = value;
          return GNCDB_SUCCESS;
        }
      }
      entry = entry->next;
    }

    // 发生冲突则创建节点挂到相应的位置
    newEntry = lookasideMalloc(db, sizeof(struct Entry));
    if (newEntry == NULL) {
      return GNCDB_SPACE_LACK;
    }

    if (hashMap->keyType == INTKEY) {
      pInt = lookasideMalloc(db, sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt         = *(int *)key;
      newEntry->key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = lookasideMalloc(db, strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      newEntry->key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    newEntry->value = value;

    newEntry->next                  = hashMap->bucketList[index].next;
    hashMap->bucketList[index].next = newEntry;
    hashMap->entryCount++;
  }

  if (hashMap->autoAssign && hashMap->entryCount >= hashMap->bucketNum) {
    // 内存扩充至原来的两倍
    // *注: 扩充时考虑的是当前存储元素数量与存储空间的大小关系，而不是存储空间是否已经存满，
    // 例如: 存储空间为10，存入了10个键值对，但是全部冲突了，所以存储空间空着9个，其余的全部挂在一个上面，
    // 这样检索的时候和遍历查询没有什么区别了，可以简单这样理解，当我存入第11个键值对的时候一定会发生冲突，
    // 这是由哈希函数本身的特性(取模)决定的，冲突就会导致检索变慢，所以这时候扩充存储空间，对原有键值对进行
    // 再次散列，会把冲突的数据再次分散开，加快索引定位速度。
    hashMapResetLookaside(db, hashMap, HASH_EXPAND(hashMap->bucketNum));
  }

  return GNCDB_SUCCESS;
}

int hashMapPutDuplicateLookaside(GNCDB *db, HashMap *hashMap, void *key, void *value)
{
  // 获取哈希值
  int   *pInt      = NULL;
  char  *pKey      = NULL;
  char  *pStr      = NULL;
  Entry *newEntry  = NULL;
  Entry *entry     = NULL;
  Entry *prevEntry = NULL;

  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    if (hashMap->keyType == INTKEY) {
      pInt = lookasideMalloc(db, sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt                          = *(int *)key;
      hashMap->bucketList[index].key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = lookasideMalloc(db, strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      hashMap->bucketList[index].key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      // printf("%s\n", (char*)entry->key);
      if (hashMap->equal(key, entry->key)) {
        // 如果键值已经存在，但不覆盖值，继续遍历链表
        prevEntry = entry;
        entry     = entry->next;
        continue;
      }
      prevEntry = entry;
      entry     = entry->next;
    }

    // 在链表末尾添加新节点
    newEntry = lookasideMalloc(db, sizeof(struct Entry));
    if (newEntry == NULL) {
      return GNCDB_SPACE_LACK;
    }

    if (hashMap->keyType == INTKEY) {
      pInt = lookasideMalloc(db, sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt         = *(int *)key;
      newEntry->key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = lookasideMalloc(db, strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      newEntry->key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    newEntry->value = value;

    // 将新节点挂到链表末尾
    if (prevEntry != NULL) {
      prevEntry->next = newEntry;
      newEntry->next  = NULL;
    } else {
      hashMap->bucketList[index].next = newEntry;
      newEntry->next                  = NULL;
    }
    hashMap->entryCount++;
  }

  if (hashMap->autoAssign && hashMap->entryCount >= hashMap->bucketNum) {
    // 内存扩充至原来的两倍
    // *注: 扩充时考虑的是当前存储元素数量与存储空间的大小关系，而不是存储空间是否已经存满，
    // 例如: 存储空间为10，存入了10个键值对，但是全部冲突了，所以存储空间空着9个，其余的全部挂在一个上面，
    // 这样检索的时候和遍历查询没有什么区别了，可以简单这样理解，当我存入第11个键值对的时候一定会发生冲突，
    // 这是由哈希函数本身的特性(取模)决定的，冲突就会导致检索变慢，所以这时候扩充存储空间，对原有键值对进行
    // 再次散列，会把冲突的数据再次分散开，加快索引定位速度。
    hashMapDuplicateReset(db, hashMap, HASH_EXPAND(hashMap->bucketNum));
  }

  return GNCDB_SUCCESS;
}

int hashMapRemoveLookaside(GNCDB *db, HashMap *hashMap, void *key)
{
  // 获取哈希值
  bool result = false;
  // void* entryKey = NULL;
  Entry *entry = NULL;
  Entry *temp  = NULL;
  Entry *p     = NULL;
  int    index = hashMap->hashCode(hashMap, key);
  entry        = &hashMap->bucketList[index];
  if (entry->key == NULL) {
    return GNCDB_PARAMNULL;
  }
  // entryKey = entry->key;
  if (hashMap->keyType == INTKEY) {
    if (*(int *)entry->key == *(int *)key) {
      hashMap->entryCount--;
      if (entry->next != NULL) {
        temp = entry->next;
        lookasideFree(db, entry->key);
        entry->key   = temp->key;
        entry->value = temp->value;
        entry->next  = temp->next;
        lookasideFree(db, temp);
      } else {
        lookasideFree(db, entry->key);
        entry->key   = NULL;
        entry->value = NULL;
      }
      result = true;
    } else {
      p     = entry;
      entry = entry->next;
      while (entry != NULL) {
        if (hashMap->equal(entry->key, key)) {
          hashMap->entryCount--;
          p->next = entry->next;
          lookasideFree(db, entry->key);
          entry->key = NULL;
          lookasideFree(db, entry);
          result = true;
          break;
        }
        p     = entry;
        entry = entry->next;
      }
    }
  } else {
    if (hashMap->equal(entry->key, key)) {
      hashMap->entryCount--;
      if (entry->next != NULL) {
        temp = entry->next;
        lookasideFree(db, entry->key);
        entry->key   = temp->key;
        entry->value = temp->value;
        entry->next  = temp->next;
        lookasideFree(db, temp);
      } else {
        lookasideFree(db, entry->key);
        entry->key   = NULL;
        entry->value = NULL;
      }
      result = true;
    } else {
      p     = entry;
      entry = entry->next;
      while (entry != NULL) {
        if (hashMap->equal(entry->key, key)) {
          hashMap->entryCount--;
          p->next = entry->next;
          lookasideFree(db, entry->key);
          entry->key = NULL;
          lookasideFree(db, entry);
          result = true;
          break;
        }
        p     = entry;
        entry = entry->next;
      }
    }
  }
  // 如果空间占用不足一半，则释放多余内存
  if (result && hashMap->autoAssign && hashMap->entryCount < hashMap->bucketNum / 2) {
    hashMapResetLookaside(db, hashMap, HASH_REDUCE(hashMap->bucketNum));
  }
  return GNCDB_SUCCESS;
}

void hashMapResetPutLookaside(GNCDB *db, HashMap *hashMap, void *key, void *value)
{
  Entry *newEntry = NULL;
  Entry *entry    = NULL;
  // 获取哈希值
  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    hashMap->bucketList[index].key   = key;
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->equal(key, entry->key)) {
        // 对于键值已经存在的直接覆盖
        entry->value = value;
        return;
      }
      entry = entry->next;
    }

    // 发生冲突则创建节点挂到相应的位置
    newEntry = lookasideMalloc(db, sizeof(struct Entry));
    if (newEntry == NULL) {
      return;
    }
    newEntry->key                   = key;
    newEntry->value                 = value;
    newEntry->next                  = hashMap->bucketList[index].next;
    hashMap->bucketList[index].next = newEntry;
    hashMap->entryCount++;
  }
}

void hashMapResetLookaside(GNCDB *db, HashMap *hashMap, int bucketNum)
{
  Entry           *tempList   = NULL;
  HashMapIterator *iterator   = NULL;
  int              entryCount = 0;
  Entry           *relist     = NULL;
  int              index      = 0;
  int              i          = 0;
  Entry           *current    = NULL;
  Entry           *temp       = NULL;
  if (hashMap->entryCount == 0) {
    return;
  }

#if DEBUG
  if (bucketNum == 2560) {
    printf("bucketNum:2560\n");
  }
#endif

  // 键值对临时存储空间
  tempList = lookasideMalloc(db, (hashMap->entryCount) * sizeof(struct Entry));
  if (tempList == NULL) {
    return;
  }

  iterator   = createHashMapIterator(hashMap);
  entryCount = hashMap->entryCount;
  for (index = 0; hasNextHashMapIterator(iterator); index++) {
    // 迭代取出所有键值对
    iterator              = nextHashMapIterator(iterator);
    tempList[index].key   = iterator->entry->key;
    tempList[index].value = iterator->entry->value;
    tempList[index].next  = NULL;
  }
  freeHashMapIterator(&iterator);

  // 清除原有键值对数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    current        = &hashMap->bucketList[i];
    current->key   = NULL;
    current->value = NULL;
    current        = current->next;
    while (current != NULL) {
      temp = current->next;
      lookasideFree(db, current);
      current = temp;
    }
  }
  hashMap->entryCount = 0;

  // 更改内存大小
  hashMap->bucketNum = bucketNum;
  relist             = (Entry *)lookasideRealloc(db, hashMap->bucketList, hashMap->bucketNum * sizeof(struct Entry));
  if (relist != NULL) {
    hashMap->bucketList = relist;
    relist              = NULL;
  }

  // 初始化数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    hashMap->bucketList[i].key   = NULL;
    hashMap->bucketList[i].value = NULL;
    hashMap->bucketList[i].next  = NULL;
  }

  // 将所有键值对重新写入内存
  for (i = 0; i < entryCount; i++) {
    hashMapResetPutLookaside(db, hashMap, tempList[i].key, tempList[i].value);
  }
  lookasideFree(db, tempList);
}

void hashMapDuplicateResetPut(GNCDB *db, HashMap *hashMap, void *key, void *value)
{
  Entry *newEntry = NULL;
  Entry *entry    = NULL;
  // 获取哈希值
  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    hashMap->bucketList[index].key   = key;
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->equal(key, entry->key)) {
        // 对于键值已经存在的则放在链表的最后
        while (entry->next != NULL) {
          entry = entry->next;
        }
      }
      entry = entry->next;
    }

    // 发生冲突则创建节点挂到相应的位置
    newEntry = lookasideMalloc(db, sizeof(struct Entry));
    if (newEntry == NULL) {
      return;
    }
    newEntry->key                   = key;
    newEntry->value                 = value;
    newEntry->next                  = hashMap->bucketList[index].next;
    hashMap->bucketList[index].next = newEntry;
    hashMap->entryCount++;
  }
}

void hashMapDuplicateReset(GNCDB *db, HashMap *hashMap, int bucketNum)
{
  Entry           *tempList   = NULL;
  HashMapIterator *iterator   = NULL;
  int              entryCount = 0;
  Entry           *relist     = NULL;
  int              index      = 0;
  int              i          = 0;
  Entry           *current    = NULL;
  Entry           *temp       = NULL;
  if (hashMap->entryCount == 0) {
    return;
  }

#if DEBUG
  if (bucketNum == 2560) {
    printf("bucketNum:2560\n");
  }
#endif

  // 键值对临时存储空间
  tempList = lookasideMalloc(db, (hashMap->entryCount) * sizeof(struct Entry));
  if (tempList == NULL) {
    return;
  }

  iterator   = createHashMapIterator(hashMap);
  entryCount = hashMap->entryCount;
  for (index = 0; hasNextHashMapIterator(iterator); index++) {
    // 迭代取出所有键值对
    iterator              = nextHashMapIterator(iterator);
    tempList[index].key   = iterator->entry->key;
    tempList[index].value = iterator->entry->value;
    tempList[index].next  = NULL;
  }
  freeHashMapIterator(&iterator);

  // 清除原有键值对数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    current        = &hashMap->bucketList[i];
    current->key   = NULL;
    current->value = NULL;
    current        = current->next;
    while (current != NULL) {
      temp = current->next;
      lookasideFree(db, current);
      current = temp;
    }
  }
  hashMap->entryCount = 0;

  // 更改内存大小
  hashMap->bucketNum = bucketNum;
  relist             = (Entry *)lookasideRealloc(db, hashMap->bucketList, hashMap->bucketNum * sizeof(struct Entry));
  if (relist != NULL) {
    hashMap->bucketList = relist;
    relist              = NULL;
  }

  // 初始化数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    hashMap->bucketList[i].key   = NULL;
    hashMap->bucketList[i].value = NULL;
    hashMap->bucketList[i].next  = NULL;
  }

  // 将所有键值对重新写入内存
  for (i = 0; i < entryCount; i++) {
    hashMapDuplicateResetPut(db, hashMap, tempList[i].key, tempList[i].value);
  }
  lookasideFree(db, tempList);
}

HashMapIterator *createHashMapIterLookaside(GNCDB *db, HashMap *hashMap)
{
  HashMapIterator *iterator = lookasideMalloc(db, sizeof(struct HashMapIterator));
  if (iterator == NULL) {
    return NULL;
  }
  iterator->hashMap = hashMap;
  iterator->count   = 0;
  iterator->index   = -1;
  iterator->entry   = NULL;
  return iterator;
}

void freeHashMapIterLookaside(GNCDB *db, HashMapIterator **iterator)
{
  lookasideFree(db, *iterator);
  *iterator = NULL;
}

/* 下面是使用lookaside内存池进行内存分配的varList的一些接口的实现，与实际逻辑一致，仅仅替换内存分配和释放的函数 */

varArrayList *varArrayListCreateLookaside(
    GNCDB *db, int flag, int elementSize, int capacity, Compare compare, Destroy destroy)
{
  varArrayList *newArray = NULL;

  if (elementSize <= 0 || capacity < 0) {
    return NULL;
  }

  /* 为结构体申请空间 */
  newArray = lookasideMalloc(db, sizeof(struct varArrayList));
  if (newArray == NULL) {
    return NULL;
  }
  /* 初始化 */
  if (flag == ORDER) {
    newArray->orderly = true;
  } else if (flag == DISORDER) {
    newArray->orderly = false;
  } else {
    lookasideFree(db, newArray);
    return NULL;
  }
  newArray->capacity     = capacity == 0 ? ARRDEFAULTCAPACITY : capacity;
  newArray->fiexdCap     = capacity == 0 ? false : true;
  newArray->elementSize  = elementSize;
  newArray->elementCount = 0;
  newArray->compare      = compare;
  newArray->destroy      = destroy;
  newArray->dataBuffer   = NULL;
  /* 为结构体内的指针申请空间 */
  newArray->dataBuffer = lookasideMalloc(db, (size_t)(newArray->capacity) * newArray->elementSize);
  if (newArray->dataBuffer == NULL) {
    /* 若没有申请到，则释放结构体空间并返回NULL */
    lookasideFree(db, newArray);
    return NULL;
  }
  /* 将申请的空间清空为0 */
  memset(newArray->dataBuffer, 0, (size_t)(newArray->capacity) * newArray->elementSize);

  return newArray;
}

/* 这里内部元素的内存目前还是从默认的malloc分配的，仅仅容器本身的内存销毁走Lookaside内存池 */
void varArrayListDestroyLookaside(GNCDB *db, varArrayList **array)
{
  char *pData = NULL;
  int   i     = 0;
  if (*array == NULL) {
    return;
  }
  if ((*array)->destroy != NULL) {
    pData = (char *)(*array)->dataBuffer;
    for (i = 0; i < (*array)->elementCount; ++i) {
      (*array)->destroy(pData);
      pData = pData + (*array)->elementSize;
    }
  }

  lookasideFree(db, (*array)->dataBuffer);
  (*array)->dataBuffer = NULL;
  lookasideFree(db, *array);
  *array = NULL;
}

int arrayResizeLookaside(GNCDB *db, varArrayList *array, int flag)
{
  int   capacity = array->capacity;
  int   num      = 0;
  void *newData  = NULL;

  if (flag == EXPAND) {
    /* 容量不够, 将容量扩大 */
    capacity = (int)MEM_EXPAND(capacity);
  } else if (flag == REDUCE) {
    /* 容量多余, 将容量缩小 */
    num      = (int)MEM_REDUCE(capacity);
    capacity = num >= ARRMINCAPACITY ? num : ARRMINCAPACITY;
  }
  /* 根据容量修改内存大小 */
  newData = lookasideRealloc(db, array->dataBuffer, capacity * (array->elementSize));
  if (newData == NULL) {
    return GNCDB_SPACE_LACK;
  }
  array->dataBuffer = newData;
  array->capacity   = capacity;

  return GNCDB_SUCCESS;
}

int varArrayListAddLookaside(GNCDB *db, varArrayList *array, void *data)
{
  int   rc     = 0;
  int   sub    = 0;
  int   offset = 0;
  char *pData  = NULL;

  /* 判断空间是否足够 */
  if (array->elementCount >= array->capacity) {
    if (array->fiexdCap) {
      return GNCDB_CAPOVERFLOW;
    } else {
      /* 内存不够, 将内存扩大 */
      rc = arrayResizeLookaside(db, array, EXPAND);
      if (rc != GNCDB_SUCCESS) {
        return GNCDB_SPACE_LACK;
      }
    }
  }
  if (array->orderly) {
    sub = varArrayListGetPosition(array, data);
    /* 先将别的数据往后挪一位 */
    memmove((char *)(array->dataBuffer) + (sub + 1) * array->elementSize,
        (char *)(array->dataBuffer) + sub * array->elementSize,
        (array->elementCount - sub) * array->elementSize);

    /* 插入数据 */
    offset = sub * array->elementSize;
    memcpy((char *)(array->dataBuffer) + offset, data, array->elementSize);

    array->elementCount++;
  } else {
    pData = (char *)array->dataBuffer + array->elementCount * array->elementSize;  // 计算内存中的数据的位置
    memcpy(pData, data, array->elementSize);

    array->elementCount++;
  }

  return GNCDB_SUCCESS;
}

int varArrayListAddPtrLookaside(GNCDB *db, varArrayList *array, void *pointer)
{
  return varArrayListAddLookaside(db, array, &pointer);
}

int varArrayListRmLookaside(GNCDB *db, varArrayList *array, void *data)
{
  char *pData = NULL;
  int   size  = 0;
  int   index = varArrayListIndexOf(array, data);

  if (index == GNCDB_NOT_FOUND) {
    return GNCDB_NOT_FOUND;
  }

  pData = (char *)array->dataBuffer + index * array->elementSize;  // 计算内存中的数据的位置

  /* 调整各个元素的位置 */
  size = (array->elementCount * array->elementSize) - ((index + 1) * array->elementSize);
  memmove(pData, pData + array->elementSize, size);
  array->elementCount--;
  /* 处理冗余内存 */
  while (array->elementCount < (array->capacity) / 3 && array->capacity > 5 && !(array->fiexdCap)) {
    arrayResizeLookaside(db, array, REDUCE);
  }
  return GNCDB_SUCCESS;
}

int varArrayListRmPtrLookaside(GNCDB *db, varArrayList *array, void *pointer)
{
  return varArrayListRmLookaside(db, array, &pointer);
}

int varArrayListRmByIdxLookaside(GNCDB *db, varArrayList *array, int index)
{
  int size = 0;
  // int i = 0;
  char *pData = varArrayListGet(array, index);
  if (pData == NULL) {
    return GNCDB_NOT_FOUND;
  }
  /* 调整各个元素的位置 */
  size = (array->elementCount * array->elementSize) - ((index + 1) * array->elementSize);
  memmove(pData, pData + array->elementSize, size);
  array->elementCount--;
  /* 处理冗余内存 */
  while (array->elementCount < (array->capacity) / 3 && array->capacity > 5 && !(array->fiexdCap)) {
    arrayResizeLookaside(db, array, REDUCE);
  }

  return GNCDB_SUCCESS;
}

/* 下面是使用lookaside内存池进行内存分配的expression的一些接口的实现，与实际逻辑一致，仅仅替换内存分配和释放的函数 */
