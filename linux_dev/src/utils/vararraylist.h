#ifndef _VARARRAYLIST_H_
#define _VARARRAYLIST_H_

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include "gncdbconstant.h"
typedef struct Value Value;

/* 默认初始容量 */
#define ARRDEFAULTCAPACITY 10
/* 容量最小限制 */
#define ARRMINCAPACITY 5

/* 标记 */
#define ORDER 1
#define DISORDER 0

/* 内存管理标记 */
#define EXPAND 4                    /* 内存不足 */
#define REDUCE 5                    /* 内存冗余 */
#define MEM_EXPAND(cap) (cap * 1.5) /* 扩大内存 */
#define MEM_REDUCE(cap) (cap * 0.5) /* 缩小内存 */

struct varArrayList;
/* 比较函数和销毁函数类型 */
typedef int (*Compare)(struct varArrayList *, void *, void *);
typedef void (*Destroy)(void *);

/* varArrayList结构体 */
typedef struct varArrayList
{
    int   capacity;     /* varArrayList容量 */
    int   elementSize;  /* 一个元素的大小 */
    int   elementCount; /* 实际容纳的元素数量 */
    void *dataBuffer;   /* 数据指针 */
    bool  orderly;      /* 设定是否有序 */
    bool  fiexdCap;     /* 设定是否根据当前数据量动态调整内存大小 */
    void *reserve;      /* 预留开发位 */

    Compare compare; /* 比较函数指针 */
    Destroy destroy; /* 销毁函数指针 */
} varArrayList;

/* 外部接口 */

/* int型比较函数 */
int intCompare(varArrayList *vp_st_array, void *vp_void_data1, void *vp_void_data2);
/* double型比较函数 */
int doubleCompare(varArrayList *vp_st_array, void *vp_void_data1, void *vp_void_data2);
/* str比较函数 */
int stringCompare(varArrayList *vp_st_array, void *vp_void_data1, void *vp_void_data2);

/* 构造函数  创建一个varArrayList */
varArrayList *varArrayListCreate(
    int v_si32_flag, int v_si32_elementSize, int v_si32_capacity, Compare vp_fun_compare, Destroy vp_fun_destroy);
/* 获取元素数量 */
int varArrayListGetCount(varArrayList *vp_st_array);
/* 在有序的array中寻找数据插入的位置 */
int varArrayListGetPosition(varArrayList *vp_st_array, void *vp_void_data);
/* 对一个OrderArratList进行插入数据 */
int varArrayListAdd(varArrayList *vp_st_array, void *vp_void_data);
/* 在idx位置插入一个数据（仅限无序状态下） */
int varArrayListInsert(varArrayList *vp_st_array, int idx, void *vp_void_data);
/* 在尾部插入一个数据 */
int varArrayListAddTail(varArrayList *vp_st_array, void *vp_void_data);
/* 在头部插入一个数据 */
int varArrayListAddHead(varArrayList *vp_st_array, void *vp_void_data);
/* 查询函数，查找获取下标 */
int varArrayListIndexOf(varArrayList *vp_st_array, void *vp_void_data);
/* 查询函数，根据下标获取数据 */
void *varArrayListGet(varArrayList *vp_st_array, int v_si32_index);
/* 将first中下标index至尾部的数据移动至second的头部 */
int varArrayListRemoveTail(varArrayList *vp_st_arrayfirst, varArrayList *vp_st_arraysecond, int v_si32_index);
/* 将first中下标0至index的数据移动至second的尾部 */
int varArrayListRemoveHead(varArrayList *vp_st_arrayfirst, varArrayList *vp_st_arraysecond, int v_si32_index);
/* 对数据进行单次删除 */
int varArrayListRemove(varArrayList *vp_st_array, void *vp_void_data);
/* varArrayList根据数据删除元素,若出现多个则全部删除 */
int varArrayListRemoveAll(varArrayList *vp_st_array, void *vp_void_data);
/* 根据下标删除数据 */
int varArrayListRemoveByIndex(varArrayList *vp_st_array, int v_si32_index);
/* 对指定的数据进行单次删除 */
int varArrayListSet(varArrayList *vp_st_array, void *vp_void_data1, void *vp_void_data2);
/* 对指定的数据进行更新 若出现多个则全部更新*/
int varArrayListSetAll(varArrayList *vp_st_array, void *vp_void_data1, void *vp_void_data2);
/* 对指定下标进行更新 */
int varArrayListSetByIndex(varArrayList *vp_st_array, int v_si32_index, void *vp_void_data);
/* 清空一个varArrayList */
int varArrayListClear(varArrayList *vp_st_array);
/* 反转varArrayList */
void varArrayListReverse(varArrayList *vp_st_array);
/* 对varArrayList进行拷贝 */
varArrayList *varArrayListCopy(varArrayList *vp_st_array);
/* 将srcList中的数据拷贝至destList
int varArrayListCopyTo(varArrayList *srcList, varArrayList *destList);  // TODO: implement the function
*/
/* 输出int array的值, 用于调试 */
void printfArrayIntValue(varArrayList *vp_st_array);

/* 指针函数接口 */
int   varArrayListAddPointer(varArrayList *vp_st_array, void *vp_void_pointer);
int   varArrayListInsertPointer(varArrayList *vp_st_array, int v_si32_idx, void *vp_void_pointer);
int   varArrayListAddPointerTail(varArrayList *vp_st_array, void *vp_void_pointer);
int   varArrayListAddPointerHead(varArrayList *vp_st_array, void *vp_void_pointer);
int   varArrayListRemovePointer(varArrayList *vp_st_array, void *vp_void_pointer);
int   varArrayListRemoveByIndexPointer(varArrayList *vp_st_array, int v_si32_index);
void *varArrayListGetPointer(varArrayList *vp_st_array, int v_si32_index);
void *varArrayListMovePointer(varArrayList *vp_st_array, int v_si32_index);
int   varArrayListIndexOfPointer(varArrayList *vp_st_array, void *vp_void_pointer);
int   varArrListSetPointer(varArrayList *vp_st_array, void *vp_void_pointer1, void *vp_void_pointer2);
int   varArrayListSetByIndexPointer(varArrayList *vp_st_array, int v_si32_index, void *vp_void_pointer);
int   varArrayListExistPointer(varArrayList *vp_st_array, void *vp_void_pointer);
void *varArrayListGetTailPointer(varArrayList *vp_st_array);
/* 销毁一个varArrayList */
void varArrayListDestroy(varArrayList **vp_st_array);
/* valArrayList结构体指针销毁函数 */
void varArrayListPointerDestroy(void *vp_void_data);

// 通过双重校验获取实际指针（先校验索引范围，再校验指针有效性）
#define varArrayListGetPointer1(vp_st_array, v_si32_index)                                                \
    ({                                                                                                    \
        void *              __ptr__ = NULL;                                                               \
        typeof(vp_st_array) __arr__ = (vp_st_array);                                                      \
        int                 __idx__ = (v_si32_index);                                                     \
        /* 第一步：索引有效性检查 */                                                           \
        if (likely(__idx__ >= 0 && __idx__ < __arr__->elementCount))                                      \
        {                                                                                                 \
            void **__storage__ = (void **)((char *)__arr__->dataBuffer + __idx__ * __arr__->elementSize); \
            /* 第二步：存储指针的有效性检查 */                                              \
            if (likely(__storage__ != NULL))                                                              \
            {                                                                                             \
                __ptr__ = (void *)*__storage__;                                                           \
            }                                                                                             \
        }                                                                                                 \
        __ptr__;                                                                                          \
    })
#endif
