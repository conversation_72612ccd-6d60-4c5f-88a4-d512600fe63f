/*
 * @ProjectName: HashMap.c
 *
 * @Author: xidb
 * @Createtime: 2023-1-31  13:30
 * @LastEditor: xidb
 * @LastEditTime: 2025-08-25 16:57:45
 */

#include "hashmap.h"
#include "gncdbconstant.h"
#include <stdint.h>
#include <string.h>

/// <summary>
/// 默认哈希函数
/// </summary>
/// <param name="hashMap">哈希表指针</param>
/// <param name="key">key值</param>
/// <returns>哈希值</returns>
int defaultHashCode(HashMap *hashMap, void *key)
{
  char         *k = (char *)key;
  unsigned long h = 0;
  unsigned long g = 0;
  while (*k) {
    h = (h << 4) + *k++;
    g = h & 0xF0000000L;
    if (g) {
      h ^= g >> 24;
    }
    h &= ~g;
  }
  return h % hashMap->bucketNum;
  // return h & (hashMap->bucketNum - 1);
}

int hashInt(HashMap *hashMap, void *key)
{
  int num = *(int *)key;
  return num & (hashMap->bucketNum - 1);
}

int hashStr(HashMap *hashMap, void *key)
{
  unsigned int hash = 2166136261;
  char        *str  = key;
  while (*str) {
    hash = (hash ^ (unsigned char)(*str++)) * 16777619;
  }
  return hash & (hashMap->bucketNum - 1);
}

/**
 * @description: 带长度参数的默认哈希函数
 * @param {HashMap} *hashMap
 * @param {void} *key
 * @return {*}
 */
int defaultHashCodeWithLen(HashMap *hashMap, void *key, int len)
{
  char         *k = (char *)key;
  unsigned long h = 0;
  unsigned long g = 0;
  for (int i = 0; i < len; i++) {
    h = (h << 4) + *k++;
    g = h & 0xF0000000L;
    if (g) {
      h ^= g >> 24;
    }
    h &= ~g;
  }
  return h % hashMap->bucketNum;
}

/**
 * @description: MurmurHash3 的 32 位版本，计算稍慢，但是哈希冲突率低，但是由于现在字节流不是特别长，性能可能略优于FNV1a
 * @param {HashMap} *hashMap
 * @param {void} *key
 * @param {int} len
 * @return {*}
 */
int defaultHashCodeWithLen_MurmurHash(HashMap *hashMap, void *key, int len)
{
  const uint32_t seed = 0;           // 初始种子值
  const uint32_t m    = 0x5bd1e995;  // 乘法常数
  const int      r    = 24;          // 移位常数
  uint32_t       h    = seed ^ len;  // 初始化哈希值
  const uint8_t *data = (const uint8_t *)key;

  // 处理 4 字节块
  while (len >= 4) {
    uint32_t k = *(uint32_t *)data;  // 读取 4 字节
    k *= m;                          // 乘法混合
    k ^= k >> r;                     // 右移并异或
    k *= m;                          // 再次乘法混合
    h *= m;                          // 更新哈希值
    h ^= k;                          // 合并当前块
    data += 4;                       // 移动指针
    len -= 4;                        // 更新剩余长度
  }

  // 处理剩余字节（尾部）
  switch (len) {
    case 3: h ^= data[2] << 16;  // 剩余 3 字节
    case 2: h ^= data[1] << 8;   // 剩余 2 字节
    case 1:
      h ^= data[0];  // 剩余 1 字节
      h *= m;        // 乘法混合
  }

  // 最终混合
  h ^= h >> 13;
  h *= m;
  h ^= h >> 15;

  // 取模映射到哈希桶
  return h % hashMap->bucketNum;
}

/**
 * @description: FNV-1a 版本，计算速度快，但是哈希冲突率不如
 * @param {HashMap} *hashMap
 * @param {void} *key
 * @param {int} len
 * @return {*}
 */
int defaultHashCodeWithLen_FNV1a(HashMap *hashMap, void *key, int len)
{
  const uint32_t FNV_prime = 16777619;    // FNV 质数
  uint32_t       h         = 2166136261;  // FNV 初始值 (FNV_offset_basis)
  const uint8_t *data      = (const uint8_t *)key;

  // 逐字节处理
  for (int i = 0; i < len; i++) {
    h ^= data[i];    // 与当前字节异或
    h *= FNV_prime;  // 乘以 FNV 质数
  }

  // 取模映射到哈希桶
  return h % hashMap->bucketNum;
}

/**
 * @description: 与CombinedHashKey对应的哈希函数
 * @param {HashMap} *hashMap
 * @param {void} *key 必须是CombinedHashKey*结构体指针
 * @return {*} 哈希桶的下标
 */
int CombinedKeyHashCode(HashMap *hashMap, void *key)
{
  CombinedHashKey *k = (CombinedHashKey *)key;
  return defaultHashCodeWithLen_MurmurHash(hashMap, k->data, k->payloadLen);
}

/// <summary>
/// 字符串判断键是否相等
/// </summary>
/// <param name="key1">key1</param>
/// <param name="key2">key2</param>
/// <returns>true 或 false</returns>
bool hashMapStrEqual(void *key1, void *key2)
{
  if (strcmp((char *)key1, (char *)key2) == 0) {
    return true;
  }
  return false;
}

/// <summary>
/// int值判断键是否相等
/// </summary>
/// <param name="key1">key1</param>
/// <param name="key2">key2</param>
/// <returns>true 或 false</returns>
bool hashMapIntEqual(void *key1, void *key2) { return *(int *)key1 == *(int *)key2; }

void HashMapValueListDestroy(HashMapValueList **list)
{
  if (*list == NULL) {
    return;
  }
  my_free((*list)->values);
  (*list)->values = NULL;
  my_free(*list);
  *list = NULL;
  return;
}

/// <summary>
///
/// </summary>
/// <param name="keyType"></param>
/// <param name="fixBuckNum">固定桶的数量,为0则自动扩充增长,</param>
/// <param name="hashCode"></param>
/// <returns></returns>
HashMap *hashMapCreate(int keyType, int fixBuckNum, HashCode hashCode)
{
  HashMap *hashMap = my_malloc(sizeof(struct HashMap));
  if (hashMap == NULL) {
    return NULL;
  }
  // 初始化
  hashMap->entryCount = 0;
  hashMap->keyType    = keyType;
  switch (keyType) {
    case INTKEY: hashMap->equal = hashMapIntEqual; break;
    case STRKEY: hashMap->equal = hashMapStrEqual; break;
    default: my_free(hashMap); return NULL;
  }
  if (hashCode != NULL) {
    hashMap->hashCode = hashCode;
  } else {
    switch (keyType) {
      case INTKEY: hashMap->hashCode = hashInt; break;
      case STRKEY: hashMap->hashCode = hashStr; break;
      default: hashMap->hashCode = defaultHashCode;
    }
  }

  if (fixBuckNum != 0) {
    hashMap->bucketNum  = fixBuckNum;
    hashMap->autoAssign = false;
  } else {
    hashMap->bucketNum  = DEFAULTCAPACITY;
    hashMap->autoAssign = true;
  }
  // 为结构体内的指针申请空间
  hashMap->bucketList = my_malloc(hashMap->bucketNum * sizeof(struct Entry));
  if (hashMap->bucketList == NULL) {
    // 若没有申请到，则释放结构体空间并返回NULL
    my_free(hashMap);
    return NULL;
  }
  // 初始化申请的空间
  // p = hashMap->bucketList;
  // for (i = 0; i < hashMap->bucketNum; i++) {
  //   p[i].key = p[i].value = p[i].next = NULL;
  // }
  memset(hashMap->bucketList, 0, hashMap->bucketNum * sizeof(struct Entry));
  return hashMap;
}

/// <summary>
/// 判断键是否存在
/// </summary>
/// <param name="hashMap">哈希表指针</param>
/// <param name="key">key值</param>
/// <returns>true 或 false</returns>
bool hashMapExists(HashMap *hashMap, void *key)
{
  int    index   = 0;
  Entry *entry   = NULL;
  int    keyType = hashMap->keyType;
  int    keyVal  = 0;
  index          = hashMap->hashCode(hashMap, key);
  entry          = &hashMap->bucketList[index];

  if (entry->key == NULL) {
    return false;
  } else {
    while (entry != NULL && entry->key != NULL) {
      if (keyType == INTKEY) {
        keyVal = *(int *)key;
        if (*(int *)entry->key == keyVal) {
          return true;
        }
      } else {
        if (hashMap->equal(entry->key, key)) {
          return true;
        }
      }
      entry = entry->next;
    }
  }
  return false;
}

/// <summary>
/// 添加键值对
/// </summary>
/// <param name="hashMap">哈希表指针</param>
/// <param name="key">key值</param>
/// <param name="value">数据值</param>
int hashMapPut(HashMap *hashMap, void *key, void *value)
{
  // 获取哈希值
  int   *pInt     = NULL;
  char  *pKey     = NULL;
  char  *pStr     = NULL;
  Entry *newEntry = NULL;
  Entry *entry    = NULL;

  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    if (hashMap->keyType == INTKEY) {
      pInt = my_malloc(sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt                          = *(int *)key;
      hashMap->bucketList[index].key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = my_malloc(strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      hashMap->bucketList[index].key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->keyType == INTKEY) {
        if (*(int *)entry->key == *(int *)key) {
          entry->value = value;
          return GNCDB_SUCCESS;
        }
      } else {
        if (hashMap->equal(entry->key, key)) {
          entry->value = value;
          return GNCDB_SUCCESS;
        }
      }
      entry = entry->next;
    }

    // 发生冲突则创建节点挂到相应的位置
    newEntry = my_malloc(sizeof(struct Entry));
    if (newEntry == NULL) {
      return GNCDB_SPACE_LACK;
    }

    if (hashMap->keyType == INTKEY) {
      pInt = my_malloc(sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt         = *(int *)key;
      newEntry->key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = my_malloc(strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      newEntry->key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    newEntry->value = value;

    newEntry->next                  = hashMap->bucketList[index].next;
    hashMap->bucketList[index].next = newEntry;
    hashMap->entryCount++;
  }

  if (hashMap->autoAssign && hashMap->entryCount >= hashMap->bucketNum) {
    // 内存扩充至原来的两倍
    // *注: 扩充时考虑的是当前存储元素数量与存储空间的大小关系，而不是存储空间是否已经存满，
    // 例如: 存储空间为10，存入了10个键值对，但是全部冲突了，所以存储空间空着9个，其余的全部挂在一个上面，
    // 这样检索的时候和遍历查询没有什么区别了，可以简单这样理解，当我存入第11个键值对的时候一定会发生冲突，
    // 这是由哈希函数本身的特性(取模)决定的，冲突就会导致检索变慢，所以这时候扩充存储空间，对原有键值对进行
    // 再次散列，会把冲突的数据再次分散开，加快索引定位速度。
    hashMapReset_v2(hashMap, HASH_EXPAND(hashMap->bucketNum));
  }

  return GNCDB_SUCCESS;
}

/// <summary>
/// 获取键值对的值
/// </summary>
/// <param name="hashMap">哈希表指针</param>
/// <param name="key">key值</param>
/// <returns>获取到的值指针，否则返回空</returns>
void *hashMapGet(HashMap *hashMap, void *key)
{
  int    index   = 0;
  Entry *entry   = NULL;
  int    keyType = hashMap->keyType;
  int    keyVal  = 0;
  index          = hashMap->hashCode(hashMap, key);
  entry          = &hashMap->bucketList[index];

  //   if (keyType == INTKEY) {
  // 	keyVal = *(int*)key;
  // 	while (entry != NULL) {
  // 		if (entry->key == NULL) {
  // 			entry = entry->next;
  // 			continue;
  // 		}
  // 		if (*(int*)entry->key == keyVal) {
  // 			return entry->value;
  // 		}
  // 		entry = entry->next;
  // 	}
  // 	} else {
  // 		while (entry != NULL) {
  // 			if (entry->key == NULL) {
  // 				entry = entry->next;
  // 				continue;
  // 			}
  // 			if (hashMap->equal(entry->key, key)) {
  // 				return entry->value;
  // 			}
  // 			entry = entry->next;
  // 		}
  // 	}

  if (entry->key == NULL) {
    return NULL;
  } else {
    while (entry != NULL && entry->key != NULL) {
      if (keyType == INTKEY) {
        keyVal = *(int *)key;
        if (*(int *)entry->key == keyVal) {
          return entry->value;
        }
      } else {
        if (hashMap->equal(entry->key, key)) {
          return entry->value;
        }
      }
      entry = entry->next;
    }
  }
  return NULL;
}

// <summary>
// 删除一个键
// </summary>
// <param name="hashMap">哈希表指针</param>
// <param name="key">key值</param>
// <returns>删除键的key指针，否则返回空</returns>
int hashMapRemove(HashMap *hashMap, void *key)
{
  // 获取哈希值
  bool result = false;
  // void* entryKey = NULL;
  Entry *entry = NULL;
  Entry *temp  = NULL;
  Entry *p     = NULL;
  int    index = hashMap->hashCode(hashMap, key);
  entry        = &hashMap->bucketList[index];
  if (entry->key == NULL) {
    return GNCDB_PARAMNULL;
  }
  // entryKey = entry->key;
  if (hashMap->keyType == INTKEY) {
    if (*(int *)entry->key == *(int *)key) {
      hashMap->entryCount--;
      if (entry->next != NULL) {
        temp = entry->next;
        my_free(entry->key);
        entry->key   = temp->key;
        entry->value = temp->value;
        entry->next  = temp->next;
        my_free(temp);
      } else {
        my_free(entry->key);
        entry->key   = NULL;
        entry->value = NULL;
      }
      result = true;
    } else {
      p     = entry;
      entry = entry->next;
      while (entry != NULL) {
        if (hashMap->equal(entry->key, key)) {
          hashMap->entryCount--;
          p->next = entry->next;
          my_free(entry->key);
          entry->key = NULL;
          my_free(entry);
          result = true;
          break;
        }
        p     = entry;
        entry = entry->next;
      }
    }
  } else {
    if (hashMap->equal(entry->key, key)) {
      hashMap->entryCount--;
      if (entry->next != NULL) {
        temp = entry->next;
        my_free(entry->key);
        entry->key   = temp->key;
        entry->value = temp->value;
        entry->next  = temp->next;
        my_free(temp);
      } else {
        my_free(entry->key);
        entry->key   = NULL;
        entry->value = NULL;
      }
      result = true;
    } else {
      p     = entry;
      entry = entry->next;
      while (entry != NULL) {
        if (hashMap->equal(entry->key, key)) {
          hashMap->entryCount--;
          p->next = entry->next;
          my_free(entry->key);
          entry->key = NULL;
          my_free(entry);
          result = true;
          break;
        }
        p     = entry;
        entry = entry->next;
      }
    }
  }
  // 如果空间占用不足一半，则释放多余内存，最小缩容到初始默认的8
  if (result && hashMap->autoAssign && hashMap->entryCount < hashMap->bucketNum / 2 &&
      hashMap->bucketNum > DEFAULTCAPACITY) {
    hashMapReset_v2(hashMap, HASH_REDUCE(hashMap->bucketNum));
  }
  return GNCDB_SUCCESS;
}

/// <summary>
/// 清空map函数
/// </summary>
/// <param name="hashMap">哈希表指针</param>
void hashMapClear(HashMap *hashMap)
{
  int    i     = 0;
  Entry *entry = NULL;
  Entry *next  = NULL;
  for (i = 0; i < hashMap->bucketNum; i++) {
    // 释放冲突值内存
    entry = hashMap->bucketList[i].next;
    while (entry != NULL) {
      next = entry->next;
      my_free(entry->key);
      entry->key = NULL;
      my_free(entry);
      entry = next;
    }
    my_free(hashMap->bucketList[i].key);
    hashMap->bucketList[i].next  = NULL;
    hashMap->bucketList[i].key   = NULL;
    hashMap->bucketList[i].value = NULL;
  }

  hashMap->entryCount = 0;
}

/// <summary>
/// 销毁map
/// </summary>
/// <param name="hashMap">需要销毁的map的地址</param>
void hashMapDestroy(HashMap **hashMap)
{
  if (*hashMap == NULL) {
    return;
  }
  if ((*hashMap)->bucketNum > 0) {
    hashMapClear(*hashMap);
  }
  my_free((*hashMap)->bucketList);
  // (*hashMap)->bucketList = NULL;
  my_free(*hashMap);
  *hashMap = NULL;
  return;
}

/// <summary>
/// 用于重构的put函数
/// </summary>
/// <param name="hashMap"></param>
/// <param name="key"></param>
/// <param name="value"></param>
static void hashMapResetPut(HashMap *hashMap, void *key, void *value)
{
  Entry *newEntry = NULL;
  Entry *entry    = NULL;
  // 获取哈希值
  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    hashMap->bucketList[index].key   = key;
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->equal(key, entry->key)) {
        // 对于键值已经存在的直接覆盖
        entry->value = value;
        return;
      }
      entry = entry->next;
    }

    // 发生冲突则创建节点挂到相应的位置
    newEntry = my_malloc(sizeof(struct Entry));
    if (newEntry == NULL) {
      return;
    }
    newEntry->key                   = key;
    newEntry->value                 = value;
    newEntry->next                  = hashMap->bucketList[index].next;
    hashMap->bucketList[index].next = newEntry;
    hashMap->entryCount++;
  }
}

/// <summary>
/// 将哈希表重新构建
/// </summary>
/// <param name="hashMap">哈希表指针</param>
/// <param name="listSize">新的大小</param>
void hashMapReset(HashMap *hashMap, int bucketNum)
{
  Entry           *tempList   = NULL;
  HashMapIterator *iterator   = NULL;
  int              entryCount = 0;
  Entry           *relist     = NULL;
  int              index      = 0;
  int              i          = 0;
  Entry           *current    = NULL;
  Entry           *temp       = NULL;
  if (hashMap->entryCount == 0) {
    return;
  }

#if DEBUG
  if (bucketNum == 2560) {
    printf("bucketNum:2560\n");
  }
#endif

  // 键值对临时存储空间
  tempList = my_malloc((hashMap->entryCount) * sizeof(struct Entry));
  if (tempList == NULL) {
    return;
  }

  iterator   = createHashMapIterator(hashMap);
  entryCount = hashMap->entryCount;
  for (index = 0; hasNextHashMapIterator(iterator); index++) {
    // 迭代取出所有键值对
    iterator              = nextHashMapIterator(iterator);
    tempList[index].key   = iterator->entry->key;
    tempList[index].value = iterator->entry->value;
    tempList[index].next  = NULL;
  }
  freeHashMapIterator(&iterator);

  // 清除原有键值对数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    current        = &hashMap->bucketList[i];
    current->key   = NULL;
    current->value = NULL;
    current        = current->next;
    while (current != NULL) {
      temp = current->next;
      my_free(current);
      current = temp;
    }
  }
  hashMap->entryCount = 0;

  // 更改内存大小
  hashMap->bucketNum = bucketNum;
  relist             = (Entry *)my_realloc(hashMap->bucketList, hashMap->bucketNum * sizeof(struct Entry));
  if (relist != NULL) {
    hashMap->bucketList = relist;
    relist              = NULL;
  }

  // 初始化数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    hashMap->bucketList[i].key   = NULL;
    hashMap->bucketList[i].value = NULL;
    hashMap->bucketList[i].next  = NULL;
  }

  // 将所有键值对重新写入内存
  for (i = 0; i < entryCount; i++) {
    hashMapResetPut(hashMap, tempList[i].key, tempList[i].value);
  }
  my_free(tempList);
}

void hashMapReset_v2(HashMap *hashMap, int bucketNum)
{
  Entry *newEntryList = NULL;
  Entry *oldEntryList = NULL;
  Entry *oldEntry     = NULL;
  Entry *newEntry     = NULL;
  int    oldBucketNum = 0;
  int    index        = 0;

  if (hashMap->entryCount == 0) {
    return;
  }

  /* 申请新数组替换旧数组，并重新初始化entryCnt */
  oldBucketNum       = hashMap->bucketNum;
  hashMap->bucketNum = bucketNum;
  oldEntryList       = hashMap->bucketList;
  newEntryList       = (Entry *)my_malloc0(hashMap->bucketNum * sizeof(Entry));
  if (newEntryList == NULL) {
    return;
  }
  hashMap->bucketList = newEntryList;
  hashMap->entryCount = 0;

  /* 遍历旧的哈希表，将数据迁移到新的哈希表 */
  for (int i = 0; i < oldBucketNum; i++) {
    oldEntry = &oldEntryList[i];
    if (oldEntry->key == NULL) {
      continue;
    }
    /* 此时的entry是在连续数组中的，无法复用到新的entry数组中 */
    hashMapResetPut(hashMap, oldEntry->key, oldEntry->value);
    while (oldEntry->next != NULL) {
      /* 此时的entry是离散独立的，不走hashMapResetPut函数，手动加进去复用 */
      oldEntry = oldEntry->next;
      /* 断开旧节点的悬垂链表 */
      oldEntryList[i].next = oldEntry->next;
      oldEntry->next       = NULL;
      /* 计算哈希和在新数组的位置 */
      index    = hashMap->hashCode(hashMap, oldEntry->key);
      newEntry = &hashMap->bucketList[index];
      if (newEntry->key == NULL) {
        /* 新数组index处是空的，直接存储 */
        newEntry->key   = oldEntry->key;
        newEntry->value = oldEntry->value;
        my_free(oldEntry); /* 此时由于新节点处于固定内存区域无法复用，释放旧节点内存 */
      } else {
        /* 新数组index处已经有数据，发生冲突，遍历index处的悬垂链表 */
        while (newEntry->next != NULL) {
          newEntry = newEntry->next;
        }
        newEntry->next = oldEntry;
      }
      oldEntry = &oldEntryList[i];
      hashMap->entryCount++;
    }
  }

  my_free(oldEntryList);
}

/**
 * @description: 与CombinedKeyHashMapResetPut一起使用，用以reset时的put操作
 * @param {HashMap} *hashMap
 * @param {void} *key int(data长度) + char*(实际data)组合
 * @param {void} *value
 * @return {*}
 */
void CombinedKeyHashMapResetPut(HashMap *hashMap, void *key, void *value)
{
  Entry *newEntry   = NULL;
  Entry *entry      = NULL;
  int    payloadLen = *(int *)key;  // 获取data长度，key是payloadLen + data的组合
  int    index      = defaultHashCodeWithLen_MurmurHash(hashMap, key, payloadLen);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    hashMap->bucketList[index].key   = key;
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->equal(key, entry->key)) {
        // 对于键值已经存在的则放在链表的最后
        while (entry->next != NULL) {
          entry = entry->next;
        }
      }
      entry = entry->next;
    }

    // 发生冲突则创建节点挂到相应的位置
    newEntry = my_malloc(sizeof(struct Entry));
    if (newEntry == NULL) {
      return;
    }
    newEntry->key                   = key;
    newEntry->value                 = value;
    newEntry->next                  = hashMap->bucketList[index].next;
    hashMap->bucketList[index].next = newEntry;
    hashMap->entryCount++;
  }
}

void myhashMapResetPut(HashMap *hashMap, void *key, void *value)
{
  Entry *newEntry = NULL;
  Entry *entry    = NULL;
  // 获取哈希值
  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    hashMap->bucketList[index].key   = key;
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->equal(key, entry->key)) {
        // 对于键值已经存在的则放在链表的最后
        while (entry->next != NULL) {
          entry = entry->next;
        }
      }
      entry = entry->next;
    }

    // 发生冲突则创建节点挂到相应的位置
    newEntry = my_malloc(sizeof(struct Entry));
    if (newEntry == NULL) {
      return;
    }
    newEntry->key                   = key;
    newEntry->value                 = value;
    newEntry->next                  = hashMap->bucketList[index].next;
    hashMap->bucketList[index].next = newEntry;
    hashMap->entryCount++;
  }
}

/**
 * @description: 与CombinedKeyHashMapResetPut对应的reset函数
 * @param {HashMap} *hashMap
 * @param {int} bucketNum
 * @return {*}
 */
void CombinedKeyHashMapReset(HashMap *hashMap, int bucketNum)
{
  Entry           *tempList   = NULL;
  HashMapIterator *iterator   = NULL;
  int              entryCount = 0;
  Entry           *relist     = NULL;
  int              index      = 0;
  int              i          = 0;
  Entry           *current    = NULL;
  Entry           *temp       = NULL;
  if (hashMap->entryCount == 0) {
    return;
  }

  // 键值对临时存储空间
  tempList = my_malloc((hashMap->entryCount) * sizeof(struct Entry));
  if (tempList == NULL) {
    return;
  }

  iterator   = createHashMapIterator(hashMap);
  entryCount = hashMap->entryCount;
  for (index = 0; hasNextHashMapIterator(iterator); index++) {
    // 迭代取出所有键值对
    iterator              = nextHashMapIterator(iterator);
    tempList[index].key   = iterator->entry->key;
    tempList[index].value = iterator->entry->value;
    tempList[index].next  = NULL;
  }
  freeHashMapIterator(&iterator);

  // 清除原有键值对数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    current        = &hashMap->bucketList[i];
    current->key   = NULL;
    current->value = NULL;
    current        = current->next;
    while (current != NULL) {
      temp = current->next;
      my_free(current);
      current = temp;
    }
  }
  hashMap->entryCount = 0;

  // 更改内存大小
  hashMap->bucketNum = bucketNum;
  relist             = (Entry *)my_realloc(hashMap->bucketList, hashMap->bucketNum * sizeof(struct Entry));
  if (relist != NULL) {
    hashMap->bucketList = relist;
    relist              = NULL;
  }

  // 初始化数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    hashMap->bucketList[i].key   = NULL;
    hashMap->bucketList[i].value = NULL;
    hashMap->bucketList[i].next  = NULL;
  }

  // 将所有键值对重新写入内存
  for (i = 0; i < entryCount; i++) {
    CombinedKeyHashMapResetPut(hashMap, tempList[i].key, tempList[i].value);
  }
  my_free(tempList);
}

void myHashMapReset(HashMap *hashMap, int bucketNum)
{
  Entry           *tempList   = NULL;
  HashMapIterator *iterator   = NULL;
  int              entryCount = 0;
  Entry           *relist     = NULL;
  int              index      = 0;
  int              i          = 0;
  Entry           *current    = NULL;
  Entry           *temp       = NULL;
  if (hashMap->entryCount == 0) {
    return;
  }

#if DEBUG
  if (bucketNum == 2560) {
    printf("bucketNum:2560\n");
  }
#endif

  // 键值对临时存储空间
  tempList = my_malloc((hashMap->entryCount) * sizeof(struct Entry));
  if (tempList == NULL) {
    return;
  }

  iterator   = createHashMapIterator(hashMap);
  entryCount = hashMap->entryCount;
  for (index = 0; hasNextHashMapIterator(iterator); index++) {
    // 迭代取出所有键值对
    iterator              = nextHashMapIterator(iterator);
    tempList[index].key   = iterator->entry->key;
    tempList[index].value = iterator->entry->value;
    tempList[index].next  = NULL;
  }
  freeHashMapIterator(&iterator);

  // 清除原有键值对数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    current        = &hashMap->bucketList[i];
    current->key   = NULL;
    current->value = NULL;
    current        = current->next;
    while (current != NULL) {
      temp = current->next;
      my_free(current);
      current = temp;
    }
  }
  hashMap->entryCount = 0;

  // 更改内存大小
  hashMap->bucketNum = bucketNum;
  relist             = (Entry *)my_realloc(hashMap->bucketList, hashMap->bucketNum * sizeof(struct Entry));
  if (relist != NULL) {
    hashMap->bucketList = relist;
    relist              = NULL;
  }

  // 初始化数据
  for (i = 0; i < hashMap->bucketNum; i++) {
    hashMap->bucketList[i].key   = NULL;
    hashMap->bucketList[i].value = NULL;
    hashMap->bucketList[i].next  = NULL;
  }

  // 将所有键值对重新写入内存
  for (i = 0; i < entryCount; i++) {
    myhashMapResetPut(hashMap, tempList[i].key, tempList[i].value);
  }
  my_free(tempList);
}

/// <summary>
/// 创建哈希表迭代器
/// </summary>
/// <param name="hashMap">哈希表指针</param>
/// <returns>返回创建好的迭代器指针</returns>
HashMapIterator *createHashMapIterator(HashMap *hashMap)
{
  HashMapIterator *iterator = my_malloc(sizeof(struct HashMapIterator));
  if (iterator == NULL) {
    return NULL;
  }
  iterator->hashMap = hashMap;
  iterator->count   = 0;
  iterator->index   = -1;
  iterator->entry   = NULL;

  //// printf("traversal HashMap start : all buckNum = %d \n", hashMap->bucketNum);

  return iterator;
}

/// <summary>
/// 迭代器是否有下一个
/// </summary>
/// <param name="iterator">迭代器指针</param>
/// <returns>true 或 false</returns>
bool hasNextHashMapIterator(HashMapIterator *iterator)
{
  //// printf("current inde = %d--", iterator->index);
  if (iterator->index > iterator->hashMap->bucketNum) {
    iterator->index = 0;
  }
  return iterator->count < iterator->hashMap->entryCount ? true : false;
}

/// <summary>
/// 迭代到下一个
/// </summary>
/// <param name="iterator">迭代器指针</param>
/// <returns>下一个迭代器指针</returns>
HashMapIterator *nextHashMapIterator(HashMapIterator *iterator)
{
  Entry *entry = NULL;
  if (hasNextHashMapIterator(iterator)) {
    if (iterator->entry != NULL && iterator->entry->next != NULL) {
      iterator->count++;
      iterator->entry = iterator->entry->next;
      return iterator;
    }
    while (++iterator->index < iterator->hashMap->bucketNum) {
      entry = &iterator->hashMap->bucketList[iterator->index];
      if (entry->key != NULL) {
        iterator->count++;
        iterator->entry = entry;
        break;
      }
    }
  }
  return iterator;
}

/// <summary>
/// 释放迭代器内存
/// </summary>
/// <param name="iterator">迭代器指针</param>
void freeHashMapIterator(HashMapIterator **iterator)
{
  my_free(*iterator);
  *iterator = NULL;

  //// printf("\ntraversal HashMap end\n");
}

/**
 * @brief 1.原hashMapPut函数存在重复key时，仅将旧value替换新value，现采用不覆盖的原则
 * @brief 2.仅限用于hashjoin中的哈希表中，key为MultiKey* 类型
 * @brief 3.存储是以类似字符串形式存储，前四个字节（int）记录字节数
 * @param hashMap
 * @param key 必须是MultiHashKey *类型
 * @param value
 * @return int
 */
int CombinedKeyHashMapPut(HashMap *hashMap, void *key, void *value)
{
  // 获取哈希值
  char            *pStr         = NULL;
  CombinedHashKey *pCombinedKey = (CombinedHashKey *)key;
  Entry           *newEntry     = NULL;
  Entry           *entry        = NULL;
  Entry           *prevEntry    = NULL;
  int              index        = 0;

  pStr = (char *)my_malloc0(pCombinedKey->payloadLen);
  if (pStr == NULL) {
    return GNCDB_SPACE_LACK;
  }
  memcpy(pStr, pCombinedKey->data, pCombinedKey->payloadLen);  // pStr = payloadLen + pCombinedKey.data

  index = hashMap->hashCode(hashMap, pCombinedKey);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    hashMap->bucketList[index].key   = pStr;
    hashMap->bucketList[index].value = value;
  } else {  // 如果键值已经存在，但不覆盖值，继续遍历链表
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->equal(pCombinedKey->data, entry->key)) {
        prevEntry = entry;
        entry     = entry->next;
        continue;
      }
      prevEntry = entry;
      entry     = entry->next;
    }

    // 在链表末尾添加新节点
    newEntry = my_malloc(sizeof(struct Entry));
    if (newEntry == NULL) {
      return GNCDB_SPACE_LACK;
    }
    newEntry->key   = pStr;
    newEntry->value = value;

    // 将新节点挂到链表末尾
    if (prevEntry != NULL) {
      prevEntry->next = newEntry;
      newEntry->next  = NULL;
    } else {
      hashMap->bucketList[index].next = newEntry;
      newEntry->next                  = NULL;
    }
    hashMap->entryCount++;
  }

  if (hashMap->autoAssign && hashMap->entryCount >= hashMap->bucketNum) {
    // 内存扩充至原来的两倍
    // *注: 扩充时考虑的是当前存储元素数量与存储空间的大小关系，而不是存储空间是否已经存满，
    // 例如: 存储空间为10，存入了10个键值对，但是全部冲突了，所以存储空间空着9个，其余的全部挂在一个上面，
    // 这样检索的时候和遍历查询没有什么区别了，可以简单这样理解，当我存入第11个键值对的时候一定会发生冲突，
    // 这是由哈希函数本身的特性(取模)决定的，冲突就会导致检索变慢，所以这时候扩充存储空间，对原有键值对进行
    // 再次散列，会把冲突的数据再次分散开，加快索引定位速度。
    CombinedKeyHashMapReset(hashMap, HASH_EXPAND(hashMap->bucketNum));
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 原hashMapPut函数存在重复key时，仅将旧value替换新value，现采用不覆盖的原则
 * @brief
 * @param hashMap
 * @param key
 * @param value
 * @return int
 */
int myHashMapPut(HashMap *hashMap, void *key, void *value)
{
  // 获取哈希值
  int   *pInt      = NULL;
  char  *pKey      = NULL;
  char  *pStr      = NULL;
  Entry *newEntry  = NULL;
  Entry *entry     = NULL;
  Entry *prevEntry = NULL;

  int index = hashMap->hashCode(hashMap, key);

  if (hashMap->bucketList[index].key == NULL) {
    hashMap->entryCount++;
    // 地址为空时直接存储
    if (hashMap->keyType == INTKEY) {
      pInt = my_malloc(sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt                          = *(int *)key;
      hashMap->bucketList[index].key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = my_malloc(strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      hashMap->bucketList[index].key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    hashMap->bucketList[index].value = value;
  } else {
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      // printf("%s\n", (char*)entry->key);
      if (hashMap->equal(key, entry->key)) {
        // 如果键值已经存在，但不覆盖值，继续遍历链表
        prevEntry = entry;
        entry     = entry->next;
        continue;
      }
      prevEntry = entry;
      entry     = entry->next;
    }

    // 在链表末尾添加新节点
    newEntry = my_malloc(sizeof(struct Entry));
    if (newEntry == NULL) {
      return GNCDB_SPACE_LACK;
    }

    if (hashMap->keyType == INTKEY) {
      pInt = my_malloc(sizeof(int));
      if (pInt == NULL) {
        return GNCDB_SPACE_LACK;
      }
      *pInt         = *(int *)key;
      newEntry->key = pInt;
    } else if (hashMap->keyType == STRKEY) {
      pKey = (char *)key;
      pStr = my_malloc(strlen(pKey) + 1);
      if (pStr == NULL) {
        return GNCDB_SPACE_LACK;
      }
      strcpy(pStr, pKey);
      newEntry->key = pStr;
    } else {
      return GNCDB_PARAMNULL;
    }
    newEntry->value = value;

    // 将新节点挂到链表末尾
    if (prevEntry != NULL) {
      prevEntry->next = newEntry;
      newEntry->next  = NULL;
    } else {
      hashMap->bucketList[index].next = newEntry;
      newEntry->next                  = NULL;
    }
    hashMap->entryCount++;
  }

  if (hashMap->autoAssign && hashMap->entryCount >= hashMap->bucketNum) {
    // 内存扩充至原来的两倍
    // *注: 扩充时考虑的是当前存储元素数量与存储空间的大小关系，而不是存储空间是否已经存满，
    // 例如: 存储空间为10，存入了10个键值对，但是全部冲突了，所以存储空间空着9个，其余的全部挂在一个上面，
    // 这样检索的时候和遍历查询没有什么区别了，可以简单这样理解，当我存入第11个键值对的时候一定会发生冲突，
    // 这是由哈希函数本身的特性(取模)决定的，冲突就会导致检索变慢，所以这时候扩充存储空间，对原有键值对进行
    // 再次散列，会把冲突的数据再次分散开，加快索引定位速度。
    myHashMapReset(hashMap, HASH_EXPAND(hashMap->bucketNum));
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 自定义的hashMapGet函数，用于获取键对应的值列表，和myHashMapPut函数配合使用
 *
 * @param hashMap
 * @param key
 * @return HashMapValueList*
 */
HashMapValueList *myHashMapGet(HashMap *hashMap, void *key)
{
  int               index     = 0;
  Entry            *entry     = NULL;
  HashMapValueList *valueList = my_malloc(sizeof(HashMapValueList));
  if (valueList == NULL) {
    // 处理内存分配失败的情况
    return NULL;
  }
  valueList->values = NULL;
  valueList->size   = 0;

  if (hashMapExists(hashMap, key)) {
    index = hashMap->hashCode(hashMap, key);
    entry = &hashMap->bucketList[index];
    while (entry != NULL) {
      if (hashMap->equal(entry->key, key)) {
        // 找到匹配的键，将值添加到列表中
        valueList->size++;
        valueList->values = my_realloc(valueList->values, valueList->size * sizeof(void *));
        if (valueList->values == NULL) {
          // 处理内存分配失败的情况
          my_free(valueList);
          return NULL;
        }
        valueList->values[valueList->size - 1] = entry->value;
      }
      entry = entry->next;
    }
  }

  if (valueList->size == 0) {
    // 如果没有找到任何值，释放内存并返回NULL
    my_free(valueList->values);
    my_free(valueList);
    return NULL;
  }

  return valueList;
}

/**
 * @brief 返回key对应的值的数量
 *
 * @param hashMap
 * @param key
 * @return int
 */
int myhashMapCount(HashMap *hashMap, void *key)
{
  HashMapValueList *value_list = myHashMapGet(hashMap, key);
  if (value_list == NULL) {
    return 0;
  } else {
    return value_list->size;
  }
}

// #include "hashmap.h"

// HashMap *hashMapCreate(int keyType, int fixBuckNum, HashCode hashCode){
//     HashMap *map = NULL;
//     map = (HashMap *)my_malloc(sizeof(HashMap));
//     if(map == NULL){
//         return NULL;
//     }
//     map->keyType = keyType;
//     if(keyType == INTKEY){
//         map->intHashMap = NULL;
//     } else {
//         map->strHashMap = NULL;
//     }
//     map->entryCount = 0;
//     return map;
// }

// bool hashMapExists(HashMap *hashMap, void *key){
// 	int intKey = 0;
// 	char* strKey = NULL;
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	if(hashMap->keyType == INTKEY){
// 	    intKey = *(int *)key;
// 		HASH_FIND_INT(hashMap->intHashMap, &intKey, intEntry);
// 		if(intEntry == NULL){
// 		    return false;
// 		} else {
// 		    return true;
// 		}
// 	} else if(hashMap->keyType == STRKEY){
// 	    strKey = (char *)key;
// 		HASH_FIND_STR(hashMap->strHashMap, strKey, strEntry);
// 		if(strEntry == NULL){
// 		    return false;
// 		} else {
// 		    return true;
// 		}
// 	}
// }

// int hashMapPut(HashMap *hashMap, void *key, void *value){
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	char* strKey = NULL;
// 	if(hashMap->keyType == INTKEY) {
// 		HASH_FIND_INT(hashMap->intHashMap, key, intEntry);
// 		if(intEntry == NULL){
// 		    intEntry = (IntHashMap *)my_malloc(sizeof(IntHashMap));
// 			if(intEntry == NULL){
// 			    return GNCDB_SPACE_LACK;
// 			}
// 			intEntry->key = *(int *)key;
// 			intEntry->value = value;
// 			HASH_ADD_INT(hashMap->intHashMap, key, intEntry);
// 		}
// 		intEntry->value = value;
// 		hashMap->entryCount = HASH_COUNT(hashMap->intHashMap);
// 	} else if(hashMap->keyType == STRKEY) {
// 		HASH_FIND_STR(hashMap->strHashMap, key, strEntry);
// 		if(strEntry == NULL){
// 		    strEntry = (StrHashMap *)my_malloc(sizeof(StrHashMap));
// 			if(strEntry == NULL){
// 			    return GNCDB_SPACE_LACK;
// 			}
// 			strKey = (char *)key;
// 			strEntry->key = my_malloc(strlen(strKey) + 1);
// 			if(strEntry->key == NULL){
// 			    return GNCDB_SPACE_LACK;
// 			}
// 			strcpy(strEntry->key, strKey);

// 			strEntry->value = value;
// 			HASH_ADD_STR(hashMap->strHashMap, key, strEntry);
// 		}
// 		strEntry->value = value;
// 		hashMap->entryCount = HASH_COUNT(hashMap->strHashMap);
// 	}
// 	return GNCDB_SUCCESS;
// }

// void *hashMapGet(HashMap *hashMap, void *key){
// 	int intKey = 0;
// 	char* strKey = NULL;
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	if(hashMap->keyType == INTKEY){
// 	    intKey = *(int *)key;
// 		HASH_FIND_INT(hashMap->intHashMap, &intKey, intEntry);
// 		if(intEntry == NULL){
// 		    return NULL;
// 		} else {
// 		    return intEntry->value;
// 		}
// 	} else if(hashMap->keyType == STRKEY){
// 	    strKey = (char *)key;
// 		HASH_FIND_STR(hashMap->strHashMap, strKey, strEntry);
// 		if(strEntry == NULL){
// 		    return NULL;
// 		} else {
// 		    return strEntry->value;
// 		}
// 	}
// }

// int hashMapRemove(HashMap *hashMap, void *key){
// 	int intKey = 0;
// 	char* strKey = NULL;
// 	IntHashMap *intEntry = NULL;
// 	StrHashMap *strEntry = NULL;
// 	if(hashMap->keyType == INTKEY){
// 	    intKey = *(int *)key;
// 		HASH_FIND_INT(hashMap->intHashMap, &intKey, intEntry);
// 		if(intEntry == NULL){
// 		    return GNCDB_PARAMNULL;
// 		}
// 		HASH_DEL(hashMap->intHashMap, intEntry);
// 		my_free(intEntry);
// 		hashMap->entryCount = HASH_COUNT(hashMap->intHashMap);
// 	} else if(hashMap->keyType == STRKEY){
// 	    strKey = (char *)key;
// 		HASH_FIND_STR(hashMap->strHashMap, strKey, strEntry);
// 		if(strEntry == NULL){
// 		    return GNCDB_PARAMNULL;
// 		}
// 		HASH_DEL(hashMap->strHashMap, strEntry);
// 		my_free(strEntry->key);
// 		my_free(strEntry);
// 		hashMap->entryCount = HASH_COUNT(hashMap->strHashMap);
// 	}
// 	return GNCDB_SUCCESS;
// }

// void hashMapClear(HashMap *hashMap){
//     IntHashMap *intEntry = NULL;
//     StrHashMap *strEntry = NULL;
//     IntHashMap *intEntryTmp = NULL;
//     StrHashMap *strEntryTmp = NULL;
//     if(hashMap->keyType == INTKEY){
//         HASH_ITER(hh, hashMap->intHashMap, intEntry, intEntryTmp){
//             HASH_DEL(hashMap->intHashMap, intEntry);
//             my_free(intEntry);
//     	}
// 		hashMap->entryCount = HASH_COUNT(hashMap->intHashMap);
// 	} else if(hashMap->keyType == STRKEY){
//         HASH_ITER(hh, hashMap->strHashMap, strEntry, strEntryTmp){
//             HASH_DEL(hashMap->strHashMap, strEntry);
//             my_free(strEntry->key);
//             my_free(strEntry);
//     	}
// 		hashMap->entryCount = HASH_COUNT(hashMap->strHashMap);
// 	}
// }

// void hashMapDestroy(HashMap **hashMap){
// 	if(*hashMap == NULL){
// 		return;
// 	}
//     hashMapClear(*hashMap);
//     my_free(*hashMap);
//     *hashMap = NULL;
// }

// int myHashMapPut(HashMap *hashMap, void *key, void *value){
// 	return hashMapPut(hashMap, key, value);
// }
// int myhashMapCount(HashMap *hashMap, void *key){
// 	if(hashMapExists(hashMap, key)){
// 		return 1;
// 	}
// 	return 0;
// }

// HashMapIterator *createHashMapIterator(HashMap *hashMap)
// {
// 	HashMapIterator *iterator = my_malloc(sizeof(struct HashMapIterator));
// 	if (iterator == NULL) {
// 		return NULL;
// 	}
// 	iterator->hashMap = hashMap;
// 	iterator->count   = 0;
//   iterator->entry = my_malloc0(sizeof(struct Entry));

// 	if(hashMap->keyType == INTKEY){
// 		iterator->intHashMap = hashMap->intHashMap;
// 	} else if(hashMap->keyType == STRKEY){
// 		iterator->strHashMap = hashMap->strHashMap;
// 	}
// 	return iterator;
// }

// bool hasNextHashMapIterator(HashMapIterator *iterator)
// {
// 	if (iterator->intHashMap != NULL && iterator->count < iterator->hashMap->entryCount) {
// 		return true;
// 	}
// 	return false;
// }

// HashMapIterator *nextHashMapIterator(HashMapIterator *iterator)
// {
// 	if(iterator->hashMap->keyType == INTKEY){
// 		iterator->entry->key = &(iterator->intHashMap->key);
// 		iterator->entry->value = iterator->intHashMap->value;
// 		iterator->intHashMap = iterator->intHashMap->hh.next;
// 	} else if(iterator->hashMap->keyType == STRKEY){
// 		iterator->entry->key = iterator->strHashMap->key;
// 		iterator->entry->value = iterator->strHashMap->value;
// 		iterator->strHashMap = iterator->strHashMap->hh.next;
// 	}
// 	iterator->count++;
// 	return iterator;
// }

// void freeHashMapIterator(HashMapIterator **iterator)
// {
//   my_free((*iterator)->entry);
// 	my_free(*iterator);
// 	*iterator = NULL;
// }