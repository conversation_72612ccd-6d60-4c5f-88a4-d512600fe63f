
#ifndef LOOKASIDE_MEM_H
#define LOOKASIDE_MEM_H

#include "gncdb.h"
#include <stdbool.h>

typedef struct LookasideSlot
{
  struct LookasideSlot *pNext; /* Next buffer in the list of free buffers */
} LookasideSlot;

typedef struct Lookaside
{
  unsigned int   bDisable;   /* Only operate the lookaside when zero */
  unsigned short sz;         /* Size of each buffer in bytes */
  unsigned short szTrue;     /* True value of sz, even if disabled */
  bool           bMalloced;  /* True if pStart obtained from sqlite3_malloc() */
  unsigned int   nSlot;      /* Number of lookaside slots allocated */
  unsigned int   nBFreeSlot; /* Number of big slots freed */
  unsigned int   nSFreeSlot; /* Number of small slots freed */
  unsigned int   anStat[3];  /* 0: hits.  1: size misses.  2: full misses */
  LookasideSlot *pInit;      /* List of buffers not previously used */
  LookasideSlot *pFree;      /* List of available buffers */
  LookasideSlot *pSmallInit; /* List of small buffers not previously used */
  LookasideSlot *pSmallFree; /* List of available small buffers */
  void          *pMiddle;    /* First byte past end of full-size buffers and
                             ** the first byte of LOOKASIDE_SMALL buffers */
  void *pStart;              /* First byte of available memory space */
  void *pEnd;                /* First byte past end of available space */
  void *pTrueEnd;            /* True value of pEnd, when db->pnBytesFreed!=0 */
} Lookaside;

#define DisableLookaside    \
  db->lookaside.bDisable++; \
  db->lookaside.sz = 0
#define EnableLookaside     \
  db->lookaside.bDisable--; \
  db->lookaside.sz = db->lookaside.bDisable ? 0 : db->lookaside.szTrue

/* Size of the smaller allocations in two-size lookaside */
#define LOOKASIDE_SMALL 128

/*
** Round down to the nearest multiple of 8
*/
#define ROUNDDOWN8(x) ((x) & ~7)

// #define WITHIN_LOOKASIDE(db, p) 
//   ((db)->lookaside && (db)->lookaside->pStart && (p) >= (db)->lookaside->pStart && (p) < (db)->lookaside->pEnd)

#define WITHIN_LOOKASIDE(db, p) \
  ((p) >= (db)->lookaside->pStart && (p) < (db)->lookaside->pEnd)

#define LOOKASIDE_SLOT_SIZE(db, p) p < db->lookaside->pMiddle ? db->lookaside->szTrue : LOOKASIDE_SMALL

/* -------------------------------lookaside模块对外提供的接口------------------------------- */

int setupLookaside(GNCDB *db, void *pBuf, int sz, int cnt);

int destroyLookaside(GNCDB *db);

void *lookasideMalloc(GNCDB *db, int n);

char *lookasideStrDup(GNCDB *db, const char *z);

int lookasideFree(GNCDB *db, void *p);

void *lookasideRealloc(GNCDB *db, void *p, int n);

/* -------------------------------lookaside分配内存的hashmap接口，注意必须一套使用 ------------------------------- */

HashMap *hashMapCreateLookaside(GNCDB *db, int keyType, int fixBuckNum, HashCode hashCode);

void hashMapDestroyLookaside(GNCDB *db, HashMap **hashMap);

void hashMapClearLookaside(GNCDB *db, HashMap *hashMap);

int hashMapPutLookaside(GNCDB *db, HashMap *hashMap, void *key, void *value);

int hashMapPutDuplicateLookaside(GNCDB *db, HashMap *hashMap, void *key, void *value);

int hashMapRemoveLookaside(GNCDB *db, HashMap *hashMap, void *key);

HashMapIterator *createHashMapIterLookaside(GNCDB *db, HashMap *hashMap);

void freeHashMapIterLookaside(GNCDB *db, HashMapIterator **iterator);

/* ------------------------------- lookaside分配内存的varList接口，注意必须一套使用 ------------------------------- */

varArrayList *varArrayListCreateLookaside(
    GNCDB *db, int flag, int elementSize, int capacity, Compare compare, Destroy destroy);

void varArrayListDestroyLookaside(GNCDB *db, varArrayList **list);

int varArrayListAddLookaside(GNCDB *db, varArrayList *array, void *data);

int varArrayListAddPtrLookaside(GNCDB *db, varArrayList *array, void *pointer);

int varArrayListRmLookaside(GNCDB *db, varArrayList *array, void *data);

int varArrayListRmPtrLookaside(GNCDB *db, varArrayList *array, void *data);

int varArrayListRmByIdxLookaside(GNCDB *db, varArrayList *array, int index);

#endif /* LOOKASIDE_MEM_H */
