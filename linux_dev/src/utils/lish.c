#include "list.h"
#include "gncdbconstant.h"

//链表节点描述
// typedef struct _DListNode
// {
//     struct _DListNode* pre;
//     struct _DListNode* next;

//     void* data;
// }DListNode;

// //链表描述
// struct _DList
// {
//     DListNode* first;
//     DListDataDestroyFunc data_destroy;
//     void* data_destroy_ctx;
// };

/************************** 内部使用的函数 ****************************/
//销毁一个节点的data成员，调用回调函数销毁
// static void _dlist_destroy_data(DList* thiz, void* data)
// {
//     if (thiz->data_destroy != NULL)
//     {
//         thiz->data_destroy(thiz->data_destroy_ctx, data);
//     }

//     return;
// }

//产生一个节点
// static DListNode *_dlist_create_node(DList *thiz, int accessRecord)
// {
//     DListNode *node = my_malloc(sizeof(DListNode));

//     if (node != NULL)
//     {
//         node->pre = NULL;
//         node->next = NULL;
//         node->accessRecord = accessRecord;
//     }
//     return node;
// }

//通过index获取一个节点
static DListNode* _dlist_get_node(DList* thiz, size_t index)
{
    DListNode* iter = thiz->first;
    while (iter != NULL && iter->next != NULL && index > 0)
    {
        iter = iter->next;
        index--;
    }
    if(index > 0)
        iter = NULL;
    else if(index == -1)
        iter = thiz->tail;
    return iter;
}

//销毁一个节点
// static void _dlist_destroy_node(DList* thiz, DListNode* node)
// {
//     if (node != NULL)
//     {
//         node->next = NULL;
//         node->pre = NULL;
//        // _dlist_destroy_data(thiz, node->data);
//         free(node);
//     }
//     return;
// }

/************************** 调用者可使用的函数 ****************************/
//链表生成，参数分别为销毁节点data的回调以及回调的参数
DList* dlist_create(DListDataDestroyFunc data_destroy, void* data_destroy_ctx)
{
    DList* thiz = my_malloc(sizeof(DList));

    if (thiz != NULL)
    {
        thiz->first = NULL;
        thiz->data_destroy = data_destroy;
        thiz->data_destroy_ctx = data_destroy_ctx;
        thiz->tail = NULL;
        thiz ->length =0;
    }
    return thiz;
}

//index为-1代表尾插，0代表头插，其余代表插入的位置，index大于链表长度也为尾插
DListRet dlist_insert(DList *thiz, int index, DListNode *node)
{
    //DListNode* node = NULL;
    DListNode* cursor = NULL;
    thiz->length++;

    //构造节点
    // if ((node = _dlist_create_node(thiz, data)) == NULL)
    // {
    //     return DLIST_RET_OOM;
    // }
    if(node == NULL)
        return DLIST_RET_FAIL;

    //插入时链表为空
    if (thiz->first == NULL){
        thiz->first = node;
        thiz->tail = node;
        return DLIST_RET_OK;
    }
    //尾插
    if(index == -1){
        cursor = thiz->tail;
        cursor->next = node;
        node->pre = cursor;
        thiz->tail = node;
        return DLIST_RET_OK;
    }


    //插入链表中间位置
    if (index < thiz->length)
    {
        //获取目标节点位置
        cursor = _dlist_get_node(thiz, index);
        //头插
        if (thiz->first == cursor)
        {
            thiz->first = node;
        }
        //插入数据中间
        else
        {
            node->pre = cursor->pre;
            cursor->pre->next = node;

        }
        node->next = cursor;
        cursor->pre = node; 
        return DLIST_RET_OK;
    }
    //index >= thiz->length,尾插
    else{
        cursor = thiz->tail;
        cursor->next = node;
        node->pre = cursor;
        thiz->tail = node;
        return DLIST_RET_OK;
    }
}

//头插
DListRet dlist_prepend(DList *thiz, DListNode *node)
{
    return dlist_insert(thiz, 0, node);
}

//尾插
DListRet dlist_append(DList *thiz, DListNode *node)
{
    return dlist_insert(thiz, -1, node);
}

//删除一个节点,只是把pageStatus移除，不需要destroy
DListRet dlist_delete(DList *thiz, DListNode *cursor)
{
    //获取目标节点位置，0表示index超过length就返回NULL
    // DListNode* cursor = _dlist_get_node(thiz, index, 0);

    if (cursor != NULL && thiz->length >0)
    {
        if (cursor == thiz->first)
        {
            thiz->first = cursor->next;
            if(cursor->next != NULL)
                cursor->next->pre = NULL;
        }
        else if (cursor->next != NULL) // 中间节点
        {
            cursor->pre->next = cursor->next;
            cursor->next->pre = cursor->pre;
        }
        else // 尾节点
        {
            thiz->tail = cursor->pre;
            cursor->pre->next = NULL;
        }
        cursor->pre = NULL;
        cursor->next = NULL;
        
        thiz->length--;
        if(thiz->length == 0){
            thiz->first = NULL;
            thiz->tail = NULL;
        }    
        return DLIST_RET_OK;
    }
    else
        return DLIST_RET_FAIL;
    
}

//通过index获取一个节点的data
DListRet dlist_get_by_index(DList *thiz, int index, int *accessRecord)
{
    DListNode *cursor = NULL;
        
    cursor = _dlist_get_node(thiz, index);

    if (cursor != NULL)
    {
        *accessRecord = cursor->accessRecord;
    }

    return cursor != NULL ? DLIST_RET_OK : DLIST_RET_FAIL;
}

//通过index设置一个节点的data
DListRet dlist_set_by_index(DList *thiz, int index, int accessRecord)
{
    DListNode *cursor = NULL;
    cursor = _dlist_get_node(thiz, index);

    if (cursor != NULL)
    {
        cursor->accessRecord = accessRecord;
    }

    return cursor != NULL ? DLIST_RET_OK : DLIST_RET_FAIL;
}


//查找目标数据所在的节点，比较函数采用回调的方法
int dlist_find(DList *thiz, int target)
{
    int i = 0;
    DListNode* iter = thiz->first;

    while (iter != NULL)
    {
        if (target == iter->accessRecord)
        {
            break;
        }
        i++;
        iter = iter->next;
    }

    return i;
}

//遍历链表，并调用回调函数
DListRet dlist_foreach(DList* thiz, DListDataVisitFunc visit, void* ctx)
{
    DListRet ret = DLIST_RET_OK;
    DListNode* iter = thiz->first;

    while (iter != NULL && ret != DLIST_RET_STOP)
    {
        ret = visit(ctx, (void*)(&iter->accessRecord));
        iter = iter->next;
    }

    return ret;
}

//销毁整个链表
void dlist_destroy(DList* thiz)
{
    // DListNode* iter = thiz->first;
    // DListNode* next = NULL;

    // while (iter != NULL)
    // {
    //     next = iter->next;
    //     _dlist_destroy_node(thiz, iter);
    //     iter = next;
    // }
    thiz->first = NULL;
    my_free(thiz);

    return;
}

int dlist_node_num(DList* thiz) {
    int cnt = 0;
    DListNode* iter = thiz->first;

    while (iter != NULL) {
        cnt++;
        iter = iter->next;
    }
    return cnt;
}