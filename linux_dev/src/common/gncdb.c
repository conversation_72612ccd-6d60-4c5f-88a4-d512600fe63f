/************************************************
 版    权: 
 文 件 名: gncdb.c
 创建日期: 2025-08-27 12:05:42
 编 写 者: zql
 内容描述: 
 关系文件: 
 更改历史: 
************************************************/
/*
 * @Descripttion:
 * @version:
 * @Author: Alan
 * @Date: 2023-01-30 09:19:38
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-26 17:20:54
 */
#include "gncdb.h"
#include "hashmap.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "catalog.h"
#include "exec_tuple.h"
#include "hashmap.h"
#include "expression.h"
#include "parse_defs.h"
#include "sql_event.h"
#include "table_stats.h"
#include "transaction.h"
#include "typedefine.h"
#include "utils.h"
#include "queryexecutor.h"
#include "value.h"
#include "vararraylist.h"
#include "runninglog.h"
#include "pagepool.h"
#include "tranmanager.h"
#include "parse_stage.h"
#include "resolve_stage.h"
#include "optimize_stage.h"
#include "execute_stage.h"
#include <stdarg.h>
#include <limits.h>
#include <float.h>
#include <time.h>
#include <ctype.h>
#include <stdio.h>
#include <string.h>
#include <sys/types.h>
extern int             printHeader;
typedef enum FieldType FieldType;
int                    dbInstanceInit(struct GNCDB **db, char *fileName);
int                    dbInstanceFillInFileHeader(struct GNCDB *db);
int                    dbInstanceCreatDatabase(struct GNCDB *db, struct Transaction *tx);
int                    dbInstanceCheckDatabaseValid(struct GNCDB *db);

int dbInstancePaserFilterStr(struct GNCDB *db, char *filterStr, char **left, char **right, enum CompOp *predicate);
int loadMetaData(struct GNCDB *db);
int checkFieldName(int tableNum, char *columnName, varArrayList *tableArray, HashMap *fieldNameMap);
int checkFilter(char *filterStr, struct GNCDB *db, int tableNum, varArrayList *tableArray, HashMap *fieldNameMap);
int parameterCheckAndTransformOfBlob(
    struct GNCDB *db, char *tableName, int columnNum, int keyValueNum, struct varArrayList *keyValueArray);

void mapArrayDestroy(HashMap *hashMap);
void tableNameArrayDestroy(void *value);
int  printHeader = 1;
// static int cnt         = 0;
// int        printHeader = 1;  // 静态变量，用于跟踪是否需要打印列名
// int        myCallBack(void *data, int argc, char **azColName, char **argv)
// {
//   int i;
//   cnt++;
//   // 如果printHeader为1，则打印列名
//   if (printHeader) {
//     for (i = 0; i < argc; i++) {
//       printf("%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
//     }
//     printf("\n");
//     printHeader = 0;  // 设置为0，下次调用时不再打印列名 // 初始化循环变量i为0
//   }  // 初始化返回码rc为0
//   for (i = 0; i < argc; i++) {
//     printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");  // 初始化全局变量SAMETABLE为0
//   }  // 从10开始循环，直到i小于MULTI_TESTNUM
//   printf("\n");
//   return 0;  // 如果i等于5
// }

int txnExecuteSQL(SQLStageEvent *sqlEvent, const char *sql)
{
  int rc                 = GNCDB_SUCCESS;
  sqlEvent->sql          = my_strdup(sql);
  sqlEvent->affectedRows = 0;
  sqlEvent->isStep       = false;
  printHeader            = true;

  /*1.词法分析*/
  rc = ParseStageHandleRequest(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    // printf("ParseStageHandleRequest failed, rc = %d\n", rc);
    return rc;
  }

  /*2.进行参数检查后对词法分析结果进行语义分析并构造成相应的stmt*/
  /* 修改为解析sql之后判断是否是查询语句再创建事务，如果是查询，跳过写begin日志操作 */
  if (sqlEvent->sqlNode->flag == SCF_SELECT) {
    sqlEvent->txn = transcationConstrcutSelect(sqlEvent->db, true);
    if (sqlEvent->txn == NULL) {
      return GNCDB_MEM;
    }
  } else {
    sqlEvent->txn = transcationConstrcutSelect(sqlEvent->db, false);
    if (sqlEvent->txn == NULL) {
      return GNCDB_MEM;
    }
  }

  /*3.构建stmt*/
  rc = ResolveStageHandleRequest(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    // printf("ResolveStageHandleRequest failed, rc = %d\n", rc);
    return rc;
  }

  /*4.查询优化*/
  rc = OptimizeStageHandleRequest(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    // printf("OptimizeStageHandleRequest failed, rc = %d\n", rc);
    return rc;
  }

  /*5.查询执行*/
  rc = ExecuteStageHandleRequest(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    // printf("ExecuteStageHandleRequest failed, rc = %d\n", rc);
    return rc;
  }
  return rc;
}

int txnExecuteOrigAPI(SQLStageEvent *sqlEvent)
{
  int rc      = GNCDB_SUCCESS;
  printHeader = true;

  rc = ResolveStageHandleRequest(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = OptimizeStageHandleRequest(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = ExecuteStageHandleRequest(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return rc;
}

int parameterCheckAndTransformOfBlob(
    struct GNCDB *db, char *tableName, int columnNum, int keyValueNum, struct varArrayList *keyValueArray)
{
  varArrayList *primaryIndexArray = NULL;
  TableSchema  *tableSchema       = NULL;
  Column       *column            = NULL;
  int          *index             = 0;
  int           rc                = 0;
  int           length            = 0;
  int           i                 = 0;
  char         *value             = NULL;
  int          *intValue          = NULL;
  char         *stringValueCopy   = NULL;
  if (db == NULL || tableName == NULL || keyValueArray == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 获取tableName对应的primaryIndexMap（同时也对tableName做了检查，看tableName是否合理） */
  LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
  WriteLock(&(db->catalog->keyIndexLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
  primaryIndexArray = hashMapGet(db->catalog->tablePrimaryKeyIndexMap, tableName);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
  WriteUnLock(&(db->catalog->keyIndexLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");
  /* 同时对primaryIndexMap->elementCount与keyValueNum做比较，如果不相等，说明传入的参数有问题 */
  if (primaryIndexArray == NULL || primaryIndexArray->elementCount != keyValueNum) {
    return GNCDB_NOT_FOUND;
  }

  /* 获取tableSchema */
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return GNCDB_NOT_FOUND;
  }

  /* 检查列号是否合法 */
  column = (Column *)varArrayListGetPointer(tableSchema->columnList, columnNum);
  if (column == NULL) {
    return GNCDB_NOT_FOUND;
  }
  if (column->fieldType != FIELDTYPE_BLOB) {
    return GNCDB_PARAM_INVALID;
  }

  for (i = 0; i < keyValueNum; i++) {
    index = (int *)varArrayListGet(primaryIndexArray, i);
    if (index == NULL) {
      return GNCDB_NOT_FOUND;
    }

    column = (Column *)varArrayListGetPointer(tableSchema->columnList, *index);
    if (column == NULL) {
      return GNCDB_NOT_FOUND;
    }

    /* 这里的fieldName需要深拷贝一份，后续要对该内容进行内存释放 */
    /*length = strlen(column->fieldName);
    fieldNameCopy = (char*)my_malloc(length + 1);
    if (fieldNameCopy == NULL) {
        return GNCDB_MEM;
    }
    memcpy(fieldNameCopy, column->fieldName, length);
    fieldNameCopy[length] = '\0';*/

    /* todo?
     * 这里需要做检查，判断输入的值是整数类型还是字符串类型，如果输入的数据类型不满足主键值在tuple中的顺序，需要返回给用户输入错误
     */
    value = (char *)varArrayListGetPointer(keyValueArray, i);
    switch (column->fieldType) {
      case FIELDTYPE_INTEGER: {
        intValue = (int *)my_malloc(sizeof(int));
        if (intValue == NULL) {
          return GNCDB_MEM;
        }
        *intValue = atoi(value);
        my_free(value);
        rc = varArrayListSetByIndexPointer(keyValueArray, i, intValue);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        break;
      }
      case FIELDTYPE_VARCHAR: {
        length          = strlen(value);
        stringValueCopy = (char *)my_malloc(length + 1);
        if (stringValueCopy == NULL) {
          return GNCDB_MEM;
        }
        memcpy(stringValueCopy, value, length);
        stringValueCopy[length] = '\0';
        my_free(value);
        rc = varArrayListSetByIndexPointer(keyValueArray, i, stringValueCopy);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        break;
      }
      default: break;
    }
  }

  return GNCDB_SUCCESS;
}

// int checkFilter(char* filterStr, struct GNCDB* db, int tableNum, varArrayList* tableArray, HashMap* fieldNameMap)
// {
// 	int rc = 0;
// 	enum Predicate predicate = EQUAL;
// 	char* left = NULL;
// 	char* right = NULL;

// 	rc = dbInstancePaserFilterStr(db, filterStr, &left, &right, &predicate);
// 	if (rc)
// 	{
// 		return rc;
// 	}
// 	rc = checkFieldName(tableNum, left, tableArray, fieldNameMap);
// 	if (rc != GNCDB_SUCCESS)
// 	{
// 		return rc;
// 	}
// 	return GNCDB_SUCCESS;
// }

/**
 * @brief  检查fieldName是否合法
 * @param  tableNum: 表的数量
 * @param  columnName: 	check的字段名
 * @param  tableArray: 	表名数组
 * @param  fieldNameMap: tableName->fieldNames的hashMap
 * @return int: 返回检查结果
 */
int checkFieldName(int tableNum, char *columnName, varArrayList *tableArray, HashMap *fieldNameMap)
{
  int           rc = 0;
  char         *token[2];
  int           nameIndex   = 0;
  varArrayList *tArray      = NULL;
  int           indexField  = 0;
  char         *tTableName  = NULL;
  varArrayList *array       = NULL;
  char         *tTableName2 = NULL;
  varArrayList *array2      = NULL;

  // 对fieldName进行check
  if (tableNum > 1 && hasOneDots(columnName) == 1) {
    split_string(columnName, token);
    nameIndex = varArrayListIndexOf(tableArray, token[0]);
    if (nameIndex == GNCDB_NOT_FOUND) {
      return GNCDB_PARAMNULL;
    }
    tArray = (varArrayList *)hashMapGet(fieldNameMap, token[0]);
    if (tArray == NULL) {
      return GNCDB_NOT_FOUND;
    }
    indexField = varArrayListIndexOf(tArray, token[1]);
    if (indexField == GNCDB_NOT_FOUND) {
      return GNCDB_PARAMNULL;
    }
  } else {
    tTableName = (char *)varArrayListGetPointer(tableArray, 0);
    array      = (varArrayList *)hashMapGet(fieldNameMap, tTableName);
    if (array == NULL) {
      return GNCDB_NOT_FOUND;
    }
    rc = varArrayListIndexOf(array, columnName);
    if (tableNum > 1 && rc == GNCDB_NOT_FOUND) {
      tTableName2 = (char *)varArrayListGetPointer(tableArray, 1);
      array2      = (varArrayList *)hashMapGet(fieldNameMap, tTableName2);
      if (array == NULL) {
        return GNCDB_NOT_FOUND;
      }
      rc = varArrayListIndexOf(array2, columnName);
    }
    if (rc == GNCDB_NOT_FOUND) {
      return GNCDB_PARAMNULL;
    }
  }
  return GNCDB_SUCCESS;
}

int dbInstanceFillInFileHeader(GNCDB *db)
{
  int   offset = 0, rc = 0;
  BYTE *fileHeader = NULL;

  fileHeader = my_malloc(db->pageCurrentSize);
  if (fileHeader == NULL) {
    return GNCDB_MEM;
  }
  memset(fileHeader, 0, db->pageCurrentSize);
  // dbOverview,dbVersion各自限长32字节
  rc = writeString((char *)db->dbOverview, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS || offset > BYTES32) {
    my_free(fileHeader);
    return GNCDB_PARAM_INVALID;
  }
  offset = BYTES32;
  // rc     = writeString((char *)db->dbVersion, &fileHeader[offset], &offset);
  // if (rc != GNCDB_SUCCESS || offset > BYTES64) {
  //   my_free(fileHeader);
  //   return GNCDB_PARAM_INVALID;
  // }
  // offset = BYTES64;
  rc = writeInt(db->dbMajor, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }
  rc = writeInt(db->dbMinor, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }

  rc = writeInt(db->pageCurrentSize, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }

  rc = writeInt(db->totalPageNum, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }
  rc = writeInt(db->firstFreePid, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }
  /* 将master表和schema表的最大id写入数据库 */
  rc = writeInt(db->catalog->masterCurrentMaxId, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }
  rc = writeInt(db->catalog->schemaCurrentMaxIncId, &fileHeader[offset], &offset);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }
  rc = osWrite(db->dbFile, fileHeader, 0, (db->pageCurrentSize));
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }

  my_free(fileHeader);

  return GNCDB_SUCCESS;
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
int loadMetaData(GNCDB *db)
{
  int rc           = 0;
  int totalPageNum = 0;
  rc               = dbInstanceReadFileHeader(db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  totalPageNum = db->totalPageNum;
  rc           = btreeTableDfsTraversal(db->catalog->masterTable, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = btreeTableDfsTraversal(db->catalog->schemaTable, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  db->totalPageNum = totalPageNum;
  return rc;
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="fileName"></param>
/// <returns></returns>
int dbInstanceInit(GNCDB **pdb, char *fileName)
{
  int          rc = 0;
  Transaction *tx = NULL;
  GNCDB       *db = *pdb;

  ReadWriteLockInit(&(db->latch));
  ReadWriteLockInit(&(db->latchSQLParse));
  rc = pagePoolConstruct(db);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_MEM;
  }
#ifdef ENABLE_LOOKASIDE_MEM_POOL
  rc = setupLookaside(db, 0, LOOKASIDE_SIZE, LOOKASIDE_COUNT);
  if (rc != GNCDB_SUCCESS) {
    pagePoolDestroy(db->pagePool);
    return rc;
  }
#endif
  rc = catalogConstruct(db);
  if (rc != GNCDB_SUCCESS) {
    pagePoolDestroy(db->pagePool);
    return rc;
  }
  if (!isFileExist(db->fileName)) {
    rc = createFile(db->fileName, &(db->dbFile));
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    db->transactionManager = txManagerConstruct(db);
    tx                     = transcationConstrcut(db);
    if (tx == NULL) {
      return GNCDB_MEM;
    }
    rc = dbInstanceCreatDatabase(db, tx);
    if (rc != GNCDB_SUCCESS) {
      transactionRollback(tx, db);
      pagePoolDestroy(db->pagePool);
      catalogDestroy(db->catalog);
      return rc;
    }

    rc = dbInstanceFillInFileHeader(db);
    if (rc != GNCDB_SUCCESS) {
      transactionRollback(tx, db);
      pagePoolDestroy(db->pagePool);
      catalogDestroy(db->catalog);
      return rc;
    }
  } else {

    rc = osOpenFile(db->fileName, &(db->dbFile));
    if (rc != GNCDB_SUCCESS) {
      pagePoolDestroy(db->pagePool);
      catalogDestroy(db->catalog);
      return rc;
    }
    db->transactionManager = txManagerConstruct(db);
    tx                     = transcationConstrcut(db);
    if (tx == NULL) {
      return GNCDB_MEM;
    }
    rc = loadMetaData(db);
    if (rc != GNCDB_SUCCESS) {
      pagePoolDestroy(db->pagePool);
      catalogDestroy(db->catalog);
      return rc;
    }
    rc = catalogParseMasterAndSchema(db, tx);
    if (rc != GNCDB_SUCCESS) {
      pagePoolDestroy(db->pagePool);
      catalogDestroy(db->catalog);
      return rc;
    }
  }

  transactionCommit(tx, db);
  return GNCDB_SUCCESS;
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="filterStr"></param>
/// <param name="left"></param>
/// <param name="right"></param>
/// <param name="op"></param>
/// <returns></returns>
int dbInstancePaserFilterStr(GNCDB *db, char *filterStr, char **left, char **right, enum CompOp *predicate)
{
  int pos = 0;
  // char* newstr = my_malloc(sizeof(64));
  // delete_space(filterStr,newstr);
  // filterStr = newstr;
  pos = find_str(filterStr, ">=");
  if ((pos = find_str(filterStr, ">=")) != -1) {
    // *predicate = GREATER_THAN_OR_EQUAL;
    *predicate = CMPOP_GREAT_EQUAL;
  } else if ((pos = find_str(filterStr, ">")) != -1) {
    // *predicate = GREATER_THAN;
    *predicate = CMPOP_GREAT_THAN;
  } else if ((pos = find_str(filterStr, "<=")) != -1) {
    // *predicate = LESS_THAN_OR_EQUAL;
    *predicate = CMPOP_LESS_EQUAL;
  } else if ((pos = find_str(filterStr, "<")) != -1) {
    // *predicate = LESS_THAN;
    *predicate = CMPOP_LESS_THAN;
  } else if ((pos = find_str(filterStr, "=")) != -1) {
    // *predicate = EQUAL;
    *predicate = CMPOP_EQUAL_TO;
  }
  // eg:A.name>=B.name -> A.name>=B.name
  switch (*predicate) {
    case CMPOP_GREAT_EQUAL:
    case CMPOP_LESS_EQUAL: {
      *left  = sub_str(filterStr, 0, pos - 1);
      *right = sub_str(filterStr, pos + 1, strlen(filterStr) - pos);
      break;
    }
    case CMPOP_LESS_THAN:
    case CMPOP_GREAT_THAN:
    case CMPOP_EQUAL_TO: {
      *left  = sub_str(filterStr, 0, pos - 1);
      *right = sub_str(filterStr, pos, strlen(filterStr) - pos);
      break;
    }
    default: break;
  }
  return GNCDB_SUCCESS;
}
/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
int dbInstanceReadFileHeader(GNCDB *db)
{
  int   rc         = 0;
  BYTE *fileHeader = NULL;
  int   offset     = 0;
  int   i          = 0;
  char  ch         = 0;
  long  fileLength = 0;
  char  dbName[32] = {0};
  int   pageSize   = 0;
  // char  dbVersion[32] = {0};

  rc = getFileLength(&fileLength, db->dbFile);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  if (fileLength < (db->pageCurrentSize)) {
    return GNCDB_PARAM_INVALID;
  }
  fileHeader = (BYTE *)my_malloc((db->pageCurrentSize));
  if (fileHeader == NULL) {
    return GNCDB_MEM;
  }

  rc = osRead(db->dbFile, 0, (db->pageCurrentSize), &fileHeader);
  if (rc != GNCDB_SUCCESS) {
    my_free(fileHeader);
    return rc;
  }

  strcpy(dbName, "GNCDB");
  // strcpy(dbVersion, "01");
  // dbOverview读取
  for (i = 0; i < OVERVIEW_LEN; i++) {
    rc = readChar(&ch, fileHeader + offset, &offset);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (ch != dbName[i]) {
      my_free(fileHeader);
      return GNCDB_PARAM_INVALID;
    }
    if (ch == 0)
      break;
    db->dbOverview[i] = ch;
  }
  db->dbOverview[i] = 0;
  // dbVersion读取
  offset = BYTES32;
  // for (i = 0; i < VERSION_LEN; i++) {
  //   rc = readChar(&ch, fileHeader + offset, &offset);
  //   if (rc != GNCDB_SUCCESS) {
  //     return rc;
  //   }
  //   if (ch != dbVersion[i]) {
  //     my_free(fileHeader);
  //     return GNCDB_PARAM_INVALID;
  //   }
  //   if (ch == 0)
  //     break;
  //   db->dbVersion[i] = ch;
  // }
  // db->dbVersion[i] = 0;
  // offset           = BYTES64;

  rc = readInt(&(db->dbMajor), fileHeader + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = readInt(&(db->dbMinor), fileHeader + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  rc = readInt(&pageSize, fileHeader + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  rc = readInt(&(db->totalPageNum), fileHeader + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = readInt(&(db->firstFreePid), fileHeader + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = readInt(&(db->catalog->masterCurrentMaxId), fileHeader + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = readInt(&(db->catalog->schemaCurrentMaxIncId), fileHeader + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  my_free(fileHeader);
  return GNCDB_SUCCESS;
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
int dbInstanceCreatDatabase(GNCDB *db, struct Transaction *tx)
{
  int        rc         = 0;
  BtreePage *masterPage = NULL;
  BtreePage *schemaPage = NULL;
  PagePool  *pagePool   = db->pagePool;

  rc = pagePoolConstructNewBtreePage(&masterPage, pagePool, db, LEAF_PAGE, MASTER_ROOT_PID, MASTERNAME, db->catalog);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_MEM;
  }
  totalPageNumAdd(db);
  rc = setPageStatusDirty(db->pagePool, masterPage->page.id, NULL);
  if (rc != GNCDB_SUCCESS) {
    // btreePageDestroy(&masterPage);
    return rc;
  }

  rc = produceOldPageData(db, (Page *)masterPage, FREE_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    // btreePageDestroy(&masterPage);
    return rc;
  }

  setPageStatusPinDown(db->pagePool, masterPage->page.id, NULL);

  rc = pagePoolConstructNewBtreePage(&schemaPage, pagePool, db, LEAF_PAGE, SCHEMA_ROOT_PID, SCHEMANAME, db->catalog);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_MEM;
  }
  totalPageNumAdd(db);

  rc = setPageStatusDirty(db->pagePool, schemaPage->page.id, NULL);
  if (rc != GNCDB_SUCCESS) {

    // btreePageDestroy(&masterPage);
    // btreePageDestroy(&schemaPage);
    return rc;
  }
  rc = produceOldPageData(db, (Page *)schemaPage, FREE_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {

    // btreePageDestroy(&masterPage);
    // btreePageDestroy(&schemaPage);
    return rc;
  }
  setPageStatusPinDown(db->pagePool, schemaPage->page.id, NULL);
  return GNCDB_SUCCESS;
}
/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
int dbInstanceCheckDatabaseValid(GNCDB *db)
{
  if (!isFileExist(db->fileName)) {
    return GNCDB_NO_VALID;

  } else {
    return GNCDB_SUCCESS;
  }
}
/// <summary>
///
/// </summary>
/// <param name="str"></param>
/// <returns></returns>
bool validFileName(char *str)
{

  int state = 0;
  int i     = 0;
  int len   = 0;

  if (str == NULL) {
    return false;
  }
  len = strlen(str);
  if (len > FILENAME_FIELD_MAXLEN || len <= 4) {
    return false;
  }
  /* 必须以.dat结尾 */
  if (str[len - 4] != '.' || str[len - 3] != 'd' || str[len - 2] != 'a' || str[len - 1] != 't') {
    return false;
  }
  for (i = 0; i < len - 4; i++) {
    switch (state) {
      case 0:  // 开头必须是字母
        if ((str[i] >= 'a' && str[i] <= 'z') || (str[i] >= 'A' && str[i] <= 'Z')) {
          state = 1;
        } else {
          return false;
        }
        break;
      case 1:  // 可以是字母、数字或下划线
        if ((str[i] >= 'a' && str[i] <= 'z') || (str[i] >= 'A' && str[i] <= 'Z') || (str[i] >= '0' && str[i] <= '9') ||
            str[i] == '_') {
          state = 1;
        } else {
          return false;
        }
        break;
    }
  }

  return true;
}

/**
 * @brief 在open数据库时进行统计信息的收集
 *
 * @param db 		数据库实例
 * @return int 		返回错误码
 */
int collectStatistics(struct GNCDB *db)
{
  int rc = GNCDB_SUCCESS;
#if STATISTICS_ENABLE
  HashMapIterator *tableIter  = NULL;
  TableStats      *tableStats = NULL;
  Transaction     *tx         = NULL;

  if (db == NULL) {
    return GNCDB_PARAMNULL;
  }

  // 如果catalog里没有表，直接返回
  if (db->catalog->tableMap->entryCount == 0) {
    return GNCDB_SUCCESS;
  }

  // 否则，遍历catalog里的所有表，进行统计信息的收集
  tx        = transcationConstrcut(db);
  tableIter = createHashMapIterator(db->catalog->tableMap);
  while (hasNextHashMapIterator(tableIter)) {
    tableIter = nextHashMapIterator(tableIter);
    if (tableIter == NULL) {
      return GNCDB_TABLE_NOT_FOUND;
    }
    rc = create_table_stats(&tableStats, tableIter->entry->key, db, tx);
    if (rc != GNCDB_SUCCESS) {
      freeHashMapIterator(&tableIter);
      transactionRollback(tx, db);
      return rc;
    }
  }
  freeHashMapIterator(&tableIter);
  transactionCommit(tx, db);
#endif
  return rc;
}

// DT_UCHAR8 gp_uc8_mem_buffer[PAG(db->pageCurrentSize)E_SIZE * MAX_PAGE_NUM];
/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="fileName"></param>
/// <returns></returns>
int GNCDB_open(struct GNCDB **db, char *fileName, int pageSize, int pagePoolCount)
{

  int   rc         = 0;
  BYTE *fileHeader = NULL;
  int   readSize   = 0;
  int   offset     = 0;
  if (fileName == NULL || !strlen(fileName)) {
    return GNCDB_PARAMNULL;
  }
  *db = my_malloc0(sizeof(GNCDB));
  if ((*db) == NULL)
    return GNCDB_MEM;
  (*db)->pagePool = NULL;
  (*db)->catalog  = NULL;
  //(*db)->totalFreePageNum = 0;
  (*db)->totalPageNum = 1;  // 第一页保存数据库描述信息
  (*db)->firstFreePid = 0;
  memcpy((*db)->dbOverview, "GNCDB", sizeof(char) * OVERVIEW_LEN);
  (*db)->dbOverview[OVERVIEW_LEN] = '\0';
  // memcpy((*db)->dbVersion, "01", sizeof(char) * VERSION_LEN);
  // (*db)->dbVersion[2] = '\0';
  (*db)->dbMajor  = 1;
  (*db)->dbMinor  = 0;
  (*db)->fileName = my_strdup(fileName);

  if (pagePoolCount <= 0) {
    (*db)->pagePoolCount = MAX_CACHED_NUM;
  } else {
    (*db)->pagePoolCount = pagePoolCount;
  }

  if (!isFileExist((*db)->fileName)) {
    if (pageSize < 1024) {
      /* 页大小最小值为1024 */
      (*db)->pageCurrentSize = PAGE_SIZE;
    } else {
      (*db)->pageCurrentSize = pageSize;
    }
  } else {
    readSize   = BYTES32 + MAJOR_VERSION + MINOR_VERSION + sizeof(int); /* 数据库描述 主版本号 服版本号 页大小 */
    fileHeader = my_malloc(readSize * sizeof(char));
    if (fileHeader == NULL) {
      return GNCDB_MEM;
    }
    rc = osOpenFile((*db)->fileName, &((*db)->dbFile));
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = osRead((*db)->dbFile, 0, readSize, &fileHeader);
    if (rc != GNCDB_SUCCESS) {
      my_free(fileHeader);
      return rc;
    }
    offset = BYTES32 + MAJOR_VERSION + MINOR_VERSION;
    readInt(&((*db)->pageCurrentSize), fileHeader + offset, &offset);
    my_free(fileHeader);
    fclose((*db)->dbFile);
  }

  (*db)->dbLog = DBLogConstruct(*db);
  if ((*db)->dbLog == NULL) {
    my_free((*db));
    return GNCDB_MEM;
  }

  (*db)->dbFile = NULL;
  LOG(LOG_INFO, "*************** OPEN DB  Name = %s ***************", (*db)->dbOverview);

#if defined(__linux__) || defined(_WIN32)

#else
  MutexLockPoolInit();
  CondtLockPoolInit();
#endif

  rc = dbInstanceInit(db, fileName);
  if (rc != GNCDB_SUCCESS) {
    my_free((*db));
    return rc;
  }

#ifdef ENABLE_JOIN_OPTIMIZATION
  // 统计信息收集
  rc = collectStatistics(*db);
  if (rc != GNCDB_SUCCESS) {
    // 如果仅统计信息收集失败，打印错误信息，不返回错误码
    printf("collectStatistics failed\n");
  }
#endif
  return GNCDB_SUCCESS;
}

bool validString(char *str)
{

  int state = 0;
  int i     = 0;
  int len   = 0;

  if (str == NULL) {
    return false;
  }
  len = strlen(str);
  if (len > TABLENAME_FIELD_MAXLEN || len <= 0) {
    return false;
  }

  for (i = 0; i < len; i++) {
    switch (state) {
      case 0:  // 开头必须是字母
        if ((str[i] >= 'a' && str[i] <= 'z') || (str[i] >= 'A' && str[i] <= 'Z')) {
          state = 1;
        } else {
          return false;
        }
        break;
      case 1:  // 可以是字母、数字或下划线
        if ((str[i] >= 'a' && str[i] <= 'z') || (str[i] >= 'A' && str[i] <= 'Z') || (str[i] >= '0' && str[i] <= '9') ||
            str[i] == '_') {
          state = 1;
        } else {
          return false;
        }
        break;
    }
  }

  return true;
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name="columnNum"></param>
/// <param name=""></param>
/// <returns></returns>
int GNCDB_createTable(struct GNCDB *db, char *tableName, int columnNum, ...)
{
  /* 处理表名超出长度 */
  int               rc     = 0;
  int               i      = 0;
  int               offset = 0;
  Transaction      *tx     = NULL;
  va_list           ap;
  varArrayList     *primaryKeyIndex      = NULL;
  varArrayList     *primaryKeyOffset     = NULL;
  varArrayList     *primaryKeyType       = NULL;
  varArrayList     *primaryKeyVarcharLen = NULL;
  varArrayList     *columnList           = NULL;
  HashMap          *fieldMap             = NULL;
  char             *fieldName            = NULL;
  int               len                  = 0;
  int               fieldType            = 0;
  int               canBeNull            = 0;
  int               isPrimaryKey         = 0;
  double            minValue             = 0;
  double            maxValue             = 0;
  ColumnConstraint *constraint           = NULL;
  Column           *column               = NULL;
  int               rowIdPrimaryKey      = 1;
  int               rowIdPrimaryType     = FIELDTYPE_INTEGER;
  ColumnConstraint *constraint1          = NULL;
  Column           *column1              = NULL;
  ColumnConstraint *constraint2          = NULL;
  Column           *column2              = NULL;
  ColumnConstraint *constraint3          = NULL;
  Column           *column3              = NULL;
  TableSchema      *tableSchema          = NULL;
  int               maxrownum            = 0;
  bool              tableExist           = false;

  if (tableName == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (!validString(tableName)) {
    return GNCDB_PARAM_INVALID;
  }

  /* 处理重复或者与master和schema表重名的表名 */
  LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
  WriteLock(&(db->catalog->tableMapLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
  tableExist = hashMapExists(db->catalog->tableMap, tableName);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
  WriteUnLock(&(db->catalog->tableMapLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");
  if (tableExist || strcmp(tableName, "master") == 0 || strcmp(tableName, "schema") == 0) {
    return GNCDB_TABLE_EXIST;
  }

  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }
  va_start(ap, columnNum);
  primaryKeyIndex = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (primaryKeyIndex == NULL) {
    transactionRollback(tx, db);
    return GNCDB_MEM;
  }
  primaryKeyOffset = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (primaryKeyOffset == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    return GNCDB_MEM;
  }
  primaryKeyVarcharLen = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (primaryKeyVarcharLen == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    return GNCDB_MEM;
  }
  primaryKeyType = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (primaryKeyType == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    return GNCDB_MEM;
  }

  columnList = varArrayListCreate(DISORDER, BYTES_POINTER, columnNum + 3, NULL, arrayColumnDestroy);
  if (columnList == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    return GNCDB_MEM;
  }

  /* 所有参数的正确性检查 */
  fieldMap = hashMapCreate(STRKEY, columnNum, NULL);
  if (fieldMap == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }
  offset = GET_BITMAP_LENGTH(columnNum + 3);
  for (i = 0; i < columnNum; i++) {
    fieldName    = va_arg(ap, char *);
    fieldType    = va_arg(ap, int);
    canBeNull    = va_arg(ap, int);
    isPrimaryKey = va_arg(ap, int);

    if (!validString(fieldName)) {
      transactionRollback(tx, db);
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&primaryKeyType);
      hashMapDestroy(&fieldMap);
      // for (j = 0; j < i; ++j)
      // {
      // 	columnP = varArrayListGetPointer(columnList, j);
      // 	columnDestroy(columnP);
      // }
      varArrayListDestroy(&columnList);

      return GNCDB_PARAM_INVALID;
    }

    /* 通过hash表判断是否出现重复的属性名 */
    if (hashMapExists(fieldMap, fieldName)) {
      transactionRollback(tx, db);
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&primaryKeyType);
      hashMapDestroy(&fieldMap);
      // for (j = 0; j < i; ++j)
      // {
      // 	columnP = varArrayListGetPointer(columnList, j);
      // 	columnDestroy(columnP);
      // }
      varArrayListDestroy(&columnList);
      return GNCDB_PARAM_INVALID;
    } else {
      hashMapPut(fieldMap, fieldName, &fieldType);
    }

    if (isPrimaryKey == 1) {
      if (canBeNull == 1) {
        transactionRollback(tx, db);
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return GNCDB_PRIMARYKEY_NOTNULL;
      }

      rc = varArrayListAdd(primaryKeyIndex, &i);
      if (rc != GNCDB_SUCCESS) {
        transactionRollback(tx, db);
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return rc;
      }

      rc = varArrayListAdd(primaryKeyType, &fieldType);
      if (rc != GNCDB_SUCCESS) {
        transactionRollback(tx, db);
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return rc;
      }
    }

    minValue = va_arg(ap, double);
    maxValue = va_arg(ap, double);
    if (maxValue < minValue) {
      transactionRollback(tx, db);
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&primaryKeyType);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return GNCDB_PARAM_INVALID;
    }

    if (fieldType == FIELDTYPE_VARCHAR) {
      maxValue += 1;  // varchar的长度需要加上'\0'字符
    }

    constraint = columnConstraintConstruct(minValue, maxValue, canBeNull, isPrimaryKey);
    if (constraint == NULL) {
      transactionRollback(tx, db);
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&primaryKeyType);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return GNCDB_MEM;
    }

    column = columnConstruct(fieldType, fieldName, constraint, offset);
    if (column == NULL) {
      transactionRollback(tx, db);
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&primaryKeyType);
      columnConstraintDestroy(constraint);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return GNCDB_MEM;
    }
    column->tableName = my_malloc0(strlen(tableName) + 1);
    strcpy(column->tableName, tableName);

    rc = varArrayListAddPointer(columnList, column);
    if (rc != GNCDB_SUCCESS) {
      transactionRollback(tx, db);
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&primaryKeyType);
      columnDestroy(column);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return rc;
    }
    switch (fieldType) {
      case FIELDTYPE_INTEGER:
        if (isPrimaryKey == 1) {
          rc = varArrayListAdd(primaryKeyOffset, &offset);
          if (rc != GNCDB_SUCCESS) {
            transactionRollback(tx, db);
            varArrayListDestroy(&primaryKeyIndex);
            varArrayListDestroy(&primaryKeyOffset);
            varArrayListDestroy(&primaryKeyVarcharLen);
            varArrayListDestroy(&primaryKeyType);
            varArrayListDestroy(&columnList);
            hashMapDestroy(&fieldMap);
            return rc;
          }
        }
        offset += INT_SIZE;
        break;
      case FIELDTYPE_REAL:
        if (isPrimaryKey == 1) {
          rc = varArrayListAdd(primaryKeyOffset, &offset);
          if (rc != GNCDB_SUCCESS) {
            transactionRollback(tx, db);
            varArrayListDestroy(&primaryKeyIndex);
            varArrayListDestroy(&primaryKeyOffset);
            varArrayListDestroy(&primaryKeyVarcharLen);
            varArrayListDestroy(&primaryKeyType);
            varArrayListDestroy(&columnList);
            hashMapDestroy(&fieldMap);
            return rc;
          }
        }
        offset += DOUBLE_SIZE;
        break;
      case FIELDTYPE_VARCHAR:
        if (isPrimaryKey == 1) {
          rc = varArrayListAdd(primaryKeyOffset, &offset);
          if (rc != GNCDB_SUCCESS) {
            transactionRollback(tx, db);
            varArrayListDestroy(&primaryKeyIndex);
            varArrayListDestroy(&primaryKeyOffset);
            varArrayListDestroy(&primaryKeyVarcharLen);
            varArrayListDestroy(&primaryKeyType);
            varArrayListDestroy(&columnList);
            hashMapDestroy(&fieldMap);
            return rc;
          }
          len = column->columnConstraint->maxValue;
          rc  = varArrayListAdd(primaryKeyVarcharLen, &len);
          if (rc != GNCDB_SUCCESS) {
            transactionRollback(tx, db);
            varArrayListDestroy(&primaryKeyIndex);
            varArrayListDestroy(&primaryKeyOffset);
            varArrayListDestroy(&primaryKeyVarcharLen);
            varArrayListDestroy(&primaryKeyType);
            varArrayListDestroy(&columnList);
            hashMapDestroy(&fieldMap);
            return rc;
          }
        }
        offset += column->columnConstraint->maxValue;
        break;
      case FIELDTYPE_BLOB:
        if (isPrimaryKey == 1) {
          rc = varArrayListAdd(primaryKeyOffset, &offset);
          if (rc != GNCDB_SUCCESS) {
            transactionRollback(tx, db);
            varArrayListDestroy(&primaryKeyIndex);
            varArrayListDestroy(&primaryKeyOffset);
            varArrayListDestroy(&primaryKeyVarcharLen);
            varArrayListDestroy(&primaryKeyType);
            varArrayListDestroy(&columnList);
            hashMapDestroy(&fieldMap);
            return rc;
          }
        }
        offset += 2 * INT_SIZE;
        break;
      default: break;
    }
  }
  hashMapDestroy(&fieldMap);

  maxrownum = va_arg(ap, int);
  if (maxrownum <= 0) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return GNCDB_PARAM_INVALID;
  }

  rowIdPrimaryKey = 0;
  if (primaryKeyIndex->elementCount < 1) {
    rowIdPrimaryKey = 1;
    rc              = varArrayListAdd(primaryKeyIndex, &columnNum);
    rc              = varArrayListAdd(primaryKeyType, &rowIdPrimaryType);
    rc              = varArrayListAdd(primaryKeyOffset, &offset);
  }

  constraint1 = columnConstraintConstruct(0, maxrownum, 0, rowIdPrimaryKey);
  if (constraint1 == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  column1            = columnConstruct(FIELDTYPE_INTEGER, "rowId", constraint1, offset);
  column1->tableName = my_malloc0(strlen(tableName) + 1);
  strcpy(column1->tableName, tableName);
  offset += INT_SIZE;
  if (column1 == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    columnConstraintDestroy(constraint1);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  rc = varArrayListAddPointer(columnList, column1);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    columnDestroy(column1);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return rc;
  }

  constraint2 = columnConstraintConstruct(INT_MIN, INT_MAX, 0, 0);
  if (constraint2 == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  column2            = columnConstruct(FIELDTYPE_INTEGER, "createTime", constraint2, offset);
  column2->tableName = my_malloc0(strlen(tableName) + 1);
  strcpy(column2->tableName, tableName);
  offset += INT_SIZE;
  if (column2 == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    columnConstraintDestroy(constraint2);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  rc = varArrayListAddPointer(columnList, column2);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    columnDestroy(column2);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return rc;
  }

  constraint3 = columnConstraintConstruct(INT_MIN, INT_MAX, 0, 0);
  if (constraint3 == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  column3            = columnConstruct(FIELDTYPE_INTEGER, "updateTime", constraint3, offset);
  column3->tableName = my_malloc0(strlen(tableName) + 1);
  strcpy(column3->tableName, tableName);
  offset += INT_SIZE;
  if (column3 == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    columnConstraintDestroy(constraint3);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  rc = varArrayListAddPointer(columnList, column3);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    columnDestroy(column3);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return rc;
  }

  tableSchema = tableSchemaConstruct(maxrownum, columnNum + 3, columnList);
  if (tableSchema == NULL) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  rc = executorCreateTable(
      db, tableName, tableSchema, primaryKeyIndex, primaryKeyType, primaryKeyOffset, primaryKeyVarcharLen, tx);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&primaryKeyType);
    tableSchemaDestroy(tableSchema);
    return rc;
  }
  rc = transactionCommit(tx, db);
  return GNCDB_SUCCESS;
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <returns></returns>
int GNCDB_dropTable(GNCDB *db, char *tableName)
{
  int          rc = 0;
  Transaction *tx = NULL;
  if (strcmp(tableName, "master") == 0 || strcmp(tableName, "schema") == 0) {
    return GNCDB_SYSTEMTABLE_NOTREMOVE;
  }
  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }
  if (catalogIsTableExist(db->catalog, tableName)) {
    transactionRollback(tx, db);
    return GNCDB_TABLE_NOT_FOUND;
  }
  /*rc = transcationStart(tx, db);
  if (rc != GNCDB_SUCCESS)
  {
      transactionRollback(tx, db);;
      return rc;
  }*/
  rc = executorDropeTable(db, tableName, tx);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    return rc;
  } else {
    rc = transactionCommit(tx, db);
  }
  return GNCDB_SUCCESS;
}

/**
 * @brief  检查字段是否合法
 * @param  fieldExpr:
 * @param  fieldName:
 * @param  db:
 * @param  tableArray:
 * @return int:
 */
int fieldCheck(FieldExpr *fieldExpr, char *fieldName, GNCDB *db, varArrayList *tableArray)
{
  int          i             = 0;
  int          j             = 0;
  char        *tableName     = NULL;
  TableSchema *tableSchema   = NULL;
  Column      *column        = NULL;
  const char  *realFieldName = NULL;
  bool         isSingleTable = false;
  int          len           = 0;

  isSingleTable = (tableArray->elementCount == 1);
  for (i = 0; i < tableArray->elementCount; ++i) {
    tableName = varArrayListGetPointer(tableArray, i);
    if (tableName == NULL) {
      return GNCDB_NOT_FOUND;
    }
    tableSchema = getTableSchema(db->catalog, tableName);
    if (tableSchema == NULL) {
      return GNCDB_NOT_FOUND;
    }
    for (j = 0; j < tableSchema->columnList->elementCount; ++j) {
      column        = varArrayListGetPointer(tableSchema->columnList, j);
      realFieldName = extractFieldName(fieldName);
      if (strcmp(column->fieldName, realFieldName) == 0) {
        fieldExpr->tableName = my_strdup(tableName);
        fieldExpr->fieldName = my_strdup(realFieldName);
        if (isSingleTable) {
          fieldExpr->name = my_strdup(realFieldName);
        } else {
          len             = strlen(realFieldName) + strlen(tableName) + 2;
          fieldExpr->name = (char *)my_malloc(len);
          if (fieldExpr->name == NULL) {
            return GNCDB_MEM;
          }
          // strncpy(fieldExpr->name, tableName, strlen(tableName));
          // strncat(fieldExpr->name, ".", 1);
          // strncat(fieldExpr->name, realFieldName, strlen(realFieldName));
          snprintf(fieldExpr->name, len, "%s.%s", tableName, realFieldName);
        }
        return GNCDB_SUCCESS;
      }
    }
  }
  return GNCDB_NOT_FOUND;
}

bool isField(varArrayList *tableArray, GNCDB *db, char *str)
{
  int          i = 0, j = 0;
  char        *tableName   = NULL;
  TableSchema *tableSchema = NULL;
  Column      *column      = NULL;

  for (i = 0; i < tableArray->elementCount; ++i) {
    tableName   = varArrayListGetPointer(tableArray, i);
    tableSchema = getTableSchema(db->catalog, tableName);
    for (j = 0; j < tableSchema->columnList->elementCount; ++j) {
      column = varArrayListGetPointer(tableSchema->columnList, j);
      if (strcmp(column->fieldName, str) == 0) {
        return true;
      }
    }
  }
  return false;
}

int valueCheck(ValueExpr *valueExpr, void *value, GNCDB *db, FieldExpr *fieldExpr)
{
  int          i           = 0;
  TableSchema *tableSchema = NULL;
  Column      *column      = NULL;
  if (valueExpr == NULL || value == NULL || db == NULL || fieldExpr == NULL) {
    return GNCDB_PARAMNULL;
  }
  tableSchema = getTableSchema(db->catalog, fieldExpr->tableName);
  if (tableSchema == NULL) {
    return GNCDB_NOT_FOUND;
  }
  for (i = 0; i < tableSchema->columnList->elementCount; ++i) {
    column = varArrayListGetPointer(tableSchema->columnList, i);
    if (strcmp(fieldExpr->fieldName, column->fieldName) == 0) {
      switch (column->fieldType) {
        case FIELDTYPE_INTEGER: {
          valueSetInt(valueExpr->value, atoi(value));
          return GNCDB_SUCCESS;
        } break;
        case FIELDTYPE_REAL: {
          // valueSetFloat(valueExpr->value, atof(value));
          valueSetDouble(valueExpr->value, atof(value));
          return GNCDB_SUCCESS;
        } break;
        case FIELDTYPE_VARCHAR: {
          valueSetString(valueExpr->value, (char *)value);
          return GNCDB_SUCCESS;
        } break;
        default: return GNCDB_NOT_FOUND;
      }
    }
  }
  return GNCDB_NOT_FOUND;
}

ComparisonExpr *condToComparisonExpr(char *leftStr, char *rightStr, enum CompOp op, GNCDB *db, varArrayList *tableArray)
{
  FieldExpr      *leftFieldExpr  = NULL;
  FieldExpr      *rightFieldExpr = NULL;
  ValueExpr      *rightValueExpr = NULL;
  ComparisonExpr *comparisonExpr = NULL;
  comparisonExpr                 = exprCreate(ETG_COMPARISON);
  if (comparisonExpr == NULL) {
    return NULL;
  }
  /* 1.处理lhs */
  leftFieldExpr = exprCreate(ETG_FIELD);
  if (leftFieldExpr == NULL) {
    // my_free(comparisonExpr);
    exprDestroy((Expression *)comparisonExpr);
    return NULL;
  }
  if (fieldCheck(leftFieldExpr, leftStr, db, tableArray) != GNCDB_SUCCESS) {
    // my_free(comparisonExpr);
    // my_free(leftFieldExpr);
    exprDestroy((Expression *)comparisonExpr);
    exprDestroy((Expression *)leftFieldExpr);
    return NULL;
  }
  comparisonExpr->left = (Expression *)leftFieldExpr;

  /* 2.处理rhs */
  if (checkStringType(rightStr) == 3 && isField(tableArray, db, rightStr)) {
    /* 2.1 rhs是字段 */
    rightFieldExpr = exprCreate(ETG_FIELD);
    if (rightFieldExpr == NULL) {
      // my_free(comparisonExpr);
      // my_free(leftFieldExpr);
      exprDestroy((Expression *)comparisonExpr);
      return NULL;
    }
    if (fieldCheck(rightFieldExpr, rightStr, db, tableArray) != GNCDB_SUCCESS) {
      // my_free(comparisonExpr);
      // my_free(rightFieldExpr);
      // my_free(leftFieldExpr);
      exprDestroy((Expression *)comparisonExpr);
      exprDestroy((Expression *)rightFieldExpr);
      return NULL;
    }
    comparisonExpr->right = (Expression *)rightFieldExpr;
  } else {
    /* 2.2 rhs是常量值 */
    rightValueExpr = exprCreate(ETG_VALUE);
    if (rightValueExpr == NULL) {
      // my_free(comparisonExpr);
      // my_free(leftFieldExpr);
      exprDestroy((Expression *)comparisonExpr);
      return NULL;
    }
    rightValueExpr->value = valueCreate();
    if (rightValueExpr->value == NULL) {
      // my_free(comparisonExpr);
      // my_free(leftFieldExpr);
      // my_free(rightValueExpr);
      exprDestroy((Expression *)comparisonExpr);
      exprDestroy((Expression *)rightValueExpr);
      return NULL;
    }
    if (valueCheck(rightValueExpr, rightStr, db, leftFieldExpr) != GNCDB_SUCCESS) {
      // my_free(comparisonExpr);
      // my_free(leftFieldExpr);
      // my_free(rightValueExpr);
      exprDestroy((Expression *)comparisonExpr);
      exprDestroy((Expression *)rightValueExpr);
      return NULL;
    }
    comparisonExpr->right = (Expression *)rightValueExpr;
  }
  /* 3.处理操作符 */
  comparisonExpr->comp = op;
  return comparisonExpr;
}

int GNCDB_select(
    GNCDB *db, CallBack2 callback, int *affectedRows, void *data, int tableNum, int columnNum, int filterNum, ...)
{
  int               rc            = 0;
  Transaction      *tx            = NULL;
  ParsedSqlNode    *parsedSqlNode = NULL;
  va_list           ap;
  char             *tableName        = NULL;
  varArrayList     *relations        = NULL;
  varArrayList     *projectExprs     = NULL;
  varArrayList     *conditions       = NULL;
  ConjunctionExpr  *condition        = NULL;
  int               i                = 0;
  InnerJoinSqlNode *innerJoinSqlNode = NULL;
  FieldExpr        *fieldExpr        = NULL;
  char             *fieldStr         = NULL;
  char             *filterStr        = NULL;
  char             *leftStr          = NULL;
  char             *rightStr         = NULL;
  enum CompOp       predicate        = 0;
  ComparisonExpr   *comparisonExpr   = NULL;
  SQLStageEvent    *sqlEvent         = NULL;
  varArrayList     *tableArray       = NULL;
  char             *tableStr         = NULL;
  // StringPair       *stringPair       = NULL;

  sqlEvent = SQLStageEventCreate();
  if (sqlEvent == NULL) {
    return GNCDB_MEM;
  }
  tx = transcationConstrcutSelect(db, true);
  if (tx == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }

  sqlEvent->callback = callback;
  sqlEvent->db       = db;
  sqlEvent->txn      = tx;
  sqlEvent->errmsg   = NULL;  // 错误信息如果分配了内存，如何处理？
  sqlEvent->data     = data;

  parsedSqlNode = ParsedSqlNodeCreate(SCF_SELECT);
  if (parsedSqlNode == NULL) {
    transactionRollbackSelect(tx, db, true);
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }
  relations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, InnerJoinSqlNodePointerDestroy);
  if (relations == NULL) {
    transactionRollbackSelect(tx, db, true);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    return GNCDB_MEM;
  }

  projectExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  if (projectExprs == NULL) {
    transactionRollbackSelect(tx, db, true);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&relations);
    return GNCDB_MEM;
  }

  tableArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, tableNameArrayDestroy);
  if (tableArray == NULL) {
    transactionRollbackSelect(tx, db, true);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&relations);
    varArrayListDestroy(&projectExprs);
    return GNCDB_MEM;
  }
  // 1. 处理表
  va_start(ap, filterNum);
  if (tableNum < 1) {
    va_end(ap);
    return GNCDB_PARAMNULL;
  }
  for (i = 0; i < tableNum; i++) {
    innerJoinSqlNode = InnerJoinSqlNodeCreate(StringPairCreate(NULL, NULL),
        varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy),
        varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy));
    if (innerJoinSqlNode == NULL) {
      transactionRollbackSelect(tx, db, true);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&relations);
      varArrayListDestroy(&projectExprs);
      varArrayListDestroy(&tableArray);
      va_end(ap);
      return GNCDB_MEM;
    }
    tableName = va_arg(ap, char *);
    if (tableName == NULL) {
      transactionRollbackSelect(tx, db, true);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&relations);
      varArrayListDestroy(&projectExprs);
      InnerJoinSqlNodeDestroy(innerJoinSqlNode);
      varArrayListDestroy(&tableArray);
      return GNCDB_PARAMNULL;
    }
    innerJoinSqlNode->baseRelation->name  = my_strdup(tableName);
    innerJoinSqlNode->baseRelation->alias = NULL;
    tableStr                              = my_malloc0(strlen(tableName) + 1);
    if (tableStr == NULL) {
      transactionRollbackSelect(tx, db, true);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&relations);
      varArrayListDestroy(&projectExprs);
      InnerJoinSqlNodeDestroy(innerJoinSqlNode);
      varArrayListDestroy(&tableArray);
      return GNCDB_MEM;
    }
    strcpy(tableStr, tableName);
    varArrayListAddPointer(tableArray, tableStr);
    varArrayListAddPointer(relations, innerJoinSqlNode);
  }

  // 2. 处理投影字段
  if (columnNum == 0) {  // select *
    fieldExpr            = exprCreate(ETG_FIELD);
    fieldExpr->tableName = my_strdup("*");
    fieldExpr->fieldName = my_strdup("*");
    if (fieldExpr == NULL) {
      transactionRollbackSelect(tx, db, true);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&relations);
      varArrayListDestroy(&projectExprs);
      varArrayListDestroy(&tableArray);
      return GNCDB_MEM;
    }
    varArrayListAddPointer(projectExprs, fieldExpr);
  } else {  // select column1, column2, ...
    for (i = 0; i < columnNum; i++) {
      fieldStr = va_arg(ap, char *);
      if (fieldStr == NULL) {
        transactionRollbackSelect(tx, db, true);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&relations);
        varArrayListDestroy(&projectExprs);
        varArrayListDestroy(&tableArray);
        return GNCDB_PARAMNULL;
      }
      fieldExpr = exprCreate(ETG_FIELD);
      if (!fieldExpr || fieldCheck(fieldExpr, fieldStr, db, tableArray) != GNCDB_SUCCESS) {
        transactionRollbackSelect(tx, db, true);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&relations);
        varArrayListDestroy(&projectExprs);
        varArrayListDestroy(&tableArray);
        exprDestroy((Expression *)fieldExpr);
        return GNCDB_NOT_FOUND;
      }
      varArrayListAddPointer(projectExprs, fieldExpr);
    }
  }

  // 3. 处理过滤条件
  conditions = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  if (conditions == NULL) {
    transactionRollbackSelect(tx, db, true);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&relations);
    varArrayListDestroy(&projectExprs);
    varArrayListDestroy(&tableArray);
    return GNCDB_MEM;
  }

  condition = exprCreate(ETG_CONJUNCTION);
  if (condition == NULL) {
    transactionRollbackSelect(tx, db, true);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&relations);
    varArrayListDestroy(&projectExprs);
    varArrayListDestroy(&conditions);
    varArrayListDestroy(&tableArray);
    return GNCDB_MEM;
  }
  condition->conjunctionType = CJET_AND;

  if (filterNum > 0) {
    for (i = 0; i < filterNum; i++) {
      filterStr = va_arg(ap, char *);
      if (filterStr == NULL) {
        transactionRollbackSelect(tx, db, true);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&relations);
        varArrayListDestroy(&projectExprs);
        varArrayListDestroy(&conditions);
        varArrayListDestroy(&tableArray);
        exprDestroy((Expression *)condition);
        return GNCDB_PARAMNULL;
      }
      rc = dbInstancePaserFilterStr(db, filterStr, &leftStr, &rightStr, &predicate);
      if (rc != GNCDB_SUCCESS) {
        transactionRollbackSelect(tx, db, true);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&relations);
        varArrayListDestroy(&projectExprs);
        varArrayListDestroy(&conditions);
        varArrayListDestroy(&tableArray);
        exprDestroy((Expression *)condition);
        my_free(leftStr);
        my_free(rightStr);
        return rc;
      }
      comparisonExpr = condToComparisonExpr(leftStr, rightStr, predicate, db, tableArray);
      if (comparisonExpr == NULL) {
        transactionRollbackSelect(tx, db, true);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&relations);
        varArrayListDestroy(&projectExprs);
        varArrayListDestroy(&conditions);
        varArrayListDestroy(&tableArray);
        exprDestroy((Expression *)condition);
        my_free(leftStr);
        my_free(rightStr);
        return GNCDB_CONDITION_INVALID;
      }
      varArrayListAddPointer(conditions, comparisonExpr);
      my_free(leftStr);
      my_free(rightStr);
    }
  }

  condition->children                  = conditions;
  conditions                           = NULL;
  parsedSqlNode->selection->conditions = (Expression *)condition;
  varArrayListReverse(projectExprs);
  parsedSqlNode->selection->projectExprs = projectExprs;
  parsedSqlNode->selection->relations    = relations;
  sqlEvent->sqlNode                      = parsedSqlNode;

  varArrayListDestroy(&tableArray);

  // 上面已经手动完成了查询的解析过程，构造了语法树parsedSqlNode，后面的流程和sql查询的处理保持流程一致
  rc = txnExecuteOrigAPI(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    SQLStageEventDestroy(&sqlEvent);
    transactionRollbackSelect(tx, db, true);
    return rc;
  }

  if (affectedRows != NULL) {
    *affectedRows = sqlEvent->affectedRows;
  }
  // if (sqlEvent->db->lookaside != NULL) {
  //   assert(sqlEvent->db->lookaside->nBFreeSlot + sqlEvent->db->lookaside->nSFreeSlot ==
  //   sqlEvent->db->lookaside->nSlot);
  // }
  // free
  SQLStageEventDestroy(&sqlEvent);
  transactionCommitSelect(tx, db, true);
  return rc;
}

void mapArrayDestroy(HashMap *hashMap)
{
  HashMapIterator *iterator = NULL;
  iterator                  = createHashMapIterator(hashMap);
  if (iterator != NULL) {
    while (hasNextHashMapIterator(iterator)) {
      iterator = nextHashMapIterator(iterator);
      if (iterator == NULL) {
        break;
      }
      if (iterator->entry->value != NULL)
        varArrayListDestroy((varArrayList **)&(iterator->entry->value));
    }
  }
  freeHashMapIterator(&iterator);
  hashMapDestroy(&hashMap);
}

void tableNameArrayDestroy(void *data)
{
  void **pData = data;
  if (pData == NULL || *pData == NULL) {
    return;
  }

  my_free(*pData);
}
/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name=""></param>
/// <returns></returns>
int GNCDB_insert(struct GNCDB *db, int *affectedRows, char *tableName, ...)
{
  int            rc          = 0;
  Transaction   *txn         = NULL;
  SQLStageEvent *sqlEvent    = NULL;
  TableSchema   *tableSchema = NULL;
  va_list        ap;
  ParsedSqlNode *parsedSqlNode = NULL;
  int            i             = 0;
  Column        *column        = NULL;
  int            valueInt      = 0;
  double         valueDouble   = 0;
  //   int blobSize = 0;
  //   int testSize = 0;
  char         *valueStr  = NULL;
  Value        *value     = NULL;
  varArrayList *valueList = NULL;

  if (strcmp(tableName, MASTERNAME) == 0 ||
      strcmp(tableName, SCHEMANAME) == 0)  // 禁止操作master和schema表, 移到语义解析更加合理
  {
    return GNCDB_NOT_REFACTOR;
  }

  sqlEvent = SQLStageEventCreate();
  if (sqlEvent == NULL) {
    return GNCDB_MEM;
  }
  txn = transcationConstrcut(db);
  if (txn == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }

  sqlEvent->db  = db;
  sqlEvent->txn = txn;

  parsedSqlNode = ParsedSqlNodeCreate(SCF_INSERT);

  if (parsedSqlNode == NULL) {
    transactionRollback(txn, db);
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }

  parsedSqlNode->insertion->relationName = my_strdup(tableName);

  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    transactionRollback(txn, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    return GNCDB_TABLE_NOT_FOUND;
  }

  parsedSqlNode->insertion->attributes = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
  if (parsedSqlNode->insertion->attributes == NULL) {
    transactionRollback(txn, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    return GNCDB_MEM;
  }

  parsedSqlNode->insertion->valuelists =
      varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, varArrayListPointerDestroy);
  if (parsedSqlNode->insertion->valuelists == NULL) {
    transactionRollback(txn, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    return GNCDB_MEM;
  }

  valueList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, valuePointerDestroy);
  if (valueList == NULL) {
    transactionRollback(txn, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    return GNCDB_MEM;
  }

  va_start(ap, tableName);
  for (i = 0; i < tableSchema->columnList->elementCount - 3; ++i) {
    column = varArrayListGetPointer(tableSchema->columnList, i);
    if (column == NULL) {
      transactionRollback(txn, db);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&valueList);
      return GNCDB_ARRAY_GET_FALSE;
    }
    varArrayListAddPointer(parsedSqlNode->insertion->attributes, my_strdup(column->fieldName));
    switch (column->fieldType) {
      case FIELDTYPE_INTEGER: {
        valueInt = va_arg(ap, int);
        value    = valueCreate();
        if (value == NULL) {
          transactionRollback(txn, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&valueList);
          return GNCDB_MEM;
        }
        valueSetInt(value, valueInt);
        varArrayListAddPointer(valueList, value);
      } break;
      case FIELDTYPE_REAL: {
        valueDouble = va_arg(ap, double);
        value       = valueCreate();
        if (value == NULL) {
          transactionRollback(txn, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&valueList);
          return GNCDB_MEM;
        }
        valueSetDouble(value, valueDouble);
        varArrayListAddPointer(valueList, value);
      } break;
      case FIELDTYPE_VARCHAR: {
        valueStr = va_arg(ap, char *);
        value    = valueCreate();
        if (value == NULL) {
          transactionRollback(txn, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&valueList);
          return GNCDB_MEM;
        }
        valueSetString(value, valueStr);
        varArrayListAddPointer(valueList, value);
      } break;

      case FIELDTYPE_BLOB: {
        va_arg(ap, int);
        value = valueCreate();
        if (value == NULL) {
          transactionRollback(txn, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&valueList);
          return GNCDB_MEM;
        }
        valueSetBlob(value);  // 暂时不支持blob，先设置为NULL
        varArrayListAddPointer(valueList, value);
      } break;

      case FIELDTYPE_TEXT: {
        valueStr = va_arg(ap, char *);
        value    = valueCreate();
        if (value == NULL) {
          transactionRollback(txn, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&valueList);
          return GNCDB_MEM;
        }
        valueSetNull(value);  // 暂时不支持text，先设置为NULL
        varArrayListAddPointer(valueList, value);
      } break;

      default: {
        transactionRollback(txn, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&valueList);
        return GNCDB_NOT_FOUND;
      }
    }
  }

  varArrayListAddPointer(parsedSqlNode->insertion->valuelists, valueList);
  sqlEvent->sqlNode = parsedSqlNode;

  rc = txnExecuteOrigAPI(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(txn, db);
    SQLStageEventDestroy(&sqlEvent);
    return rc;
  }

  if (affectedRows != NULL) {
    *affectedRows = sqlEvent->affectedRows;
  }
  // free
  transactionCommit(txn, db);
  SQLStageEventDestroy(&sqlEvent);

  return GNCDB_SUCCESS;
}

/// <summary>
///
/// </summary>
/// <param name="tableSchema"></param>
/// <param name="fieldName"></param>
/// <returns></returns>
int checkFieldNameisValid(TableSchema *tableSchema, char *fieldName)
{
  int     i      = 0;
  int     flag   = 0;
  Column *column = NULL;

  for (i = 0; i < tableSchema->columnList->elementCount; i++) {
    column = varArrayListGetPointer(tableSchema->columnList, i);
    if (column == NULL) {
      return GNCDB_NOT_FOUND;
    }
    if (strcmp(fieldName, (column->fieldName)) == 0) {
      flag = 1;
      break;
    }
  }
  if (flag == 0) {
    return GNCDB_PARAM_INVALID;
  } else {
    return GNCDB_SUCCESS;
  }
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name="filterNum"></param>
/// <param name=""></param>
/// <returns></returns>
int GNCDB_delete(struct GNCDB *db, int *affectedRows, char *tableName, int filterNum, ...)
{

  int              rc            = 0;
  Transaction     *tx            = NULL;
  ParsedSqlNode   *parsedSqlNode = NULL;
  SQLStageEvent   *sqlEvent      = NULL;
  va_list          ap;
  int              i              = 0;
  ComparisonExpr  *comparisonExpr = NULL;
  char            *filterStr      = NULL;
  char            *leftStr        = NULL;
  char            *rightStr       = NULL;
  enum CompOp      predicate      = 0;
  varArrayList    *tableArray     = NULL;
  varArrayList    *conditions     = NULL;
  ConjunctionExpr *condition      = NULL;
  char            *tableStr       = NULL;

  if (!hashMapExists(db->catalog->tableMap, tableName)) {
    return GNCDB_TABLE_NOT_FOUND;
  }

  sqlEvent = SQLStageEventCreate();
  if (sqlEvent == NULL) {
    return GNCDB_MEM;
  }

  tx = transcationConstrcut(db);
  if (tx == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }

  sqlEvent->db  = db;
  sqlEvent->txn = tx;

  parsedSqlNode = ParsedSqlNodeCreate(SCF_DELETE);
  if (parsedSqlNode == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }

  parsedSqlNode->deletion->relationName = my_malloc(strlen(tableName) + 1);
  if (parsedSqlNode->deletion->relationName == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }
  strcpy(parsedSqlNode->deletion->relationName, tableName);
  parsedSqlNode->deletion->relationName[strlen(tableName)] = '\0';

  tableArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, tableNameArrayDestroy);
  if (tableArray == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    return GNCDB_MEM;
  }
  tableStr = my_malloc0(strlen(tableName) + 1);
  if (tableStr == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&tableArray);
    return GNCDB_MEM;
  }
  strcpy(tableStr, tableName);
  varArrayListAddPointer(tableArray, tableStr);

  va_start(ap, filterNum);

  if (filterNum > 0) {
    conditions = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
    if (conditions == NULL) {
      transactionRollback(tx, db);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&tableArray);
      my_free(parsedSqlNode->deletion->relationName);
      return GNCDB_MEM;
    }
    for (i = 0; i < filterNum; i++) {
      filterStr = va_arg(ap, char *);
      if (filterStr == NULL) {
        transactionRollback(tx, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&tableArray);
        varArrayListDestroy(&conditions);
        return GNCDB_PARAMNULL;
      }

      rc = dbInstancePaserFilterStr(db, filterStr, &leftStr, &rightStr, &predicate);
      if (rc != GNCDB_SUCCESS) {
        transactionRollback(tx, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&tableArray);
        varArrayListDestroy(&conditions);
        my_free(leftStr);
        my_free(rightStr);
        return rc;
      }

      comparisonExpr = condToComparisonExpr(leftStr, rightStr, predicate, db, tableArray);
      if (comparisonExpr == NULL) {
        transactionRollback(tx, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&tableArray);
        varArrayListDestroy(&conditions);
        my_free(leftStr);
        my_free(rightStr);
        return GNCDB_FIELD_NOT_EXIST;
      }
      varArrayListAddPointer(conditions, (Expression *)comparisonExpr);
      my_free(leftStr);
      my_free(rightStr);
    }

    condition = exprCreate(ETG_CONJUNCTION);
    if (condition == NULL) {
      transactionRollback(tx, db);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&tableArray);
      varArrayListDestroy(&conditions);
      return GNCDB_MEM;
    }
    condition->conjunctionType          = CJET_AND;
    condition->children                 = conditions;
    conditions                          = NULL;
    parsedSqlNode->deletion->conditions = (Expression *)condition;
    condition                           = NULL;
  }

  sqlEvent->sqlNode = parsedSqlNode;
  rc                = txnExecuteOrigAPI(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    varArrayListDestroy(&tableArray);
    varArrayListDestroy(&conditions);
    exprDestroy((Expression *)condition);
    return rc;
  }

  if (affectedRows != NULL) {
    *affectedRows = sqlEvent->affectedRows;
  }
  // free
  transactionCommit(tx, db);
  SQLStageEventDestroy(&sqlEvent);
  varArrayListDestroy(&tableArray);
  return GNCDB_SUCCESS;
}

/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name="setNum"></param>
/// <param name="filterNum"></param>
/// <param name=""></param>
/// <returns></returns>
int GNCDB_update(struct GNCDB *db, int *affectedRows, char *tableName, int setNum, int filterNum, ...)
{
  int          rc = 0;
  Transaction *tx = NULL;
  va_list      ap;

  ParsedSqlNode *parsedSqlNode = NULL;
  SQLStageEvent *sqlEvent      = NULL;

  int              i              = 0;
  ComparisonExpr  *comparisonExpr = NULL;
  char            *filterStr      = NULL;
  char            *leftStr        = NULL;
  char            *rightStr       = NULL;
  enum CompOp      predicate      = 0;
  varArrayList    *tableArray     = NULL;
  varArrayList    *conditions     = NULL;
  ConjunctionExpr *condition      = NULL;
  TableSchema     *tableSchema    = NULL;
  int              fieldIndex     = 0;
  Column          *column         = NULL;
  int              valueInt       = 0;
  double           valueDouble    = 0;
  char            *valueStr       = NULL;
  Value           *value          = NULL;
  ValueExpr       *valueExpr      = NULL;
  char            *tableStr       = NULL;

  sqlEvent = SQLStageEventCreate();
  if (sqlEvent == NULL) {
    return GNCDB_MEM;
  }

  tx = transcationConstrcut(db);
  if (tx == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }

  sqlEvent->db  = db;
  sqlEvent->txn = tx;

  parsedSqlNode = ParsedSqlNodeCreate(SCF_UPDATE);
  if (parsedSqlNode == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }

  parsedSqlNode->update->relationName = my_strdup(tableName);

  tableArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (tableArray == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    return GNCDB_MEM;
  }

  tableStr = my_malloc0(strlen(tableName) + 1);
  if (tableStr == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&tableArray);
    my_free(tableStr);
    return GNCDB_MEM;
  }
  strcpy(tableStr, tableName);
  varArrayListAddPointer(tableArray, tableStr);

  parsedSqlNode->update->attributeNames = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
  if (parsedSqlNode->update->attributeNames == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&tableArray);
    my_free(tableStr);
    varArrayListDestroy(&conditions);
    return GNCDB_MEM;
  }
  parsedSqlNode->update->values = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  if (parsedSqlNode->update->values == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&tableArray);
    my_free(tableStr);
    varArrayListDestroy(&conditions);
    return GNCDB_MEM;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    ParsedSqlNodeDestroy(parsedSqlNode);
    varArrayListDestroy(&tableArray);
    my_free(tableStr);
    varArrayListDestroy(&conditions);
    return GNCDB_TABLE_NOT_FOUND;
  }
  va_start(ap, filterNum);
  for (i = 0; i < setNum; i++) {
    leftStr = va_arg(ap, char *);
    varArrayListAddPointer(parsedSqlNode->update->attributeNames, my_strdup(leftStr));
    fieldIndex = columnFindFieldGet(tableSchema->columnList, leftStr);
    if (fieldIndex < 0) {
      transactionRollback(tx, db);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&tableArray);
      my_free(tableStr);
      varArrayListDestroy(&conditions);
      return GNCDB_NOT_FOUND;
    }
    column = varArrayListGetPointer(tableSchema->columnList, fieldIndex);
    if (column == NULL) {
      transactionRollback(tx, db);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&tableArray);
      my_free(tableStr);
      varArrayListDestroy(&conditions);
      return GNCDB_NOT_FOUND;
    }

    switch (column->fieldType) {
      case FIELDTYPE_INTEGER: {
        valueInt = va_arg(ap, int);
        value    = valueCreate();
        if (value == NULL) {
          transactionRollback(tx, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&tableArray);
          my_free(tableStr);
          varArrayListDestroy(&conditions);
          return GNCDB_MEM;
        }
        valueSetInt(value, valueInt);
        valueExpr = exprCreate(ETG_VALUE);
        if (valueExpr == NULL) {
          transactionRollback(tx, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&tableArray);
          my_free(tableStr);
          varArrayListDestroy(&conditions);
          valueDestroy(&value);
          return GNCDB_MEM;
        }
        valueExpr->value = value;
        varArrayListAddPointer(parsedSqlNode->update->values, valueExpr);
      } break;
      case FIELDTYPE_REAL: {
        valueDouble = va_arg(ap, double);
        value       = valueCreate();
        if (value == NULL) {
          transactionRollback(tx, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&tableArray);
          my_free(tableStr);
          varArrayListDestroy(&conditions);
          return GNCDB_MEM;
        }
        valueSetDouble(value, valueDouble);
        valueExpr = exprCreate(ETG_VALUE);
        if (valueExpr == NULL) {
          transactionRollback(tx, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&tableArray);
          my_free(tableStr);
          varArrayListDestroy(&conditions);
          valueDestroy(&value);
          return GNCDB_MEM;
        }
        valueExpr->value = value;
        varArrayListAddPointer(parsedSqlNode->update->values, valueExpr);
      } break;
      case FIELDTYPE_VARCHAR: {
        valueStr = va_arg(ap, char *);
        value    = valueCreate();
        if (value == NULL) {
          transactionRollback(tx, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&tableArray);
          my_free(tableStr);
          varArrayListDestroy(&conditions);
          return GNCDB_MEM;
        }
        valueSetString(value, valueStr);
        valueExpr = exprCreate(ETG_VALUE);
        if (valueExpr == NULL) {
          transactionRollback(tx, db);
          SQLStageEventDestroy(&sqlEvent);
          ParsedSqlNodeDestroy(parsedSqlNode);
          varArrayListDestroy(&tableArray);
          my_free(tableStr);
          varArrayListDestroy(&conditions);
          valueDestroy(&value);
          return GNCDB_MEM;
        }
        valueExpr->value = value;
        varArrayListAddPointer(parsedSqlNode->update->values, valueExpr);
      } break;
      default: {
        transactionRollback(tx, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&tableArray);
        my_free(tableStr);
        varArrayListDestroy(&conditions);
        return GNCDB_INTERNAL;
      }
    }
  }

  if (filterNum > 0) {
    conditions = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
    if (conditions == NULL) {
      transactionRollback(tx, db);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&tableArray);
      my_free(tableStr);
      return GNCDB_MEM;
    }
    for (i = 0; i < filterNum; i++) {
      filterStr = va_arg(ap, char *);
      if (filterStr == NULL) {
        transactionRollback(tx, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&tableArray);
        my_free(tableStr);
        varArrayListDestroy(&conditions);
        return GNCDB_PARAMNULL;
      }

      rc = dbInstancePaserFilterStr(db, filterStr, &leftStr, &rightStr, &predicate);
      if (rc != GNCDB_SUCCESS) {
        transactionRollback(tx, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&tableArray);
        my_free(tableStr);
        varArrayListDestroy(&conditions);
        my_free(leftStr);
        my_free(rightStr);
        return rc;
      }

      comparisonExpr = condToComparisonExpr(leftStr, rightStr, predicate, db, tableArray);
      if (comparisonExpr == NULL) {
        transactionRollback(tx, db);
        SQLStageEventDestroy(&sqlEvent);
        ParsedSqlNodeDestroy(parsedSqlNode);
        varArrayListDestroy(&tableArray);
        my_free(tableStr);
        varArrayListDestroy(&conditions);
        my_free(leftStr);
        my_free(rightStr);
        return GNCDB_CONDITION_INVALID;
      }
      varArrayListAddPointer(conditions, comparisonExpr);
      my_free(leftStr);
      my_free(rightStr);
    }

    condition = exprCreate(ETG_CONJUNCTION);
    if (condition == NULL) {
      transactionRollback(tx, db);
      SQLStageEventDestroy(&sqlEvent);
      ParsedSqlNodeDestroy(parsedSqlNode);
      varArrayListDestroy(&tableArray);
      my_free(tableStr);
      varArrayListDestroy(&conditions);
      return GNCDB_MEM;
    }
    condition->conjunctionType        = CJET_AND;
    condition->children               = conditions;
    conditions                        = NULL;
    parsedSqlNode->update->conditions = (Expression *)condition;
    condition                         = NULL;
  }

  sqlEvent->sqlNode = parsedSqlNode;
  rc                = txnExecuteOrigAPI(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    SQLStageEventDestroy(&sqlEvent);
    varArrayListDestroy(&tableArray);
    my_free(tableStr);
    varArrayListDestroy(&conditions);
    exprDestroy((Expression *)condition);
    return rc;
  }

  if (affectedRows != NULL) {
    *affectedRows = sqlEvent->affectedRows;
  }

  // free
  transactionCommit(tx, db);
  SQLStageEventDestroy(&sqlEvent);
  varArrayListDestroy(&tableArray);
  my_free(tableStr);
  return GNCDB_SUCCESS;
}

/// <summary>
/// 接收用户传进来关于设置Blob的参数
/// </summary>
/// <param name="db"></param>
/// <param name="tableName">表名</param>
/// <param name="columnNum">列号</param>
/// <param name="buf">存放Blob数据的缓存区</param>
/// <param name="size">Blob的数据大小</param>
/// <param name="keyValueNum">keyValue的个数</param>
/// /// <param name=""></param>
/// <returns></returns>
int GNCDB_setBlob(struct GNCDB *db, char *tableName, int columnNum, BYTE *buf, int size, int keyValueNum, ...)
{
  int           rc = 0, trc = 0;
  Transaction  *tx            = NULL;
  varArrayList *keyValueArray = NULL;
  va_list       ap;
  int           i        = 0;
  char         *value    = NULL;
  char         *keyValue = NULL;

  if (db == NULL || tableName == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 创建事务 */
  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }

  keyValueArray = varArrayListCreate(DISORDER, sizeof(char *), 0, NULL, keyValueDestroy);
  if (keyValueArray == NULL) {
    transactionRollback(tx, db);
    return GNCDB_MEM;
  }

  /* 读取参数列表 */
  va_start(ap, keyValueNum);
  for (i = 0; i < keyValueNum; i++) {
    value    = va_arg(ap, char *);
    keyValue = my_malloc(strlen(value) + 1);
    memcpy(keyValue, value, strlen(value) + 1);
    rc = varArrayListAddPointer(keyValueArray, keyValue);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&keyValueArray);
      return rc;
    }
  }

  rc = parameterCheckAndTransformOfBlob(db, tableName, columnNum, keyValueNum, keyValueArray);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&keyValueArray);
    transactionRollback(tx, db);
    return rc;
  }

  rc = executorSetBlob(db, tableName, columnNum, keyValueArray, buf, size, tx);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&keyValueArray);
    transactionRollback(tx, db);
    return rc;
  } else {
    trc = transactionCommit(tx, db);
    if (trc != GNCDB_SUCCESS) {
      varArrayListDestroy(&keyValueArray);
      return trc;
    }
  }
  varArrayListDestroy(&keyValueArray);
  return GNCDB_SUCCESS;
}

/// <summary>
/// 获取Blob数据
/// </summary>
/// <param name="db"></param>
/// <param name="tableName">表名</param>
/// <param name="columnNum">列号</param>
/// <param name="buf">Blob数据</param>
/// <param name="size">Blob数据的大小</param>
/// <param name="keyValueNum">keyValue的个数</param>
/// <param name=""></param>
/// <returns></returns>
int GNCDB_getBlob(struct GNCDB *db, char *tableName, int columnNum, BYTE *buf, int size, int keyValueNum, ...)
{
  int           rc            = 0;
  Transaction  *tx            = NULL;
  varArrayList *keyValueArray = NULL;
  va_list       ap;
  int           i        = 0;
  char         *value    = NULL;
  char         *keyValue = NULL;

  if (db == NULL || tableName == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 创建事务 */
  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }

  keyValueArray = varArrayListCreate(DISORDER, sizeof(char *), 0, NULL, keyValueDestroy);
  if (keyValueArray == NULL) {
    transactionRollback(tx, db);
    return GNCDB_MEM;
  }

  /* 读取参数列表 */
  va_start(ap, keyValueNum);
  for (i = 0; i < keyValueNum; i++) {
    value    = va_arg(ap, char *);
    keyValue = my_malloc(strlen(value) + 1);
    memcpy(keyValue, value, strlen(value) + 1);
    rc = varArrayListAddPointer(keyValueArray, keyValue);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&keyValueArray);
      return rc;
    }
  }
  rc = parameterCheckAndTransformOfBlob(db, tableName, columnNum, keyValueNum, keyValueArray);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&keyValueArray);
    transactionRollback(tx, db);
    return rc;
  }

  rc = executorGetBlob(db, tableName, columnNum, keyValueArray, buf, size, tx);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&keyValueArray);
    transactionRollback(tx, db);
    return rc;
  } else {
    transactionCommit(tx, db);
  }
  varArrayListDestroy(&keyValueArray);
  return GNCDB_SUCCESS;
}

/// <summary>
/// 获取Blob数据
/// </summary>
/// <param name="db"></param>
/// <param name="tableName">表名</param>
/// <param name="columnNum">列号</param>
/// <param name="buf">Blob数据</param>
/// <param name="size">Blob数据的大小</param>
/// <param name="keyValueNum">keyValue的个数</param>
/// <param name=""></param>
/// <returns></returns>
int GNCDB_deleteBlob(struct GNCDB *db, char *tableName, int columnNum, int keyValueNum, ...)
{
  int           rc            = 0;
  Transaction  *tx            = NULL;
  varArrayList *keyValueArray = NULL;
  va_list       ap;
  int           i        = 0;
  char         *value    = NULL;
  char         *keyValue = NULL;

  if (db == NULL || tableName == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 创建事务 */
  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }

  keyValueArray = varArrayListCreate(DISORDER, sizeof(char *), 0, NULL, keyValueDestroy);
  if (keyValueArray == NULL) {
    transactionRollback(tx, db);
    return GNCDB_MEM;
  }

  /* 读取参数列表 */
  va_start(ap, keyValueNum);
  for (i = 0; i < keyValueNum; i++) {
    value    = va_arg(ap, char *);
    keyValue = my_malloc(strlen(value) + 1);
    memcpy(keyValue, value, strlen(value) + 1);
    rc = varArrayListAddPointer(keyValueArray, keyValue);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&keyValueArray);
      return rc;
    }
  }

  rc = parameterCheckAndTransformOfBlob(db, tableName, columnNum, keyValueNum, keyValueArray);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&keyValueArray);
    transactionRollback(tx, db);
    return rc;
  }

  rc = executorDeleteBlob(db, tableName, columnNum, keyValueArray, tx);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    varArrayListDestroy(&keyValueArray);
    return rc;
  } else {
    transactionCommit(tx, db);
  }
  varArrayListDestroy(&keyValueArray);
  return rc;
}

int GNCDB_setTableParam(struct GNCDB *db, char *tableName)
{

  int rc = 0;
  /* 创建事务 */
  Transaction *tx          = NULL;
  TableSchema *tableSchema = NULL;
  BtreeTable  *btreeTable  = NULL;

  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }
  rc = catalogGetTable(db->catalog, &btreeTable, tableName);
  if (rc) {
    transactionRollback(tx, db);
    return GNCDB_TABLE_NOT_FOUND;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    transactionRollback(tx, db);
    return GNCDB_TABLESCHEMA_NOT_FOUND;
  }

  rc = executorSetTableParam(db, tableName, tableSchema, tx);
  if (rc) {
    transactionRollback(tx, db);
    return rc;
  } else {
    transactionCommit(tx, db);
  }
  return GNCDB_SUCCESS;
}
/// <summary>
///
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
int GNCDB_close(struct GNCDB **db)
{
  int rc = 0;
  if ((*db) == NULL || db == NULL) {
    return GNCDB_WAS_CLOSED;
  }
  if ((*db)->dbFile == NULL) {
    return GNCDB_WAS_CLOSED;
  }
  rc = dbInstanceFillInFileHeader((*db));
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  fseek((*db)->dbFile, -1, SEEK_SET);
  rc = pagePoolFlushAllDirtyPages((*db));
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  fclose((*db)->dbFile);
  (*db)->dbFile = NULL;
// TODO
#ifdef ENABLE_LOOKASIDE_MEM_POOL
  destroyLookaside(*db);
#endif
  ReadWriteLockDestroy(&((*db)->transactionManager->logger->latch));
  txManagerDestroy(&((*db)->transactionManager));
  catalogDestroy((*db)->catalog);
  pagePoolDestroy((*db)->pagePool);
  DBLoggerDestory(&((*db)->dbLog));
  ReadWriteLockDestroy(&((*db)->latch));
  my_free((*db)->fileName);
  my_free((*db));
  (*db) = NULL;
  return GNCDB_SUCCESS;
}

void fieldGetMinMaxValue(int len, FieldType type, double *minValue, double *maxValue)
{
  // TODO: 如何确定最大值和最小值
  switch (type) {
    case FIELDTYPE_DATE:
      *minValue = 0;
      *maxValue = INT_MAX;
      break;
    case FIELDTYPE_INTEGER:
      *minValue = INT_MIN;
      *maxValue = INT_MAX;
      break;
    case FIELDTYPE_REAL:
      *minValue = -DBL_MAX;
      *maxValue = DBL_MAX;
      break;
    case FIELDTYPE_VARCHAR:
      *minValue = 0;
      *maxValue = len;
      break;
    case FIELDTYPE_BLOB:
      // 自定义？
      *minValue = 0;
      *maxValue = 255;
      break;
    case FIELDTYPE_DATETIME:
      *minValue = 0;
      *maxValue = INT_MAX;
      break;
    case FIELDTYPE_TEXT:
      *minValue = 0;
      /* 最大65535字节 */
      *maxValue = 65535;
      break;
    default:
      *minValue = INT_MIN;
      *maxValue = INT_MAX;
      break;
  }
}

int GNCDB_exec(GNCDB *db, const char *sql, CallBack2 callback, void *data, char **errmsg)
{
  int            rc       = GNCDB_SUCCESS;
  SQLStageEvent *sqlEvent = NULL;
  Transaction   *txn      = NULL;

  sqlEvent = SQLStageEventCreate();
  if (sqlEvent == NULL) {
    return GNCDB_MEM;
  }
  sqlEvent->callback = callback;
  sqlEvent->db       = db;
  sqlEvent->errmsg   = errmsg;
  sqlEvent->data     = data;

  // PRINT("sql = %s\n", sql);

  rc  = txnExecuteSQL(sqlEvent, sql);
  txn = sqlEvent->txn;
  if (rc != GNCDB_SUCCESS) {
    if (txn != NULL) {
      transactionRollbackSelect(txn, db, sqlEvent->sqlNode->flag == SCF_SELECT);
    }
    SQLStageEventDestroy(&sqlEvent);
    return rc;
  }
  sqlEvent->res = NULL;
  // if (sqlEvent->db->lookaside != NULL) {
  //   assert(sqlEvent->db->lookaside->nBFreeSlot + sqlEvent->db->lookaside->nSFreeSlot ==
  //   sqlEvent->db->lookaside->nSlot);
  // }
  transactionCommitSelect(txn, db, sqlEvent->sqlNode->flag == SCF_SELECT);
  SQLStageEventDestroy(&sqlEvent);
  return rc;
}

int txnExecuteSQLStep(SQLStageEvent *sqlEvent, const char *sql)
{
  int rc                 = GNCDB_SUCCESS;
  sqlEvent->sql          = sql;
  sqlEvent->affectedRows = 0;
  sqlEvent->isStep       = true;

  if (sqlEvent->plan != NULL) {
    rc = ExecuteStageHandleRequest(sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      if (rc == GNCDB_NEXT_EOF) {
        // printf("step execute finished\n");
        return GNCDB_SUCCESS;
      } else {
        // printf("ExecuteStageHandleRequest failed, rc = %d\n", rc);
        return rc;
      }
    }
    return rc;
  } else {
    rc          = ParseStageHandleRequest(sqlEvent);
    printHeader = true;
    if (rc != GNCDB_SUCCESS) {
      //   printf("ParseStageHandleRequest failed, rc = %d\n", rc);
      return rc;
    }
    rc = ResolveStageHandleRequest(sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      //   printf("ResolveStageHandleRequest failed, rc = %d\n", rc);
      return rc;
    }
    rc = OptimizeStageHandleRequest(sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      //   printf("OptimizeStageHandleRequest failed, rc = %d\n", rc);
      return rc;
    }
    rc = ExecuteStageHandleRequest(sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      if (rc == GNCDB_NEXT_EOF) {
        // printf("step execute finished\n");
        return GNCDB_SUCCESS;
      } else {
        // printf("ExecuteStageHandleRequest failed, rc = %d\n", rc);
        return rc;
      }
    }
    return rc;
  }
}

// static int testESFWriteCallback(void *data, int argc, char **azColName, char **argv)
// {
//   FILE *fp = (FILE *)data;
//   int   i;

//   if (fp == NULL) {
//     fprintf(stderr, "Error: File pointer is NULL in callback.\n");
//     return -1;
//   }
//   if (argc == 0) {
//     return 0;
//   }

//   for (i = 0; i < argc; i++) {
//     fprintf(fp, "%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
//   }
//   fprintf(fp, "\n");
//   for (i = 0; i < argc; i++) {
//     fprintf(fp, "%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");
//   }
//   fprintf(fp, "\n");
//   return 0;
// }

// static int testESFCallback(void *data, int argc, char **azColName, char **argv)
// {
//   int i;
//   for (i = 0; i < argc; i++) {
//     printf("%s%s", azColName[i], (i == argc - 1) ? "" : "| ");
//   }
//   printf("\n");
//   for (i = 0; i < argc; i++) {
//     printf("%s%s", argv[i] ? argv[i] : "NULL", (i == argc - 1) ? "" : "| ");
//   }
//   printf("\n");
//   return 0;
// }

int executeSQLFile(GNCDB *db, char *fileName)
{
  FILE  *file;
  FILE  *resWrite;
  char  *line            = NULL;
  size_t len             = 0;
  char  *current_sql     = NULL;
  size_t current_sql_len = 0;
  char  *temp            = NULL;
  char  *start;
  char  *end;
  char  *comment;
  size_t line_len;
  char  *resWriteName = NULL;
  int    writeToFile  = 0;
  // CallBack2 callback     = testESFCallback;

  writeToFile = 0;  // 设置为1表示写入文件，0表示不写入文件
  // 判断文件是否以.test结尾
  //   if (strlen(fileName) < 5 || strcmp(fileName + strlen(fileName) - 5, ".test") != 0) {
  //     fprintf(stderr, "Error: File name does not end with .test\n");
  //     return 1;
  //   }

  // 将fileName的后缀.test替换为.res
  resWriteName                           = my_strdup(fileName);
  resWriteName[strlen(resWriteName) - 4] = '\0';
  strcat(resWriteName, "res");

  if (writeToFile) {
    resWrite = fopen(resWriteName, "w");
    if (resWrite == NULL) {
      perror("Error opening file");
      my_free(resWriteName);
      return 1;
    }
    // callback = testESFWriteCallback;
  }
  my_free(resWriteName);

  file = fopen(fileName, "r");
  if (file == NULL) {
    perror("Error opening file");
    return 1;  // 或者更具体的GNCDB错误代码
  }

  while ((getline(&line, &len, file)) != -1) {
    // 删除前导和尾随空格，包括换行符
    start = line;
    while (isspace((unsigned char)*start)) {
      start++;
    }

    if (*start == '\0') {
      continue;
    }

    end = line + strlen(line) - 1;
    while (end > start && isspace((unsigned char)*end)) {
      end--;
    }
    *(end + 1) = '\0';

    // 处理注释
    comment = strstr(start, "--");
    if (comment != NULL) {
      *comment = '\0';  // 在注释处终止字符串
    }

    // 在删除注释后检查该行是否为空
    if (*start == '\0') {
      continue;
    }

    // 追加到 current_sql，处理重新分配
    line_len = strlen(start);
    temp     = my_realloc(current_sql, current_sql_len + line_len + 2);  // 为空间和空终止符增加2
    if (!temp) {
      perror("Memory allocation failed");
      my_free(line);
      my_free(current_sql);  // 释放先前分配的内存
      fclose(file);
      return GNCDB_MEM;
    }
    current_sql = temp;

    if (current_sql_len > 0) {
      current_sql[current_sql_len++] = ' ';
    }
    strcpy(current_sql + current_sql_len, start);  // 高效复制
    current_sql_len += line_len;
    // current_sql[current_sql_len] = '\0'; // strcpy 已经自动添加了空终止符

    // 在追加*之后*检查分号
    if (current_sql[current_sql_len - 1] == ';') {
      // printf("SQL: %s\n", current_sql);
      if (writeToFile) {
        fprintf(resWrite, "%s\n", current_sql);
      }
      GNCDB_exec(db, current_sql, NULL, writeToFile ? resWrite : NULL, NULL);  // 执行
      if (writeToFile) {
        fprintf(resWrite, "\n");
      }
      // printf("\n");
      my_free(current_sql);  // 执行后释放
      current_sql     = NULL;
      current_sql_len = 0;
    }
  }

  // 处理任何剩余的 SQL 语句（如果文件未以 ';' 结尾）
  if (current_sql != NULL) {
    GNCDB_exec(db, current_sql, NULL, writeToFile ? resWrite : NULL, NULL);
    my_free(current_sql);
  }

  my_free(line);  // 释放行缓冲区
  fclose(file);
  if (writeToFile) {
    fclose(resWrite);
  }
  return GNCDB_SUCCESS;
}
/**
 * @brief 创建哈希索引
 * @param db 数据库指针
 * @param tableName 表名
 * @param indexName 索引名
 * @param columnNum 索引列数量
 * @param ... 可变参数，包含索引列名
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int GNCDB_createHashIndex(struct GNCDB *db, char *tableName, char *indexName, int columnNum, ...)
{
  /* 所有变量声明放在函数开头 */
  int           rc          = 0;
  int           columnIndex = 0;
  Transaction  *tx          = NULL;
  TableSchema  *tableSchema = NULL;
  TableSchema  *indexSchema = NULL;
  varArrayList *columnList  = NULL;
  Column       *column      = NULL;
  Column       *tableColumn = NULL;
  char         *fieldName   = NULL;
  va_list       ap;

  /* 参数检查 */
  if (db == NULL || tableName == NULL || indexName == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 创建事务 */
  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }

  /* 检查表是否存在 */
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    transactionRollback(tx, db);
    return GNCDB_TABLE_NOT_FOUND;
  }

  /* 检查索引名是否已存在 */
  if (hashMapExists(db->catalog->indexMap, indexName)) {
    printf("Error: 索引名 '%s' 已存在\n", indexName);
    transactionRollback(tx, db);
    return GNCDB_PARAM_INVALID;
  }

  /* 检查索引名长度 */
  if (strlen(indexName) > TABLENAME_FIELD_MAXLEN) {
    printf("Error: 索引名 '%s' 超出最大长度限制 %d\n", indexName, TABLENAME_FIELD_MAXLEN);
    transactionRollback(tx, db);
    return GNCDB_PARAM_INVALID;
  }

  /* 创建索引列列表 */
  columnList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, arrayColumnDestroy);
  if (columnList == NULL) {
    transactionRollback(tx, db);
    return GNCDB_MEM;
  }

  /* 哈希索引目前只支持单列索引 */
  if (columnNum != 1) {
    // printf("Error: 哈希索引只支持单列索引，当前指定了 %d 列\n", columnNum);
    va_end(ap);
    varArrayListDestroy(&columnList);
    transactionRollback(tx, db);
    return GNCDB_PARAM_INVALID;
  }

  /* 处理可变参数，获取索引列 */
  va_start(ap, columnNum);

  /* 获取索引列名并验证 */
  fieldName = va_arg(ap, char *);
  if (fieldName == NULL) {
    va_end(ap);
    varArrayListDestroy(&columnList);
    transactionRollback(tx, db);
    return GNCDB_PARAMNULL;
  }

  /* 检查列是否存在于表中 */
  columnIndex = tableSchemaGetIndex(tableSchema, fieldName);
  if (columnIndex < 0) {
    va_end(ap);
    varArrayListDestroy(&columnList);
    transactionRollback(tx, db);
    return GNCDB_COLUMN_NOT_FOUND;
  }

  /* 获取列信息 */
  tableColumn = varArrayListGetPointer(tableSchema->columnList, columnIndex);
  if (tableColumn == NULL) {
    va_end(ap);
    varArrayListDestroy(&columnList);
    transactionRollback(tx, db);
    return GNCDB_COLUMN_NOT_FOUND;
  }

  // 需要新建深拷贝一个
  column = columnDeepCopy(tableColumn);

  /* 添加列到列表 */
  rc = varArrayListAddPointer(columnList, column);
  if (rc != GNCDB_SUCCESS) {
    my_free(column->fieldName);
    my_free(column);
    va_end(ap);
    varArrayListDestroy(&columnList);
    transactionRollback(tx, db);
    return rc;
  }

  // todo 这里使用了tableSchema当作IndexSchema
  /* 创建索引表结构 */
  indexSchema = tableSchemaConstruct(0, 1, columnList);
  if (indexSchema == NULL) {
    va_end(ap);
    varArrayListDestroy(&columnList);
    transactionRollback(tx, db);
    return GNCDB_MEM;
  }

  /* 调用执行器创建哈希索引 */
  rc = executorCreateHashIndex(db, tableName, indexName, indexSchema, tx);
  if (rc != GNCDB_SUCCESS) {
    va_end(ap);
    tableSchemaDestroy(indexSchema);
    transactionRollback(tx, db);
    return rc;
  }
  /* 提交事务 */
  rc = transactionCommit(tx, db);
  /* 清理资源时的顺序调整 */
  if (rc != GNCDB_SUCCESS) {
    va_end(ap);
    tableSchemaDestroy(indexSchema);
    transactionRollback(tx, db);
    return rc;
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 删除哈希索引
 * @param db 数据库指针
 * @param indexName 索引名
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int GNCDB_dropHashIndex(struct GNCDB *db, char *tableName, char *indexName)
{
  /* 所有变量声明放在函数开头 */
  int          rc = 0;
  Transaction *tx = NULL;
  // printf("开始调用GNCDB_dropHashIndex\n");
  /* 参数检查 */
  if (db == NULL || indexName == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 创建事务 */
  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }
  // printf("准备调用executorDropeHashIndexx\n");
  /* 调用执行器删除哈希索引 */
  rc = executorDropeHashIndex(db, tableName, indexName, tx);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    return rc;
  }
  // printf("成功调用executorDropeHashIndexx\n");
  /* 提交事务 */
  rc = transactionCommit(tx, db);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    return rc;
  }
  // printf("调用GNCDB_dropHashIndex事务结束\n");
  /* 清理资源 */
  // transcationDestroy(&tx, db);

  return GNCDB_SUCCESS;
}