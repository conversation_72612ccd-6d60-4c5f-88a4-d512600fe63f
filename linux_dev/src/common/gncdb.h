/*
 * @Descripttion: 
 * @version: 
 * @Author: Alan
 * @Date: 2023-01-30 09:19:38
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-07-29 15:28:16
 */
#ifndef _GNCDB_H_
#define _GNCDB_H_
#ifdef __cplusplus
extern "C" {
#endif
#include "catalog.h"
// #include "pagepool.h"
// #include "tranmanager.h"
#include "sql_event.h"
typedef struct CreateTableStmt CreateTableStmt;
struct PagePool;
struct Catalog;
struct TransactionManager;
struct ReadWriteLock;
struct Lookaside;

typedef struct GNCDB
{
    char *fileName;                             /* 文件名称 */
    FILE *dbFile;                               /* 文件句柄 */
    BYTE dbOverview[BYTES32 +1];                /* 数据库概述 限长32*/
    // BYTE dbVersion[BYTES32 +1];                 /* 数据库版本 限长32*/
    int dbMajor;                                /* 数据库主版本号 */
    int dbMinor;                                /* 数据库次版本号 */
    //注：文件中1号page存配置信息，2、3号存master，schema表
    int totalPageNum;                           /* 数据库总页数 */ 
    int firstFreePid;                           /* 第一个空页的页号 */
    //int totalFreePageNum;                       /* 总的空页数 */
    struct PagePool *pagePool;
    struct Catalog *catalog;
    struct TransactionManager* transactionManager;
    struct DBLog* dbLog;
    struct ReadWriteLock latch;
    struct ReadWriteLock latchSQLParse;
    struct Lookaside* lookaside; /* 内存池 */
    int pagePoolCount;
    int pageCurrentSize;
}GNCDB;
int dbInstanceReadFileHeader(struct GNCDB* db);
/* 外部接口*/
int GNCDB_open(struct GNCDB **db, char *fileName, int pageSize, int pagePoolCount);
int GNCDB_select(GNCDB* db, CallBack2 callback, int* affectedRows,void* data, int tableNum, int columnNum, int filterNum, ...);
int GNCDB_createTable(struct GNCDB* db, char* tableName, int columnNum, ...);
int GNCDB_dropTable(GNCDB* db, char* tableName);
int GNCDB_insert(struct GNCDB* db, int* affectedRows, char* tableName, ...);
int GNCDB_delete(struct GNCDB* db, int* affectedRows, char* tableName, int filterNum, ...);
int GNCDB_update(struct GNCDB* db, int* affectedRows, char* tableName, int setNum, int filterNum, ...);
/* 这里的buf由用户分配 */
int GNCDB_setBlob(struct GNCDB* db, char* tableName, int columnNum, BYTE* buf, int size, int keyValueNum, ...);
int GNCDB_getBlob(struct GNCDB* db, char* tableName, int columnNum, BYTE* buf, int size, int keyValueNum, ...);
int GNCDB_deleteBlob(struct GNCDB* db, char* tableName, int columnNum, int keyValueNum, ...);
int GNCDB_setTableParam(struct GNCDB* db, char* tableName);
int GNCDB_close(struct GNCDB** db);

////////////////////////////////SQL///////////////////////////////////////
int GNCDB_exec(GNCDB* db, const char *sql, CallBack2 callback, void* data, char** errmsg);

// todo 该函数不为外部接口，待定
int txnExecuteSQL(SQLStageEvent* sqlEvent, const char *sql);
int txnExecuteSQLStep(SQLStageEvent* sqlEvent, const char *sql);
int executeSQLFile(GNCDB* db, char* fileName);


/* R树索引内容 */
int GNCDB_createRtreeIndex(struct GNCDB* db, char* tableName, char* indexName, int v_si32_dim, ...);

int GNCDB_dropRtreeTable(GNCDB* db, char* tableName);

int GNCDB_selectRtreeIndex(GNCDB* db, CallBack callback, int* affectedRows, int tableNum, int columnNum, int filterNum, ...);

/* 哈希索引相关API */
int GNCDB_createHashIndex(struct GNCDB* db, char* tableName, char* indexName, int columnNum, ...);

int GNCDB_dropHashIndex(GNCDB* db, char* tableName, char* indexName);



#endif