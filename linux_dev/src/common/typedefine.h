/*
 * @Descripttion:
 * @version:
 * @Author: Alan
 * @Date: 2023-01-30 09:19:38
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-04-24 14:46:32
 */
#ifndef TYPE_DEFINE_H_
#define TYPE_DEFINE_H_

#include "readwritelock.h"
#include "condvariable.h"

typedef unsigned char BYTE;
// typedef enum FieldType FieldType;
// typedef enum PageType PageType;
// typedef enum Predicate Predicate;
// typedef enum LockType LockType;
// typedef enum TableType TableType;
// typedef enum OperatorType OperatorType;
// typedef enum TransactionStatus TransactionStatus;
// typedef enum IsolationLevel IsolationLevel;
// typedef enum LogType LogType;
// typedef enum OperationType OperationType;
// typedef enum LogLockObjType LogLockObjType;
// typedef enum LogLockType LogLockType;

typedef int (*CallBack)(int, char **, char **);
typedef int (*CallBack2)(void*, int, char **, char **);

/* 定义不同的操作类型，
主要是针对btreeTableFindTupleInLeafPage函数，可以根据不同的操作类型进行不同的加锁思路 */
typedef enum OperationType
{
    OP_SEARCH,
    OP_INSERT,
    OP_DELETE,
    OP_UPDATE,
    OP_BLOB_SET,
    OP_BLOB_GET,
    OP_BLOB_DELETE
}OperationType;
/* 日志加解锁对象类别 */
typedef enum LogLockObjType
{
    TABLE_LOCK,
    PAGE_LOCK,
    DATABASE_LOCK,
    LOGGER_LOCK,
    CATALOG_LOCK,
    PAGEPOOL_LOCK,
    TXMANAGER_LOCK,
}LogLockObjType;
/* 锁类别 */
typedef enum LogLockType
{
    LOCK_ING,
    UNLOCK_ING,
    LOCK_END,
    UNLOCK_END,
}LogLockType;
/*  事务状态 */
typedef enum TransactionStatus
{
    GROWING,
    SHRINKING,
    COMMITTED,
    ABORTED
}TransactionStatus;

/* 事务的隔离级别 */
typedef enum IsolationLevel
{ /* 默认只实现读已提交这一种隔离级别 */
  READ_COMMITTED
}IsolationLevel;

typedef enum PageType
{
    LEAF_PAGE,
    INTERNAL_PAGE,
    OVERFLOW_PAGE,
    FREE_PAGE,
    META_PAGE,
    BUCKET_PAGE,
    HASH_OVERFLOW_PAGE
}PageType;

typedef enum FieldType
{   
    FIELDTYPE_INVALID,
    FIELDTYPE_INTEGER,
    FIELDTYPE_REAL,    // 浮点类型
    FIELDTYPE_VARCHAR, // 字符串
    FIELDTYPE_BLOB,
    FIELDTYPE_DATE,
    FIELDTYPE_DATETIME,
    FIELDTYPE_TEXT,
}FieldType;

typedef enum Predicate
{
    EQUAL,
    GREATER_THAN,
    GREATER_THAN_OR_EQUAL,
    NOT_EQUAL,
    LESS_THAN,
    LESS_THAN_OR_EQUAL
}Predicate;

// enum LockStatus
// {
//     LOCK_UP,
//     LOCK_UN
// };

typedef enum LockType
{
    SHARD,
    EXCLUSIVE,
    UNKNOW
}LockType;

typedef enum TableType
{
    TableType_TABLE,
    TableType_INDEX
}TableType;

typedef enum OperatorType
{
    OperatorType_SELECT,
    OperatorType_FILTER,
    OperatorType_JOIN,
    OperatorType_PROJECTION,
    OperatorType_SCAN
}OperatorType;

// sqlNode
typedef enum SqlCommandFlag
{
  SCF_ERROR = 0,
  SCF_CALC,
  SCF_SELECT,
  SCF_INSERT,
  SCF_UPDATE,
  SCF_DELETE,
  SCF_CREATE_TABLE,
  SCF_DROP_TABLE,
  SCF_CREATE_INDEX,
  SCF_DROP_INDEX,
  SCF_SYNC,
  SCF_SHOW_TABLES,
  SCF_DESC_TABLE,
  SCF_BEGIN,
  SCF_COMMIT,
  SCF_CLOG_SYNC,
  SCF_ROLLBACK,
  SCF_LOAD_DATA,
  SCF_HELP,
  SCF_EXIT,
  SCF_EXPLAIN,
  SCF_SET_VARIABLE,
} SqlCommandFlag;

// stmt类型
typedef enum StmtType
{
  ST_CALC,
  ST_SELECT,
  ST_INSERT,
  ST_UPDATE,
  ST_DELETE,
  ST_CREATE_TABLE,
  ST_DROP_TABLE,
  ST_CREATE_INDEX,
  ST_DROP_INDEX,
  ST_SYNC,
  ST_SHOW_TABLES,
  ST_DESC_TABLE,
  ST_BEGIN,
  ST_COMMIT,
  ST_ROLLBACK,
  ST_LOAD_DATA,
  ST_HELP,
  ST_EXIT,
  ST_EXPLAIN,
  ST_PREDICATE,
  ST_SET_VARIABLE,
  ST_JOIN_TABLES,
  ST_GROUP_BY,
  ST_ORDER_BY,
  ST_LIMIT,
} StmtType;
typedef enum LogicalOperatorType
{
  LO_CALC,
  LO_TABLE_GET,     ///< 从表中获取数据
  LO_INDEX_SCAN,    ///< 索引扫描
  LO_PREDICATE,     ///< 过滤，就是谓词
  LO_PROJECTION,    ///< 投影，就是select
  LO_JOIN,          ///< 连接
  LO_INSERT,        ///< 插入
  LO_DELETE,        ///< 删除，删除可能会有子查询
  LO_EXPLAIN,       ///< 查看执行计划
  LO_UPDATE,        ///< 更新
  LO_GROUPBY,       ///< 分组
  LO_ORDERBY,       ///< 排序
  LO_CREATE_TABLE,  ///< 创建表
  LO_CREATE_INDEX,  ///<
  LO_LIMIT,         ///< 限制返回的行数
} LogicalOperatorType;

typedef enum PhysicalOperatorType
{
  PO_INVALID,
  PO_TABLE_SCAN,
  PO_INDEX_SCAN,
  PO_NESTED_LOOP_JOIN,
  PO_EXPLAIN,
  PO_PREDICATE,
  PO_PROJECT,
  PO_CALC,
  PO_STRING_LIST,
  PO_DELETE,
  PO_INSERT,
  PO_UPDATE,
  PO_GROUPBY,
  PO_HASH_JOIN,
  PO_SORT_MERGE_JOIN,
  PO_ORDER_BY,
  PO_CREATE_TABLE,
  PO_LIMIT,
} PhysicalOperatorType;

typedef enum
{
  CJET_INVALID,
  CJET_AND,
  CJET_OR
} ConjunctionExprType;

/* 描述比较运算符 */
typedef enum CompOp
{
  CMPOP_INVALID,        //  无效的比较运算符
  CMPOP_EQUAL_TO,       // "="
  CMPOP_LESS_EQUAL,     // "<="
  CMPOP_NOT_EQUAL,      // "<>"
  CMPOP_LESS_THAN,      // "<"
  CMPOP_GREAT_EQUAL,    // ">="
  CMPOP_GREAT_THAN,     // ">"
  CMPOP_LIKE_OP,        // like
  CMPOP_NOT_LIKE_OP,    // not like
  CMPOP_IN_OP,          ///< in (sub query)
  CMPOP_NOT_IN_OP,      ///< not in (sub query)
  CMPOP_EXISTS_OP,      ///< exists (sub query)
  CMPOP_NOT_EXISTS_OP,  ///< not exists (sub query)
  CMPOP_NO_OP,
} CompOp;

typedef enum
{
  ARITH_INVALID,
  ARITH_ADD,
  ARITH_SUB,
  ARITH_MUL,
  ARITH_DIV,
  ARITH_NEGATIVE
} ArithmeticExprType;

typedef enum
{
  AGG_INVALID,
  AGG_MAX,
  AGG_MIN,
  AGG_SUM,
  AGG_AVG,
  AGG_COUNT,
  AGGR_FUNC_TYPE_NUM
} AggrFuncType;

typedef enum ExprTag
{
  ETG_BASE = 0,
  ETG_FIELD,
  ETG_VALUE,
  ETG_CAST,
  ETG_COMPARISON,
  ETG_CONJUNCTION,
  ETG_ARITHMETIC,
  ETG_AGGRFUNC,
  ETG_SUBQUERY,
  ETG_EXPRLIST,
} ExprTag;

typedef enum TupleType
{
  INVALID_TUPLE,    /* 无效的元组 */
  ROW_TUPLE,        /* 一行数据的元组 */
  PROJECT_TUPLE,    /* 一行数据的投影元组 */
  EXPRESSION_TUPLE, /* 表达式元组 */
  VALUELIST_TUPLE,  /* 一些常量值组成的Tuple */
  JOINED_TUPLE,     /* 连接的元组 */
  GROUP_TUPLE,      /* 分组的元组 */
  SPLICED_TUPLE,
  SUBQUERY_TUPLE,
} TupleType;


typedef enum JCondType
{
  // 1.可以用于连接，是单个连接条件，例如 a.x = b.y
  JC_DIRECT,
  // 2.不能用于连接，例如 a inner join b on a.x = 非字段 此类情形   也可能是conjExpr类型，例如 a inner join b on（1|2）（AND|OR）（1|2）
  JC_INDIRECT
} JCondType;

typedef enum JoinType
{
  JT_INVALID,
  JT_CROSS,
  JT_NESTED_LOOP,
  JT_HASH,
  JT_SORT_MERGE,
} JoinType;

#endif