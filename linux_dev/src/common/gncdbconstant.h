/*
 * @Descripttion: 代码中的状态码和常量
 * @version:
 * @Author: Alan
 * @Date: 2023-01-30 09:19:38
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-19 09:29:25
 */

#ifndef _GNCDBCONSTANT_H_
#define _GNCDBCONSTANT_H_

#include "oss_type_def.h"
#include <assert.h>
#include "runninglog.h"
#if defined _WIN32
// #include <windows.h>
#include <time.h>
#endif
#define EPSILON (1E-6)
#define true 1
#define false 0
#define DEBUG 0
/* 根据具体操作系统运行路径设置 */
/*
windows  ..
linux\天脉  .
*/

#if defined _WIN32
#define LOCAL_PATH "."
#else
#define LOCAL_PATH "."
#define REOPEN_DATEBASE 0
#define LOAD_DATA 0

// #define ENABLE_LOOKASIDE_MEM_POOL 
// #define PINTF_OPERATOR_TREE
#define ENABLE_JOIN_OPTIMIZATION
#define SELECT_PLAN_BYPASS
// static char* tpccTimeCost = "tpccTimeCostByhashJoin";
// static FILE *fp;
// #define PRINT_TRXS_COST_TIME
// #define PINTF_OPERATOR_TREE
#endif
// #define USE_LOG 1
// #define SELF_DEFINE_MEMORY 1

#ifndef ASSERT
#define ASSERT(expression, description, ...)                                \
  do {                                                                      \
    if (!(expression)) {                                                    \
      char dbLogStr[1024];                                                  \
      snprintf(dbLogStr,                                                    \
          sizeof(dbLogStr),                                                 \
          "Assertion failed: (%s), function %s, file %s, line %d. "         \
          "Description: " description " ",                                  \
          #expression,                                                      \
          __FUNCTION__,                                                     \
          __FILE__,                                                         \
          __LINE__,                                                         \
          ##__VA_ARGS__);                                                   \
      DBLoggerPrint(LOG_TRACE, dbLogStr, __FILE__, __FUNCTION__, __LINE__); \
      assert(expression);                                                   \
    }                                                                       \
  } while (0)
#endif  // ASSERT

// #define LOG_PRINT(logtype, dbLogStr) (DBLoggerPrint(logtype, dbLogStr, __FILE__, __FUNCTION__, __LINE__))
/* int logtype:日志level, format日志内容格式化*/

#ifdef USE_LOG
#define LOG(logtype, format, ...) \
    do { \
        DBLoggerPrint(logtype, __builtin_FILE(), __FUNCTION__, __LINE__, format, ##__VA_ARGS__); \
    } while (0)
#else
#define LOG(logtype, format, ...) \
  do {                            \
  } while (0)
#endif

#ifdef SELF_DEFINE_MEMORY
#define my_malloc_init(buffer, size) s_maes_init(buffer, size)
#define my_free(p) s_maes_free(p)
#define my_malloc(size) s_maes_malloc(size)
#define my_realloc(p, size) s_maes_realloc(p, size)
#else
#define my_free(p) free(p)
#define my_malloc(size) malloc(size)
#define my_realloc(p, size) realloc(p, size)
#endif

#define safeMalloc0(size)        \
  ({                             \
    void *_ptr;                  \
    if ((size) == 0) {           \
      _ptr = NULL;               \
    } else {                     \
      _ptr = my_malloc(size);    \
      if (_ptr != NULL) {        \
        memset(_ptr, 0, (size)); \
      }                          \
    }                            \
    _ptr;                        \
  })

#define my_strdup(s)                   \
  ({                                 \
      const char *__src = (s);       \
      char *__dst = NULL;            \
      if (__src) {                   \
          size_t __len = strlen(__src) + 1; \
          __dst = (char *)my_malloc(__len);   \
          if (__dst) memcpy(__dst, __src, __len); \
      }                              \
      __dst;                         \
  })



#define my_malloc0(size) safeMalloc0(size)
#define my_new0(type) (safeMalloc0(sizeof(type)))

#define likely(x) __builtin_expect((x) != 0, 1)
#define unlikely(x) __builtin_expect((x) != 0, 0)

#define DEBUG_PRINT

#ifdef DEBUG_PRINT
#define PRINT(format, ...) printf(format, ##__VA_ARGS__)
#else
#define PRINT(format, ...)
#endif

#define DB_SUCCESS(x) ((x) == GNCDB_SUCCESS)
#define DB_FAILED(x) ((x) != GNCDB_SUCCESS)

#define MAX_TRANSACTIONS 100         /* 最大事务数*/
#define PAGE_SIZE 4096          /* 页大小*/
#define MAX_CACHED_NUM 6000          /* 最大缓存页数*/
#define PAGE_HEAD_SIZE 13            /* 页头字节大小*/
#define OVERFLOWPAGE_HEAD_SIZE 5     /* 溢出页头字节大小*/
#define BYTES32 32                   /* 32字节*/
#define BYTES64 64                   /* 32字节*/
#define BYTES256 256                 /* 256字节*/
#define BYTES_POINTER sizeof(void *) /* 指针类型占4字节内存*/
#define MAXTABLE 10000               /* 最多有10000张表*/
#define MAXINDEX 5                   /* 一个表最多有5个索引*/
#define MAXCOLNUM 32                 /* 一个表最多有32列*/
#define MASTERCOLNUM 12              /* master有11列*/
#define SCHEMACOLNUM 12              /* schema有11列*/
#define MASTERNAME "master"          /* master表名*/
#define SCHEMANAME "schema"          /* schema表名*/
#define MAX_PAGE_NUM (2 * 1024)      /* 最大页数*/
#define OVERVIEW_LEN 5               /* 数据库概述*/
// #define VERSION_LEN 2
#define MAJOR_VERSION 4                /* 主版本号长度*/
#define MINOR_VERSION 4                /* 副版本号长度*/
#define MASTER_ROOT_PID 2            /* master根节点*/
#define SCHEMA_ROOT_PID 3            /* schema根节点*/
#define MASTERTWOCOLUMN 1            /* master表的第二列*/
#define MASTERFORECOLUMN 4           /* master表的第四列*/
#define MASTERFIVECOLUMN 5           /* master表的第五列*/
#define MAIN_TID (-1)                /* */
#define TABLENAME_FIELD_MAXLEN 16    /* 表名和属性的最大长度限制 */
#define FILENAME_FIELD_MAXLEN 20     /* 数据名最大长度限制 */
#define LOGFILE_MAXSIZE 10485760000  /* 日志文件最大暂定为10MB */
#define MAX_LOGGER 300000            /*  */
#define LOGGER_MIN_SIZE 17           /*  */
#define COND_COUNT 200               /*  */
#define MAX_CHARLEN_NUM 256          /*  */

#define STATISTICS_ENABLE 1   /* 统计信息开关 */
#define RECORD_CHECK_COST 0.2 /* 检测一条记录是否符合搜索条件的成本 */

#define INT_SIZE sizeof(int)
#define DOUBLE_SIZE sizeof(double)
#define TYPE_SIZE 1

#define LOOKASIDE_SIZE 600  /* lookaside内存池一个大槽的大小 */
#define LOOKASIDE_COUNT 80  /* lookaside内存池中大槽的数量 */

/* 日志宏模块 */
#define LOG_TRACE 1001
#define LOG_DEBUG 1002
#define LOG_INFO 1003
#define LOG_WARNING 1004
#define LOG_ERROR 1005

#define EPSILON (1E-6)

#define GNCDB_SUCCESS 0                                 /* 成功 */
#define GNCDB_MEM -1                                    /* malloc失败 */
#define GNCDB_FILEISNULL -2                             /* 文件为空 */
#define GNCDB_PARAMNULL -3                              /* 输入参数为空 */
#define GNCDB_SPACE_LACK -4                             /* 空间不足 */
#define GNCDB_NOT_FOUND -5                              /* 未找到 */
#define GNCDB_TABLE_NOT_FOUND -6                        /* 表未找到 */
#define GNCDB_NO_PAGE -7                                /* 没有这个页 */
#define GNCDB_FLUSH_FAILED -8                           /* 刷新失败 */
#define GNCDB_READ_FAILED -9                            /* 读取失败 */
#define GNCDB_NOT_OPEN -10                              /* 不能打开 */
#define GNCDB_NO_VALID -11                              /* 数据库不存在 */
#define GNCDB_CREATE_FALSE -12                          /* 数据库创建失败 */
#define GNCDB_PARAM_INVALID -13                         /* 输入参数不合法 */
#define GNCDB_TABLE_EXIST -14                           /* table存在 */
#define GNCDB_DUPLICATE_PRIMARY_KEY -15                 /* 插入tuple时，主键值重复 */
#define GNCDB_PRIMARY_KEY_IMMUTABLE -16                 /* 主键值不可修改 */
#define GNCDB_CAPOVERFLOW -17                           /* list容量溢出 */
#define GNCDB_NO_REMOVE -18                             /* 不能删除 */
#define GNCDB_COLUMN_NOT_FOUND -19                      /* 列找不到 */
#define GNCDB_ARRAY_ADD_FALSE -20                       /* array插入失败 */
#define GNCDB_PRIMARYKEYINDEXMAP_CREATE_FALSE -21       /* 主键列号map创建失败 */
#define GNCDB_PRIMARYKEYTYPEMAP_CREATE_FALSE -22        /* 主键类型map创建失败 */
#define GNCDB_TABLESCHEMAMAP_CREATE_FALSE -23           /* tableSchemaMap创建失败 */
#define GNCDB_MASTER_TS_CREATE_FALSE -24                /* masterTableSchema创建失败 */
#define GNCDB_SCHEMA_TS_CREATE_FALSE -25                /* schemaTableSchema创建失败 */
#define GNCDB_MASTERTABLE_CREATE_FALSE -26              /* masterTable构造失败 */
#define GNCDB_SCHEMATABLE_CREATE_FALSE -27              /* schemaTable构造失败 */
#define GNCDB_TABLEMAP_CREATE_FALSE -28                 /* tableMap构造失败 */
#define GNCDB_BTC_CREATE_FALSE -29                      /* btreeCursor构造失败 */
#define GNCDB_ARRAY_CREATE_FALSE -30                    /* vararray创建失败 */
#define GNCDB_MASTERFIELDARRAY_NOT_FOUND -31            /* masterTuple的fieldArray找不到 */
#define GNCDB_ARRAY_GETPOINTER_FALSE -32                /* array查找失败 */
#define GNCDB_CONDITION_CREATE_FALSE -33                /* condition构造失败 */
#define GNCDB_SCHEMATUPLE_NOT_FOUND -34                 /* schemaTuple没有找到 */
#define GNCDB_COLUMNCONSTRAINT_CREATE_FALSE -35         /* columnconstraint构造失败 */
#define GNCDB_COLUMN_CREATE_FALSE -36                   /* column构造失败 */
#define GNCDB_TABLESCHEMA_CREATE_FALSE -37              /* tableSchema构造失败 */
#define GNCDB_BTREETABLE_CREATE_FALSE -38               /* btreeTable构造失败 */
#define GNCDB_BTREEDFS_FALSE -39                        /* DFS失败 */
#define GNCDB_LEAFTUPLE_CREATE_FALSE -40                /* tuple构造失败 */
#define GNCDB_INTFIELD_CREATE_FALSE -41                 /* intfield构造失败 */
#define GNCDB_LEAFTUPLE_ADD_FALSE -42                   /* tuple添加field失败 */
#define GNCDB_VARCHARFIELD_CREATE_FALSE -43             /* varcharField构造失败 */
#define GNCDB_INSERTTTUPLE_FALSE -44                    /* btreeTableInsertTuple失败 */
#define GNCDB_REALFIELD_FALSE -45                       /* realField构造失败 */
#define GNCDB_MASTERTUPLE_NOT_FOUND -46                 /* masterTuple找不到 */
#define GNCDB_FIELD_NOT_EXIST -47                       /* 属性不存在 */
#define GNCDB_MAP_CREATE_FALSE -48                      /* map创建失败 */
#define GNCDB_STATUS_NOT_FOUND -49                      /* pagestatus找不到 */
#define GNCDB_MAP_GET_FALSE -50                         /* map查找失败 */
#define GNCDB_ARRAY_GET_FALSE -51                       /* ARRAY的get失败 */
#define GNCDB_BTG_CREATE_FALSE -52                      /* btreePage构造失败 */
#define GNCDB_FREEPAGE_CREATE_FALSE -53                 /* freePage构造失败 */
#define GNCDB_OVERFLOWPAGE_CREATE_FALSE -54             /* overflow构造失败 */
#define GNCDB_ARRAY_REMOVE_FALSE -55                    /* array删除失败 */
#define GNCDB_MAP_PUT_FALSE -56                         /* MAP的插入失败 */
#define GNCDB_MAP_REMOVE_FALSE -57                      /* MAP的删除失败 */
#define GNCDB_STATUS_CREATE_FALSE -58                   /* pageStatus构造失败 */
#define GNCDB_TOBYTE_FALSE -59                          /* 转化字节流失败 */
#define GNCDB_MAP_ITERATOR_FALSE -60                    /* map迭代器创建失败 */
#define GNCDB_MAP_NEXT_NOT_FOUND -61                    /* map的下一个找不到 */
#define GNCDB_PAGETYPE_NOT_FOUND -62                    /* pageType错误 */
#define GNCDB_TABLE_PARAM_FALSE -63                     /* 表只能读，不能写 */
#define GNCDB_DEEPCOPY_FAIL -64                         /* 深拷贝失败 */
#define GNCDB_LEAFPAGE_NOT_FOUND -65                    /* 获取叶子页失败 */
#define GNCDB_NOT_FOUND_PAGE -66                        /* 找不到指定的页 */
#define GNCDB_PRIMARYKEY_NOTNULL -67                    /* 主键值不能为空 */
#define GNCDB_WAS_CLOSED -68                            /* 数据库已经关闭 */
#define GNCDB_AIIPAGE_IS_PIN -69                        /* 所有的页均被钉住 */
#define GNCDB_LOCK_FAIL -70                             /* 申请锁失败 */
#define GNCDB_UN_JOIN -71                               /* tuple不满足条件无法join */
#define GNCDB_ROWS_OVERFLOW -72                         /* 表已经达到最大行数 */
#define GNCDB_LOG_READ_ERROR -73                        /* log日志读取失败 */
#define GNCDB_FILE_NOT_FOUND -74                        /* 数据库文件不存在 */
#define GNCDB_BLOB_EXIST -75                            /* blob文件已存在 */
#define GNCDB_BLOB_NO_DELETE -76                        /* tuple中存在blob无法删除 */
#define GNCDB_SYSTEMTABLE_NOTREMOVE -77                 /* 系统表不能删除 */
#define GNCDB_NOT_REFACTOR -78                          /* master和schema禁止插入 */
#define GNCDB_TABLESCHEMA_NOT_FOUND -79                 /* tableSchema找不到 */
#define GNCDB_WRITE_FILE_FAILED -80                     /* 写文件失败 */
#define GNCDB_OLD_PAGE_EXIST -81                        /* 页已备份 */
#define GNCDB_BLOB_NOT_EXIST -82                        /* tuple无大对象 */
#define GNCDB_WRITE_FILE_FAILED -80                     /* 写文件失败 */
#define GNCDB_OLD_PAGE_EXIST -81                        /* 页已备份 */
#define GNCDB_PARSE_FALSE -82                           /* parse 解析失败 */
#define GNCDB_INTERNAL -83                              /* 内部错误状态码 */
#define GNCDB_SQL_SYNTAX -84                            /* sql语法错误 */
#define GNCDB_UNIMPLENMENT -85                          /* 暂未实现 */
#define GNCDB_SCHEMA_NUM_MISMATCH -86                   /* 表schema数量不匹配 */
#define GNCDB_SCHEMA_FIELD_TYPE_MISMATCH -87            /* 表schema类型不匹配 */
#define GNCDB_SCHEMA_FIELD_MISSING -88                  /* 无效字段名 */
#define GNCDB_NEXT_EOF -89                              /* next结束 */
#define GNCDB_INSERT_FAILED -90                         /* 插入失败 */
#define GNCDB_TUPLE_NOT_FOUND -91                       /* tuple未找到 */
#define GNCDB_EXPR_EVAL_FAILED -92                      /* 表达式求值失败 */
#define GNCDB_JOIN_KEY_NOT_FOUND -93                    /* join key未找到 */
#define GNCDB_DATE_TYPE_INVALID -94                     /* 日期类型无效 */
#define GNCDB_ORDER_BY_INVALID -95                      /* order by无效 */
#define GNCDB_DATETIME_TYPE_INVALID -96                 /* datetime类型无效 */
#define GNCDB_FIELD_NOT_ALLOW_NULL -97                  /* 字段不允许为空 */
#define GNCDB_TEXT_LENGTH_EXCEED -98                    /* 文本长度超出限制 */
#define GNCDB_ROW_DONE -99                              /* row结束 */
#define GNCDB_GET_TABLE_ROWNUM_FAILED -100              /* 获取表行数失败 */
#define GNCDB_VARARRAYLIST_NULL -101                    /* varArrayList为空 */
#define GNCDB_TYPE_CAST_INVALID -102                    /* 类型转换无效 */
#define GNCDB_PREDICATE_PUSHDOWN_FAILED -103            /* 谓词下推失败 */
#define GNCDB_SORT_MERGE_NO_CONDITION -104              /* sort merge join没有连接条件 */
#define GNCDB_CONDITION_INVALID -105                    /* 条件无效 */
#define GNCDB_SQLFILE_INVALID -106                      /* sql文件无效 */
#define GNCDB_JOINLIST_NULL -107                        /* joinList为空 */
#define GNCDB_PARENTPAGEID_NOT_EXIST -108               /* 未能在LatchedPageSet中找到父页 */
#define GNCDB_PTR_SWAP_FAILED -109                      /* 指针交换失败 */
#define GNCDB_PRIMARYKEYVARCHARLENMAP_CREATE_FALSE -110 /* 主键varchar偏移map获取失败 */
#define GNCDB_PRIMARYKEYOFFSETMAP_CREATE_FALSE -111     /* 主键偏移map获取失败 */
#define GNCDB_PAGEPOOLNUM_EDIT_FAILED -112              /* 缓冲池大小只允许在数据库打开时设置 */
#define GNCDB_SUBQUERY_FIELD_IS_NOT_ONE -113            /* 子查询字段数不为1 */
#define GNCDB_DTOA_FAILED -114                          /* double to ascii失败 */
#define GNCDB_TXN_STATUS_INVALID -115                   /* 事务状态无效 */
#define GNCDB_META_CREATE_FAIL -116                     /* metaePage构造失败 */
#define GNCDB_BUCKET_CREATE_FAIL -117                   /* bucketePage构造失败 */
#define GNCDB_HASHOVERFLOW_CREATE_FAIL -118             /* hashOverflowPage构造失败 */
#define GNCDB_INDEX_NOT_SUITABLE -119                   /* 没有合适的索引 */
#define GNCDB_KEY_NOT_FOUND -120                        /* 没有找到键值对 */
#define GNCDB_INDEX_NOT_FOUND -121                      /* 哈希索引不存在 */
#define GNCDB_INVALID_FIELD_TYPE -122                   /* 哈希索引值非法类型 */
#define GNCDB_HASHINDEX_INSERT_FAILED -123              /*插入失败*/
#define GNCDB_HASHPAGE_UNSAFE -124                      /*哈希页面已满*/
#define GNCDB_PRIMARY_KEY_NOT_FOUND -125                /* 没有找到主键 */
#define GNCDB_HASHSELECT_FAILED -126                    /* 哈希索引查询失败 */
#define GNDCB_HASHPAGE_ERROR -127                       /* 哈希页面处理失败 */
#define GNCDB_EMPTY_HASHPAGE -128                       /* 哈希页面为空 */
#define GNCDB_INDEX_EXISTS -129                         /*哈希索引已经存在*/

static const int  DATE_SIZE       = sizeof(int);
static const int  SYS_FIELD_NUM   = 3;
static const int  MAX_TEXT_LENGTH = 65535;
static const int  FLOAT_SIZE      = sizeof(float);
static const int  DATETIME_SIZE   = 19;
static const int  CHECK_MINSIZE   = LOGGER_MIN_SIZE * MAX_LOGGER;
static const char TXN_BEGIN       = 0;
static const char TXN_COMMIT      = 1;
static const char TXN_ABORT       = 2;
static const char LOG_UPDATE      = 3;
static const char CHECK           = 4;
static const int  ID_SIZE         = 4;
static const int  PAGEID_SIZE     = 4;
static const int  OFFSET_SIZE     = 4;
static const int  LSN_SIZE        = 4;
static const int  INIT_MAX_PAGE   = 1000000;
static const int  LRU_K           = 20;
static const int  BEGIN_SIZE      = 4 * INT_SIZE + TYPE_SIZE;        
static const int  COMMIT_SIZE     = 4 * INT_SIZE + TYPE_SIZE;        
static const int  ABORT_SIZE      = 4 * INT_SIZE + TYPE_SIZE;        
static const int  CHECK_SIZE      = 2 * INT_SIZE + 2 * TYPE_SIZE;    

typedef struct OPT_COST_CONFIG
{
  double TUPLE_COST;
  double CPU_OPERATOR_COST;
  double MEM_PAGE_COST;
  double RANDOM_PAGE_COST;
} OPT_COST_CONFIG;

extern OPT_COST_CONFIG optCostConfig;

inline void set_tuple_cost(double value) { optCostConfig.TUPLE_COST = value; }

inline void set_cpu_operator_cost(double value) { optCostConfig.CPU_OPERATOR_COST = value; }

inline void set_mem_page_cost(double value) { optCostConfig.MEM_PAGE_COST = value; }

inline void set_random_page_cost(double value) { optCostConfig.RANDOM_PAGE_COST = value; }

extern int JOIN_BLOCK_TNUM;

inline void set_join_block_tnum(int value) { JOIN_BLOCK_TNUM = value; }

#endif
