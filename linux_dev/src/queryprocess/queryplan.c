/**
 * @file queryplan.c
 * <AUTHOR>
 * @brief
 * @date 2023-02-14
 * @LastEditTime: 2023-03-03 9:15:57
 * @copyright Copyright (c) 2023
 */

 #include "queryplan.h"
#include "btreetable.h"
#include "exec_tuple.h"
#include "typedefine.h"
#include <string.h>

 /// <summary>
 /// 构造一个查询计划
 /// </summary>
 /// <param name="queryTuple">回调函数</param>
 /// <param name="tid">事务id</param>
 /// <returns>成功返回结构体指针，失败返回状态码</returns>
Queryplan* queryplanConstruct(CallBack callback, varArrayList* tableArray, struct Transaction* tx)
{
	Queryplan* queryplan = NULL;

	queryplan = my_malloc(sizeof(struct Queryplan));
	if (queryplan == NULL)
	{
		return NULL;
	}
	queryplan->tableArray = tableArray;
	queryplan->scanMap = hashMapCreate(STRKEY, 0, NULL);
	if (queryplan->scanMap == NULL)
	{
		my_free(queryplan);
		return NULL;
	}
	queryplan->filterMap = hashMapCreate(STRKEY, 0, NULL);
	if (queryplan->filterMap == NULL)
	{
		hashMapDestroy(&queryplan->scanMap);
		my_free(queryplan);
		return NULL;
	}
	queryplan->joinArr = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (queryplan->joinArr == NULL)
	{
		hashMapDestroy(&queryplan->scanMap);
		hashMapDestroy(&queryplan->filterMap);
		my_free(queryplan);
		return NULL;
	}
	queryplan->projection = NULL;
	queryplan->callback = callback;
	queryplan->tx = tx;

	return queryplan;
}

/// <summary>
/// 保存scan算子
/// </summary>
/// <param name="queryplan">查询计划指针</param>
/// <param name="tableName">表名</param>
/// <param name="filter">filter算子</param>
/// <returns>状态码</returns>
int queryplanPushScanOperator(struct Queryplan* queryplan, char* tableName, struct Scan* scan)
{
	hashMapPut(queryplan->scanMap, tableName, scan);

	return GNCDB_SUCCESS;
}

/// <summary>
/// 保存filter算子
/// </summary>
/// <param name="queryplan">查询计划指针</param>
/// <param name="tableName">表名</param>
/// <param name="filter">filter算子</param>
/// <returns>状态码</returns>
int queryplanPushFilterOperator(struct Queryplan* queryplan, char* tableName, struct Filter* filter)
{
	hashMapPut(queryplan->filterMap, tableName, filter);

	return GNCDB_SUCCESS;
}

/// <summary>
/// 保存join算子
/// </summary>
/// <param name="queryplan">查询计划指针</param>
/// <param name="pJoin">join算子指针的指针</param>
/// <returns>状态码</returns>
int queryplanPushJoinOperator(struct Queryplan* queryplan, struct Join* join)
{
	varArrayListAddPointer(queryplan->joinArr, join);

	return GNCDB_SUCCESS;
}

/// <summary>
/// 保存投影算子
/// </summary>
/// <param name="queryplan"></param>
/// <param name="projection"></param>
/// <returns></returns>
int queryplanPushProjectionOperator(struct Queryplan* queryplan, struct Projection* projection)
{
	queryplan->projection = projection;

	return GNCDB_SUCCESS;
}

/// <summary>
/// 将各类结构体的数据均转化为字符串
/// </summary>
/// <param name="queryplan"></param>
/// <param name="fieldNameArray"></param>
/// <param name="fieldArray"></param>
/// <returns></returns>
//int structConverChar(CallBack callback, struct varArrayList* fieldNameArray, struct varArrayList* fieldArray){
//    return 0;
//}
int structConverChar(CallBack callback,TableSchema* tableSchema, struct varArrayList* fieldNameArray, BYTE* leafRecord, varArrayList* joinColumnOffsetArray)
{
	char** fieldName = NULL;
	char** fieldValue = NULL;
	int i = 0;
	int index = 0;

	char* name = NULL;
	Column* column = NULL;
	char string[255] = {0};
	int intValue;
	double realValue;
	int offset = 0;
	// Field* field = NULL;
	// IntField* intField = NULL;
	// RealField* realField = NULL;
	// VarCharField* varCharField = NULL;
	// BlobField* blobField = NULL;
    if (fieldNameArray == NULL || leafRecord == NULL || fieldNameArray->elementCount == 0)
    {
        return GNCDB_PARAMNULL;
    }
	/* 创建属性名的字符串数组 */
	fieldName = (char**)my_malloc(BYTES_POINTER * (fieldNameArray->elementCount));
	if (fieldName == NULL)
	{
		return GNCDB_SPACE_LACK;
	}
	memset(fieldName, 0, BYTES_POINTER * fieldNameArray->elementCount);
	/* 创建数据的字符串数组 */
	fieldValue = (char**)my_malloc(BYTES_POINTER * fieldNameArray->elementCount);
	if (fieldValue == NULL)
	{
		return GNCDB_SPACE_LACK;
	}
	memset(fieldValue, 0, BYTES_POINTER * fieldNameArray->elementCount);

	for (i = 0; i < fieldNameArray->elementCount; ++i)
	{
		name = varArrayListGetPointer(fieldNameArray, i);
		index = tableSchemaGetIndex(tableSchema, name);
		column = varArrayListGetPointer(tableSchema->columnList, index);
		if (name == NULL)
		{
			return GNCDB_MEM;
		}
		fieldName[i] = (char*)my_malloc(strlen(name) + 1);
		if (fieldName[i] == NULL)
		{
			return GNCDB_SPACE_LACK;
		}
		strcpy(fieldName[i], name);

		if(joinColumnOffsetArray != NULL){
			offset = *(int*)varArrayListGet(joinColumnOffsetArray, index);
		}
		else
			offset = column->offset;

		// field = varArrayListGetPointer(fieldArray, i);
		if (column->fieldType == FIELDTYPE_INTEGER)
		{
			// intField = (IntField*)field;
			fieldValue[i] = my_malloc(INTLEN);
			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			// itoa(intField->value, fieldValue[i], 10);
			memcpy(&intValue, leafRecord + offset, INT_SIZE);
			sprintf(fieldValue[i], "%d", intValue);
			// sprintf(fieldValue[i], "%d", intField->value);
		}
		else if (column->fieldType == FIELDTYPE_REAL)
		{
			// realField = (RealField*)field;
			fieldValue[i] = my_malloc(DOUBLELEN);
			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			// sprintf(fieldValue[i], "%f", realField->value);
			memcpy(&realValue, leafRecord + offset, DOUBLE_SIZE);
			sprintf(fieldValue[i], "%lf", realValue);
		}
		else if (column->fieldType == FIELDTYPE_VARCHAR)
		{
			// varCharField = (VarCharField*)field;
			//fieldValue[i] = my_malloc((*varCharField)->baseInfo->fieldSize);
			memcpy(string, leafRecord + offset, column->columnConstraint->maxValue);
			fieldValue[i] = my_malloc(strlen(string) + 1);

			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			memcpy(fieldValue[i], string, strlen(string) + 1);
			// strcpy(fieldValue[i], varCharField->value);

		}
		else if (column->fieldType == FIELDTYPE_BLOB)
		{
			// blobField = (BlobField*)field;
			fieldValue[i] = my_malloc(INTLEN);
			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			memcpy(&intValue, leafRecord + offset + INT_SIZE, INT_SIZE);
			sprintf(fieldValue[i], "%d", intValue);
			//itoa(blobField->size, fieldValue[i], 10);
			// sprintf(fieldValue[i], "%d", blobField->size);
		}
	}

	if (callback != NULL)
	{
		callback(fieldNameArray->elementCount, fieldName, fieldValue);
	}

	for (i = 0; i < fieldNameArray->elementCount; ++i)
	{
		if (fieldName[i] != NULL)
		{
			my_free(fieldName[i]);
            fieldName[i] = NULL;
		}
		if (fieldValue[i] != NULL)
		{
			my_free(fieldValue[i]);
            fieldValue[i] = NULL;
		}
	}
	my_free(fieldName);
    fieldName = NULL;
	my_free(fieldValue);
    fieldValue = NULL;

	return GNCDB_SUCCESS;
}

int structConverCharForJoin(CallBack callback,TableSchema* tableSchema, struct varArrayList* fieldNameArray, BYTE* leafRecord)
{
	char** fieldName = NULL;
	char** fieldValue = NULL;
	int i = 0;
	int index = 0;

	char* name = NULL;
	Column* column = NULL;
	char string[255] = {0};
	int intValue;
	double realValue;
	int offset = 0;
	// Field* field = NULL;
	// IntField* intField = NULL;
	// RealField* realField = NULL;
	// VarCharField* varCharField = NULL;
	// BlobField* blobField = NULL;
    if (fieldNameArray == NULL || leafRecord == NULL || fieldNameArray->elementCount == 0)
    {
        return GNCDB_PARAMNULL;
    }
	/* 创建属性名的字符串数组 */
	fieldName = (char**)my_malloc(BYTES_POINTER * (fieldNameArray->elementCount));
	if (fieldName == NULL)
	{
		return GNCDB_SPACE_LACK;
	}
	memset(fieldName, 0, BYTES_POINTER * fieldNameArray->elementCount);
	/* 创建数据的字符串数组 */
	fieldValue = (char**)my_malloc(BYTES_POINTER * fieldNameArray->elementCount);
	if (fieldValue == NULL)
	{
		return GNCDB_SPACE_LACK;
	}
	memset(fieldValue, 0, BYTES_POINTER * fieldNameArray->elementCount);
	offset = GET_BITMAP_LENGTH(fieldNameArray->elementCount);
	for (i = 0; i < fieldNameArray->elementCount; ++i)
	{
		name = varArrayListGetPointer(fieldNameArray, i);
		index = tableSchemaGetIndex(tableSchema, name);
		column = varArrayListGetPointer(tableSchema->columnList, index);
		if (name == NULL)
		{
			return GNCDB_MEM;
		}
		fieldName[i] = (char*)my_malloc(strlen(name) + 1);
		if (fieldName[i] == NULL)
		{
			return GNCDB_SPACE_LACK;
		}
		strcpy(fieldName[i], name);

		// field = varArrayListGetPointer(fieldArray, i);
		if (column->fieldType == FIELDTYPE_INTEGER)
		{
			// intField = (IntField*)field;
			fieldValue[i] = my_malloc(INTLEN);
			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			//itoa(intField->value, fieldValue[i], 10);
			memcpy(&intValue, leafRecord + offset, INT_SIZE);
			offset += INT_SIZE;
			sprintf(fieldValue[i], "%d", intValue);
			// sprintf(fieldValue[i], "%d", intField->value);
		}
		else if (column->fieldType == FIELDTYPE_REAL)
		{
			// realField = (RealField*)field;
			fieldValue[i] = my_malloc(DOUBLELEN);
			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			// sprintf(fieldValue[i], "%f", realField->value);
			memcpy(&realValue, leafRecord + offset, DOUBLE_SIZE);
			offset += DOUBLE_SIZE;
			sprintf(fieldValue[i], "%lf", realValue);
		}
		else if (column->fieldType == FIELDTYPE_VARCHAR)
		{
			// varCharField = (VarCharField*)field;
			//fieldValue[i] = my_malloc((*varCharField)->baseInfo->fieldSize);
			memcpy(string, leafRecord + offset, column->columnConstraint->maxValue);
			offset += column->columnConstraint->maxValue;
			fieldValue[i] = my_malloc(strlen(string) + 1);

			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			memcpy(fieldValue[i], string, strlen(string) + 1);
			// strcpy(fieldValue[i], varCharField->value);

		}
		else if (column->fieldType == FIELDTYPE_BLOB)
		{
			// blobField = (BlobField*)field;
			fieldValue[i] = my_malloc(INTLEN);
			if (fieldValue[i] == NULL)
			{
				return GNCDB_SPACE_LACK;
			}
			//itoa(blobField->size, fieldValue[i], 10);
			// sprintf(fieldValue[i], "%d", blobField->size);
		}
	}

	if (callback != NULL)
	{
		callback(fieldNameArray->elementCount, fieldName, fieldValue);
	}

	for (i = 0; i < fieldNameArray->elementCount; ++i)
	{
		if (fieldName[i] != NULL)
		{
			my_free(fieldName[i]);
            fieldName[i] = NULL;
		}
		if (fieldValue[i] != NULL)
		{
			my_free(fieldValue[i]);
            fieldValue[i] = NULL;
		}
	}
	my_free(fieldName);
    fieldName = NULL;
	my_free(fieldValue);
    fieldValue = NULL;

	return GNCDB_SUCCESS;
}



/// <summary>
/// 执行所有算子
/// </summary>
/// <param name="queryplan"></param>
/// <param name="db"></param>
/// <returns></returns>
int queryplanExecute(struct Queryplan* queryplan, int* affectedRows, BYTE** recordReturn, struct GNCDB* db,BtreeTable* btreeTable)
{
	int rc = 0;
	char* tableName = NULL;
	Catalog* catalog = NULL;
	TableSchema* tableSchema = NULL;
	Scan* scan = NULL;
	// Tuple* tuple = NULL;
	BYTE* record = NULL;
	Filter* filter = NULL;
	Join* join = NULL;
	TableSchema* tableSchemaOther = NULL;
	Scan* otherScan = NULL;
	Filter* otherFilter = NULL;
	TableSchema* newTableSchema = NULL;
	// Tuple* tupleJoin = NULL;
	// Tuple* tupleOther = NULL;
	BYTE* recordOther = NULL;
	BYTE* recordJoin = NULL;
	varArrayList* joinColumnOffsetArray = NULL;

	/* 取第一个表 */
	tableName = varArrayListGetPointer(queryplan->tableArray, 0);
	if (tableName == NULL)
	{
		return GNCDB_PARAMNULL;
	}

	catalog = db->catalog;

	/* 获取主表tableschema */
	tableSchema = getTableSchema(catalog, tableName);
    if(tableSchema == NULL)
    {
        return GNCDB_TABLESCHEMA_NOT_FOUND;
    }

	/* 获取主表的tuple */
	scan = hashMapGet(queryplan->scanMap, tableName);
	record = operatorScan(db, scan);
	if (record == NULL)
	{
		*recordReturn = NULL;
		return GNCDB_SUCCESS;
	}

	/* 拿取filter执行filter */
	filter = hashMapGet(queryplan->filterMap, tableName);
	// varArrayListDestroy(&(filter->conditionArray));
	if (!operatorFilter(filter, record, tableSchema,catalog))
	{
		*recordReturn = NULL;
		return GNCDB_SUCCESS;
	}
	/* 此时说明tuple符合过滤条件 */
	*recordReturn = record;

	/* 是否执行join */
	if (queryplan->joinArr->elementCount == 0)
	{
		/* 单表查询 */
		/* 是否执行projection */
        rc = processQueryResult(queryplan, db, tableSchema, record,btreeTable,NULL);
        if (rc)
        {
            return rc;
        }
		if (affectedRows != NULL)
		{
			*affectedRows += 1;
		}
	}
	else
	{
		/* 多表查询 */
		/* 暂时只支持连接两个表 */
		join = varArrayListGetPointer(queryplan->joinArr, 0);

		tableSchemaOther = getTableSchema(catalog, join->tableName2);
		/* 拿取第二张表的scan和filter算子 */
		otherScan = hashMapGet(queryplan->scanMap, join->tableName2);
		if (otherScan == NULL)
		{
			return GNCDB_PARAMNULL;
		}
		otherFilter = hashMapGet(queryplan->filterMap, join->tableName2);
		if (otherFilter == NULL)
		{
			return GNCDB_PARAMNULL;
		}


		/* 合并二个表的tableSchema */
		joinColumnOffsetArray = varArrayListCreate(DISORDER, sizeof(int), tableSchema->columnNum + tableSchemaOther->columnNum, NULL, NULL);
		rc = tableSchemaMerge(&newTableSchema, tableSchema, tableSchemaOther, btreeTable, joinColumnOffsetArray);
		if (rc != GNCDB_SUCCESS)
		{
			varArrayListDestroy(&joinColumnOffsetArray);
			return rc;
		}

		rc = resetScan(otherScan, db, queryplan->tx);
		if (rc != GNCDB_SUCCESS)
		{
			varArrayListDestroy(&joinColumnOffsetArray);
			tableSchemaDestroy(newTableSchema);
			return rc;
		}

		while (hasNextTuple(otherScan))
		{
			// tupleOther = operatorScan(db, otherScan);
			recordOther = operatorScan(db, otherScan);
			if (!operatorFilter(otherFilter, recordOther, tableSchemaOther,catalog))
			{
				continue;
			}
			rc = operatorJoin(db, &recordJoin, tableSchema, record, tableSchemaOther, recordOther, join, queryplan->tx);
			if (rc == GNCDB_UN_JOIN)
			{
				continue;
			}
			if (rc != GNCDB_SUCCESS)
			{
				varArrayListDestroy(&joinColumnOffsetArray);
				tableSchemaDestroy(newTableSchema);
				return rc;
			}

			/* 是否执行projection */
			if (affectedRows != NULL)
			{
				*affectedRows += 1;
			}
            rc = processQueryResult(queryplan, db, newTableSchema, recordJoin,btreeTable,joinColumnOffsetArray);
			// varArrayListDestroy(&(tupleJoin->fieldArray));
        	// my_free(tupleJoin);
			my_free(recordJoin);
            if (rc)
            {
				varArrayListDestroy(&joinColumnOffsetArray);
				tableSchemaDestroy(newTableSchema);
                return rc;
            }
		}
		// tableSchemaDestroy(newTableSchema);
		varArrayListDestroy(&joinColumnOffsetArray);

	}

	return GNCDB_SUCCESS;
}



/// <summary>
/// 销毁一个查询计划
/// </summary>
/// <param name="queryplan">结构体指针</param>
/// <returns>状态码</returns>
int queryplanDestroy(struct Queryplan* queryplan)
{
	HashMapIterator* iterator1 = NULL;
	HashMapIterator* iterator2 = NULL;
	int index = 0;
	Scan* scan = NULL;
	Filter* filter = NULL;
	Join* join = NULL;
    int i = 0;
    if (queryplan == NULL)
    {
        return GNCDB_PARAMNULL;
    }
	/* 销毁表名 */
	if (queryplan->tableArray != NULL)
	{
		varArrayListDestroy(&(queryplan->tableArray));
	}
	/* 销毁scan算子 */
	iterator1 = createHashMapIterator(queryplan->scanMap);
	for (index = 0; hasNextHashMapIterator(iterator1); index++)
	{
		/* 迭代取出所有键值对 */
		iterator1 = nextHashMapIterator(iterator1);
		scan = iterator1->entry->value;
		scanDestroy(scan);
	}
	freeHashMapIterator(&iterator1);
	hashMapDestroy(&queryplan->scanMap);

	/* 销毁filter算子 */
	iterator2 = createHashMapIterator(queryplan->filterMap);
	for (index = 0; hasNextHashMapIterator(iterator2); index++)
	{
		/* 迭代取出所有键值对 */
		iterator2 = nextHashMapIterator(iterator2);
		filter = iterator2->entry->value;
		filterDestroy(filter);
	}
	freeHashMapIterator(&iterator2);
	hashMapDestroy(&queryplan->filterMap);
	
	/* 销毁join算子 */
	for (i = 0; i < queryplan->joinArr->elementCount; ++i)
	{
		join = varArrayListGetPointer(queryplan->joinArr, i);
		joinDestroy(join);
	}
	varArrayListDestroy(&queryplan->joinArr);
	/* 销毁projection算子 */
	if (queryplan->projection != NULL)
	{
		projectionDestroy(queryplan->projection);
	}
	my_free(queryplan);

	return GNCDB_SUCCESS;
}


int processQueryResult(Queryplan* queryplan, struct GNCDB* db, TableSchema* tableSchema, BYTE* leafRecord,BtreeTable* btreeTable, varArrayList* joinColumnOffsetArray)
{
    int i = 0;
    int rc = 0;
    varArrayList * fieldNameArray = NULL;
    // Tuple* newTuple = tuple;
	BYTE* newLeafRecord = leafRecord;
    Column * column = NULL;
    if (queryplan->projection != NULL && joinColumnOffsetArray == NULL)
    {
        rc = operatorProjection(db, tableSchema, &newLeafRecord, leafRecord, queryplan->projection, queryplan->tx,btreeTable);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        rc = structConverChar(queryplan->callback,tableSchema, queryplan->projection->fieldNameArray, newLeafRecord, joinColumnOffsetArray);
        if (rc)
        {
			// varArrayListDestroy(&(newTuple->fieldArray));
        	my_free(newLeafRecord);
            return rc;
        }
        // varArrayListDestroy(&(newTuple->fieldArray));
        my_free(newLeafRecord);
    }
	/*join投影*/
	else if(queryplan->projection != NULL){
		rc = operatorJoinProjection(db, tableSchema, &newLeafRecord, leafRecord, queryplan->projection, queryplan->tx,joinColumnOffsetArray);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        rc = structConverCharForJoin(queryplan->callback,tableSchema, queryplan->projection->fieldNameArray, newLeafRecord);
        if (rc)
        {
        	my_free(newLeafRecord);
            return rc;
        }
        my_free(newLeafRecord);
	}
    else
    {
        fieldNameArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (fieldNameArray == NULL)
        {
            return GNCDB_SPACE_LACK;
        }
        for (i = 0; i < tableSchema->columnList->elementCount; ++i)
        {
            column = varArrayListGetPointer(tableSchema->columnList, i);
            varArrayListAddPointer(fieldNameArray, column->fieldName);
        }
        rc = structConverChar(queryplan->callback,tableSchema, fieldNameArray, newLeafRecord,joinColumnOffsetArray);
        if (rc)
        {
			varArrayListDestroy(&fieldNameArray);
            return rc;
        }
        varArrayListDestroy(&fieldNameArray);
    }

    return GNCDB_SUCCESS;
}
/// <summary>
/// 执行所有算子,R树
/// </summary>
/// <param name="queryplan"></param>
/// <param name="db"></param>
/// <returns></returns>
int queryplanExecuteRtree(struct Queryplan* queryplan, int* affectedRows, Tuple** tupleReturn, struct GNCDB* db)
{
	// int rc = 0;
	// char* tableName = NULL;
	// char* depTableName = NULL;
	// char* depTableNameOther = NULL;
	// //Catalog* catalog = NULL;
	// TableSchema* tableSchema = NULL;
	// RtreeScan* scan = NULL;
	// Tuple* tuple = NULL;
	// Filter* filter = NULL;
	// Tuple* newTuple = NULL;
	// Column* column = NULL;
	// int i = 0;
	// varArrayList* fieldNameArray = NULL;
	// Join* join = NULL;
	// TableSchema* tableSchemaOther = NULL;
	// RtreeScan* otherScan = NULL;
	// Filter* otherFilter = NULL;
	// TableSchema* newTableSchema = NULL;
	// Tuple* tupleJoin = NULL;
	// Tuple* tupleOther = NULL;

	// /* 取第一个表 */
	// tableName = varArrayListGetPointer(queryplan->tableArray, 0);
	// if (tableName == NULL)
	// {
	// 	return GNCDB_PARAMNULL;
	// }


	// /* 获取主表tableschema */
	// /* TODO?获取所属索引依赖表名函数，建议在R树tableSchema加入字段从而提高效率 */
	// rc = catalogGetDependentTableName(db, scan->tableName, &depTableName, NULL);
	// if (rc != GNCDB_SUCCESS) {
	// 	return rc;
	// }
	// /* 获取tableSchema */
	// tableSchema = getTableSchema(db->catalog, depTableName);
	// if (tableSchema == NULL) {
	// 	return GNCDB_NOT_FOUND;
	// }

	// /* 获取主表的tuple */
	// scan = hashMapGet(queryplan->scanMap, tableName);
	// tuple = operatorRtreeScan(db, scan);

	// if (tuple == NULL)
	// {
	// 	*tupleReturn = NULL;
	// 	return GNCDB_SUCCESS;
	// }

	// /* 拿取filter执行filter */
	// filter = hashMapGet(queryplan->filterMap, tableName);
	// // varArrayListDestroy(&(filter->conditionArray));
	// if (!operatorFilter(filter, tuple, tableSchema))
	// {
	// 	*tupleReturn = NULL;
	// 	return GNCDB_SUCCESS;
	// }

	// /* 此时说明tuple符合过滤条件 */
	// newTuple = tuple;
	// *tupleReturn = tuple;

	// /* 是否执行join */
	// if (queryplan->joinArr->elementCount == 0)
	// {
	// 	/* 单表查询 */
	// 	/* 是否执行projection */
	// 	if (queryplan->projection != NULL)
	// 	{
	// 		rc = operatorProjection(db, tableSchema, &newTuple, tuple, queryplan->projection, queryplan->tx);
	// 		if (rc != GNCDB_SUCCESS)
	// 		{
	// 			return rc;
	// 		}
	// 		rc = structConverChar(queryplan->callback, queryplan->projection->fieldNameArray, newTuple->fieldArray);
	// 		if (rc)
	// 		{
	// 			return rc;
	// 		}
	// 		varArrayListDestroy(&(newTuple->fieldArray));
	// 		my_free(newTuple);
	// 	}
	// 	else
	// 	{
	// 		fieldNameArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// 		if (fieldNameArray == NULL)
	// 		{
	// 			return GNCDB_SPACE_LACK;
	// 		}
	// 		for (i = 0; i < tableSchema->columnList->elementCount; ++i)
	// 		{
	// 			column = varArrayListGetPointer(tableSchema->columnList, i);
	// 			varArrayListAddPointer(fieldNameArray, column->fieldName);
	// 		}
	// 		rc = structConverChar(queryplan->callback, fieldNameArray, newTuple->fieldArray);
	// 		if (rc)
	// 		{
	// 			return rc;
	// 		}
	// 		varArrayListDestroy(&fieldNameArray);
	// 	}
	// 	if (affectedRows != NULL)
	// 	{
	// 		*affectedRows += 1;
	// 	}
	// }
	// else
	// {
	// 	/* 多表查询 */
	// 	/* 暂时只支持连接两个表 */
	// 	join = varArrayListGetPointer(queryplan->joinArr, 0);

	// 	rc = catalogGetDependentTableName(db, scan->tableName, &depTableNameOther, NULL);
	// 	if (rc != GNCDB_SUCCESS) {
	// 		return rc;
	// 	}
	// 	/* 获取tableSchema */
	// 	tableSchemaOther = getTableSchema(db->catalog, depTableNameOther);
	// 	if (tableSchemaOther == NULL) {
	// 		return GNCDB_NOT_FOUND;
	// 	}
	// 	/* 拿取第二张表的scan和filter算子 */
	// 	otherScan = hashMapGet(queryplan->scanMap, join->tableName2);
	// 	if (otherScan == NULL)
	// 	{
	// 		return GNCDB_PARAMNULL;
	// 	}
	// 	otherFilter = hashMapGet(queryplan->filterMap, join->tableName2);
	// 	if (otherFilter == NULL)
	// 	{
	// 		return GNCDB_PARAMNULL;
	// 	}

	// 	/* 合并二个表的tableSchema */
	// 	rc = tableSchemaMerge(&newTableSchema, tableSchema, tableSchemaOther);
	// 	if (rc != GNCDB_SUCCESS)
	// 	{
	// 		return rc;
	// 	}

	// 	rc = resetRtreeScan(otherScan, db, queryplan->tx);
	// 	if (rc != GNCDB_SUCCESS)
	// 	{
	// 		return rc;
	// 	}

	// 	while (rtreeHasNextTuple(otherScan))
	// 	{
	// 		tupleOther = operatorRtreeScan(db, otherScan);
	// 		if (!operatorFilter(otherFilter, tupleOther, tableSchemaOther))
	// 		{
	// 			continue;
	// 		}
	// 		rc = operatorJoin(db, &tupleJoin, tableSchema, tuple, tableSchemaOther, tupleOther, join, queryplan->tx);
	// 		if (rc == GNCDB_UN_JOIN)
	// 		{
	// 			continue;
	// 		}
	// 		if (rc != GNCDB_SUCCESS)
	// 		{
	// 			return rc;
	// 		}

	// 		/* 是否执行projection */
	// 		if (affectedRows != NULL)
	// 		{
	// 			*affectedRows += 1;
	// 		}
	// 		if (queryplan->projection != NULL)
	// 		{
	// 			rc = operatorProjection(db, newTableSchema, &newTuple, tupleJoin, queryplan->projection, queryplan->tx);
	// 			if (rc != GNCDB_SUCCESS)
	// 			{
	// 				return rc;
	// 			}
	// 			rc = structConverChar(queryplan->callback, queryplan->projection->fieldNameArray, newTuple->fieldArray);
	// 			if (rc)
	// 			{
	// 				return rc;
	// 			}
	// 			varArrayListDestroy(&(tupleJoin->fieldArray));
	// 			my_free(tupleJoin);
	// 			varArrayListDestroy(&(newTuple->fieldArray));
	// 			my_free(newTuple);
	// 		}
	// 		else
	// 		{
	// 			fieldNameArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// 			if (fieldNameArray == NULL)
	// 			{
	// 				return GNCDB_SPACE_LACK;
	// 			}
	// 			for (i = 0; i < newTableSchema->columnList->elementCount; ++i)
	// 			{
	// 				column = varArrayListGetPointer(newTableSchema->columnList, i);
	// 				varArrayListAddPointer(fieldNameArray, column->fieldName);
	// 			}

	// 			if (tupleJoin == NULL)
	// 			{
	// 				break;
	// 			}
	// 			rc = structConverChar(queryplan->callback, fieldNameArray, tupleJoin->fieldArray);
	// 			if (rc)
	// 			{
	// 				return rc;
	// 			}
	// 			varArrayListDestroy(&(tupleJoin->fieldArray));
	// 			my_free(tupleJoin);
	// 			varArrayListDestroy(&fieldNameArray);

	// 		}
	// 	}
	// 	tableSchemaDestroy(newTableSchema);

	// }
	return GNCDB_SUCCESS;
}
/**
 * @brief 执行哈希索引查询计划
 * @param queryplan 查询计划指针
 * @param affectedRows 受影响行数指针
 * @param tupleReturn 返回元组指针的指针
 * @param db 数据库指针
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int queryplanExecuteHash(struct Queryplan *queryplan, int *affectedRows, Tuple **tupleReturn, struct GNCDB *db)
{
  // /* 所有变量声明放在函数开头 */
  // int          rc               = 0;
  // char        *tableName        = NULL;
  // Catalog     *catalog          = NULL;
  // TableSchema *tableSchema      = NULL;
  // HashScan    *hashScan         = NULL;
  // BYTE       *record            = NULL;
  // Filter      *filter           = NULL;
  // Join        *join             = NULL;
  // TableSchema *tableSchemaOther = NULL;
  // Scan        *otherScan        = NULL;
  // Filter      *otherFilter      = NULL;
  // TableSchema *newTableSchema   = NULL;
  // Tuple       *tupleJoin        = NULL;
  // Tuple       *tupleOther       = NULL;
	// BtreeTable *table1 = NULL;

  // /* 参数检查 */
  // if (queryplan == NULL || db == NULL) {
  //   return GNCDB_PARAMNULL;
  // }

  // /* 取第一个表 */
  // tableName = varArrayListGetPointer(queryplan->tableArray, 0);
  // if (tableName == NULL) {
  //   return GNCDB_PARAMNULL;
  // }

  // catalog = db->catalog;

  // /* 获取主表tableschema */
  // tableSchema = getTableSchema(catalog, tableName);
  // if (tableSchema == NULL) {
  //   return GNCDB_TABLESCHEMA_NOT_FOUND;
  // }

  // /* 获取主表的tuple */
  // hashScan = hashMapGet(queryplan->scanMap, tableName);
  // if (hashScan == NULL) {
  //   return GNCDB_PARAMNULL;
  // }

  // /* 初始化受影响行数 */
  // if (affectedRows != NULL) {
  //   *affectedRows = 0;
  // }

  // /* 检查是否有下一条记录 */
  // while (hashScanHasNext(hashScan, db)) {
  //   record = operatorHashScan(db, hashScan);
  //   if (record == NULL) {
  //     continue;
  //   }

  //   /* 拿取filter执行filter */
  //   filter = hashMapGet(queryplan->filterMap, tableName);
  //   if (filter != NULL && !operatorFilter(filter, record, tableSchema, catalog)) {
  //     /* 不符合过滤条件，释放tuple并继续 */
  //     RecordDestroy(&record);
  //     my_free(record);
  //     continue;
  //   }

  //   /* 此时说明tuple符合过滤条件 */
  //   *tupleReturn = record;

  //   /* 是否执行join */
  //   if (queryplan->joinArr->elementCount == 0) {
  //     /* 单表查询 */
  //     /* 是否执行projection */
  //     rc = processQueryResult(queryplan, db, tableSchema, record, b);
  //     if (rc) {
  //       return rc;
  //     }
  //     if (affectedRows != NULL) {
  //       *affectedRows += 1;
  //     }
  //   } else {
  //     /* 多表查询 */
  //     /* 暂时只支持连接两个表 */
  //     join = varArrayListGetPointer(queryplan->joinArr, 0);
  //     if (join == NULL) {
  //       return GNCDB_PARAMNULL;
  //     }

  //     tableSchemaOther = getTableSchema(catalog, join->tableName2);
  //     if (tableSchemaOther == NULL) {
  //       return GNCDB_TABLESCHEMA_NOT_FOUND;
  //     }

  //     /* 拿取第二张表的scan和filter算子 */
  //     otherScan = hashMapGet(queryplan->scanMap, join->tableName2);
  //     if (otherScan == NULL) {
  //       return GNCDB_PARAMNULL;
  //     }
  //     otherFilter = hashMapGet(queryplan->filterMap, join->tableName2);
  //     if (otherFilter == NULL) {
  //       return GNCDB_PARAMNULL;
  //     }

  //     /* 合并二个表的tableSchema */
  //     rc = tableSchemaMerge(&newTableSchema, tableSchema, tableSchemaOther);
  //     if (rc != GNCDB_SUCCESS) {
  //       return rc;
  //     }

  //     /* 重置第二个表的扫描 */
  //     rc = resetScan(otherScan, db, queryplan->tx);
  //     if (rc != GNCDB_SUCCESS) {
  //       tableSchemaDestroy(newTableSchema);
  //       return rc;
  //     }

  //     /* 遍历第二个表 */
  //     while (hasNextTuple(otherScan)) {
  //       tupleOther = operatorScan(db, otherScan);
  //       if (tupleOther == NULL) {
  //         continue;
  //       }

  //       /* 过滤第二个表的记录 */
  //       if (!operatorFilter(otherFilter, tupleOther, tableSchemaOther)) {
  //         varArrayListDestroy(&(tupleOther->fieldArray));
  //         my_free(tupleOther);
  //         continue;
  //       }

  //       /* 执行连接 */
  //       rc = operatorJoin(db, &tupleJoin, tableSchema, record, tableSchemaOther, tupleOther, join, queryplan->tx);
  //       if (rc == GNCDB_UN_JOIN) {
  //         varArrayListDestroy(&(tupleOther->fieldArray));
  //         my_free(tupleOther);
  //         continue;
  //       }
  //       if (rc != GNCDB_SUCCESS) {
  //         varArrayListDestroy(&(tupleOther->fieldArray));
  //         my_free(tupleOther);
  //         tableSchemaDestroy(newTableSchema);
  //         return rc;
  //       }

  //       /* 处理连接结果 */
  //       if (affectedRows != NULL) {
  //         *affectedRows += 1;
  //       }
  //       rc = processQueryResult(queryplan, db, newTableSchema, tupleJoin);

  //       /* 释放连接结果 */
  //       varArrayListDestroy(&(tupleJoin->fieldArray));
  //       my_free(tupleJoin);

  //       /* 释放第二个表的记录 */
  //       varArrayListDestroy(&(tupleOther->fieldArray));
  //       my_free(tupleOther);

  //       if (rc) {
  //         tableSchemaDestroy(newTableSchema);
  //         return rc;
  //       }
  //     }

  //     /* 释放合并的表结构 */
  //     tableSchemaDestroy(newTableSchema);
  //   }

  //   /* 释放当前记录 */
  //   varArrayListDestroy(&(record->fieldArray));
  //   my_free(record);
  // }

  return GNCDB_SUCCESS;
}