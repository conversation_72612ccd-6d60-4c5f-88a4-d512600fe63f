#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "groupby_physical_operator.h"
#include "tuple.h"
#include "typedefine.h"
#include "lookaside_mem.h"
#include "utils.h"
#include "value.h"
#include "gncdb.h"
#include <assert.h>
#include <string.h>
bool isFieldMeta(Meta *meta)
{
  ColMeta *colMeta = NULL;
  if (meta == NULL) {
    return false;
  }
  if (meta->type == NormalField) {
    colMeta = (ColMeta *)meta;
    if (colMeta->aggrType == AGG_INVALID) {
      return true;
    }
  }
  return false;
}

ColMeta *ColMetaCopy(ColMeta *colMeta)
{
  ColMeta *newColMeta = NULL;
  if (colMeta == NULL) {
    return NULL;
  }

  newColMeta = (ColMeta *)my_malloc(sizeof(ColMeta));
  if (newColMeta == NULL) {
    return NULL;
  }

  memcpy(newColMeta, colMeta, sizeof(ColMeta));
  newColMeta->owned = false;  // 拷贝的 ColMeta 是不拥有的，调用者不需要释放
  return newColMeta;
}

ColMeta *ColMetaDeepCopy(ColMeta *colMeta)
{
  ColMeta *newColMeta = NULL;
  if (colMeta == NULL) {
    return NULL;
  }

  newColMeta = (ColMeta *)my_malloc(sizeof(ColMeta));
  if (newColMeta == NULL) {
    return NULL;
  }
  if (colMeta->tableName != NULL) {
    newColMeta->tableName = my_strdup(colMeta->tableName);
    if (newColMeta->tableName == NULL) {
      my_free(newColMeta);
      return NULL;
    }
  }
  if (colMeta->name != NULL) {
    newColMeta->name = my_strdup(colMeta->name);
    if (newColMeta->name == NULL) {
      my_free(newColMeta->tableName);
      my_free(newColMeta);
      return NULL;
    }
  }
  newColMeta->type         = colMeta->type;
  newColMeta->fieldType    = colMeta->fieldType;
  newColMeta->len          = colMeta->len;
  newColMeta->offset       = colMeta->offset;
  newColMeta->aggrType     = colMeta->aggrType;
  newColMeta->isBitMap     = colMeta->isBitMap;
  newColMeta->index        = colMeta->index;         // 索引列的索引
  newColMeta->bitmapOffset = colMeta->bitmapOffset;  // 位图的偏移
  newColMeta->owned        = true;                   // 深拷贝的 ColMeta 是拥有的，调用者需要释放

  return newColMeta;
}

void ColMetaDestroy(ColMeta **colMeta)
{
  if (colMeta == NULL || *colMeta == NULL) {
    return;
  }
  if ((*colMeta)->owned) {
    if ((*colMeta)->tableName != NULL) {
      my_free((*colMeta)->tableName);
      (*colMeta)->tableName = NULL;
    }
    if ((*colMeta)->name != NULL) {
      my_free((*colMeta)->name);
      (*colMeta)->name = NULL;
    }
  }
  my_free(*colMeta);
  *colMeta = NULL;
}

void ColMetaPtrDestroy(void *data)
{
  ColMeta **colMeta = (ColMeta **)data;
  ColMetaDestroy(colMeta);
}

CompositeMeta *CompositeMetaCopy(CompositeMeta *compositeMeta)
{
  CompositeMeta *newCompositeMeta = NULL;
  if (compositeMeta == NULL) {
    return NULL;
  }

  newCompositeMeta = (CompositeMeta *)my_malloc0(sizeof(CompositeMeta));
  if (newCompositeMeta == NULL) {
    return NULL;
  }

  memcpy(newCompositeMeta, compositeMeta, sizeof(CompositeMeta));
  newCompositeMeta->owned = false;  // 拷贝的 CompositeMeta 是不拥有的，调用者不需要释放
  return newCompositeMeta;
}

CompositeMeta *CompositeMetaDeepCopy(CompositeMeta *compositeMeta)
{
  CompositeMeta *newCompositeMeta = NULL;
  if (compositeMeta == NULL) {
    return NULL;
  }
  newCompositeMeta = (CompositeMeta *)my_malloc0(sizeof(CompositeMeta));
  if (newCompositeMeta == NULL) {
    return NULL;
  }
  if (compositeMeta->expr != NULL) {
    newCompositeMeta->expr = exprDeepCopy(compositeMeta->expr);
    if (newCompositeMeta->expr == NULL) {
      my_free(newCompositeMeta);
      return NULL;
    }
  }
  newCompositeMeta->type         = compositeMeta->type;
  newCompositeMeta->len          = compositeMeta->len;
  newCompositeMeta->offset       = compositeMeta->offset;
  newCompositeMeta->fieldType    = compositeMeta->fieldType;
  newCompositeMeta->index        = compositeMeta->index;         // 索引列的索引
  newCompositeMeta->bitmapOffset = compositeMeta->bitmapOffset;  // 位图的偏移，compositeMeta 也可以有位图
  newCompositeMeta->owned        = true;  // 深拷贝的 CompositeMeta 是拥有的，调用者需要释放
  return newCompositeMeta;
}

void CompositeMetaDestroy(CompositeMeta **compositeMeta)
{
  if (compositeMeta == NULL || *compositeMeta == NULL) {
    return;
  }
  if((*compositeMeta)->owned) {
    if ((*compositeMeta)->expr != NULL) {
      exprDestroy((*compositeMeta)->expr);
    }
  }
  my_free(*compositeMeta);
  *compositeMeta = NULL;
}

void CompositeMetaPtrDestroy(void *data)
{
  CompositeMeta **compositeMeta = (CompositeMeta **)data;
  CompositeMetaDestroy(compositeMeta);
}

Meta *MetaCopy(Meta *meta)
{
  if (meta == NULL) {
    return NULL;
  }
  if (meta->type == NormalField) {
    return (Meta *)ColMetaCopy((ColMeta *)meta);
  } else if (meta->type == CompositeField) {
    return (Meta *)CompositeMetaCopy((CompositeMeta *)meta);
  } else {
    assert(false);  // 不支持的类型
    return NULL;
  }
}

Meta *MetaDeepCopy(Meta *meta)
{
  Meta *newMeta = NULL;
  if (meta == NULL) {
    return NULL;
  }
  if (meta->type == NormalField) {
    newMeta = (Meta *)ColMetaDeepCopy((ColMeta *)meta);
  } else if (meta->type == CompositeField) {
    newMeta = (Meta *)CompositeMetaDeepCopy((CompositeMeta *)meta);
  } else {
    assert(false);
  }
  return newMeta;
}

void MetaDestroy(Meta **meta)
{
  if (meta == NULL || *meta == NULL) {
    return;
  }
  if ((*meta)->type == NormalField) {
    ColMetaDestroy((ColMeta **)meta);
  } else if ((*meta)->type == CompositeField) {
    CompositeMetaDestroy((CompositeMeta **)meta);
  } else {
    assert(false);
  }
}

void MetaPtrDestroy(void *data)
{
  Meta **meta = (Meta **)data;
  MetaDestroy(meta);
}

Record *RecordCreate(BYTE *data, int size)
{
  Record *record = (Record *)my_malloc(sizeof(Record));
  if (record == NULL) {
    return NULL;
  }
  record->size = size;
  record->data = (BYTE *)my_malloc(size);
  if (record->data == NULL) {
    my_free(record);
    return NULL;
  }
  memcpy(record->data, data, size);
  record->allocated = true;
  return record;
}

Record *RecordCreateFromLookaside(GNCDB* db, BYTE *data, int size)
{
  Record *record = (Record *)lookasideMalloc(db, sizeof(Record));
  if (record == NULL) {
    return NULL;
  }
  record->size = size;
  record->data = (BYTE *)lookasideMalloc(db, size);
  if (record->data == NULL) {
    lookasideFree(db, record);
    return NULL;
  }
  memcpy(record->data, data, size);
  record->allocated = true;
  return record;
}

Record *RecordCreateWithoutData(int size)
{
  Record *record = (Record *)my_malloc(sizeof(Record));
  if (unlikely(record == NULL)) {
    return NULL;
  }
  record->size = size;
  record->data = (BYTE *)my_malloc0(size);
  if (unlikely(record->data == NULL)) {
    my_free(record);
    return NULL;
  }
  record->allocated = true;
  return record;
}

void RecordDestroy(Record **record)
{
  if (record == NULL || *record == NULL) {
    return;
  }
  if ((*record)->allocated) {
    my_free((*record)->data);
  }
  my_free(*record);
  *record = NULL;
}

void RecordDestroyLookaside(GNCDB* db, Record **record) {
  if (record == NULL || *record == NULL) {
    return;
  }
  if ((*record)->allocated) {
    lookasideFree(db, (*record)->data);
  }
  lookasideFree(db, *record);
  *record = NULL;
}

void RecordPtrDestroy(void *data)
{
  Record **record = (Record **)data;
  if (record == NULL || *record == NULL) {
    return;
  }
  RecordDestroy(record);
}

Record *RecordDeepCopy(Record *record)
{
  Record *newRecord = NULL;
  if (record == NULL) {
    return NULL;
  }
  newRecord = (Record *)my_malloc0(sizeof(Record));
  if (newRecord == NULL) {
    return NULL;
  }
  newRecord->size = record->size;
  newRecord->data = (BYTE *)my_malloc0(record->size);
  if (newRecord->data == NULL) {
    my_free(newRecord);
    return NULL;
  }
  memcpy(newRecord->data, record->data, record->size);
  newRecord->allocated = true;
  return newRecord;
}

void RecordFillData(Record *record, BYTE *data_)
{
  if (record == NULL || data_ == NULL) {
    return;
  }
  memcpy(record->data, data_, record->size);
}

void RecordSetData(Record *record, int offset, void *src, int size)
{
  if (record == NULL || src == NULL) {
    return;
  }
  memmove(record->data + offset, src, size);
}

Record *RecordMerge(Record *head, Record *tail)
{
  int     newSize      = 0;
  Record *merge_record = NULL;
  if (tail == NULL || head == NULL) {
    return NULL;
  }
  if (tail->allocated != head->allocated) {  // 应该是都是分配了内存
    return NULL;
  }
  newSize      = tail->size + head->size;
  merge_record = (Record *)my_new0(Record);
  if (merge_record == NULL) {
    return NULL;
  }
  merge_record->data = (BYTE *)my_malloc0(newSize);
  if (merge_record->data == NULL) {
    my_free(merge_record);
    return NULL;
  }
  memcpy(merge_record->data, head->data, head->size);
  memcpy(merge_record->data + head->size, tail->data, tail->size);
  merge_record->size      = newSize;
  merge_record->allocated = true;
  return merge_record;
}

Record *RecordMergeFromLookaside(GNCDB*db, Record *head, Record *tail)
{
  int     newSize      = 0;
  Record *merge_record = NULL;
  if (tail == NULL || head == NULL) {
    return NULL;
  }
  if (tail->allocated != head->allocated) {  // 应该是都是分配了内存
    return NULL;
  }
  newSize      = tail->size + head->size;
  merge_record = (Record *)lookasideMalloc(db, sizeof(Record));
  if (merge_record == NULL) {
    return NULL;
  }
  merge_record->data = (BYTE *)lookasideMalloc(db, newSize);
  if (merge_record->data == NULL) {
    lookasideFree(db, merge_record);
    return NULL;
  }
  memcpy(merge_record->data, head->data, head->size);
  memcpy(merge_record->data + head->size, tail->data, tail->size);
  merge_record->size      = newSize;
  merge_record->allocated = true;
  return merge_record;
}

/**
 * @description: 申请一个新的List并合并输入的两个List，深拷贝ColMeta，调用者需释放
 * @param {varArrayList*} head
 * @param {varArrayList*} tail
 * @return {*}
 */
varArrayList *colMetaListMerge(varArrayList *head, varArrayList *tail)
{
  ColMeta *colMeta = varArrayListGetPointer(head, head->elementCount - 1);  // 获取第一个List的最后一个ColMeta
  int      offset  = colMeta->offset + colMeta->len;  // 合并之后array2的ColMeta应该增加的偏移量
  varArrayList *array = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, ColMetaPtrDestroy);
  for (int i = 0; i < head->elementCount; i++) {
    colMeta = ColMetaDeepCopy((ColMeta *)varArrayListGetPointer(head, i));
    varArrayListAddPointer(array, colMeta);
  }
  for (int i = 0; i < tail->elementCount; i++) {
    colMeta = ColMetaDeepCopy((ColMeta *)varArrayListGetPointer(tail, i));
    colMeta->offset += offset;  // 增加偏移量（array1的字段总大小）
    colMeta->bitmapOffset = offset;
    varArrayListAddPointer(array, colMeta);
  }
  return array;
}

int getRecordLength(varArrayList *cols, int *len)
{
  ColMeta *col = NULL;
  int      rc  = GNCDB_SUCCESS;
  if (cols == NULL || len == NULL) {
    rc = GNCDB_PARAMNULL;
  } else if (cols->elementCount == 0) {
    *len = 0;
  } else {
    col  = varArrayListGetPointer(cols, cols->elementCount - 1);
    *len = col->len + col->offset;
  }
  return rc;
}

int rowTupleCellAt(RowTuple *rowTuple, int index, Value *cell)
{
  Column      *column = NULL;
  TableSchema *ts     = NULL;
  int          valueInt;
  double       valueDouble;
  char        *valueString;
  int          len;
  if (rowTuple == NULL || cell == NULL) {
    return GNCDB_PARAMNULL;
  }
  ts = rowTuple->tableSchema;
  if (index < 0 || index >= ts->columnList->elementCount) {
    printf("invalid argument. index=%d, speces->elementCount=%d\n", index, ts->columnList->elementCount);
    return GNCDB_PARAM_INVALID;
  }
  //原逻辑暂未使用位图，先默认没有为NULL的字段
  // if(leafTupleGetBitMap(rowTuple->record, index, ts->columnList->elementCount) == 0) {
  //   valueSetNull(cell);
  //   return GNCDB_SUCCESS;
  // }

  column = (Column *)varArrayListGetPointer(ts->columnList, index);
  if (column == NULL) {
    return GNCDB_INTERNAL;
  }

  switch (column->fieldType) {
    case FIELDTYPE_INTEGER:
      memcpy(&valueInt, rowTuple->record + column->offset, INT_SIZE);
      valueSetInt(cell, valueInt);
      break;
    case FIELDTYPE_REAL:
      memcpy(&valueDouble, rowTuple->record + column->offset, DOUBLE_SIZE);
      valueSetDouble(cell, valueDouble);
      break;
    case FIELDTYPE_VARCHAR:
      len         = column->columnConstraint->maxValue + 1;
      valueString = (char *)my_malloc0(len);
      memcpy(valueString, rowTuple->record + column->offset, column->columnConstraint->maxValue);
      valueSetString(cell, valueString);
      my_free(valueString);
      break;
    case FIELDTYPE_BLOB:
      memcpy(&valueInt, rowTuple->record + column->offset + INT_SIZE, INT_SIZE);
      valueSetInt(cell, valueInt);
      break;
    case FIELDTYPE_DATE: break;
    case FIELDTYPE_DATETIME: break;
    case FIELDTYPE_TEXT: {
    }
    default: break;
  }
  return GNCDB_SUCCESS;
}
int rowTupleFindCell(RowTuple *rowTuple, TupleCellSpec *spec, Value *cell)
{
  int     i         = 0;
  Column *column    = NULL;
  char   *tableName = spec->tableName;
  char   *fieldName = spec->fieldName;
  if (rowTuple == NULL || spec == NULL || cell == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (tableName == NULL) {
    return GNCDB_NOT_FOUND;
  }
  if (0 != strcmp(tableName, rowTuple->table->tableName)) {
    return GNCDB_NOT_FOUND;
  }
  // for (i = 0; i < rowTuple->tuple->fieldArray->elementCount; i++) {
  for (i = 0; i < rowTuple->tableSchema->columnList->elementCount; i++) {
    column = (Column *)varArrayListGetPointer(rowTuple->tableSchema->columnList, i);
    if (0 == strcmp(fieldName, column->fieldName)) {
      return rowTupleCellAt(rowTuple, i, cell);
    }
  }
  return GNCDB_NOT_FOUND;
}

int projectTupleCellAt(ProjectTuple *projectTuple, int index, Value *cell)
{
  // Expression *expr = NULL;
  if (projectTuple == NULL || cell == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (index < 0 || index >= projectTuple->exprs->elementCount) {
    return GNCDB_INTERNAL;
  }
  if (projectTuple->tuple == NULL) {
    return GNCDB_INTERNAL;
  }
  // TupleCellSpec* spec = (TupleCellSpec*)varArrayListGet(projectTuple->speces, index);
  // 原本这里会根据 tuple cell spec 去 tuple_ 里 find cell
  // 现在这个逻辑是在 FieldExpr 的 get_value 里面
  // expr = (Expression *)varArrayListGetPointer(projectTuple->exprs, index);
  return GNCDB_SUCCESS;
}

int projectTupleFindCell(ProjectTuple *projectTuple, TupleCellSpec *spec, Value *cell)
{
  return tupleFindCell(projectTuple->tuple, spec, cell);
}

int expressionTupleCellAt(ExpressionTuple *expressionTuple, int index, Value *cell)
{
  Expression *expr = NULL;
  if (expressionTuple == NULL || cell == NULL) {
    return GNCDB_PARAMNULL;
  }

  if (index < 0 || index >= expressionTuple->expressions->elementCount) {
    return GNCDB_PARAM_INVALID;
  }
  expr = (Expression *)varArrayListGetPointer(expressionTuple->expressions, index);
  return exprTryGetValue(expr, cell);
}

int expressionTupleFindCell(ExpressionTuple *expressionTuple, TupleCellSpec *spec, Value *cell)
{
  int         i    = 0;
  Expression *expr = NULL;
  if (expressionTuple == NULL || spec == NULL || cell == NULL) {
    return GNCDB_PARAMNULL;
  }

  for (i = 0; i < expressionTuple->expressions->elementCount; i++) {
    expr = (Expression *)varArrayListGetPointer(expressionTuple->expressions, i);
    if (0 == strcmp(spec->alias, expr->name)) {
      return exprTryGetValue(expr, cell);
    }
  }
  return GNCDB_NOT_FOUND;
}

int valueListTupleCellAt(ValueListTuple *valueListTuple, int index, Value *cell)
{

  if (valueListTuple == NULL || cell == NULL) {
    return GNCDB_PARAMNULL;
  }

  if (index < 0 || index >= valueListTuple->cells->elementCount) {
    return GNCDB_PARAM_INVALID;
  }
  *cell = *(Value *)varArrayListGet(valueListTuple->cells, index);
  return GNCDB_SUCCESS;
}

int valueListTupleFindCell(ValueListTuple *valueListTuple, TupleCellSpec *spec, Value *cell) { return GNCDB_INTERNAL; }

int joinTupleCellAt(JoinedTuple *joinTuple, int index, Value *cell)
{
  // const int leftCellNum  = ((RowTuple *)joinTuple->left)->tuple->fieldArray->elementCount - 3;
  // const int rightCellNum = ((RowTuple *)joinTuple->right)->tuple->fieldArray->elementCount - 3;
  const int leftCellNum  = ((RowTuple *)joinTuple->left)->tableSchema->columnList->elementCount - 3;
  const int rightCellNum = ((RowTuple *)joinTuple->right)->tableSchema->columnList->elementCount - 3;
  if (index > 0 && index < leftCellNum) {
    return tupleCellAt(joinTuple->left, index, cell);
  } else if (index >= leftCellNum && index < leftCellNum + rightCellNum) {
    return tupleCellAt(joinTuple->right, index - leftCellNum, cell);
  } else {
    return GNCDB_NOT_FOUND;
  }
}

int joinTupleFindCell(JoinedTuple *joinTuple, TupleCellSpec *spec, Value *cell)
{
  int rc = GNCDB_SUCCESS;
  if (joinTuple == NULL || spec == NULL || cell == NULL) {
    return GNCDB_PARAMNULL;
  }
  rc = tupleFindCell(joinTuple->left, spec, cell);
  if (rc == GNCDB_SUCCESS || rc != GNCDB_NOT_FOUND) {
    return rc;
  }
  return tupleFindCell(joinTuple->right, spec, cell);
}

int tupleCellAt(AbstractTuple *tuple, int index, Value *cell)
{
  switch (tuple->type) {
    case ROW_TUPLE: return rowTupleCellAt((RowTuple *)tuple, index, cell);
    case PROJECT_TUPLE: return projectTupleCellAt((ProjectTuple *)tuple, index, cell);
    case EXPRESSION_TUPLE: return expressionTupleCellAt((ExpressionTuple *)tuple, index, cell);
    case VALUELIST_TUPLE: return valueListTupleCellAt((ValueListTuple *)tuple, index, cell);
    case JOINED_TUPLE: return joinTupleCellAt((JoinedTuple *)tuple, index, cell);
    case GROUP_TUPLE: return groupTupleCellAt((GroupTuple *)tuple, index, cell);
    case SPLICED_TUPLE: return splicedTupleCellAt((SplicedTuple *)tuple, index, cell);
    case SUBQUERY_TUPLE: return SubQueryTupleCellAt((SubQueryTuple *)tuple, index, cell);
    default: break;
  }
  return GNCDB_NOT_FOUND;
}

int tupleFindCell(AbstractTuple *tuple, TupleCellSpec *spec, Value *cell)
{
  if (tuple == NULL) {
    return GNCDB_INTERNAL;
  }
  switch (tuple->type) {
    case ROW_TUPLE: return rowTupleFindCell((RowTuple *)tuple, spec, cell);
    case PROJECT_TUPLE: return projectTupleFindCell((ProjectTuple *)tuple, spec, cell);
    case EXPRESSION_TUPLE: return expressionTupleFindCell((ExpressionTuple *)tuple, spec, cell);
    case VALUELIST_TUPLE: return valueListTupleFindCell((ValueListTuple *)tuple, spec, cell);
    case JOINED_TUPLE: return joinTupleFindCell((JoinedTuple *)tuple, spec, cell);
    case GROUP_TUPLE: return groupTupleFindCell((GroupTuple *)tuple, spec, cell);
    case SPLICED_TUPLE: return splicedTupleFindCell((SplicedTuple *)tuple, spec, cell);
    case SUBQUERY_TUPLE: return SubQueryTupleFindCell((SubQueryTuple *)tuple, spec, cell);
    default: break;
  }
  return GNCDB_NOT_FOUND;
}

RowTuple *RowTupleConstructUseTuple(BYTE *record, struct GNCDB *db, struct BtreeTable *table)
{
  RowTuple *rowTuple = (RowTuple *)my_malloc0(sizeof(RowTuple));
  if (rowTuple == NULL) {
    return NULL;
  }
  rowTuple->type        = ROW_TUPLE;
  rowTuple->record      = record;
  rowTuple->table       = table;
  rowTuple->tableSchema = getTableSchema(db->catalog, table->tableName);
  return rowTuple;
}

GroupTuple *groupTupleCreate()
{
  GroupTuple *groupTuple = (GroupTuple *)my_malloc0(sizeof(GroupTuple));
  if (groupTuple == NULL) {
    return NULL;
  }
  groupTuple->type         = GROUP_TUPLE;
  groupTuple->aggrResults  = NULL;
  groupTuple->fieldResults = NULL;
  return groupTuple;
}

int groupTupleCellAt(GroupTuple *groupTuple, int index, Value *cell)
{
  // if (groupTuple->tuple == NULL) {
  //   return GNCDB_INTERNAL;
  // }
  // return tupleCellAt(groupTuple->tuple, index, cell);
  assert(false);
  return GNCDB_SUCCESS;
}

int GroupTupleFindIndexByAlias(GroupTuple *groupTuple, char *alias)
{
  int           i    = 0;
  AggrFuncExpr *expr = NULL;
  if (groupTuple == NULL || alias == NULL) {
    return -1;
  }
  for (i = 0; i < groupTuple->aggrResults->elementCount; i++) {
    expr = ((AggrExprResults *)varArrayListGetPointer(groupTuple->aggrResults, i))->expr;
    if (0 == strcmp(alias, expr->name)) {
      return i;
    }
  }
  return -1;
}

int groupTupleFindCellByAlias(GroupTuple *groupTuple, char *alias, Value *cell)
{
  int index = GroupTupleFindIndexByAlias(groupTuple, alias);
  if (index < 0 || index >= groupTuple->aggrResults->elementCount) {
    return GNCDB_NOT_FOUND;
  }
  *cell          = *((AggrExprResults *)varArrayListGetPointer(groupTuple->aggrResults, index))->result;
  cell->isMalloc = false;
  return GNCDB_SUCCESS;
}

int groupTupleFindCell(GroupTuple *groupTuple, TupleCellSpec *spec, Value *cell)
{
  int        i    = 0;
  FieldExpr *expr = NULL;

  if (!isBlank(spec->tableName) && !isBlank(spec->fieldName)) {
    for (i = 0; i < groupTuple->fieldResults->elementCount; i++) {
      expr = ((FieldExprResults *)varArrayListGetPointer(groupTuple->fieldResults, i))->expr;
      if (0 == strcmp(expr->fieldName, spec->fieldName) && 0 == strcmp(expr->tableName, spec->tableName)) {
        *cell          = *((FieldExprResults *)varArrayListGetPointer(groupTuple->fieldResults, i))->result;
        cell->isMalloc = false;
        return GNCDB_SUCCESS;
      }
    }
  }
  return groupTupleFindCellByAlias(groupTuple, spec->alias, cell);
}

// void groupTupleDoAggrFirst(Record* childRecord, GroupByPhysicalOperator* groupby)
// {

// }

// void groupTupleDoAggr(GroupTuple *groupTuple, AbstractTuple *tuple)
// {
//   int              i           = 0;
//   AggrExprResults *aggrResults = NULL;
//   for (i = 0; i < groupTuple->aggrResults->elementCount; i++) {
//     aggrResults = (AggrExprResults *)varArrayListGetPointer(groupTuple->aggrResults, i);
//     AggrExprResultsAdvance(aggrResults, tuple);
//   }
// }

// void groupTupleDoAggrDone(GroupTuple *groupTuple)
// {
//   int              i           = 0;
//   AggrExprResults *aggrResults = NULL;
//   for (i = 0; i < groupTuple->aggrResults->elementCount; i++) {
//     aggrResults = (AggrExprResults *)varArrayListGetPointer(groupTuple->aggrResults, i);
//     AggrExprResultsFinalize(aggrResults);
//   }
// }

// 每个 group 的第一行调用一次 init
// void AggrExprResultsInit(AggrExprResults *aggrResults, AbstractTuple *tuple)
// {
//   // 1. reset
//   aggrResults->count   = 0;
//   aggrResults->allNull = true;

//   // 2. count(1) count(*) count(1+1)
//   if (aggrResults->expr->paramIsConstexpr) {
//     // 不能跳过 null 这种情况下可以直接递增 count
//     aggrResults->count = 1;
//     return;
//   }
//   // 3. get current value and set result
//   // expressionGetValue(aggrResults->expr->param, tuple, aggrResults->result);
//   // 4. ignore null
//   if (aggrResults->result->attrType != NULLS) {
//     aggrResults->count   = 1;
//     aggrResults->allNull = false;
//   }
//   return;
// }

// // 每个 group 进行中间结果的计算
// void AggrExprResultsAdvance(AggrExprResults *aggrResults, AbstractTuple *tuple)
// {

//   Value *currentValue = NULL;
//   // 1. count(1) count(*) count(1+1)
//   if (aggrResults->expr->paramIsConstexpr) {
//     aggrResults->count++;
//     return;
//   }

//   // 2. get current value
//   currentValue = valueCreate();
//   // expressionGetValue(aggrResults->expr->param, tuple, currentValue);

//   // 3. ignore null
//   if (currentValue->attrType == NULLS) {
//     valueDestroy(&currentValue);
//     return;
//   }

//   // 4. update result
//   aggrResults->count++;
//   aggrResults->allNull = false;

//   // 5. 处理init 的时候拿到的是 null的情况
//   if (aggrResults->result->attrType == NULLS) {
//     aggrResults->result = PTR_MOVE((void **)&currentValue);
//     // valueDestroy(currentValue);
//     return;
//   }
//   // 6. do aggr calc
//   switch (aggrResults->expr->aggrType) {
//     case AGG_COUNT: {
//     } break;
//     case AGG_SUM:
//     case AGG_AVG: {
//       aggrResults->result = valueAdd(aggrResults->result, currentValue);
//     } break;
//     case AGG_MAX: {
//       if (valueCompare(aggrResults->result, currentValue) < 0) {
//         valueDestroy(&aggrResults->result);
//         aggrResults->result = PTR_MOVE((void **)&currentValue);
//       }
//     } break;
//     case AGG_MIN: {
//       if (valueCompare(aggrResults->result, currentValue) > 0) {
//         valueDestroy(&aggrResults->result);
//         aggrResults->result = PTR_MOVE((void **)&currentValue);
//       }
//     } break;
//     default: printf("aggr type not supported %d\n", aggrResults->expr->aggrType); break;
//   }
//   valueDestroy(&currentValue);
// }

// // 每个 group 迭代完之后计算最终结果
// void AggrExprResultsFinalize(AggrExprResults *aggrResults)
// {
//   // 1. count(*) count(1) count(1+1) count(id)
//   if (aggrResults->expr->aggrType == AGG_COUNT) {
//     aggrResults->result->numValue.intValue = aggrResults->count;
//     return;
//   }

//   // 2. all null
//   if (aggrResults->allNull) {
//     aggrResults->result->attrType = NULLS;
//     return;
//   }

//   // 3. other situation
//   switch (aggrResults->expr->aggrType) {
//     case AGG_COUNT: {
//       aggrResults->result->numValue.intValue = aggrResults->count;
//     } break;

//     case AGG_AVG: {
//       aggrResults->result = valueDiv(aggrResults->result, aggrResults->count);
//     } break;

//     default: {
//       // do nothing
//     } break;
//   }
// }

// void FieldExprResultsInit(FieldExprResults *fieldResults, AbstractTuple *tuple)
// {
//   // expressionGetValue((Expression *)fieldResults->expr, tuple, fieldResults->result);
// }

AbstractTuple *rowTupleDeepCopy(RowTuple *tuple)
{
  RowTuple *copyTuple    = (RowTuple *)my_malloc0(sizeof(RowTuple));
  copyTuple->type        = tuple->type;
  copyTuple->record      = leafTupleDeepCopy(tuple->table, tuple->record);
  copyTuple->table       = tuple->table;
  copyTuple->tableSchema = tuple->tableSchema;
  return (AbstractTuple *)copyTuple;
}

AbstractTuple *projectTupleDeepCopy(ProjectTuple *projectTuple)
{
  ProjectTuple *copyTuple = (ProjectTuple *)my_malloc0(sizeof(ProjectTuple));
  copyTuple->type         = projectTuple->type;
  copyTuple->exprs        = PTR_MOVE((void **)&projectTuple->exprs);
  copyTuple->tuple        = projectTuple->tuple;
  return (AbstractTuple *)copyTuple;
}

AbstractTuple *exprTupleDeepCopy(ExpressionTuple *exprTuple)
{
  ExpressionTuple *copyTuple = (ExpressionTuple *)my_malloc0(sizeof(ExpressionTuple));
  copyTuple->type            = exprTuple->type;
  copyTuple->expressions     = PTR_MOVE((void **)&exprTuple->expressions);
  return (AbstractTuple *)copyTuple;
}
AbstractTuple *valueListTupleDeepCopy(ValueListTuple *valueListTuple)
{
  ValueListTuple *copyTuple = (ValueListTuple *)my_malloc0(sizeof(ValueListTuple));
  copyTuple->type           = valueListTuple->type;
  copyTuple->cells          = PTR_MOVE((void **)&valueListTuple->cells);
  return (AbstractTuple *)copyTuple;
}
AbstractTuple *joinTupleDeepCopy(JoinedTuple *joinedTuple)
{
  JoinedTuple *copyTuple = (JoinedTuple *)my_malloc0(sizeof(JoinedTuple));
  copyTuple->type        = joinedTuple->type;
  copyTuple->left        = tupleCopy(joinedTuple->left);
  copyTuple->right       = tupleCopy(joinedTuple->right);
  return (AbstractTuple *)copyTuple;
}
/**
 * @brief 拷贝
 *
 * @param tuple
 * @return AbstractTuple*
 */
AbstractTuple *tupleCopy(AbstractTuple *tuple)
{
  if (tuple == NULL) {
    return NULL;
  }
  switch (tuple->type) {
    case ROW_TUPLE: {
      return rowTupleDeepCopy((RowTuple *)tuple);
    }
    case PROJECT_TUPLE: {
      return projectTupleDeepCopy((ProjectTuple *)tuple);
    }
    case EXPRESSION_TUPLE: {
      return exprTupleDeepCopy((ExpressionTuple *)tuple);
    }
    case VALUELIST_TUPLE: {
      return valueListTupleDeepCopy((ValueListTuple *)tuple);
    }
    case JOINED_TUPLE: {
      return joinTupleDeepCopy((JoinedTuple *)tuple);
    }
    default: return NULL;
  }
}

int splicedTupleCellAt(SplicedTuple *splicedTuple, int index, Value *cell)
{
  if (index < 0 || index >= splicedTuple->cells->elementCount) {
    return GNCDB_TUPLE_NOT_FOUND;
  }
  //   *cell = *(Value *)varArrayListGet(splicedTuple->cells, index);
  copyValueTo(varArrayListGetPointer(splicedTuple->cells, index), cell);
  return GNCDB_SUCCESS;
}

int splicedTupleFindCell(SplicedTuple *splicedTuple, TupleCellSpec *spec, Value *cell)
{
  int           i         = 0;
  Expression   *expr      = NULL;
  FieldExpr    *fieldExpr = NULL;
  AggrFuncExpr *aggrExpr  = NULL;

  for (i = 0; i < splicedTuple->exprs->elementCount; i++) {
    expr = (Expression *)varArrayListGetPointer(splicedTuple->exprs, i);
    if (expr->type == ETG_FIELD) {
      fieldExpr = (FieldExpr *)expr;
      if (0 == strcmp(fieldExpr->fieldName, spec->fieldName) && 0 == strcmp(fieldExpr->tableName, spec->tableName)) {
        splicedTupleCellAt(splicedTuple, i, cell);
        return GNCDB_SUCCESS;
      }
    } else if (expr->type == ETG_AGGRFUNC) {
      aggrExpr = (AggrFuncExpr *)expr;
      if (0 == strcmp(aggrExpr->name, spec->alias)) {
        splicedTupleCellAt(splicedTuple, i, cell);
        return GNCDB_SUCCESS;
      }
    } else {
      return GNCDB_INTERNAL;
    }
  }
  return GNCDB_NOT_FOUND;
}

int SubQueryTupleCellAt(SubQueryTuple *subQueryTuple, int index, Value *cell)
{
  if (index < 0 || index >= 2) {
    return GNCDB_TUPLE_NOT_FOUND;
  }
  if (index == 0) {
    return tupleCellAt(subQueryTuple->parentTuple, 0, cell);
  } else {
    return tupleCellAt(subQueryTuple->childTuple, 0, cell);
  }
}

int SubQueryTupleFindCell(SubQueryTuple *subQueryTuple, TupleCellSpec *spec, Value *cell)
{
  int rc = GNCDB_SUCCESS;
  rc     = tupleFindCell(subQueryTuple->childTuple, spec, cell);
  if (rc == GNCDB_NOT_FOUND) {
    /* child_tuple未找到，继续在parent_tuple中找 */
    if (subQueryTuple->parentTuple != NULL)
      rc = tupleFindCell(subQueryTuple->parentTuple, spec, cell);
    return rc;
  }
  return rc;
}

/****************************************tuple的创建函数****************************************/
SubQueryTuple *SubQueryTupleCreate(AbstractTuple *parentTuple, AbstractTuple *childTuple)
{
  SubQueryTuple *subQueryTuple = (SubQueryTuple *)my_malloc0(sizeof(SubQueryTuple));
  subQueryTuple->type          = SUBQUERY_TUPLE;
  subQueryTuple->parentTuple   = parentTuple;
  subQueryTuple->childTuple    = childTuple;
  return subQueryTuple;
}

/****************************************tuple的销毁函数****************************************/
void RowTupleDestroy(RowTuple *rowTuple)
{
  if (rowTuple == NULL) {
    return;
  }

  if (rowTuple->record != NULL) {
    my_free(rowTuple->record);
    rowTuple->record = NULL;
  }
  my_free(rowTuple);
}

void ProjectTupleDestroy(ProjectTuple *projectTuple)
{
  if (projectTuple == NULL) {
    return;
  }

  if (projectTuple->tuple != NULL) {
    tupleDestroy(projectTuple->tuple);
    projectTuple->tuple = NULL;
  }

  my_free(projectTuple);
}

void ExpressionTupleDestroy(ExpressionTuple *expressionTuple)
{
  if (expressionTuple == NULL) {
    return;
  }

  if (expressionTuple->expressions != NULL) {
    varArrayListDestroy(&expressionTuple->expressions);
  }

  my_free(expressionTuple);
}

void ValueListTupleDestroy(ValueListTuple *valueListTuple)
{
  if (valueListTuple == NULL) {
    return;
  }

  if (valueListTuple->cells != NULL) {
    varArrayListDestroy(&valueListTuple->cells);
  }

  my_free(valueListTuple);
}

void JoinedTupleDestroy(JoinedTuple *joinedTuple)
{
  if (joinedTuple == NULL) {
    return;
  }
  tupleDestroy(joinedTuple->left);
  tupleDestroy(joinedTuple->right);
  my_free(joinedTuple);
}

void GroupTupleDestroy(GroupTuple *groupTuple)
{
  if (groupTuple == NULL) {
    return;
  }
  if (groupTuple->aggrResults != NULL) {
    varArrayListDestroy(&groupTuple->aggrResults);
  }
  if (groupTuple->fieldResults != NULL) {
    varArrayListDestroy(&groupTuple->fieldResults);
  }
  my_free(groupTuple);
}

void SplicedTupleDestroy(SplicedTuple *splicedTuple)
{
  if (splicedTuple == NULL) {
    return;
  }
  my_free(splicedTuple);
}

void AggrExprResultsDestroy(AggrExprResults *aggrResults)
{
  if (aggrResults == NULL) {
    return;
  }
  if (aggrResults->result != NULL) {
    valueDestroy(&aggrResults->result);
  }

  my_free(aggrResults);
}

void FieldExprResultsDestroy(FieldExprResults *fieldResults)
{
  if (fieldResults == NULL) {
    return;
  }
  if (fieldResults->result != NULL) {
    valueDestroy(&fieldResults->result);
  }

  my_free(fieldResults);
}

void SubQueryTupleDestroy(SubQueryTuple *subQueryTuple)
{
  if (subQueryTuple == NULL) {
    return;
  }
  my_free(subQueryTuple);
}

void tupleDestroy(AbstractTuple *tuple)
{
  if (tuple == NULL) {
    return;
  }
  switch (tuple->type) {
    case ROW_TUPLE: RowTupleDestroy((RowTuple *)tuple); break;
    case PROJECT_TUPLE: ProjectTupleDestroy((ProjectTuple *)tuple); break;
    case EXPRESSION_TUPLE: ExpressionTupleDestroy((ExpressionTuple *)tuple); break;
    case VALUELIST_TUPLE: ValueListTupleDestroy((ValueListTuple *)tuple); break;
    case JOINED_TUPLE: JoinedTupleDestroy((JoinedTuple *)tuple); break;
    case GROUP_TUPLE: GroupTupleDestroy((GroupTuple *)tuple); break;
    case SPLICED_TUPLE: SplicedTupleDestroy((SplicedTuple *)tuple); break;
    case SUBQUERY_TUPLE: SubQueryTupleDestroy((SubQueryTuple *)tuple); break;
    default: break;
  }
}

/****************************************tuple pointer的销毁函数****************************************/

void RowTuplePointerDestroy(void *data)
{
  RowTuple **rowTuple = (RowTuple **)data;
  if (rowTuple == NULL || *rowTuple == NULL) {
    return;
  }
  RowTupleDestroy(*rowTuple);
  *rowTuple = NULL;
}

void ProjectTuplePointerDestroy(void *data)
{
  ProjectTuple **projectTuple = (ProjectTuple **)data;
  if (projectTuple == NULL || *projectTuple == NULL) {
    return;
  }
  ProjectTupleDestroy(*projectTuple);
  *projectTuple = NULL;
}

void ExpressionTuplePointerDestroy(void *data)
{
  ExpressionTuple **expressionTuple = (ExpressionTuple **)data;
  if (expressionTuple == NULL || *expressionTuple == NULL) {
    return;
  }
  ExpressionTupleDestroy(*expressionTuple);
  *expressionTuple = NULL;
}

void ValueListTuplePointerDestroy(void *data)
{
  ValueListTuple **valueListTuple = (ValueListTuple **)data;
  if (valueListTuple == NULL || *valueListTuple == NULL) {
    return;
  }
  ValueListTupleDestroy(*valueListTuple);
  *valueListTuple = NULL;
}

void JoinedTuplePointerDestroy(void *data)
{
  JoinedTuple **joinedTuple = (JoinedTuple **)data;
  if (joinedTuple == NULL || *joinedTuple == NULL) {
    return;
  }
  JoinedTupleDestroy(*joinedTuple);
  *joinedTuple = NULL;
}

void GroupTuplePointerDestroy(void *data)
{
  GroupTuple **groupTuple = (GroupTuple **)data;
  if (groupTuple == NULL || *groupTuple == NULL) {
    return;
  }
  GroupTupleDestroy(*groupTuple);
  *groupTuple = NULL;
}

void SplicedTuplePointerDestroy(void *data)
{
  SplicedTuple **splicedTuple = (SplicedTuple **)data;
  if (splicedTuple == NULL || *splicedTuple == NULL) {
    return;
  }
  SplicedTupleDestroy(*splicedTuple);
  *splicedTuple = NULL;
}

void AggrExprResultsPointerDestroy(void *data)
{
  AggrExprResults **aggrResults = (AggrExprResults **)data;
  if (aggrResults == NULL || *aggrResults == NULL) {
    return;
  }
  AggrExprResultsDestroy(*aggrResults);
  *aggrResults = NULL;
}

void FieldExprResultsPointerDestroy(void *data)
{
  FieldExprResults **fieldResults = (FieldExprResults **)data;
  if (fieldResults == NULL || *fieldResults == NULL) {
    return;
  }
  FieldExprResultsDestroy(*fieldResults);
  *fieldResults = NULL;
}

void SubQueryTuplePointerDestroy(void *data)
{
  SubQueryTuple **subQueryTuple = (SubQueryTuple **)data;
  if (subQueryTuple == NULL || *subQueryTuple == NULL) {
    return;
  }
  SubQueryTupleDestroy(*subQueryTuple);
  *subQueryTuple = NULL;
}

void TuplePointerDestroy(void *data)
{
  AbstractTuple **tuple = (AbstractTuple **)data;
  if (tuple == NULL || *tuple == NULL) {
    return;
  }
  tupleDestroy(*tuple);
  *tuple = NULL;
}