#ifndef EXEC_TUPLE_H
#define EXEC_TUPLE_H
#include "btreetable.h"
#include "catalog.h"
#include "tuple.h"
#include "value.h"
#include "vararraylist.h"

typedef struct GNCDB        GNCDB;
typedef struct FieldExpr    FieldExpr;
typedef struct AggrFuncExpr AggrFuncExpr;
/* record的字段的描述 */
typedef enum
{
  // field、aggr或者bitmap
  NormalField = 0,
  // 复合类型，comp conj等
  CompositeField,
} MetaType;

typedef struct Meta
{
  MetaType  type;          // 字段的类型
  int       len;           // 存储的数据长度，如果是字符类型，那么是字符的实际长度，拷贝时需要+1，留一个bit给'\0'
  int       offset;        // 在record中的偏移量
  int       bitmapOffset;  // record位图的偏移量，仅在bitmap类型时使用
  int       index;         // 列在record中的位置索引
  bool      owned;      // 是否拥有数据的所有权，如果是从其他地方拷贝过来的，那么就是false
  FieldType fieldType;  // 存储的数据类型
} Meta;

typedef struct ColMeta
{
  MetaType     type;  // 字段的类型
  int          len;  // 存储的数据长度，如果是字符类型，那么是字符的实际长度，拷贝时需要+1，留一个bit给'\0'
  int          offset;        // 在record中的偏移量
  int          bitmapOffset;  // record位图的偏移量
  int          index;         // 列在record中的位置索引
  bool         owned;  // 是否拥有数据的所有权，如果是从其他地方拷贝过来的，那么就是false
  FieldType    fieldType;  // 存储的数据类型
  char        *name;  // 名称，如果是表的字段，那么是字段名，如果是聚合函数，那么是聚合函数的名字，还可能是复合类型的名字
  char        *tableName;  // 所属的表名(如果有)
  AggrFuncType aggrType;   // 如果是聚合函数，那么是聚合函数的类型
  bool         isBitMap;   // 是否是bitmap
} ColMeta;

typedef struct CompositeMeta
{
  MetaType    type;          // 字段的类型
  int         len;           // 存储的数据长度，如果是字符类型，那么是字符的实际长度，拷贝时需要+1，留一个bit给'\0'
  int         offset;        // 在record中的偏移量
  int         bitmapOffset;  // record位图的偏移量
  int         index;         // 列在record中的位置索引
  bool        owned;  // 是否拥有数据的所有权，如果是从其他地方拷贝过来的，那么就是false
  FieldType   fieldType;  // 存储的数据类型
  Expression *expr;       // 复合类型的情况，无法被简单解析成字段名或者aggr(字段名)或者bitmap
} CompositeMeta;

/* 判断一个Meta是否是表的字段 */
bool isFieldMeta(Meta *meta);
/* 浅拷贝一个ColMeta */
ColMeta *ColMetaCopy(ColMeta *colMeta);
/* 深拷贝一个ColMeta */
ColMeta *ColMetaDeepCopy(ColMeta *colMeta);
/* 销毁FieldDesc指针 */
void ColMetaDestroy(ColMeta **colMeta);
/* 销毁list中的FieldDesc指针 */
void ColMetaPtrDestroy(void *data);

/* 浅拷贝一个CompositeMeta */
CompositeMeta *CompositeMetaCopy(CompositeMeta *compositeMeta);
/* 深拷贝一个CompositeMeta */
CompositeMeta *CompositeMetaDeepCopy(CompositeMeta *compositeMeta);
/* 销毁CompositeMeta指针 */
void CompositeMetaDestroy(CompositeMeta **compositeMeta);
/* 销毁list中的CompositeMeta指针 */
void CompositeMetaPtrDestroy(void *data);

Meta *MetaCopy(Meta *meta);
Meta *MetaDeepCopy(Meta *meta);
void  MetaDestroy(Meta **meta);
void  MetaPtrDestroy(void *data);

typedef struct Record
{
  BYTE *data;       // 记录的数据
  int   size;       // 记录的大小
  bool  allocated;  // 是否已经为数据分配空间
} Record;

/**
 * @description: 创建一个记录，将data拷贝而非赋值
 * @param {BYTE} *data
 * @param {int} size
 * @return {*}
 */
Record *RecordCreate(BYTE *data, int size);

/**
 * @description: 从lookaside内存池中创建一个记录，将data拷贝而非赋值
 * @param {GNCDB*} db
 * @param {BYTE} *data
 * @param {int} size
 * @return {*}
 */
Record *RecordCreateFromLookaside(GNCDB *db, BYTE *data, int size);

/**
 * @description: 创建一个记录，不包含数据，后续由RecordFillData设置data
 * @param {int} size
 * @return {*}
 */
Record *RecordCreateWithoutData(int size);

/**
 * @description: 销毁一个记录
 * @param {Record} *
 * @return {*}
 */
void RecordDestroy(Record **record);

/**
 * @description: 销毁一个lookaside内存池中的记录
 * @param {GNCDB*} db
 * @param {Record} *
 * @return {*}
 */
void RecordDestroyLookaside(GNCDB *db, Record **record);

/**
 * @description: 销毁一个Record**指针，一般是在varArrayList的destroy中使用
 * @param {void} * data 实际上是Record**指针
 * @return {*}
 */
void RecordPtrDestroy(void *data);

/**
 * @description: 深拷贝一个记录
 * @param {Record} *record
 * @return {*}
 */
Record *RecordDeepCopy(Record *record);

/**
 * @description: 将data_复制到record中
 * @param {Record} *record
 * @param {char} *data_
 * @return {*}
 */
void RecordFillData(Record *record, BYTE *data_);

/**
 * @description: 将数据填充到记录指定偏移量中
 * @param {Record} *record
 * @param {int} offset
 * @param {void} *src
 * @param {int} size
 * @return {*}
 */
void RecordSetData(Record *record, int offset, void *src, int size);

/**
 * @description: 申请一个新的Record，分别复制head和tail
 * @param {Record} *head
 * @param {Record} *tail
 * @return {*}
 */
Record *RecordMerge(Record *head, Record *tail);

/**
 * @description: 从lookaside内存池中申请一个新的Record，分别复制head和tail
 * @param {GNCDB*} db
 * @param {Record} *head
 * @param {Record} *tail
 * @return {*}
 */
Record *RecordMergeFromLookaside(GNCDB *db, Record *head, Record *tail);

varArrayList *colMetaListMerge(varArrayList *head, varArrayList *tail);

int getRecordLength(varArrayList *cols, int *len);

typedef struct AbstractTuple
{
  TupleType type; /* 元组的类型 */
} AbstractTuple;

typedef struct RowTuple
{
  TupleType type; /* 元组的类型 */

  BYTE        *record;      /* 元组的内容 */
  BtreeTable  *table;       /* 元组所属的表 */
  TableSchema *tableSchema; /* 元组所属的表的schema */
} RowTuple;

typedef struct ProjectTuple
{
  TupleType type; /* 元组的类型 */

  varArrayList  *exprs; /* element type:<Expression*> */
  AbstractTuple *tuple; /* 元组的内容 */
} ProjectTuple;

typedef struct ExpressionTuple
{
  TupleType type; /* 元组的类型 */

  varArrayList *expressions; /* element type:<Expression*> */
} ExpressionTuple;

typedef struct ValueListTuple
{
  TupleType type; /* 元组的类型 */

  varArrayList *cells; /* element type:<Value> */
} ValueListTuple;

typedef struct JoinedTuple
{
  TupleType type; /* 元组的类型 */

  AbstractTuple *left;  /* 连接的左元组 */
  AbstractTuple *right; /* 连接的右元组 */
} JoinedTuple;

typedef enum
{
  AGGR,
  FIELD
} ResType;
typedef struct Result
{
  ResType type;
} Result;
typedef struct AggrExprResults
{
  ResType       type;
  AggrFuncExpr *expr;    /* 聚合函数 */
  Value        *result;  /* 聚合函数的结果 */
  int           count;   /* 聚合函数的计数 */
  bool          allNull; /* 是否全为空 */
} AggrExprResults;

typedef struct FieldExprResults
{
  ResType    type;
  FieldExpr *expr;   /* 字段表达式 */
  Value     *result; /* 字段表达式的结果 */
} FieldExprResults;

void ResultPtrDestroy(void *data);
void ResultDestroy();

typedef struct GroupTuple
{
  TupleType type; /* 元组的类型 */

  int           count; /* 元组的计数 */
  varArrayList *aggrResults;
  varArrayList *fieldResults;
} GroupTuple;

typedef struct SplicedTuple
{
  TupleType type; /* 元组的类型 */

  varArrayList *cells; /* element type:<Value>  */

  varArrayList *exprs;  // 在 create order by stmt 之前提取的  select clause 后的 field_expr (非a gg_expr 中的)和
                        //  agg_expr
  /*element type:<Expression*> */
} SplicedTuple;

/* 针对子查询的tuple */
typedef struct SubQueryTuple
{
  TupleType type; /* 元组的类型 */

  AbstractTuple *parentTuple;
  AbstractTuple *childTuple;
} SubQueryTuple;

/*其他tuple的函数*/
int tupleFindCell(AbstractTuple *tuple, TupleCellSpec *spec, Value *cell);
int tupleCellAt(AbstractTuple *tuple, int index, Value *cell);

int rowTupleFindCell(RowTuple *rowTuple, TupleCellSpec *spec, Value *cell);
int rowTupleCellAt(RowTuple *rowTuple, int index, Value *cell);
// RowTuple *RowTupleConstructUseTuple(Tuple *tuple, struct GNCDB *db, struct BtreeTable *table);
RowTuple *RowTupleConstructUseTuple(BYTE *record, struct GNCDB *db, struct BtreeTable *table);
int       projectTupleCellAt(ProjectTuple *projectTuple, int index, Value *cell);
int       projectTupleFindCell(ProjectTuple *projectTuple, TupleCellSpec *spec, Value *cell);

int groupTupleCellAt(GroupTuple *groupTuple, int index, Value *cell);
int GroupTupleFindIndexByAlias(GroupTuple *groupTuple, char *exprName);
int groupTupleFindCellByAlias(GroupTuple *groupTuple, char *exprName, Value *cell);
int groupTupleFindCell(GroupTuple *groupTuple, TupleCellSpec *spec, Value *cell);

// void groupTupleDoAggr(GroupTuple *groupTuple, AbstractTuple *tuple);
// void groupTupleDoAggrDone(GroupTuple *groupTuple);

// void AggrExprResultsInit(AggrExprResults *aggrResults, AbstractTuple *tuple);
// void AggrExprResultsAdvance(AggrExprResults *aggrResults, AbstractTuple *tuple);
// void AggrExprResultsFinalize(AggrExprResults *aggrResults);

// void FieldExprResultsInit(FieldExprResults *fieldResults, AbstractTuple *tuple);

AbstractTuple *tupleCopy(AbstractTuple *tuple);

int splicedTupleCellAt(SplicedTuple *splicedTuple, int index, Value *cell);
int splicedTupleFindCell(SplicedTuple *splicedTuple, TupleCellSpec *spec, Value *cell);

int SubQueryTupleFindCell(SubQueryTuple *subQueryTuple, TupleCellSpec *spec, Value *cell);
int SubQueryTupleCellAt(SubQueryTuple *subQueryTuple, int index, Value *cell);

/* tuple的创建函数*/
SubQueryTuple *SubQueryTupleCreate(AbstractTuple *parentTuple, AbstractTuple *childTuple);
GroupTuple    *groupTupleCreate();

/* tuple的销毁函数 */
void tupleDestroy(AbstractTuple *tuple);
/* aggrExprResults的销毁函数 */
void AggrExprResultsDestroy(AggrExprResults *aggrResults);
/* fieldExprResults的销毁函数 */
void FieldExprResultsDestroy(FieldExprResults *fieldResults);

/* tuple pointer的销毁函数 */
void AggrExprResultsPointerDestroy(void *data);
void FieldExprResultsPointerDestroy(void *data);
void TuplePointerDestroy(void *data);

#endif  // EXEC_TUPLE_H