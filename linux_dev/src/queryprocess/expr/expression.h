#ifndef EXPRESSION_H
#define EXPRESSION_H
#include "exec_tuple.h"
#include "select_stmt.h"
#include "vararraylist.h"
#include <string.h>
struct Expression;
typedef struct SelectStmt       SelectStmt;
typedef struct LogicalOperator  LogicalOperator;
typedef struct PhysicalOperator PhysicalOperator;
typedef struct AggrFuncExpr     AggrFuncExpr;

/* 函数指针类型定义 */
typedef int (*CheckFunc)(Expression *, varArrayList *, int, va_list args);

/* 结构体定义 */
typedef struct Expression
{
  ExprTag type;
  char   *name;  /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char   *alias; /* 表达式的别名 */
} Expression;

typedef struct FieldExpr
{
  ExprTag   type;      /* 表达式类型 */
  char     *name;      /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char     *alias;     /* 表达式的别名 */
  char     *tableName; /* 表名 */
  char     *fieldName; /* 字段名 */
  FieldType fieldType; /* 字段类型 */
  void     *listptr;
  ColMeta  *colMeta; /* 描述tuple的字段元信息 */
} FieldExpr;

typedef struct ValueExpr
{
  ExprTag type;  /* 表达式类型 */
  char   *name;  /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char   *alias; /* 表达式的别名 */
  Value  *value; /* 常量值 */
} ValueExpr;

typedef struct CastExpr
{
  ExprTag     type;     /* 表达式类型 */
  char       *name;     /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char       *alias;    /* 表达式的别名 */
  Expression *child;    /* 子表达式 */
  AttrType    castType; /* 转换后的类型 */
} CastExpr;

typedef struct ComparisonExpr
{
  ExprTag     type;  /* 表达式类型 */
  char       *name;  /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char       *alias; /* 表达式的别名 */
  CompOp      comp;  /* 比较运算符 */
  Expression *left;  /* 左子表达式 */
  Expression *right; /* 右子表达式 */
} ComparisonExpr;

typedef struct ConjunctionExpr
{
  ExprTag             type;            /* 表达式类型 */
  char               *name;            /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char               *alias;           /* 表达式的别名 */
  ConjunctionExprType conjunctionType; /* 联结类型 */
  varArrayList       *children;        /* 子表达式list，element type: <Expression*> */
} ConjunctionExpr;

typedef struct ArithmeticExpr
{
  ExprTag            type;           /* 表达式类型 */
  char              *name;           /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char              *alias;          /* 表达式的别名 */
  ArithmeticExprType arithmeticType; /* 算术运算类型 */
  Expression        *left;           /* 左子表达式 */
  Expression        *right;          /* 右子表达式 */
} ArithmeticExpr;

typedef struct AggrFuncExpr
{
  ExprTag      type;     /* 表达式类型 */
  char        *name;     /* 表达式的名字，比如是字段名称，或者用户在执行SQL语句时输入的内容 */
  char        *alias;    /* 表达式的别名 */
  AggrFuncType aggrType; /* 聚合函数类型 */
  Expression  *param;
  bool         paramIsConstexpr;
} AggrFuncExpr;

typedef struct SubQueryExpr
{
  ExprTag           type;
  char             *name;
  char             *alias;
  SelectSqlNode    *sqlNode;
  SelectStmt       *stmt;
  LogicalOperator  *logicalOper;
  PhysicalOperator *physicalOper;
  SQLStageEvent    *subEvent;
} SubQueryExpr;

typedef struct ExprListExpr
{
  ExprTag       type;
  char         *name;
  char         *alias;
  int           curIdx;
  varArrayList *exprs; /* element type: <Expression*> */
} ExprListExpr;
/* 表达式trygetvalue */
int exprTryGetValue(Expression *expr, Value *cell);
/* 表达式getvalue, 默认不拷贝字符串*/
int expressionGetValueNCP(Expression *expr, Record *tuple, varArrayList *colMetas, Value *value);
/* 表达式getvalue，并拷贝字符串值 */
int expressionGetValueDC(Expression *expr, Record *tuple, varArrayList *colMetas, Value *value);
/* 表达式getvaluetype */
AttrType expressionGetValueType(Expression *expr);
/* 表达式getvalueLen */
int expressionGetValueLen(Expression *expr, varArrayList *colMetas);
/* 表达式是否可以下推 */
bool exprCheckCanPushDown(Expression *expr, HashMap *tableMap);
/* 表达式检查field */
int exprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, Expression *expr, BtreeTable *defaultTable,
    HashMap *tableAliasMap);
/* fieldExpr检验 */
int fieldExprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, FieldExpr *fieldExpr,
    BtreeTable *defaultTable, HashMap *tableAliasMap);
/* 表达式检查是否是const */
int exprCheckIsConstexpr(Expression *expr);
/* 递归检查表达式 */
int exprTraverseCheckPrepare(
    Expression *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, ...);
int exprTraverseCheck(
    Expression *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list ap);
/* 表达式创建 */
void *exprCreate(ExprTag type);
/* 表达式拷贝 */
Expression *exprDeepCopy(Expression *expr);
/* 表达式深拷贝，使用lookaside内存池 */
Expression *exprDeepCopyLookaside(GNCDB *db, Expression *expr);
/* 表达式销毁 */
void exprDestroy(Expression *expr);
/* 表达式销毁，使用lookaside内存池 */
void exprDestroyLookaside(GNCDB*db, Expression *expr);
/* 表达式队列销毁 */
/**
 * @description: 该宏用以销毁表达式List
 * @param {varArrayList} *exprList 待销毁的表达式List
 * @note: 该宏会销毁exprList中的所有表达式（不管有没有设置destroy函数），并销毁exprList本身
 * @return {*}
 */
#define EXPRLIST_DESTROY(exprList)                           \
  do {                                                       \
    Expression *expr = NULL;                                 \
    if ((exprList) != NULL) {                                \
      if ((exprList)->destroy == NULL) {                     \
        for (int i = 0; i < (exprList)->elementCount; i++) { \
          expr = varArrayListGetPointer((exprList), i);      \
          if (expr != NULL) {                                \
            exprDestroy(expr);                               \
          }                                                  \
        }                                                    \
      }                                                      \
      varArrayListDestroy(&(exprList));                      \
      (exprList) = NULL;                                     \
    }                                                        \
  } while (0)
/* 表达式指针销毁，用于list destroy */
void exprPtrDestroy(void *data);
/* 表达式打印 */
void printExpr(Expression *expr);
/* 表达式是否有更多的rows，用于检查子查询是否只返回了一条语句 */
bool   ExprHasMoreRow(Expression *expr, Record *tuple, varArrayList *cols);
int    aggrExprCheckIsConstexpr(AggrFuncExpr *expr);
CompOp swapOp(CompOp op);
#endif /* EXPRESSION_H */
