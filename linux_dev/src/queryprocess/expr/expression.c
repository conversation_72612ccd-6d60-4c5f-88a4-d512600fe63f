#include "expression.h"
#include "catalog.h"
#include "exec_tuple.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "lookaside_mem.h"
#include "parse_defs.h"
#include "physical_operator.h"
#include "limits.h"
#include <float.h>
#ifdef _WIN32
// 根据实际安装的msys的包设定路径
#include <C:\msys64\mingw32\include\regex.h>
#else
#include <regex.h>
#endif

#include <stdarg.h>
#include <string.h>
#include "sql_event.h"
#include "utils.h"
#include "assert.h"
#include "tuple_cell.h"
#include "typedefine.h"
#include "value.h"
#include "gncdb.h"
#include "vararraylist.h"
#include "typedefine.h"
extern Meta *matchAggrFunc(AggrFuncExpr *aggrFuncExpr, varArrayList *colMetas);
extern void  fillField(Field *field, Column *column);
extern int   expressionTupleCellAt(ExpressionTuple *expression_tuple, int index, Value *cell);
extern void printValue(Value *value);
extern void fieldExprSetName(
    const char *table_name, const char *field_name, FieldExpr *field_expr, bool is_single_table);
int static SubQueryExprNum = 0;
// 打印ExprTag类型
void printExprTag(ExprTag tag)
{
  switch (tag) {
    case ETG_BASE: printf("T_Expr\t"); break;
    case ETG_FIELD: printf("T_FieldExpr\t"); break;
    case ETG_VALUE: printf("T_ValueExpr\t"); break;
    case ETG_CAST: printf("T_CastExpr\t"); break;
    case ETG_COMPARISON: printf("T_ComparisonExpr\t"); break;
    case ETG_CONJUNCTION: printf("T_ConjunctionExpr\t"); break;
    case ETG_ARITHMETIC: printf("T_ArithmeticExpr\t"); break;
    default: printf("Unknown ExprTag\t"); break;
  }
}

void printFieldExpr(FieldExpr *expr) { printf("%s ", expr->fieldName); }

void printValue(Value *value)
{
  char *str = NULL;
  printAttrType(value->attrType);
  if (value == NULL) {
    printf("NULL");
    return;
  }
  switch (value->attrType) {
    case INTS: printf("%d", value->numValue.intValue); break;
    case DOUBLES: printf("%f", value->numValue.doubleValue); break;
    case CHARS: printf("%s", value->strValue); break;
    case BOOLEANS: printf("%s", value->numValue.boolValue == 1 ? "true" : "false"); break;
    case DATES: {
      str = dateToString(value->numValue.intValue);
      printf("%s", str);
      my_free(str);
    } break;
    case DATETIMES: {
      str = datetimeToString(&value->numValue.datetimeValue);
      printf("%s", str);
      my_free(str);
    } break;
    default: printf("Unknown value type"); break;
  }
}

void printCompOp(enum CompOp op)
{
  switch (op) {
    case CMPOP_EQUAL_TO: printf(" = "); break;
    case CMPOP_LESS_EQUAL: printf(" <= "); break;
    case CMPOP_NOT_EQUAL: printf(" <> "); break;
    case CMPOP_LESS_THAN: printf(" < "); break;
    case CMPOP_GREAT_EQUAL: printf(" >= "); break;
    case CMPOP_GREAT_THAN: printf(" > "); break;
    case CMPOP_LIKE_OP: printf(" LIKE "); break;
    case CMPOP_NOT_LIKE_OP: printf(" NOT LIKE "); break;
    case CMPOP_EXISTS_OP: printf(" EXISTS "); break;
    case CMPOP_NOT_EXISTS_OP: printf(" NOT EXISTS "); break;
    case CMPOP_NO_OP: printf(" NO_OP "); break;
    default: printf(" Unknown "); break;
  }
}

void printConjunctionExprType(ConjunctionExprType type)
{
  switch (type) {
    case CJET_AND: printf(" AND "); break;
    case CJET_OR: printf(" OR "); break;
    default: printf(" Unknown "); break;
  }
}
void printValueExpr(ValueExpr *expr) { printValue(expr->value); }

void printCastExpr(CastExpr *expr)
{
  printExpr(expr->child);
  printAttrType(expr->castType);
}
void printComparisonExpr(ComparisonExpr *expr)
{
  printf("(");
  printExpr(expr->left);
  printCompOp(expr->comp);
  printExpr(expr->right);
  printf(")");
}

void printConjunctionExpr(ConjunctionExpr *expr)
{
  int i = 0;
  printf("(");
  for (i = 0; i < expr->children->elementCount - 1; i++) {
    printf("(");
    printExpr(varArrayListGetPointer(expr->children, i));
    printf(")");
    printConjunctionExprType(expr->conjunctionType);
  }
  if (expr->children->elementCount - 1 >= 0) {
    printExpr(varArrayListGetPointer(expr->children, expr->children->elementCount - 1));
  }
  printf(")");
}

void printArithmeticExpr(ArithmeticExpr *expr)
{
  printf("(");
  printExpr(expr->left);
  switch (expr->arithmeticType) {
    case ARITH_ADD: printf(" ADD "); break;
    case ARITH_SUB: printf(" SUB "); break;
    case ARITH_MUL: printf(" MUL "); break;
    case ARITH_DIV: printf(" DIV "); break;
    case ARITH_NEGATIVE: printf(" - "); break;
    default: printf(" Unknown "); break;
  }
  printExpr(expr->right);
  printf(")");
}

void printSubQueryExpr(SubQueryExpr *expr) { printf(" SubQuery#%d ", ++SubQueryExprNum); }
// 打印Expression结构体
void printExpr(Expression *expr)
{
  if (expr == NULL) {
    printf("NULL");
    return;
  }
  switch (expr->type) {
    case ETG_FIELD: printFieldExpr((FieldExpr *)expr); break;
    case ETG_VALUE:
      // 打印常量值表达式的具体信息
      printValueExpr((ValueExpr *)expr);
      break;
    case ETG_CAST:
      // 打印类型转换表达式的具体信息
      printCastExpr((CastExpr *)expr);
      break;
    case ETG_COMPARISON:
      // 打印比较表达式的具体信息
      printComparisonExpr((ComparisonExpr *)expr);
      break;
    case ETG_CONJUNCTION:
      // 打印联结表达式的具体信息
      printConjunctionExpr((ConjunctionExpr *)expr);
      break;
    case ETG_ARITHMETIC:
      // 打印算术表达式的具体信息
      printArithmeticExpr((ArithmeticExpr *)expr);
      break;
    case ETG_SUBQUERY:
      // 打印子查询表达式的具体信息
      printSubQueryExpr((SubQueryExpr *)expr);
      break;
    default: printf("Unknown expression type "); break;
  }
}

void replaceAll(char *str, const char *from, const char *to)
{
  int   fromLen = 0;
  int   toLen   = 0;
  char *pos     = NULL;
  if (strlen(from) == 0) {
    return;
  }
  fromLen = strlen(from);
  toLen   = strlen(to);
  pos     = str;
  while ((pos = strstr(pos, from)) != NULL) {
    memmove(pos + toLen, pos + fromLen, strlen(pos + fromLen) + 1);
    memcpy(pos, to, toLen);
    pos += toLen;
  }
}

int strLike(Value *left, Value *right)
{
  regex_t    regex;
  char      *rawReg = NULL;
  size_t     nmatch = 2;
  regmatch_t pmatch[nmatch];
  int        ret = 0;

  rawReg = my_strdup(right->strValue);
  replaceAll(rawReg, "_", "[^']");
  replaceAll(rawReg, "%", "[^']*");
  ret = regcomp(&regex, rawReg, REG_EXTENDED);
  if (ret != 0) {
    LOG(LOG_WARNING, "Failed to compile regex\n");
    my_free(rawReg);
    return 0;
  }
  ret = regexec(&regex, left->strValue, nmatch, pmatch, 0);
  // 开头必须匹配
  if (ret == 0 && pmatch[0].rm_so != 0) {
    ret = REG_NOMATCH;
  }
  regfree(&regex);
  my_free(rawReg);
  return !ret;
}
/* Comparison表达式 */
/**
 * @brief Comparison表达式比较值
 * @param comp
 * @param left
 * @param right
 * @param value
 * @return int
 */
int ComparisonExprCompareValue(CompOp *comp, Value *left, Value *right, bool *result)
{
  int rc         = GNCDB_SUCCESS;
  int cmp_result = 0;
  if (left->attrType == NULLS || right->attrType == NULLS) {
    *result = false;
  } else if (*comp == CMPOP_LIKE_OP) {
    *result = strLike(left, right);
  } else if (*comp == CMPOP_NOT_LIKE_OP) {
    *result = !strLike(left, right);
  } else {
    cmp_result = valueCompare(left, right);
    *result    = false;
    switch (*comp) {
      case CMPOP_EQUAL_TO:
        if (cmp_result == 0) {
          *result = true;
        }
        break;
      case CMPOP_LESS_EQUAL:
        if (cmp_result <= 0) {
          *result = true;
        }
        break;
      case CMPOP_NOT_EQUAL:
        if (cmp_result != 0) {
          *result = true;
        }
        break;
      case CMPOP_LESS_THAN:
        if (cmp_result < 0) {
          *result = true;
        }
        break;
      case CMPOP_GREAT_EQUAL:
        if (cmp_result >= 0) {
          *result = true;
        }
        break;
      case CMPOP_GREAT_THAN:
        if (cmp_result > 0) {
          *result = true;
        }
        break;
      default: rc = GNCDB_INTERNAL; break;
    }
  }
  return rc;
}

int ComparisonExprTryGetValue(ComparisonExpr *compExpr, Value *cell)
{
  int        rc    = GNCDB_SUCCESS;
  ValueExpr *left  = NULL;
  ValueExpr *right = NULL;
  bool       value = false;
  if (compExpr->left->type == ETG_VALUE && compExpr->right->type == ETG_VALUE) {
    left  = (ValueExpr *)compExpr->left;
    right = (ValueExpr *)compExpr->right;
    value = false;
    rc    = ComparisonExprCompareValue(&compExpr->comp, left->value, right->value, &value);
    if (rc != GNCDB_SUCCESS) {
    } else {
      valueSetBoolean(cell, value);
    }
    return rc;
  }
  return GNCDB_PARAM_INVALID;
}

/* CastExpr */
int CastExprCast(CastExpr *castExpr, Value *value, Value *castValue)
{
  int rc = GNCDB_SUCCESS;
  if (castExpr->castType == value->attrType) {
    *castValue = *value;
    return rc;
  }
  switch (castExpr->castType) {
    case BOOLEANS: {
      valueSetBoolean(castValue, valueGetBoolean(value));
    } break;
    default: {
      rc = GNCDB_INTERNAL;
    }
  }
  return rc;
}
int CastExprTryGetValue(CastExpr *castExpr, Value *value)
{
  int rc = exprTryGetValue(castExpr->child, value);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return CastExprCast(castExpr, value, value);
}

/* ArithmeticExpr */
// TODO: 如何处理字符串、NULL值的情形
AttrType ArithmeticExprGetValueType(ArithmeticExpr *arithmeticExpr)
{
  if (arithmeticExpr->left != NULL && arithmeticExpr->right == NULL) {
    return expressionGetValueType(arithmeticExpr->left);
  }
  if (arithmeticExpr->right != NULL && arithmeticExpr->left == NULL) {
    return expressionGetValueType(arithmeticExpr->right);
  }
  if (arithmeticExpr->left == NULL || arithmeticExpr->right == NULL) {
    return UNDEFINED;
  }
  if (expressionGetValueType(arithmeticExpr->left) == INTS && expressionGetValueType(arithmeticExpr->right) == INTS &&
      arithmeticExpr->arithmeticType != ARITH_DIV) {
    return INTS;
  }
  return DOUBLES;
}

int ArithmeticExprCalcValue(ArithmeticExpr *arithmeticExpr, Value *leftValue, Value *rightValue, Value *value)
{
  int      rc          = GNCDB_SUCCESS;
  AttrType target_type = ArithmeticExprGetValueType(arithmeticExpr);
  if ((leftValue != NULL && leftValue->attrType == NULLS) || (rightValue != NULL && rightValue->attrType == NULLS)) {
    valueSetNull(value);
    return rc;
  }

  switch (arithmeticExpr->arithmeticType) {
    case ARITH_ADD:
      if (target_type == INTS) {
        valueSetInt(value, valueGetInt(leftValue) + valueGetInt(rightValue));
      } else {
        valueSetDouble(value, valueGetFloat(leftValue) + valueGetFloat(rightValue));
      }
      break;

    case ARITH_SUB:
      if (target_type == INTS) {
        valueSetInt(value, valueGetInt(leftValue) - valueGetInt(rightValue));
      } else {
        valueSetDouble(value, valueGetFloat(leftValue) - valueGetFloat(rightValue));
      }
      break;

    case ARITH_MUL:
      if (target_type == INTS) {
        valueSetInt(value, valueGetInt(leftValue) * valueGetInt(rightValue));
      } else {
        valueSetDouble(value, valueGetFloat(leftValue) * valueGetFloat(rightValue));
      }

      break;

    case ARITH_DIV:
      if (target_type == INTS) {
        if (valueGetInt(rightValue) == 0) {
          // NOTE: 设置为整数最大值是不正确的。通常的做法是设置为NULL
          // valueSetInt(value, INT_MAX);
          valueSetNull(value);
        } else {
          valueSetInt(value, valueGetInt(leftValue) / valueGetInt(rightValue));
        }
      } else {
        if (valueGetFloat(rightValue) > -EPSILON && valueGetFloat(rightValue) < EPSILON) {
          // NOTE: 设置为浮点数最大值是不正确的。通常的做法是设置为NULL
          // valueSetDouble(value, FLT_MAX);
          valueSetNull(value);
        } else {
          valueSetDouble(value, valueGetFloat(leftValue) / valueGetFloat(rightValue));
        }
      }
      break;
    case ARITH_NEGATIVE:
      if (target_type == INTS) {
        valueSetInt(value, -valueGetInt(leftValue));
      } else {
        valueSetDouble(value, -valueGetFloat(leftValue));
      }
      break;
    default: {
      rc = GNCDB_INTERNAL;
    } break;
  }
  return rc;
}

int ArithmeticExprTryGetValue(ArithmeticExpr *arithmetic_expr, Value *value)
{
  int    rc = GNCDB_SUCCESS;
  Value *left_value;
  Value *right_value;

  left_value = valueCreate();
  if (left_value == NULL) {
    return GNCDB_MEM;
  }
  right_value = valueCreate();
  if (right_value == NULL) {
    valueDestroy(&left_value);
    return GNCDB_MEM;
  }
  rc = exprTryGetValue(arithmetic_expr->left, left_value);
  if (rc != GNCDB_SUCCESS) {
    valueDestroy(&left_value);
    valueDestroy(&right_value);
    return rc;
  }
  if (arithmetic_expr->right != NULL) {
    rc = exprTryGetValue(arithmetic_expr->right, right_value);
    if (rc != GNCDB_SUCCESS) {
      valueDestroy(&left_value);
      valueDestroy(&right_value);
      return rc;
    }
  }
  rc = ArithmeticExprCalcValue(arithmetic_expr, left_value, right_value, value);
  valueDestroy(&left_value);
  valueDestroy(&right_value);
  return rc;
}

/*表达式获取值的函数*/
int exprTryGetValue(Expression *expr, Value *cell)
{
  int rc = GNCDB_SUCCESS;
  switch (expr->type) {
    case ETG_VALUE:
      // 获取常量表达式的值
      *cell = *((ValueExpr *)expr)->value;
      break;
    case ETG_COMPARISON:
      // 获取比较表达式的值
      rc = ComparisonExprTryGetValue((ComparisonExpr *)expr, cell);
      break;
    case ETG_CAST:
      // 获取类型转换表达式的值
      rc = CastExprTryGetValue((CastExpr *)expr, cell);
      break;

    case ETG_ARITHMETIC: rc = ArithmeticExprTryGetValue((ArithmeticExpr *)expr, cell); break;

    default:
      // printf("unsupported expression type. type=%d\n", expr->type);
      rc = GNCDB_INTERNAL;
      break;
  }
  return rc;
}

static int setValueFromColMeta(ColMeta *colMeta, Record *tuple, Value *value)
{
  BYTE *bitmap = tuple->data + colMeta->bitmapOffset;
  int   isNULL = leafTupleGetBitMap(bitmap, colMeta->index, 0);

  if (isNULL == 0) {
    valueSetNull(value);
  } else {
    switch (colMeta->fieldType) {
      case FIELDTYPE_INTEGER:
        value->attrType          = INTS;
        value->numValue.intValue = *(int *)(tuple->data + colMeta->offset);
        break;
      case FIELDTYPE_REAL:
        value->attrType             = DOUBLES;
        value->numValue.doubleValue = *(double *)(tuple->data + colMeta->offset);
        break;
      case FIELDTYPE_VARCHAR: valueSetStringNoCopy(value, (char *)(tuple->data + colMeta->offset)); break;
      default: printf("Unsupported field type: %d\n", colMeta->fieldType); return GNCDB_INTERNAL;
    }
  }
  return GNCDB_SUCCESS;
}

int FieldExprGetValue(FieldExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int      rc        = GNCDB_SUCCESS;
  int      i         = 0;
  char    *tableName = NULL;
  char    *fieldName = NULL;
  ColMeta *colMeta   = NULL;
  Meta    *meta      = NULL;

  if (expr == NULL || tuple == NULL || colMetas == NULL || value == NULL) {
    return GNCDB_PARAMNULL;
  }

  if (expr->listptr != (void *)colMetas) {
    tableName = expr->tableName;
    fieldName = expr->fieldName;

    if (!tableName || !fieldName) {
      return GNCDB_INTERNAL;
    }
    for (i = 0; i < colMetas->elementCount; i++) {
      meta = varArrayListGetPointer(colMetas, i);
      if (!meta || meta->type != NormalField) {
        continue;
      }
      colMeta = (ColMeta *)meta;
      if (colMeta->aggrType != AGG_INVALID || colMeta->isBitMap) {
        continue;
      }
      if (0 == strcmp(tableName, colMeta->tableName) && 0 == strcmp(colMeta->name, fieldName)) {
        if (DB_FAILED(rc = setValueFromColMeta(colMeta, tuple, value))) {
          return rc;
        } else {
          expr->colMeta = colMeta;
          expr->listptr = (void *)colMetas;
          return rc;
        }
      }
    }
  } else {
    colMeta = (ColMeta *)expr->colMeta;
    if (colMeta == NULL) {
      return GNCDB_INTERNAL;
    }
    // 直接调用setValueFromColMeta
    return setValueFromColMeta(colMeta, tuple, value);
  }
  return GNCDB_NOT_FOUND;
}

int ValueExprGetValue(ValueExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  // copyValueTo(expr->value, value);
  memcpy(value, expr->value, sizeof(Value));
  value->isMalloc = false;
  return GNCDB_SUCCESS;
}

int CastExprGetValue(CastExpr *expr, Record *tuple, varArrayList *colMetas, Value *cell)
{
  int rc = expressionGetValueNCP(expr->child, tuple, colMetas, cell);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return CastExprCast(expr, cell, cell);
}

SubQueryExpr *openSubqueryExpr(Expression *expr)
{
  int           rc           = GNCDB_SUCCESS;
  SubQueryExpr *subQueryExpr = NULL;

  if (expr->type == ETG_SUBQUERY) {
    subQueryExpr = (SubQueryExpr *)expr;
    rc           = PhysicalOperatorOpen(subQueryExpr->physicalOper, subQueryExpr->subEvent);
    if (rc != GNCDB_SUCCESS) {
      return NULL;
    }
  }
  return subQueryExpr;
}

void closeSubqueryExpr(Expression *expr)
{
  SubQueryExpr *subQueryExpr = NULL;
  if (expr->type == ETG_SUBQUERY) {
    subQueryExpr = (SubQueryExpr *)expr;
    PhysicalOperatorClose(subQueryExpr->physicalOper, subQueryExpr->subEvent);
  }
}

int ComparisonExprGetValue(ComparisonExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int           rc = GNCDB_SUCCESS;
  Value         leftValue  = {0};
  Value         rightValue  = {0};
  SubQueryExpr *leftSubqueryExpr  = NULL;
  SubQueryExpr *rightSubqueryExpr = NULL;
  bool          res               = false;  // 有一样的值
  bool          boolValue         = false;

  // leftValue = valueCreate();
  // if (leftValue == NULL) {
  //   return GNCDB_MEM;
  // }
  // rightValue = valueCreate();
  // if (rightValue == NULL) {
  //   valueDestroy(&leftValue);
  //   return GNCDB_MEM;
  // }

  if (expr->left->type == ETG_SUBQUERY) {
    leftSubqueryExpr = openSubqueryExpr(expr->left);
  }
  if (expr->right->type == ETG_SUBQUERY) {
    rightSubqueryExpr = openSubqueryExpr(expr->right);
  }

  if (expr->comp == CMPOP_EXISTS_OP || expr->comp == CMPOP_NOT_EXISTS_OP) {
    rc = expressionGetValueNCP(expr->right, tuple, colMetas, &rightValue);
    valueSetBoolean(value, expr->comp == CMPOP_EXISTS_OP ? rc == GNCDB_SUCCESS : rc == GNCDB_NEXT_EOF);
    closeSubqueryExpr(expr->left);
    closeSubqueryExpr(expr->right);
    valueReset(&leftValue);
    valueReset(&rightValue);
    return rc == GNCDB_NEXT_EOF ? GNCDB_SUCCESS : rc;
  }

  rc = expressionGetValueNCP(expr->left, tuple, colMetas, &leftValue);
  if (rc != GNCDB_SUCCESS) {
    closeSubqueryExpr(expr->left);
    closeSubqueryExpr(expr->right);
    valueReset(&leftValue);
    valueReset(&rightValue);
    return rc;
  }
  if (leftSubqueryExpr && ExprHasMoreRow((Expression *)leftSubqueryExpr, tuple, colMetas)) {
    closeSubqueryExpr(expr->left);
    closeSubqueryExpr(expr->right);
    valueReset(&leftValue);
    valueReset(&rightValue);
    return GNCDB_PARAM_INVALID;
  }

  // 不考虑空值null的情况
  if (expr->comp == CMPOP_IN_OP || expr->comp == CMPOP_NOT_IN_OP) {
    if (expr->right->type == ETG_EXPRLIST) {
      ((ExprListExpr *)expr->right)->curIdx = 0;
    }
    while (GNCDB_SUCCESS == (rc = expressionGetValueNCP(expr->right, tuple, colMetas, &rightValue))) {
      if (valueCompare(&leftValue, &rightValue) == 0) {
        res = true;
      }
    }
    valueSetBoolean(value, expr->comp == CMPOP_IN_OP ? res : !res);
    closeSubqueryExpr(expr->left);
    closeSubqueryExpr(expr->right);
    valueReset(&leftValue);
    valueReset(&rightValue);
    return rc == GNCDB_NEXT_EOF ? GNCDB_SUCCESS : rc;
  }

  rc = expressionGetValueNCP(expr->right, tuple, colMetas, &rightValue);
  if (rc != GNCDB_SUCCESS) {
    closeSubqueryExpr(expr->left);
    closeSubqueryExpr(expr->right);
    valueReset(&leftValue);
    valueReset(&rightValue);
    return rc;
  }

  if (rightSubqueryExpr && ExprHasMoreRow((Expression *)rightSubqueryExpr, tuple, colMetas)) {
    closeSubqueryExpr(expr->left);
    closeSubqueryExpr(expr->right);
    valueReset(&leftValue);
    valueReset(&rightValue);
    return GNCDB_PARAM_INVALID;
  }

  rc = ComparisonExprCompareValue(&expr->comp, &leftValue, &rightValue, &boolValue);
  if (rc == GNCDB_SUCCESS) {
    valueSetBoolean(value, boolValue);
  }

  if (expr->left->type == ETG_SUBQUERY) {
    closeSubqueryExpr(expr->left);
  }
  if (expr->right->type == ETG_SUBQUERY) {
    closeSubqueryExpr(expr->right);
  }
  // valueReset(&leftValue);
  // valueReset(&rightValue);
  return rc;
}

int ComparisonExprGetValue1(ComparisonExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int           rc = GNCDB_SUCCESS;
  Value         leftValue = {0};
  Value         rightValue = {0};
  SubQueryExpr *leftSubqueryExpr  = NULL;
  SubQueryExpr *rightSubqueryExpr = NULL;
  bool          res               = false;  // 有一样的值
  bool          boolValue         = false;

  // 初始化子查询表达式
  if (expr->left->type == ETG_SUBQUERY) {
    leftSubqueryExpr = openSubqueryExpr(expr->left);
  }

  if (expr->right->type == ETG_SUBQUERY) {
    rightSubqueryExpr = openSubqueryExpr(expr->right);
  }

  // 处理 EXISTS 或 NOT EXISTS 操作符
  if (expr->comp == CMPOP_EXISTS_OP || expr->comp == CMPOP_NOT_EXISTS_OP) {
    rc = expressionGetValueNCP(expr->right, tuple, colMetas, &rightValue);
    valueSetBoolean(value, expr->comp == CMPOP_EXISTS_OP ? rc == GNCDB_SUCCESS : rc == GNCDB_NEXT_EOF);
    // closeSubqueryExpr(expr->left);
    // closeSubqueryExpr(expr->right);
    // valueReset(&leftValue);
    // valueReset(&rightValue);
    if (rc == GNCDB_NEXT_EOF) {
      rc = GNCDB_SUCCESS;
    }
  } else {
    // 获取左操作数的值
    if (DB_SUCCESS(rc = expressionGetValueNCP(expr->left, tuple, colMetas, &leftValue))) {
      // 检查左操作数是否是多行子查询
      if (leftSubqueryExpr && ExprHasMoreRow((Expression *)leftSubqueryExpr, tuple, colMetas)) {
        rc = GNCDB_PARAM_INVALID;
      } else {
        // 处理 IN 或 NOT IN 操作符
        if (expr->comp == CMPOP_IN_OP || expr->comp == CMPOP_NOT_IN_OP) {
          if (expr->right->type == ETG_EXPRLIST) {
            ((ExprListExpr *)expr->right)->curIdx = 0;
          }

          while (GNCDB_SUCCESS == (rc = expressionGetValueNCP(expr->right, tuple, colMetas, &rightValue))) {
            if (valueCompare(&leftValue, &rightValue) == 0) {
              res = true;
            }
          }

          valueSetBoolean(value, expr->comp == CMPOP_IN_OP ? res : !res);

          if (rc == GNCDB_NEXT_EOF) {
            rc = GNCDB_SUCCESS;
          }
        } else {
          // 获取右操作数的值
          rc = expressionGetValueNCP(expr->right, tuple, colMetas, &rightValue);

          if (rc == GNCDB_SUCCESS) {
            // 检查右操作数是否是多行子查询
            if (rightSubqueryExpr && ExprHasMoreRow((Expression *)rightSubqueryExpr, tuple, colMetas)) {
              rc = GNCDB_PARAM_INVALID;
            } else {
              // 执行比较操作
              rc = ComparisonExprCompareValue(&expr->comp, &leftValue, &rightValue, &boolValue);

              if (rc == GNCDB_SUCCESS) {
                valueSetBoolean(value, boolValue);
              }
            }
          }
        }
      }
    }
  }

  // 清理资源
  if (expr->left->type == ETG_SUBQUERY) {
    closeSubqueryExpr(expr->left);
  }

  if (expr->right->type == ETG_SUBQUERY) {
    closeSubqueryExpr(expr->right);
  }

  // 释放临时变量
  valuefreeStr(&leftValue);
  valuefreeStr(&rightValue);

  // 统一在函数末尾返回结果
  return rc;
}

int ComparisonExprGetValue2(ComparisonExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int   rc = GNCDB_SUCCESS;
  Value leftValue;
  Value rightValue;
  bool  boolValue = false;
  if (!expr) {
    return GNCDB_PARAMNULL;
  } else if (expr->left->type == ETG_FIELD && expr->right->type == ETG_FIELD) {
    rc = FieldExprGetValue((FieldExpr *)expr->left, tuple, colMetas, &leftValue);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = FieldExprGetValue((FieldExpr *)expr->right, tuple, colMetas, &rightValue);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    rc = ComparisonExprCompareValue(&expr->comp, &leftValue, &rightValue, &boolValue);
    if (rc == GNCDB_SUCCESS) {
      valueSetBoolean(value, boolValue);
    }
    return rc;
  } else if (expr->left->type == ETG_FIELD && expr->right->type == ETG_VALUE) {
    rc = FieldExprGetValue((FieldExpr *)expr->left, tuple, colMetas, &leftValue);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = ComparisonExprCompareValue(&expr->comp, &leftValue, ((ValueExpr *)expr->right)->value, &boolValue);
    if (rc == GNCDB_SUCCESS) {
      valueSetBoolean(value, boolValue);
    }
    return rc;
  } else if (expr->left->type == ETG_VALUE && expr->right->type == ETG_FIELD) {
    rc = FieldExprGetValue((FieldExpr *)expr->right, tuple, colMetas, &rightValue);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = ComparisonExprCompareValue(&expr->comp, ((ValueExpr *)expr->left)->value, &rightValue, &boolValue);
    if (rc == GNCDB_SUCCESS) {
      valueSetBoolean(value, boolValue);
    }
    return rc;
  } else if (expr->left->type == ETG_VALUE && expr->right->type == ETG_VALUE) {
    rc = ComparisonExprCompareValue(
        &expr->comp, ((ValueExpr *)expr->left)->value, ((ValueExpr *)expr->right)->value, &boolValue);
    if (rc == GNCDB_SUCCESS) {
      valueSetBoolean(value, boolValue);
    }
    return rc;
  } else {
    return ComparisonExprGetValue1(expr, tuple, colMetas, value);
  }
  return rc;
}

int ConjunctionExprGetValue(ConjunctionExpr *conjExpr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int         rc           = GNCDB_SUCCESS;
  Value      *tmpValue     = NULL;
  int         i            = 0;
  Expression *expr         = NULL;
  bool        boolValue    = false;
  bool        defaultValue = false;

  if (conjExpr->children->elementCount == 0) {
    VALUE_SET_BOOLEAN(value, true);
    return rc;
  }
  tmpValue = valueCreate();
  if (tmpValue == NULL) {
    return GNCDB_MEM;
  }
  for (i = 0; i < conjExpr->children->elementCount; i++) {
    expr = varArrayListGetPointer(conjExpr->children, i);
    rc   = expressionGetValueNCP(expr, tuple, colMetas, tmpValue);
    if (rc != GNCDB_SUCCESS) {
      valueDestroy(&tmpValue);
      return rc;
    }
    boolValue = valueGetBoolean(tmpValue);
    if ((conjExpr->conjunctionType == CJET_AND && !boolValue) || (conjExpr->conjunctionType == CJET_OR && boolValue)) {
      VALUE_SET_BOOLEAN(value, boolValue);
      valueDestroy(&tmpValue);
      return rc;
    }
  }
  defaultValue = conjExpr->conjunctionType == CJET_AND;
  VALUE_SET_BOOLEAN(value, defaultValue);
  valueDestroy(&tmpValue);
  return rc;
}

int ArithmeticExprGetValue(ArithmeticExpr *arithmeticExpr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int    rc         = GNCDB_SUCCESS;
  Value *leftValue  = NULL;
  Value *rightValue = NULL;

  leftValue = valueCreate();
  if (leftValue == NULL) {
    return GNCDB_MEM;
  }
  rightValue = valueCreate();
  if (rightValue == NULL) {
    valueDestroy(&leftValue);
    return GNCDB_MEM;
  }
  rc = expressionGetValueNCP(arithmeticExpr->left, tuple, colMetas, leftValue);
  if (rc != GNCDB_SUCCESS) {
    valueDestroy(&leftValue);
    valueDestroy(&rightValue);
    return rc;
  }
  if (arithmeticExpr->right != NULL) {
    rc = expressionGetValueNCP(arithmeticExpr->right, tuple, colMetas, rightValue);
    if (rc != GNCDB_SUCCESS) {
      valueDestroy(&leftValue);
      valueDestroy(&rightValue);
      return rc;
    }
  }
  rc = ArithmeticExprCalcValue(arithmeticExpr, leftValue, rightValue, value);
  valueDestroy(&leftValue);
  valueDestroy(&rightValue);
  return rc;
}

int SubQueryExprGetValue(SubQueryExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int               rc               = GNCDB_SUCCESS;
  SQLStageEvent    *sqlEvent         = NULL;
  Record           *currentTuple     = NULL;
  varArrayList     *cols             = NULL;
  Meta             *meta             = NULL;
  BYTE             *bitmap           = NULL;
  CompositeMeta    *compositeMeta    = NULL;
  PhysicalOperator *child            = NULL;
  int               realChildColsNum = 0;
  char             *str              = NULL;

  cols = getColMeta(expr->physicalOper);
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }

  sqlEvent = expr->subEvent;
  if (GNCDB_SUCCESS != (rc = PhysicalOperatorNext(expr->physicalOper, sqlEvent))) {
    return rc;
  }
  currentTuple = GetCurrentTuple(expr->physicalOper);
  // rc           = tupleCellAt(currentTuple, 0, value);
  meta = varArrayListGetPointer(cols, 0);
  // if (meta->fieldType == FIELDTYPE_INTEGER) {
  //   valueSetInt(value, *(int *)currentTuple->data + meta->offset);
  // } else if (meta->fieldType == FIELDTYPE_REAL) {
  //   valueSetDouble(value, *(double *)currentTuple->data + meta->offset);
  // } else if (meta->fieldType == FIELDTYPE_VARCHAR) {
  //   char *str = my_malloc0(meta->len + 1);
  //   strncpy(str, (char *)currentTuple->data + meta->offset, meta->len);
  //   valueSetString(value, str);
  //   my_free(str);
  // } else {
  //   assert(false);  // 暂时不支持其他类型
  // }

  child            = varArrayListGetPointer(expr->physicalOper->children, 0);
  realChildColsNum = child->cols->elementCount - 1;
  if (meta->type == NormalField) {
    bitmap = currentTuple->data + meta->bitmapOffset;
    if (leafTupleGetBitMap(bitmap, meta->index, realChildColsNum) == 0) {
      // tmp = my_strdup("NULL");
      valueSetNull(value);
    } else {
      if (meta->fieldType == FIELDTYPE_INTEGER) {
        valueSetInt(value, *(int *)(currentTuple->data + meta->offset));
      } else if (meta->fieldType == FIELDTYPE_REAL) {
        valueSetDouble(value, *(double *)(currentTuple->data + meta->offset));
      } else if (meta->fieldType == FIELDTYPE_VARCHAR) {
        str = my_malloc0(meta->len + 1);
        strncpy(str, (char *)(currentTuple->data + meta->offset), meta->len);
        valueSetString(value, str);
        my_free(str);
      } else {
        assert(false);  // 暂时不支持其他类型
      }
    }
  } else {
    compositeMeta = (CompositeMeta *)meta;
    if (compositeMeta == NULL) {
      return GNCDB_INTERNAL;
    }
    rc = expressionGetValueNCP(compositeMeta->expr, tuple, child->cols, value);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return rc;
}

int ExprListExprGetValue(ExprListExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int         rc      = GNCDB_SUCCESS;
  Expression *exprTmp = NULL;

  if (expr->curIdx >= expr->exprs->elementCount) {
    return GNCDB_NEXT_EOF;
  }
  exprTmp = (Expression *)varArrayListGetPointer(expr->exprs, expr->curIdx++);
  rc      = expressionGetValueNCP(exprTmp, tuple, colMetas, value);
  return rc;
}

int AggrFuncExprGetValue(AggrFuncExpr *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int   rc      = GNCDB_SUCCESS;
  Meta *colMeta = NULL;
  char *str     = NULL;
  // TupleCellSpec *spec = TupleCellSpecCreateByName(expr->name);
  // if (spec == NULL) {
  //   return GNCDB_MEM;
  // }
  // rc = tupleFindCell(tuple, spec, value);
  // TupleCellSpecDestroy(spec);
  colMeta = matchAggrFunc(expr, colMetas);
  if (colMeta == NULL) {
    return GNCDB_INTERNAL;
  }
  if (colMeta->fieldType == FIELDTYPE_INTEGER) {
    valueSetInt(value, *(int *)(tuple->data + colMeta->offset));
  } else if (colMeta->fieldType == FIELDTYPE_REAL) {
    valueSetDouble(value, *(double *)(tuple->data + colMeta->offset));
  } else if (colMeta->fieldType == FIELDTYPE_VARCHAR) {
    str = my_malloc0(colMeta->len + 1);
    strncpy(str, (char *)(tuple->data + colMeta->offset), colMeta->len);
    valueSetString(value, str);
    my_free(str);
  } else {
    assert(false);  // 暂时不支持其他类型
  }
  return rc;
}

int expressionGetValueNCP(Expression *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int rc = GNCDB_SUCCESS;
  if (expr == NULL) {
    return GNCDB_PARAM_INVALID;
  }
  // valueReset(value);  // 这里reset是为了释放value中可能已分配的字符串str内存
  switch (expr->type) {
    case ETG_FIELD: {
      rc = FieldExprGetValue((FieldExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_VALUE: {
      rc = ValueExprGetValue((ValueExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_CAST: {
      rc = CastExprGetValue((CastExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_COMPARISON: {
      rc = ComparisonExprGetValue2((ComparisonExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_CONJUNCTION: {
      rc = ConjunctionExprGetValue((ConjunctionExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_ARITHMETIC: {
      rc = ArithmeticExprGetValue((ArithmeticExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_SUBQUERY: {
      rc = SubQueryExprGetValue((SubQueryExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_EXPRLIST: {
      rc = ExprListExprGetValue((ExprListExpr *)expr, tuple, colMetas, value);
    } break;
    case ETG_AGGRFUNC: {
      rc = AggrFuncExprGetValue((AggrFuncExpr *)expr, tuple, colMetas, value);
    } break;
    default: {
      rc = GNCDB_INTERNAL;
    } break;
  }
  // value->isMalloc = t;
  return rc;
}

int expressionGetValueDC(Expression *expr, Record *tuple, varArrayList *colMetas, Value *value)
{
  int rc = GNCDB_SUCCESS;
  if (DB_SUCCESS(rc = expressionGetValueNCP(expr, tuple, colMetas, value))) {
    if (value->attrType == CHARS) {
      value->strValue = my_strdup(value->strValue);
      if (value->strValue == NULL) {
        return GNCDB_MEM;
      }
      value->isMalloc = true;
    }
  }
  return rc;
}

AttrType AggrFuncExprGetValueType(AggrFuncExpr *expr)
{
  switch (expr->aggrType) {
    case AGG_MIN:
    case AGG_MAX:
    case AGG_SUM: {
      return expressionGetValueType(expr->param);
    } break;
    case AGG_AVG: {
      return DOUBLES;
    } break;
    case AGG_COUNT: {
      return INTS;
    } break;
    default: return UNDEFINED; break;
  }
  return UNDEFINED;
}

AttrType expressionGetValueType(Expression *expr)
{
  if (expr->type == ETG_FIELD) {
    return (AttrType)((FieldExpr *)expr)->fieldType;
  } else if (expr->type == ETG_VALUE) {
    return ((ValueExpr *)expr)->value->attrType;
  } else if (expr->type == ETG_CAST) {
    return ((CastExpr *)expr)->castType;
  } else if (expr->type == ETG_COMPARISON) {
    return BOOLEANS;
  } else if (expr->type == ETG_CONJUNCTION) {
    return BOOLEANS;
  } else if (expr->type == ETG_ARITHMETIC) {
    return ArithmeticExprGetValueType((ArithmeticExpr *)expr);
  } else if (expr->type == ETG_AGGRFUNC) {
    return AggrFuncExprGetValueType((AggrFuncExpr *)expr);
  }
  return UNDEFINED;
}

int expressionGetValueLen(Expression *expr, varArrayList *colMetas)
{
  Meta           *meta           = NULL;
  ColMeta        *colMeta        = NULL;
  ArithmeticExpr *arithmeticExpr = NULL;
  int             i              = 0;
  AttrType        type           = expressionGetValueType(expr);
  if (type == INTS) {
    return sizeof(int);
  } else if (type == DOUBLES) {
    return sizeof(double);
  } else if (type == CHARS) {
    colMeta = NULL;
    if (expr->type == ETG_FIELD) {
      for (i = 0; i < colMetas->elementCount; i++) {
        meta = varArrayListGetPointer(colMetas, i);
        if (meta->type != NormalField) {
          continue;
        }
        colMeta = (ColMeta *)meta;
        if (colMeta->aggrType == AGG_INVALID && strcmp(colMeta->name, ((FieldExpr *)expr)->fieldName) == 0) {
          return colMeta->len;
        }
      }
      return -1;
    } else if (expr->type == ETG_VALUE) {
      // assert(false);// 不应该存在这种情形
      return ((ValueExpr *)expr)->value->length;
    } else if (expr->type == ETG_ARITHMETIC) {
      arithmeticExpr = (ArithmeticExpr *)expr;
      if (arithmeticExpr->left != NULL && arithmeticExpr->right == NULL) {
        return expressionGetValueLen(arithmeticExpr->left, colMetas);
      } else if (arithmeticExpr->right != NULL && arithmeticExpr->left == NULL) {
        return expressionGetValueLen(arithmeticExpr->right, colMetas);
      } else if (arithmeticExpr->left == NULL || arithmeticExpr->right == NULL) {
        return -1;
      }
    } else if (expr->type == ETG_AGGRFUNC) {
      return expressionGetValueLen(((AggrFuncExpr *)expr)->param, colMetas);
    }

  } else if (type == BOOLEANS) {
    return sizeof(bool);
  } else if (type == NULLS) {
    return -1;
  } else if (type == UNDEFINED) {
    return -1;
  } else {
    printf("unsupported type. type=%d\n", type);
    return -1;
  }
  return 0;
}

bool fieldTableExists(char *fieldName, char *tableName, GNCDB *db)
{
  TableSchema *tableSchema = NULL;
  Column      *column      = NULL;
  int          i           = 0;

  tableSchema = getTableSchema(db->catalog, tableName);
  for (i = 0; i < tableSchema->columnList->elementCount; i++) {
    column = varArrayListGetPointer(tableSchema->columnList, i);
    if (0 == strcmp(column->fieldName, fieldName)) {
      return true;
    }
  }
  return false;
}

// 检查表达式中出现的 表,列 是否存在
int fieldExprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, FieldExpr *fieldExpr,
    BtreeTable *defaultTable, HashMap *tableAliasMap)
{
  char            *tableName     = NULL;
  char            *fieldName     = NULL;
  BtreeTable      *table         = NULL;
  BtreeTable      *tmpTable      = NULL;
  HashMapIterator *iter          = NULL;
  Column          *column        = NULL;
  TableSchema     *tableSchema   = NULL;
  bool             fieldExist    = false;
  int              i             = 0;
  bool             isSingleTable = false;
  char            *alias         = NULL;

  tableName = fieldExpr->tableName;
  fieldName = fieldExpr->fieldName;
  /* 先获取表 */
  /* 1.如果表名不为空 */
  if (!isBlank(tableName)) {
    // table = hashMapGet(tableMap, tableName);
    // table = hashMapGet(db->catalog->tableMap, tableName);
    catalogGetTable(db->catalog, &table, tableName);
    if (NULL == table) {
      // LOG(LOG_WARNING,"no such table. table_name=%s", table_name);
      return GNCDB_TABLE_NOT_FOUND;
    }

  }
  /* 2.如果表名为空 */
  else {
    iter = createHashMapIterator(tableMap);
    for (; hasNextHashMapIterator(iter);) {
      iter     = nextHashMapIterator(iter);
      tmpTable = iter->entry->value;
      if (fieldTableExists(fieldExpr->fieldName, tmpTable->tableName, db)) {
        table = tmpTable;
        tableName = table->tableName;
      }
    }
    if (NULL == table) {
      freeHashMapIterator(&iter);
      return GNCDB_FIELD_NOT_EXIST;
    }
    freeHashMapIterator(&iter);
  }

  /*3.检查列是否是合法的列，也就是存在于相应的表中*/
  if (isBlank(fieldName)) {
    return GNCDB_INTERNAL;
  }
  tableSchema = getTableSchemaUnLock(db->catalog, table->tableName);
  {
    /*3.1检查字段名是否是tables中的某个列*/
    for (i = 0; i < tableSchema->columnList->elementCount; i++) {
      column = varArrayListGetPointer(tableSchema->columnList, i);
      if (0 == strcmp(column->fieldName, fieldName)) {
        fieldExist = true;
        break;
      }
    }
    if (!fieldExist) {
      printf("no such field. table_name=%s, field_name=%s", table->tableName, fieldName);
      return GNCDB_SCHEMA_FIELD_MISSING;
    }

    /*3.2统一字段的名称，先统一fieldname，再统一别名*/
    isSingleTable = (tableMap->entryCount == 1);
    if (fieldExpr->tableName == NULL) {
      fieldExpr->tableName = my_strdup(table->tableName);
    }
    fieldExpr->fieldType = column->fieldType;
    fieldExprSetName(table->tableName, fieldName, fieldExpr, isSingleTable);
    if (tableAliasMap != NULL && tableAliasMap->entryCount > 0) {
      if (isSingleTable) {
        fieldExpr->alias = fieldName;
      } else {
        alias = hashMapGet(tableAliasMap, table->tableName);
        if (alias != NULL) {
          fieldExpr->alias = (char *)my_malloc0(strlen(alias) + strlen(fieldName) + 2);
          sprintf(fieldExpr->alias, "%s.%s", alias, fieldName);
        } else {
          fieldExpr->alias = (char *)my_malloc0(strlen(fieldExpr->tableName) + strlen(fieldName) + 2);
          sprintf(fieldExpr->alias, "%s.%s", fieldExpr->tableName, fieldName);
        }
      }
    }
  }
  return GNCDB_SUCCESS;
}

int castExprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, CastExpr *expr, BtreeTable *defaultTable,
    HashMap *tableAliasMap)
{
  return exprCheckField(tableMap, tables, db, expr->child, defaultTable, tableAliasMap);
}

int comparisonExprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, ComparisonExpr *expr,
    BtreeTable *defaultTable, HashMap *tableAliasMap)
{
  int rc = GNCDB_SUCCESS;
  /*1.检查左表达式*/
  rc = exprCheckField(tableMap, tables, db, expr->left, defaultTable, tableAliasMap);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  } else {
    /*2.左表达式合法，检查右表达式*/
    rc = exprCheckField(tableMap, tables, db, expr->right, defaultTable, tableAliasMap);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return GNCDB_SUCCESS;
}

int conjunctionExprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, ConjunctionExpr *expr,
    BtreeTable *defaultTable, HashMap *tableAliasMap)
{
  int         rc         = GNCDB_SUCCESS;
  int         i          = 0;
  Expression *child_expr = NULL;

  /*1.检查联结条件中的每一个比较条件*/
  for (i = 0; i < expr->children->elementCount; i++) {
    child_expr = varArrayListGetPointer(expr->children, i);
    rc         = exprCheckField(tableMap, tables, db, child_expr, defaultTable, tableAliasMap);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return rc;
}

int arithmeticExprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, ArithmeticExpr *expr,
    BtreeTable *defaultTable, HashMap *tableAliasMap)
{
  int rc = GNCDB_SUCCESS;
  rc     = exprCheckField(tableMap, tables, db, expr->left, defaultTable, tableAliasMap);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = exprCheckField(tableMap, tables, db, expr->right, defaultTable, tableAliasMap);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

int aggrFuncCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, AggrFuncExpr *expr, BtreeTable *defaultTable,
    HashMap *tableAliasMap)
{
  return exprCheckField(tableMap, tables, db, expr->param, defaultTable, tableAliasMap);
}

int exprListExprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, ExprListExpr *expr,
    BtreeTable *defaultTable, HashMap *tableAliasMap)
{
  int         rc      = GNCDB_SUCCESS;
  int         i       = 0;
  Expression *exprTmp = NULL;
  /*1.检查每一个表达式*/
  for (i = 0; i < expr->exprs->elementCount; i++) {
    exprTmp = varArrayListGetPointer(expr->exprs, i);
    rc      = exprCheckField(tableMap, tables, db, exprTmp, defaultTable, tableAliasMap);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return GNCDB_SUCCESS;
}

int exprCheckField(HashMap *tableMap, varArrayList *tables, GNCDB *db, Expression *expr, BtreeTable *defaultTable,
    HashMap *tableAliasMap)
{
  if (expr == NULL) {
    return GNCDB_SUCCESS;
  }
  switch (expr->type) {
    case ETG_FIELD: {
      /*列类型检查*/
      return fieldExprCheckField(tableMap, tables, db, (FieldExpr *)expr, defaultTable, tableAliasMap);
    } break;
    case ETG_CAST: {
      return castExprCheckField(tableMap, tables, db, (CastExpr *)expr, defaultTable, tableAliasMap);
    } break;

    case ETG_COMPARISON: {
      /*比较表达式类型检查*/
      return comparisonExprCheckField(tableMap, tables, db, (ComparisonExpr *)expr, defaultTable, tableAliasMap);
    } break;

    case ETG_CONJUNCTION: {
      /*联结表达式类型检查*/
      return conjunctionExprCheckField(tableMap, tables, db, (ConjunctionExpr *)expr, defaultTable, tableAliasMap);
    } break;

    case ETG_ARITHMETIC: {
      /*算术表达式类型检查*/
      return arithmeticExprCheckField(tableMap, tables, db, (ArithmeticExpr *)expr, defaultTable, tableAliasMap);
    } break;

    case ETG_AGGRFUNC: {
      /*聚合表达式类型检查*/
      return aggrFuncCheckField(tableMap, tables, db, (AggrFuncExpr *)expr, defaultTable, tableAliasMap);
    } break;

    case ETG_EXPRLIST:
      /*表达式列表类型检查*/
      return exprListExprCheckField(tableMap, tables, db, (ExprListExpr *)expr, defaultTable, tableAliasMap);
    default: return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

bool fieldExprCheckCanPushDown(FieldExpr *expr, HashMap *tableMap) { return hashMapExists(tableMap, expr->tableName); }
bool castExprCheckCanPushDown(CastExpr *expr, HashMap *tableMap) { return exprCheckCanPushDown(expr->child, tableMap); }

bool comparisonExprCheckCanPushDown(ComparisonExpr *expr, HashMap *tableMap)
{
  return exprCheckCanPushDown(expr->left, tableMap) && exprCheckCanPushDown(expr->right, tableMap);
}

bool conjunctionExprCheckCanPushDown(ConjunctionExpr *expr, HashMap *tableMap)
{
  int i = 0;
  for (i = 0; i < expr->children->elementCount; i++) {
    if (!exprCheckCanPushDown(varArrayListGetPointer(expr->children, i), tableMap)) {
      return false;
    }
  }
  return true;
}

bool arithmeticExprCheckCanPushDown(ArithmeticExpr *expr, HashMap *tableMap)
{
  if (!exprCheckCanPushDown(expr->left, tableMap)) {
    return false;
  }
  if (expr->arithmeticType != ARITH_NEGATIVE) {
    assert(expr->right != NULL);
    return exprCheckCanPushDown(expr->right, tableMap);
  }
  return true;
}
bool exprCheckCanPushDown(Expression *expr, HashMap *tableMap)
{
  switch (expr->type) {
    case ETG_FIELD: {
      return fieldExprCheckCanPushDown((FieldExpr *)expr, tableMap);
    } break;

    case ETG_VALUE: {
      return true;
    } break;

    case ETG_CAST: {
      return castExprCheckCanPushDown((CastExpr *)expr, tableMap);
    } break;

    case ETG_COMPARISON: {
      return comparisonExprCheckCanPushDown((ComparisonExpr *)expr, tableMap);
    } break;

    case ETG_CONJUNCTION: {
      return conjunctionExprCheckCanPushDown((ConjunctionExpr *)expr, tableMap);
    } break;

    case ETG_ARITHMETIC: {
      return arithmeticExprCheckCanPushDown((ArithmeticExpr *)expr, tableMap);
    } break;

    case ETG_AGGRFUNC: {
      return false;
    } break;

    case ETG_SUBQUERY: {
      return false;
    } break;

    default: return true;
  }
}

/**
 * @brief 检查是否是const表达式
 *
 * @param expr
 * @return int
 */
int exprCheckIsConstexpr(Expression *expr)
{
  int i = 0;
  switch (expr->type) {
    case ETG_FIELD: return false;
    case ETG_VALUE: return true;
    case ETG_CAST: return exprCheckIsConstexpr(((CastExpr *)expr)->child);
    case ETG_COMPARISON:
      return exprCheckIsConstexpr(((ComparisonExpr *)expr)->left) &&
             exprCheckIsConstexpr(((ComparisonExpr *)expr)->right);
    case ETG_CONJUNCTION: {
      for (i = 0; i < ((ConjunctionExpr *)expr)->children->elementCount; i++) {
        if (!exprCheckIsConstexpr(varArrayListGetPointer(((ConjunctionExpr *)expr)->children, i))) {
          return false;
        }
      }
      return true;
    }
    case ETG_ARITHMETIC:
      return exprCheckIsConstexpr(((ArithmeticExpr *)expr)->left) &&
             exprCheckIsConstexpr(((ArithmeticExpr *)expr)->right);
    case ETG_AGGRFUNC: return exprCheckIsConstexpr(((AggrFuncExpr *)expr)->param);
    case ETG_SUBQUERY: return false;
    case ETG_EXPRLIST: {
      for (i = 0; i < ((ExprListExpr *)expr)->exprs->elementCount; i++) {
        if (!exprCheckIsConstexpr(varArrayListGetPointer(((ExprListExpr *)expr)->exprs, i))) {
          return false;
        }
      }
      return true;
    }
    default: return false;
  }
  return false;
}

int aggrExprCheckIsConstexpr(AggrFuncExpr *expr) { return exprCheckIsConstexpr(expr->param); }

const char *aggrExprGetFuncName(AggrFuncType type)
{
  switch (type) {
    case AGG_COUNT: return "count";
    case AGG_SUM: return "sum";
    case AGG_AVG: return "avg";
    case AGG_MAX: return "max";
    case AGG_MIN: return "min";
    default: return "unknown_aggr_fun";
  }
}

int AggrFuncExprTraverseCheck(
    AggrFuncExpr *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  int     rc = GNCDB_SUCCESS;
  va_list args_copy1;
  va_list args_copy2;
  va_copy(args_copy1, args);
  va_copy(args_copy2, args);
  if (GNCDB_SUCCESS != (rc = checkFunc((Expression *)expr, fields, additionalParamsCount, args_copy1))) {
    va_end(args_copy1);
    va_end(args_copy2);
    return rc;
  } else if (!expr->paramIsConstexpr &&
             GNCDB_SUCCESS !=
                 (rc = exprTraverseCheck(expr->param, checkFunc, fields, additionalParamsCount, args_copy2))) {
    va_end(args_copy1);
    va_end(args_copy2);
    return rc;
  }

  va_end(args_copy1);
  va_end(args_copy2);
  return rc;
}

int ArithmeticExprTraverseCheck(
    ArithmeticExpr *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  int     rc = GNCDB_SUCCESS;
  va_list args_copy1;
  va_list args_copy2;
  va_list args_copy3;

  va_copy(args_copy1, args);
  va_copy(args_copy2, args);
  va_copy(args_copy3, args);
  if (GNCDB_SUCCESS != (rc = checkFunc((Expression *)expr, fields, additionalParamsCount, args_copy1))) {
    va_end(args_copy1);
    va_end(args_copy2);
    va_end(args_copy3);
    return rc;
  } else if (GNCDB_SUCCESS !=
             (rc = exprTraverseCheck(expr->left, checkFunc, fields, additionalParamsCount, args_copy2))) {
    va_end(args_copy1);
    va_end(args_copy2);
    va_end(args_copy3);
    return rc;
  } else if (expr->right && GNCDB_SUCCESS != (rc = exprTraverseCheck(
                                                  expr->right, checkFunc, fields, additionalParamsCount, args_copy3))) {
    va_end(args_copy1);
    va_end(args_copy2);
    va_end(args_copy3);
    return rc;
  }
  va_end(args_copy1);
  va_end(args_copy2);
  va_end(args_copy3);
  return rc;
}

int ConjunctionExprTraverseCheck(
    ConjunctionExpr *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  int         rc        = GNCDB_SUCCESS;
  int         i         = 0;
  Expression *childExpr = NULL;
  va_list     args_copy1;
  va_list     args_copy2;
  va_list     args_copy3;
  va_copy(args_copy1, args);
  va_copy(args_copy2, args);

  if (GNCDB_SUCCESS != (rc = checkFunc((Expression *)expr, fields, additionalParamsCount, args_copy1))) {
    va_end(args_copy1);
    va_end(args_copy2);
    return rc;
  }
  for (i = 0; i < expr->children->elementCount; i++) {
    childExpr = varArrayListGetPointer(expr->children, i);
    va_copy(args_copy3, args_copy2);
    if (GNCDB_SUCCESS != (rc = exprTraverseCheck(childExpr, checkFunc, fields, additionalParamsCount, args_copy3))) {
      va_end(args_copy1);
      va_end(args_copy2);
      va_end(args_copy3);
      return rc;
    }
    va_end(args_copy3);
  }
  va_end(args_copy1);
  va_end(args_copy2);
  return GNCDB_SUCCESS;
}

int ComparisonExprTraverseCheck(
    ComparisonExpr *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  int     rc                = GNCDB_SUCCESS;
  bool    isSubQueryExistOp = false;  // 是否存在子查询且op为Exsit，这种情况子查询的fieldnum不要求为1
  va_list args_copy1;
  va_list args_copy2;
  va_list args_copy3;

  va_copy(args_copy1, args);
  va_copy(args_copy2, args);
  va_copy(args_copy3, args);

  if (expr->comp == CMPOP_EXISTS_OP) {
    if (expr->left->type == ETG_VALUE && expr->right->type == ETG_SUBQUERY) {
      isSubQueryExistOp = true;
    }
  }

  if (GNCDB_SUCCESS != (rc = checkFunc((Expression *)expr, fields, additionalParamsCount, args_copy1))) {
    va_end(args_copy1);
    va_end(args_copy2);
    va_end(args_copy3);
    return rc;
  } else if (GNCDB_SUCCESS !=
             (rc = exprTraverseCheck(expr->left, checkFunc, fields, additionalParamsCount, args_copy2))) {
    va_end(args_copy1);
    va_end(args_copy2);
    va_end(args_copy3);
    return rc;
  } else if (expr->right != NULL &&
             GNCDB_SUCCESS !=
                 (rc = exprTraverseCheck(expr->right, checkFunc, fields, additionalParamsCount, args_copy3))) {
    if (isSubQueryExistOp && rc == GNCDB_SUBQUERY_FIELD_IS_NOT_ONE) {
      rc = GNCDB_SUCCESS;
    }
  }
  va_end(args_copy1);
  va_end(args_copy2);
  va_end(args_copy3);
  return rc;
}

int CastExprTraverseCheck(
    CastExpr *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  int     rc = GNCDB_SUCCESS;
  va_list args_copy1;
  va_list args_copy2;
  va_copy(args_copy1, args);
  va_copy(args_copy2, args);

  if (GNCDB_SUCCESS != (rc = checkFunc((Expression *)expr, fields, additionalParamsCount, args_copy1))) {
    va_end(args_copy1);
    va_end(args_copy2);
    return rc;
  } else if (GNCDB_SUCCESS !=
             (rc = exprTraverseCheck(expr->child, checkFunc, fields, additionalParamsCount, args_copy2))) {
    va_end(args_copy1);
    va_end(args_copy2);
    return rc;
  }
  va_end(args_copy1);
  va_end(args_copy2);
  return GNCDB_SUCCESS;
}

int FieldExprTraverseCheck(
    FieldExpr *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  return checkFunc((Expression *)expr, fields, additionalParamsCount, args);
}

int ExprListExprTraverseCheck(
    ExprListExpr *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  int         rc = GNCDB_SUCCESS;
  int         i  = 0;
  va_list     args_copy;
  Expression *tmpExpr = NULL;

  for (i = 0; i < expr->exprs->elementCount; i++) {
    va_copy(args_copy, args);
    tmpExpr = varArrayListGetPointer(expr->exprs, i);
    if (GNCDB_SUCCESS != (rc = checkFunc(tmpExpr, fields, additionalParamsCount, args_copy))) {
      va_end(args_copy);
      return rc;
    }
    va_end(args_copy);
  }
  return GNCDB_SUCCESS;
}

int SubQueryExprTraverseCheck(
    SubQueryExpr *sub_query_expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  return checkFunc((Expression *)sub_query_expr, fields, additionalParamsCount, args);
}

int exprTraverseCheckPrepare(Expression *expr, CheckFunc checkFunc, varArrayList *list, int additionalParamsCount, ...)
{
  int     rc = GNCDB_SUCCESS;
  va_list args;
  // 初始化 args 来获取额外参数
  va_start(args, additionalParamsCount);
  rc = exprTraverseCheck(expr, checkFunc, list, additionalParamsCount, args);
  va_end(args);
  return rc;
}

int exprTraverseCheck(
    Expression *expr, CheckFunc checkFunc, varArrayList *fields, int additionalParamsCount, va_list args)
{
  if (expr == NULL) {
    return GNCDB_SUCCESS;
  }
  switch (expr->type) {
    case ETG_AGGRFUNC: {
      return AggrFuncExprTraverseCheck((AggrFuncExpr *)expr, checkFunc, fields, additionalParamsCount, args);
    } break;

    case ETG_ARITHMETIC: {
      return ArithmeticExprTraverseCheck((ArithmeticExpr *)expr, checkFunc, fields, additionalParamsCount, args);
    } break;

    case ETG_CONJUNCTION: {
      return ConjunctionExprTraverseCheck((ConjunctionExpr *)expr, checkFunc, fields, additionalParamsCount, args);
    } break;

    case ETG_COMPARISON: {
      return ComparisonExprTraverseCheck((ComparisonExpr *)expr, checkFunc, fields, additionalParamsCount, args);
    } break;

    case ETG_CAST: {
      return CastExprTraverseCheck((CastExpr *)expr, checkFunc, fields, additionalParamsCount, args);
    } break;

    case ETG_FIELD: return FieldExprTraverseCheck((FieldExpr *)expr, checkFunc, fields, additionalParamsCount, args);

    case ETG_EXPRLIST:
      return ExprListExprTraverseCheck((ExprListExpr *)expr, checkFunc, fields, additionalParamsCount, args);

    case ETG_SUBQUERY:
      return SubQueryExprTraverseCheck((SubQueryExpr *)expr, checkFunc, fields, additionalParamsCount, args);

    default: return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

/***********************************表达式深拷贝***********************************/
Expression *fieldExprDeepCopy(FieldExpr *expr)
{
  FieldExpr *newExpr = (FieldExpr *)my_malloc0(sizeof(FieldExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_FIELD;
  // new_expr->fieldInfo = FieldInfoCopy(expr->fieldInfo);
  newExpr->tableName = expr->tableName == NULL ? NULL : my_strdup(expr->tableName);
  newExpr->fieldName = expr->fieldName == NULL ? NULL : my_strdup(expr->fieldName);
  newExpr->name      = expr->name == NULL ? NULL : my_strdup(expr->name);
  newExpr->alias     = expr->alias == NULL ? NULL : my_strdup(expr->alias);
  newExpr->fieldType = expr->fieldType;
  return (Expression *)newExpr;
}

Expression *valueExprDeepCopy(ValueExpr *expr)
{
  ValueExpr *new_expr = (ValueExpr *)my_malloc0(sizeof(ValueExpr));
  if (new_expr == NULL) {
    return NULL;
  }
  new_expr->type  = ETG_VALUE;
  new_expr->value = valueCopy(expr->value);
  new_expr->name  = expr->name == NULL ? NULL : my_strdup(expr->name);
  new_expr->alias = expr->alias == NULL ? NULL : my_strdup(expr->alias);
  return (Expression *)new_expr;
}

Expression *castExprDeepCopy(CastExpr *expr)
{
  CastExpr *new_expr = (CastExpr *)my_malloc0(sizeof(CastExpr));
  if (new_expr == NULL) {
    return NULL;
  }
  new_expr->type     = ETG_CAST;
  new_expr->castType = expr->castType;
  new_expr->child    = exprDeepCopy(expr->child);
  new_expr->name     = expr->name == NULL ? NULL : my_strdup(expr->name);
  new_expr->alias    = expr->alias == NULL ? NULL : my_strdup(expr->alias);
  return (Expression *)new_expr;
}

Expression *comparisonExprDeepCopy(ComparisonExpr *expr)
{
  ComparisonExpr *new_expr = (ComparisonExpr *)my_malloc0(sizeof(ComparisonExpr));
  if (new_expr == NULL) {
    return NULL;
  }
  new_expr->type  = ETG_COMPARISON;
  new_expr->comp  = expr->comp;
  new_expr->left  = exprDeepCopy(expr->left);
  new_expr->right = exprDeepCopy(expr->right);
  new_expr->name  = expr->name == NULL ? NULL : my_strdup(expr->name);
  new_expr->alias = expr->alias == NULL ? NULL : my_strdup(expr->alias);
  return (Expression *)new_expr;
}

Expression *conjunctionExprDeepCopy(ConjunctionExpr *expr)
{
  int         i          = 0;
  Expression *child_expr = NULL;

  ConjunctionExpr *new_expr = (ConjunctionExpr *)my_malloc0(sizeof(ConjunctionExpr));
  if (new_expr == NULL) {
    return NULL;
  }
  new_expr->type            = ETG_CONJUNCTION;
  new_expr->conjunctionType = expr->conjunctionType;
  new_expr->children        = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  for (i = 0; i < expr->children->elementCount; i++) {
    child_expr = varArrayListGetPointer(expr->children, i);
    varArrayListAddPointer(new_expr->children, exprDeepCopy(child_expr));
  }
  new_expr->name  = expr->name == NULL ? NULL : my_strdup(expr->name);
  new_expr->alias = expr->alias == NULL ? NULL : my_strdup(expr->alias);
  return (Expression *)new_expr;
}

Expression *arithmeticExprDeepCopy(ArithmeticExpr *expr)
{
  ArithmeticExpr *new_expr = (ArithmeticExpr *)my_malloc0(sizeof(ArithmeticExpr));
  if (new_expr == NULL) {
    return NULL;
  }
  new_expr->type           = ETG_ARITHMETIC;
  new_expr->arithmeticType = expr->arithmeticType;
  new_expr->left           = exprDeepCopy(expr->left);
  new_expr->right          = exprDeepCopy(expr->right);
  new_expr->name           = expr->name == NULL ? NULL : my_strdup(expr->name);
  new_expr->alias          = expr->alias == NULL ? NULL : my_strdup(expr->alias);
  return (Expression *)new_expr;
}

Expression *aggrFuncExprDeepCopy(AggrFuncExpr *expr)
{
  AggrFuncExpr *new_expr = (AggrFuncExpr *)my_malloc0(sizeof(AggrFuncExpr));
  if (new_expr == NULL) {
    return NULL;
  }
  new_expr->type             = ETG_AGGRFUNC;
  new_expr->aggrType         = expr->aggrType;
  new_expr->param            = exprDeepCopy(expr->param);
  new_expr->paramIsConstexpr = expr->paramIsConstexpr;
  new_expr->name             = expr->name == NULL ? NULL : my_strdup(expr->name);
  new_expr->alias            = expr->alias == NULL ? NULL : my_strdup(expr->alias);
  return (Expression *)new_expr;
}

/**
 * @brief 深拷贝表达式
 *
 * @param expr 原表达式
 * @return 新拷贝的表达式
 */
Expression *exprDeepCopy(Expression *expr)
{
  if (expr == NULL) {
    return NULL;
  }
  switch (expr->type) {
    case ETG_FIELD: {
      return fieldExprDeepCopy((FieldExpr *)expr);
    } break;

    case ETG_VALUE: {
      return valueExprDeepCopy((ValueExpr *)expr);
    } break;

    case ETG_CAST: {
      return castExprDeepCopy((CastExpr *)expr);
    } break;

    case ETG_COMPARISON: {
      return comparisonExprDeepCopy((ComparisonExpr *)expr);
    } break;

    case ETG_CONJUNCTION: {
      return conjunctionExprDeepCopy((ConjunctionExpr *)expr);
    } break;

    case ETG_ARITHMETIC: {
      return arithmeticExprDeepCopy((ArithmeticExpr *)expr);
    }

    case ETG_AGGRFUNC: {
      return aggrFuncExprDeepCopy((AggrFuncExpr *)expr);
    } break;

    default: return NULL;
  }
  return NULL;
}

Expression *fieldExprDeepCopyLookaside(GNCDB *db, FieldExpr *expr)
{
  FieldExpr *newExpr = (FieldExpr *)lookasideMalloc(db, sizeof(FieldExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_FIELD;
  newExpr->tableName = expr->tableName == NULL ? NULL : lookasideStrDup(db, expr->tableName);
  newExpr->fieldName = expr->fieldName == NULL ? NULL : lookasideStrDup(db, expr->fieldName);
  newExpr->name = expr->name == NULL ? NULL : lookasideStrDup(db, expr->name);
  newExpr->alias = expr->alias == NULL ? NULL : lookasideStrDup(db, expr->alias);
  newExpr->fieldType = expr->fieldType;
  return (Expression *)newExpr;
}

Expression *valueExprDeepCopyLookaside(GNCDB *db, ValueExpr *expr)
{
  ValueExpr *newExpr = (ValueExpr *)lookasideMalloc(db, sizeof(ValueExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_VALUE;
  newExpr->value = valueCopy(expr->value); // Assuming valueCopy is compatible with lookaside
  newExpr->name = expr->name == NULL ? NULL : lookasideStrDup(db, expr->name);
  newExpr->alias = expr->alias == NULL ? NULL : lookasideStrDup(db, expr->alias);
  return (Expression *)newExpr;
}

Expression *castExprDeepCopyLookaside(GNCDB *db, CastExpr *expr)
{
  CastExpr *newExpr = (CastExpr *)lookasideMalloc(db, sizeof(CastExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_CAST;
  newExpr->castType = expr->castType;
  newExpr->child = exprDeepCopyLookaside(db, expr->child);
  newExpr->name = expr->name == NULL ? NULL : lookasideStrDup(db, expr->name);
  newExpr->alias = expr->alias == NULL ? NULL : lookasideStrDup(db, expr->alias);
  return (Expression *)newExpr;
}

Expression *comparisonExprDeepCopyLookaside(GNCDB *db, ComparisonExpr *expr)
{
  ComparisonExpr *newExpr = (ComparisonExpr *)lookasideMalloc(db, sizeof(ComparisonExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_COMPARISON;
  newExpr->comp = expr->comp;
  newExpr->left = exprDeepCopyLookaside(db, expr->left);
  newExpr->right = exprDeepCopyLookaside(db, expr->right);
  newExpr->name = expr->name == NULL ? NULL : lookasideStrDup(db, expr->name);
  newExpr->alias = expr->alias == NULL ? NULL : lookasideStrDup(db, expr->alias);
  return (Expression *)newExpr;
}

Expression *conjunctionExprDeepCopyLookaside(GNCDB *db, ConjunctionExpr *expr)
{
  int i = 0;
  Expression *childExpr = NULL;

  ConjunctionExpr *newExpr = (ConjunctionExpr *)lookasideMalloc(db, sizeof(ConjunctionExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_CONJUNCTION;
  newExpr->conjunctionType = expr->conjunctionType;
  newExpr->children = varArrayListCreateLookaside(db, DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  for (i = 0; i < expr->children->elementCount; i++) {
    childExpr = varArrayListGetPointer(expr->children, i);
    varArrayListAddPtrLookaside(db, newExpr->children, exprDeepCopyLookaside(db, childExpr));
  }
  newExpr->name = expr->name == NULL ? NULL : lookasideStrDup(db, expr->name);
  newExpr->alias = expr->alias == NULL ? NULL : lookasideStrDup(db, expr->alias);
  return (Expression *)newExpr;
}

Expression *arithmeticExprDeepCopyLookaside(GNCDB *db, ArithmeticExpr *expr)
{
  ArithmeticExpr *newExpr = (ArithmeticExpr *)lookasideMalloc(db, sizeof(ArithmeticExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_ARITHMETIC;
  newExpr->arithmeticType = expr->arithmeticType;
  newExpr->left = exprDeepCopyLookaside(db, expr->left);
  newExpr->right = exprDeepCopyLookaside(db, expr->right);
  newExpr->name = expr->name == NULL ? NULL : lookasideStrDup(db, expr->name);
  newExpr->alias = expr->alias == NULL ? NULL : lookasideStrDup(db, expr->alias);
  return (Expression *)newExpr;
}

Expression *aggrFuncExprDeepCopyLookaside(GNCDB *db, AggrFuncExpr *expr)
{
  AggrFuncExpr *newExpr = (AggrFuncExpr *)lookasideMalloc(db, sizeof(AggrFuncExpr));
  if (newExpr == NULL) {
    return NULL;
  }
  newExpr->type = ETG_AGGRFUNC;
  newExpr->aggrType = expr->aggrType;
  newExpr->param = exprDeepCopyLookaside(db, expr->param);
  newExpr->paramIsConstexpr = expr->paramIsConstexpr;
  newExpr->name = expr->name == NULL ? NULL : lookasideStrDup(db, expr->name);
  newExpr->alias = expr->alias == NULL ? NULL : lookasideStrDup(db, expr->alias);
  return (Expression *)newExpr;
}

/**
 * @brief 深拷贝表达式（使用 lookaside 内存分配）
 *
 * @param db 数据库上下文
 * @param expr 原表达式
 * @return 新拷贝的表达式
 */
Expression *exprDeepCopyLookaside(GNCDB *db, Expression *expr)
{
  if (expr == NULL) {
    return NULL;
  }
  switch (expr->type) {
    case ETG_FIELD:
      return fieldExprDeepCopyLookaside(db, (FieldExpr *)expr);
    case ETG_VALUE:
      return valueExprDeepCopyLookaside(db, (ValueExpr *)expr);
    case ETG_CAST:
      return castExprDeepCopyLookaside(db, (CastExpr *)expr);
    case ETG_COMPARISON:
      return comparisonExprDeepCopyLookaside(db, (ComparisonExpr *)expr);
    case ETG_CONJUNCTION:
      return conjunctionExprDeepCopyLookaside(db, (ConjunctionExpr *)expr);
    case ETG_ARITHMETIC:
      return arithmeticExprDeepCopyLookaside(db, (ArithmeticExpr *)expr);
    case ETG_AGGRFUNC:
      return aggrFuncExprDeepCopyLookaside(db, (AggrFuncExpr *)expr);
    default:
      return NULL;
  }
}

bool SubQueryExprHasMoreRow(SubQueryExpr *sub_query_expr, Record *tuple, varArrayList *cols)
{
  int     rc            = GNCDB_SUCCESS;
  Record *subquerytuple = NULL;

  rc = PhysicalOperatorNext(sub_query_expr->physicalOper, sub_query_expr->subEvent);
  if (rc == GNCDB_SUCCESS) {
    // 如果有下一行，需要将push上去的tuple销毁，因为所有权是从下到上传递的
    subquerytuple = GetCurrentTuple(sub_query_expr->physicalOper);
    if (subquerytuple != NULL) {
    }
    return true;
  }
  return false;
}

bool ExprHasMoreRow(Expression *expr, Record *tuple, varArrayList *cols)
{
  switch (expr->type) {
    case ETG_SUBQUERY: {
      return SubQueryExprHasMoreRow((SubQueryExpr *)expr, tuple, cols);
    } break;
    default: return false;
  }
}

CompOp swapOp(CompOp op)
{
  switch (op) {
    case CMPOP_LESS_EQUAL: { /* <= */ return CMPOP_GREAT_EQUAL;
    }
    case CMPOP_GREAT_EQUAL: { /* >= */ return CMPOP_LESS_EQUAL;
    }
    case CMPOP_LESS_THAN: { /* < */ return CMPOP_GREAT_THAN;
    }
    case CMPOP_GREAT_THAN: { /* > */ return CMPOP_LESS_THAN;
    }
    case CMPOP_EQUAL_TO: { /* = */ return CMPOP_EQUAL_TO;
    }
    case CMPOP_NOT_EQUAL: { /* != */ return CMPOP_NOT_EQUAL;
    }
    default: {
      return CMPOP_INVALID;
    }
  }
}

void CompExprSwapLR(ComparisonExpr **expr)
{
  Expression *tmp = (*expr)->left;
  (*expr)->left   = (*expr)->right;
  (*expr)->right  = tmp;
  (*expr)->comp   = swapOp((*expr)->comp);
}

/***********************************表达式创建***********************************/
FieldExpr *FieldExprCreate(char *tableName, char *fieldName, char *name, char *alias)
{
  FieldExpr *expr = (FieldExpr *)my_malloc0(sizeof(FieldExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type      = ETG_FIELD;
  expr->tableName = tableName;
  expr->fieldName = fieldName;
  // expr->fieldInfo =  fieldInfo;
  expr->fieldType = FIELDTYPE_INVALID;
  expr->name      = name;
  expr->alias     = alias;
  return expr;
}

/**
 * @brief  创建一个值表达式
 * @param  value:如果不为NULL，则直接使用该值，否则创建一个新的值
 * @param  name:
 * @param  alias:
 * @return ValueExpr*:
 */
ValueExpr *ValueExprCreate(Value *value, char *name, char *alias)
{
  ValueExpr *expr = (ValueExpr *)my_malloc0(sizeof(ValueExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->value = value;
  expr->type  = ETG_VALUE;
  expr->name  = name;
  expr->alias = alias;
  return expr;
}

CastExpr *CastExprCreate(AttrType cast_type, Expression *child, char *name, char *alias)
{
  CastExpr *expr = (CastExpr *)my_malloc0(sizeof(CastExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type     = ETG_CAST;
  expr->castType = cast_type;
  expr->child    = child;
  expr->name     = name;
  expr->alias    = alias;
  return expr;
}

ComparisonExpr *ComparisonExprCreate(CompOp comp, Expression *left, Expression *right, char *name, char *alias)
{
  ComparisonExpr *expr = (ComparisonExpr *)my_malloc0(sizeof(ComparisonExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type  = ETG_COMPARISON;
  expr->comp  = comp;
  expr->left  = left;
  expr->right = right;
  expr->name  = name;
  expr->alias = alias;
  return expr;
}

ConjunctionExpr *ConjunctionExprCreate(
    ConjunctionExprType conjunctiontype, varArrayList *children, char *name, char *alias)
{
  ConjunctionExpr *expr = (ConjunctionExpr *)my_malloc0(sizeof(ConjunctionExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type            = ETG_CONJUNCTION;
  expr->conjunctionType = conjunctiontype;
  expr->children        = children;
  expr->name            = name;
  expr->alias           = alias;
  return expr;
}

ArithmeticExpr *ArithmeticExprCreate(
    ArithmeticExprType arithmetic_type, Expression *left, Expression *right, char *name, char *alias)
{
  ArithmeticExpr *expr = (ArithmeticExpr *)my_malloc0(sizeof(ArithmeticExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type           = ETG_ARITHMETIC;
  expr->arithmeticType = arithmetic_type;
  expr->left           = left;
  expr->right          = right;
  expr->name           = name;
  expr->alias          = alias;
  return expr;
}

AggrFuncExpr *AggrFuncExprCreate(AggrFuncType aggr_type, Expression *param, char *name, char *alias)
{
  AggrFuncExpr *expr = (AggrFuncExpr *)my_malloc0(sizeof(AggrFuncExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type             = ETG_AGGRFUNC;
  expr->aggrType         = aggr_type;
  expr->param            = param;
  expr->name             = name;
  expr->alias            = alias;
  expr->paramIsConstexpr = false;
  return expr;
}

SubQueryExpr *SubQueryExprCreate(SelectSqlNode *sqlNode, SQLStageEvent *sqlEvent, char *name, char *alias)
{
  SubQueryExpr *expr = (SubQueryExpr *)my_malloc0(sizeof(SubQueryExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type         = ETG_SUBQUERY;
  expr->subEvent     = sqlEvent;
  expr->sqlNode      = sqlNode;
  expr->physicalOper = NULL;
  expr->name         = name;
  expr->alias        = alias;
  expr->logicalOper  = NULL;
  expr->stmt         = NULL;
  return expr;
}

ExprListExpr *ExprListExprCreate(varArrayList *exprs, char *name, char *alias)
{
  ExprListExpr *expr = (ExprListExpr *)my_malloc0(sizeof(ExprListExpr));
  if (expr == NULL) {
    return NULL;
  }
  expr->type  = ETG_EXPRLIST;
  expr->exprs = exprs;
  expr->name  = name;
  expr->alias = alias;
  return expr;
}

void *exprCreate(ExprTag type)
{
  switch (type) {
    case ETG_FIELD: return FieldExprCreate(NULL, NULL, NULL, NULL);
    case ETG_VALUE: return ValueExprCreate(NULL, NULL, NULL);
    case ETG_CAST: return CastExprCreate(UNDEFINED, NULL, NULL, NULL);
    case ETG_COMPARISON: return ComparisonExprCreate(CMPOP_INVALID, NULL, NULL, NULL, NULL);
    case ETG_CONJUNCTION: return ConjunctionExprCreate(CJET_INVALID, NULL, NULL, NULL);
    case ETG_ARITHMETIC: return ArithmeticExprCreate(ARITH_INVALID, NULL, NULL, NULL, NULL);
    case ETG_AGGRFUNC: return AggrFuncExprCreate(AGG_INVALID, NULL, NULL, NULL);
    case ETG_SUBQUERY: return SubQueryExprCreate(NULL, NULL, NULL, NULL);
    case ETG_EXPRLIST: return ExprListExprCreate(NULL, NULL, NULL);
    default: return NULL;
  }
}

/***********************************表达式结构体销毁***********************************/

void FieldExprDestroy(FieldExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->tableName != NULL) {
    my_free(expr->tableName);
    expr->tableName = NULL;
  }
  if (expr->fieldName != NULL) {
    my_free(expr->fieldName);
    expr->fieldName = NULL;
  }
  if (expr->name != NULL) {
    my_free(expr->name);
    expr->name = NULL;
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
    expr->alias = NULL;
  }

  my_free(expr);
}

void ValueExprDestroy(ValueExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    my_free(expr->name);
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
  }
  if (expr->value != NULL) {
    valueDestroy(&expr->value);
  }

  my_free(expr);
}

void CastExprDestroy(CastExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    my_free(expr->name);
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
  }
  if (expr->child != NULL) {
    exprDestroy(expr->child);
  }
  my_free(expr);
}

void ComparisonExprDestroy(ComparisonExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    my_free(expr->name);
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
  }
  if (expr->left != NULL) {
    exprDestroy(expr->left);
  }
  if (expr->right != NULL) {
    exprDestroy(expr->right);
  }
  my_free(expr);
}

void ConjunctionExprDestroy(ConjunctionExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    my_free(expr->name);
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
  }
  varArrayListDestroy(&(expr->children));
  my_free(expr);
}

void ArithmeticExprDestroy(ArithmeticExpr *expr)
{
  if (expr == NULL) {
    return;
  }

  if (expr->name != NULL) {
    my_free(expr->name);
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
  }
  if (expr->left != NULL) {
    exprDestroy(expr->left);
  }
  if (expr->right != NULL) {
    exprDestroy(expr->right);
  }
  my_free(expr);
}

void AggrFuncExprDestroy(AggrFuncExpr *expr)
{
  if (expr == NULL) {
    return;
  }

  if (expr->name != NULL) {
    my_free(expr->name);
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
  }
  if (expr->param != NULL) {
    exprDestroy(expr->param);
  }
  my_free(expr);
}

void SubQueryExprDestroy(SubQueryExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    my_free(expr->name);
    expr->name = NULL;
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
    expr->alias = NULL;
  }
  if (expr->subEvent != NULL) {
    SQLStageEventDestroy(&expr->subEvent);
    expr->subEvent = NULL;
  }
  my_free(expr);
}

void ExprListExprDestroy(ExprListExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    my_free(expr->name);
  }
  if (expr->alias != NULL) {
    my_free(expr->alias);
  }
  if (expr->exprs != NULL) {
    varArrayListDestroy(&(expr->exprs));
  }
  my_free(expr);
}

void exprDestroy(Expression *expr)
{
  if (expr == NULL) {
    return;
  }
  switch (expr->type) {
    case ETG_FIELD: FieldExprDestroy((FieldExpr *)expr); break;
    case ETG_VALUE: ValueExprDestroy((ValueExpr *)expr); break;
    case ETG_CAST: CastExprDestroy((CastExpr *)expr); break;
    case ETG_COMPARISON: ComparisonExprDestroy((ComparisonExpr *)expr); break;
    case ETG_CONJUNCTION: ConjunctionExprDestroy((ConjunctionExpr *)expr); break;
    case ETG_ARITHMETIC: ArithmeticExprDestroy((ArithmeticExpr *)expr); break;
    case ETG_AGGRFUNC: AggrFuncExprDestroy((AggrFuncExpr *)expr); break;
    case ETG_SUBQUERY: SubQueryExprDestroy((SubQueryExpr *)expr); break;
    case ETG_EXPRLIST: ExprListExprDestroy((ExprListExpr *)expr); break;
    default: break;
  }
}

/* 以下是讲内存分配替换为内存池之后的销毁函数实现，逻辑与上述一致 */
void FieldExprDestroyLookaside(GNCDB *db, FieldExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->tableName != NULL) {
    lookasideFree(db, expr->tableName);
    expr->tableName = NULL;
  }
  if (expr->fieldName != NULL) {
    lookasideFree(db, expr->fieldName);
    expr->fieldName = NULL;
  }
  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
    expr->name = NULL;
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
    expr->alias = NULL;
  }

  lookasideFree(db, expr);
}

void ValueExprDestroyLookaside(GNCDB *db, ValueExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
  }
  if (expr->value != NULL) {
    valueDestroy(&expr->value); // Assuming valueDestroy is compatible with lookaside
  }

  lookasideFree(db, expr);
}

void CastExprDestroyLookaside(GNCDB *db, CastExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
  }
  if (expr->child != NULL) {
    exprDestroyLookaside(db, expr->child);
  }
  lookasideFree(db, expr);
}

void ComparisonExprDestroyLookaside(GNCDB *db, ComparisonExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
  }
  if (expr->left != NULL) {
    exprDestroyLookaside(db, expr->left);
  }
  if (expr->right != NULL) {
    exprDestroyLookaside(db, expr->right);
  }
  lookasideFree(db, expr);
}

void ConjunctionExprDestroyLookaside(GNCDB *db, ConjunctionExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
  }
  varArrayListDestroyLookaside(db, &(expr->children));
  lookasideFree(db, expr);
}

void ArithmeticExprDestroyLookaside(GNCDB *db, ArithmeticExpr *expr)
{
  if (expr == NULL) {
    return;
  }

  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
  }
  if (expr->left != NULL) {
    exprDestroyLookaside(db, expr->left);
  }
  if (expr->right != NULL) {
    exprDestroyLookaside(db, expr->right);
  }
  lookasideFree(db, expr);
}

void AggrFuncExprDestroyLookaside(GNCDB *db, AggrFuncExpr *expr)
{
  if (expr == NULL) {
    return;
  }

  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
  }
  if (expr->param != NULL) {
    exprDestroyLookaside(db, expr->param);
  }
  lookasideFree(db, expr);
}

void SubQueryExprDestroyLookaside(GNCDB *db, SubQueryExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
    expr->name = NULL;
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
    expr->alias = NULL;
  }
  if (expr->subEvent != NULL) {
    SQLStageEventDestroy(&expr->subEvent);
    expr->subEvent = NULL;
  }
  lookasideFree(db, expr);
}

void ExprListExprDestroyLookaside(GNCDB *db, ExprListExpr *expr)
{
  if (expr == NULL) {
    return;
  }
  if (expr->name != NULL) {
    lookasideFree(db, expr->name);
  }
  if (expr->alias != NULL) {
    lookasideFree(db, expr->alias);
  }
  if (expr->exprs != NULL) {
    varArrayListDestroyLookaside(db, &(expr->exprs));
  }
  lookasideFree(db, expr);
}

void exprDestroyLookaside(GNCDB *db, Expression *expr)
{
  if (expr == NULL) {
    return;
  }
  switch (expr->type) {
    case ETG_FIELD: FieldExprDestroyLookaside(db, (FieldExpr *)expr); break;
    case ETG_VALUE: ValueExprDestroyLookaside(db, (ValueExpr *)expr); break;
    case ETG_CAST: CastExprDestroyLookaside(db, (CastExpr *)expr); break;
    case ETG_COMPARISON: ComparisonExprDestroyLookaside(db, (ComparisonExpr *)expr); break;
    case ETG_CONJUNCTION: ConjunctionExprDestroyLookaside(db, (ConjunctionExpr *)expr); break;
    case ETG_ARITHMETIC: ArithmeticExprDestroyLookaside(db, (ArithmeticExpr *)expr); break;
    case ETG_AGGRFUNC: AggrFuncExprDestroyLookaside(db, (AggrFuncExpr *)expr); break;
    case ETG_SUBQUERY: SubQueryExprDestroyLookaside(db, (SubQueryExpr *)expr); break;
    case ETG_EXPRLIST: ExprListExprDestroyLookaside(db, (ExprListExpr *)expr); break;
    default: break;
  }
}

/***********************************表达式结构体指针销毁***********************************/

void FieldExprPointerDestroy(void *data)
{
  FieldExpr **expr = (FieldExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  FieldExprDestroy(*expr);
  *expr = NULL;
}

void ValueExprPointerDestroy(void *data)
{
  ValueExpr **expr = (ValueExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void CastExprPointerDestroy(void *data)
{
  CastExpr **expr = (CastExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void ComparisonExprPointerDestroy(void *data)
{
  ComparisonExpr **expr = (ComparisonExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void ConjunctionExprPointerDestroy(void *data)
{
  ConjunctionExpr **expr = (ConjunctionExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void ArithmeticExprPointerDestroy(void *data)
{
  ArithmeticExpr **expr = (ArithmeticExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void AggrFuncExprPointerDestroy(void *data)
{
  AggrFuncExpr **expr = (AggrFuncExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void SubQueryExprPointerDestroy(void *data)
{
  SubQueryExpr **expr = (SubQueryExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void ExprListExprPointerDestroy(void *data)
{
  ExprListExpr **expr = (ExprListExpr **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy((Expression *)*expr);
  *expr = NULL;
}

void exprPtrDestroy(void *data)
{
  Expression **expr = (Expression **)data;
  if (expr == NULL || *expr == NULL) {
    return;
  }
  exprDestroy(*expr);
  *expr = NULL;
}
