#include "tuple_cell.h"
#include "gncdbconstant.h"
#include "string.h"
#include "utils.h"
#include "assert.h"
#include <stdlib.h>
TupleCellSpec *TupleCellSpecCreate(const char *tableName, const char *fieldName, const char *alias)
{
  int len = 0;
  TupleCellSpec *tupleCellSpec = (TupleCellSpec *)my_malloc0(sizeof(TupleCellSpec));
  if (tupleCellSpec != NULL) {
    tupleCellSpec->tableName = (tableName && *tableName) ? my_strdup(tableName) : NULL;
    tupleCellSpec->fieldName = (fieldName && *fieldName) ? my_strdup(fieldName) : NULL;

    if (alias && *alias) {
      tupleCellSpec->alias = my_strdup(alias);
    } else {
      if (tableName && *tableName) {
        len              = strlen(tableName) + strlen(fieldName) + 2;
        tupleCellSpec->alias = (char *)my_malloc0(len * sizeof(char));
        strcpy(tupleCellSpec->alias, tableName);
        strcat(tupleCellSpec->alias, ".");
        strcat(tupleCellSpec->alias, fieldName);
      } else {
        assert(fieldName && *fieldName);
        tupleCellSpec->alias = my_strdup(fieldName);
      }
    }
  }
  return tupleCellSpec;
}

TupleCellSpec *TupleCellSpecCreateByName(const char *alias)
{
  TupleCellSpec *tupleCellSpec = (TupleCellSpec *)my_malloc0(sizeof(TupleCellSpec));
  if (tupleCellSpec != NULL) {
    tupleCellSpec->alias     = my_strdup(alias);
    tupleCellSpec->tableName = NULL;
    tupleCellSpec->fieldName = NULL;
  }
  return tupleCellSpec;
}

void TupleCellSpecDestroy(TupleCellSpec *spec)
{
  if (spec == NULL) {
    return;
  }

  if (spec->tableName != NULL) {
    my_free(spec->tableName);
  }

  if (spec->fieldName != NULL) {
    my_free(spec->fieldName);
  }

  if (spec->alias != NULL) {
    my_free(spec->alias);
  }

  my_free(spec);
}

void TupleCellSpecPointerDestroy(void *data)
{
  TupleCellSpec **spec = (TupleCellSpec **)data;
  if (spec == NULL || *spec == NULL) {
    return;
  }

  TupleCellSpecDestroy(*spec);
  *spec = NULL;
}
