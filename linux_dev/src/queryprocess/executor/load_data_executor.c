#include "load_data_executor.h"
#include "utils.h"
#include "load_data_stmt.h"
#include "vararraylist.h"
#include "os.h"
#include <errno.h>
#include <sys/types.h>
#include <time.h>
#include <stdio.h>
#include "gncdb.h"
extern int sqlInsert(struct GNCDB *db, int *affectedRows, char *tableName, varArrayList *values, Transaction *txn);
int        LoadDataExecute(SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  rc     = loadData(sqlEvent, ((LoadDataStmt *)(sqlEvent->stmt))->table, ((LoadDataStmt *)(sqlEvent->stmt))->filename);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return rc;
}

void stringsToValues(char **strings, varArrayList *values, varArrayList *columnList, int maxColumnCount)
{
  int     i      = 0;
  Column *column = NULL;
  Value  *value  = NULL;
  if(!strings || !values || !columnList) {
    return;
  }
  for (i = 0; i < columnList->elementCount - 3 && i < maxColumnCount; i++) {
    column = (Column *)varArrayListGetPointer(columnList, i);
    value  = valueCreate();
    if (!value) {
      return;
    }
    switch (column->fieldType) {
      case FIELDTYPE_INTEGER: {
        value->attrType          = INTS;
        value->numValue.intValue = atoi(strings[i]);
        value->length            = INT_SIZE;
      } break;
      case FIELDTYPE_VARCHAR: {
        value->attrType = CHARS;
        value->isMalloc = true;
        value->strValue = my_strdup(strings[i]);
        value->length   = strlen(strings[i]);
      } break;

      case FIELDTYPE_REAL: {
        value->attrType             = DOUBLES;
        value->numValue.doubleValue = atof(strings[i]);
        value->length               = FLOAT_SIZE;
      } break;

      case FIELDTYPE_DATE: {
        value->attrType = DATES;
        stringToDate(strings[i], &value->numValue.intValue);
        value->length = INT_SIZE;
      } break;

      case FIELDTYPE_DATETIME: {
        value->attrType = DATETIMES;
        stringToDatetime(strings[i], &value->numValue.datetimeValue);
        value->length = INT_SIZE * 2;
      }

      default: {
      }
    }
    varArrayListAddPointer(values, value);
  }
}

int loadData(SQLStageEvent *sqlEvent, BtreeTable *table, const char *fileName)
{
  TableSchema  *tableShema   = NULL;
  varArrayList *recordValues = NULL;
  size_t        lineSize;
  char         *line = NULL;
  ssize_t       read;
  char         *fileValues[256] = {0};
  const char   *delim          = "|";
  int           lineNum        = 0;
  int           insertionCount = 0;
  int           rc             = GNCDB_SUCCESS;
  char         *token          = NULL;
  int           i              = 0;

  Catalog *catalog = sqlEvent->db->catalog;
  FILE    *fs      = fopen(fileName, "rb");
  if (!fs) {
    char resultString[256];
    snprintf(
        resultString, sizeof(resultString), "Failed to open file: %s. system error=%s\n", fileName, strerror(errno));
    return GNCDB_FILE_NOT_FOUND;
  }

  tableShema   = hashMapGet(catalog->tableSchemaMap, table->tableName);
  recordValues = varArrayListCreate(DISORDER, sizeof(Value *), 0, NULL, valuePointerDestroy);
  if (!recordValues) {
    fclose(fs);
    return GNCDB_MEM;
  }

  while ((read = getline(&line, &lineSize, fs)) != -1 && GNCDB_SUCCESS == rc) {
    // 去除行尾所有的换行符
    while (read > 0 && (line[read - 1] == '\n' || line[read - 1] == '\r')) {
      line[--read] = '\0';
    }
    lineNum++;
    if (isBlank(line)) {
      continue;
    }
    token = strtok(line, delim);
    i     = 0;
    while (token != NULL) {
      fileValues[i++] = token;
      token           = strtok(NULL, delim);
    }
    if (i < tableShema->columnList->elementCount - 3) {
      printf("Line:%d column count not match\n", lineNum);
      fclose(fs);
      return GNCDB_SQLFILE_INVALID;
    }
    stringsToValues(fileValues, recordValues, tableShema->columnList, 256);
    rc = sqlInsert(sqlEvent->db, &sqlEvent->affectedRows, table->tableName, recordValues, sqlEvent->txn);
    if (rc != GNCDB_SUCCESS) {
      printf("Line:%d insert record failed. error:%d\n", lineNum, rc);
      fclose(fs);
      return rc;
    } else {
      insertionCount++;
      varArrayListClear(recordValues);
    }
  }
  fclose(fs);
  varArrayListDestroy(&recordValues);
  return rc;
}

// int load_data(SQLStageEvent* sql_event, BtreeTable *table, const char *file_name) {
//     Catalog* catalog = sql_event->db->catalog;

//     // 使用内存映射文件
//     int fd = open(file_name, O_RDONLY);
//     if (fd == -1) {
//         // 处理打开文件错误
//         perror("Error opening file");
//         return GNCDB_FILE_NOT_FOUND;
//     }

//     // 获取文件大小
//     struct stat sb;
//     if (fstat(fd, &sb) == -1) {
//         // 处理获取文件大小错误
//         perror("Error getting file size");
//         close(fd);
//         return GNCDB_FILE_NOT_FOUND;
//     }

//     char *file_in_memory = mmap(NULL, sb.st_size, PROT_READ, MAP_PRIVATE, fd, 0);
//     if (file_in_memory == MAP_FAILED) {
//         // 处理内存映射错误
//         perror("Error mapping file");
//         close(fd);
//         return GNCDB_MEM;
//     }

//     struct timespec begin_time;
//     clock_gettime(CLOCK_MONOTONIC, &begin_time);
//     TableSchema* table_shema = hashMapGet(catalog->tableSchemaMap, table->tableName);
//     varArrayList *record_values = varArrayListCreate(DISORDER, sizeof(Value), 0, NULL, NULL);
//     if (!record_values) {
//         return GNCDB_MEM;
//     }
//     char *current_position = file_in_memory;
//     char *end_of_file = file_in_memory + sb.st_size;
//     const char *delim = "|";
//     int line_num = 0;
//     int insertion_count = 0;
//     int rc = GNCDB_SUCCESS;

//     // 读取文件内容
//     while (current_position < end_of_file && GNCDB_SUCCESS == rc) {
//         // 找到下一行的结束位置
//         char *end_of_line = strchr(current_position, '\n');
//         if (end_of_line == NULL) {
//             end_of_line = end_of_file; // 如果是文件的最后一行
//         }

//         // 处理当前行
//         *end_of_line = '\0'; // 临时替换换行符以创建字符串结束
//         if (!is_blank(current_position)) {
//             char *file_values[256];
//             char *token = strtok(current_position, delim);
//             int i = 0;
//             while (token != NULL) {
//                 file_values[i++] = token;
//                 token = strtok(NULL, delim);
//             }

//             // 将字符串转换为值并插入数据库
//             stringsToValues(file_values, record_values, table_shema->columnList);
//             rc = GNCDB_sqlInsert(sql_event->db, &sql_event->affected_rows, table->tableName, record_values,
//             sql_event->txn); if (rc != GNCDB_SUCCESS) {
//                 fprintf(stderr, "Line:%d insert record failed. error:%d\n", line_num, rc);
//                 munmap(file_in_memory, sb.st_size);
//                 close(fd);
//                 return rc;
//             } else {
//                 insertion_count++;
//                 varArrayListClear(record_values);
//             }
//         }
//         *end_of_line = '\n'; // 恢复换行符
//         current_position = end_of_line + 1; // 移动到下一行的开始位置
//         line_num++;
//     }

//     // 清理资源
//     munmap(file_in_memory, sb.st_size);
//     close(fd);

//     struct timespec end_time;
//     clock_gettime(CLOCK_MONOTONIC, &end_time);
//     long cost_nano = (end_time.tv_sec - begin_time.tv_sec) * 1000000000L + (end_time.tv_nsec - begin_time.tv_nsec);
//     if (GNCDB_SUCCESS == rc) {
//         printf("total %d line(s) handled and %d record(s) loaded, total cost %.3f second(s)\n", line_num,
//         insertion_count, cost_nano / 1000000000.0);
//     }

//     varArrayListDestroy(&record_values);
//     return rc;
// }