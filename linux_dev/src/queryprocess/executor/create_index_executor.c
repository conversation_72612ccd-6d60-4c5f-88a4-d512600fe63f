#include "create_index_executor.h"
#include "create_index_stmt.h"
#include "gncdb.h"
#include "gncdbconstant.h"
#include "hash.h"
#include "parse_defs.h"
#include "queryexecutor.h"
#include <string.h>

int CreateIndexExecute(SQLStageEvent *sqlEvent)
{
  int              rc                                          = GNCDB_SUCCESS;
  GNCDB           *db                                          = sqlEvent->db;
  CreateIndexStmt *createIndexStmt                             = NULL;
  HashIndex       *hashIndex                                   = NULL;
  char             fullIdxName[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};  // tabName.idxName + '\0'

  if (sqlEvent->stmt->type != ST_CREATE_INDEX) {
    return GNCDB_PARAM_INVALID;
  }

  createIndexStmt = (CreateIndexStmt *)sqlEvent->stmt;
  //* 1.检查索引是否存在
  sprintf(fullIdxName, "%s.%s", createIndexStmt->relationName, createIndexStmt->indexName);
  rc = catalogGetHashIndex(sqlEvent->db->catalog, &hashIndex, fullIdxName);
  //* 2.索引已存在
  if (rc == GNCDB_SUCCESS && hashIndex != NULL) {
    return GNCDB_INDEX_EXISTS;
  }
  //* 3.创建索引
  if (createIndexStmt->indexType == INDEX_TYPE_HASH) {
    //* 3.2.创建哈希索引
    rc = GNCDB_createHashIndex(  // todo 列暂定只能为一列
        db,
        createIndexStmt->relationName,
        createIndexStmt->indexName,
        1,
        createIndexStmt->attributeName);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } else if (createIndexStmt->indexType == INDEX_TYPE_BTREE) {
  }

  return rc;
}