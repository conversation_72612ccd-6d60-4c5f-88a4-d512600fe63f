#include "catalog.h"
#include "drop_table_stmt.h"
#include "transaction.h"
#include "vararraylist.h"
#include "hash.h"
#include "queryexecutor.h"
int DropTable(struct GNCDB *db, char *tableName, struct Transaction *tx)
{
  int           rc              = 0;
  BtreeTable   *table           = NULL;
  TableSchema  *tableSchema     = NULL;
  varArrayList *hashIndexList   = NULL;
  varArrayList *primaryKeyIndex = NULL;
  varArrayList *primaryKeyType  = NULL;
  HashIndex    *hashIndex       = NULL;
  if (db == NULL || tableName == NULL) {
    return GNCDB_PARAMNULL;
  }

  rc = catalogGetTable(db->catalog, &table, tableName);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_SUCCESS;
  }
  // 获取表的所有哈希索引
  hashIndexList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  rc            = catalogGetHashIndexList(db, hashIndexList, tableName, tx);
  if (rc != GNCDB_SUCCESS || hashIndexList == NULL) {
    varArrayListDestroy(&hashIndexList);
    return rc;
  }
  for (int i = 0; i< hashIndexList->elementCount; i++) {
    hashIndex = *(HashIndex **)varArrayListGet(hashIndexList, i);
    rc        = executorDropeHashIndex(db, hashIndex->tableName, hashIndex->indexName, tx);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&hashIndexList);
      return rc;
    }
  }
  rc = btreeTableDropTable(table, db, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = catalogDeleteTable(tableName, tx, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  btreeTableDestroy(&table);

  tableSchema = getTableSchema(db->catalog, tableName);
  WriteLock(&(db->catalog->tableSchemaLatch));
  hashMapRemove(db->catalog->tableSchemaMap, tableName);
  WriteUnLock(&(db->catalog->tableSchemaLatch));

  primaryKeyIndex = getPrimaryKeyIndex(db->catalog, tableName);
  WriteLock(&(db->catalog->keyIndexLatch));
  hashMapRemove(db->catalog->tablePrimaryKeyIndexMap, tableName);
  WriteUnLock(&(db->catalog->keyIndexLatch));

  primaryKeyType = getPrimaryKeyType(db->catalog, tableName);
  WriteLock(&(db->catalog->keyTypeLatch));
  hashMapRemove(db->catalog->tablePrimaryKeyTypeMap, tableName);
  WriteUnLock(&(db->catalog->keyTypeLatch));
  tableSchemaDestroy(tableSchema);
  varArrayListDestroy(&primaryKeyIndex);
  varArrayListDestroy(&primaryKeyType);
  return GNCDB_SUCCESS;
}

int executeDropTable(GNCDB *db, char *tableName)
{
  int          rc = 0;
  Transaction *tx = NULL;
  if (strcmp(tableName, "master") == 0 || strcmp(tableName, "schema") == 0) {
    return GNCDB_SYSTEMTABLE_NOTREMOVE;
  }
  tx = transcationConstrcut(db);
  if (tx == NULL) {
    return GNCDB_MEM;
  }
  if (catalogIsTableExist(db->catalog, tableName)) {
    transactionRollback(tx, db);
    return GNCDB_TABLE_NOT_FOUND;
  }
  /*rc = transcationStart(tx, db);
  if (rc != GNCDB_SUCCESS)
  {
      transactionRollback(tx, db);;
      return rc;
  }*/
  rc = DropTable(db, tableName, tx);
  if (rc != GNCDB_SUCCESS) {
    transactionRollback(tx, db);
    return rc;
  } else {
    rc = transactionCommit(tx, db);
  }
  return GNCDB_SUCCESS;
}

int DropTableExecute(SQLStageEvent *sqlEvent)
{
  int            rc            = GNCDB_SUCCESS;
  Stmt          *stmt          = NULL;
  DropTableStmt *dropTableStmt = NULL;
  BtreeTable    *table         = NULL;

  stmt = sqlEvent->stmt;
  if (stmt->type != ST_DROP_TABLE) {}
  dropTableStmt = (DropTableStmt *)stmt;
  if (dropTableStmt->ifExist) {
    catalogGetTable(sqlEvent->db->catalog, &table, dropTableStmt->tableName);
    if (table == NULL) {
      return GNCDB_SUCCESS;
    }
  }
  rc = executeDropTable(sqlEvent->db, dropTableStmt->tableName);
  if (rc != 0) {
    // LOG(LOG_WARNING, "drop table failed");
  }
  return rc;
}