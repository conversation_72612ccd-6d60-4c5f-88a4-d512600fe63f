#include "command_executor.h"
#include "show_tables_executor.h"
#include "drop_table_executor.h"
#include "create_index_executor.h"
#include "drop_index_executor.h"
#include "load_data_executor.h"
#include "stmt.h"
int commandExecutor(SQLStageEvent *sqlEvent)
{
  Stmt *stmt = sqlEvent->stmt;
  int   rc   = GNCDB_SUCCESS;
  switch (stmt->type) {
    case ST_CREATE_INDEX: rc = CreateIndexExecute(sqlEvent); break;
    case ST_DROP_TABLE: rc = DropTableExecute(sqlEvent); break;
    case ST_DROP_INDEX: rc = DropIndexExecute(sqlEvent); break;
    case ST_DESC_TABLE: break;
    case ST_HELP: break;
    case ST_SHOW_TABLES: rc = ShowTableExecute(sqlEvent); break;
    case ST_BEGIN: break;
    case ST_COMMIT:
    case ST_ROLLBACK: break;
    case ST_SET_VARIABLE: break;
    case ST_LOAD_DATA: rc = LoadDataExecute(sqlEvent); break;
    case ST_EXIT: break;
    default: rc = GNCDB_UNIMPLENMENT;
  }
  return rc;
}
