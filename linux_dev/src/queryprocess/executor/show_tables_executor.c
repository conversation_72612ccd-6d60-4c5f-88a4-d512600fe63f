#include "show_tables_executor.h"
#include "hashmap.h"
#include "gncdb.h"
int ShowTableExecute(SQLStageEvent *sqlEvent)
{
  GNCDB           *db       = sqlEvent->db;
  HashMap         *tableMap = db->catalog->tableMap;
  HashMapIterator *iter     = createHashMapIterator(tableMap);
  while (hasNextHashMapIterator(iter)) {
    iter = nextHashMapIterator(iter);
    printf("%s\n", (char *)iter->entry->key);
  }
  freeHashMapIterator(&iter);
  return GNCDB_SUCCESS;
}