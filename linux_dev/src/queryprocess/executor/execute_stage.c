#include "execute_stage.h"
#include "create_table_physical_operator.h"
#include "delete_physical_operator.h"
#include "command_executor.h"
#include "exec_tuple.h"
#include "gncdbconstant.h"
#include "groupby_physical_operator.h"
#include "insert_physical_operator.h"
#include "join_physical_operator.h"
#include "limit_physical_operator.h"
#include "orderby_physical_operator.h"
#include "physical_operator.h"
#include "predicate_physical_operator.h"
#include "project_physical_operator.h"
#include "sql_event.h"
#include "typedefine.h"
#include "utils.h"
#include "table_scan_physical_operator.h"
#include "index_scan_physical_operator.h"
#include "update_physical_operator.h"
#include "expression.h"
#include "value.h"
#include "vararraylist.h"
#include <assert.h>
#include <stdio.h>
#include <time.h>
// void resetValueStrings(char **valueStrings, int tupleSchemaCount, int i)
// {
//   // int i = 0;
//   for (i = 0; i < tupleSchemaCount; i++) {
//     if (valueStrings[i] != NULL) {
//       my_free(valueStrings[i]);
//       valueStrings[i] = NULL;
//     }
//   }
// }

// void freeSchemaAndValues(char **tupleSchema, char **valueStrings, int tupleSchemaCount, int i)
// {
//   // int i = 0;
//   if (tupleSchema != NULL) {
//     for (i = 0; i < tupleSchemaCount; i++) {
//       my_free(tupleSchema[i]);
//     }
//     my_free(tupleSchema);
//   }
//   if (valueStrings != NULL) {
//     my_free(valueStrings);
//   }
// }

// 宏定义 resetValueStrings
#define RESET_VALUE_STRINGS(valueStrings, tupleSchemaCount, i) \
  do {                                                         \
    for (i = 0; i < (tupleSchemaCount); i++) {                 \
      if ((valueStrings)[i] != NULL) {                         \
        my_free((valueStrings)[i]);                            \
        (valueStrings)[i] = NULL;                              \
      }                                                        \
    }                                                          \
  } while (0)

// 宏定义 freeSchemaAndValues
#define FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, i) \
  do {                                                                         \
    if ((tupleSchema) != NULL) {                                               \
      for (i = 0; i < (tupleSchemaCount); i++) {                               \
        if ((tupleSchema)[i] != NULL) {                                        \
          my_free((tupleSchema)[i]);                                           \
        }                                                                      \
      }                                                                        \
      my_free((tupleSchema));                                                  \
    }                                                                          \
    if ((valueStrings) != NULL) {                                              \
      for (j = 0; j < (tupleSchemaCount); j++) {                               \
        if ((valueStrings)[j] != NULL) {                                       \
          my_free((valueStrings)[j]);                                          \
        }                                                                      \
      }                                                                        \
      my_free((valueStrings));                                                 \
    }                                                                          \
  } while (0)

/*

// char *valTostring(void *val, FieldType type, int maxLen)
// {
//   int   len = 0;
//   char *str = NULL;
//   switch (type) {
//     case FIELDTYPE_INTEGER: {
//       len = snprintf(NULL, 0, "%d", *(int *)val);
//       str = (char *)my_malloc0(len + 1);
//       snprintf(str, len + 1, "%d", *(int *)val);
//     } break;
//     case FIELDTYPE_REAL: {
//       len = snprintf(NULL, 0, "%lf", *(double *)val);
//       str = (char *)my_malloc0(len + 1);
//       snprintf(str, len + 1, "%lf", *(double *)val);
//     } break;
//     case FIELDTYPE_VARCHAR: {
//       len = maxLen;
//       str = (char *)my_malloc0(len + 1);
//       if(str == NULL) {
//         return NULL;
//       }
//       strncpy(str, (char *)val, len);
//     } break;

//     default: printf("valTostring: unknown type\n"); break;
//   }
//   return str;
// }
// #define VAL_TO_STRING(val, type, maxLen)                                 \
//   ({                                                                     \
//     char *str = NULL;                                                    \
//     int   len = 0;                                                       \
//     switch (type) {                                                      \
//       case FIELDTYPE_INTEGER: {                                          \
//         len = snprintf(NULL, 0, "%d", *(int *)(val));                    \
//         str = (char *)my_malloc0(len + 1);                               \
//         if (str) {                                                       \
//           snprintf(str, len + 1, "%d", *(int *)(val));                   \
//         }                                                                \
//       } break;                                                           \
//       case FIELDTYPE_REAL: {                                             \
//         len = snprintf(NULL, 0, "%lf", *(double *)(val));                \
//         str = (char *)my_malloc0(len + 1);                               \
//         if (str) {                                                       \
//           snprintf(str, len + 1, "%lf", *(double *)(val));               \
//         }                                                                \
//       } break;                                                           \
//       case FIELDTYPE_VARCHAR: {                                          \
//         len = maxLen;                                                    \
//         str = (char *)my_malloc0(len + 1);                               \
//         if (str) {                                                       \
//           strncpy(str, (char *)(val), len);                              \
//           str[len] = '\0';                                               \
//         }                                                                \
//       } break;                                                           \
//       case FIELDTYPE_BLOB: {                                             \
//          对于BLOB类型，直接返回NULL \
//         str = my_strdup("NULL");                                            \
//       } break;                                                           \
//       default: printf("valTostring: unknown type\n"); break;             \
//     }                                                                    \
//     str;                                                                 \
//   })

// #define VAL_TO_STRING(val, type, maxLen)                                       \
//   ({                                                                           \
//     char *str = NULL;                                                          \
//     switch (type) {                                                            \
//       case FIELDTYPE_INTEGER: {                                                \
//         char buf[16] = {0};                                                    \
//         int  len     = snprintf(buf, sizeof(buf), "%d", *(int *)(val));        \
//         if (len > 0 && len < (int)sizeof(buf)) {                               \
//           str = (char *)my_malloc(len + 1);                                    \
//           if (str)                                                             \
//             memcpy(str, buf, len + 1);                                         \
//         }                                                                      \
//       } break;                                                                 \
//       case FIELDTYPE_REAL: {                                                   \
//         char buf[256] = {0};                                                   \
//         int  len      = snprintf(buf, sizeof(buf), "%.15g", *(double *)(val)); \
//         if (len > 0 && len < (int)sizeof(buf)) {                               \
//           str = (char *)my_malloc(len + 1);                                    \
//           if (str)                                                             \
//             memcpy(str, buf, len + 1);                                         \
//         }                                                                      \
//       } break;                                                                 \
//       case FIELDTYPE_VARCHAR: {                                                \
//         str = (char *)my_malloc(maxLen + 1);                                   \
//         if (str) {                                                             \
//           memcpy(str, (char *)(val), maxLen);                                  \
//           str[maxLen] = '\0';                                                  \
//         }                                                                      \
//       } break;                                                                 \
//       case FIELDTYPE_BLOB: {                                                   \
//         str = my_malloc0(5);                                                   \
//         if (str)                                                               \
//           memcpy(str, "NULL", 5);                                              \
//       } break;                                                                 \
//       default: printf("valTostring: unknown type\n"); break;                   \
//     }                                                                          \
//     str;                                                                       \
//   })

// int valToStr(void* val, FieldType type, int maxLen, char *str)
// {
//   if(type == FIELDTYPE_INTEGER){
//     snprintf(str, 21, "%d", *(int *)val);
//   }
//   else if(type == FIELDTYPE_REAL){
//     snprintf(str, 25, "%.15g", *(double *)val);
//   }
//   else if(type == FIELDTYPE_VARCHAR){
//     snprintf(str, maxLen, "%s", (char *)val);
//   }
//   else{
//     return GNCDB_INTERNAL;
//   }
//   return GNCDB_SUCCESS;
// }

// #define valToStr(val, type, maxLen, str)                         \
//   ({                                                             \
//     int ret_code;                                                \
//     if ((type) == FIELDTYPE_INTEGER) {                           \
//       ret_code = snprintf((str), 21, "%d", *(int *)(val));       \
//     } else if ((type) == FIELDTYPE_REAL) {                       \
//       ret_code = snprintf((str), 25, "%.15g", *(double *)(val)); \
//     } else if ((type) == FIELDTYPE_VARCHAR) {                    \
//       ret_code = memcpy((str), (char *)(val), (maxLen));         \
//       (str)[(maxLen)] = '\0';                                    \
//     } else {                                                     \
//       ret_code = GNCDB_INTERNAL;                                 \
//     }                                                            \
//     (ret_code == -1) ? GNCDB_INTERNAL : GNCDB_SUCCESS;           \
//   })

// #define valToStr(val, type, maxLen, str)                         \
//   ({                                                             \
//     int ret_code = 0;                                            \
//     if ((type) == FIELDTYPE_INTEGER) {                           \
//       ret_code = snprintf((str), 21, "%d", *(int *)(val));       \
//     } else if ((type) == FIELDTYPE_REAL) {                       \
//       ret_code = snprintf((str), 25, "%.15g", *(double *)(val)); \
//     } else if ((type) == FIELDTYPE_VARCHAR) {                    \
//       str = (val);                                               \
//     } else {                                                     \
//       ret_code = GNCDB_INTERNAL;                                 \
//     }                                                            \
//     (ret_code == -1) ? GNCDB_INTERNAL : GNCDB_SUCCESS;           \
//   })
 */
int valToStr(void *val, FieldType type, int maxLen, char **str)
{
  if (type == FIELDTYPE_INTEGER) {
    def_itoa(*(int *)val, *str);
  } else if (type == FIELDTYPE_REAL) {
    format_double_dtoa(*(double *)val, *str, 25);
  } else if (type == FIELDTYPE_VARCHAR) {
    *str = val;
  } else if (type == FIELDTYPE_BLOB) {
    def_itoa(*((int *)val + 1), *(str));
  } else {
    return GNCDB_INTERNAL;
  }
  return GNCDB_SUCCESS;
}

int valToStrCp(void *val, FieldType type, int maxLen, char **str)
{
  if (type == FIELDTYPE_INTEGER) {
    // sprintf(*str, "%d", *(int *)val);
    def_itoa(*(int *)val, *str);
  } else if (type == FIELDTYPE_REAL) {
    // sprintf(*str, "%.15g", *(double *)val);
    format_double_dtoa(*(double *)val, *str, 25);
  } else if (type == FIELDTYPE_VARCHAR) {
    memcpy(*str, (char *)val, maxLen);
    (*str)[maxLen] = '\0';
  } else {
    return GNCDB_INTERNAL;
  }
  return GNCDB_SUCCESS;
}

// int valToStr(void* val, FieldType type, int maxLen, char *str)
// {
//   int len = 0;
//   if (type == FIELDTYPE_INTEGER) {
//     snprintf(str, 21, "%d", *(int *)val);
//   } else if (type == FIELDTYPE_REAL) {
//     snprintf(str, 25, "%.15g", *(double *)val);
//   } else if (type == FIELDTYPE_VARCHAR) {
//     len = strlen((char *)val);
//     if (len > maxLen) {
//       len = maxLen;
//     }
//     snprintf(str, len + 1, "%s", (char *)val);
//     str[len] = '\0';
//   } else {
//     return GNCDB_INTERNAL;
//   }
//   return GNCDB_SUCCESS;
// }

int ExecuteStageHandleRequest(SQLStageEvent *sqlEvent)
{
  int               rc       = GNCDB_SUCCESS;
  Stmt             *stmt     = NULL;
  PhysicalOperator *physOper = NULL;
  /*1.参数检查*/
  if (sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }
  stmt     = sqlEvent->stmt;
  physOper = sqlEvent->plan;

  /*2.执行物理算子*/
  if (physOper != NULL) {
    /*2.1DML语句*/
    rc = setColMeta(physOper);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (sqlEvent->isStep) {
      rc = ExecutePhysicalPlanByStep(sqlEvent);
    } else {
      rc = ExecutePhysicalPlan(sqlEvent);
    }
  } else if (stmt != NULL) {
    /*2.2DDL语句*/
    rc = commandExecutor(sqlEvent);
  }

  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

/**
 * @brief 获取当前元组的schema和字段数
 *
 * @param sqlEvent  SQLStageEvent对象
 * @param tupleSchema  元组的schema字符串形式
 * @param fieldCount  字段数
 * @return int  返回执行结果
 */
int getTupleSchemaAndCount(SQLStageEvent *sqlEvent, char ***tupleSchema, int *fieldCount)
{
  ProjectPhysicalOperator *projectOper = NULL;
  int                      i           = 0;
  Expression              *expr        = NULL;
  if (sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (sqlEvent->plan->type == PO_PROJECT) {
    projectOper    = (ProjectPhysicalOperator *)sqlEvent->plan;
    (*tupleSchema) = (char **)my_malloc0(sizeof(char *) * projectOper->projectedFields->elementCount);
    if ((*tupleSchema) == NULL) {
      return GNCDB_INTERNAL;
    }
    for (i = 0; i < projectOper->projectedFields->elementCount; i++) {
      expr = varArrayListGetPointer(projectOper->projectedFields, i);
      if (expr == NULL) {
        return GNCDB_INTERNAL;
      }
      if (expr->alias != NULL) {
        // (*tupleSchema)[i] = my_strdup(expr->alias);
        (*tupleSchema)[i] = my_malloc0(strlen(expr->alias) + 1);
        strcpy((*tupleSchema)[i], expr->alias);

      } else {
        if (expr->name == NULL) {
          return GNCDB_INTERNAL;
        }
        // (*tupleSchema)[i] = my_strdup(expr->name);
        (*tupleSchema)[i] = my_malloc0(strlen(expr->name) + 1);
        strcpy((*tupleSchema)[i], expr->name);
      }
    }
    *fieldCount = projectOper->projectedFields->elementCount;
  } else if (sqlEvent->plan->type == PO_DELETE) {
    (*tupleSchema) = (char **)my_malloc0(sizeof(char *) * 1);
    if ((*tupleSchema) == NULL) {
      return GNCDB_INTERNAL;
    }
    // (*tupleSchema)[0] = my_strdup("DELETE COUNT");
    (*tupleSchema)[0] = my_malloc0(strlen("DELETE COUNT") + 1);
    strcpy((*tupleSchema)[0], "DELETE COUNT");
    *fieldCount = 1;
  } else if (sqlEvent->plan->type == PO_INSERT) {
    (*tupleSchema) = (char **)my_malloc0(sizeof(char *) * 1);
    if ((*tupleSchema) == NULL) {
      return GNCDB_INTERNAL;
    }
    // (*tupleSchema)[0] = my_strdup("INSERT COUNT");
    (*tupleSchema)[0] = my_malloc0(strlen("INSERT COUNT") + 1);
    strcpy((*tupleSchema)[0], "INSERT COUNT");
    *fieldCount = 1;
  } else if (sqlEvent->plan->type == PO_UPDATE) {
    (*tupleSchema) = (char **)my_malloc0(sizeof(char *) * 1);
    if ((*tupleSchema) == NULL) {
      return GNCDB_INTERNAL;
    }
    // (*tupleSchema)[0] = my_strdup("UPDATE COUNT");
    (*tupleSchema)[0] = my_malloc0(strlen("UPDATE COUNT") + 1);
    strcpy((*tupleSchema)[0], "UPDATE COUNT");
    *fieldCount = 1;
  } else {
    *fieldCount = 1;
  }
  return GNCDB_SUCCESS;
}

int ExecutePhysicalPlan(SQLStageEvent *sqlEvent)
{
  /*自顶向下执行open*/
  int               rc               = GNCDB_SUCCESS;
  int               i                = 0;
  char            **tupleSchema      = NULL;
  char            **valueStrings     = NULL;
  char            **valueStringsTemp = NULL;
  int               tupleSchemaCount = 0;
  int               j                = 0;
  Record           *tuple            = NULL;
  varArrayList     *cols             = NULL;
  varArrayList     *childcols        = NULL;
  CompositeMeta    *compositeMeta    = NULL;
  PhysicalOperator *child            = NULL;
  Meta             *meta             = NULL;
  Meta            **metas            = NULL;
  BYTE             *bitmap           = NULL;
  int               realChildColsNum = 0;
  Value             val;
  int               isDML = 0; /* 是否是增删改查 */

  if (sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*1.open各个算子*/
  rc = PhysicalOperatorOpen(sqlEvent->plan, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  sqlEvent->isOpen = true;

  /*2.获取最上层算子处理的元组的模式，主要是列名或者别名（如果有）以及列的个数*/
  rc = getTupleSchemaAndCount(sqlEvent, &tupleSchema, &tupleSchemaCount);
  if (rc != GNCDB_SUCCESS) {
    FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, 0, j);
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    return rc;
  }

  /*3.insert、delete、update、project是DML语句，与建表、事务、索引相关的是DDL语句*/
  isDML = (sqlEvent->plan->type == PO_INSERT || sqlEvent->plan->type == PO_DELETE ||
              sqlEvent->plan->type == PO_UPDATE || sqlEvent->plan->type == PO_PROJECT)
              ? 1
              : 0;

  /*4.定义meta用来存储执行结果的列对应的微元*/
  metas = my_malloc0(sizeof(Meta *) * tupleSchemaCount);
  if (metas == NULL) {
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    return GNCDB_INTERNAL;
  }
  
  /*5.定义存储查询结果的数组*/
  if (isDML) {
    /*存放查询结果中列的值的字符串,投影是各个列的值，其它的算子是行数，比如delete了1行，则该值为1*/
    valueStrings = (char **)my_malloc0(sizeof(char *) * tupleSchemaCount);
    if (valueStrings == NULL) {
      FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, 0, j);
      PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
      my_free(metas);
      return GNCDB_INTERNAL;
    }
    if (sqlEvent->plan->type == PO_PROJECT) {
      cols             = getColMeta(sqlEvent->plan);
      child            = varArrayListGetPointer(sqlEvent->plan->children, 0);
      childcols        = getColMeta(child);
      realChildColsNum = childcols->elementCount - 1;
      /*遍历投影列的微元并存入meta数组，同时根据投影列的类型为valueString对应的位置申请内存*/
      for (i = 0; i < cols->elementCount; i++) {
        meta = varArrayListGetPointer(cols, i);
        if (meta->fieldType == FIELDTYPE_INTEGER) {
          valueStrings[i] = (char *)my_malloc0(sizeof(char) * 21);  // int的最大长度
          if (valueStrings[i] == NULL) {
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, i + 1, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else if (meta->fieldType == FIELDTYPE_REAL) {
          valueStrings[i] = (char *)my_malloc0(sizeof(char) * 25);  // .15g最大长度（包括\0）
          if (valueStrings[i] == NULL) {
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, i + 1, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else if (meta->fieldType == FIELDTYPE_VARCHAR) {
          valueStrings[i] = (char *)my_malloc0(sizeof(char) * (meta->len + 1));  // varchar的最大长度
          if (valueStrings[i] == NULL) {
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, i + 1, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else if (meta->fieldType == FIELDTYPE_BLOB) {
          valueStrings[i] = (char *)my_malloc0(sizeof(char) * 42);  // varchar的最大长度
          if (valueStrings[i] == NULL) {
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, i + 1, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else {
          assert(false);
        }
        metas[i] = meta;
      }
    } else {
      /*int的最大长度，非投影列只需记录影响的列数*/
      valueStrings[0] = (char *)my_malloc0(sizeof(char) * 21);  // int的最大长度
      if (valueStrings[0] == NULL) {
        FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, 0, j);
        PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
        my_free(metas);
        return GNCDB_INTERNAL;
      }
    }
    /*Q:这里为什么又检查了一遍*/
    if (valueStrings == NULL) {
      FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
      PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
      my_free(metas);
      return GNCDB_INTERNAL;
    }
  }

  valueStringsTemp = (char **)my_malloc0(sizeof(char *) * tupleSchemaCount);
  if (valueStringsTemp == NULL) {
    FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    my_free(metas);
    return GNCDB_INTERNAL;
  }

  /*6.每次循环调用一次NEXT（）来获取一次执行结果,获取结果后将结果格式化未字符串并通过callback函数输出*/
  while (1) {
    rc = PhysicalOperatorNext(sqlEvent->plan, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      break;
    }
    if (isDML) {
      tuple = GetCurrentTuple(sqlEvent->plan);
      /*6.1tuple是算子一次执行的结果，如果传了callback函数，那么把查询结果通过callback函数输出*/
      if (sqlEvent->callback != NULL) {
        for (i = 0; i < tupleSchemaCount; i++) {
          valueStringsTemp[i] = valueStrings[i];
          meta                = metas[i];
          if (meta->type == NormalField) {
            bitmap = (BYTE *)tuple->data + meta->bitmapOffset;
            /*6.1.1根据record的bitmap来判断是否是NULL值*/
            if (leafTupleGetBitMap(bitmap, meta->index, realChildColsNum) == 0) {
              /*NULL 处理分支（小概率）*/
              valueStringsTemp[i] = "NULL";
            } else {
              /*正常值处理（大概率），就是把值输出为字符串并放到valueStringsTemp数组对应的位置*/
              valToStr(tuple->data + meta->offset, meta->fieldType, meta->len, &valueStringsTemp[i]);
            }
          } else {
            /*6.2Composite 字段处理（小概率）,比如查询 select a+b AS ab from table1，这里的a+b就是复合字段*/
            compositeMeta = (CompositeMeta *)meta;
            if (compositeMeta == NULL) {
              FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
              PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
              my_free(valueStringsTemp);
              my_free(metas);
              return GNCDB_INTERNAL;
            }

            /*6.2.1先获取复合类型的值*/
            valueSetNull(&val);
            rc = expressionGetValueNCP(compositeMeta->expr, tuple, childcols, &val);

            /*6.2.2错误处理*/
            if ((rc != GNCDB_SUCCESS)) {
              valueReset(&val);
              FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
              PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
              my_free(valueStringsTemp);
              my_free(metas);
              return rc;
            }
            /*6.2.3正常值处理，把计算过后的复合值放入数组*/
            if ((val.attrType != NULLS)) {
              VALUE_TO_STRING(&val, valueStringsTemp[i]);
              /*小概率错误*/
              if (valueStringsTemp[i] == NULL) {
                valueReset(&val);
                FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
                PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
                my_free(valueStringsTemp);
                my_free(metas);
                return GNCDB_INTERNAL;
              }
            } else {
              /*snprintf(valueStringsTemp[i], 5, "NULL");*/
              valueStringsTemp[i] = "NULL";
            }
            valueReset(&val);
          }
        }
        /*6.3调用用户传的callback输出结果*/
        sqlEvent->callback(sqlEvent->data, tupleSchemaCount, tupleSchema, valueStringsTemp);
      }
    }
  }

  my_free(valueStringsTemp);
  /*若是没有执行到最后一行提前退出则报错*/
  if (rc != GNCDB_NEXT_EOF) {
    FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    my_free(metas);
    return rc;
  }
  /*若是写操作则输出受影响的行数*/
  if (sqlEvent->plan->type == PO_INSERT || sqlEvent->plan->type == PO_DELETE || sqlEvent->plan->type == PO_UPDATE) {
    sprintf(valueStrings[0], "%d", sqlEvent->affectedRows);
    if (sqlEvent->callback != NULL) {
      sqlEvent->callback(sqlEvent->data, tupleSchemaCount, tupleSchema, valueStrings);
    }
  }
  /*清理资源*/
  rc = PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
    my_free(metas);
    return rc;
  }
  /*sqlEvent->isOpen = false;*/
  FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);

  my_free(metas);
  return GNCDB_SUCCESS;
}

int ExecutePhysicalPlanByStep(SQLStageEvent *sqlEvent)
{
  // 自顶向下执行open（如果尚未打开）
  // sql_event->is_open = 0;
  int               rc               = GNCDB_SUCCESS;
  char            **tupleSchema      = NULL;
  char            **valueStrings     = NULL;
  char            **valueStringsTemp = NULL;
  int               tupleSchemaCount = 0;
  Record           *tuple            = NULL;
  int               i = 0, j = 0;
  varArrayList     *cols             = NULL;
  varArrayList     *childcols        = NULL;
  CompositeMeta    *compositeMeta    = NULL;
  PhysicalOperator *child            = NULL;
  Meta             *meta             = NULL;
  Meta            **metas            = NULL;
  BYTE             *bitmap           = NULL;
  int               realChildColsNum = 0;
  int               isDML            = 0; /* 是否是增删改查 */
  Value             val;

  if (!sqlEvent->isOpen) {
    rc = PhysicalOperatorOpen(sqlEvent->plan, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    sqlEvent->isOpen = 1;
    rc               = getTupleSchemaAndCount(sqlEvent, &tupleSchema, &tupleSchemaCount);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    sqlEvent->res->fieldCount = tupleSchemaCount;
    sqlEvent->res->fieldNames = tupleSchema;
    // sqlEvent->res->fieldValues = tupleSchemaCount == 0 ? NULL : (char **)my_malloc0(sizeof(char *) *
    // tupleSchemaCount); init2DArray(sqlEvent->res->fieldValues, tupleSchemaCount);
  } else {
    tupleSchemaCount = sqlEvent->res->fieldCount;
    tupleSchema      = sqlEvent->res->fieldNames;
  }

  isDML = (sqlEvent->plan->type == PO_INSERT || sqlEvent->plan->type == PO_DELETE ||
              sqlEvent->plan->type == PO_UPDATE || sqlEvent->plan->type == PO_PROJECT)
              ? 1
              : 0;

  metas = my_malloc0(sizeof(Meta *) * tupleSchemaCount);
  if (metas == NULL) {
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    return GNCDB_INTERNAL;
  }

  if (isDML) {
    sqlEvent->res->fieldValues = (char **)my_malloc0(sizeof(char *) * tupleSchemaCount);
    if (sqlEvent->res->fieldValues == NULL) {
      FREE_SCHEMA_AND_VALUES(tupleSchema, sqlEvent->res->fieldValues, 0, j);
      PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
      my_free(metas);
      return GNCDB_INTERNAL;
    }
    valueStrings = sqlEvent->res->fieldValues;
    if (sqlEvent->plan->type == PO_PROJECT) {
      cols             = getColMeta(sqlEvent->plan);
      child            = varArrayListGetPointer(sqlEvent->plan->children, 0);
      childcols        = getColMeta(child);
      realChildColsNum = childcols->elementCount - 1;

      for (i = 0; i < cols->elementCount; i++) {
        meta = varArrayListGetPointer(cols, i);
        if (meta->fieldType == FIELDTYPE_INTEGER) {
          valueStrings[i] = (char *)my_malloc0(sizeof(char) * 21);  // int的最大长度
          if (valueStrings[i] == NULL) {
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, i + 1, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else if (meta->fieldType == FIELDTYPE_REAL) {
          valueStrings[i] = (char *)my_malloc0(sizeof(char) * 25);  // .15g最大长度（包括\0）
          if (valueStrings[i] == NULL) {
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, i + 1, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else if (meta->fieldType == FIELDTYPE_VARCHAR) {
          valueStrings[i] = (char *)my_malloc0(sizeof(char) * (meta->len + 1));  // varchar的最大长度
          if (valueStrings[i] == NULL) {
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, i + 1, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else {
          assert(false);
        }
        metas[i] = meta;
      }
    } else {
      valueStrings[0] = (char *)my_malloc0(sizeof(char) * 21);  // int的最大长度
      if (valueStrings[0] == NULL) {
        FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, 0, j);
        PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
        my_free(metas);
        return GNCDB_INTERNAL;
      }
    }
    if (valueStrings == NULL) {
      FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
      PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
      my_free(metas);
      return GNCDB_INTERNAL;
    }
  }

  valueStringsTemp = (char **)my_malloc0(sizeof(char *) * tupleSchemaCount);
  if (valueStringsTemp == NULL) {
    FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    my_free(metas);
    return GNCDB_INTERNAL;
  }

  rc = PhysicalOperatorNext(sqlEvent->plan, sqlEvent);
  if (rc == GNCDB_SUCCESS) {
    tuple = GetCurrentTuple(sqlEvent->plan);
    for (i = 0; i < tupleSchemaCount; i++) {
      valueStringsTemp[i] = valueStrings[i];
      meta                = metas[i];
      if (meta->type == NormalField) {
        bitmap = (BYTE *)tuple->data + meta->bitmapOffset;
        if (leafTupleGetBitMap(bitmap, meta->index, realChildColsNum) == 0) {  // NULL 处理分支（小概率）
          valueStringsTemp[i] = "NULL";
        } else {  // 正常值处理（大概率）
          valToStrCp(tuple->data + meta->offset, meta->fieldType, meta->len, &valueStringsTemp[i]);
        }
      } else {  // Composite 字段处理（小概率）
        compositeMeta = (CompositeMeta *)meta;
        if (compositeMeta == NULL) {
          FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
          PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
          my_free(valueStringsTemp);
          my_free(metas);
          return GNCDB_INTERNAL;
        }

        valueSetNull(&val);
        rc = expressionGetValueNCP(compositeMeta->expr, tuple, childcols, &val);

        if ((rc != GNCDB_SUCCESS)) {  // 错误处理
          valueReset(&val);
          FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
          PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
          my_free(valueStringsTemp);
          my_free(metas);
          return rc;
        }

        if ((val.attrType != NULLS)) {  // 正常值处理
          VALUE_TO_STRING(&val, valueStringsTemp[i]);
          if (valueStringsTemp[i] == NULL) {  // 小概率错误
            valueReset(&val);
            FREE_SCHEMA_AND_VALUES(tupleSchema, valueStrings, tupleSchemaCount, j);
            PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
            my_free(valueStringsTemp);
            my_free(metas);
            return GNCDB_INTERNAL;
          }
        } else {  // NULL 处理
          // snprintf(valueStringsTemp[i], 5, "NULL");
          valueStringsTemp[i] = "NULL";
        }

        valueReset(&val);
      }
    }
    if (sqlEvent->callback != NULL) {
      sqlEvent->callback(sqlEvent->data, tupleSchemaCount, tupleSchema, valueStringsTemp);
    }
    my_free(metas);
    my_free(valueStringsTemp);
    return GNCDB_SUCCESS;

  } else if (rc == GNCDB_NEXT_EOF) {
    my_free(metas);
    my_free(valueStringsTemp);
    SqlResultReset(sqlEvent->res);
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    sqlEvent->isOpen = 0;
    sqlEvent->plan   = NULL;
    return rc;
  } else {
    my_free(metas);
    my_free(valueStringsTemp);
    SqlResultReset(sqlEvent->res);
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    return rc;
  }
  return GNCDB_SUCCESS;
}

/**
 * @brief 删除该物理计划，并递归删除子计划
 *
 * @param physOper  需要删除的物理计划
 */
void PhysicalPlanDestroy(PhysicalOperator *physOper)
{
  PhysicalOperator *child     = NULL;
  PhysicalOperator *joinLeft  = NULL;
  PhysicalOperator *joinRight = NULL;
  if (physOper == NULL) {
    return;
  }
  switch (physOper->type) {
    case PO_INSERT: {
      InsertPhysOperDestroy((InsertPhysicalOperator *)physOper);
    } break;
    case PO_TABLE_SCAN: {
      TableScanPhysOperDestroy((TableScanPhysicalOperator *)physOper);
    } break;
    case PO_INDEX_SCAN: {
      IndexScanPhysOperDestroy((IndexScanPhysicalOperator *)physOper);
    } break;
    case PO_NESTED_LOOP_JOIN: {
      PhysicalPlanDestroy(((NestedLoopJoinPhysicalOperator *)physOper)->left);
      PhysicalPlanDestroy(((NestedLoopJoinPhysicalOperator *)physOper)->right);
      NestedLoopJoinPhysOperDestroy((NestedLoopJoinPhysicalOperator *)physOper);
    } break;
    case PO_PREDICATE: {
      if (((PredicatePhysicalOperator *)physOper)->children == NULL ||
          ((PredicatePhysicalOperator *)physOper)->children->elementCount == 0) {
        PredicatePhysOperDestroy((PredicatePhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((PredicatePhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      PredicatePhysOperDestroy((PredicatePhysicalOperator *)physOper);
    } break;
    case PO_DELETE: {
      if (((DeletePhysicalOperator *)physOper)->children == NULL ||
          ((DeletePhysicalOperator *)physOper)->children->elementCount == 0) {
        DeletePhysOperDestroy((DeletePhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((DeletePhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      DeletePhysOperDestroy((DeletePhysicalOperator *)physOper);
    } break;
    case PO_PROJECT: {
      if (((ProjectPhysicalOperator *)physOper)->children == NULL ||
          ((ProjectPhysicalOperator *)physOper)->children->elementCount == 0) {
        ProjectPhysOperDestroy((ProjectPhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((ProjectPhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      ProjectPhysOperDestroy((ProjectPhysicalOperator *)physOper);
    } break;
    case PO_UPDATE: {
      if (((UpdatePhysicalOperator *)physOper)->children == NULL ||
          ((UpdatePhysicalOperator *)physOper)->children->elementCount == 0) {
        UpdatePhysOperDestroy((UpdatePhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((UpdatePhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      UpdatePhysOperDestroy((UpdatePhysicalOperator *)physOper);
    } break;
    case PO_GROUPBY: {
      if (((GroupByPhysicalOperator *)physOper)->children == NULL ||
          ((GroupByPhysicalOperator *)physOper)->children->elementCount == 0) {
        GroupByPhysOperDestroy((GroupByPhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((GroupByPhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      GroupByPhysOperDestroy((GroupByPhysicalOperator *)physOper);
    } break;
    case PO_HASH_JOIN: {
      if (((HashJoinPhysicalOperator *)physOper)->children == NULL ||
          ((HashJoinPhysicalOperator *)physOper)->children->elementCount == 0) {
        HashJoinPhysOperDestroy((HashJoinPhysicalOperator *)physOper);
        return;
      }
      joinLeft  = varArrayListGetPointer(((UpdatePhysicalOperator *)physOper)->children, 0);
      joinRight = varArrayListGetPointer(((UpdatePhysicalOperator *)physOper)->children, 1);
      PhysicalPlanDestroy(joinLeft);
      PhysicalPlanDestroy(joinRight);
      HashJoinPhysOperDestroy((HashJoinPhysicalOperator *)physOper);
    } break;
    case PO_SORT_MERGE_JOIN: {
      if (((SortMergeJoinPhysicalOperator *)physOper)->children == NULL ||
          ((SortMergeJoinPhysicalOperator *)physOper)->children->elementCount == 0) {
        SortMergeJoinPhysOperDestroy((SortMergeJoinPhysicalOperator *)physOper);
        return;
      }
      PhysicalPlanDestroy(((SortMergeJoinPhysicalOperator *)physOper)->left);
      PhysicalPlanDestroy(((SortMergeJoinPhysicalOperator *)physOper)->right);
      SortMergeJoinPhysOperDestroy((SortMergeJoinPhysicalOperator *)physOper);
    } break;
    case PO_ORDER_BY: {
      if (((OrderByPhysicalOperator *)physOper)->children == NULL ||
          ((OrderByPhysicalOperator *)physOper)->children->elementCount == 0) {
        OrderByPhysOperDestroy((OrderByPhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((OrderByPhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      OrderByPhysOperDestroy((OrderByPhysicalOperator *)physOper);
    } break;
    case PO_CREATE_TABLE: {
      if (((CreateTablePhysicalOperator *)physOper)->children == NULL ||
          ((CreateTablePhysicalOperator *)physOper)->children->elementCount == 0) {
        CreateTablePhysOperDestroy((CreateTablePhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((CreateTablePhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      CreateTablePhysOperDestroy((CreateTablePhysicalOperator *)physOper);
    } break;
    case PO_LIMIT: {
      if (((LimitPhysicalOperator *)physOper)->children == NULL ||
          ((LimitPhysicalOperator *)physOper)->children->elementCount == 0) {
        LimitPhysOperDestroy((LimitPhysicalOperator *)physOper);
        return;
      }
      child = varArrayListGetPointer(((LimitPhysicalOperator *)physOper)->children, 0);
      PhysicalPlanDestroy(child);
      LimitPhysOperDestroy((LimitPhysicalOperator *)physOper);
    } break;
    default:;  // do nothing
  }
}

int PlanDestroy(SQLStageEvent *sqlEvent)
{
  if (sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (sqlEvent->plan != NULL) {
    PhysicalPlanDestroy(sqlEvent->plan);
    sqlEvent->plan = NULL;
  } else {
    StmtDestroy(sqlEvent->stmt);
    sqlEvent->stmt = NULL;
  }
  return GNCDB_SUCCESS;
}