#include "catalog.h"
#include "drop_index_stmt.h"
#include "hash.h"

int DropIndexExecute(SQLStageEvent *sqlEvent)
{
  int            rc            = GNCDB_SUCCESS;
  Stmt          *stmt          = NULL;
  DropIndexStmt *dropIndexStmt = NULL;

  stmt = sqlEvent->stmt;
  if (stmt->type != ST_DROP_INDEX) {
    return GNCDB_PARAM_INVALID;
  }

  dropIndexStmt = (DropIndexStmt *)stmt;

  // todo 当前仅仅删除只能哈希索引，实际应该根据索引类型调用不同的索引删除函数
  // 执行删除索引操作
  rc = GNCDB_dropHashIndex(sqlEvent->db, dropIndexStmt->relationName, dropIndexStmt->indexName);
  if (rc != 0) {
    return rc;
  }

  return rc;
}