/**
 * @file operator.c
 * <AUTHOR>
 * @brief 为用户提供算子模型，供用户输入，
 * 然后将被组织到执行计划中进行执行
 * @date 2023-02-14
 *
 * @copyright Copyright (c) 2023
 */

 #include "operator.h"
 #include "btreepage.h"
 #include "hash.h"
#include "typedefine.h"
 
  /// <summary>
  /// 通过join算子实现连接后保存数据指针的Orderarraylist的销毁函数
  /// </summary>
  /// <param name="data">数据指针</param>
 void operatorTupleDestroy(void* data)
 {
     Tuple** ptuple = data;
     Tuple* tuple = *ptuple;
     if (data == NULL)
     {
         return;
     }
     varArrayListDestroy(&(tuple->fieldArray));
     my_free(tuple);
 }
 
 /*****************************************************
 算子类型
 *****************************************************/
 
 /// <summary>
 /// 将谓词类型转换为相应的符号
 /// </summary>
 /// <param name="">谓词类型</param>
 /// <returns>符号指针</returns>
 char* getOpStr(enum Predicate predicate)
 {
     char* opStr = NULL;
     switch (predicate)
     {
     case GREATER_THAN: opStr = ">"; break;
     case GREATER_THAN_OR_EQUAL: opStr = ">="; break;
     case EQUAL: opStr = "="; break;
     case LESS_THAN: opStr = "<"; break;
     case LESS_THAN_OR_EQUAL: opStr = "<="; break;
     default: break;
     }
 
     return opStr;
 }
 
 /// <summary>
 /// 构建一个算子类型
 /// </summary>
 /// <param name="type">算子类型</param>
 /// <returns>成功返回算子指针，失败返回错误信息</returns>
 Operator* operatorConstruct(OperatorType type)
 {
     Operator* operator = NULL;
 
     operator = my_malloc(sizeof(struct Operator));
     if (operator == NULL)
     {
         return NULL;
     }                       
     operator->type = type;
 
     return operator;
 }
 
 /// <summary>
 /// 销毁一个算子类型
 /// </summary>
 /// <returns>成功返回GNCDB_SUCCESS，失败返回错误信息</returns>
 int operatorDestroy(Operator* operator)
 {
     if (operator == NULL)
     {
         return GNCDB_PARAMNULL;
     }
     my_free(operator);
 
     return GNCDB_SUCCESS;
 }
 
 /*****************************************************
 Scan算子
 *****************************************************/
 
 /// <summary>
 /// scan算子构造函数
 /// </summary>
 /// <param name="db"></param>
 /// <param name="tableName"></param>
 /// <param name="op"></param>
 /// <param name="tid"></param>
 /// <returns></returns>
 Scan* scanConstruct(struct GNCDB* db, char* tableName, varArrayList* startKeyValue, varArrayList* maxValue, struct Transaction* tx)
 {
     Scan* scan = NULL;
     if (tableName == NULL)
     {
         return NULL;
     }
     scan = my_malloc(sizeof(struct Scan));
     if (scan == NULL)
     {
         return NULL;
     }
     scan->baseinfo = operatorConstruct(OperatorType_SCAN);
     if (scan->baseinfo == NULL)
     {
         my_free(scan);
         return NULL;
     }
     scan->tableName = tableName;
     scan->startKeyValue = startKeyValue;
     scan->maxValue = maxValue;
     scan->isEnd = false;
     scan->cursor = btreeCursorConstruct(tableName, db, startKeyValue, tx);
     if (scan->cursor == NULL)
     {
         scanDestroy(scan);
         return NULL;
     }
     return scan;
 }
 
 int resetScan(Scan* scan, struct GNCDB* db, struct Transaction* tx)
 {
     btreeCursorDestroy(&(scan->cursor));
     scan->cursor = btreeCursorConstruct(scan->tableName, db, scan->startKeyValue, tx);
     if (scan->cursor == NULL)
     {
         return GNCDB_BTC_CREATE_FALSE;
     }
     scan->isEnd = false;
     return GNCDB_SUCCESS;
 }
 
 /// <summary>
 /// 判断是否有下一条记录
 /// </summary>
 /// <param name="scan"></param>
 /// <returns></returns>
 bool hasNextTuple(Scan* scan)
 {
     if(scan->isEnd)
     {
         return false;
     }
     return btreeTableHasNextTuple(scan->cursor);
 }
 
 /// <summary>
 /// 获取下一条记录
 /// </summary>
 /// <param name="db"></param>
 /// <param name="scan"></param>
 /// <param name="tid"></param>
 /// <returns></returns>
 BYTE* operatorScan(struct GNCDB* db, Scan* scan)
{
    /* 获取btreeTable */
    int rc = 0;
    BtreeTable* btreeTable = NULL;
    // Tuple* tuple = NULL;
    BYTE* record = NULL;
    int i = 0;
    int accordFlag = 0;
    void* value = NULL;
    // Field* field = NULL;
    // IntField* intField = NULL;
    int intFieldValue = 0;
    int intValue = 0;
    // RealField* realField = NULL;
    double realFieldValue = 0;
    double doubleValue = 0;
    // VarCharField* charField = NULL;
    char charFieldValue[255] = {0};
    char* strValue = NULL;
    varArrayList* keyIndex = NULL;
    varArrayList* keyOffset = NULL;
    int *index = NULL;
    TableSchema *tableSchema = NULL;
    Column* column = NULL;
    int *offset = 0;

    rc = catalogGetTable(db->catalog, &btreeTable, scan->tableName);
    if (rc != GNCDB_SUCCESS) {
        return NULL;
    }
    tableSchema = getTableSchema(db->catalog, scan->tableName);
    if (rc != GNCDB_SUCCESS) {
        return NULL;
    }
    /* 通过游标获取下一个tuple */
    record = btreeTableGetNextRecord(btreeTable, scan->cursor, db);
    // tuple = btreeTableGetNextTuple(btreeTable, scan->cursor, db);
    if (record == NULL)
    {
        return NULL;
    }

    /* 如果没有末尾的下界便直接返回 否则判断是否到达下界 */
    if (scan->maxValue == NULL || scan->maxValue->elementCount == 0)
    {
        return record;
    }
    keyIndex = getPrimaryIndexArray(db->catalog, scan->tableName);
    keyOffset = getPrimaryOffsetArray(db->catalog, scan->tableName);
    for (i = 0; i < scan->maxValue->elementCount; ++i)
    {
        value = varArrayListGetPointer(scan->maxValue, i);
        index = varArrayListGet(keyIndex, i);
        offset = varArrayListGet(keyOffset, i);
        column = varArrayListGetPointer(tableSchema->columnList, *index);
        // field = varArrayListGetPointer(tuple->fieldArray, *index);
        
        if (column->fieldType == FIELDTYPE_INTEGER)
        {
            // intField = (IntField*)field;
            memcpy(&intFieldValue, record + *offset, sizeof(int));
            intValue = *(int*)value;
            if (intValue >= intFieldValue)
            {
                accordFlag++;
            }
        }
        else if (column->fieldType == FIELDTYPE_REAL)
        {
            // realField = (RealField*)field;
            memcpy(&realFieldValue, record + *offset, sizeof(double));
            doubleValue = *(double*)value;
            if (doubleValue >= realFieldValue)
            {
                accordFlag++;
            }
        }
        else if (column->fieldType == FIELDTYPE_VARCHAR)
        {
            // charField = (VarCharField*)field;
            memcpy(charFieldValue, record + *offset, column->columnConstraint->maxValue);
            strValue = (char*)value;
            if (strcmp(strValue, charFieldValue) >= 0)
            {
                accordFlag++;
            }
        }

    }
    if (accordFlag == scan->maxValue->elementCount)
    {
        return record;
    }
    else
    {
        scan->isEnd = true;
        return NULL;
    }
    
}

 
 /// <summary>
 /// scan算子销毁函数
 /// </summary>
 /// <param name="scan"></param>
 void scanDestroy(Scan* scan)
 {
     if (scan == NULL)
     {
         return;
     }
     operatorDestroy(scan->baseinfo);
     if (scan->startKeyValue != NULL)
     {
         varArrayListDestroy(&scan->startKeyValue);
     }
     varArrayListDestroy(&scan->maxValue);
     if (scan->cursor != NULL)
     {
         btreeCursorDestroy(&(scan->cursor));
     }
     my_free(scan);
 
     return;
 }
 
 /*****************************************************
 Filter算子
 *****************************************************/
 
 /**
   * @brief 判断条件condition的创建
   * @param value 存放field值（函数外分配空间）
   * @param fieldName 需要进行比较的字段名称（函数外分配空间）
   * @param predicate 判断符号
   * @return 返回创建的condition
   */
 struct Condition* conditionConstruct(void* value, char* fieldName, Predicate predicate) {
     Condition* condition = NULL;
     if (value == NULL || fieldName == NULL) {
         return NULL;
     }
     condition = (Condition*)my_malloc(sizeof(Condition));
     if (condition == NULL) {
         return NULL;
     }
 
     condition->value = value;
     condition->fieldName = fieldName;
     condition->predicate = predicate;
 
     return condition;
 }
 
 /**
  * @brief 判断条件condition的销毁
  * @param condition 判断条件
  */
 void conditonDestroy(void* data) {
     Condition** condition = (Condition** )data;
     if (condition == NULL || *condition == NULL) {
         return;
     }
     if ((*condition)->fieldName != NULL)
     {
         my_free((*condition)->fieldName);
     }
     if ((*condition)->value != NULL)
     {
         my_free((*condition)->value);
     }
     
     my_free(*condition);
 
     *condition = NULL;
 }
 
 /// <summary>
 /// 创建一个Filter算子并进行初始化
 /// </summary>
 /// <param name="db">数据库指针</param>
 /// <param name="tableName">表名</param>
 /// <returns>成功返回结构体指针，失败返回NULL</returns>
 Filter* filterConstruct(GNCDB* db, varArrayList* conditionArray, char* tableName)
 {
     Filter* filter = NULL;
     /* 判断参数是否有效 */
     if (tableName == NULL )
     {
         return NULL;
     }
     /* 为结构体申请空间 */
     filter = my_malloc(sizeof(struct Filter));
     if (filter == NULL)
     {
         return NULL;
     }
     filter->baseinfo = operatorConstruct(OperatorType_FILTER);
     if (filter->baseinfo == NULL)
     {
         my_free(filter);
         return NULL;
     }
     /* 初始化 */
     filter->tableName = tableName;
     filter->conditionArray = conditionArray;
     
     return filter;
 }
 
 /// <summary>
 /// filter算子的实现,满足条件返回true
 /// </summary>
 /// <param name="filter"></param>
 /// <param name="tuple"></param>
 /// <param name="tableSchema"></param>
 /// <returns></returns>
 bool operatorFilter(Filter* filter, BYTE* record, TableSchema* tableSchema, Catalog* catalog)
{
    int accordFlag = 1;
    int i = 0;
    Condition* condition = NULL;
    char* fieldName = NULL;
    void* value = NULL;
    Predicate predicate = 1;
    int fieldIndex = 0;
    // Field* field = NULL;
    FieldType fieldType = 0;
    int intValue = 0;
    double doubleValue = 0;
    char charValue[255] = {0};
    Column* column = NULL;


    if (filter->conditionArray == NULL || filter->conditionArray->elementCount == 0)
    {
        return true;
    }

    for (i = 0; i < filter->conditionArray->elementCount; i++) 
    {
        /* 获取判断条件 */
        condition = (Condition*)varArrayListGetPointer(filter->conditionArray, i);
        if (condition == NULL) 
        {
            return false;
        }
        fieldName = condition->fieldName;
        value = condition->value;
        predicate = condition->predicate;
        /* 获取条件中的属性名下标 */
        fieldIndex = tableSchemaGetIndex(tableSchema, fieldName);
    
        if (fieldIndex < 0) 
        {
            return false;
        }
        column = varArrayListGetPointer(tableSchema->columnList, fieldIndex);
        if (column == NULL)
        {
            return false;
        }
        /* 拿取下标对应的数据 */
        // field = (Field*)varArrayListGetPointer(tuple->fieldArray, fieldIndex);
        // if (field == NULL) 
        // {
        //     return false;
        // }
        // fieldType = field->fieldType;
        fieldType = column->fieldType;
        /* 与condition中的数据进行比较 */
        switch (fieldType)
        {
        case FIELDTYPE_INTEGER: 
        {
            // accordFlag = compareCertainFieldValue(&((IntField*)field)->value, (int*)value, fieldType, predicate);
            memcpy(&intValue, record + column->offset, sizeof(int));
            accordFlag = compareCertainFieldValue(&intValue, (int*)value, fieldType, predicate);
            break;
        }
        case FIELDTYPE_REAL: 
        {
            // accordFlag = compareCertainFieldValue(&((RealField*)field)->value, value, fieldType, predicate);
            memcpy(&doubleValue, record + column->offset, sizeof(double));
            accordFlag = compareCertainFieldValue(&doubleValue, (double*)value, fieldType, predicate);
            break;
        }
        case FIELDTYPE_VARCHAR: 
        {
            // accordFlag = compareCertainFieldValue(((VarCharField*)field)->value, (char*)value, fieldType, predicate);
            memcpy(charValue, record + column->offset, column->columnConstraint->maxValue);
            accordFlag = compareCertainFieldValue(charValue, (char*)value, fieldType, predicate);
            break;
        }
        default:
            break;
        }

        if (accordFlag == 0)
        {
            return false;
        }
    }

    if (accordFlag == 1)
    {
        return true;
    }
    else
    {
        return false;
    }
}

 
 /// <summary>
 /// 销毁一个filter算子
 /// </summary>
 /// <param name="filter">filter算子</param>
 /// <returns>成功返回GNCDB_SUCCESS，失败返回错误信息</returns>
 int filterDestroy(Filter* filter)
 {
     if (filter == NULL)
     {
         return GNCDB_SUCCESS;
     }
     operatorDestroy(filter->baseinfo);
     if (filter->conditionArray != NULL)
     {
         varArrayListDestroy(&(filter->conditionArray));
     }
     
     my_free(filter);
 
     return GNCDB_SUCCESS;
 }
 
 /*****************************************************
 Join算子
 *****************************************************/
 
 /// <summary>
 /// 构造一个Join算子并进行初始化
 /// </summary>
 /// <param name="db">数据库指针</param>
 /// <param name="tableName1">表1名</param>
 /// <param name="fieldName1">表1属性</param>
 /// <param name="tableName2">表2名</param>
 /// <param name="fieldName2">表2属性</param>
 /// <param name="predicate">谓词</param>
 /// <returns>成功返回结构体指针，失败返回出错信息</returns>
 Join* joinConstruct(GNCDB* db, char* tableName1, char* tableName2, varArrayList* conditionArray)
 {
     Join* join = NULL;
     if (db == NULL || tableName1 == NULL || tableName2 == NULL || conditionArray == NULL)
     {
         return NULL;
     }
 
     join = my_malloc(sizeof(struct Join));
     if (join == NULL)
     {
         return NULL;
     }
     join->baseinfo = operatorConstruct(OperatorType_JOIN);
     if (join->baseinfo == NULL)
     {
         my_free(join);
         return NULL;
     }
     // 初始化
     join->tableName1 = tableName1;
     join->tableName2 = tableName2;
     join->conditionArray = conditionArray;
 
     return join;
 }
 
 /// <summary>
 /// join算子的实现
 /// </summary>
 /// <param name="db">数据库指针</param>
 /// <param name="cursor">游标</param>
 /// <param name="newTuple">保存连接好的元组</param>
 /// <param name="tuple1">表一的元组</param>
 /// <param name="join">Join算子</param>
 /// <param name="tid"></param>
 /// <returns>状态码</returns>
 int operatorJoin(GNCDB* db, BYTE** newRecord, TableSchema* tableSchema1, BYTE* record1, TableSchema* tableSchema2, BYTE* record2, Join* join, struct Transaction* tx)
{
    int rc = 0;
    int accordFlag = 1;
    int i = 0;
    Condition* condition = NULL;
    char* fieldName1 = NULL;
    char* fieldName2 = NULL;
    Predicate predicate = 0;
    int fieldIndex1 = 0;
    int fieldIndex2 = 0;
    // Field* field1 = NULL;
    // Field* field2 = NULL;
    FieldType fieldType = 0;
    Column* column1 = NULL;
    Column* column2 = NULL;
    int intValue1 = 0;
    int intValue2 = 0;
    double doubleValue1 = 0;
    double doubleValue2 = 0;
    char charValue1[255] = {0};
    char charValue2[255] = {0};
    // Tuple* tuple = NULL;
    BYTE* record = NULL;
    // Tuple* p = NULL;
    BtreeTable* btreeTable1 = NULL;
    BtreeTable* btreeTable2 = NULL;

    if (db == NULL || record1 == NULL || record2 == NULL || join == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    /* 获取btreeTable */
    rc = catalogGetTable(db->catalog, &btreeTable1, join->tableName1);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = catalogGetTable(db->catalog, &btreeTable2, join->tableName2);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    for (i = 0; i < join->conditionArray->elementCount; ++i)
    {
        /* 获取判断条件 */
        condition = (Condition*)varArrayListGetPointer(join->conditionArray, i);
        if (condition == NULL)
        {
            return GNCDB_NOT_FOUND;
        }
        fieldName1 = condition->fieldName;
        fieldName2 = condition->value;
        predicate = condition->predicate;
        /* 获取条件中的属性名下标 */
        fieldIndex1 = tableSchemaGetIndex(tableSchema1, fieldName1);
        if (fieldIndex1 < 0)
        {
            return GNCDB_COLUMN_NOT_FOUND;
        }
        column1 = varArrayListGetPointer(tableSchema1->columnList, fieldIndex1);
        if(column1 == NULL)
        {
            return GNCDB_COLUMN_NOT_FOUND;
        }
        fieldIndex2 = tableSchemaGetIndex(tableSchema2, fieldName2);
        if (fieldIndex2 < 0)
        {
            return GNCDB_COLUMN_NOT_FOUND;
        }
        column2 = varArrayListGetPointer(tableSchema2->columnList, fieldIndex2);
        if(column2 == NULL)
        {
            return GNCDB_COLUMN_NOT_FOUND;
        }
        /* 拿取下标对应的数据 */
        // field1 = (Field*)varArrayListGetPointer(tuple1->fieldArray, fieldIndex1);
        // if (field1 == NULL)
        // {
        //     return GNCDB_COLUMN_NOT_FOUND;
        // }
        // field2 = (Field*)varArrayListGetPointer(tuple2->fieldArray, fieldIndex2);
        // if (field1 == NULL)
        // {
        //     return GNCDB_COLUMN_NOT_FOUND;
        // }
        // fieldType = field1->fieldType;
        fieldType = column1->fieldType;
        /* 与condition中的数据进行比较 */
        switch (fieldType)
        {
        case FIELDTYPE_INTEGER:
        {
            memcpy(&intValue1, record1 + column1->offset, sizeof(int));
            memcpy(&intValue2, record2 + column2->offset, sizeof(int));
            accordFlag = compareCertainFieldValue(&intValue1, &intValue2, fieldType, predicate);
            // accordFlag = compareCertainFieldValue(&((IntField*)field1)->value, &((IntField*)field2)->value, fieldType, predicate);
            break;
        }
        case FIELDTYPE_REAL:
        {
            memcpy(&doubleValue1, record1 + column1->offset, sizeof(double));
            memcpy(&doubleValue2, record2 + column2->offset, sizeof(double));
            accordFlag = compareCertainFieldValue(&doubleValue1, &doubleValue2, fieldType, predicate);
            // accordFlag = compareCertainFieldValue(&((RealField*)field1)->value, &((RealField*)field2)->value, fieldType, predicate);
            break;
        }
        case FIELDTYPE_VARCHAR:
        {
            memcpy(charValue1, record1 + column1->offset, column1->columnConstraint->maxValue);
            memcpy(charValue2, record2 + column2->offset, column2->columnConstraint->maxValue);
            accordFlag = compareCertainFieldValue(charValue1, charValue2, fieldType, predicate);
            // accordFlag = compareCertainFieldValue(((VarCharField*)field1)->value, ((VarCharField*)field2)->value, fieldType, predicate);
            break;
        }
        default:
            break;
        }

        if (accordFlag == 0)
        {
            return GNCDB_UN_JOIN;
        }
    }
    if (accordFlag == 0)
    {
        return GNCDB_UN_JOIN;
    }

    /* 说明两条tuple符合条件,进行连接 */
    record = my_malloc(btreeTable1->leafRecordLength + btreeTable2->leafRecordLength);
    // tuple = my_malloc((sizeof(struct Tuple)));
    // if (tuple == NULL)
    // {
    //     return GNCDB_SPACE_LACK;
    // }
    // tuple->fieldArray = varArrayListCreate(DISORDER,BYTES_POINTER, 0, NULL, NULL);
    // if (tuple->fieldArray == NULL)
    // {
    //     my_free(tuple);
    //     return GNCDB_NOT_FOUND;
    // }
    // for (i = 0; i < tuple1->fieldArray->elementCount; ++i)
    // {
    //     p = varArrayListGetPointer(tuple1->fieldArray, i);
    //     varArrayListAddPointer(tuple->fieldArray, p);
    // }
    memcpy(record, record1, btreeTable1->leafRecordLength);
    // for (i = 0; i < tuple2->fieldArray->elementCount; ++i)
    // {
    //     p = varArrayListGetPointer(tuple2->fieldArray, i);
    //     varArrayListAddPointer(tuple->fieldArray, p);
    // }
    memcpy(record + btreeTable1->leafRecordLength, record2, btreeTable2->leafRecordLength);

    // *newTuple = tuple;
    *newRecord = record;
    return GNCDB_SUCCESS;
}   

 
 /// <summary>
 /// 销毁一个Join算子
 /// </summary>
 /// <param name="join">Join算子</param>
 /// <returns>成功返回GNCDB_SUCCESS，失败返回错误信息</returns>
 int joinDestroy(Join* join)
 {
     if (join == NULL)
     {
         return GNCDB_PARAMNULL;
     }
     if (join->conditionArray != NULL)
     {
         varArrayListDestroy(&join->conditionArray);
     }
     operatorDestroy(join->baseinfo);
     my_free(join);
 
     return GNCDB_SUCCESS;
 }
 /*****************************************************
 projection算子
 *****************************************************/
 
 /// <summary>
 /// 构造一个projection算子
 /// </summary>
 /// <param name="db">数据库指针</param>
 /// <param name="array">属性列表</param>
 /// <returns>成功返回结构体指针，失败返回NULL</returns>
 Projection* projectionConstruct(GNCDB* db, varArrayList* array)
 {
     Projection* projection = NULL;
     if (db == NULL || array == NULL)
     {
         return NULL;
     }
     projection = my_malloc(sizeof(struct Projection));
     if (projection == NULL)
     {
         return NULL;
     }
     projection->baseinfo = operatorConstruct(OperatorType_PROJECTION);
     if (projection->baseinfo == NULL)
     {
         my_free(projection);
         return NULL;
     }
     projection->fieldNameArray = array;
 
     return projection;
 }
 
 /// <summary>
 /// projection算子的实现
 /// </summary>
 /// <param name="db">数据库指针</param>
 /// <param name="tableSchema"></param>
 /// <param name="newTuple"></param>
 /// <param name="tuple"></param>
 /// <param name="projection"></param>
 /// <param name="tid"></param>
 /// <returns></returns>
 int operatorProjection(struct GNCDB* db, struct TableSchema* tableSchema, BYTE** pNewLeafRecord, BYTE* leafRecord, struct Projection* projection, struct Transaction* tx,BtreeTable* btreeTable)
{
    // varArrayList* indexArray = NULL;
    int i = 0;
    char* fieldName = NULL;
    int index = 0;
    // Tuple* newTuple = NULL;
    BYTE* newLeafRecord = NULL;
    // char* pindex = NULL;
    // int j = 0;
    Column* column = NULL;
    // Field* field = NULL;
    if (db == NULL || leafRecord == NULL || projection == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    // /* 创建一个新的数组保存需要投影的属性的下标 */
    // indexArray = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
    // if (indexArray == NULL)
    // {
    //     return GNCDB_SPACE_LACK;
    // }
    newLeafRecord = my_malloc(btreeTable->leafRecordLength);

    /* 遍历寻找属性下标并保存 */
    for (i = 0; i < projection->fieldNameArray->elementCount; ++i)
    {
        fieldName = varArrayListGetPointer(projection->fieldNameArray, i);
        index = tableSchemaGetIndex(tableSchema, fieldName);
        column = varArrayListGetPointer(tableSchema->columnList, index);
        switch (column->fieldType)
        {
            case FIELDTYPE_INTEGER:
            {
                memcpy(newLeafRecord + column->offset, leafRecord + column->offset, sizeof(int));
                break;
            }
            case FIELDTYPE_REAL:
            {
                memcpy(newLeafRecord + column->offset, leafRecord + column->offset, sizeof(double));
                break;
            }
            case FIELDTYPE_VARCHAR:
            {
                memcpy(newLeafRecord + column->offset, leafRecord + column->offset, column->columnConstraint->maxValue);
                break;
            }
            default:
                break;
        }
        // varArrayListAdd(indexArray, &index);
    }

    // newTuple = my_malloc((sizeof(struct Tuple)));
    // if (newTuple == NULL)
    // {
    //     return GNCDB_SPACE_LACK;
    // }
    
    // newTuple->fieldArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    // if (newTuple->fieldArray == NULL)
    // {
    //     my_free(newTuple);
    //     return GNCDB_SUCCESS;
    // }

    /* 对元组进行筛选，将符合要求的保存在新的元组里 */
    // pindex = indexArray->dataBuffer;
    // for (j = 0; j < indexArray->elementCount; ++j)
    // {
    //     index = *pindex;
    //     field = varArrayListGetPointer(tuple->fieldArray, index);

    //     varArrayListAddPointer(newTuple->fieldArray, field);

    //     pindex = pindex + indexArray->elementSize;
    // }
    // varArrayListDestroy(&indexArray);

    // *pNewTuple = newTuple;
    *pNewLeafRecord = newLeafRecord;

    return GNCDB_SUCCESS;
}

int operatorJoinProjection(struct GNCDB* db, struct TableSchema* tableSchema, BYTE** pNewLeafRecord, BYTE* leafRecord, struct Projection* projection, struct Transaction* tx, varArrayList* joinColumnOffsetArray){
    // varArrayList* indexArray = NULL;
    int i = 0;
    char* fieldName = NULL;
    int index = 0;
    // Tuple* newTuple = NULL;
    BYTE* newLeafRecord = NULL;
    // char* pindex = NULL;
    // int j = 0;
    Column* column = NULL;
    int offset = 0;
    int len = 0;
    int offset_new = 0;
    // Field* field = NULL;
    if (db == NULL || leafRecord == NULL || projection == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    /*遍历计算record长度*/
    for (i = 0; i < projection->fieldNameArray->elementCount; ++i)
    {
        fieldName = varArrayListGetPointer(projection->fieldNameArray, i);
        index = tableSchemaGetIndex(tableSchema, fieldName);
        column = varArrayListGetPointer(tableSchema->columnList, index);
        switch (column->fieldType)
        {
            case FIELDTYPE_INTEGER:
            {
                len += INT_SIZE;
                break;
            }
            case FIELDTYPE_REAL:
            {
                len += DOUBLE_SIZE;
                break;
            }
            case FIELDTYPE_VARCHAR:
            {
               len += column->columnConstraint->maxValue;
                break;
            }
            default:
                break;
        }
        len += GET_BITMAP_LENGTH(projection->fieldNameArray->elementCount);
        // varArrayListAdd(indexArray, &index);
    }
    newLeafRecord = my_malloc(len);
    offset_new = GET_BITMAP_LENGTH(projection->fieldNameArray->elementCount); 
    /* 遍历寻找属性下标并保存 */
    for (i = 0; i < projection->fieldNameArray->elementCount; ++i)
    {
        fieldName = varArrayListGetPointer(projection->fieldNameArray, i);
        index = tableSchemaGetIndex(tableSchema, fieldName);
        column = varArrayListGetPointer(tableSchema->columnList, index);
        offset = *(int*)varArrayListGet(joinColumnOffsetArray, index);
        switch (column->fieldType)
        {
            case FIELDTYPE_INTEGER:
            {
                memcpy(newLeafRecord + offset_new, leafRecord + offset, sizeof(int));
                offset_new += INT_SIZE;
                break;
            }
            case FIELDTYPE_REAL:
            {
                memcpy(newLeafRecord + offset_new, leafRecord + offset, sizeof(double));
                offset_new += DOUBLE_SIZE;
                break;
            }
            case FIELDTYPE_VARCHAR:
            {
                memcpy(newLeafRecord + offset_new, leafRecord + offset, column->columnConstraint->maxValue);
                offset_new += column->columnConstraint->maxValue;
                break;
            }
            default:
                break;
        }
        // varArrayListAdd(indexArray, &index);
    }

    // newTuple = my_malloc((sizeof(struct Tuple)));
    // if (newTuple == NULL)
    // {
    //     return GNCDB_SPACE_LACK;
    // }
    
    // newTuple->fieldArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    // if (newTuple->fieldArray == NULL)
    // {
    //     my_free(newTuple);
    //     return GNCDB_SUCCESS;
    // }

    /* 对元组进行筛选，将符合要求的保存在新的元组里 */
    // pindex = indexArray->dataBuffer;
    // for (j = 0; j < indexArray->elementCount; ++j)
    // {
    //     index = *pindex;
    //     field = varArrayListGetPointer(tuple->fieldArray, index);

    //     varArrayListAddPointer(newTuple->fieldArray, field);

    //     pindex = pindex + indexArray->elementSize;
    // }
    // varArrayListDestroy(&indexArray);

    // *pNewTuple = newTuple;
    *pNewLeafRecord = newLeafRecord;

    return GNCDB_SUCCESS;
}


 
 /// <summary>
 /// 销毁一个projection算子
 /// </summary>
 /// <param name="projection">projection算子</param>
 /// <returns></returns>
 int projectionDestroy(Projection* projection)
 {
     if (projection == NULL)
     {
         return GNCDB_PARAMNULL;
     }
     if (projection->fieldNameArray != NULL)
     {
         varArrayListDestroy(&projection->fieldNameArray);
     }
     operatorDestroy(projection->baseinfo);
     my_free(projection);
 
     return GNCDB_SUCCESS;
 }
  /// <summary>
  /// scan算子构造函数
  /// </summary>
  /// <param name="db"></param>
  /// <param name="tableName"></param>
  /// <param name="op"></param>
  /// <param name="tid"></param>
  /// <returns></returns>
 RtreeScan* rtreeScanConstruct(struct GNCDB* db, char* tableName, struct varArrayList* searchvalue, struct Transaction* tx)
 {
     RtreeScan* scan = NULL;
 
     if (tableName == NULL)
     {
         return NULL;
     }
     scan = my_malloc(sizeof(struct RtreeScan));
     if (scan == NULL)
     {
         return NULL;
     }
     scan->baseinfo = operatorConstruct(OperatorType_SCAN);
     if (scan->baseinfo == NULL)
     {
         my_free(scan);
         return NULL;
     }
     scan->cursor = rtreeCursorConstruct(db, searchvalue, tableName, 1, tx);
     if (scan->cursor == NULL)
     {
         my_free(scan);
         return NULL;
     }
     scan->tableName = tableName;
     scan->searchvalue = searchvalue;
     return scan;
 }
 
 int resetRtreeScan(RtreeScan* scan, struct GNCDB* db, struct Transaction* tx)
 {
     rtreeCursorDestroy(&(scan->cursor));
     scan->cursor = rtreeCursorConstruct(db, scan->searchvalue, scan->tableName, 1, tx);
     if (scan->cursor == NULL)
     {
         return GNCDB_BTC_CREATE_FALSE;
     }
     return GNCDB_SUCCESS;
 }
 
 /// <summary>
 /// 判断是否有下一条记录
 /// </summary>
 /// <param name="scan"></param>
 /// <returns></returns>
 bool rtreeHasNextTuple(RtreeScan* scan)
 {
     return rtreeTableHasNextTuple(scan->cursor);
 }
 
 /// <summary>
 /// 获取下一条记录
 /// </summary>
 /// <param name="db"></param>
 /// <param name="scan"></param>
 /// <param name="tid"></param>
 /// <returns></returns>
 Tuple* operatorRtreeScan(struct GNCDB* db, RtreeScan* scan)
 {
    //  /* 获取btreeTable */
    //  int rc = 0;
    //  RtreeTable* rtreeTable = NULL;
    //  RtreeTuple* rtreeTuple = NULL;
    //  char* depTableName = NULL;
    //  BtreeTable* btreeTable = NULL;
    //  BtreePage* btreePage = NULL;
    //  TableSchema* tableSchema = NULL;
 
    //  rc = catalogGetRtreeTable(db->catalog, &rtreeTable, scan->tableName);
    //  if (rc != GNCDB_SUCCESS) {
    //      return NULL;
    //  }
    //  /* TODO?获取所属索引依赖表名函数，建议在R树tableSchema加入字段从而提高效率 */
    //  rc = catalogGetDependentTableName(db, scan->tableName, &depTableName, NULL);
    //  if (rc != GNCDB_SUCCESS) {
    //      return NULL;
    //  }
 
    //  /* 通过游标获取下一个rtreetuple */
    //  rtreeTuple = rtreeTableGetNextTuple(rtreeTable, scan->cursor, db);
    //  if (rtreeTuple == NULL)
    //  {
    //      return NULL;
    //  }
    //  /* 获取btreeTable */
    //  rc = catalogGetTable(db->catalog, &btreeTable, depTableName);
    //  if (rc != GNCDB_SUCCESS) {
    //      return NULL;
    //  }
    //  /* 获取tableSchema */
    //  tableSchema = getTableSchema(db->catalog, depTableName);
    //  if (tableSchema == NULL) {
    //      return NULL;
    //  }
    //  btreePage = btreeTableFindTupleInLeafPage(btreeTable, rtreeTuple->primkey, tableSchema, db, NULL, OP_SEARCH, true, NULL);
    //  if (btreePage == NULL)
    //  {
    //      return NULL;
    //  }
    //  return leafPageFindEntryByKeyvalue(btreePage, rtreeTuple->primkey, tableSchema, depTableName, db->catalog, EQUAL);
    return NULL;
 }
 
 /// <summary>
 /// scan算子销毁函数
 /// </summary>
 /// <param name="scan"></param>
 void RtreeScanDestroy(RtreeScan* scan)
 {
     if (scan == NULL)
     {
         return;
     }
     operatorDestroy(scan->baseinfo);
     if (scan->searchvalue != NULL)
     {
         varArrayListDestroy(&scan->searchvalue);
     }
     if (scan->cursor != NULL)
     {
         rtreeCursorDestroy(&(scan->cursor));
     }
     my_free(scan);
 
     return;
 }