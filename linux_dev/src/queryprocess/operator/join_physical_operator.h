/*
 * @Author: zql <EMAIL>
 * @Date: 2025-02-25 09:31:58
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-26 21:38:04
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/join_physical_operator.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef JOIN_PHYSICAL_OPERATOR_H
#define JOIN_PHYSICAL_OPERATOR_H
#include "exec_tuple.h"
#include "expression.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "vararraylist.h"
typedef struct NestedLoopJoinPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;  // element type:<PhysicalOperator*>
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  HashMap             *colMap;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文

  PhysicalOperator *left;
  PhysicalOperator *right;
  varArrayList     *leftColMeta;
  varArrayList     *rightColMeta;
  varArrayList     *leftJoinFields;   // 左表连接字段，元素：ColMeta*
  varArrayList     *rightJoinFields;  // 右表连接字段，元素：ColMeta*
  varArrayList     *joinOps;          // 与joinFields对应的连接操作符，元素：CompOp
  varArrayList     *leftBlock;        // 左表元组组成的逻辑块
  Record           *leftRecord;       // 左表记录
  Record           *rightRecord;      // 右表记录
  Record           *resRecord;        // 连接结果记录
  Transaction      *trx;              // 事务
  int               blkCap;           // 块容纳元组数量
  int               leftIdx;          // 左表块的下标索引
  int               leftTnum;         // 左表块当前元组数量
  bool              roundDone;        // 右表遍历的一轮是否结束
  varArrayList     *predicates;       // Expression*
} NestedLoopJoinPhysicalOperator;
int     JoinSetColMeta(PhysicalOperator *joinPhysoper);
int     NestedLoopJoinPhysOperInit(NestedLoopJoinPhysicalOperator *bnljoper, PhysicalOperatorType type);
int     NestedLoopJoinPhysOperOpen(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent);
int     NestedLoopJoinPhysOperNext(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent);
int     NestedLoopJoinPhysOperClose(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent);
int     NestedLoopJoinPhysOperRightNext(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent);
int     NestedLoopJoinPhysOperReset(NestedLoopJoinPhysicalOperator *bnljoper);
Record *NestedLoopJoinPhysOperGetCurrentTuple(NestedLoopJoinPhysicalOperator *oper);
bool    NestedLoopJoinPhysOperFilter(NestedLoopJoinPhysicalOperator *oper);
void    NestedLoopJoinPhysOperDestroy(NestedLoopJoinPhysicalOperator *bnljoper);
void    NestedLoopJoinPhysOperPointerDestroy(void *data);

typedef struct HashJoinPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;  // element type:<PhysicalOperator*>
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文

  varArrayList   *leftColMeta;
  varArrayList   *rightColMeta;
  varArrayList   *leftJoinFields;   // 左表连接字段，元素：ColMeta*
  varArrayList   *rightJoinFields;  // 右表连接字段，元素：ColMeta*
  Transaction    *trx;              // 事务
  varArrayList   *eqPredicates;     // 等值连接的谓词
  varArrayList   *otherPredicates;  // 非等值连接的谓词
  varArrayList   *filterOps;        // 过滤的谓词op
  varArrayList   *filterLeftMeta;   // 过滤的左表元组
  varArrayList   *filterRightMeta;  // 过滤的右表元组
  HashMap        *hashTable;        // 哈希表
  Record         *leftRecord;       // 左表记录
  Record         *rightRecord;      // 右表记录
  Record         *resRecord;        // 连接结果记录
  CombinedHashKey combinedKey;      // 组合键
  Entry          *leftBucktIter;    // 左表桶的迭代器
} HashJoinPhysicalOperator;
int     HashJoinPhysOperInit(HashJoinPhysicalOperator *hashJoinPhysOper, PhysicalOperatorType type);
int     HashJoinPhysOperOpen(HashJoinPhysicalOperator *hashJoinPhysOper, SQLStageEvent *sqlEvent);
int     HashJoinPhysOperNext(HashJoinPhysicalOperator *hashJoinPhysOper, SQLStageEvent *sqlEvent);
int     HashJoinPhysOperClose(HashJoinPhysicalOperator *hashJoinPhysOper, SQLStageEvent *sqlEvent);
int     HashJoinPhysOperReset(HashJoinPhysicalOperator *hjoper);
Record *HashJoinPhysOperGetCurrentTuple(HashJoinPhysicalOperator *oper);
int     HJFindJoinKeys(HashJoinPhysicalOperator *oper, Expression *expr);
bool    HashJoinPhysOperFilter(HashJoinPhysicalOperator *oper);
void    HashJoinPhysOperDestroy(HashJoinPhysicalOperator *hashJoinPhysOper);
void    HashJoinPhysOperPointerDestroy(void *data);

typedef struct SortMergeJoinPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  HashMap             *colMap;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文

  PhysicalOperator *left;
  PhysicalOperator *right;
  varArrayList     *leftColMeta;
  varArrayList     *rightColMeta;
  Transaction      *trx;
  bool              isPKSorted;      /* 左右是否已经主键排序 */
  bool              isFullPK;        /* 是否是全主键连接 */
  varArrayList     *pkPredicates;    /* 主键连接条件phy */
  varArrayList     *eqPredicates;    /* 非主键有序时的等值连接条件 */
  varArrayList     *otherPredicates; /* 连接后的非等值连接过滤条件 */
  varArrayList     *leftJoinFields;  /* 连接的左字段，元素：ColMeta* */
  varArrayList     *rightJoinFields; /* 连接的右字段，元素：ColMeta* */
  varArrayList     *filterOps;       /* 过滤的谓词op */
  varArrayList     *filterLeftMeta;  /* 过滤的左表元组 */
  varArrayList     *filterRightMeta; /* 过滤的右表元组 */
  varArrayList     *leftRecs;
  varArrayList     *rightRecs;
  Record           *leftRecord;
  Record           *rightRecord;
  Record           *resRecord;
  int               leftIdx;
  int               rightIdx;
  int               rightSearchIdx;
  int               cmpRes;
} SortMergeJoinPhysicalOperator;
int     SortMergeJoinPhysOperInit(SortMergeJoinPhysicalOperator *sortMergeJoinPhysOper, PhysicalOperatorType type);
int     SortMergeJoinPhysOperOpen(SortMergeJoinPhysicalOperator *sortMergeJoinPhysOper, SQLStageEvent *sqlEvent);
int     SortMergeJoinPhysOperNext(SortMergeJoinPhysicalOperator *sortMergeJoinPhysOper, SQLStageEvent *sqlEvent);
int     SortMergeJoinPhysOperClose(SortMergeJoinPhysicalOperator *sortMergeJoinPhysOper, SQLStageEvent *sqlEvent);
int     SortMergeJoinPhysOperReset(SortMergeJoinPhysicalOperator *smjoper);
Record *SortMergeJoinPhysOperGetCurrentTuple(SortMergeJoinPhysicalOperator *oper);
int     SMJFindJoinKeys(SortMergeJoinPhysicalOperator *oper, Expression *expr);
bool    SortMergeJoinPhysOperFilter(SortMergeJoinPhysicalOperator *oper);
void    SortMergeJoinPhysOperDestroy(SortMergeJoinPhysicalOperator *sortMergeJoinPhysOper);
#endif  // JOIN_PHYSICAL_OPERATOR_H
