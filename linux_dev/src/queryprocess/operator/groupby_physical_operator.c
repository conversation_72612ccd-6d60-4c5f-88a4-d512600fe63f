#include "groupby_physical_operator.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "typedefine.h"
#include "utils.h"
#include "value.h"
#include "vararraylist.h"
#include <assert.h>
extern ColMeta *matchField(FieldExpr *fieldExpr, varArrayList *colMetas);
extern Meta    *matchMeta(Expression *expr, varArrayList *colMetas);

int GroupByPhysOperInit(GroupByPhysicalOperator *groupbyPhysOper, PhysicalOperatorType type)
{
  groupbyPhysOper->type          = type;
  groupbyPhysOper->children      = varArrayListCreate(DISORDER, sizeof(PhysicalOperator *), 0, NULL, NULL);
  groupbyPhysOper->parentTuple   = NULL;
  groupbyPhysOper->isFirst       = true;
  groupbyPhysOper->isNewGroup    = true;
  groupbyPhysOper->isRecordEof   = false;
  groupbyPhysOper->groupbyFields = NULL;
  groupbyPhysOper->preValues     = varArrayListCreate(DISORDER, sizeof(Value *), 0, NULL, valuePointerDestroy);
  groupbyPhysOper->currentTuple  = NULL;
  groupbyPhysOper->aggExprs      = NULL;
  groupbyPhysOper->fieldExprs    = NULL;
  groupbyPhysOper->prevTuple     = NULL;
  return GNCDB_SUCCESS;
}

void GroupByGetParmsValues(GroupByPhysicalOperator *groupbyPhysOper, varArrayList *groupbyFields,
    varArrayList *aggExprs, varArrayList *fieldExprs)
{
  if (groupbyPhysOper == NULL) {
    return;
  }
  if (groupbyPhysOper->groupbyFields != NULL) {
    varArrayListDestroy(&groupbyPhysOper->groupbyFields);
  }
  groupbyPhysOper->groupbyFields = groupbyFields;
  groupbyPhysOper->aggExprs      = aggExprs;
  groupbyPhysOper->fieldExprs    = fieldExprs;

  return;
}

static void setValueFromRecord(Value *result, Record *tuple, ColMeta *col)
{
  int    intVal;
  double doubleVal;
  char  *strVal;
  if (result == NULL || tuple == NULL || col == NULL) {
    return;
  }

  if (col->fieldType == FIELDTYPE_INTEGER) {
    intVal = *(int *)(tuple->data + col->offset);
    valueSetInt(result, intVal);
  } else if (col->fieldType == FIELDTYPE_REAL) {
    doubleVal = *(double *)(tuple->data + col->offset);
    valueSetDouble(result, doubleVal);
  } else if (col->fieldType == FIELDTYPE_VARCHAR) {
    strVal = (char *)my_malloc0(col->len + 1);
    if (strVal == NULL) {
      return;
    }
    memcpy(strVal, tuple->data + col->offset, col->len);
    strVal[col->len] = '\0';
    valueSetString(result, strVal);
    my_free(strVal);

  } else {
    valueSetNull(result);
  }
}

void AggrExprResultsInit(AggrExprResults *aggrResults, Record *tuple, varArrayList *cols)
{
  // int rc = GNCDB_SUCCESS;
  // 1. reset
  aggrResults->count   = 0;
  aggrResults->allNull = true;

  // 2. count(1) count(*) count(1+1)
  if (aggrResults->expr->paramIsConstexpr) {
    // 不能跳过 null 这种情况下可以直接递增 count
    aggrResults->count = 1;
    return;
  }

  // 3. get current value and set result
  // setValueFromRecord(aggrResults->result, tuple, col);
  expressionGetValueDC(aggrResults->expr->param, tuple, cols, aggrResults->result);

  // 4. ignore null
  if (aggrResults->result->attrType != NULLS) {
    aggrResults->count   = 1;
    aggrResults->allNull = false;
  }
  return;
}

void groupTupleDoAggrFirst(Record *childRecord, GroupByPhysicalOperator *groupby)
{
  ColMeta          *col          = NULL;
  varArrayList     *childCols    = NULL;
  int               i            = 0;
  PhysicalOperator *child        = NULL;
  AggrExprResults  *aggrResults  = NULL;
  FieldExprResults *fieldResults = NULL;
  FieldExpr        *fieldExpr    = NULL;
  // AggrFuncExpr     *aggrExpr     = NULL;

  /*1.获取子算子以及对应得col元数据*/
  child = (PhysicalOperator *)varArrayListGetPointer(groupby->children, 0);
  if (child == NULL) {
    return;
  }

  childCols   = child->cols;
  if (childCols == NULL) {
    return;
  }

  /*2.初始化每个聚合字段的聚合结果，就是在遇到一个新的group by分组的时候从childrecord获得初始值的过程*/
  for (i = 0; i < groupby->aggExprs->elementCount; i++) {
    aggrResults = (AggrExprResults *)varArrayListGetPointer(groupby->aggrResults, i);
    // aggrExpr    = (AggrFuncExpr *)varArrayListGetPointer(groupby->aggExprs, i);
    // if (!aggrExpr->paramIsConstexpr) {
    //   col = matchField((FieldExpr *)aggrExpr->param, cols);
    //   if (col == NULL) {
    //     return;
    //   }
    // }
    AggrExprResultsInit(aggrResults, childRecord, childCols);
  }
  /*3.从childRecord中获取非聚合列的值，比如seelct id，count(*) from a;这里的id就是非聚合列*/
  for (i = 0; i < groupby->fieldExprs->elementCount; i++) {
    fieldResults = (FieldExprResults *)varArrayListGetPointer(groupby->fieldResults, i);
    fieldExpr    = (FieldExpr *)varArrayListGetPointer(groupby->fieldExprs, i);
    col          = matchField(fieldExpr, childCols);
    if (col == NULL) {
      return;
    }
    setValueFromRecord(fieldResults->result, childRecord, col);
  }
}

void AggrExprResultsAdvance(AggrExprResults *aggrResults, Record *tuple, varArrayList *cols)
{

  Value *currentValue = NULL;

  /*1. count(1) count(*) count(1+1)*/
  if (aggrResults->expr->paramIsConstexpr) {
    aggrResults->count++;
    return;
  }

  /*2. get current value*/
  currentValue = valueCreate();
  expressionGetValueDC(aggrResults->expr->param, tuple, cols, currentValue);
  // setValueFromRecord(currentValue, tuple, col);

  /*3. ignore null*/
  if (currentValue->attrType == NULLS) {
    valueDestroy(&currentValue);
    return;
  }

  /*4. update result*/
  aggrResults->count++;
  aggrResults->allNull = false;

  /*5. 处理init 的时候拿到的是 null的情况*/
  if (aggrResults->result->attrType == NULLS) {
    valueDestroy(&aggrResults->result);
    aggrResults->result = ptrMove((void **)&currentValue);
    return;
  }
  /*6. do aggr calc*/
  switch (aggrResults->expr->aggrType) {
    case AGG_COUNT: {
    } break;
    case AGG_SUM:
    case AGG_AVG: {
      aggrResults->result = valueAdd(aggrResults->result, currentValue);
    } break;
    case AGG_MAX: {
      if (valueCompare(aggrResults->result, currentValue) < 0) {
        valueDestroy(&aggrResults->result);
        aggrResults->result = ptrMove((void **)&currentValue);
      }
    } break;
    case AGG_MIN: {
      if (valueCompare(aggrResults->result, currentValue) > 0) {
        valueDestroy(&aggrResults->result);
        aggrResults->result = ptrMove((void **)&currentValue);
      }
    } break;
    default: printf("aggr type not supported %d\n", aggrResults->expr->aggrType); break;
  }
  valueDestroy(&currentValue);
}

void AggrExprResultsFinalize(AggrExprResults *aggrResults)
{
  /*主要处理与计算有关得聚合函数如count和avg，其它得不用因为聚合结果已经在result字段*/
  /*1. count(*) count(1) count(1+1) count(id)*/
  if (aggrResults->expr->aggrType == AGG_COUNT) {
    // aggrResults->result->numValue.intValue = aggrResults->count;
    valueSetInt(aggrResults->result, aggrResults->count);
    return;
  }

  /*2. all null*/
  if (aggrResults->allNull) {
    aggrResults->result->attrType = NULLS;
    return;
  }

  /*3. other situation*/
  switch (aggrResults->expr->aggrType) {
    case AGG_COUNT: {
      // aggrResults->result->numValue.intValue = aggrResults->count;
      valueSetInt(aggrResults->result, aggrResults->count);
    } break;

    case AGG_AVG: {
      aggrResults->result = valueDiv(aggrResults->result, aggrResults->count);
    } break;

    default: {
      // do nothing
    } break;
  }
}

void groupTupleDoAggr(GroupByPhysicalOperator *groupby, Record *tuple, varArrayList *cols)
{
  int              i           = 0;
  AggrExprResults *aggrResults = NULL;
  // ColMeta         *col         = NULL;
  /*遍历每一个聚合字段，执行具体的聚合*/
  for (i = 0; i < groupby->aggExprs->elementCount; i++) {
    aggrResults = (AggrExprResults *)varArrayListGetPointer(groupby->aggrResults, i);
    // col = matchField((FieldExpr *)aggrExpr->param, cols);
    // if (col == NULL) {
    //   return;
    // }
    /*具体的聚合，这里的AVG没有在这一步计算而是延迟到返回给上层的时候*/
    AggrExprResultsAdvance(aggrResults, tuple, cols);
  }
}

void groupTupleDoAggrDone(GroupByPhysicalOperator *groupby)
{
  int               i               = 0;
  int               newRecordLength = 0;
  Record           *newRecord       = NULL;
  varArrayList     *cols            = groupby->cols;
  AggrExprResults  *aggrResult      = NULL;
  FieldExprResults *fieldResult     = NULL;
  Value            *value           = NULL;
  ColMeta          *col             = NULL;
  AggrExprResults  *aggrResults     = NULL;
  BYTE             *bitmap          = NULL;
  int               realColNum      = 0;
  // FieldExpr        *fieldExpr       = NULL;
  int realStrLen = 0;

  /*1.每个聚合产生最终的结果*/
  for (i = 0; i < groupby->aggrResults->elementCount; i++) {
    aggrResults = (AggrExprResults *)varArrayListGetPointer(groupby->aggrResults, i);
    AggrExprResultsFinalize(aggrResults);
  }

  /*2.根据aggResults和fieldResults生成新的Record*/
  getRecordLength(groupby->cols, &newRecordLength);
  newRecord = RecordCreateWithoutData(newRecordLength);
  // TODO: 处理null的情形

  /*3.处理null的情形,如果结果列中有空值则将对应位置的bitmap置0，同时把聚合结果复制到newrecord*/
  realColNum = cols->elementCount - 1;
  for (i = 0; i < groupby->aggExprs->elementCount; i++) {
    aggrResult = (AggrExprResults *)varArrayListGetPointer(groupby->aggrResults, i);
    col        = (ColMeta *)varArrayListGetPointer(cols, i + 1);  // 注意第0列是bitmap列，所以从1开始取
    value      = aggrResult->result;

    bitmap = newRecord->data + col->bitmapOffset;
    if (value->attrType == NULLS) {
      leafTupleSetBitMap(bitmap, col->index, 0, realColNum);
    } else {
      memcpy(newRecord->data + col->offset, valueGetPointer(value), col->len);
      leafTupleSetBitMap(bitmap, col->index, 1, realColNum);
    }
  }
  /*4.处理非聚合列的情况，这里的非聚合列的colmeta在聚合列的colmeta的后面*/
  for (i = 0; i < groupby->fieldExprs->elementCount; i++) {
    fieldResult = (FieldExprResults *)varArrayListGetPointer(groupby->fieldResults, i);
    col         = (ColMeta *)varArrayListGetPointer(cols, 1 + i + groupby->aggExprs->elementCount);
    value       = fieldResult->result;
    bitmap      = newRecord->data + col->bitmapOffset;
    if (value->attrType == NULLS) {
      leafTupleSetBitMap(bitmap, col->index, 0, realColNum);
    } else {
      realStrLen = value->length < col->len ? value->length : col->len;  // value中的string长度可能小于col->len
      memcpy(newRecord->data + col->offset, valueGetPointer(value), realStrLen);
      leafTupleSetBitMap(bitmap, col->index, 1, realColNum);
    }
  }
  if (groupby->currentTuple != NULL) {
    RecordDestroy(&groupby->currentTuple);
  }
  groupby->currentTuple = newRecord;

  /*5.聚合结果已经复制到newRecord，此时可以reset聚合字段和非聚合字段*/
  for (i = 0; i < groupby->aggExprs->elementCount; i++) {
    aggrResults          = (AggrExprResults *)varArrayListGetPointer(groupby->aggrResults, i);
    aggrResults->count   = 0;
    aggrResults->allNull = true;
    valueReset(aggrResults->result);
    if (aggrResults->expr->paramIsConstexpr) {
      valueSetInt(aggrResults->result, 0);
    } else {
      valueSetNull(aggrResults->result);
    }
  }

  for (i = 0; i < groupby->fieldExprs->elementCount; i++) {
    fieldResult = (FieldExprResults *)varArrayListGetPointer(groupby->fieldResults, i);
    valueReset(fieldResult->result);
    valueSetNull(fieldResult->result);
  }
}

int GroupByPhysOperOpen(GroupByPhysicalOperator *groupbyPhysOper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  // int               i            = 0;
  PhysicalOperator *child = NULL;
  // ColMeta          *col          = NULL;
  // varArrayList     *cols         = NULL;
  // Value            *value        = NULL;
  // AggrExprResults  *aggrResults  = NULL;
  // FieldExprResults *fieldResults = NULL;

  /*1.参数非空检查，分组聚合算子有且只有一个子算子*/
  if (groupbyPhysOper == NULL || sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (groupbyPhysOper->children->elementCount != 1) {
    return GNCDB_INTERNAL;
  }
  child = (PhysicalOperator *)varArrayListGetPointer(groupbyPhysOper->children, 0);
  rc    = PhysicalOperatorOpen(child, sqlEvent);
  if (rc == GNCDB_NEXT_EOF) {
    rc = GNCDB_SUCCESS;
  }
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_INTERNAL;
  }
  groupbyPhysOper->isRecordEof = false;
  groupbyPhysOper->isFirst     = true;
  groupbyPhysOper->isNewGroup  = true;

  return rc;
}

/**
 * @brief 逐行遍历结果集，根据分组条件聚合数据，并在遇到新的分组时返回聚合结果
 *
 * @param sqlEvent
 * @return int
 */
int GroupByPhysOperNext(GroupByPhysicalOperator *groupbyPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc        = GNCDB_SUCCESS;
  PhysicalOperator *child     = NULL;
  FieldExpr        *fieldExpr = NULL;
  Value            *value1    = NULL;
  Value            *value2    = NULL;
  Value            *preValue  = NULL;
  int               i         = 0;
  varArrayList     *cols      = NULL;
  int               newRecordLength;
  // Record           *record = NULL;

  /*1.如果已经到达记录末尾，返回结束标志 */
  if (groupbyPhysOper->isRecordEof) {
    return GNCDB_NEXT_EOF;
  }

  /*2.获取子算子以及colmeta*/
  child = (PhysicalOperator *)varArrayListGetPointer(groupbyPhysOper->children, 0);
  cols  = getColMeta(child);
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }

  /*3.获取new Record的长度*/
  rc = getRecordLength(groupbyPhysOper->cols, &newRecordLength);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /*4.第一次遍历，获取第一行数据，自由第一次调用的时候会执行 */
  if (groupbyPhysOper->isFirst) {
    /*4.1从子算子获取一个元组*/
    rc = PhysicalOperatorNext(child, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      if (rc == GNCDB_NEXT_EOF) {
        groupbyPhysOper->isRecordEof = true;
        groupTupleDoAggrDone(groupbyPhysOper);
        groupbyPhysOper->isFirst = false;
        return GNCDB_SUCCESS;
      }
      return rc;
    }
    groupbyPhysOper->prevTuple  = GetCurrentTuple(child);
    groupbyPhysOper->isFirst    = false;
    groupbyPhysOper->isNewGroup = true;

    /*4.2初始化 pre_values,从当前元组获取groupby段的值 */
    for (i = 0; i < groupbyPhysOper->groupbyFields->elementCount; i++) {
      value1    = valueCreate();
      fieldExpr = (FieldExpr *)varArrayListGetPointer(groupbyPhysOper->groupbyFields, i);
      expressionGetValueDC((Expression *)fieldExpr, groupbyPhysOper->prevTuple, cols, value1);
      varArrayListAddPointer(groupbyPhysOper->preValues, PTR_MOVE(&value1));
    }
  }

  /*5.聚合，从子算子获取一条记录若是新分组则返回当前组的聚合结果，若不是则继续当前组的聚合*/
  while (true) {
    /*5.1如果是新分组，执行初始化聚合操作，就是加入遇到了新的group by数据进行的初始化操作*/
    if (groupbyPhysOper->isNewGroup) {
      groupTupleDoAggrFirst(groupbyPhysOper->prevTuple, groupbyPhysOper);
      groupbyPhysOper->isNewGroup = false;
    }

    /*5.2获取下一行数据 */
    rc = PhysicalOperatorNext(child, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      break;
    }
    groupbyPhysOper->prevTuple = GetCurrentTuple(child);
    assert(groupbyPhysOper->prevTuple != NULL);  /*正常情况下，currentTuple不应该为空*/

    /*5.3判断当前元组是否属于新分组 ,主要的操作是从当前元组提取聚合段如果有一个字段不同则是新的聚合段*/
    for (i = 0; i < groupbyPhysOper->groupbyFields->elementCount; i++) {
      value2    = valueCreate();
      fieldExpr = (FieldExpr *)varArrayListGetPointer(groupbyPhysOper->groupbyFields, i);
      /*5.3.1获取聚合段的值*/
      expressionGetValueDC((Expression *)fieldExpr, groupbyPhysOper->prevTuple, cols, value2);
      /*5.3.2与当前组的聚合字段得对应位置得值进行比较，不同则是下一组，同则下一段*/
      preValue = (Value *)varArrayListGetPointer(groupbyPhysOper->preValues, i);
      if (valueCompare(value2, preValue) != 0) {
        varArrayListSetByIndexPointer(groupbyPhysOper->preValues, i, PTR_MOVE((void **)&value2));
        valueDestroy(&preValue);
        groupbyPhysOper->isNewGroup = true;
      } else {
        valueDestroy(&value2);
      }
    }

    /*5.4如果是新分组，返回当前行 */
    if (groupbyPhysOper->isNewGroup) {
      groupTupleDoAggrDone(groupbyPhysOper);
      return rc;
    }

    /*5.5否则继续执行聚合操作并更新结果 */
    groupTupleDoAggr(groupbyPhysOper, groupbyPhysOper->prevTuple, cols);
  }

  /*6.如果到达EOF，完成最后的聚合操作 */
  if (rc == GNCDB_NEXT_EOF) {
    groupTupleDoAggrDone(groupbyPhysOper);
    groupbyPhysOper->isRecordEof = true;
    return GNCDB_SUCCESS;
  }
  return rc;
}

int GroupByPhysOperClose(GroupByPhysicalOperator *groupbyPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = (PhysicalOperator *)varArrayListGetPointer(groupbyPhysOper->children, 0);
  rc                      = PhysicalOperatorClose(child, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return rc;
}

int GroupBySetColMeta(GroupByPhysicalOperator *groupbyPhysOper)
{
  int               i            = 0;
  int               rc           = GNCDB_SUCCESS;
  varArrayList     *cols         = NULL;
  PhysicalOperator *child        = NULL;
  Meta             *tmpcol       = NULL;
  Meta             *newcol       = NULL;
  ColMeta          *colMeta      = NULL;  // 临时使用的列元信息
  Expression       *expr         = NULL;
  AggrFuncExpr     *aggr         = NULL;
  int               offset       = 0;
  ColMeta          *col          = NULL;
  Value            *value        = NULL;
  AggrExprResults  *aggrResults  = NULL;
  FieldExprResults *fieldResults = NULL;

  if (groupbyPhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  child = (PhysicalOperator *)varArrayListGetPointer(groupbyPhysOper->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }

  cols   = child->cols;
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }

  groupbyPhysOper->cols = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, MetaPtrDestroy);

  colMeta = (ColMeta *)my_malloc0(sizeof(ColMeta));
  if (colMeta == NULL) {
    return GNCDB_MEM;
  }
  colMeta->owned     = true;
  colMeta->type      = NormalField;
  colMeta->tableName = NULL;
  colMeta->name      = my_strdup("__bitMap");
  colMeta->len = GET_BITMAP_LENGTH(groupbyPhysOper->aggExprs->elementCount + groupbyPhysOper->fieldExprs->elementCount);
  colMeta->offset   = offset;
  colMeta->isBitMap = true;
  colMeta->index    = (-1);  // 设置索引，便于后续处理bitmap
  offset += colMeta->len;

  varArrayListAddPointer(groupbyPhysOper->cols, colMeta);  // 添加bitmap列元信息
  for (i = 0; i < groupbyPhysOper->aggExprs->elementCount; i++) {
    aggr = (AggrFuncExpr *)varArrayListGetPointer(groupbyPhysOper->aggExprs, i);
    // if (aggrExprCheckIsConstexpr(aggr)) {
    //   newcol = my_new0(ColMeta);
    //   if (newcol == NULL) {
    //     return GNCDB_INTERNAL;
    //   }
    //   if (aggr->name) {
    //     newcol->name = my_strdup(aggr->name);
    //     if (newcol->name == NULL) {
    //       ColMetaDestroy(&newcol);
    //       return GNCDB_MEM;
    //     }
    //   }
    //   newcol->fieldType = (int)expressionGetValueType((Expression *)aggr);
    //   newcol->len       = expressionGetValueLen((Expression *)aggr, cols);
    //   newcol->offset    = offset;
    //   newcol->aggrType  = aggr->aggrType;

    // } else {
    //   // 查看聚合函数的参数是否在子节点的结果集中
    //   col = matchField((FieldExpr *)aggr->param, cols);
    //   if (col == NULL) {
    //     return GNCDB_INTERNAL;
    //   }
    //   newcol = my_new0(ColMeta);
    //   if (newcol == NULL) {
    //     return GNCDB_INTERNAL;
    //   }
    //   newcol->name = my_strdup(aggr->name);
    //   if (newcol->name == NULL) {
    //     ColMetaDestroy(&newcol);
    //     return GNCDB_MEM;
    //   }
    //   newcol->offset    = offset;  // reset offset
    //   newcol->aggrType  = aggr->aggrType;
    //   newcol->fieldType = (int)expressionGetValueType((Expression *)aggr);
    //   if (newcol->fieldType == FIELDTYPE_INTEGER) {
    //     newcol->len = INT_SIZE;
    //   } else if (newcol->fieldType == FIELDTYPE_REAL) {
    //     newcol->len = DOUBLE_SIZE;
    //   } else if (newcol->fieldType == FIELDTYPE_VARCHAR) {
    //     newcol->len = col->len;
    //   }
    // }
    if (!aggr->param) {
      return GNCDB_INTERNAL;
    }
    // 聚合函数不允许嵌套，所以如果是复合类型，返回的应该是null
    tmpcol = matchMeta((Expression *)aggr->param, cols);
    tmpcol = MetaDeepCopy(tmpcol);
    if (tmpcol == NULL) {  // 如果是常量表达式，则不需要匹配
      if (!aggr->paramIsConstexpr) {
        newcol = my_new0(CompositeMeta);
        if (newcol == NULL) {
          MetaDestroy(&tmpcol);
          return GNCDB_MEM;
        }
        ((CompositeMeta *)newcol)->expr = exprDeepCopy((Expression *)aggr);
        newcol->type                    = CompositeField;
      } else {
        newcol = my_new0(ColMeta);
        if (newcol == NULL) {
          return GNCDB_MEM;
        }
        newcol->owned = true;
        ((ColMeta *)newcol)->name = aggr->name ? my_strdup(aggr->name) : NULL;
        // ((ColMeta *)newcol)->tableName = ((ColMeta *)tmpcol)->tableName ? my_strdup(((ColMeta *)tmpcol)->tableName) :
        // NULL;
        ((ColMeta *)newcol)->aggrType = aggr->aggrType;
      }
    } else {
      newcol = my_new0(ColMeta);
      if (newcol == NULL) {
        MetaDestroy(&tmpcol);
        return GNCDB_MEM;
      }
      newcol->owned = true;
      ((ColMeta *)newcol)->name      = aggr->name ? my_strdup(aggr->name) : NULL;
      ((ColMeta *)newcol)->tableName = ((ColMeta *)tmpcol)->tableName ? my_strdup(((ColMeta *)tmpcol)->tableName) : NULL;
      ((ColMeta *)newcol)->aggrType  = aggr->aggrType;
      newcol->type                   = NormalField;
    }
    MetaDestroy(&tmpcol);
    newcol->len          = expressionGetValueLen((Expression *)aggr, cols);
    newcol->fieldType    = (int)expressionGetValueType((Expression *)aggr);
    newcol->offset       = offset;  // reset offset
    newcol->index        = (i);     // 设置索引，便于后续处理bitmap和聚合函数的结果
    newcol->bitmapOffset = 0;
    offset += newcol->len;
    rc = varArrayListAddPointer(groupbyPhysOper->cols, newcol);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  for (i = 0; i < groupbyPhysOper->fieldExprs->elementCount; i++) {
    expr = (Expression *)varArrayListGetPointer(groupbyPhysOper->fieldExprs, i);
    // col  = matchField((FieldExpr *)expr, cols);
    // if (col == NULL) {
    //   return GNCDB_INTERNAL;
    // }
    // newcol = MetaDeepCopy(col);
    // if (newcol == NULL) {
    //   return GNCDB_INTERNAL;
    // }
    // newcol->offset = offset;  // reset offset
    // offset += newcol->len;
    // rc = varArrayListAddPointer(groupbyPhysOper->cols, newcol);
    // if (rc != GNCDB_SUCCESS) {
    //   return rc;
    // }
    newcol = matchMeta(expr, cols);
    newcol = MetaDeepCopy(newcol);
    if (newcol == NULL) {  // fieldExprs里都是字段，不应该有找不到的情况
      return GNCDB_INTERNAL;
    }
    // if (newcol->type == CompositeField) {
    //   newcol->len = expressionGetValueLen(expr, cols);
    // }
    newcol->fieldType = (int)expressionGetValueType((Expression *)expr);
    newcol->offset    = offset;                                         // reset offset
    newcol->index     = (i + groupbyPhysOper->aggExprs->elementCount);  // 设置索引，便于后续处理bitmap和聚合函数的结果
    newcol->bitmapOffset = 0;
    offset += newcol->len;
    rc = varArrayListAddPointer(groupbyPhysOper->cols, newcol);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  groupbyPhysOper->aggrResults = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AggrExprResultsPointerDestroy);
  if (groupbyPhysOper->aggrResults == NULL) {
    return GNCDB_MEM;
  }
  groupbyPhysOper->fieldResults = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, FieldExprResultsPointerDestroy);
  if (groupbyPhysOper->fieldResults == NULL) {
    return GNCDB_MEM;
  }

  cols = groupbyPhysOper->cols;
  for (i = 0; i < groupbyPhysOper->aggExprs->elementCount; i++) {
    col   = (ColMeta *)varArrayListGetPointer(cols, i);
    value = valueCreate();
    if (value == NULL) {
      return GNCDB_MEM;
    }
    if (col->aggrType == AGG_COUNT) {  // 如果是count函数，初始化为0
      valueSetInt(value, 0);
    } else {
      valueSetNull(value);  // 其他函数初始化为null
    }
    aggrResults = my_new0(AggrExprResults);
    if (aggrResults == NULL) {
      valueDestroy(&value);
      return GNCDB_MEM;
    }
    aggrResults->result  = value;
    aggrResults->allNull = true;
    aggrResults->count   = 0;
    varArrayListAddPointer(groupbyPhysOper->aggrResults, aggrResults);
    aggrResults->expr = (AggrFuncExpr *)varArrayListGetPointer(groupbyPhysOper->aggExprs, i);
  }

  for (i = 0; i < groupbyPhysOper->fieldExprs->elementCount; i++) {
    value = valueCreate();
    if (value == NULL) {
      return GNCDB_MEM;
    }
    valueSetNull(value);
    fieldResults = my_new0(FieldExprResults);
    if (fieldResults == NULL) {
      valueDestroy(&value);
      return GNCDB_MEM;
    }
    fieldResults->result = value;
    varArrayListAddPointer(groupbyPhysOper->fieldResults, fieldResults);
  }

  return GNCDB_SUCCESS;
}

Record *GroupByPhysOperGetCurrentTuple(GroupByPhysicalOperator *groupbyPhysOper)
{
  // return (Record *)PTR_MOVE((void **)&groupbyPhysOper->currentTuple);
  return groupbyPhysOper->currentTuple; 
}

void GroupByPhysOperDestroy(GroupByPhysicalOperator *groupbyPhysOper)
{
  if (groupbyPhysOper == NULL) {
    return;
  }

  if (groupbyPhysOper->children != NULL) {
    varArrayListDestroy(&groupbyPhysOper->children);
  }

  if (groupbyPhysOper->cols != NULL) {
    varArrayListDestroy(&groupbyPhysOper->cols);
  }

  if (groupbyPhysOper->groupbyFields != NULL) {
    varArrayListDestroy(&groupbyPhysOper->groupbyFields);
  }

  if (groupbyPhysOper->preValues != NULL) {
    varArrayListDestroy(&groupbyPhysOper->preValues);
  }

  if (groupbyPhysOper->aggExprs != NULL) {
    varArrayListDestroy(&groupbyPhysOper->aggExprs);
  }

  if (groupbyPhysOper->fieldExprs != NULL) {
    varArrayListDestroy(&groupbyPhysOper->fieldExprs);
  }

  if (groupbyPhysOper->currentTuple != NULL) {
    RecordDestroy(&groupbyPhysOper->currentTuple); 
  }

  if (groupbyPhysOper->aggrResults != NULL) {
    varArrayListDestroy(&groupbyPhysOper->aggrResults);
  }

  if (groupbyPhysOper->fieldResults != NULL) {
    varArrayListDestroy(&groupbyPhysOper->fieldResults);
  }
  my_free(groupbyPhysOper);
}
