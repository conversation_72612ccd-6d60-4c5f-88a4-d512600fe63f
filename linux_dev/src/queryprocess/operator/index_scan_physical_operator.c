/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-23 21:00:59
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-18 21:25:50
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/index_scan_physical_operator.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "index_scan_physical_operator.h"
#include "physical_operator.h"
#include "btreecursor.h"
#include "catalog.h"
#include "condvariable.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "select_stmt.h"
#include "sql_event.h"
#include "hash.h"
#include "typedefine.h"
#include "value.h"
#include "pagepool.h"
#include "vararraylist.h"
#include <stdbool.h>
#include <string.h>

int IndexScanPhysOperFilter(IndexScanPhysicalOperator *indexScanPhysOper, bool *res);

int btreeCursorSTConstruct(BtreeCursor *btreeCursor, struct varArrayList *startKeyValue)
{
  int          rc          = 0;
  BtreeTable  *table       = NULL;
  TableSchema *tableSchema = NULL;
  BtreePage   *page        = NULL;
  Transaction *tx          = NULL;
  GNCDB       *db          = btreeCursor->db;
  int          tupleIndex  = 0;
  if (btreeCursor == NULL || btreeCursor->db == NULL) {
    return GNCDB_PARAMNULL;
  }
  rc = catalogGetTable(btreeCursor->db->catalog, &table, btreeCursor->page->tableName);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  tableSchema = getTableSchema(btreeCursor->db->catalog, btreeCursor->page->tableName);
  if (tableSchema == NULL) {
    return GNCDB_PARAMNULL;
  }

  tx = btreeCursor->tx;

  LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", table->tableName);
  ReadLock(&table->rwlock_t);
  LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", table->tableName);
  // clock_gettime(CLOCK_REALTIME, &st);

  /* 给定起始位置, 调用函数使cursor初始化至起始位置 */
  page = btreeTableFindTupleInLeafPage(table, startKeyValue, tableSchema, db, tx, OP_SEARCH, true, NULL);
  if (page == NULL) {
    return GNCDB_NOT_FOUND;
  }
  btreeCursor->page = page;
  tupleIndex        = leafPageFindEntryIndexByKeyvalue(
      page, startKeyValue, tableSchema, btreeCursor->page->tableName, db->catalog, GREATER_THAN_OR_EQUAL, table);
  if (tupleIndex < 0) {
    btreeCursor->currentTupleIndex = page->entryNum;
  } else {
    btreeCursor->currentTupleIndex = tupleIndex;
  }

  LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", page->page.id);
  ReadUnLock(&page->page.rwlock_t);
  LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", page->page.id);
  btreeCursor->currentLeafPageId = page->page.id;

  // /* 解除当前页的pin */
  // rc = setPageStatusPinDown(db->pagePool, page->page.id, NULL);
  // if (rc != GNCDB_SUCCESS) {
  //   return GNCDB_INTERNAL;
  // }

  // /* 释放锁 */
  // rc = lockManagerReleaseLock(db->transactionManager->lockManager, tx, page->page.id, SHARD);
  // if (rc != GNCDB_SUCCESS) {
  //   return GNCDB_INTERNAL;
  // }

  return GNCDB_SUCCESS;
}

int IndexScanSetColMeta(IndexScanPhysicalOperator *indexScanPhysOper)
{
  if (indexScanPhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  if (indexScanPhysOper->tableSchema == NULL) {
    return GNCDB_PARAM_INVALID;
  }

  if (indexScanPhysOper->cols == NULL) {
    indexScanPhysOper->cols = indexScanPhysOper->tableSchema->tablecolMetas;
  }
  return GNCDB_SUCCESS;
}

int setCursor(IndexScanPhysicalOperator *indexScanPhysOper, SQLStageEvent *sqlEvent)
{
  int rc = 0;
  // 如果没有就构造cursor
  if (indexScanPhysOper->cursor == NULL) {
    indexScanPhysOper->cursor = btreeCursorConstruct(
        indexScanPhysOper->table->tableName, sqlEvent->db, indexScanPhysOper->startKeyValue, sqlEvent->txn);
    if (indexScanPhysOper->cursor == NULL) {
      return GNCDB_BTC_CREATE_FALSE;
    }
    return GNCDB_SUCCESS;
  }

  // 有就修改
  rc = btreeCursorSTConstruct(indexScanPhysOper->cursor, indexScanPhysOper->startKeyValue);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

void IndexScanPhysOperInit(IndexScanPhysicalOperator *indexScanPhysOper)
{
  indexScanPhysOper->type        = PO_INDEX_SCAN;
  indexScanPhysOper->children    = varArrayListCreate(0, BYTES_POINTER, 0, NULL, NULL);
  indexScanPhysOper->record      = NULL;
  indexScanPhysOper->parentTuple = NULL;
  indexScanPhysOper->predicates  = NULL;
}

int IndexScanPhysOperOpen(IndexScanPhysicalOperator *oper, SQLStageEvent *sqlEvent)
{
  int             rc            = GNCDB_SUCCESS;
  ComparisonExpr *indexCompExpr = NULL;
  ValueExpr      *valueExpr     = NULL;
  int             hashValue     = 0;

  oper->record = RecordCreateWithoutData(oper->table->leafRecordLength);

  indexCompExpr = varArrayListGetPointer(oper->indexExprs, 0);
  valueExpr     = (ValueExpr *)indexCompExpr->right;

  //* 1.收集索引列信息
  // todo 目前仅支持单列哈希索引
  oper->indexCol = varArrayListGetPointer(oper->index->index_columns, 0);

  //* 2.计算hashcode
  hashValue = oper->indexCol->fieldType == FIELDTYPE_VARCHAR
                  ? getHashValue(valueExpr->value->strValue, oper->indexCol->fieldType)
                  : getHashValue(&(valueExpr->value->numValue), oper->indexCol->fieldType);

  //* 3.得到pkValueList
  oper->pkValues   = varArrayListCreate(0, BYTES_POINTER, 0, NULL, NULL);
  oper->pkValueIdx = 0;
  rc               = hashIndexSelect(oper->index, hashValue, oper->pkValues, sqlEvent->db, sqlEvent->txn);
  return rc;
}

int IndexScanPhysOperNextCurrent(IndexScanPhysicalOperator *indexScanPhysOper, SQLStageEvent *sqlEvent)
{
  int           rc           = GNCDB_SUCCESS;
  varArrayList *pkValueArray = NULL;
  BYTE         *tmpRecData   = NULL;
  int           iVal         = 0;
  double        dval         = 0;
  char         *sVal         = NULL;
  bool          equal        = false;

  // 通过pkValue回表，直到找到一个对应的元组
  while (indexScanPhysOper->pkValueIdx < indexScanPhysOper->pkValues->elementCount) {

    //* 由于是相等条件，直接设置start、end为同一个值
    pkValueArray = varArrayListGetPointer(indexScanPhysOper->pkValues, indexScanPhysOper->pkValueIdx);
    indexScanPhysOper->startKeyValue = pkValueArray;
    rc                               = setCursor(indexScanPhysOper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    //? 按理来说，这里是完全主键匹配查找应该只会找到最多一个元组
    if (btreeTableHasNextTuple(indexScanPhysOper->cursor)) {
      tmpRecData = btreeTableGetNextRecord(indexScanPhysOper->table, indexScanPhysOper->cursor, sqlEvent->db);
      switch (indexScanPhysOper->indexCol->fieldType) {
        case FIELDTYPE_INTEGER:
          iVal = *(int *)(tmpRecData + indexScanPhysOper->indexCol->offset);
          if (iVal == valueGetInt(indexScanPhysOper->indexVal)) {
            equal = true;
          }
          break;
        case FIELDTYPE_REAL:
          dval = *(double *)(tmpRecData + indexScanPhysOper->indexCol->offset);
          if (dval == valueGetDouble(indexScanPhysOper->indexVal)) {
            equal = true;
          }
          break;
        case FIELDTYPE_VARCHAR:
          sVal = (char *)(tmpRecData + indexScanPhysOper->indexCol->offset);
          if (strcmp(sVal, indexScanPhysOper->indexVal->strValue) == 0) {
            equal = true;
            break;
            default: indexScanPhysOper->pkValueIdx++; break;
          }
      }
    }
    // 下一次回表前要重置start、end
    varArrayListClear(indexScanPhysOper->startKeyValue);
    indexScanPhysOper->pkValueIdx++;

    if (equal) {
      RecordFillData(indexScanPhysOper->record, tmpRecData);
      return GNCDB_SUCCESS;
    }
  }
  return GNCDB_NEXT_EOF;
}

int IndexScanPhysOperNext(IndexScanPhysicalOperator *indexScanPhysOper, SQLStageEvent *sqlEvent)
{
  int  rc           = GNCDB_SUCCESS;
  bool filterResult = true;

  do {  // 这个while循环主要实现哈希索引的后过滤，将不是索引连接的条件放在这里进行过滤
    rc = IndexScanPhysOperNextCurrent(indexScanPhysOper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (indexScanPhysOper->predicates != NULL && indexScanPhysOper->predicates->elementCount != 0) {
      rc = IndexScanPhysOperFilter(indexScanPhysOper, &filterResult);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  } while (!filterResult);
  return rc;
}

int IndexScanPhysOperReset(IndexScanPhysicalOperator *indexScanPhysOper)
{
  indexScanPhysOper->pkValueIdx = 0;
  return GNCDB_SUCCESS;
}

int IndexScanPhysOperClose(IndexScanPhysicalOperator *indexScanPhysOper, SQLStageEvent *sqlEvent)
{
  if (indexScanPhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  btreeCursorDestroy(&indexScanPhysOper->cursor);
  RecordDestroy(&indexScanPhysOper->record);
  return GNCDB_SUCCESS;
}

int IndexScanPhysOperFilter(IndexScanPhysicalOperator *indexScanPhysOper, bool *res)
{
  int         rc = GNCDB_SUCCESS;
  Value       value;
  int         i    = 0;
  Expression *expr = NULL;

  if (indexScanPhysOper == NULL) {
    return GNCDB_PARAM_INVALID;
  }
  for (i = 0; i < indexScanPhysOper->predicates->elementCount; i++) {
    expr = (Expression *)varArrayListGetPointer(indexScanPhysOper->predicates, i);
    rc   = expressionGetValueNCP(expr, indexScanPhysOper->record, indexScanPhysOper->cols, &value);
    // rc   = expressionGetValue(expr, (AbstractTuple *)tp, &value);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (!value.numValue.boolValue) {
      *res = false;
      return rc;
    }
  }
  *res = true;
  return rc;
}

Record *IndexScanPhysOperGetCurrentTuple(IndexScanPhysicalOperator *indexScanPhysOper)
{
  return indexScanPhysOper->record;
}

void IndexScanPhysOperDestroy(IndexScanPhysicalOperator *indexScanPhysOper)
{
  Expression   *expr        = NULL;
  varArrayList *pkValueList = NULL;
  if (indexScanPhysOper == NULL) {
    return;
  }
  if (indexScanPhysOper->children != NULL) {
    varArrayListDestroy(&indexScanPhysOper->children);
  }
  if (indexScanPhysOper->cursor != NULL) {
    btreeCursorDestroy(&(indexScanPhysOper->cursor));
  }
  if (indexScanPhysOper->pkValues != NULL) {
    for (int i = 0; i < indexScanPhysOper->pkValues->elementCount; i++) {
      pkValueList = (varArrayList *)varArrayListGetPointer(indexScanPhysOper->pkValues, i);
      varArrayListDestroy(&pkValueList);
    }
    varArrayListDestroy(&(indexScanPhysOper->pkValues));
  }
  if (indexScanPhysOper->indexExprs != NULL) {
    for (int i = 0; i < indexScanPhysOper->indexExprs->elementCount; i++) {
      expr = (Expression *)varArrayListGetPointer(indexScanPhysOper->indexExprs, i);
      exprDestroy(expr);
    }
    varArrayListDestroy(&(indexScanPhysOper->indexExprs));
  }
  if (indexScanPhysOper->predicates != NULL) {
    for (int i = 0; i < indexScanPhysOper->predicates->elementCount; i++) {
      expr = (Expression *)varArrayListGetPointer(indexScanPhysOper->predicates, i);
      exprDestroy(expr);
    }
    varArrayListDestroy(&(indexScanPhysOper->predicates));
  }
  my_free(indexScanPhysOper);
}

void IndexScanPhysOperPointerDestroy(void *data)
{
  IndexScanPhysicalOperator **indexScanPhysOper = (IndexScanPhysicalOperator **)data;
  if (indexScanPhysOper == NULL || *indexScanPhysOper == NULL) {
    return;
  }

  IndexScanPhysOperDestroy(*indexScanPhysOper);
  *indexScanPhysOper = NULL;
}