#include "insert_logical_operator.h"
#include "vararraylist.h"
void InsertLogiOperDestroy(InsertLogicalOperator *insertLogiOper)
{
  if (insertLogiOper->children != NULL) {
    varArrayListDestroy(&insertLogiOper->children);
  }
  if (insertLogiOper->expressions != NULL) {
    varArrayListDestroy(&insertLogiOper->expressions);
  }
  if (insertLogiOper->valuelists != NULL) {
    varArrayListDestroy(&insertLogiOper->valuelists);
  }

  my_free(insertLogiOper);
}

void InsertLogiOperPointerDestroy(void *data)
{
  InsertLogicalOperator **insertLogiOper = (InsertLogicalOperator **)data;
  if (insertLogiOper == NULL || *insertLogiOper == NULL) {
    return;
  }
  InsertLogiOperDestroy(*insertLogiOper);
  *insertLogiOper = NULL;
}
