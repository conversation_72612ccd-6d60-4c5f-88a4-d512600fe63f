#ifndef TABLE_SCAN_PHYSICAL_OPERATOR_H
#define TABLE_SCAN_PHYSICAL_OPERATOR_H
#include "catalog.h"
#include "exec_tuple.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "tuple.h"
#include "vararraylist.h"
typedef struct TableScanPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文
  Transaction         *txn;
  BtreeTable          *table;
  varArrayList        *startKeyValue;
  BtreeCursor         *cursor;
  varArrayList        *predicates;
  Record              *record;
  TableSchema         *tableSchema;
  varArrayList        *complexCols;
  BtreeCursor         *backupCursor;  //* 初次创建cursor时备份，用以reset使用
  bool isNoNeddScan; /* 无需扫描的情况，在该种情况下已经通过主键索引提前定位并判断表中不存在满足过滤条件的record */

} TableScanPhysicalOperator;
TableScanPhysicalOperator *TableScanPhysOperCreate();

void              TableScanPhysicalOperatorInit(TableScanPhysicalOperator *tableScanPhysOper);
int               TableScanPhysOperOpen(TableScanPhysicalOperator *tableScanPhysOper, SQLStageEvent *sqlEvent);
int               TableScanPhysOperNext(TableScanPhysicalOperator *tableScanPhysOper, SQLStageEvent *sqlEvent);
int               TableScanPhysOperClose(TableScanPhysicalOperator *tableScanPhysOper, SQLStageEvent *sqlEvent);
int               TableScanPhysOperReset(TableScanPhysicalOperator *tableScanPhysOper);
int               ScanSetColMeta(TableScanPhysicalOperator *tableScanPhysOper);
int               TableScanPhysOperFilter(TableScanPhysicalOperator *tableScanPhysOper, Record *tuple, bool *result);
BtreeCursor      *TableScanPhysOperGetScanCursor(TableScanPhysicalOperator *tableScanPhysOper);
Record           *TableScanPhysOperGetCurrentTuple(TableScanPhysicalOperator *tableScanPhysOper);
void              TableScanPhysOperDestroy(TableScanPhysicalOperator *tableScanPhysOper);
void              TableScanPhysOperPointerDestroy(void *data);
varArrayList     *TableScanPhysOperGetCols(TableScanPhysicalOperator *tableScanPhysOper);
int               TableScanPhysOperGetTupleLength(TableScanPhysicalOperator *tableScanPhysOper);
struct BtreePage *TableScanPhysOperGetCurrentPage(TableScanPhysicalOperator *tableScanPhysOper);
BYTE             *TableScanPhysOperGetCurrentRecord(TableScanPhysicalOperator *tableScanPhysOper);
int               TableScanPhysOperGetCurrentRecordIndex(TableScanPhysicalOperator *tableScanPhysOper);
#endif  // TABLE_SCAN_PHYSICAL_OPERATOR_H
