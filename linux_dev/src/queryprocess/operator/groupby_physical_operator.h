#ifndef GROUPBY_PHYSICAL_OPERATOR_H
#define GROUPBY_PHYSICAL_OPERATOR_H
#include "exec_tuple.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "vararraylist.h"
typedef struct GroupTuple GroupTuple;
typedef struct GroupByPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  HashMap             *colMap;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文
  bool                 isFirst;
  bool                 isNewGroup;
  bool                 isRecordEof;
  varArrayList        *groupbyFields;
  varArrayList        *aggExprs;
  varArrayList        *fieldExprs;
  varArrayList        *preValues;
  varArrayList        *aggrResults;   // 存储聚合函数的结果，只分配一次内存
  varArrayList        *fieldResults;  // 存储普通字段的值，只分配一次内存
  Record              *prevTuple;     // 用于存储上一个元组
  Record              *currentTuple;
} GroupByPhysicalOperator;
int  GroupByPhysOperInit(GroupByPhysicalOperator *groupbyPhysOper, PhysicalOperatorType type);
void GroupByGetParmsValues(GroupByPhysicalOperator *groupbyPhysOper, varArrayList *groupbyFields,
    varArrayList *aggExprs, varArrayList *fieldExprs);
int  GroupByPhysOperOpen(GroupByPhysicalOperator *groupbyPhysOper, SQLStageEvent *sqlEvent);
int  GroupByPhysOperNext(GroupByPhysicalOperator *groupbyPhysOper, SQLStageEvent *sqlEvent);
int  GroupByPhysOperClose(GroupByPhysicalOperator *groupbyPhysOper, SQLStageEvent *sqlEvent);
int  GroupBySetColMeta(GroupByPhysicalOperator *groupbyPhysOper);
void GroupByPhysOperDestroy(GroupByPhysicalOperator *groupbyPhysOper);
void GroupByPhysOperPointerDestory(void *data);

Record *GroupByPhysOperGetCurrentTuple(GroupByPhysicalOperator *groupbyPhysOper);
#endif  // GROUPBY_PHYSICAL_OPERATOR_H
