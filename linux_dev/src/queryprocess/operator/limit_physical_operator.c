#include "limit_physical_operator.h"
#include "exec_tuple.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "expression.h"
#include "sql_event.h"
#include "vararraylist.h"
#include "assert.h"
#include <string.h>
int LimitPhysOperInit(LimitPhysicalOperator *limitPhysOper, PhysicalOperatorType type)
{
  limitPhysOper->type          = type;
  limitPhysOper->children      = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  limitPhysOper->parentTuple   = NULL;
  limitPhysOper->type          = type;
  limitPhysOper->limit         = -1;  // -1 means no limit
  limitPhysOper->offset        = -1;  // -1 means no offset
  limitPhysOper->currentOffset = 0;
  return 0;
}

int LimitPhysOperOpen(LimitPhysicalOperator *limitPhysOper, SQLStageEvent *sqlEvent)
{
  PhysicalOperator *child = NULL;
  assert(limitPhysOper->children->elementCount == 1);
  child = (PhysicalOperator *)varArrayListGetPointer(limitPhysOper->children, 0);
  return PhysicalOperatorOpen(child, sqlEvent);
}

int LimitPhysOperNext(LimitPhysicalOperator *limitPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = NULL;

  child = (PhysicalOperator *)varArrayListGetPointer(limitPhysOper->children, 0);
  while (1) {
    rc = PhysicalOperatorNext(child, sqlEvent);
    if (rc == GNCDB_NEXT_EOF) {
      return GNCDB_NEXT_EOF;
    }
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    /*没有limit条件的情况*/
    if (limitPhysOper->limit <= -1) {
      return GNCDB_SUCCESS;
    }
    /*没有offset且当前已经返回的元组个数小于limit*/
    if (limitPhysOper->offset <= -1 && limitPhysOper->currentOffset < limitPhysOper->limit) {
      limitPhysOper->currentOffset++;
      return GNCDB_SUCCESS;
    }
    /*有offset且当前位移小于offset，就是跳过的行没达到目标行*/
    if (limitPhysOper->currentOffset < limitPhysOper->offset) {
      limitPhysOper->currentOffset++;
      continue;
    }
    /*已经跳过或者返回的行数大于offset和limit之和，代表处理完毕*/
    if (limitPhysOper->currentOffset >= limitPhysOper->limit + limitPhysOper->offset) {
      return GNCDB_NEXT_EOF;
    }
    /*这里是limitPhysOper->offset < limitPhysOper->currentOffset < limitPhysOper->limit + limitPhysOper->offset的情况*/
    /*Q:这里为什么没有curroffset + 1*/
    return GNCDB_SUCCESS;
  }
}

int LimitPhysOperClose(LimitPhysicalOperator *limitPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = NULL;

  child = (PhysicalOperator *)varArrayListGetPointer(limitPhysOper->children, 0);
  rc    = PhysicalOperatorClose(child, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

Record *LimitPhysOperGetCurrentTuple(LimitPhysicalOperator *limitPhysOper)
{
  PhysicalOperator *child = NULL;
  child                   = (PhysicalOperator *)varArrayListGetPointer(limitPhysOper->children, 0);
  return GetCurrentTuple(child);
}

int LimitSetColMeta(LimitPhysicalOperator *limitPhysOper)
{
  int               i             = 0;
  PhysicalOperator *child         = NULL;
  varArrayList     *cols          = NULL;
  Meta             *col           = NULL;

  if (limitPhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (limitPhysOper->children == NULL || limitPhysOper->children->elementCount == 0) {
    return GNCDB_SUCCESS;  // 这里返回SUCCESS是因为如果where clause是永假的，其子操作符会被直接delete掉
  }
  child = (PhysicalOperator *)varArrayListGetPointer(limitPhysOper->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }

  cols = getColMeta(child);
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }

  limitPhysOper->cols = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (limitPhysOper->cols == NULL) {
    return GNCDB_MEM;
  }

  for (i = 0; i < cols->elementCount; i++) {
    col = varArrayListGetPointer(cols, i);
    varArrayListAddPointer(limitPhysOper->cols, col);
  }

  return GNCDB_SUCCESS;
}

void LimitPhysOperDestroy(LimitPhysicalOperator *limitPhysOper)
{
  if (limitPhysOper == NULL) {
    return;
  }
  if (limitPhysOper->children != NULL) {
    varArrayListDestroy(&limitPhysOper->children);
  }

  if (limitPhysOper->cols != NULL) {
    varArrayListDestroy(&limitPhysOper->cols);
  }
  my_free(limitPhysOper);
}

void LimitPhysOperDestroyPointerDestroy(void *data)
{
  LimitPhysicalOperator **limitPhysOper = (LimitPhysicalOperator **)data;
  if (limitPhysOper == NULL || *limitPhysOper == NULL) {
    return;
  }
  LimitPhysOperDestroy(*limitPhysOper);
  *limitPhysOper = NULL;
}
