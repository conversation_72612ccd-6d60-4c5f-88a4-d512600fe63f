#include "predicate_logical_operator.h"
#include "vararraylist.h"

void PredicateLogiOperDestroy(PredicateLogicalOperator *predicateLogiOper)
{
  if (predicateLogiOper == NULL) {
    return;
  }
  if (predicateLogiOper->children != NULL) {
    varArrayListDestroy(&predicateLogiOper->children);
  }

  if (predicateLogiOper->expressions != NULL) {
    varArrayListDestroy(&predicateLogiOper->expressions);
  }

  my_free(predicateLogiOper);
}

void PredicateLogiOperPointerDestroy(void *data)
{
  PredicateLogicalOperator **predicateLogiOper = (PredicateLogicalOperator **)data;
  if (predicateLogiOper == NULL || *predicateLogiOper == NULL) {
    return;
  }

  PredicateLogiOperDestroy(*predicateLogiOper);
  *predicateLogiOper = NULL;
}