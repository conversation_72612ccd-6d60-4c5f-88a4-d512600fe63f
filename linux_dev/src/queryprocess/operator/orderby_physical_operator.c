#include "orderby_physical_operator.h"
#include "exec_tuple.h"
#include "expression.h"
#include "assert.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sort_r.h"
#include "typedefine.h"
#include "utils.h"
#include "value.h"
#include "vararraylist.h"

extern void    *matchMeta(Expression *expr, varArrayList *colMetas);
extern ColMeta *matchField(FieldExpr *fieldExpr, varArrayList *colMetas);

void CmpPairDestroy(CmpPair *pair)
{
  if (pair == NULL) {
    return;
  }
  if (pair->cells != NULL) {
    varArrayListDestroy(&(pair->cells));
  }
  my_free(pair);
}

void CmpPairPointerDestroy(void *data)
{
  CmpPair **pair = (CmpPair **)data;
  if (pair == NULL || *pair == NULL) {
    return;
  }
  CmpPairDestroy(*pair);
  *pair = NULL;
}

int OrderByPhysOperInit(OrderByPhysicalOperator *orderbyPhysOper, PhysicalOperatorType type)
{
  orderbyPhysOper->type         = type;
  orderbyPhysOper->children     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  orderbyPhysOper->parentTuple  = NULL;
  orderbyPhysOper->orderbyUnits = NULL;
  orderbyPhysOper->exprs        = NULL;
  orderbyPhysOper->values       = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, RecordPtrDestroy);
  orderbyPhysOper->orderedIndex = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  orderbyPhysOper->iter         = 0;
  orderbyPhysOper->cols         = NULL;
  return 0;
}

int cmp(const void *a, const void *b, void *order)
{
  CmpPair **pairA    = NULL;
  CmpPair **pairB    = NULL;
  bool     *orderArr = NULL;
  int       i        = 0;
  Value    *cellA    = NULL;
  Value    *cellB    = NULL;

  pairA    = (CmpPair **)a;
  pairB    = (CmpPair **)b;
  orderArr = (bool *)order;
  assert((*pairA)->cells->elementCount == (*pairB)->cells->elementCount);
  for (i = 0; i < (*pairA)->cells->elementCount; ++i) {
    cellA = varArrayListGetPointer((*pairA)->cells, i);
    cellB = varArrayListGetPointer((*pairB)->cells, i);
    if (valueCompare(cellA, cellB) != 0) {
      return orderArr[i] ? (valueCompare(cellA, cellB)) : (-valueCompare(cellA, cellB));
    }
  }
  return 0;  // 完全相同
}

int OrderByPhysOperSort(OrderByPhysicalOperator *orderbyPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc              = GNCDB_SUCCESS;
  int               index           = 0;
  varArrayList     *pairSortTable   = NULL;
  varArrayList     *pairCell        = NULL;
  Record           *newRecord       = NULL;
  PhysicalOperator *child           = NULL;
  int               i               = 0;
  OrderByUnit      *orderbyUnit     = NULL;
  Expression       *expr            = NULL;
  Value            *cell            = NULL;
  CmpPair          *pair            = NULL;
  bool             *order           = NULL;
  Record           *tuple           = NULL;
  varArrayList     *childcols       = NULL;
  int               newRecordLength = 0;
  ColMeta          *colMeta         = NULL;
  ColMeta          *newColMeta      = NULL;
  FieldExpr        *fieldExpr       = NULL;
  BYTE             *bitmap          = NULL;  /*用于bitmap的临时变量*/
  int               childRealColNum = 0;     /*child的列数，用于计算bitmap的长度*/
  int               isNull;

  child = varArrayListGetPointer(orderbyPhysOper->children, 0);
  /*获取new Record的长度*/
  rc    = getRecordLength(orderbyPhysOper->cols, &newRecordLength);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  childcols = getColMeta(child);
  if (childcols == NULL) {
    return GNCDB_INTERNAL;
  }
  childRealColNum = childcols->elementCount - 1;
  pairSortTable   = varArrayListCreate(DISORDER, sizeof(CmpPair *), 0, NULL, CmpPairPointerDestroy);  /*要排序的内容*/
  while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext(child, sqlEvent))) {
    tuple = GetCurrentTuple(child);
    if (tuple == NULL) {
      varArrayListDestroy(&pairSortTable);
      return GNCDB_INTERNAL;
    }
    pairCell = varArrayListCreate(DISORDER, sizeof(Value *), 0, NULL, valuePointerDestroy);  /*参与排序的列*/
    varArrayListClear(pairCell);
    /*从元组中取出要排序的字段放入cell，然后再把cell放入pairCell中，pairCell是整个元组的排序字段*/
    for (i = 0; i < orderbyPhysOper->orderbyUnits->elementCount; i++) {
      cell        = valueCreate();
      orderbyUnit = varArrayListGetPointer(orderbyPhysOper->orderbyUnits, i);
      expr        = orderbyUnit->expr;
      rc          = expressionGetValueDC(expr, tuple, childcols, cell);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&pairSortTable);
        varArrayListDestroy(&pairCell);
        valueDestroy(&cell);
        return rc;
      }
      rc = varArrayListAddPointer(pairCell, cell);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&pairSortTable);
        varArrayListDestroy(&pairCell);
        valueDestroy(&cell);
        return rc;
      }
    }
    /*生成一个用于排序的pair，它的cells是该元组排序字段上的值，index是该元组的下标意识是第几个元组*/
    pair = (CmpPair *)my_malloc0(sizeof(CmpPair));
    if (pair == NULL) {
      varArrayListDestroy(&pairSortTable);
      varArrayListDestroy(&pairCell);
      return GNCDB_INTERNAL;
    }
    pair->index = index++;
    pair->cells = PTR_MOVE((void **)&pairCell);
    /*将排序的pair放入相应的pairtable*/
    rc          = varArrayListAddPointer(pairSortTable, pair);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pairSortTable);
      varArrayListDestroy(&pairCell);
      return rc;
    }
    /*exprs放的是上层算子需要的列*/
    if (orderbyPhysOper->exprs->elementCount == 0) {
      varArrayListAddPointer(orderbyPhysOper->values, RecordDeepCopy(tuple));
      tuple = NULL;
    } else {
      newRecord = RecordCreateWithoutData(newRecordLength);
      for (i = 0; i < orderbyPhysOper->exprs->elementCount; i++) {
        /*这里这样处理是因为orderbyPhysOper->exprs里的一定都是FieldExpr*/
        fieldExpr = varArrayListGetPointer(orderbyPhysOper->exprs, i);
        colMeta   = matchField(fieldExpr, childcols);
        if (colMeta == NULL) {
          RecordDestroy(&newRecord);
          varArrayListDestroy(&pairSortTable);
          varArrayListDestroy(&pairCell);
          return GNCDB_INTERNAL;
        }
        /*获取bitmap对应的值*/
        bitmap     = (tuple->data + colMeta->bitmapOffset);
        isNull     = leafTupleGetBitMap(bitmap, colMeta->index, childRealColNum);
        newColMeta = varArrayListGetPointer(orderbyPhysOper->cols, i + 1);
        RecordSetData(newRecord, newColMeta->offset, tuple->data + colMeta->offset, colMeta->len);
        leafRecordSetBitMap(newRecord->data, newColMeta->index, isNull);
      }
      rc = varArrayListAddPointer(orderbyPhysOper->values, PTR_MOVE((void **)&newRecord));
      if (rc != GNCDB_SUCCESS) {
        printf("OrderByPhysicalOperator_Sort: varArrayListAdd failed\n");
        varArrayListDestroy(&pairSortTable);
        varArrayListDestroy(&pairCell);
        RecordDestroy(&newRecord);
        return rc;
      }
    }
  }
  if (rc != GNCDB_NEXT_EOF) {
    varArrayListDestroy(&pairSortTable);
    return rc;
  }
  order = my_malloc0(sizeof(bool) * orderbyPhysOper->orderbyUnits->elementCount);
  if (order == NULL) {
    varArrayListDestroy(&pairSortTable);
    return GNCDB_MEM;
  }
  for (i = 0; i < orderbyPhysOper->orderbyUnits->elementCount; i++) {
    orderbyUnit = varArrayListGetPointer(orderbyPhysOper->orderbyUnits, i);
    order[i]    = orderbyUnit->isAsc;
  }
#if defined(_WIN32)
  sort_r(pairSortTable->dataBuffer, pairSortTable->elementCount, sizeof(CmpPair *), cmp, order);
#else
  qsort_r(pairSortTable->dataBuffer, pairSortTable->elementCount, sizeof(CmpPair *), cmp, order);
#endif
  for (i = 0; i < pairSortTable->elementCount; i++) {
    pair = varArrayListGetPointer(pairSortTable, i);
    varArrayListAdd(orderbyPhysOper->orderedIndex, &pair->index);
  }
  my_free(order);
  varArrayListDestroy(&pairSortTable);
  return rc;
}

int OrderByPhysOperOpen(OrderByPhysicalOperator *orderbyPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = NULL;
  /*1.orderby的算子有且只有一个*/
  if (orderbyPhysOper->children->elementCount != 1) {
    return GNCDB_INTERNAL;
  }
  /*2.open子算子*/
  child = varArrayListGetPointer(orderbyPhysOper->children, 0);
  if (GNCDB_SUCCESS != (rc = PhysicalOperatorOpen(child, sqlEvent))) {
    if (rc != GNCDB_NEXT_EOF) {
      return rc;
    }
  }
  /*3.执行排序*/
  rc = OrderByPhysOperSort(orderbyPhysOper, sqlEvent);
  return rc;
}

int OrderByPhysOperNext(OrderByPhysicalOperator *orderbyPhysOper, SQLStageEvent *sqlEvent)
{
  int     rc     = GNCDB_SUCCESS;
  int     index  = 0;
  Record *record = NULL;
  /*Q:iter是代表什么*/
  if (orderbyPhysOper->iter >= orderbyPhysOper->orderedIndex->elementCount) {
    return GNCDB_NEXT_EOF;
  }
  index  = *(int *)varArrayListGet(orderbyPhysOper->orderedIndex, orderbyPhysOper->iter);
  record = varArrayListGetPointer(orderbyPhysOper->values, index);
  // varArrayListSetByIndexPointer(
  //     orderbyPhysOper->values, index, NULL);  // 因为record的所有权被上层算子接管，这里需要将record置空

  orderbyPhysOper->currentTuple = record;
  orderbyPhysOper->iter++;
  return rc;
}

int OrderByPhysOperClose(OrderByPhysicalOperator *orderbyPhysOper, SQLStageEvent *sqlEvent)
{
  PhysicalOperator *child = NULL;
  int               rc    = GNCDB_SUCCESS;
  assert(orderbyPhysOper->children->elementCount == 1);
  child = varArrayListGetPointer(orderbyPhysOper->children, 0);
  rc    = PhysicalOperatorClose(child, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return rc;
}

int OrderBySetColMeta(OrderByPhysicalOperator *orderbyPhysOper)
{
  PhysicalOperator *child      = NULL;
  varArrayList     *childCols  = NULL;
  int               i          = 0;
  int               offset     = 0;
  varArrayList     *fieldExprs = NULL;
  Expression       *expr       = NULL;
  Meta             *meta       = NULL;
  Meta             *newMeta    = NULL;
  ColMeta          *colMeta    = NULL;

  child = varArrayListGetPointer(orderbyPhysOper->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }
  childCols = getColMeta(child);
  if (childCols == NULL) {
    return GNCDB_INTERNAL;
  }

  orderbyPhysOper->cols = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, MetaPtrDestroy);
  if (orderbyPhysOper->cols == NULL) {
    return GNCDB_INTERNAL;
  }

  fieldExprs = orderbyPhysOper->exprs;
  // 直接返回所有字段
  if (fieldExprs->elementCount == 0) {
    for (i = 0; i < childCols->elementCount; i++) {
      meta    = varArrayListGetPointer(childCols, i);
      newMeta = MetaDeepCopy(meta);
      if (newMeta == NULL) {
        return GNCDB_INTERNAL;
      }
      // if(newMeta->type == CompositeField) {// 对于复合类型可能需要重新计算长度
      //   newMeta->len = expressionGetValueLen(expr, childCols);
      //   if (newMeta->len < 0) {
      //     return GNCDB_INTERNAL;
      //   }
      // }
      // newMeta->offset = offset;
      // offset += newMeta->len;
      varArrayListAddPointer(orderbyPhysOper->cols, newMeta);
      // 如果是复合类型的字段，直接跳过
      // if (newMeta->type == CompositeField) {
      //   continue;
      // }
      // // 如果是普通类型的字段，需要reset offset
      // newCol         = (ColMeta *)newMeta;
      // newCol->offset = offset;  // reset offset
      // offset += newCol->len;
      // varArrayListAddPointer(orderbyPhysOper->cols, newCol);
    }
    return GNCDB_SUCCESS;
  }

  colMeta = (ColMeta *)my_malloc0(sizeof(ColMeta));
  if (colMeta == NULL) {
    return GNCDB_MEM;
  }
  colMeta->owned     = true;
  colMeta->type      = NormalField;
  colMeta->tableName = NULL;
  colMeta->name      = my_strdup("__bitMap");
  colMeta->len       = GET_BITMAP_LENGTH(fieldExprs->elementCount);
  colMeta->offset    = offset;
  colMeta->isBitMap  = true;
  colMeta->index     = (-1);  // 设置索引，便于后续处理bitmap
  offset += colMeta->len;
  varArrayListAddPointer(orderbyPhysOper->cols, colMeta);  // 添加bitmap列到orderby的cols中

  for (i = 0; i < fieldExprs->elementCount; i++) {
    expr = varArrayListGetPointer(fieldExprs, i);
    // if (expr->type != ETG_FIELD) {
    //   return GNCDB_INTERNAL;  // 不应该出现FieldExpr之外的表达式
    // }
    // if (expr->type == ETG_FIELD) {
    //   meta = matchField((FieldExpr *)expr, childCols);
    //   if (Meta == NULL) {
    //     return GNCDB_INTERNAL;
    //   }
    // } else if (expr->type == ETG_AGGRFUNC) {
    //   meta = matchAggrFunc((AggrFuncExpr *)expr, childCols);
    //   if (meta == NULL) {
    //     return GNCDB_INTERNAL;
    //   }
    // }
    // newCol = MetaDeepCopy(meta);
    // TODO: 处理null值的情形
    newMeta = matchMeta(expr, childCols);
    newMeta = MetaDeepCopy(newMeta);
    if (newMeta == NULL) {
      return GNCDB_INTERNAL;
    }
    if (newMeta->type == CompositeField) {
      newMeta->len = expressionGetValueLen(expr, childCols);
      if (newMeta->len < 0) {
        return GNCDB_INTERNAL;
      }
    }
    newMeta->offset       = offset;
    newMeta->index        = (i);  // 设置索引，便于后续处理bitmap
    newMeta->bitmapOffset = 0;
    offset += newMeta->len;
    varArrayListAddPointer(orderbyPhysOper->cols, newMeta);

    // // 如果是复合类型的字段，直接跳过
    // if (newMeta->type == CompositeField) {
    //   continue;
    // }
    // // 如果是普通类型的字段，需要reset offset
    // newCol         = (ColMeta *)newMeta;
    // newCol->offset = offset;  // reset offset
    // offset += newCol->len;
    // varArrayListAddPointer(orderbyPhysOper->cols, newMeta);
  }
  return GNCDB_SUCCESS;
}

Record *OrderByPhysOperGetCurrentTuple(OrderByPhysicalOperator *orderbyPhysOper)
{
  return PTR_MOVE((void **)&orderbyPhysOper->currentTuple);
}

void OrderByPhysOperDestroy(OrderByPhysicalOperator *orderbyPhysOper)
{
  // Record *record = NULL;
  if (orderbyPhysOper == NULL) {
    return;
  }
  if (orderbyPhysOper->children != NULL) {
    varArrayListDestroy(&orderbyPhysOper->children);
  }
  if (orderbyPhysOper->cols != NULL) {
    varArrayListDestroy(&orderbyPhysOper->cols);
  }
  if (orderbyPhysOper->orderbyUnits != NULL) {
    varArrayListDestroy(&orderbyPhysOper->orderbyUnits);
  }
  if (orderbyPhysOper->values != NULL) {
    varArrayListDestroy(&orderbyPhysOper->values);
  }
  if (orderbyPhysOper->orderedIndex != NULL) {
    varArrayListDestroy(&orderbyPhysOper->orderedIndex);
  }
  if (orderbyPhysOper->exprs != NULL) {
    varArrayListDestroy(&orderbyPhysOper->exprs);
  }
  my_free(orderbyPhysOper);
}

void OrderByPhysOperPointerDestroy(void *data)
{
  OrderByPhysicalOperator **orderbyPhysOper = NULL;
  orderbyPhysOper                           = (OrderByPhysicalOperator **)data;
  if (orderbyPhysOper == NULL || *orderbyPhysOper == NULL) {
    return;
  }
  OrderByPhysOperDestroy(*orderbyPhysOper);
  *orderbyPhysOper = NULL;
}
