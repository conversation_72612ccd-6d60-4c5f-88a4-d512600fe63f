#include "calc_physical_operator.h"
void CalcPhysicalOperator<PERSON><PERSON>roy(CalcPhysicalOperator *calcPhysOper)
{
  if (calcPhysOper == NULL) {
    return;
  }

  if (calcPhysOper->children != NULL) {
    varArrayListDestroy(&calcPhysOper->children);
  }

  if (calcPhysOper->expressions != NULL) {
    varArrayListDestroy(&calcPhysOper->expressions);
  }

  my_free(calcPhysOper);
}

void CalcPhysicalOperatorPointerDestroy(void *data)
{
  CalcPhysicalOperator **calcPhysOper = (CalcPhysicalOperator **)data;
  if (calcPhysOper == NULL || *calcPhysOper == NULL) {
    return;
  }

  CalcPhysicalOperatorDestroy(*calcPhysOper);
  *calcPhysOper = NULL;
}
