#include "delete_logical_operator.h"

void DeleteLogiOperDestroy(DeleteLogicalOperator *deleteLogiOper)
{
  if (deleteLogiOper == NULL) {
    return;
  }

  if (deleteLogiOper->children != NULL) {
    varArrayListDestroy(&deleteLogiOper->children);
  }
  if (deleteLogiOper->expressions != NULL) {
    varArrayListDestroy(&deleteLogiOper->expressions);
  }
  my_free(deleteLogiOper);
}

void DeleteLogiOperPointerDestroy(void *data)
{
  DeleteLogicalOperator **deleteLogiOper = (DeleteLogicalOperator **)data;
  if (deleteLogiOper == NULL || *deleteLogiOper == NULL) {
    return;
  }
  DeleteLogiOperDestroy(*deleteLogiOper);
  *deleteLogiOper = NULL;
}
