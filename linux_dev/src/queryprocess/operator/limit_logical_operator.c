#include "limit_logical_operator.h"
void LimitLogiOperDestroy(LimitLogicalOperator *limitLogiOper)
{
  if (limitLogiOper->children != NULL) {
    varArrayListDestroy(&limitLogiOper->children);
  }

  if (limitLogiOper->expressions != NULL) {
    varArrayListDestroy(&limitLogiOper->expressions);
  }

  my_free(limitLogiOper);
}

void LimitLogiOperPointerDestroy(void *data)
{
  LimitLogicalOperator **limitLogiOper = NULL;

  limitLogiOper = (LimitLogicalOperator **)data;
  if (limitLogiOper == NULL || *limitLogiOper == NULL) {
    return;
  }
  LimitLogiOperDestroy(*limitLogiOper);
  *limitLogiOper = NULL;
}
