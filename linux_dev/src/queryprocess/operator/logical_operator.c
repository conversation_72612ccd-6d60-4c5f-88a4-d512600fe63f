#include "logical_operator.h"
#include "gncdbconstant.h"
#include "table_get_logical_operator.h"
#include "index_scan_logical_operator.h"
#include "predicate_logical_operator.h"
#include "calc_logical_operator.h"
#include "create_table_logical_operator.h"
#include "create_index_logical_operator.h"
#include "delete_logical_operator.h"
#include "groupby_logical_operator.h"
#include "insert_logical_operator.h"
#include "join_logical_operator.h"
#include "limit_logical_operator.h"
#include "orderby_logical_operator.h"
#include "project_logical_operator.h"
#include "typedefine.h"
#include "update_logical_operator.h"
#include "vararraylist.h"
void LogicalOperatorToString(LogicalOperator *logicalOperator, int depth)
{
  Expression      *expression = NULL;
  LogicalOperator *child      = NULL;
  int              i          = 0;

  printf("%*s", depth * 4, "");  // 缩进
  switch (logicalOperator->type) {
    case LO_CALC: printf("LO_CALC\n"); break;
    case LO_TABLE_GET:
      printf("LO_TABLE_GET:%s\n", ((TableGetLogicalOperator *)logicalOperator)->table->tableName);
      break;
    case LO_PREDICATE:
      printf("LO_PREDICATE: ");
      expression = varArrayListGetPointer(((PredicateLogicalOperator *)logicalOperator)->expressions, 0);
      printExpr(expression);
      printf("\n");
      break;
    case LO_PROJECTION: printf("LO_PROJECTION\n"); break;
    case LO_JOIN: printf("LO_JOIN\n"); break;
    case LO_INSERT: printf("LO_INSERT\n"); break;
    case LO_DELETE: printf("LO_DELETE\n"); break;
    case LO_EXPLAIN: printf("LO_EXPLAIN\n"); break;
    case LO_UPDATE: printf("LO_UPDATE\n"); break;
    case LO_GROUPBY: printf("LO_GROUPBY\n"); break;
    case LO_ORDERBY: printf("LO_ORDERBY\n"); break;
    case LO_CREATE_TABLE: printf("LO_CREATE_TABLE\n"); break;
    case LO_CREATE_INDEX: printf("LO_CREATE_INDEX\n"); break;
    case LO_LIMIT: printf("LO_LIMIT\n"); break;
    default: printf("Unknown Logical Operator\n"); break;
  }
  for (i = 0; i < logicalOperator->children->elementCount; i++) {
    child = (LogicalOperator *)varArrayListGetPointer(logicalOperator->children, i);
    LogicalOperatorToString(child, depth + 1);
  }
}

void LogicalOperatorDestroy(LogicalOperator **logicalOperator)
{
  if (logicalOperator == NULL || *logicalOperator == NULL) {
    return;
  }
  switch ((*logicalOperator)->type) {
    case LO_CALC: {
      CalcLogicalOperatorDestroy((CalcLogicalOperator *)(*logicalOperator));
    } break;
    case LO_CREATE_TABLE: {
      CreateTableLogiOperDestroy((CreateTableLogicalOperator *)(*logicalOperator));
    } break;
    case LO_CREATE_INDEX: {
      CreateIndexLogiOperDestroy((CreateIndexLogicalOperator *)(*logicalOperator));
    } break;
    case LO_DELETE: {
      DeleteLogiOperDestroy((DeleteLogicalOperator *)(*logicalOperator));
    } break;
    case LO_GROUPBY: {
      GroupByLogiOperDestroy((GroupByLogicalOperator *)(*logicalOperator));
    } break;
    case LO_INSERT: {
      InsertLogiOperDestroy((InsertLogicalOperator *)(*logicalOperator));
    } break;
    case LO_JOIN: {
      JoinLogiOperFullDestroy((JoinLogicalOperator *)(*logicalOperator));
    } break;
    case LO_LIMIT: {
      LimitLogiOperDestroy((LimitLogicalOperator *)(*logicalOperator));
    } break;
    case LO_ORDERBY: {
      OrderByLogiOperDestroy((OrderByLogicalOperator *)(*logicalOperator));
    } break;
    case LO_PREDICATE: {
      PredicateLogiOperDestroy((PredicateLogicalOperator *)(*logicalOperator));
    } break;
    case LO_PROJECTION: {
      ProjectLogiOperDestroy((ProjectLogicalOperator *)(*logicalOperator));
    } break;
    case LO_TABLE_GET: {
      TableGetLogiOperDestroy((TableGetLogicalOperator *)(*logicalOperator));
    } break;
    case LO_UPDATE: {
      UpdateLogiOperDestroy((UpdateLogicalOperator *)(*logicalOperator));
    } break;
    default: {
      break;
    }
  }
  *logicalOperator = NULL;
}

void LogicalPlanDestroy(LogicalOperator **logOper)
{
  LogicalOperator *child1 = NULL;
  LogicalOperator *child2 = NULL;

  if (logOper == NULL || *logOper == NULL) {
    return;
  }
  switch ((*logOper)->type) {
    case LO_INSERT: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      InsertLogiOperDestroy((InsertLogicalOperator *)(*logOper));
    } break;
    case LO_TABLE_GET: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      TableGetLogiOperDestroy((TableGetLogicalOperator *)(*logOper));
    } break;
    case LO_INDEX_SCAN: {
      INDEX_SCAN_LOGIOPER_DESTROY((IndexScanLogicalOperator *)(*logOper));
    } break;
    case LO_JOIN: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((JoinLogicalOperator *)(*logOper))->children, 0);
      child2 = varArrayListGetPointer(((JoinLogicalOperator *)(*logOper))->children, 1);
      LogicalPlanDestroy(&child1);
      LogicalPlanDestroy(&child2);
      JoinLogiOperFullDestroy((JoinLogicalOperator *)(*logOper));
    } break;
    case LO_DELETE: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((DeleteLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      DeleteLogiOperDestroy((DeleteLogicalOperator *)(*logOper));
    } break;
    case LO_UPDATE: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((UpdateLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      UpdateLogiOperDestroy((UpdateLogicalOperator *)(*logOper));
    } break;
    case LO_PROJECTION: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((ProjectLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      ProjectLogiOperDestroy((ProjectLogicalOperator *)(*logOper));
    } break;
    case LO_GROUPBY: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((GroupByLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      GroupByLogiOperDestroy((GroupByLogicalOperator *)(*logOper));
    } break;
    case LO_ORDERBY: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((OrderByLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      OrderByLogiOperDestroy((OrderByLogicalOperator *)(*logOper));
    } break;
    case LO_CREATE_TABLE: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((CreateTableLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      CreateTableLogiOperDestroy((CreateTableLogicalOperator *)(*logOper));
    } break;
    case LO_CREATE_INDEX: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((CreateIndexLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      CreateIndexLogiOperDestroy((CreateIndexLogicalOperator *)(*logOper));
    } break;
    case LO_LIMIT: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((LimitLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      LimitLogiOperDestroy((LimitLogicalOperator *)(*logOper));
    } break;
    case LO_PREDICATE: {
      if (((JoinLogicalOperator *)(*logOper))->children == NULL ||
          ((JoinLogicalOperator *)(*logOper))->children->elementCount == 0) {
        JoinLogiOperDestroy((JoinLogicalOperator *)(*logOper));
        return;
      }
      child1 = varArrayListGetPointer(((PredicateLogicalOperator *)(*logOper))->children, 0);
      LogicalPlanDestroy(&child1);
      PredicateLogiOperDestroy((PredicateLogicalOperator *)(*logOper));
    } break;

    default: printf("unknown logical operator type %d\n", (*logOper)->type);
  }
  // varArrayListClear(((JoinLogicalOperator*)(*logOper))->children);
  *logOper = NULL;
}

void LogicalOperatorPointerDestroy(void *data)
{
  LogicalOperator **logicalOperator = NULL;
  logicalOperator                   = (LogicalOperator **)data;
  if (logicalOperator == NULL || *logicalOperator == NULL) {
    return;
  }
  LogicalOperatorDestroy(logicalOperator);
  *logicalOperator = NULL;
}