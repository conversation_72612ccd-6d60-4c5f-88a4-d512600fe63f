#include "insert_physical_operator.h"
#include "gncdbconstant.h"
#include "queryexecutor.h"
#include "math.h"
#include "tuple.h"
#include "typedefine.h"
#include <linux/falloc.h>
#include <string.h>
/**
 * @brief
 *
 * @param db
 * @param affectedRows
 * @param tableName 表名
 * @param values 插入的值
 * @return int
 */
int sqlInsert(struct GNCDB *db, int *affectedRows, char *tableName, varArrayList *values, Transaction *txn)
{
  int          rc          = 0;
  Transaction *tx          = NULL;
  BtreeTable  *table       = NULL;
  TableSchema *tableSchema = NULL;
  BYTE        *record      = NULL;
  int          i           = 0;
  Column      *column      = NULL;
  int          valueInt    = 0;
  double       valueDouble = 0;
  char        *strs        = NULL;
  time_t       create      = 0;
  time_t       update      = 0;
  int          len         = 0;
  Value       *value       = NULL;
  int          isNull      = false;
  int          offset      = 0;
  // int          zero        = 0;
  tx                       = txn;
  /*1.参数检查，包括参数非空检查，插入的表名不能为系统表*/
  if (tx == NULL) {
    return GNCDB_MEM;
  }
  if (strcmp(tableName, MASTERNAME) == 0 || strcmp(tableName, SCHEMANAME) == 0) {
    return GNCDB_NOT_REFACTOR;
  }

  /*2.获取表元信息，并检查表的权限信息*/
  rc = catalogGetTable(db->catalog, &table, tableName);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return GNCDB_MEM;
  }
  if (tableSchema->tableParam == '0') {
    return GNCDB_TABLE_PARAM_FALSE;
  }

  /*3.申请一个空元组并初始化为0*/
  record = (BYTE *)my_malloc(table->leafRecordLength);
  memset(record, 0, table->leafRecordLength);
  offset = GET_BITMAP_LENGTH(tableSchema->columnNum);
  if (record == NULL) {
    return GNCDB_MEM;
  }

  /*4.根据列元信息构造元组*/
  for (i = 0; i < tableSchema->columnNum - 3; i++) {
    isNull = false;
    column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
    switch (column->fieldType) {
      case FIELDTYPE_DATE: {
      }
      case FIELDTYPE_INTEGER: {
        value = varArrayListGetPointer(values, i);
        if (value->attrType == NULLS) {
          /*4.1若是该值为空但是列约束中该列不能为空则失败，否则标记为空值*/
          if (column->columnConstraint->canBeNull == false) {
            my_free(record);
            return GNCDB_FIELD_NOT_ALLOW_NULL;
          }
          isNull = true;
        } else {
          valueInt = value->numValue.intValue;
          if (valueInt < column->columnConstraint->minValue || valueInt > column->columnConstraint->maxValue) {
            my_free(record);
            return GNCDB_PARAM_INVALID;
          }
          memcpy(record + column->offset, &valueInt, INT_SIZE);
        }
        offset = column->offset + INT_SIZE;
        break;
      }
      case FIELDTYPE_REAL: {
        value = varArrayListGetPointer(values, i);
        if (value->attrType == NULLS) {
          /*4.1若是该值为空但是列约束中该列不能为空则失败，否则标记为空值*/
          if (column->columnConstraint->canBeNull == false) {
            my_free(record);
            return GNCDB_FIELD_NOT_ALLOW_NULL;
          }
          isNull = true;
        } else {
          valueDouble = value->numValue.doubleValue;
          if (fabs(valueDouble) < column->columnConstraint->minValue ||
              fabs(valueDouble) > column->columnConstraint->maxValue) {
            my_free(record);
            return GNCDB_PARAM_INVALID;
          }
          memcpy(record + column->offset, &valueDouble, DOUBLE_SIZE);
        }
        offset = column->offset + DOUBLE_SIZE;
        break;
      }
      case FIELDTYPE_VARCHAR: {
        value = varArrayListGetPointer(values, i);
        if (value->attrType == NULLS) {
          /*4.1若是该值为空但是列约束中该列不能为空则失败，否则标记为空值*/
          if (column->columnConstraint->canBeNull == false) {
            my_free(record);
            return GNCDB_FIELD_NOT_ALLOW_NULL;
          }
          isNull = true;
        } else {
          /*4.2从value的union中获取string的值，并进行列约束检查*/
          strs = value->strValue;
          len  = strlen(strs);
          if (len < column->columnConstraint->minValue || len > column->columnConstraint->maxValue) {
            my_free(record);
            return GNCDB_PARAM_INVALID;
          }
          if (tableSchema == NULL) {
            my_free(record);
            return GNCDB_MEM;
          }
          memset(record + column->offset, 0, column->columnConstraint->maxValue);
          memcpy(record + column->offset, strs, len);
          // if (len < column->columnConstraint->maxValue) {
          //   memset(record + column->offset + len, '\0', column->columnConstraint->maxValue - len);
          // }
        }
        offset = column->offset + column->columnConstraint->maxValue;
        break;
      }
      case FIELDTYPE_BLOB: {
        value = varArrayListGetPointer(values, i);
        if (value->attrType == NULLS) {
          if (column->columnConstraint->canBeNull == false) {
            my_free(record);
            return GNCDB_FIELD_NOT_ALLOW_NULL;
          }
          isNull = true;
        } else {
          memcpy(record + column->offset, &value->overFlowPageId, INT_SIZE);
          memcpy(record + column->offset + INT_SIZE, &value->numValue.intValue, INT_SIZE);
        }
        offset += INT_SIZE * 2;
        break;
      }
      case FIELDTYPE_DATETIME: {
        break;
      }
      /* TEXT类型 */
      case FIELDTYPE_TEXT: {
        break;
      }
      default: break;
    }

    if (isNull) {
      leafTupleSetBitMap(record, i, 0, tableSchema->columnNum);
    } else {
      leafTupleSetBitMap(record, i, 1, tableSchema->columnNum);
    }
  }

  /*5.构造三个系统字段*/
  memcpy(record + offset, &tableSchema->rowId, INT_SIZE);
  tableSchema->rowId++;
  leafRecordSetBitMap(record, tableSchema->columnNum - 3, 1);
  offset += INT_SIZE;

  create = time(0);
  memcpy(record + offset, &create, INT_SIZE);
  leafRecordSetBitMap(record, tableSchema->columnNum - 2, 1);
  offset += INT_SIZE;

  update = time(0);
  memcpy(record + offset, &update, INT_SIZE);
  leafRecordSetBitMap(record, tableSchema->columnNum - 1, 1);
  
  /*6.将构造好的元组插入b+树*/
  rc = executorInsert(db, affectedRows, tableName, table, tableSchema, record, tx);
  if (rc != GNCDB_SUCCESS) {
    my_free(record);
    return rc;
  }

  /*7.将构造好的元组插入hash索引*/
  rc = executorInsertHashIndex(db, tableName, record, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  my_free(record);
  return GNCDB_SUCCESS;
}

int InsertPhysOperInit(InsertPhysicalOperator *insertPhysOper)
{
  insertPhysOper->type        = PO_INSERT;
  insertPhysOper->children    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  insertPhysOper->parentTuple = NULL;
  return GNCDB_SUCCESS;
}

int InsertPhysOperOpen(InsertPhysicalOperator *insertPhysOper, SQLStageEvent *sqlEvent)
{
  int           rc           = GNCDB_SUCCESS;
  int           i            = 0;
  int           affectedRows = 0;
  varArrayList *values       = NULL;
  /*1.直接进行物理插入*/
  for (i = 0; i < insertPhysOper->valuelists->elementCount; i++) {
    affectedRows = 0;
    values       = (varArrayList *)varArrayListGetPointer(insertPhysOper->valuelists, i);
    rc           = sqlInsert(sqlEvent->db, &affectedRows, insertPhysOper->table->tableName, values, sqlEvent->txn);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    sqlEvent->affectedRows += affectedRows;
  }

  return rc;
}

int InsertPhysOperClose(InsertPhysicalOperator *insertPhysOper, SQLStageEvent *sqlEvent) { return GNCDB_SUCCESS; }

int InsertPhysOperNext(InsertPhysicalOperator *insertPhysOper, SQLStageEvent *sqlEvent) { return GNCDB_NEXT_EOF; }

void InsertPhysOperDestroy(InsertPhysicalOperator *insertPhysOper)
{
  if (insertPhysOper == NULL) {
    return;
  }
  if (insertPhysOper->children != NULL) {
    varArrayListDestroy(&insertPhysOper->children);
  }
  if (insertPhysOper->valuelists != NULL) {
    varArrayListDestroy(&insertPhysOper->valuelists);
  }
  my_free(insertPhysOper);
}

void InsertPhysOperPointerDestroy(void *data)
{
  InsertPhysicalOperator **insertPhysOper = (InsertPhysicalOperator **)data;
  if (insertPhysOper == NULL || *insertPhysOper == NULL) {
    return;
  }
  InsertPhysOperDestroy(*insertPhysOper);
  *insertPhysOper = NULL;
}
