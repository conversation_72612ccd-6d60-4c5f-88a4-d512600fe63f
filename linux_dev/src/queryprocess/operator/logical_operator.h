/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-23 21:01:00
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-04-23 21:13:18
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/logical_operator.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef LOGICAL_OPERATOR_H
#define LOGICAL_OPERATOR_H
#include "expression.h"
#include "hashmap.h"
#include "vararraylist.h"

typedef struct LogicalOperator
{
  LogicalOperatorType type;
  varArrayList       *children;           // 子算子element type:<LogicalOperator*>
  varArrayList       *expressions;        // 表达式，比如select中的列，where中的谓词等等，都可以使用表达式来表示element
} LogicalOperator;
void LogicalOperatorToString(LogicalOperator *logical_operator, int depth);
void LogicalOperatorDestroy(LogicalOperator **logical_operator);
void LogicalOperatorPointerDestroy(void *data);
void LogicalPlanDestroy(LogicalOperator **logOper);
#endif  // LOGICAL_OPERATOR_H