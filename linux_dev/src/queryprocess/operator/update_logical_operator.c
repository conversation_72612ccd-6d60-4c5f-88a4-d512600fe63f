#include "update_logical_operator.h"
void UpdateLogiOperDestroy(UpdateLogicalOperator *updateLogiOper)
{
  if (updateLogiOper == NULL) {
    return;
  }

  if (updateLogiOper->children != NULL) {
    varArrayListDestroy(&updateLogiOper->children);
  }

  if (updateLogiOper->expressions != NULL) {
    varArrayListDestroy(&updateLogiOper->expressions);
  }

  if (updateLogiOper->updateFieldNames != NULL) {
    varArrayListDestroy(&updateLogiOper->updateFieldNames);
  }

  if (updateLogiOper->updateValues != NULL) {
    varArrayListDestroy(&updateLogiOper->updateValues);
  }

  my_free(updateLogiOper);
}

void UpdateLogiOperPointerDestroy(void *data)
{
  UpdateLogicalOperator **updateLogiOper = (UpdateLogicalOperator **)data;
  if (updateLogiOper == NULL || *updateLogiOper == NULL) {
    return;
  }

  UpdateLogiOperDestroy(*updateLogiOper);
  *updateLogiOper = NULL;
}
