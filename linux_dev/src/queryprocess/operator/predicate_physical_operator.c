#include "predicate_physical_operator.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "utils.h"
#include "value.h"
#include "vararraylist.h"
#include <string.h>
int PredicatePhysOperInit(PredicatePhysicalOperator *predicatePhysOper, PhysicalOperatorType type)
{
  predicatePhysOper->type         = type;
  predicatePhysOper->children     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  predicatePhysOper->parentTuple  = NULL;
  predicatePhysOper->expression   = NULL;
  predicatePhysOper->currentTuple = NULL;
  return GNCDB_SUCCESS;
}

int PredicatePhysOperOpen(PredicatePhysicalOperator *predicatePhysOper, SQLStageEvent *sqlEvent)
{
  PhysicalOperator *childOperator = NULL;
  int               rc            = GNCDB_SUCCESS;
  
  /*1.参数非空性检查*/
  if (predicatePhysOper == NULL || sqlEvent == NULL) {
    return GNCDB_PARAM_INVALID;
  }

  /*2.过滤谓词的子算子有且只有一个*/
  if (predicatePhysOper->children == NULL || predicatePhysOper->children->elementCount != 1) {
    return GNCDB_INTERNAL;
  }
  childOperator = (PhysicalOperator *)varArrayListGetPointer(predicatePhysOper->children, 0);
  rc            = PhysicalOperatorOpen(childOperator, sqlEvent);
  return (rc == GNCDB_NEXT_EOF) ? GNCDB_SUCCESS : rc;
}

int PredicatePhysOperNext(PredicatePhysicalOperator *predicatePhysOper, SQLStageEvent *sqlEvent)
{
  int               rc            = GNCDB_SUCCESS;
  PhysicalOperator *childOperator = NULL;
  Record           *tuple         = NULL;
  Value            *value         = NULL;
  // SubQueryTuple    *tp            = NULL;
  varArrayList *colMetas     = NULL;
  Record       *complexTuple = NULL;
  Record       *realTuple    = NULL;
  varArrayList *realCols     = NULL;

  childOperator = (PhysicalOperator *)varArrayListGetPointer(predicatePhysOper->children, 0);
  colMetas      = getColMeta(childOperator);
  if (colMetas == NULL) {
    return GNCDB_INTERNAL;
  }
  value = valueCreate();
  if (value == NULL) {
    return GNCDB_MEM;
  }
  /*从子算子中获取一条满足过滤条件的元组，每次返回一条，内部的逻辑和filter算子很像*/
  while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext(childOperator, sqlEvent))) {
    tuple = GetCurrentTuple(childOperator);
    if (NULL == tuple) {
      rc = GNCDB_INTERNAL;
      break;
    }
    if (predicatePhysOper->parentTuple != NULL) {
      complexTuple = RecordMerge(predicatePhysOper->parentTuple, tuple);
      if (complexTuple == NULL) {
        valueDestroy(&value);
        return GNCDB_INTERNAL;
      }
      if (predicatePhysOper->complexCols == NULL) {
        predicatePhysOper->complexCols = colMetaListMerge(predicatePhysOper->parentCols, predicatePhysOper->cols);
        if (predicatePhysOper->complexCols == NULL) {
          valueDestroy(&value);
          RecordDestroy(&complexTuple);
          return GNCDB_INTERNAL;
        }
      }
    }
    if (predicatePhysOper->expression == NULL) {
      predicatePhysOper->currentTuple = tuple;
      valueDestroy(&value);
      RecordDestroy(&complexTuple);
      return rc;
    }
    realTuple = complexTuple ? complexTuple : tuple;
    realCols  = predicatePhysOper->complexCols ? predicatePhysOper->complexCols : colMetas;
    // tp    = SubQueryTupleCreate(predicatePhysOper->parentTuple, tuple);
    rc = expressionGetValueNCP(predicatePhysOper->expression, realTuple, realCols, value);
    if (rc != GNCDB_SUCCESS) {
      // tupleDestroy((AbstractTuple *)tp);
      valueDestroy(&value);
      RecordDestroy(&complexTuple);
      return rc;
    }
    if (valueGetBoolean(value)) {
      // 返回符合条件的tuple
      // tupleDestroy((AbstractTuple *)tp);
      valueDestroy(&value);
      predicatePhysOper->currentTuple = tuple;
      RecordDestroy(&complexTuple);
      return rc;
    }
    RecordDestroy(&complexTuple);
    valueReset(value);
  }
  valueDestroy(&value);
  RecordDestroy(&complexTuple);
  return rc;
}

BtreeCursor *PredicatePhysOperGetScanCursor(PredicatePhysicalOperator *predicatePhysOper)
{
  return GetScanCursor((PhysicalOperator *)varArrayListGetPointer(predicatePhysOper->children, 0));
}

int PredicateSetColMeta(PredicatePhysicalOperator *predicatePhysOper)
{
  int               i             = 0;
  PhysicalOperator *child         = NULL;
  varArrayList     *cols          = NULL;
  Meta             *col           = NULL;

  if (predicatePhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (predicatePhysOper->children == NULL || predicatePhysOper->children->elementCount == 0) {
    return GNCDB_SUCCESS;  // 这里返回SUCCESS是因为如果where clause是永假的，其子操作符会被直接delete掉
  }
  child = (PhysicalOperator *)varArrayListGetPointer(predicatePhysOper->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }

  cols = getColMeta(child);
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }

  predicatePhysOper->cols = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);

  for (i = 0; i < cols->elementCount; i++) {
    col = varArrayListGetPointer(cols, i);
    varArrayListAddPointer(predicatePhysOper->cols, col);
  }

  return GNCDB_SUCCESS;
}

Record *PredicatePhysOperGetCurrentTuple(PredicatePhysicalOperator *predicatePhysOper)
{
  return PTR_MOVE((void **)&predicatePhysOper->currentTuple);
}

int PredicatePhysOperClose(PredicatePhysicalOperator *predicatePhysOper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  rc     = PhysicalOperatorClose((PhysicalOperator *)varArrayListGetPointer(predicatePhysOper->children, 0), sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

void PredicatePhysOperDestroy(PredicatePhysicalOperator *predicatePhysOper)
{
  if (predicatePhysOper == NULL) {
    return;
  }

  if (predicatePhysOper->children != NULL) {
    varArrayListDestroy(&predicatePhysOper->children);
  }

  if (predicatePhysOper->cols != NULL) {
    varArrayListDestroy(&predicatePhysOper->cols);
  }

  if (predicatePhysOper->expression != NULL) {
    exprDestroy(predicatePhysOper->expression);
  }

  if (predicatePhysOper->complexCols != NULL) {
    varArrayListDestroy(&predicatePhysOper->complexCols);
  }

  my_free(predicatePhysOper);
}

void PredicatePhysOperPointerDestroy(void *data)
{
  PredicatePhysicalOperator **predicatePhysOper = (PredicatePhysicalOperator **)data;
  if (predicatePhysOper == NULL || *predicatePhysOper == NULL) {
    return;
  }

  PredicatePhysOperDestroy(*predicatePhysOper);
  *predicatePhysOper = NULL;
}