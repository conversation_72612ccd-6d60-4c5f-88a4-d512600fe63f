/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-23 21:00:59
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-04-25 22:02:13
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/table_get_logical_operator.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "table_get_logical_operator.h"

void TableGetLogiOperDestroy(TableGetLogicalOperator *tableGetLogiOper)
{
  if (tableGetLogiOper == NULL) {
    return;
  }

  if (tableGetLogiOper->children != NULL) {
    varArrayListDestroy(&tableGetLogiOper->children);
  }

  if (tableGetLogiOper->expressions != NULL) {
    varArrayListDestroy(&tableGetLogiOper->expressions);
  }

  if (tableGetLogiOper->fields != NULL) {
    varArrayListDestroy(&tableGetLogiOper->fields);
  }

  my_free(tableGetLogiOper);
}

void TableGetLogiOperPointerDestroy(void *data)
{
  TableGetLogicalOperator **tableGetLogiOper = (TableGetLogicalOperator **)data;
  if (tableGetLogiOper == NULL || *tableGetLogiOper == NULL) {
    return;
  }
  TableGetLogiOperDestroy(*tableGetLogiOper);
  *tableGetLogiOper = NULL;
}
