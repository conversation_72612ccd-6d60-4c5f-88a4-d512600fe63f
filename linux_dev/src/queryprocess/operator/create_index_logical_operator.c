#include "create_index_logical_operator.h"

void CreateIndexLogiOperDestroy(CreateIndexLogicalOperator *createIndexLogiOper)
{
  if (createIndexLogiOper == NULL) {
    return;
  }
  if (createIndexLogiOper->children != NULL) {
    varArrayListDestroy(&createIndexLogiOper->children);
  }
  if (createIndexLogiOper->expressions != NULL) {
    varArrayListDestroy(&createIndexLogiOper->expressions);
  }
  if (createIndexLogiOper->indexName != NULL) {
    my_free(createIndexLogiOper->indexName);
  }
  if (createIndexLogiOper->relationName != NULL) {
    my_free(createIndexLogiOper->relationName);
  }
  if (createIndexLogiOper->attributeName != NULL) {
    my_free(createIndexLogiOper->attributeName);
  }
  my_free(createIndexLogiOper);
}

void CreateIndexLogiOperPointerDestroy(void *data)
{
  CreateIndexLogicalOperator **createIndexLogiOper = (CreateIndexLogicalOperator **)data;
  if (createIndexLogiOper == NULL || *createIndexLogiOper == NULL) {
    return;
  }
  CreateIndexLogiOperDestroy(*createIndexLogiOper);
  *createIndexLogiOper = NULL;
}

CreateIndexLogicalOperator *CreateIndexLogiOperMove(CreateIndexLogicalOperator **createIndexLogiOper)
{
  CreateIndexLogicalOperator *result = NULL;
  if (createIndexLogiOper == NULL || *createIndexLogiOper == NULL) {
    return NULL;
  }
  result               = *createIndexLogiOper;
  *createIndexLogiOper = NULL;
  return result;
}