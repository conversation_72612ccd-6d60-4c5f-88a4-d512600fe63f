#ifndef PROJECT_PHYSICAL_OPERATOR_H_
#define PROJECT_PHYSICAL_OPERATOR_H_
#include "exec_tuple.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "vararraylist.h"
typedef struct TupleSchema
{
  varArrayList *columnList;
} TupleSchema;
typedef struct ProjectPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;  // 提供给上层调用获取指定字段的值的描述
  SQLStageEvent       *sqlEvent;    // SQL生命周期上下文
  int                  recordSize;  // 该投影算子输出的记录大小
  ProjectTuple        *tuple;
  bool                 isDistinct;
  HashMap             *distinctMap;
  TupleSchema         *schema;
  Record              *currentTuple;
  varArrayList        *projectedFields;
} ProjectPhysicalOperator;
void          ProjectPhysOperInit(ProjectPhysicalOperator *projectPhysOper);
int           ProjectPhysOperOpen(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent);
int           ProjectPhysOperNext(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent);
int           ProjectPhysOperClose(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent);
int           ProjectPhysOperDistinctFilter(ProjectPhysicalOperator *projectPhysOper, char **fieldValues);
int           ProjectSetColMeta(ProjectPhysicalOperator *projectPhysOper);
varArrayList *ProjectGetCols(ProjectPhysicalOperator *projectPhysOper);
Record       *ProjectPhysOperGetCurrentTuple(ProjectPhysicalOperator *projectPhysOper);
int  ProjectPhysOperGetSchemaTotalSize(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent, int *len);
void ProjectPhysOperDestroy(ProjectPhysicalOperator *projectPhysOper);
void ProjectPhysOperPointerDestroy(void *data);
#endif  // PROJECT_PHYSICAL_OPERATOR_H_.