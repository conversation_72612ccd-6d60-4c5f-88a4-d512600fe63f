#include "update_physical_operator.h"
#include "btreepage.h"
#include "catalog.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "pagepool.h"
#include "physical_operator.h"
#include "hash.h"
#include "sql_event.h"
#include "typedefine.h"
#include "value.h"
#include "vararraylist.h"
#include "gncdb.h"
#include <stdio.h>
#include <string.h>
int SqlExecuteUpdate(struct GNCDB *db, int *affectedRows, char *tableName, varArrayList *updateFieldNameArray,
    varArrayList *ValueList, BYTE *record, Transaction *tx, BtreePage *page)
{
  int          rc             = 0;
  BtreeTable  *table          = NULL;
  TableSchema *tableSchema    = NULL;
  int          i              = 0;
  int          j              = 0;
  char        *fieldName      = NULL;
  Column      *column         = NULL;
  int         *fieldValueInt  = NULL;
  int         *fieldValueDate = NULL;
  char        *fieldValueStr  = NULL;
  DateTime     fieldValueDateTime;
  int          len              = 0;
  double      *fieldValueDouble = NULL;
  int          setNum           = updateFieldNameArray->elementCount;
  void        *updateFieldValue = NULL;

  varArrayList *updateFieldValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  Value        *val                   = NULL;

  /*1.表名不能是系统表*/
  if (strcmp(tableName, MASTERNAME) == 0 || strcmp(tableName, SCHEMANAME) == 0) {
    varArrayListDestroy(&updateFieldValueArray);
    return GNCDB_NOT_REFACTOR;
  }

  /*2.获取表和表元信息*/
  rc = catalogGetTable(db->catalog, &table, tableName);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&updateFieldValueArray);
    return rc;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema->tableParam == '0') {
    varArrayListDestroy(&updateFieldValueArray);
    return GNCDB_TABLE_PARAM_FALSE;
  }

  /*3.对更新的值进行合法性检查 */
  for (i = 0; i < setNum; i++) {
    /*3.1在表元信息中找到要更新的列*/
    fieldName = varArrayListGetPointer(updateFieldNameArray, i);
    for (j = 0; j < tableSchema->columnList->elementCount; j++) {
      column = (Column *)varArrayListGetPointer(tableSchema->columnList, j);
      if (strcmp(column->fieldName, fieldName) == 0) {
        break;
      } else {
        column = NULL;
      }
    }
    if (column == NULL) {
      /*没找到，非法更新列*/
      varArrayListDestroy(&updateFieldValueArray);
      return GNCDB_PARAM_INVALID;
    }
    /*3.2若该列是主键，主键不可更改*/
    if (column->columnConstraint->isPrimaryKey) {
      varArrayListDestroy(&updateFieldValueArray);
      return GNCDB_PRIMARY_KEY_IMMUTABLE;
    }
    /*3.4对要更新的新值进行列约束检查*/
    switch (column->fieldType) {
      case FIELDTYPE_DATE: {
        val = varArrayListGetPointer(ValueList, i);
        if (val->attrType == NULLS) {
          if (column->columnConstraint->canBeNull == false) {
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, NULL);
        } else {
          fieldValueDate  = my_malloc(sizeof(int));
          *fieldValueDate = val->numValue.intValue;
          if (*fieldValueDate < column->columnConstraint->minValue ||
              *fieldValueDate > column->columnConstraint->maxValue) {
            my_free(fieldValueDate);
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, fieldValueDate);
        }
        break;
      }
      case FIELDTYPE_INTEGER: {
        val = varArrayListGetPointer(ValueList, i);
        if (val->attrType == NULLS) {
          if (column->columnConstraint->canBeNull == false) {
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, NULL);
        } else {
          fieldValueInt  = my_malloc(sizeof(int));
          *fieldValueInt = val->numValue.intValue;
          if (*fieldValueInt < column->columnConstraint->minValue ||
              *fieldValueInt > column->columnConstraint->maxValue) {
            my_free(fieldValueInt);
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, fieldValueInt);
        }
        break;
      }
      case FIELDTYPE_REAL: {
        val = varArrayListGetPointer(ValueList, i);
        if (val->attrType == NULLS) {
          if (column->columnConstraint->canBeNull == false) {
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, NULL);
        } else {
          fieldValueDouble  = my_malloc(sizeof(double));
          *fieldValueDouble = val->numValue.doubleValue;
          if (*fieldValueDouble < column->columnConstraint->minValue ||
              *fieldValueDouble > column->columnConstraint->maxValue) {
            my_free(fieldValueDouble);
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, fieldValueDouble);
        }

        break;
      }
      case FIELDTYPE_VARCHAR: {
        val = varArrayListGetPointer(ValueList, i);
        if (val->attrType == NULLS) {
          if (column->columnConstraint->canBeNull == false) {
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, NULL);
        } else {
          len           = strlen(val->strValue);
          fieldValueStr = my_malloc(sizeof(char) * (len + 1));
          strcpy(fieldValueStr, val->strValue);
          if (len < column->columnConstraint->minValue || len > column->columnConstraint->maxValue) {
            my_free(fieldValueStr);
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, fieldValueStr);
        }

        break;
      }
      case FIELDTYPE_BLOB: {

        break;
      }
      case FIELDTYPE_DATETIME: {
        val = varArrayListGetPointer(ValueList, i);
        if (val->attrType == NULLS) {
          if (column->columnConstraint->canBeNull == false) {
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, NULL);
        } else {
          fieldValueDateTime = val->numValue.datetimeValue;
          if (fieldValueDateTime.date < column->columnConstraint->minValue ||
              fieldValueDateTime.date > column->columnConstraint->maxValue) {
            my_free(fieldValueStr);
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          if (fieldValueDateTime.time < column->columnConstraint->minValue ||
              fieldValueDateTime.time > column->columnConstraint->maxValue) {
            my_free(fieldValueStr);
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          fieldValueStr = datetimeToString(&fieldValueDateTime);
          varArrayListAddPointer(updateFieldValueArray, fieldValueStr);
        }
        break;
      }
      /* TEXT类型 */
      case FIELDTYPE_TEXT: {
        val = varArrayListGetPointer(ValueList, i);
        if (val->attrType == NULLS) {
          if (column->columnConstraint->canBeNull == false) {
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, NULL);
        } else {
          fieldValueStr = val->strValue;
          len           = strlen(fieldValueStr);
          if (len / 8 < column->columnConstraint->minValue || len / 8 > column->columnConstraint->maxValue) {
            my_free(fieldValueStr);
            varArrayListDestroy(&updateFieldValueArray);
            return GNCDB_PARAM_INVALID;
          }
          varArrayListAddPointer(updateFieldValueArray, fieldValueStr);
        }
        break;
      }
      default: break;
    }
  }

  /*4.在b+树中进行物理更新*/
  rc = btreeTableUpdateTuple(table, updateFieldNameArray, updateFieldValueArray, tableSchema, record, db, tx, page);
  for (i = updateFieldValueArray->elementCount; i > 0; i--) {
    updateFieldValue = varArrayListGetPointer(updateFieldValueArray, i - 1);
    my_free(updateFieldValue);
  }
  varArrayListDestroy(&updateFieldValueArray);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  if (affectedRows != NULL) {
    *affectedRows += 1;
  }

  return GNCDB_SUCCESS;
}

void UpdatePhysOperInit(UpdatePhysicalOperator *UpdatePhysicalOperator)
{
  UpdatePhysicalOperator->type        = PO_UPDATE;
  UpdatePhysicalOperator->children    = varArrayListCreate(0, BYTES_POINTER, 0, NULL, NULL);
  UpdatePhysicalOperator->parentTuple = NULL;
}

int UpdatePhysOperOpen(UpdatePhysicalOperator *UpdatePhysicalOperator, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = NULL;
  /*更新算子有且只有一个算子*/
  if (UpdatePhysicalOperator->children == NULL || UpdatePhysicalOperator->children->elementCount == 0) {
    return GNCDB_SUCCESS;
  }
  /*open子算子*/
  child = varArrayListGetPointer(UpdatePhysicalOperator->children, 0);
  rc    = PhysicalOperatorOpen(child, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    printf("failed to open child operator:%d\n", rc);
    return rc;
  }

  return rc;
}

int UpdatePhysOperNext(UpdatePhysicalOperator *UpdatePhysicalOperator, SQLStageEvent *sqlEvent)
{
  int               rc            = GNCDB_SUCCESS;
  int               i             = 0;
  PhysicalOperator *child         = NULL;
  Record           *tuple         = NULL;
  varArrayList     *updateValues  = NULL;
  Expression       *expression    = NULL;
  Value            *value         = NULL;
  varArrayList     *hashIndexList = NULL; /*表上的索引实例指针List<HashIndex *>*/
  HashMap          *colIndexMap   = NULL; /*hashmap<fieldName, hashIdx>*/
  HashMap          *fieldNameMap  = NULL;
  char             *fieldName     = NULL;
  Column           *indexCol      = NULL;
  HashIndex        *hashIndex     = NULL;
  TableSchema      *tableSchema   = NULL;
  varArrayList     *pkValueArray  = NULL;
  void             *keyValue      = NULL;
  int               hashValue     = 0;
  MetaPage         *metaPage      = NULL;
  BtreePage        *page          = NULL;
  BYTE             *record        = NULL;

  if (UpdatePhysicalOperator->children != NULL && UpdatePhysicalOperator->children->elementCount != 0) {
    child = (PhysicalOperator *)varArrayListGetPointer(UpdatePhysicalOperator->children, 0);

    //* 检查每列上是否有索引
    hashIndexList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    fieldNameMap  = hashMapCreate(STRKEY, 0, NULL);
    colIndexMap   = hashMapCreate(STRKEY, 0, NULL);

    //* 1.为所有更新列创建map
    for (int i = 0; i < UpdatePhysicalOperator->updateFieldNames->elementCount; i++) {
      fieldName = varArrayListGetPointer(UpdatePhysicalOperator->updateFieldNames, i);
      hashMapPut(fieldNameMap, fieldName, NULL);
    }
    //* 2.获取表上的所有索引名称
    rc = catalogGetHashIndexList(sqlEvent->db, hashIndexList, UpdatePhysicalOperator->tableName, sqlEvent->txn);
    if (rc != GNCDB_SUCCESS) {
      hashMapDestroy(&colIndexMap);
      hashMapDestroy(&fieldNameMap);
      varArrayListDestroy(&hashIndexList);
      return rc;
    }
    //* 3.根据索引名获取索引schema
    for (int i = 0; i < hashIndexList->elementCount; i++) {
      hashIndex = varArrayListGetPointer(hashIndexList, i);
      indexCol  = varArrayListGetPointer(hashIndex->index_columns, 0);
      // 4.该列存在索引，获取并存入map
      if (hashMapExists(fieldNameMap, indexCol->fieldName)) {
        hashMapPut(colIndexMap, indexCol->fieldName, hashIndex);
      }
    }
    pkValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    tableSchema  = getTableSchema(sqlEvent->db->catalog, UpdatePhysicalOperator->tableName);
    /*1.更新从子算子获取的每一条记录*/
    while ((rc = PhysicalOperatorNext(child, sqlEvent)) == GNCDB_SUCCESS) {
      tuple  = GetCurrentTuple(child);
      record = GetCurrentRecord(child);
      if (tuple == NULL) {
        varArrayListDestroy(&updateValues);
        hashMapDestroy(&colIndexMap);
        hashMapDestroy(&fieldNameMap);
        varArrayListDestroy(&hashIndexList);
        varArrayListDestroy(&pkValueArray);
        return GNCDB_TUPLE_NOT_FOUND;
      }
      /*1.1要更新的记录所在的页*/
      page         = GetCurrentPage(child);
      updateValues = varArrayListCreate(DISORDER, sizeof(Value *), 0, NULL, valuePointerDestroy);
      /*1.2根据元组以及更新表达式计算更新值数组*/
      for (i = 0; i < UpdatePhysicalOperator->updateFieldNames->elementCount; i++) {
        expression = varArrayListGetPointer(UpdatePhysicalOperator->updateValues, i);
        value      = valueCreate();
        if ((rc = expressionGetValueNCP(expression, tuple, UpdatePhysicalOperator->cols, value)) != GNCDB_SUCCESS) {
          printf("failed to get value\n");
          valueDestroy(&value);
          varArrayListDestroy(&updateValues);
          hashMapDestroy(&colIndexMap);
          hashMapDestroy(&fieldNameMap);
          varArrayListDestroy(&hashIndexList);
          varArrayListDestroy(&pkValueArray);
          return rc;
        }
        varArrayListAddPointer(updateValues, value);
      }
      /*1.3更新B+树上对应位置的记录*/
      rc = SqlExecuteUpdate(sqlEvent->db,
          &sqlEvent->affectedRows,
          UpdatePhysicalOperator->tableName,
          UpdatePhysicalOperator->updateFieldNames,
          updateValues,
          record,
          sqlEvent->txn,
          page);

      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&updateValues);
        hashMapDestroy(&colIndexMap);
        hashMapDestroy(&fieldNameMap);
        varArrayListDestroy(&hashIndexList);
        varArrayListDestroy(&pkValueArray);
        return rc;
      }

      // *更新哈希索引
      for (i = 0; i < UpdatePhysicalOperator->updateFieldNames->elementCount; i++) {
        fieldName = varArrayListGetPointer(UpdatePhysicalOperator->updateFieldNames, i);
        if (!hashMapExists(colIndexMap, fieldName)) {
          continue;
        }
        hashIndex = hashMapGet(colIndexMap, fieldName);
        indexCol  = varArrayListGetPointer(hashIndex->index_columns, 0);

        rc = leafTupleGetKeyValue(
            pkValueArray, tuple->data, sqlEvent->db->catalog, tableSchema, UpdatePhysicalOperator->tableName);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&updateValues);
          hashMapDestroy(&colIndexMap);
          hashMapDestroy(&fieldNameMap);
          varArrayListDestroy(&hashIndexList);
          varArrayListDestroy(&pkValueArray);
          return rc;
        }
        // 从哈希索引中删除旧值
        keyValue  = tuple->data + indexCol->offset;
        hashValue = getHashValue(keyValue, indexCol->fieldType);
        rc        = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, hashIndex->tableName, sqlEvent->db);
        rc        = hashIndexDelete(hashIndex, hashValue, pkValueArray, sqlEvent->db, sqlEvent->txn);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&updateValues);
          hashMapDestroy(&colIndexMap);
          hashMapDestroy(&fieldNameMap);
          varArrayListDestroy(&hashIndexList);
          varArrayListDestroy(&pkValueArray);
          return rc;
        }
        // 从哈希索引插入新值
        value = varArrayListGetPointer(updateValues, i);
        if (indexCol->fieldType == FIELDTYPE_VARCHAR) {
          hashValue = getHashValue(value->strValue, indexCol->fieldType);
        } else {
          hashValue = getHashValue(&(value->numValue), indexCol->fieldType);
        }
        rc = hashIndexInsert(hashIndex, hashValue, pkValueArray, false, sqlEvent->db, sqlEvent->txn);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&updateValues);
          hashMapDestroy(&colIndexMap);
          hashMapDestroy(&fieldNameMap);
          varArrayListDestroy(&hashIndexList);
          varArrayListDestroy(&pkValueArray);
          return rc;
        }

        varArrayListClear(pkValueArray);
      }

      varArrayListDestroy(&updateValues);
    }
    hashMapDestroy(&colIndexMap);
    hashMapDestroy(&fieldNameMap);
    varArrayListDestroy(&hashIndexList);
    varArrayListDestroy(&pkValueArray);
    return rc;
  } else {
    return GNCDB_NEXT_EOF;
  }
}

int UpdatePhysOperClose(UpdatePhysicalOperator *updatePhysicalOperator, SQLStageEvent *sql_event)
{
  int rc = GNCDB_SUCCESS;
  if (updatePhysicalOperator->children != NULL && updatePhysicalOperator->children->elementCount != 0) {
    rc = PhysicalOperatorClose(varArrayListGetPointer(updatePhysicalOperator->children, 0), sql_event);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return GNCDB_SUCCESS;
}

int UpdateSetColMeta(UpdatePhysicalOperator *updatePhysicalOperator)
{
  int               i     = 0;
  PhysicalOperator *child = NULL;
  varArrayList     *cols  = NULL;
  Meta             *col   = NULL;

  if (updatePhysicalOperator == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (updatePhysicalOperator->children == NULL || updatePhysicalOperator->children->elementCount == 0) {
    /*这里返回SUCCESS是因为如果where clause是永假的，其子操作符会被直接delete掉*/
    return GNCDB_SUCCESS;
  }
  child = (PhysicalOperator *)varArrayListGetPointer(updatePhysicalOperator->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }

  cols = getColMeta(child);
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }

  updatePhysicalOperator->cols = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);

  for (i = 0; i < cols->elementCount; i++) {
    col = varArrayListGetPointer(cols, i);
    varArrayListAddPointer(updatePhysicalOperator->cols, col);
  }

  return GNCDB_SUCCESS;
}

void UpdatePhysOperDestroy(UpdatePhysicalOperator *updatePhysicalOperator)
{
  if (updatePhysicalOperator == NULL) {
    return;
  }

  if (updatePhysicalOperator->children != NULL) {
    varArrayListDestroy(&updatePhysicalOperator->children);
  }

  if (updatePhysicalOperator->cols != NULL) {
    varArrayListDestroy(&updatePhysicalOperator->cols);
  }

  if (updatePhysicalOperator->updateValues != NULL) {
    varArrayListDestroy(&updatePhysicalOperator->updateValues);
  }

  if (updatePhysicalOperator->updateFieldNames != NULL) {
    varArrayListDestroy(&updatePhysicalOperator->updateFieldNames);
  }

  if (updatePhysicalOperator->tableName != NULL) {
    my_free(updatePhysicalOperator->tableName);
  }

  my_free(updatePhysicalOperator);
}

void UpdatePhysOperPointerDestroy(void *data)
{
  UpdatePhysicalOperator **updatePhysicalOperator = (UpdatePhysicalOperator **)data;
  if (updatePhysicalOperator == NULL || *updatePhysicalOperator == NULL) {
    return;
  }

  UpdatePhysOperDestroy(*updatePhysicalOperator);
  *updatePhysicalOperator = NULL;
}