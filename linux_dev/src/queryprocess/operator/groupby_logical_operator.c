#include "groupby_logical_operator.h"
void GroupByLogiOperDestroy(GroupByLogicalOperator *groupbyLogiOper)
{
  if (groupbyLogiOper == NULL) {
    return;
  }
  if (groupbyLogiOper->children != NULL) {
    varArrayListDestroy(&groupbyLogiOper->children);
  }
  if (groupbyLogiOper->expressions != NULL) {
    varArrayListDestroy(&groupbyLogiOper->expressions);
  }
  if (groupbyLogiOper->groupbyFields != NULL) {
    varArrayListDestroy(&groupbyLogiOper->groupbyFields);
  }
  if (groupbyLogiOper->aggExprs != NULL) {
    varArrayListDestroy(&groupbyLogiOper->aggExprs);
  }
  if (groupbyLogiOper->fieldExprs != NULL) {
    varArrayListDestroy(&groupbyLogiOper->fieldExprs);
  }
  my_free(groupbyLogiOper);
}

void GroupByLogiOperPointerDestroy(void *data)
{
  GroupByLogicalOperator **groupbyLogiOper = (GroupByLogicalOperator **)data;
  if (groupbyLogiOper == NULL || *groupbyLogiOper == NULL) {
    return;
  }
  GroupByLogiOperDestroy(*groupbyLogiOper);
  *groupbyLogiOper = NULL;
}
