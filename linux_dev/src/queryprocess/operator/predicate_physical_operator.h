/*
 * @Author: zql <EMAIL>
 * @Date: 2025-05-27 09:52:26
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-07-14 17:23:56
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/predicate_physical_operator.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef PREDICATE_PHSICAL_OPERATOR_H
#define PREDICATE_PHSICAL_OPERATOR_H
#include "exec_tuple.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "vararraylist.h"
typedef struct PredicatePhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文
  Expression          *expression;
  Record              *currentTuple;
  varArrayList        *complexCols;
} PredicatePhysicalOperator;
int          PredicatePhysOperInit(PredicatePhysicalOperator *predicatePhysOper, PhysicalOperatorType type);
int          PredicatePhysOperOpen(PredicatePhysicalOperator *predicatePhysOper, SQLStageEvent *sqlEvent);
int          PredicatePhysOperNext(PredicatePhysicalOperator *predicatePhysOper, SQLStageEvent *sqlEvent);
int          PredicatePhysOperClose(PredicatePhysicalOperator *predicatePhysOper, SQLStageEvent *sqlEvent);
BtreeCursor *PredicatePhysOperGetScanCursor(PredicatePhysicalOperator *predicatePhysOper);
int          PredicateSetColMeta(PredicatePhysicalOperator *predicatePhysOper);
// varArrayList *PredicateGetCols(PredicatePhysicalOperator *predicatePhysOper);
Record *PredicatePhysOperGetCurrentTuple(PredicatePhysicalOperator *predicatePhysOper);
void    PredicatePhysOperDestroy(PredicatePhysicalOperator *predicatePhysOper);
void    PredicatePhysOperPointerDestroy(void *data);
#endif  // PREDICATE_PHSICAL_OPERATOR_H