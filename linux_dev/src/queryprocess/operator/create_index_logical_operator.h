#ifndef CREATE_INDEX_LOGICAL_OPERATOR_H
#define CREATE_INDEX_LOGICAL_OPERATOR_H
#include "logical_operator.h"
typedef struct CreateIndexLogicalOperator
{
  LogicalOperatorType type;
  varArrayList       *children;
  varArrayList       *expressions;
  GNCDB              *db;
  char               *indexName;
  char               *relationName;
  char               *attributeName;
  IndexType           indexType;
} CreateIndexLogicalOperator;
void                        CreateIndexLogiOperDestroy(CreateIndexLogicalOperator *createIndexLogiOper);
void                        CreateIndexLogiOperPointerDestroy(void *data);
CreateIndexLogicalOperator *CreateIndexLogiOperMove(CreateIndexLogicalOperator **createIndexLogiOper);
#endif  // CREATE_INDEX_LOGICAL_OPERATOR_H