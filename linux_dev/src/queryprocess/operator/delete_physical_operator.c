#include "delete_physical_operator.h"
#include "btreecursor.h"
#include "btreetable.h"
#include "catalog.h"
#include "exec_tuple.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "queryexecutor.h"
#include "sql_event.h"
#include "gncdb.h"
#include "expression.h"
#include <string.h>
#include <time.h>
#include "vararraylist.h"

int txnExecutorDelete(char *tableName, BYTE *record, SQLStageEvent *sqlEvent, BtreeCursor *cursor, BtreePage *btreePage,
    int index, BtreeTable *table, TableSchema *tableSchema)
{
  int           rc            = 0;
  varArrayList *keyValueArray = NULL;
  int          *updatedPageId = NULL;
  if (tableName == NULL || record == NULL || sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 用于更新cursor */
  updatedPageId = (int *)my_malloc0(sizeof(int));
  if (updatedPageId == NULL) {
    return GNCDB_SPACE_LACK;
  }

  /* 删除数据 */
  keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (keyValueArray == NULL) {
    my_free(updatedPageId);
    return GNCDB_SPACE_LACK;
  }

  rc = leafTupleGetKeyValue(keyValueArray, record, sqlEvent->db->catalog, tableSchema, tableName);
  if (rc != GNCDB_SUCCESS) {
    my_free(updatedPageId);
    varArrayListDestroy(&keyValueArray);
    return rc;
  }

  *updatedPageId = -1;
  rc             = btreeTableDeleteTuple(
      table, keyValueArray, tableSchema, sqlEvent->db, sqlEvent->txn, updatedPageId, btreePage, index);
  if (rc != GNCDB_SUCCESS) {
    my_free(updatedPageId);
    varArrayListDestroy(&keyValueArray);
    return rc;
  }
  (sqlEvent->affectedRows) += 1;
  varArrayListDestroy(&keyValueArray);

  /* cursor的更新 */
  if (*updatedPageId != -1) {
    rc = updateCursor(cursor, sqlEvent->db, table->tableName, *updatedPageId);
    if (rc != GNCDB_SUCCESS) {
      my_free(updatedPageId);
      varArrayListDestroy(&keyValueArray);
      return rc;
    }
  }
  (cursor)->currentTupleIndex -= 1;
  my_free(updatedPageId);
  return GNCDB_SUCCESS;
}

int DeletePhysOperInit(DeletePhysicalOperator *deletePhysOper, PhysicalOperatorType type)
{
  deletePhysOper->type        = type;
  deletePhysOper->children    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  deletePhysOper->parentTuple = NULL;

  deletePhysOper->table  = NULL;
  deletePhysOper->txn    = NULL;
  deletePhysOper->cursor = NULL;
  return GNCDB_SUCCESS;
}

int DeletePhysOperOpen(DeletePhysicalOperator *deletePhysOper, SQLStageEvent *sqlEvent)
{
  int               rc     = GNCDB_SUCCESS;
  PhysicalOperator *child  = NULL;
  BtreeCursor      *cursor = NULL;
  if (deletePhysOper == NULL || sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*1.delete算子有且只有一个子算子*/
  if (deletePhysOper->children == NULL || deletePhysOper->children->elementCount == 0) {
    return GNCDB_INTERNAL;
  }
  child = (PhysicalOperator *)varArrayListGetPointer(deletePhysOper->children, 0);
  rc    = PhysicalOperatorOpen((PhysicalOperator *)child, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /*2.获取相应的cursor*/
  cursor = GetScanCursor((PhysicalOperator *)deletePhysOper);
  if (cursor == NULL) {
    return GNCDB_INTERNAL;
  }
  deletePhysOper->cursor = cursor;
  deletePhysOper->txn    = sqlEvent->txn;
  return GNCDB_SUCCESS;
}

int DeletePhysOperNext(DeletePhysicalOperator *deletePhysOper, SQLStageEvent *sqlEvent)
{
  int               rc                = GNCDB_SUCCESS;
  PhysicalOperator *child             = NULL;
  Record           *tuple             = NULL;
  BtreePage        *btreePage         = NULL;
  int               index             = -1;
  int               leafTupleMinCount = 0;
  TableSchema      *tableSchema       = NULL;
  BtreeTable       *table             = NULL;
  varArrayList     *hashIndexList     = NULL;
  varArrayList     *pkValueArray      = NULL;
  varArrayList     *hkValueArray      = NULL;
  /*1.参数检查*/
  if (deletePhysOper->children == NULL || deletePhysOper->children->elementCount == 0) {
    return GNCDB_NEXT_EOF;
  }
  /*2.获取要删除的记录所在的表和模式*/
  table = deletePhysOper->table;
  if (table == NULL) {
    return GNCDB_PARAMNULL;
  }

  tableSchema = getTableSchema(sqlEvent->db->catalog, table->tableName);
  if (tableSchema == NULL) {
    return GNCDB_PARAMNULL;
  }

  child = (PhysicalOperator *)varArrayListGetPointer(deletePhysOper->children, 0);
  tableSchema   = getTableSchema(sqlEvent->db->catalog, deletePhysOper->table->tableName);
  hashIndexList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  pkValueArray  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  hkValueArray  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  rc            = catalogGetHashIndexList(sqlEvent->db, hashIndexList, deletePhysOper->table->tableName, sqlEvent->txn);
  if (rc != GNCDB_SUCCESS || hashIndexList == NULL) {
    return rc;
  }
  /*3.从子算子获取要删除的记录，这里是全部删完而不是删一条就返回*/
  while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext((PhysicalOperator *)child, sqlEvent))) {
    tuple = GetCurrentTuple((PhysicalOperator *)child);
    if (tuple == NULL) {
      return rc;
    }
    /* 先删除索引 */
    if (hashIndexList->elementCount != 0) {
      rc = leafTupleGetKeyValue(pkValueArray, tuple->data, sqlEvent->db->catalog, tableSchema, table->tableName);
      rc = executorBatchDeleteHashIndex(
          sqlEvent->db, tableSchema, tuple->data, hashIndexList, pkValueArray, hkValueArray, sqlEvent->txn);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      varArrayListClear(pkValueArray);
    }
    /*3.1获取要删除的记录所在的页*/
    btreePage = GetCurrentPage((PhysicalOperator *)child);
    if (btreePage == NULL) {
 
      return GNCDB_INTERNAL;
    }
    /*3.2获取要删除的记录在对应的页的index*/
    index = GetCurrentRecordIndex((PhysicalOperator *)child);
    /*3.3判断删除该记录后是否会导致该页的数目变少*/
    leafTupleMinCount = getLeafTupleMinCount(sqlEvent->db->pageCurrentSize, deletePhysOper->table);
    if (btreePage->entryNum - 1 < leafTupleMinCount) {
      btreePage = NULL;
    }
    /*3.4在B+树上删除对应的记录*/
    rc = txnExecutorDelete(deletePhysOper->table->tableName,
        tuple->data,
        sqlEvent,
        deletePhysOper->cursor,
        btreePage,
        index,
        table,
        tableSchema);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  if (hashIndexList != NULL) {
    varArrayListDestroy(&hashIndexList);
  }
  if (pkValueArray != NULL) {
    varArrayListDestroy(&pkValueArray);
  }
  if (hkValueArray != NULL) {
    varArrayListDestroy(&hkValueArray);
  }
  return GNCDB_NEXT_EOF;
}

int DeletePhysOperClose(DeletePhysicalOperator *deletePhysOper, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = NULL;
  if (deletePhysOper->children != NULL && deletePhysOper->children->elementCount > 0) {
    child = (PhysicalOperator *)varArrayListGetPointer(deletePhysOper->children, 0);
    rc    = PhysicalOperatorClose((PhysicalOperator *)child, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return GNCDB_SUCCESS;
}

int DeleteSetColMeta(DeletePhysicalOperator *deletePhysOper)
{
  int               i     = 0;
  PhysicalOperator *child = NULL;
  varArrayList     *cols  = NULL;
  Meta             *col   = NULL;

  if (deletePhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (deletePhysOper->children == NULL || deletePhysOper->children->elementCount == 0) {
    return GNCDB_SUCCESS;  // 这里返回SUCCESS是因为如果where clause是永假的，其子操作符会被直接delete掉
  }
  child = (PhysicalOperator *)varArrayListGetPointer(deletePhysOper->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }

  cols = getColMeta(child);
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }

  deletePhysOper->cols = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (deletePhysOper->cols == NULL) {
    return GNCDB_MEM;
  }
  for (i = 0; i < cols->elementCount; i++) {
    col = varArrayListGetPointer(cols, i);
    varArrayListAddPointer(deletePhysOper->cols, col);
  }

  return GNCDB_SUCCESS;
}

void DeletePhysOperDestroy(DeletePhysicalOperator *deletePhysOper)
{
  if (deletePhysOper == NULL) {
    return;
  }

  if (deletePhysOper->children != NULL) {
    varArrayListDestroy(&deletePhysOper->children);
  }

  if (deletePhysOper->cols != NULL) {
    varArrayListDestroy(&deletePhysOper->cols);
  }

  my_free(deletePhysOper);
}

void DeletePhysOperPointerDestory(void *data)
{
  DeletePhysicalOperator **deletePhysOper = (DeletePhysicalOperator **)data;
  if (deletePhysOper == NULL || *deletePhysOper == NULL) {
    return;
  }
  DeletePhysOperDestroy(*deletePhysOper);
  *deletePhysOper = NULL;
}
