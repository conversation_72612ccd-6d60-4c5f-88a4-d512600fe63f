#include "physical_operator.h"
#include "btreecursor.h"
#include "delete_physical_operator.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "orderby_stmt.h"
#include "project_physical_operator.h"
#include "sql_event.h"
#include "table_scan_physical_operator.h"
#include "insert_physical_operator.h"
#include "index_scan_physical_operator.h"
#include "predicate_physical_operator.h"
#include "typedefine.h"
#include "update_physical_operator.h"
#include "join_physical_operator.h"
#include "groupby_physical_operator.h"
#include "orderby_physical_operator.h"
#include "limit_physical_operator.h"
#include "create_table_physical_operator.h"
#include "vararraylist.h"
#include <assert.h>
char *physicalOperatorTypeName(PhysicalOperatorType type)
{
  switch (type) {
    case PO_TABLE_SCAN: return my_strdup("TABLE_SCAN");
    case PO_INDEX_SCAN: return my_strdup("INDEX_SCAN");
    case PO_NESTED_LOOP_JOIN: return my_strdup("NESTED_LOOP_JOIN");
    case PO_EXPLAIN: return my_strdup("EXPLAIN");
    case PO_PREDICATE: return my_strdup("PREDICATE");
    case PO_INSERT: return my_strdup("INSERT");
    case PO_DELETE: return my_strdup("DELETE");
    case PO_PROJECT: return my_strdup("PROJECT");
    case PO_STRING_LIST: return my_strdup("STRING_LIST");
    default: return my_strdup("UNKNOWN");
  }
}

int PhysicalOperatorInit(PhysicalOperator *physicalOperator, PhysicalOperatorType type)
{
  physicalOperator->type     = type;
  physicalOperator->children = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  return GNCDB_SUCCESS;
}

int PhysicalOperatorOpen(PhysicalOperator *physicalOperator, SQLStageEvent *sqlEvent)
{
  switch (physicalOperator->type) {
    case PO_INSERT: return InsertPhysOperOpen((InsertPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_TABLE_SCAN: return TableScanPhysOperOpen((TableScanPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_INDEX_SCAN: return IndexScanPhysOperOpen((IndexScanPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_NESTED_LOOP_JOIN:
      return NestedLoopJoinPhysOperOpen((NestedLoopJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_EXPLAIN: return GNCDB_SUCCESS;
    case PO_PREDICATE: return PredicatePhysOperOpen((PredicatePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_DELETE: return DeletePhysOperOpen((DeletePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_PROJECT: return ProjectPhysOperOpen((ProjectPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_STRING_LIST: return GNCDB_SUCCESS;
    case PO_UPDATE: return UpdatePhysOperOpen((UpdatePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_GROUPBY: return GroupByPhysOperOpen((GroupByPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_HASH_JOIN: return HashJoinPhysOperOpen((HashJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_SORT_MERGE_JOIN:
      return SortMergeJoinPhysOperOpen((SortMergeJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_ORDER_BY: return OrderByPhysOperOpen((OrderByPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_CREATE_TABLE: return CreateTablePhysOperOpen((CreateTablePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_LIMIT: return LimitPhysOperOpen((LimitPhysicalOperator *)physicalOperator, sqlEvent);
    default: return GNCDB_SUCCESS;
  }

  return GNCDB_SUCCESS;
}

int PhysicalOperatorNext(PhysicalOperator *physicalOperator, SQLStageEvent *sqlEvent)
{
  switch (physicalOperator->type) {
    case PO_INSERT: return InsertPhysOperNext((InsertPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_TABLE_SCAN: return TableScanPhysOperNext((TableScanPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_INDEX_SCAN: return IndexScanPhysOperNext((IndexScanPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_NESTED_LOOP_JOIN:
      return NestedLoopJoinPhysOperNext((NestedLoopJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_EXPLAIN: return GNCDB_SUCCESS;
    case PO_PREDICATE: return PredicatePhysOperNext((PredicatePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_DELETE: return DeletePhysOperNext((DeletePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_PROJECT: return ProjectPhysOperNext((ProjectPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_STRING_LIST: return GNCDB_SUCCESS;
    case PO_UPDATE: return UpdatePhysOperNext((UpdatePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_GROUPBY: return GroupByPhysOperNext((GroupByPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_HASH_JOIN: return HashJoinPhysOperNext((HashJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_SORT_MERGE_JOIN:
      return SortMergeJoinPhysOperNext((SortMergeJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_ORDER_BY: return OrderByPhysOperNext((OrderByPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_CREATE_TABLE: return CreateTablePhysOperNext((CreateTablePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_LIMIT: return LimitPhysOperNext((LimitPhysicalOperator *)physicalOperator, sqlEvent);
    default: return GNCDB_SUCCESS;
  }
}

int PhysicalOperatorClose(PhysicalOperator *physicalOperator, SQLStageEvent *sqlEvent)
{
  switch (physicalOperator->type) {
    case PO_INSERT: return InsertPhysOperClose((InsertPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_TABLE_SCAN: return TableScanPhysOperClose((TableScanPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_INDEX_SCAN: return IndexScanPhysOperClose((IndexScanPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_NESTED_LOOP_JOIN:
      return NestedLoopJoinPhysOperClose((NestedLoopJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_EXPLAIN: return GNCDB_SUCCESS;
    case PO_PREDICATE: return PredicatePhysOperClose((PredicatePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_DELETE: return DeletePhysOperClose((DeletePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_PROJECT: return ProjectPhysOperClose((ProjectPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_STRING_LIST: return GNCDB_SUCCESS;
    case PO_UPDATE: return UpdatePhysOperClose((UpdatePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_GROUPBY: return GroupByPhysOperClose((GroupByPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_HASH_JOIN: return HashJoinPhysOperClose((HashJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_SORT_MERGE_JOIN:
      return SortMergeJoinPhysOperClose((SortMergeJoinPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_ORDER_BY: return OrderByPhysOperClose((OrderByPhysicalOperator *)physicalOperator, sqlEvent);
    case PO_CREATE_TABLE: return CreateTablePhysOperClose((CreateTablePhysicalOperator *)physicalOperator, sqlEvent);
    case PO_LIMIT: return LimitPhysOperClose((LimitPhysicalOperator *)physicalOperator, sqlEvent);
    default: return GNCDB_SUCCESS;
  }

  return GNCDB_SUCCESS;
}

int PhysicalOperatorReset(PhysicalOperator *physicalOperator)
{
  PhysicalOperator *child = NULL;

  switch (physicalOperator->type) {
    case PO_TABLE_SCAN: return TableScanPhysOperReset((TableScanPhysicalOperator *)physicalOperator);
    case PO_INDEX_SCAN: return IndexScanPhysOperReset((IndexScanPhysicalOperator *)physicalOperator);
    case PO_NESTED_LOOP_JOIN: return NestedLoopJoinPhysOperReset((NestedLoopJoinPhysicalOperator *)physicalOperator);
    case PO_HASH_JOIN: return HashJoinPhysOperReset((HashJoinPhysicalOperator *)physicalOperator);
    case PO_SORT_MERGE_JOIN: return SortMergeJoinPhysOperReset((SortMergeJoinPhysicalOperator *)physicalOperator);
    case PO_PREDICATE:
    case PO_PROJECT:
    case PO_GROUPBY:
    case PO_ORDER_BY:
    case PO_LIMIT:
      child = (PhysicalOperator *)varArrayListGetPointer(physicalOperator->children, 0);
      return PhysicalOperatorReset(child);
    default: return GNCDB_SUCCESS;
  }
}

BtreeCursor *GetScanCursor(PhysicalOperator *physicalOperator)
{
  PhysicalOperator *child = NULL;
  switch (physicalOperator->type) {
    case PO_TABLE_SCAN: return TableScanPhysOperGetScanCursor((TableScanPhysicalOperator *)physicalOperator);
    case PO_NESTED_LOOP_JOIN:
    case PO_HASH_JOIN:
    case PO_SORT_MERGE_JOIN: return NULL;
    default: {
      child = varArrayListGetPointer(physicalOperator->children, 0);
      return GetScanCursor(child);
    }
  }
}

/**
 * @brief 设置下层物理计划节点的tuple描述，因为tuple是字节流形式，需要额外信息来解析
 *
 * @param physicalOperator
 * @return varArrayList*
 */
int setColMeta(PhysicalOperator *physicalOperator)
{
  PhysicalOperator *child = NULL;
  int               i     = 0;
  int               rc    = GNCDB_SUCCESS;
  /*1.传递列元信息，首先进行参数检查*/
  if (physicalOperator == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.递归调用，设置所有算子的列元信息*/
  for (i = 0; i < physicalOperator->children->elementCount; i++) {
    child = (PhysicalOperator *)varArrayListGetPointer(physicalOperator->children, i);
    rc    = setColMeta(child);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /*3.根据算子类型，进行colmeta信息的赋值*/
  switch (physicalOperator->type) {
    case PO_TABLE_SCAN: return ScanSetColMeta((TableScanPhysicalOperator *)physicalOperator);
    case PO_INDEX_SCAN: return IndexScanSetColMeta((IndexScanPhysicalOperator*) physicalOperator);
    case PO_PROJECT: return ProjectSetColMeta((ProjectPhysicalOperator *)physicalOperator);
    case PO_HASH_JOIN:
    case PO_NESTED_LOOP_JOIN:
    case PO_SORT_MERGE_JOIN: return JoinSetColMeta((PhysicalOperator *)physicalOperator);
    case PO_GROUPBY: return GroupBySetColMeta((GroupByPhysicalOperator *)physicalOperator);
    case PO_ORDER_BY: return OrderBySetColMeta((OrderByPhysicalOperator *)physicalOperator);
    case PO_PREDICATE: return PredicateSetColMeta((PredicatePhysicalOperator *)physicalOperator);
    case PO_UPDATE: return UpdateSetColMeta((UpdatePhysicalOperator *)physicalOperator);
    case PO_LIMIT: return LimitSetColMeta((LimitPhysicalOperator *)physicalOperator);
    case PO_DELETE: return DeleteSetColMeta((DeletePhysicalOperator *)physicalOperator);
    default: return GNCDB_SUCCESS;
  }

  return GNCDB_SUCCESS;
}

varArrayList *getColMeta(PhysicalOperator *physicalOperator) { return physicalOperator->cols; }

BYTE         *GetCurrentRecord(PhysicalOperator *physicalOperator){
  switch (physicalOperator->type) {
    case PO_TABLE_SCAN: return TableScanPhysOperGetCurrentRecord((TableScanPhysicalOperator *)physicalOperator);
    case PO_PREDICATE: {
      PhysicalOperator *child = NULL;
      child = (PhysicalOperator *)varArrayListGetPointer(physicalOperator->children, 0);
      return GetCurrentRecord(child);}
    default: return NULL;
  }
}

int  GetCurrentRecordIndex(PhysicalOperator *physicalOperator){
  switch (physicalOperator->type) {
    case PO_TABLE_SCAN: return TableScanPhysOperGetCurrentRecordIndex((TableScanPhysicalOperator *)physicalOperator);
    case PO_PREDICATE: {
      PhysicalOperator *child = NULL;
      child = (PhysicalOperator *)varArrayListGetPointer(physicalOperator->children, 0);
      return GetCurrentRecordIndex(child);}
    default: return -1;
  }
}
/**
 * @brief tuple自下而上被传递，其所有权被转移，需要上层调用该函数者释放tuple
 *
 * @param physicalOperator
 * @return AbstractTuple*
 */
Record *GetCurrentTuple(PhysicalOperator *physicalOperator)
{
  switch (physicalOperator->type) {
    case PO_INSERT: return NULL;
    case PO_TABLE_SCAN: return TableScanPhysOperGetCurrentTuple((TableScanPhysicalOperator *)physicalOperator);
    case PO_INDEX_SCAN: return IndexScanPhysOperGetCurrentTuple((IndexScanPhysicalOperator *)physicalOperator);
    case PO_NESTED_LOOP_JOIN:
      return NestedLoopJoinPhysOperGetCurrentTuple((NestedLoopJoinPhysicalOperator *)physicalOperator);
    case PO_EXPLAIN: return NULL;
    case PO_PREDICATE: return PredicatePhysOperGetCurrentTuple((PredicatePhysicalOperator *)physicalOperator);
    case PO_DELETE: return NULL;
    case PO_PROJECT: return ProjectPhysOperGetCurrentTuple((ProjectPhysicalOperator *)physicalOperator);
    case PO_STRING_LIST: return NULL;
    case PO_HASH_JOIN: return HashJoinPhysOperGetCurrentTuple((HashJoinPhysicalOperator *)physicalOperator);
    case PO_SORT_MERGE_JOIN:
      return SortMergeJoinPhysOperGetCurrentTuple((SortMergeJoinPhysicalOperator *)physicalOperator);
    case PO_ORDER_BY: return OrderByPhysOperGetCurrentTuple((OrderByPhysicalOperator *)physicalOperator);
    case PO_GROUPBY: return GroupByPhysOperGetCurrentTuple((GroupByPhysicalOperator *)physicalOperator);
    case PO_CREATE_TABLE: return CreateTablePhysOperGetCurrentTuple((CreateTablePhysicalOperator *)physicalOperator);
    case PO_LIMIT: return LimitPhysOperGetCurrentTuple((LimitPhysicalOperator *)physicalOperator);
    default: return NULL;
  }
}

struct BtreePage* GetCurrentPage(PhysicalOperator *physicalOperator){
  switch (physicalOperator->type) {
    case PO_TABLE_SCAN: return TableScanPhysOperGetCurrentPage((TableScanPhysicalOperator *)physicalOperator);
    case PO_PREDICATE: {
      PhysicalOperator *child = NULL;
      child = (PhysicalOperator *)varArrayListGetPointer(physicalOperator->children, 0);
      return GetCurrentPage(child);}
    default: return NULL;
  }
}

HashMap *colsMapDeepCopy(HashMap *map)
{
  HashMapIterator *iter = NULL;
  HashMap         *newMap;
  char            *key;
  int              rc;

  if (map == NULL || map->entryCount == 0) {
    return NULL;
  }
  newMap = hashMapCreate(STRKEY, 0, NULL);
  if (newMap == NULL) {
    return NULL;
  }
  iter = createHashMapIterator(map);
  if (iter == NULL) {
    hashMapDestroy(&newMap);
    return NULL;
  }

  while (hasNextHashMapIterator(iter)) {
    iter = nextHashMapIterator(iter);
    key  = iter->entry->key;
    rc   = hashMapPut(newMap, key, MetaDeepCopy(iter->entry->value));
    assert(rc == GNCDB_SUCCESS);
  }
  freeHashMapIterator(&iter);

  return newMap;
}

int mergetableColsMap(HashMap *map, HashMap *parentmap)
{
  // 这里假设子查询和父查询的表明不一样
  // TODO(yankee): 处理表名一样的情况
  HashMapIterator *iter      = NULL;
  HashMap         *colsMap   = NULL;
  char            *tableName = NULL;
  if (map == NULL || map->entryCount == 0) {
    return GNCDB_INTERNAL;
  }

  if (parentmap == NULL) {
    return GNCDB_SUCCESS;
  }
  iter = createHashMapIterator(parentmap);
  if (iter == NULL) {
    return GNCDB_INTERNAL;
  }

  while (hasNextHashMapIterator(iter)) {
    iter      = nextHashMapIterator(iter);
    tableName = iter->entry->key;
    // 假设表名不一样
    if (!hashMapExists(map, tableName)) {
      colsMap = colsMapDeepCopy(iter->entry->value);
      if (colsMap == NULL) {
        freeHashMapIterator(&iter);
        return GNCDB_INTERNAL;
      }
      hashMapPut(map, tableName, colsMap);
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 设置物理计划节点的最顶层的父节点类型
 *
 * @param physicalOperator
 * @param parentType
 */
void setParentType(PhysicalOperator *physicalOperator, PhysicalOperatorType parentType)
{
  int               i          = 0;
  PhysicalOperator *child      = NULL;
  physicalOperator->parentType = parentType;
  assert(physicalOperator->children);
  for (i = 0; i < physicalOperator->children->elementCount; i++) {
    child = (PhysicalOperator *)varArrayListGetPointer(physicalOperator->children, i);
    setParentType(child, parentType);
  }
}

/**
 * @brief 打印物理计划树
 *
 * @param depth 树的深度
 */
void PhysicalOperatorToString(PhysicalOperator *physicalOperator, int depth)
{
  TableScanPhysicalOperator      *tableScan      = NULL;
  Expression                     *expression     = NULL;
  int                             i              = 0;
  IndexScanPhysicalOperator      *indexScan      = NULL;
  NestedLoopJoinPhysicalOperator *nextedLoopJoin = NULL;
  GroupByPhysicalOperator        *groupBy        = NULL;
  HashJoinPhysicalOperator       *hashJoin       = NULL;
  SortMergeJoinPhysicalOperator  *sortMergeJoin  = NULL;
  OrderByPhysicalOperator        *orderBy        = NULL;
  OrderByUnit                    *orderByUnit    = NULL;
  PhysicalOperator               *child          = NULL;

  printf("%*s", depth * 4, "");  // 缩进
  switch (physicalOperator->type) {
    case PO_TABLE_SCAN: {
      tableScan = (TableScanPhysicalOperator *)physicalOperator;
      printf("PO_TABLE_SCAN:%s ", tableScan->table->tableName);
      if (tableScan->predicates != NULL && tableScan->predicates->elementCount > 0) {
        printf(",predicates:");
        for (i = 0; i < tableScan->predicates->elementCount; i++) {
          expression = (Expression *)varArrayListGetPointer(tableScan->predicates, i);
          printExpr(expression);
          printf(",");
        }
      }
      printf("\n");
    } break;
    case PO_INDEX_SCAN: {
      indexScan = (IndexScanPhysicalOperator *)physicalOperator;
      printf("PO_INDEX_SCAN:%s ", indexScan->table->tableName);
      if (indexScan->predicates != NULL && indexScan->predicates->elementCount > 0) {
        printf(",predicates:");
        for (i = 0; i < indexScan->predicates->elementCount; i++) {
          expression = (Expression *)varArrayListGetPointer(indexScan->predicates, i);
          printExpr(expression);
          printf(",");
        }
      }
      printf("\n");
    } break;
    case PO_NESTED_LOOP_JOIN: {
      nextedLoopJoin = (NestedLoopJoinPhysicalOperator *)physicalOperator;
      printf("PO_NESTED_LOOP_JOIN ");
      if (nextedLoopJoin->predicates != NULL && nextedLoopJoin->predicates->elementCount > 0) {
        printf(",predicates:");
        for (i = 0; i < nextedLoopJoin->predicates->elementCount; i++) {
          expression = (Expression *)varArrayListGetPointer(nextedLoopJoin->predicates, i);
          printExpr(expression);
          printf(",");
        }
      }
      printf("\n");
    } break;
    case PO_EXPLAIN: printf("PO_EXPLAIN\n"); break;
    case PO_PREDICATE:
      printf("PO_PREDICATE:");
      expression = ((PredicatePhysicalOperator *)physicalOperator)->expression;
      printExpr(expression);
      printf("\n");
      break;
    case PO_PROJECT: printf("PO_PROJECT\n"); break;
    case PO_CALC: printf("PO_CALC\n"); break;
    case PO_STRING_LIST: printf("PO_STRING_LIST\n"); break;
    case PO_DELETE: printf("PO_DELETE\n"); break;
    case PO_INSERT: printf("PO_INSERT\n"); break;
    case PO_UPDATE: printf("PO_UPDATE\n"); break;
    case PO_GROUPBY: {
      printf("PO_GroupBy ");
      groupBy = (GroupByPhysicalOperator *)physicalOperator;
      if (groupBy->groupbyFields != NULL && groupBy->groupbyFields->elementCount > 0) {
        printf(",groupby_fields:");
        for (i = 0; i < groupBy->groupbyFields->elementCount; i++) {
          expression = (Expression *)varArrayListGetPointer(groupBy->groupbyFields, i);
          printExpr(expression);
          printf(",");
        }
      }
      printf("\n");
    } break;
    case PO_HASH_JOIN: {
      hashJoin = (HashJoinPhysicalOperator *)physicalOperator;
      printf("PO_HASH_JOIN ");
      if (hashJoin->eqPredicates != NULL && hashJoin->eqPredicates->elementCount > 0) {
        printf(",predicates:");
        for (i = 0; i < hashJoin->eqPredicates->elementCount; i++) {
          expression = (Expression *)varArrayListGetPointer(hashJoin->eqPredicates, i);
          printExpr(expression);
          printf(",");
        }
      }
      printf("\n");
    } break;
    case PO_SORT_MERGE_JOIN: {
      sortMergeJoin = (SortMergeJoinPhysicalOperator *)physicalOperator;
      printf("PO_SORT_MERGE_JOIN ");
      if (sortMergeJoin->eqPredicates != NULL && sortMergeJoin->eqPredicates->elementCount > 0) {
        printf(",predicates:");
        for (i = 0; i < sortMergeJoin->eqPredicates->elementCount; i++) {
          expression = (Expression *)varArrayListGetPointer(sortMergeJoin->eqPredicates, i);
          printExpr(expression);
          printf(",");
        }
      }
      printf("\n");
    } break;
    case PO_ORDER_BY: {
      printf("PO_ORDER_BY ");
      orderBy = (OrderByPhysicalOperator *)physicalOperator;
      if (orderBy->orderbyUnits != NULL && orderBy->orderbyUnits->elementCount > 0) {
        for(i = 0; i < orderBy->orderbyUnits->elementCount; i++) {
          orderByUnit = (OrderByUnit *)varArrayListGetPointer(orderBy->orderbyUnits, 0);
          printf("%s ", orderByUnit->expr->name);
          printf("%s ", orderByUnit->isAsc ? "ASC" : "DESC");
        }
      }
      printf("\n");
    } break;
    case PO_CREATE_TABLE: printf("PO_CREATE_TABLE\n"); break;
    case PO_LIMIT: printf("PO_LIMIT\n"); break;
    default: printf("UNKNOWN\n");
  }
  for (i = 0; i < physicalOperator->children->elementCount; i++) {
    child = (PhysicalOperator *)varArrayListGetPointer(physicalOperator->children, i);
    PhysicalOperatorToString(child, depth + 1);
  }
}
