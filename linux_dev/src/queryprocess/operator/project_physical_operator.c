#include "project_physical_operator.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "typedefine.h"
#include "utils.h"
#include "value.h"
#include "vararraylist.h"
#include <assert.h>
#include <stdio.h>
#include <string.h>
extern unsigned long timeCostProject;

void ProjectPhysOperInit(ProjectPhysicalOperator *projectPhysOper)
{
  projectPhysOper->type        = PO_PROJECT;
  projectPhysOper->children    = varArrayListCreate(0, sizeof(PhysicalOperator *), 0, NULL, NULL);
  projectPhysOper->parentTuple = NULL;

  projectPhysOper->tuple           = NULL;
  projectPhysOper->distinctMap     = NULL;
  projectPhysOper->isDistinct      = false;
  projectPhysOper->currentTuple    = NULL;
  projectPhysOper->projectedFields = NULL;
}

int ProjectPhysOperOpen(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = NULL;
  if (projectPhysOper->children == NULL || projectPhysOper->children->elementCount == 0) {
    return GNCDB_SUCCESS;
  }
  /*投影有且只有一个算子*/
  child = varArrayListGetPointer(projectPhysOper->children, 0);
  rc    = PhysicalOperatorOpen(child, sqlEvent);
  if (rc == GNCDB_NEXT_EOF) {
    rc = GNCDB_SUCCESS;
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return rc;
}

void fillValues(Value *value, char **fieldValue)
{
  char  fieldValueTmp[256] = {0};
  char *str                = NULL;
  if (value->attrType == NULLS) {
    *fieldValue = NULL;
    return;
  }

  if (value->attrType == CHARS || value->attrType == TEXTS) {
    // assert(value->strValue != NULL);
    *fieldValue = (char *)my_malloc0(strlen(value->strValue) + 1);
    strcpy(*fieldValue, value->strValue);
    return;
  }
  switch (value->attrType) {
    case INTS: sprintf(fieldValueTmp, "%d", value->numValue.intValue); break;
    case DOUBLES: sprintf(fieldValueTmp, "%f", value->numValue.doubleValue); break;
    case BOOLEANS: sprintf(fieldValueTmp, "%s", value->numValue.boolValue ? "true" : "false"); break;
    case DATES: {
      str = dateToString(value->numValue.intValue);
      sprintf(fieldValueTmp, "%s", str);
      my_free(str);
    } break;
    case DATETIMES: {
      char *str = datetimeToString(&value->numValue.datetimeValue);
      sprintf(fieldValueTmp, "%s", str);
      my_free(str);
      break;
    }
    default: break;
  }
  *fieldValue = (char *)my_malloc0(strlen(fieldValueTmp) + 1);
  strcpy(*fieldValue, fieldValueTmp);
}

void resetFieldValues(char **fieldValues, int fieldCount)
{
  int i = 0;
  for (i = 0; i < fieldCount; i++) {
    if (fieldValues[i] != NULL) {
      my_free(fieldValues[i]);
    }
  }
  my_free(fieldValues);
}

int ProjectPhysOperNext(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent)
{
  int               rc                = GNCDB_SUCCESS;
  PhysicalOperator *child             = NULL;
  int               i                 = 0;
  int               projectFieldCount = 0;
  Expression       *expr              = NULL;
  Record           *tuple             = NULL;
  Value            *value             = NULL;
  char            **fieldValues       = NULL;
  varArrayList     *cols              = NULL;
  /*1.参数检查*/
  if (projectPhysOper->children == NULL || projectPhysOper->children->elementCount == 0) {
    return GNCDB_NEXT_EOF;
  }
  /*2.获取子算子以及相应的colmeta*/
  child = varArrayListGetPointer(projectPhysOper->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }
  cols = getColMeta(child);
  if (cols == NULL) {
    return GNCDB_INTERNAL;
  }
  projectFieldCount = projectPhysOper->projectedFields->elementCount;
  /*3.从子算子获取一个满足去重条件的元组*/
  while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext(child, sqlEvent))) {
    tuple = GetCurrentTuple(child);
    if (unlikely(projectPhysOper->isDistinct)) {
      fieldValues = (char **)my_malloc0(sizeof(char *) * projectFieldCount);
      for (i = 0; i < projectFieldCount; i++) {
        expr = varArrayListGetPointer(projectPhysOper->projectedFields, i);
        if (expr == NULL) {
          my_free(fieldValues);
          return GNCDB_PARAMNULL;
        }
        value = valueCreate();
        /*3.1从子算子返回的元组中获取投影列的值*/
        rc    = expressionGetValueNCP(expr, tuple, cols, value);
        if (rc != GNCDB_SUCCESS) {
          my_free(fieldValues);
          valueDestroy(&value);
          return GNCDB_EXPR_EVAL_FAILED;
        }
        /*3.2将value放到数组的对应位置*/
        fillValues(value, &fieldValues[i]);
        valueDestroy(&value);
      }
      /*3.3不满足去重条件则下一个*/
      if (!ProjectPhysOperDistinctFilter(projectPhysOper, fieldValues)) {
        resetFieldValues(fieldValues, projectFieldCount);
        continue;
      }
      resetFieldValues(fieldValues, projectFieldCount);
    }
    /*3.4返回非重复的元组*/
    sqlEvent->affectedRows++;
    projectPhysOper->currentTuple = (tuple);
    return GNCDB_SUCCESS;
  }
  return rc;
}

int ProjectPhysOperClose(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  if (projectPhysOper->children != NULL && projectPhysOper->children->elementCount != 0) {
    rc = PhysicalOperatorClose(varArrayListGetPointer(projectPhysOper->children, 0), sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return GNCDB_SUCCESS;
}

/**
 * @brief distinct过滤重复数据
 *
 * @param projectPhysOper
 * @param fieldValues
 * @return int
 */
int ProjectPhysOperDistinctFilter(ProjectPhysicalOperator *projectPhysOper, char **fieldValues)
{
  int    exprCount      = 0;
  char  *hashString     = NULL;
  int    i              = 0;
  char **oldFieldValues = NULL;
  if (projectPhysOper == NULL || fieldValues == NULL) {
    return GNCDB_PARAMNULL;
  }

  if (projectPhysOper->isDistinct) {
    exprCount = projectPhysOper->tuple->exprs->elementCount;
    /* 将fieldValues的字符串值每个取最多10个字符到hashString中 */
    hashString = (char *)my_malloc0(sizeof(char) * (exprCount * 10 + 1));
    if (hashString == NULL) {
      return GNCDB_MEM;
    }
    memset(hashString, 0, exprCount * 10 + 1);
    for (i = 0; i < exprCount; i++) {
      strncat(hashString, fieldValues[i], 10);
    }
    if (projectPhysOper->distinctMap == NULL) {
      projectPhysOper->distinctMap = hashMapCreate(STRKEY, 0, NULL);
    }
    if (hashMapExists(projectPhysOper->distinctMap, hashString)) {
      oldFieldValues = hashMapGet(projectPhysOper->distinctMap, hashString);
      for (i = 0; i < exprCount; i++) {
        if (strcmp(oldFieldValues[i], fieldValues[i]) != 0) {
          my_free(hashString);
          return true;
        }
      }
      return false;
    }
    hashMapPut(projectPhysOper->distinctMap, hashString, deepCopy2DArray(fieldValues, exprCount));
  }
  return true;
}

Meta *matchAggrFunc(AggrFuncExpr *aggrFuncExpr, varArrayList *colMetas)
{
  int            i             = 0;
  Meta          *meta          = NULL;
  ColMeta       *colMeta       = NULL;
  CompositeMeta *compositeMeta = NULL;

  for (i = 0; i < colMetas->elementCount; i++) {
    meta = varArrayListGetPointer(colMetas, i);
    if (meta->type != NormalField) {
      compositeMeta = (CompositeMeta *)meta;
      if (strcmp(aggrFuncExpr->name, compositeMeta->expr->name) == 0) {
        return meta;
      }
    } else {
      colMeta = (ColMeta *)meta;
      if (strcmp(aggrFuncExpr->name, colMeta->name) == 0 && aggrFuncExpr->aggrType == colMeta->aggrType) {
        return meta;
      }
    }
  }
  return NULL;
}

ColMeta *matchField(FieldExpr *fieldExpr, varArrayList *colMetas)
{
  int      i       = 0;
  Meta    *meta    = NULL;
  ColMeta *colMeta = NULL;

  for (i = 0; i < colMetas->elementCount; i++) {
    meta = varArrayListGetPointer(colMetas, i);
    if (meta->type != NormalField) {
      continue;
    }
    colMeta = (ColMeta *)meta;
    if (!colMeta->tableName) {  // 如果表名为空，说明是一个非Field类型的表达式
      continue;
    }
    if (strcmp(fieldExpr->tableName, colMeta->tableName) == 0 && strcmp(fieldExpr->fieldName, colMeta->name) == 0) {
      return colMeta;
    }
  }
  return NULL;
}

/**
 * @brief 根据表达式类型匹配对应的元数据(拷贝操作分配了内存，需要调用者释放)
 *
 * @param expr
 * @param colMetas
 * @return void*
 */
Meta *matchMeta(Expression *expr, varArrayList *colMetas)
{
  Meta *meta     = NULL;
  if (expr->type == ETG_FIELD) {
    meta = (Meta *)matchField((FieldExpr *)expr, colMetas);
    if (meta == NULL) {
      return NULL;
    }
    return meta;
  } else if (expr->type == ETG_AGGRFUNC) {
    // meta = matchAggrFunc((AggrFuncExpr *)expr, colMetas);
    meta = matchAggrFunc((AggrFuncExpr *)expr, colMetas);
    if (meta == NULL) {
      return NULL;
    }
    return meta;
  }
  //  else {
  //   compositeMeta       = my_new0(CompositeMeta);
  //   if(unlikely(compositeMeta == NULL)) {
  //     return NULL;
  //   }
  //   compositeMeta->type = CompositeField;
  //   compositeMeta->expr = exprDeepCopy(expr);
  //   meta                = compositeMeta;
  // }
  return meta;
}

int ProjectSetColMeta(ProjectPhysicalOperator *projectPhysOper)
{
  PhysicalOperator *child            = NULL;
  int               i                = 0;
  Expression       *expr             = NULL;
  Meta             *meta             = NULL;
  varArrayList     *childCols        = NULL;
  int               offset           = 0;

  if (projectPhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  child = varArrayListGetPointer(projectPhysOper->children, 0);
  if (child == NULL) {
    return GNCDB_INTERNAL;
  }

  childCols   = child->cols;
  if (childCols == NULL) {
    return GNCDB_INTERNAL;
  }

  projectPhysOper->cols = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (projectPhysOper->cols == NULL) {
    return GNCDB_MEM;
  }

  // 为projectedFields设置colMeta，简单考虑为Field类型的表达式
  // TODO: 如果是子查询需要考虑重新设置offset
  for (i = 0; i < projectPhysOper->projectedFields->elementCount; i++) {
    expr = varArrayListGetPointer(projectPhysOper->projectedFields, i);
    // if (expr->type == ETG_FIELD) {
    //   fieldExpr = (FieldExpr *)expr;
    //   colMeta   = matchField(fieldExpr, childColMetaMap);
    //   if (colMeta == NULL) {
    //     return GNCDB_INTERNAL;  // 不应该有找不到的情况
    //   }
    //   colMetaCopy = MetaDeepCopy(colMeta);
    //   if (colMetaCopy == NULL) {
    //     return GNCDB_MEM;
    //   }
    //   varArrayListAddPointer(projectPhysOper->cols, colMetaCopy);
    // } else if (expr->type == ETG_AGGRFUNC) {
    //   aggrFuncExpr = (AggrFuncExpr *)expr;
    //   colMeta      = matchAggrFunc(aggrFuncExpr, childColMetaMap);
    //   if (colMeta == NULL) {
    //     return GNCDB_INTERNAL;  // 不应该有找不到的情况
    //   }
    //   colMetaCopy = MetaDeepCopy(colMeta);
    //   if (colMetaCopy == NULL) {
    //     return GNCDB_MEM;
    //   }
    //   varArrayListAddPointer(projectPhysOper->cols, colMetaCopy);
    // }
    meta = matchMeta(expr, childCols);
    if (meta == NULL) {
      meta                          = my_new0(CompositeMeta);
      meta->type                    = CompositeField;
      meta->fieldType               = (FieldType)expressionGetValueType(expr);
      meta->len                     = expressionGetValueLen(expr, childCols);
      ((CompositeMeta *)meta)->expr = (expr);
      ((CompositeMeta *)meta)->owned = false;

      // meta->offset = offset;
      // offset += meta->len;
      // meta->index        = (i);
      // meta->bitmapOffset = 0;
      varArrayListAddPointer(projectPhysOper->cols, (meta));
    } else {

      // meta->offset = offset;
      // offset += meta->len;
      // meta->index            = (i);
      // meta->bitmapOffset = 0;
      varArrayListAddPointer(projectPhysOper->cols, meta);
    }
  }

  projectPhysOper->recordSize = offset;  // 设置record的大小，后续RecordCreate会用到
  return GNCDB_SUCCESS;
}

varArrayList *ProjectGetCols(ProjectPhysicalOperator *projectPhysOper) { return projectPhysOper->cols; }

Record *ProjectPhysOperGetCurrentTuple(ProjectPhysicalOperator *projectPhysOper)
{
  return PTR_MOVE((void **)&projectPhysOper->currentTuple);
}

int ProjectPhysOperGetSchemaTotalSize(ProjectPhysicalOperator *projectPhysOper, SQLStageEvent *sqlEvent, int *len)
{
  int         i    = 0;
  Expression *expr = NULL;
  if (projectPhysOper == NULL || sqlEvent == NULL || len == NULL) {
    return GNCDB_PARAMNULL;
  }
  *len = 0;
  for (i = 0; i < projectPhysOper->tuple->exprs->elementCount; i++) {
    expr = varArrayListGetPointer(projectPhysOper->tuple->exprs, i);
    if (expr->type == ETG_FIELD || expr->type == ETG_AGGRFUNC) {
      if (expr->alias != NULL) {
        *len += strlen(expr->alias);
      } else {
        *len += strlen(expr->name);
      }
    } else {
      return GNCDB_INTERNAL;
    }
  }
  return GNCDB_SUCCESS;
}

void ProjectPhysOperDestroy(ProjectPhysicalOperator *projectPhysOper)
{
  HashMapIterator *iterator    = NULL;
  char           **fieldValues = NULL;
  int              i           = 0;

  if (projectPhysOper == NULL) {
    return;
  }

  if (projectPhysOper->children != NULL) {
    varArrayListDestroy(&projectPhysOper->children);
  }

  // 释放distinct_map的内存
  if (projectPhysOper->distinctMap != NULL) {
    iterator = createHashMapIterator(projectPhysOper->distinctMap);
    while (hasNextHashMapIterator(iterator)) {
      iterator    = nextHashMapIterator(iterator);
      fieldValues = (char **)iterator->entry->value;
      for (i = 0; i < projectPhysOper->tuple->exprs->elementCount; i++) {
        my_free(fieldValues[i]);
      }
      my_free(fieldValues);
    }
    hashMapDestroy(&projectPhysOper->distinctMap);
  }

  if (projectPhysOper->projectedFields != NULL) {
    varArrayListDestroy(&projectPhysOper->projectedFields);
  }

  if (projectPhysOper->tuple != NULL) {
    tupleDestroy((AbstractTuple *)projectPhysOper->tuple);
  }

  if (projectPhysOper->cols != NULL) {
    varArrayListDestroy(&projectPhysOper->cols);
  }

  my_free(projectPhysOper);
}

void ProjectPhysOperPointerDestroy(void *data)
{
  ProjectPhysicalOperator **projectPhysOper = (ProjectPhysicalOperator **)data;
  if (projectPhysOper == NULL || *projectPhysOper == NULL) {
    return;
  }

  ProjectPhysOperDestroy(*projectPhysOper);
  *projectPhysOper = NULL;
}