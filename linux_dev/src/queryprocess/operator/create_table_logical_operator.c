#include "create_table_logical_operator.h"
void CreateTableLogiOperDestroy(CreateTableLogicalOperator *createTableLogiOper)
{
  if (createTableLogiOper == NULL) {
    return;
  }
  if (createTableLogiOper->children != NULL) {
    varArrayListDestroy(&createTableLogiOper->children);
  }
  if (createTableLogiOper->expressions != NULL) {
    varArrayListDestroy(&createTableLogiOper->expressions);
  }
  if (createTableLogiOper->tableName != NULL) {
    my_free(createTableLogiOper->tableName);
  }
  if (createTableLogiOper->attrInfos != NULL) {
    varArrayListDestroy(&createTableLogiOper->attrInfos);
  }
  my_free(createTableLogiOper);
}

void CreateTableLogiOperPointerDestroy(void *data)
{
  CreateTableLogicalOperator **createTableLogiOper = (CreateTableLogicalOperator **)data;
  if (createTableLogiOper == NULL || *createTableLogiOper == NULL) {
    return;
  }
  CreateTableLogiOperDestroy(*createTableLogiOper);
  *createTableLogiOper = NULL;
}
