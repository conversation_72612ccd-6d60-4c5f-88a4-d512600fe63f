#include "project_logical_operator.h"
#include "vararraylist.h"

void ProjectLogiOperDestroy(ProjectLogicalOperator *projectLogiOper)
{
  if (projectLogiOper == NULL) {
    return;
  }

  if (projectLogiOper->children != NULL) {
    varArrayListDestroy(&projectLogiOper->children);
  }

  if (projectLogiOper->expressions != NULL) {
    varArrayListDestroy(&projectLogiOper->expressions);
  }

  my_free(projectLogiOper);
}

void ProjectLogiOperPointerDestroy(void *data)
{
  ProjectLogicalOperator **projectLogiOper = (ProjectLogicalOperator **)data;
  if (projectLogiOper == NULL || *projectLogiOper == NULL) {
    return;
  }
  ProjectLogiOperDestroy(*projectLogiOper);
  *projectLogiOper = NULL;
}
