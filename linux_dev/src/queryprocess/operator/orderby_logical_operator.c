#include "orderby_logical_operator.h"
#include "vararraylist.h"
void OrderByLogiOperDestroy(OrderByLogicalOperator *orderbyLogiOper)
{
  if (orderbyLogiOper == NULL) {
    return;
  }

  if (orderbyLogiOper->children != NULL) {
    varArrayListDestroy(&orderbyLogiOper->children);
  }

  if (orderbyLogiOper->expressions != NULL) {
    varArrayListDestroy(&orderbyLogiOper->expressions);
  }

  if (orderbyLogiOper->orderbyUnits != NULL) {
    varArrayListDestroy(&orderbyLogiOper->orderbyUnits);
  }

  if (orderbyLogiOper->exprs != NULL) {
    varArrayListDestroy(&orderbyLogiOper->exprs);
  }

  my_free(orderbyLogiOper);
}

void OrderByLogiOperPointerDestroy(void *data)
{
  OrderByLogicalOperator **orderbyLogiOper = NULL;
  orderbyLogiOper                          = (OrderByLogicalOperator **)data;
  if (orderbyLogiOper == NULL || *orderbyLogiOper == NULL) {
    return;
  }
  OrderByLogiOperDestroy(*orderbyLogiOper);
  *orderbyLogiOper = NULL;
}
