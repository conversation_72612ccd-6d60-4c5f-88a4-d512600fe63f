#ifndef TABLE_GET_LOGICAL_OPERATOR_H
#define TABLE_GET_LOGICAL_OPERATOR_H
#include "btreetable.h"
#include "logical_operator.h"
typedef struct TableGetLogicalOperator
{
  LogicalOperatorType type;
  varArrayList       *children;  ///< 子算子
                                 ///< element type:<LogicalOperator*>

  varArrayList *expressions;  ///< 表达式，比如select中的列，where中的谓词等等，都可以使用表达式来表示
                              ///< 表达式能是一个常量，也可以是一个函数，也可以是一个列，也可以是一个子查询等等
                              ///< element type:<Expression*>
  BtreeTable   *table;
  varArrayList *fields;  ///< 字段，比如select中的列，where中的谓词等等，都可以使用字段来表示
                         ///< element type:<FieldInfo>

  bool readOnly;  ///< 是否只读
} TableGetLogicalOperator;
void TableGetLogiOperDestroy(TableGetLogicalOperator *tableGetLogiOper);
void TableGetLogiOperPointerDestroy(void *data);
#endif  // TABLE_GET_LOGICAL_OPERATOR_H