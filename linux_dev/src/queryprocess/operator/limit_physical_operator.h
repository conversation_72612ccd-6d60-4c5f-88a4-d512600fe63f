/*
 * @Author: zql <EMAIL>
 * @Date: 2025-07-03 21:26:18
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-07-14 17:24:20
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/limit_physical_operator.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef LIMIT_PHYSICAL_OPERATOR_H
#define LIMIT_PHYSICAL_OPERATOR_H
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
typedef struct LimitPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文
  int                  limit;
  int                  offset;
  int                  currentOffset;
} LimitPhysicalOperator;
int     LimitPhysOperInit(LimitPhysicalOperator *limitPhysOper, PhysicalOperatorType type);
int     LimitPhysOperOpen(LimitPhysicalOperator *limitPhysOper, SQLStageEvent *sqlEvent);
int     LimitPhysOperNext(LimitPhysicalOperator *limitPhysOper, SQLStageEvent *sqlEvent);
int     LimitPhysOperClose(LimitPhysicalOperator *limitPhysOper, SQLStageEvent *sqlEvent);
Record *LimitPhysOperGetCurrentTuple(LimitPhysicalOperator *limitPhysOper);
int     LimitSetColMeta(LimitPhysicalOperator *limitPhysOper);
void    LimitPhysOperDestroy(LimitPhysicalOperator *limitPhysOper);
void    LimitPhysOperDestroyPointerDestroy(void *data);
#endif  // LIMIT_PHYSICAL_OPERATOR_H
