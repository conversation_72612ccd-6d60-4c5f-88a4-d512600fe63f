#ifndef PHYSICAL_OPERATOR_H
#define PHYSICAL_OPERATOR_H
#include "btreepage.h"
#include "exec_tuple.h"
#include "hashmap.h"
#include "sql_event.h"
#include "vararraylist.h"
typedef struct SQLStageEvent SQLStageEvent;
typedef struct PhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;  // element type:<PhysicalOperator*>
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文
} PhysicalOperator;
int               PhysicalOperatorInit(PhysicalOperator *physicalOperator, PhysicalOperatorType type);
int               PhysicalOperatorOpen(PhysicalOperator *physicalOperator, SQLStageEvent *sqlEvent);
int               PhysicalOperatorNext(PhysicalOperator *physicalOperator, SQLStageEvent *sqlEvent);
int               PhysicalOperatorClose(PhysicalOperator *physicalOperator, SQLStageEvent *sqlEvent);
int               PhysicalOperatorReset(PhysicalOperator *physicalOperator);
Record           *GetCurrentTuple(PhysicalOperator *physicalOperator);
BYTE             *GetCurrentRecord(PhysicalOperator *physicalOperator);
int               GetCurrentRecordIndex(PhysicalOperator *physicalOperator);
struct BtreePage *GetCurrentPage(PhysicalOperator *physicalOperator);
BtreeCursor      *GetScanCursor(PhysicalOperator *physicalOperator);
int               setColMeta(PhysicalOperator *physicalOperator);
varArrayList     *getColMeta(PhysicalOperator *physicalOperator);
void              setParentType(PhysicalOperator *physicalOperator, PhysicalOperatorType parentType);
void              PhysicalOperatorToString(PhysicalOperator *physicalOperator, int depth);
void              PhysicalOperatorDestroy(PhysicalOperator *physicalOperator);
void              PhysicalOperatorPointerDestroy(void *data);  // TODO: implement this function

#endif  // PHYSICAL_OPERATOR_H
