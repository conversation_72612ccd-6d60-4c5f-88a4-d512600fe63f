#ifndef INSERT_PHYSICAL_OPERATOR_H
#define INSERT_PHYSICAL_OPERATOR_H
#include "physical_operator.h"
typedef struct InsertPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;  // element type:<PhysicalOperator*>
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文
  BtreeTable          *table;
  varArrayList        *valuelists;  // element type:<varArrayList>
} InsertPhysicalOperator;

int InsertPhysOperInit(InsertPhysicalOperator *insertPhysOper);
int InsertPhysOperOpen(InsertPhysicalOperator *insertPhysOper, SQLStageEvent *sqlEvent);
int InsertPhysOperNext(InsertPhysicalOperator *insertPhysOper, SQLStageEvent *sqlEvent);
int InsertPhysOperClose(InsertPhysicalOperator *insertPhysOper, SQLStageEvent *sqlEvent);
void InsertPhysOperDestroy(InsertPhysicalOperator *insertPhysOper);
void InsertPhysOperPointerDestroy(void *data);
#endif  // INSERT_PHYSICAL_OPERATOR_H