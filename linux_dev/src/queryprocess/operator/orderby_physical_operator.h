#ifndef ORDERBY_PHYSICAL_OPERATOR_H
#define ORDERBY_PHYSICAL_OPERATOR_H
#include "exec_tuple.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "vararraylist.h"
typedef struct SplicedTuple SplicedTuple;
typedef struct OrderByPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;      // SQL生命周期上下文
  varArrayList        *orderbyUnits;  // 排序依据的列，可能来自order by 或者 group by
  varArrayList        *exprs;         // 上层算子需要的列，可能是聚合的列，也可能是普通的列，但都是fieldExpr
  varArrayList        *values;
  varArrayList        *orderedIndex;
  int                  iter;
  Record              *currentTuple;
} OrderByPhysicalOperator;

// 用于比较的结构体
typedef struct
{
  varArrayList *cells;  // 用于排序的values
  int           index;  // 用于排序的index
} CmpPair;
void CmpPairDestroy(CmpPair *pair);
void CmpPairPointerDestroy(void *data);

int     OrderByPhysOperInit(OrderByPhysicalOperator *orderbyPhysOper, PhysicalOperatorType type);
int     OrderByPhysOperOpen(OrderByPhysicalOperator *orderbyPhysOper, SQLStageEvent *sqlEvent);
int     OrderByPhysOperNext(OrderByPhysicalOperator *orderbyPhysOper, SQLStageEvent *sqlEvent);
int     OrderByPhysOperClose(OrderByPhysicalOperator *orderbyPhysOper, SQLStageEvent *sqlEvent);
int     OrderBySetColMeta(OrderByPhysicalOperator *orderbyPhysOper);
Record *OrderByPhysOperGetCurrentTuple(OrderByPhysicalOperator *orderbyPhysOper);
void    OrderByPhysOperDestroy(OrderByPhysicalOperator *orderbyPhysOper);
void    OrderByPhysOperPointerDestroy(void *data);
#endif  // ORDERBY_PHYSICAL_OPERATOR_H
