/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-25 22:04:44
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-07-30 18:21:24
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/index_scan_logical_operator.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%
 */
#ifndef INDEX_SCAN_LOGICAL_OPERATOR_H
#define INDEX_SCAN_LOGICAL_OPERATOR_H
#include "btreetable.h"
#include "hash.h"
#include "parse_defs.h"
#include "vararraylist.h"

typedef struct IndexScanLogicalOperator
{
  LogicalOperatorType type;
  varArrayList       *children;
  varArrayList       *expressions;
  IndexType           indexType;   //* 索引类型
  HashIndex          *index;       //* 目前仅支持哈希索引
  varArrayList       *otherExprs;  //* 非索引的其他条件
  BtreeTable         *table;       //* 索引表结构体
  Value              *indexVal;    //* 索引查找的值
} IndexScanLogicalOperator;

#define CREATE_INDEX_SCAN_LOGIOPER(oper)                                               \
  do {                                                                                 \
    (oper) = (IndexScanLogicalOperator *)my_malloc0(sizeof(IndexScanLogicalOperator)); \
    if ((oper)) {                                                                      \
      (oper)->type        = LO_INDEX_SCAN;                                             \
      (oper)->children    = NULL;                                                      \
      (oper)->expressions = NULL;                                                      \
      (oper)->otherExprs  = NULL;                                                      \
      (oper)->table       = NULL;                                                      \
      (oper)->index       = NULL;                                                      \
    }                                                                                  \
  } while (0)

#define INDEX_SCAN_LOGIOPER_DESTROY(oper)          \
  do {                                             \
    if ((oper)) {                                  \
      if ((oper)->children) {                      \
        varArrayListDestroy(&(oper)->children);    \
      }                                            \
      if ((oper)->expressions) {                   \
        varArrayListDestroy(&(oper)->expressions); \
      }                                            \
      if ((oper)->otherExprs) {                    \
        varArrayListDestroy(&(oper)->otherExprs);  \
      }                                            \
      my_free((oper));                                \
    }                                              \
  } while (0)

#endif