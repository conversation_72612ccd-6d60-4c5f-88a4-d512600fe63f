#include "calc_logical_operator.h"
void CalcLogicalOperatorDestroy(CalcLogicalOperator *calcLogiOper)
{
  if (calcLogiOper == NULL) {
    return;
  }
  if (calcLogiOper->children != NULL) {
    varArrayListDestroy(&calcLogiOper->children);
  }

  if (calcLogiOper->expressions != NULL) {
    varArrayListDestroy(&calcLogiOper->expressions);
  }
  my_free(calcLogiOper);
}

void CalcLogicalOperatorPointerDestroy(void *data)
{
  CalcLogicalOperator **calcLogiOper = (CalcLogicalOperator **)data;
  if (calcLogiOper == NULL || *calcLogiOper == NULL) {
    return;
  }
  CalcLogicalOperatorDestroy(*calcLogiOper);
  *calcLogiOper = NULL;
}
