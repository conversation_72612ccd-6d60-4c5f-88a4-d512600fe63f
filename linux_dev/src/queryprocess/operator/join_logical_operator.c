#include "join_logical_operator.h"
#include "vararraylist.h"

void JoinLogiOperDestroy(JoinLogicalOperator *joinLogiOper)
{
  if (joinLogiOper->children != NULL) {
    varArrayListDestroy(&joinLogiOper->children);
  }
  if (joinLogiOper->expressions != NULL) {
    varArrayListDestroy(&joinLogiOper->expressions);
  }
  // if (joinLogiOper->pkExprs != NULL) {
  //   varArrayListDestroy(&joinLogiOper->pkExprs);
  // }
  // if (joinLogiOper->otherExprs != NULL) {
  //   varArrayListDestroy(&joinLogiOper->otherExprs);
  // }
  my_free(joinLogiOper);
}

/**
 * @description: 相较于上个函数，此函数会释放内部的表达式List，但是不释放表达式
 * @param {JoinLogicalOperator} *joinLogiOper
 * @return {*}
 */
void JoinLogiOperFullDestroy(JoinLogicalOperator *joinLogiOper)
{
  if (joinLogiOper == NULL) {
    return;
  }
  if (joinLogiOper->children != NULL) {
    varArrayListDestroy(&joinLogiOper->children);
  }
  if (joinLogiOper->expressions != NULL) {
    varArrayListDestroy(&joinLogiOper->expressions);
  }
  if (joinLogiOper->pkExprs != NULL) {
    varArrayListDestroy(&joinLogiOper->pkExprs);
  }
  if (joinLogiOper->otherExprs != NULL) {
    varArrayListDestroy(&joinLogiOper->otherExprs);
  }
  my_free(joinLogiOper);
}

void JoinLogiOperPointerDestroy(void *data)
{
  JoinLogicalOperator **joinLogiOper = (JoinLogicalOperator **)data;
  if (joinLogiOper == NULL || *joinLogiOper == NULL) {
    return;
  }
  JoinLogiOperDestroy(*joinLogiOper);
  *joinLogiOper = NULL;
}
