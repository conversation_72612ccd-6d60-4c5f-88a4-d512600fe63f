#ifndef JOIN_LOGICAL_OPERATOR_H
#define JOIN_LOGICAL_OPERATOR_H
#include "logical_operator.h"
#include "vararraylist.h"

typedef struct JoinLogicalOperator
{
  LogicalOperatorType type;
  varArrayList       *children;
  varArrayList       *expressions;  /* 相等的连接条件 */
  varArrayList       *pkExprs;      /* 主键连接条件 */
  varArrayList       *otherExprs;   /* 其他的连接条件 */
  JoinType            joinType;     /* 对应的物理连接的类型 */
} JoinLogicalOperator;

void JoinLogiOperDestroy(JoinLogicalOperator *joinLogiOper);
void JoinLogiOperFullDestroy(JoinLogicalOperator *joinLogiOper);
void JoinLogiOperPointerDestroy(void *data);
#endif  // JOIN_LOGICAL_OPERATOR_H
