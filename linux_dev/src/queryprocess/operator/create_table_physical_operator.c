#include "create_table_physical_operator.h"
#include "project_physical_operator.h"
#include "expression.h"
#include "queryexecutor.h"
#include "limits.h"
#include "assert.h"
#include "value.h"
#include "vararraylist.h"
#include <float.h>
extern int  sqlInsert(struct GNCDB *db, int *affectedRows, char *tableName, varArrayList *values, Transaction *txn);
static void fieldGetMinMaxValue(int len, FieldType type, double *minValue, double *maxValue)
{
  double min = -2147483647.0;
  double max = 2147483647.0;
  // TODO: 如何确定最大值和最小值
  switch (type) {
    case FIELDTYPE_DATE:
      // *minValue = 0;
      // *maxValue = INT_MAX;
      *minValue = min;
      *maxValue = max;
      break;
    case FIELDTYPE_INTEGER:
      // *minValue = INT_MIN;
      // *maxValue = INT_MAX;
      *minValue = min;
      *maxValue = max;
      break;
    case FIELDTYPE_REAL:
      // *minValue = -DBL_MAX;
      // *maxValue = DBL_MAX;
      *minValue = min;
      *maxValue = max;
      break;
    case FIELDTYPE_VARCHAR:
      *minValue = 0;
      *maxValue = len + 1;//留一个作为结束符
      break;
    case FIELDTYPE_BLOB:
      // 自定义？
      *minValue = 0;
      *maxValue = 255;
      break;
    case FIELDTYPE_DATETIME:
      // *minValue = 0;
      // *maxValue = INT_MAX;
      *minValue = min;
      *maxValue = max;
      break;
    case FIELDTYPE_TEXT:
      *minValue = 0;
      /* 最大65535字节 */
      *maxValue = 65535;
      break;
    default:
      // *minValue = INT_MIN;
      // *maxValue = INT_MAX;
      *minValue = min;
      *maxValue = max;
      break;
  }
}
int sqlCreateTable(SQLStageEvent *sqlEvent, char *tableName, varArrayList *attrInfos)
{
  /* 处理表名超出长度 */
  int          rc = 0;
  int          i  = 0;
  Transaction *tx = NULL;
  // va_list ap;
  varArrayList     *primaryKeyIndex = NULL;
  varArrayList     *primaryKeyType  = NULL;
  varArrayList     *primaryKeyOffset = NULL;
  varArrayList     *primaryKeyVarcharLen = NULL;
  varArrayList     *columnList      = NULL;
  HashMap          *fieldMap        = NULL;
  char             *fieldName       = NULL;
  int               fieldType       = 0;
  int               canBeNull       = 0;
  int               isPrimaryKey    = 0;
  int               len             = 0;
  double            minValue        = 0;
  double            maxValue        = 0;
  int               maxValueInt     = 0;
  ColumnConstraint *constraint      = NULL;
  Column           *column          = NULL;
  int               rowIdPrimaryKey = 1;
  // int createTimePrimaryKey = 1;
  int               rowIdPrimaryType = FIELDTYPE_INTEGER;
  ColumnConstraint *constraint1      = NULL;
  Column           *column1          = NULL;
  ColumnConstraint *constraint2      = NULL;
  Column           *column2          = NULL;
  ColumnConstraint *constraint3      = NULL;
  Column           *column3          = NULL;
  TableSchema      *tableSchema      = NULL;
  int               maxrownum        = 0;
  int               columnNum        = 0;
  int               offset           = 0;
  if (tableName == NULL) {
    return GNCDB_PARAMNULL;
  }
  // if (!validString(tableName)) // 可以用词法分析与语法分析判断
  // {
  // 	return GNCDB_PARAM_INVALID;
  // }

  /* 处理重复或者与master和schema表重名的表名 */
  if (hashMapExists(sqlEvent->db->catalog->tableMap, tableName) || strcmp(tableName, "master") == 0 ||
      strcmp(tableName, "schema") == 0) {
    return GNCDB_TABLE_EXIST;
  }

  tx = sqlEvent->txn;
  // va_start(ap, columnNum);
  columnNum = attrInfos->elementCount;
  // 主键索引
  primaryKeyIndex = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (primaryKeyIndex == NULL) {
    // transactionRollback(tx, sqlEvent->db);
    return GNCDB_MEM;
  }
  // 主键类型
  primaryKeyType = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (primaryKeyType == NULL) {
    // transactionRollback(tx, sqlEvent->db);
    varArrayListDestroy(&primaryKeyIndex);
    return GNCDB_MEM;
  }
  primaryKeyOffset = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
	if (primaryKeyOffset == NULL)
	{
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
		return GNCDB_MEM;
	}
	primaryKeyVarcharLen = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
	if (primaryKeyVarcharLen == NULL)
	{
		varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
		varArrayListDestroy(&primaryKeyOffset);
		return GNCDB_MEM;
	}

  // 列 columnNum + 3
  columnList = varArrayListCreate(DISORDER, BYTES_POINTER, columnNum + 3, NULL, arrayColumnDestroy);
  offset = GET_BITMAP_LENGTH(columnNum + 3);
  if (columnList == NULL) {
    // transactionRollback(tx, sqlEvent->db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    return GNCDB_MEM;
  }

  /* 所有参数的正确性检查 */
  fieldMap = hashMapCreate(STRKEY, columnNum, NULL);
  if (fieldMap == NULL) {
    // transactionRollback(tx, sqlEvent->db);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }
  for (i = 0; i < columnNum; i++) {

    fieldName    = ((AttrInfoSqlNode *)varArrayListGetPointer(attrInfos, i))->name;
    fieldType    = ((AttrInfoSqlNode *)varArrayListGetPointer(attrInfos, i))->type;
    canBeNull    = ((AttrInfoSqlNode *)varArrayListGetPointer(attrInfos, i))->canBeNull;
    isPrimaryKey = ((AttrInfoSqlNode *)varArrayListGetPointer(attrInfos, i))->isPrimaryKey;
    len          = ((AttrInfoSqlNode *)varArrayListGetPointer(attrInfos, i))->length;
    /* 通过hash表判断是否出现重复的属性名 */
    if (hashMapExists(fieldMap, fieldName)) {
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyType);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      hashMapDestroy(&fieldMap);
      varArrayListDestroy(&columnList);
      return GNCDB_PARAM_INVALID;
    } else {
      hashMapPut(fieldMap, fieldName, &fieldType);
    }

    if (isPrimaryKey == 1) {
      if (canBeNull == 1) {
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return GNCDB_PRIMARYKEY_NOTNULL;
      }

      rc = varArrayListAdd(primaryKeyIndex, &i);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return rc;
      }

      rc = varArrayListAdd(primaryKeyType, &fieldType);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return rc;
      }
      rc = varArrayListAdd(primaryKeyOffset, &offset);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return rc;
      }
    }
    // 最大最小值按要求修改
    fieldGetMinMaxValue(len, fieldType, &minValue, &maxValue);
    if (maxValue < minValue) {
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyType);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return GNCDB_PARAM_INVALID;
    }
    if(fieldType == FIELDTYPE_VARCHAR && isPrimaryKey==1){
      maxValueInt = (int)maxValue;
      rc = varArrayListAdd(primaryKeyVarcharLen, &maxValueInt);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&primaryKeyIndex);
        varArrayListDestroy(&primaryKeyType);
        varArrayListDestroy(&primaryKeyOffset);
        varArrayListDestroy(&primaryKeyVarcharLen);
        varArrayListDestroy(&columnList);
        hashMapDestroy(&fieldMap);
        return rc;
      }
    }

    constraint = columnConstraintConstruct(minValue, maxValue, canBeNull, isPrimaryKey);
    if (constraint == NULL) {
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyType);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return GNCDB_MEM;
    }

    column = columnConstruct(fieldType, fieldName, constraint,offset);
    if (column == NULL) {
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyType);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      columnConstraintDestroy(constraint);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return GNCDB_MEM;
    }
    column->tableName = my_malloc0(strlen(tableName) + 1);
    strcpy(column->tableName, tableName);
    switch (fieldType)
    {
      case FIELDTYPE_INTEGER:
        offset += INT_SIZE;
        break;
      case FIELDTYPE_REAL:
        offset += DOUBLE_SIZE;
        break;
      case FIELDTYPE_VARCHAR:
        offset += maxValue;
        break;
      case FIELDTYPE_BLOB:
        offset += 2 * INT_SIZE;
        break;
      default:
        break;
    }

    rc = varArrayListAddPointer(columnList, column);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&primaryKeyIndex);
      varArrayListDestroy(&primaryKeyType);
      varArrayListDestroy(&primaryKeyOffset);
      varArrayListDestroy(&primaryKeyVarcharLen);
      columnDestroy(column);
      varArrayListDestroy(&columnList);
      hashMapDestroy(&fieldMap);
      return rc;
    }
  }
  hashMapDestroy(&fieldMap);
  // 自定义maxrownum？
  maxrownum = 10000000;
  if (maxrownum <= 0) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return GNCDB_PARAM_INVALID;
  }

  rowIdPrimaryKey = 0;
  if (primaryKeyIndex->elementCount < 1) {
    rowIdPrimaryKey = 1;
    rc              = varArrayListAdd(primaryKeyIndex, &columnNum);
    rc              = varArrayListAdd(primaryKeyType, &rowIdPrimaryType);
    rc              = varArrayListAdd(primaryKeyOffset, &offset);
  }

  constraint1 = columnConstraintConstruct(0, maxrownum, 0, rowIdPrimaryKey);
  if (constraint1 == NULL) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  column1 = columnConstruct(FIELDTYPE_INTEGER, "rowId", constraint1,offset);
  if (column1 == NULL) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    columnConstraintDestroy(constraint1);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }
  column1->tableName = my_malloc0(strlen(tableName) + 1);
	strcpy(column1->tableName, tableName);
  offset += INT_SIZE;

  rc = varArrayListAddPointer(columnList, column1);
  if (rc != GNCDB_SUCCESS) {
    columnDestroy(column1);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return rc;
  }

  constraint2 = columnConstraintConstruct(INT_MIN, INT_MAX, 0, 0);
  if (constraint2 == NULL) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  column2 = columnConstruct(FIELDTYPE_INTEGER, "createTime", constraint2,offset);
  if (column2 == NULL) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    columnConstraintDestroy(constraint2);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }
  column2->tableName = my_malloc0(strlen(tableName) + 1);
	strcpy(column2->tableName, tableName);
  offset += INT_SIZE;

  rc = varArrayListAddPointer(columnList, column2);
  if (rc != GNCDB_SUCCESS) {
    columnDestroy(column2);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return rc;
  }

  constraint3 = columnConstraintConstruct(INT_MIN, INT_MAX, 0, 0);
  if (constraint3 == NULL) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  column3 = columnConstruct(FIELDTYPE_INTEGER, "updateTime", constraint3, offset);
  if (column3 == NULL) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    columnConstraintDestroy(constraint3);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }
  column3->tableName = my_malloc0(strlen(tableName) + 1);
	strcpy(column3->tableName, tableName);
  offset+=INT_SIZE;

  rc = varArrayListAddPointer(columnList, column3);
  if (rc != GNCDB_SUCCESS) {
    columnDestroy(column3);
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return rc;
  }

  tableSchema = tableSchemaConstruct(maxrownum, columnNum + 3, columnList);
  if (tableSchema == NULL) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    varArrayListDestroy(&columnList);
    return GNCDB_MEM;
  }

  rc = executorCreateTable(sqlEvent->db, tableName, tableSchema, primaryKeyIndex, primaryKeyType, primaryKeyOffset, primaryKeyVarcharLen, tx);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&primaryKeyIndex);
    varArrayListDestroy(&primaryKeyType);
    varArrayListDestroy(&primaryKeyOffset);
    varArrayListDestroy(&primaryKeyVarcharLen);
    tableSchemaDestroy(tableSchema);
    return rc;
  }
  return GNCDB_SUCCESS;
}

int CreateTablePhysOperInit(CreateTablePhysicalOperator *createTablePhysOper, PhysicalOperatorType type)
{
  createTablePhysOper->type        = type;
  createTablePhysOper->children    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  createTablePhysOper->parentTuple = NULL;

  createTablePhysOper->db        = NULL;
  createTablePhysOper->tableName = NULL;
  createTablePhysOper->attrInfos = NULL;
  return 0;
}

int CreateTablePhysOperOpen(CreateTablePhysicalOperator *createTableOper, SQLStageEvent *sqlEvent)
{
  int                      rc           = GNCDB_SUCCESS;
  PhysicalOperator        *child        = NULL;
  varArrayList            *values       = NULL;
  ProjectPhysicalOperator *projectOper  = NULL;
  // AbstractTuple           *tuple        = NULL;
  Record                  *tuple        = NULL;
  int                      i            = 0;
  Value                   *value        = NULL;
  Expression              *expr         = NULL;
  int                      affectedRows = 0;
  varArrayList            *colMetas     = NULL;

  /*1.open子算子*/
  if (createTableOper->children != NULL && createTableOper->children->elementCount > 0) {
    child = (PhysicalOperator *)varArrayListGetPointer(createTableOper->children, 0);
    rc    = PhysicalOperatorOpen(child, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  /*2.执行实际的建表操作*/
  assert(createTableOper->attrInfos != NULL);
  rc = sqlCreateTable(sqlEvent, createTableOper->tableName, createTableOper->attrInfos);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /*3.如果有子算子，就是后跟select的情况，要将select的结果插入到新建的表*/
  if (createTableOper->children != NULL && createTableOper->children->elementCount > 0) {
    values = varArrayListCreate(DISORDER, sizeof(Value *), 0, NULL, valuePointerDestroy);
    child  = (PhysicalOperator *)varArrayListGetPointer(createTableOper->children, 0);
    assert(child->type == PO_PROJECT);
    projectOper = (ProjectPhysicalOperator *)child;
    colMetas    = getColMeta(child);
    /*3.1将select的结果插入表中*/
    while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext(child, sqlEvent))) {
      varArrayListClear(values);
      tuple = GetCurrentTuple(child);
      // for (i = 0; i < projectOper->tuple->exprs->elementCount; i++) {
      /*3.1.1根据colmetas从tuple中提取值组成要插入的元组*/
      for (i = 0; i < colMetas->elementCount; i++) {
        value = valueCreate();
        // expr = (Expression *)varArrayListGetPointer(projectOper->tuple->exprs, i);
        expr = varArrayListGetPointer(projectOper->projectedFields, i);
        rc   = expressionGetValueNCP(expr, tuple, colMetas, value);
        if (rc != GNCDB_SUCCESS) {
          valueDestroy(&value);
          varArrayListDestroy(&values);
          return rc;
        }
        varArrayListAddPointer(values, value);
      }
      /*3.1.2将元组插入表中*/
      rc = sqlInsert(sqlEvent->db, &affectedRows, createTableOper->tableName, values, sqlEvent->txn);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&values);
        return rc;
      }
    }
    rc = rc == GNCDB_NEXT_EOF ? GNCDB_SUCCESS : rc;
  }
  return rc;
}

int CreateTablePhysOperNext(CreateTablePhysicalOperator *createTablePhysOper, SQLStageEvent *sqlEvent)
{
  return GNCDB_NEXT_EOF;
}

int CreateTablePhysOperClose(CreateTablePhysicalOperator *createTablePhysOper, SQLStageEvent *sqlEvent)
{
  int               rc    = GNCDB_SUCCESS;
  PhysicalOperator *child = NULL;

  if (createTablePhysOper->children != NULL && createTablePhysOper->children->elementCount > 0) {
    child = (PhysicalOperator *)varArrayListGetPointer(createTablePhysOper->children, 0);
    rc    = PhysicalOperatorClose(child, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to close child oper, rc=%d\n", rc);
      return rc;
    }
  }
  return rc;
}

// AbstractTuple *CreateTablePhysOperGetCurrentTuple(CreateTablePhysicalOperator *createTablePhysOper) { return NULL; }
Record *CreateTablePhysOperGetCurrentTuple(CreateTablePhysicalOperator *createTablePhysOper) { return NULL; }
void CreateTablePhysOperDestroy(CreateTablePhysicalOperator *createTablePhysOper)
{
  if (createTablePhysOper == NULL) {
    return;
  }

  if (createTablePhysOper->children != NULL) {
    varArrayListDestroy(&createTablePhysOper->children);
  }

  if (createTablePhysOper->tableName != NULL) {
    my_free(createTablePhysOper->tableName);
  }

  if (createTablePhysOper->attrInfos != NULL) {
    varArrayListDestroy(&createTablePhysOper->attrInfos);
  }

  my_free(createTablePhysOper);
}

void CreateTablePhysOperPointerDestroy(void *data)
{
  CreateTablePhysicalOperator **createTablePhysOper = (CreateTablePhysicalOperator **)data;
  if (createTablePhysOper == NULL || *createTablePhysOper == NULL) {
    return;
  }
  CreateTablePhysOperDestroy(*createTablePhysOper);
  *createTablePhysOper = NULL;
}
