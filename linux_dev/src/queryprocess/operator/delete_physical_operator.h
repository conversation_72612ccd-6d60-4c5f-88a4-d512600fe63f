#ifndef DELETE_PHYSICAL_OPERATOR_H
#define DELETE_PHYSICAL_OPERATOR_H
#include "btreecursor.h"
#include "hashmap.h"
#include "physical_operator.h"
typedef struct DeletePhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;  // element type:<PhysicalOperator*>
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  BtreeTable          *table;
  Transaction         *txn;
  BtreeCursor         *cursor;
} DeletePhysicalOperator;

int     DeletePhysOperInit(DeletePhysicalOperator *deletePhysOper, PhysicalOperatorType type);
int     DeletePhysOperOpen(DeletePhysicalOperator *deletePhysOper, SQLStageEvent *sqlEvent);
int     DeletePhysOperNext(DeletePhysicalOperator *deletePhysOper, SQLStageEvent *sqlEvent);
int     DeletePhysOperClose(DeletePhysicalOperator *deletePhysOper, SQLStageEvent *sqlEvent);
int     DeleteSetColMeta(DeletePhysicalOperator *deletePhysOper);
Record *DeletePhysOperGetCurrentTuple(DeletePhysicalOperator *deletePhysOper);
void    DeletePhysOperDestroy(DeletePhysicalOperator *deletePhysOper);
void    DeletePhysOperPointerDestory(void *data);

#endif  // DELETE_PHYSICAL_OPERATOR_H
