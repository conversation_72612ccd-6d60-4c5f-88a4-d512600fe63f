#include "table_scan_physical_operator.h"
#include "btreecursor.h"
#include "btreetable.h"
#include "catalog.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "parse_defs.h"
#include "tuple.h"
#include "typedefine.h"
#include "utils.h"
#include "gncdb.h"  // Include the header file where struct GNCDB is defined
#include "value.h"
#include "vararraylist.h"
#include "tranmanager.h"
#include "yacc_sql.h"
#include <assert.h>
#include <float.h>
#include <limits.h>
#include <stdarg.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>
#include "btreetable.h"
#include "transaction.h"

TableScanPhysicalOperator *TableScanPhysOperCreate()
{
  TableScanPhysicalOperator *tableScanPhysOper =
      (TableScanPhysicalOperator *)my_malloc0(sizeof(TableScanPhysicalOperator));
  TableScanPhysicalOperatorInit(tableScanPhysOper);
  return tableScanPhysOper;
}

int ScanSetColMeta(TableScanPhysicalOperator *tableScanPhysOper)
{
  // TableSchema *tableSchema = NULL;
  // ColMeta     *colMeta     = NULL;
  // Column      *column      = NULL;
  // int          offset      = 0;
  if (tableScanPhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  if (tableScanPhysOper->tableSchema == NULL) {
    return GNCDB_PARAM_INVALID;
  }

  if (tableScanPhysOper->cols == NULL) {
    tableScanPhysOper->cols = tableScanPhysOper->tableSchema->tablecolMetas;
  }

  return GNCDB_SUCCESS;
}

// varArrayList *ScanGetCols(TableScanPhysicalOperator *tableScanPhysOper) { return tableScanPhysOper->cols; }

void TableScanPhysicalOperatorInit(TableScanPhysicalOperator *tableScanPhysOper)
{
  if (tableScanPhysOper == NULL) {
    return;
  }
  tableScanPhysOper->type        = PO_TABLE_SCAN;
  tableScanPhysOper->children    = varArrayListCreate(0, BYTES_POINTER, 0, NULL, NULL);
  tableScanPhysOper->record      = NULL;
  tableScanPhysOper->tableSchema = NULL;
  tableScanPhysOper->cursor      = NULL;
  tableScanPhysOper->parentTuple = NULL;
  tableScanPhysOper->predicates  = NULL;
}

bool fieldIsPrimaryKey(varArrayList *primaryKey, const char *fieldName)
{
  Column *column = NULL;
  int     i      = 0;
  for (i = 0; i < primaryKey->elementCount; i++) {
    column = (Column *)varArrayListGetPointer(primaryKey, i);
    if (strcmp(column->fieldName, fieldName) == 0) {
      return true;
    }
  }
  return false;
}

int collectcond(Expression *expr, varArrayList *list, int addtionalParamNum, va_list args)
{
  ConjunctionExpr *conjExpr      = NULL;
  ComparisonExpr  *compExpr      = NULL;
  char            *fieldName     = NULL;
  varArrayList    *primaryKey    = NULL;
  HashMap         *pkFieldMap    = NULL;
  varArrayList    *fieldcondList = NULL;
  va_list          argsCopy;
  Expression      *tempExpr = NULL;
  va_copy(argsCopy, args);
  if (expr->type == ETG_CONJUNCTION) {
    conjExpr = (ConjunctionExpr *)expr;
    if (conjExpr->conjunctionType == CJET_OR) {
      varArrayListClear(list);
      va_end(argsCopy);
      return GNCDB_PARAM_INVALID;
    }
  } else if (expr->type == ETG_COMPARISON) {
    primaryKey = va_arg(args, varArrayList *);
    compExpr   = (ComparisonExpr *)expr;
    pkFieldMap = va_arg(args, HashMap *);
    if (compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_VALUE) {
      fieldName = ((FieldExpr *)compExpr->left)->fieldName;
      if (fieldName == NULL) {
        varArrayListClear(list);
        va_end(argsCopy);
        return GNCDB_PARAM_INVALID;
      }
      if (fieldIsPrimaryKey(primaryKey, fieldName)) {
        varArrayListAddPointer(list, expr);
        fieldcondList = (varArrayList *)hashMapGet(pkFieldMap, fieldName);
        if (fieldcondList == NULL) {
          varArrayListClear(list);
          va_end(argsCopy);
          return GNCDB_PARAM_INVALID;
        }
        varArrayListAddPointer(fieldcondList, expr);
      }
    } else if (compExpr->left->type == ETG_VALUE && compExpr->right->type == ETG_FIELD) {
      // 如果是value在左边，field在右边，交换两者
      tempExpr        = compExpr->left;
      compExpr->left  = compExpr->right;
      compExpr->right = tempExpr;
      compExpr->comp  = swapOp(compExpr->comp);
      fieldName       = ((FieldExpr *)compExpr->left)->fieldName;
      if (fieldName == NULL) {
        varArrayListClear(list);
        va_end(argsCopy);
        return GNCDB_PARAM_INVALID;
      }
      if (fieldIsPrimaryKey(primaryKey, fieldName)) {
        varArrayListAddPointer(list, expr);
        fieldcondList = (varArrayList *)hashMapGet(pkFieldMap, fieldName);
        if (fieldcondList == NULL) {
          varArrayListClear(list);
          va_end(argsCopy);
          return GNCDB_PARAM_INVALID;
        }
        varArrayListAddPointer(fieldcondList, expr);
      }
    }
  }
  va_end(argsCopy);
  return GNCDB_SUCCESS;
}

void pkFieldMapDestroy(HashMap **pkFieldMap)
{
  HashMapIterator *iter          = NULL;
  varArrayList    *fieldcondList = NULL;
  iter                           = createHashMapIterator(*pkFieldMap);
  while (hasNextHashMapIterator(iter)) {
    iter          = nextHashMapIterator(iter);
    fieldcondList = (varArrayList *)iter->entry->value;
    varArrayListDestroy(&fieldcondList);
  }
  hashMapDestroy(pkFieldMap);
  freeHashMapIterator(&iter);
}

/**
 * @brief b+树主键索引最左匹配
 *
 * @param tableScanPhysOper   tableScan物理操作符
 * @param db                数据库
 * @param txn            事务
 * @return int          状态码
 */
int TableScanPhysOperSetStEdCursor(TableScanPhysicalOperator *tableScanPhysOper, GNCDB *db, Transaction *txn)
{
  // 收集所有包含主键的condition
  varArrayList   *conditions     = NULL;
  varArrayList   *primaryKeyList = NULL;
  int             rc             = GNCDB_SUCCESS;
  TableSchema    *tableSchema    = NULL;
  HashMap        *pkFieldMap     = NULL;
  int             i = 0, j = 0;
  Expression     *expr                 = NULL;
  ComparisonExpr *compExpr             = NULL;
  Column         *column               = NULL;
  varArrayList   *fieldcondList        = NULL;
  int            *maxPrimaryIntKey1    = NULL;
  double         *maxPrimaryDoubleKey1 = NULL;
  char           *maxPrimaryStringKey1 = NULL;
  int            *minPrimaryIntKey1    = NULL;
  double         *minPrimaryDoubleKey1 = NULL;
  char           *minPrimaryStringKey1 = NULL;
  ValueExpr      *valueExpr            = NULL;
  char           *tmpStr               = NULL;
  varArrayList   *startKeyValue        = NULL;
  varArrayList   *endKeyValue          = NULL;
  bool            hasPointQuery        = false;
  bool            wholePointQuery       = true;

  if (tableScanPhysOper == NULL || db == NULL || txn == NULL) {
    return GNCDB_PARAM_INVALID;
  }

  startKeyValue = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, keyValueDestroy);
  if (startKeyValue == NULL) {
    return GNCDB_MEM;
  }
  endKeyValue = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, keyValueDestroy);
  if (endKeyValue == NULL) {
    varArrayListDestroy(&startKeyValue);
    return GNCDB_MEM;
  }

  conditions = varArrayListCreate(
      DISORDER, BYTES_POINTER, 0, NULL, NULL);  // 保存所有包含主键的条件，如果结果为空，则当前不可使用主键索引
  if (conditions == NULL) {
    varArrayListDestroy(&startKeyValue);
    varArrayListDestroy(&endKeyValue);
    return GNCDB_MEM;
  }
  tableSchema    = tableScanPhysOper->tableSchema;
  primaryKeyList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (primaryKeyList == NULL) {
    varArrayListDestroy(&startKeyValue);
    varArrayListDestroy(&endKeyValue);
    varArrayListDestroy(&conditions);
    return GNCDB_MEM;
  }

  pkFieldMap = hashMapCreate(STRKEY, 0, NULL);
  if (pkFieldMap == NULL) {
    varArrayListDestroy(&startKeyValue);
    varArrayListDestroy(&endKeyValue);
    varArrayListDestroy(&conditions);
    varArrayListDestroy(&primaryKeyList);
    return GNCDB_MEM;
  }

  for (i = 0; i < tableSchema->columnList->elementCount; i++) {
    column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
    if (column->columnConstraint->isPrimaryKey) {
      fieldcondList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
      if (fieldcondList == NULL) {
        varArrayListDestroy(&startKeyValue);
        varArrayListDestroy(&endKeyValue);
        varArrayListDestroy(&conditions);
        varArrayListDestroy(&primaryKeyList);
        pkFieldMapDestroy(&pkFieldMap);
        return GNCDB_MEM;
      }
      hashMapPut(pkFieldMap, column->fieldName, fieldcondList);
      varArrayListAddPointer(primaryKeyList, column);
      switch (column->fieldType) {
        case FIELDTYPE_INTEGER:
          maxPrimaryIntKey1  = my_malloc0(sizeof(int));
          minPrimaryIntKey1  = my_malloc0(sizeof(int));
          *maxPrimaryIntKey1 = INT_MAX;
          *minPrimaryIntKey1 = INT_MIN;
          varArrayListAddPointer(startKeyValue, minPrimaryIntKey1);
          varArrayListAddPointer(endKeyValue, maxPrimaryIntKey1);
          break;
        case FIELDTYPE_REAL:
          maxPrimaryDoubleKey1  = my_malloc0(sizeof(double));
          minPrimaryDoubleKey1  = my_malloc0(sizeof(double));
          *maxPrimaryDoubleKey1 = DBL_MAX;
          *minPrimaryDoubleKey1 = -DBL_MAX;
          varArrayListAddPointer(startKeyValue, minPrimaryDoubleKey1);
          varArrayListAddPointer(endKeyValue, maxPrimaryDoubleKey1);
          break;
        case FIELDTYPE_VARCHAR:
          maxPrimaryStringKey1 = my_malloc0(sizeof(char) * (int)(column->columnConstraint->maxValue + 1));
          minPrimaryStringKey1 = my_malloc0(sizeof(char) * (int)(column->columnConstraint->maxValue + 1));
          memset(maxPrimaryStringKey1, 0xff, column->columnConstraint->maxValue);  // set 0xff
          memset(minPrimaryStringKey1, 0x00, column->columnConstraint->maxValue);  // set 0x00
          varArrayListAddPointer(startKeyValue, minPrimaryStringKey1);
          varArrayListAddPointer(endKeyValue, maxPrimaryStringKey1);
          break;
        default:
          assert(false);  // 不应该出现这种情况
          break;          // impossible
      }
    }
  }

  for (i = 0; i < tableScanPhysOper->predicates->elementCount; i++) {
    expr = (Expression *)varArrayListGetPointer(tableScanPhysOper->predicates, i);
    if (expr == NULL) {
      continue;
    }
    exprTraverseCheckPrepare(expr, collectcond, conditions, 2, primaryKeyList, pkFieldMap);
  }

  // 收集完毕，开始构造startKeyValue
  // 1.如果conditions为空，则不可使用主键索引
  if (conditions->elementCount == 0) {
    varArrayListDestroy(&startKeyValue);
    varArrayListDestroy(&endKeyValue);
    varArrayListDestroy(&conditions);
    varArrayListDestroy(&primaryKeyList);
    pkFieldMapDestroy(&pkFieldMap);
    return GNCDB_NOT_FOUND;
  }

  // 2.根据conditions的内容，构造startKeyValue 和 endKeyValue
  for (i = 0; i < primaryKeyList->elementCount; i++) {
    column        = (Column *)varArrayListGetPointer(primaryKeyList, i);
    fieldcondList = (varArrayList *)hashMapGet(pkFieldMap, column->fieldName);
    if (fieldcondList == NULL) {
      varArrayListDestroy(&startKeyValue);
      varArrayListDestroy(&endKeyValue);
      varArrayListDestroy(&conditions);
      varArrayListDestroy(&primaryKeyList);
      pkFieldMapDestroy(&pkFieldMap);
      return GNCDB_INTERNAL;
    }
    for (j = 0; j < fieldcondList->elementCount; j++) {
      // TODO: 处理子查询的情况
      compExpr  = (ComparisonExpr *)varArrayListGetPointer(fieldcondList, j);
      valueExpr = (ValueExpr *)compExpr->right;
      if (compExpr->comp == CMPOP_EQUAL_TO) {
        hasPointQuery = true;
      } else {
        wholePointQuery = false;
      }

      {
        switch (column->fieldType) {
          case FIELDTYPE_INTEGER:
            if (compExpr->comp == CMPOP_EQUAL_TO) {
              minPrimaryIntKey1 = (int *)varArrayListGetPointer(startKeyValue, i);
              if (valueGetInt(valueExpr->value) > *minPrimaryIntKey1) {
                *minPrimaryIntKey1 = valueGetInt(valueExpr->value);
              }
              maxPrimaryIntKey1 = (int *)varArrayListGetPointer(endKeyValue, i);
              if (valueGetInt(valueExpr->value) < *maxPrimaryIntKey1) {
                *maxPrimaryIntKey1 = valueGetInt(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_LESS_THAN) {
              maxPrimaryIntKey1 = (int *)varArrayListGetPointer(endKeyValue, i);
              if (valueGetInt(valueExpr->value) < *maxPrimaryIntKey1) {
                *maxPrimaryIntKey1 = valueGetInt(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_GREAT_THAN) {
              minPrimaryIntKey1 = (int *)varArrayListGetPointer(startKeyValue, i);
              if (valueGetInt(valueExpr->value) > *minPrimaryIntKey1) {
                *minPrimaryIntKey1 = valueGetInt(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_LESS_EQUAL) {
              maxPrimaryIntKey1 = (int *)varArrayListGetPointer(endKeyValue, i);
              if (valueGetInt(valueExpr->value) <= *maxPrimaryIntKey1) {
                *maxPrimaryIntKey1 = valueGetInt(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_GREAT_EQUAL) {
              minPrimaryIntKey1 = (int *)varArrayListGetPointer(startKeyValue, i);
              if (valueGetInt(valueExpr->value) >= *minPrimaryIntKey1) {
                *minPrimaryIntKey1 = valueGetInt(valueExpr->value);
              }
            }
            // printf("minPrimaryIntKey1: %d, maxPrimaryIntKey1: %d\n", *minPrimaryIntKey1, *maxPrimaryIntKey1);
            break;
          case FIELDTYPE_REAL:
            if (compExpr->comp == CMPOP_EQUAL_TO) {
              minPrimaryDoubleKey1 = (double *)varArrayListGetPointer(startKeyValue, i);
              if (valueGetDouble(valueExpr->value) > *minPrimaryDoubleKey1) {
                *minPrimaryDoubleKey1 = valueGetDouble(valueExpr->value);
              }
              maxPrimaryDoubleKey1 = (double *)varArrayListGetPointer(endKeyValue, i);
              if (valueGetDouble(valueExpr->value) < *maxPrimaryDoubleKey1) {
                *maxPrimaryDoubleKey1 = valueGetDouble(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_LESS_THAN) {
              maxPrimaryDoubleKey1 = (double *)varArrayListGetPointer(endKeyValue, i);
              if (valueGetDouble(valueExpr->value) < *maxPrimaryDoubleKey1) {
                *maxPrimaryDoubleKey1 = valueGetDouble(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_GREAT_THAN) {
              minPrimaryDoubleKey1 = (double *)varArrayListGetPointer(startKeyValue, i);
              if (valueGetDouble(valueExpr->value) > *minPrimaryDoubleKey1) {
                *minPrimaryDoubleKey1 = valueGetDouble(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_LESS_EQUAL) {
              maxPrimaryDoubleKey1 = (double *)varArrayListGetPointer(endKeyValue, i);
              if (valueGetDouble(valueExpr->value) <= *maxPrimaryDoubleKey1) {
                *maxPrimaryDoubleKey1 = valueGetDouble(valueExpr->value);
              }
            } else if (compExpr->comp == CMPOP_GREAT_EQUAL) {
              minPrimaryDoubleKey1 = (double *)varArrayListGetPointer(startKeyValue, i);
              if (valueGetDouble(valueExpr->value) >= *minPrimaryDoubleKey1) {
                *minPrimaryDoubleKey1 = valueGetDouble(valueExpr->value);
              }
            }
            break;
          case FIELDTYPE_VARCHAR:
            tmpStr = valueGetString(valueExpr->value);
            if (compExpr->comp == CMPOP_EQUAL_TO) {
              minPrimaryStringKey1 = (char *)varArrayListGetPointer(startKeyValue, i);
              if (strcmp(tmpStr, minPrimaryStringKey1) > 0) {
                strncpy(minPrimaryStringKey1, tmpStr, (int)column->columnConstraint->maxValue);
                minPrimaryStringKey1[(int)column->columnConstraint->maxValue] = '\0';
              }
              maxPrimaryStringKey1 = (char *)varArrayListGetPointer(endKeyValue, i);
              if (strcmp(tmpStr, maxPrimaryStringKey1) < 0) {
                strncpy(maxPrimaryStringKey1, tmpStr, (int)column->columnConstraint->maxValue);
                maxPrimaryStringKey1[(int)column->columnConstraint->maxValue] = '\0';
              }
            } else if (compExpr->comp == CMPOP_LESS_THAN) {
              maxPrimaryStringKey1 = (char *)varArrayListGetPointer(endKeyValue, i);
              if (strcmp(tmpStr, maxPrimaryStringKey1) < 0) {
                strncpy(maxPrimaryStringKey1, tmpStr, (int)column->columnConstraint->maxValue);
                maxPrimaryStringKey1[(int)column->columnConstraint->maxValue] = '\0';
              }
            } else if (compExpr->comp == CMPOP_GREAT_THAN) {
              minPrimaryStringKey1 = (char *)varArrayListGetPointer(startKeyValue, i);
              if (strcmp(tmpStr, minPrimaryStringKey1) > 0) {
                strncpy(minPrimaryStringKey1, tmpStr, (int)column->columnConstraint->maxValue);
                minPrimaryStringKey1[(int)column->columnConstraint->maxValue] = '\0';
              }
            } else if (compExpr->comp == CMPOP_LESS_EQUAL) {
              maxPrimaryStringKey1 = (char *)varArrayListGetPointer(endKeyValue, i);
              if (strcmp(tmpStr, maxPrimaryStringKey1) <= 0) {
                strncpy(maxPrimaryStringKey1, tmpStr, (int)column->columnConstraint->maxValue);
                maxPrimaryStringKey1[(int)column->columnConstraint->maxValue] = '\0';
              }
            } else if (compExpr->comp == CMPOP_GREAT_EQUAL) {
              minPrimaryStringKey1 = (char *)varArrayListGetPointer(startKeyValue, i);
              if (strcmp(tmpStr, minPrimaryStringKey1) >= 0) {
                strncpy(minPrimaryStringKey1, tmpStr, (int)column->columnConstraint->maxValue);
                minPrimaryStringKey1[(int)column->columnConstraint->maxValue] = '\0';
              }
            }
            my_free(tmpStr);
            break;
          default:
            assert(false);  // 不应该出现这种情况
            break;          // impossible
        }
      }
    }
  }

  // 3.构造cursor
  tableScanPhysOper->cursor = btreeCursorConstruct(tableScanPhysOper->table->tableName, db, startKeyValue, txn);
  if (tableScanPhysOper->cursor == NULL) {
    varArrayListDestroy(&startKeyValue);
    varArrayListDestroy(&endKeyValue);
    varArrayListDestroy(&conditions);
    varArrayListDestroy(&primaryKeyList);
    pkFieldMapDestroy(&pkFieldMap);
    tableScanPhysOper->isNoNeddScan = true;
    return GNCDB_INTERNAL;
  }

  /* 当且仅当全是点查询，而且条件全主键匹配时，直接略过endKeyValue的查找过程 */
  if (wholePointQuery && fieldcondList->elementCount == primaryKeyList->elementCount &&
      tableScanPhysOper->parentType == PO_PROJECT) {
    tableScanPhysOper->cursor->endPageId     = tableScanPhysOper->cursor->currentLeafPageId;
    tableScanPhysOper->cursor->endTupleIndex = tableScanPhysOper->cursor->currentTupleIndex + 1;
  } else if (hasPointQuery || (!hasPointQuery && tableScanPhysOper->parentType == PO_PROJECT)) {
    /*
      如果有点查询，无论是update、delete还是select，都可以构造endKeyValue，
      但是如果仅范围查询，只有在select的情况下才可以构造endKeyValue，因为update和delete会变b+树的结构
     */
    rc = btreeCursorEndConstruct(tableScanPhysOper->cursor, endKeyValue);
    if (rc != GNCDB_SUCCESS) {
      btreeCursorDestroy(&tableScanPhysOper->cursor);
      tableScanPhysOper->cursor = NULL;
    }
  }

  varArrayListDestroy(&conditions);
  varArrayListDestroy(&primaryKeyList);
  pkFieldMapDestroy(&pkFieldMap);
  varArrayListDestroy(&startKeyValue);
  varArrayListDestroy(&endKeyValue);
  return rc;
}

int TableScanPhysOperOpen(TableScanPhysicalOperator *tableScanPhysOper, SQLStageEvent *sqlEvent)
{
  BtreeTable *table = tableScanPhysOper->table;
  /*1.参数非空性检查*/
  if (table == NULL || sqlEvent->db == NULL) {
    return GNCDB_PARAM_INVALID;
  }

  /*2.先检查事务状态的正确性 */
  if (sqlEvent->txn == NULL || sqlEvent->txn->status == COMMITTED || sqlEvent->txn->status == ABORTED) {
    return GNCDB_TXN_STATUS_INVALID;
  }

  tableScanPhysOper->record = RecordCreateWithoutData(table->leafRecordLength);

  /*3.根据谓词条件构造b+树的游标的算子*/
  if (tableScanPhysOper->predicates && tableScanPhysOper->predicates->elementCount > 0) {
    TableScanPhysOperSetStEdCursor(tableScanPhysOper, sqlEvent->db, sqlEvent->txn);
  }

  if (!tableScanPhysOper->isNoNeddScan) {
    /*如果需要全表扫描且谓词为空（也就是需要全表扫描），那么则构造一个全表扫描的算子*/
    if (tableScanPhysOper->cursor == NULL) {
      tableScanPhysOper->cursor = btreeCursorConstruct(table->tableName, sqlEvent->db, NULL, sqlEvent->txn);
      if (tableScanPhysOper->cursor == NULL) {
        return GNCDB_INTERNAL;
      }
    }
  }

  /*4.浅拷贝初始的游标，初次创建cursor时备份，用以reset使用*/
  BTREE_CURSOR_SHALLOW_COPY(tableScanPhysOper->backupCursor, tableScanPhysOper->cursor);
  if (tableScanPhysOper->cursor && !tableScanPhysOper->backupCursor) {
    return GNCDB_MEM;
  }

  /*5.再次检查当前事务状态是否正确 */
  if (sqlEvent->txn == NULL || sqlEvent->txn->status == COMMITTED || sqlEvent->txn->status == ABORTED) {
    return GNCDB_TXN_STATUS_INVALID;
  }

  return GNCDB_SUCCESS;
}

int TableScanPhysOperNext(TableScanPhysicalOperator *tableScanPhysOper, SQLStageEvent *sqlEvent)
{
  int    rc           = GNCDB_SUCCESS;
  BYTE  *tmpTuple     = NULL;
  bool   filterResult = false;
  Record tmpRecord;
  bool   needFilter   = tableScanPhysOper->predicates != NULL && tableScanPhysOper->predicates->elementCount != 0;
  tmpRecord.allocated = false;
  tmpRecord.size      = tableScanPhysOper->table->leafRecordLength;

  if (!tableScanPhysOper->isNoNeddScan) {
    /*1.使用open阶段定义的游标来获取record，每次只返回一个满足条件的元组*/
    while (btreeTableHasNextTuple(tableScanPhysOper->cursor)) {
      tmpTuple = btreeTableGetNextRecord(tableScanPhysOper->table, tableScanPhysOper->cursor, sqlEvent->db);
      if (tmpTuple == NULL) {
        return GNCDB_TUPLE_NOT_FOUND;
      }
      filterResult   = false;
      tmpRecord.data = tmpTuple;
      /*2.满足谓词才返回不满足则获取下一个*/
      if (needFilter) {
        rc = TableScanPhysOperFilter(tableScanPhysOper, &tmpRecord, &filterResult);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        if (!filterResult) {
          continue;
        }
      }
      /*3.把得到的元组放到表扫描算子的record字段*/
      RecordFillData(tableScanPhysOper->record, tmpTuple);
      // tableScanPhysOper->record = RecordCreate(tmpTuple, tableScanPhysOper->table->leafRecordLength);

      return GNCDB_SUCCESS;
    }
  }
  return GNCDB_NEXT_EOF;
}

int TableScanPhysOperClose(TableScanPhysicalOperator *tableScanPhysOper, SQLStageEvent *sqlEvent)
{
  if (tableScanPhysOper == NULL) {
    return GNCDB_PARAM_INVALID;
  }
  btreeCursorDestroy(&tableScanPhysOper->cursor);
  if (tableScanPhysOper->backupCursor != NULL) {
    my_free(tableScanPhysOper->backupCursor);
    tableScanPhysOper->backupCursor = NULL;
  }
  if (tableScanPhysOper->record != NULL) {
    RecordDestroy(&tableScanPhysOper->record);
    tableScanPhysOper->record = NULL;
  }
  return GNCDB_SUCCESS;
}

void TableScanPhysOperDestroy(TableScanPhysicalOperator *tableScanPhysOper)
{
  if (tableScanPhysOper == NULL) {
    return;
  }
  if (tableScanPhysOper->children != NULL) {
    varArrayListDestroy(&tableScanPhysOper->children);
  }
  // if (tableScanPhysOper->cols != NULL) {
  //   varArrayListDestroy(&tableScanPhysOper->cols);
  // }
  if (tableScanPhysOper->startKeyValue != NULL) {
    varArrayListDestroy(&tableScanPhysOper->startKeyValue);
  }
  if (tableScanPhysOper->cursor != NULL) {
    btreeCursorDestroy(&tableScanPhysOper->cursor);
  }
  if (tableScanPhysOper->predicates != NULL) {
    EXPRLIST_DESTROY(tableScanPhysOper->predicates);
  }
  if (tableScanPhysOper->complexCols != NULL) {
    varArrayListDestroy(&tableScanPhysOper->complexCols);
  }
  my_free(tableScanPhysOper);
}

// void setRecord(TableScanPhysicalOperator *tableScanPhysOper, BYTE *tuple)
// {
//   if (tableScanPhysOper == NULL || tuple == NULL) {
//     return;
//   }
//   tableScanPhysOper->record = RecordCreate(tuple, tableScanPhysOper->table->leafRecordLength);
// }

int TableScanPhysOperFilter(TableScanPhysicalOperator *tableScanPhysOper, Record *tuple, bool *result)
{
  int         rc = GNCDB_SUCCESS;
  int         i  = 0;
  Value       value;
  Expression *expr = NULL;
  // bool          tmpResult    = false;
  Record       *complexTuple = NULL;
  Record       *realTuple    = NULL;
  varArrayList *realCols     = NULL;

  if (tableScanPhysOper == NULL || tuple == NULL || result == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (tableScanPhysOper->predicates == NULL) {
    *result = true;
    return rc;
  }

  realTuple = tuple;
  realCols  = tableScanPhysOper->cols;
  if (tableScanPhysOper->parentTuple != NULL) {
    complexTuple = RecordMerge(tableScanPhysOper->parentTuple, tuple);
    if (complexTuple == NULL) {
      return GNCDB_INTERNAL;
    }
    if (tableScanPhysOper->complexCols == NULL) {
      tableScanPhysOper->complexCols = colMetaListMerge(tableScanPhysOper->parentCols, tableScanPhysOper->cols);
      if (tableScanPhysOper->complexCols == NULL) {
        RecordDestroy(&complexTuple);
        return GNCDB_INTERNAL;
      }
    }
    realTuple = complexTuple;
    realCols  = tableScanPhysOper->complexCols;
  }
  for (i = 0; i < tableScanPhysOper->predicates->elementCount; i++) {
    expr = (Expression *)varArrayListGetPointer1(tableScanPhysOper->predicates, i);
    rc   = expressionGetValueNCP(expr, realTuple, realCols, &value);
    if (rc != GNCDB_SUCCESS) {
      *result = false;
      RecordDestroy(&complexTuple);
      return rc;
    }
    // tmpResult = valueGetBoolean(&value);
    if (!value.numValue.boolValue) {
      *result = false;
      RecordDestroy(&complexTuple);
      return rc;
    }
  }
  *result = true;
  RecordDestroy(&complexTuple);
  return rc;
}

Record *TableScanPhysOperGetCurrentTuple(TableScanPhysicalOperator *tableScanPhysOper)
{
  return tableScanPhysOper->record;
}

struct BtreePage *TableScanPhysOperGetCurrentPage(TableScanPhysicalOperator *tableScanPhysOper)
{
  return tableScanPhysOper->cursor->page;
}

BYTE *TableScanPhysOperGetCurrentRecord(TableScanPhysicalOperator *tableScanPhysOper)
{
  return tableScanPhysOper->cursor->page->page.pData + PAGE_HEAD_SIZE +
         (tableScanPhysOper->cursor->currentTupleIndex - 1) * tableScanPhysOper->table->leafRecordLength;
}

int TableScanPhysOperGetCurrentRecordIndex(TableScanPhysicalOperator *tableScanPhysOper)
{
  return tableScanPhysOper->cursor->currentTupleIndex - 1;
}

BtreeCursor *TableScanPhysOperGetScanCursor(TableScanPhysicalOperator *tableScanPhysOper)
{
  return tableScanPhysOper->cursor;
}

void TableScanPhysOperPointerDestroy(void *data)
{
  TableScanPhysicalOperator **tableScanPhysOper = (TableScanPhysicalOperator **)data;
  if (tableScanPhysOper == NULL || *tableScanPhysOper == NULL) {
    return;
  }

  TableScanPhysOperDestroy(*tableScanPhysOper);
  *tableScanPhysOper = NULL;
}

/**
 * @description: 将当前cursor恢复到备份的cursor
 * @param {TableScanPhysicalOperator} *tableScanPhysOper
 * @return {*}
 */
int TableScanPhysOperReset(TableScanPhysicalOperator *tableScanPhysOper)
{
  int rc = GNCDB_SUCCESS;
  if (tableScanPhysOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (tableScanPhysOper->backupCursor == NULL) {
    return GNCDB_INTERNAL;
  }
  rc = btreeCursorReset(tableScanPhysOper->cursor, tableScanPhysOper->backupCursor, tableScanPhysOper->cursor->db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}