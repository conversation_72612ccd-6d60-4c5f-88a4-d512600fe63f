#include "join_physical_operator.h"
#include "exec_tuple.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "physical_operator.h"
#include "sql_event.h"
#include "table_scan_physical_operator.h"
#include "typedefine.h"
#include "utils.h"
#include "value.h"
#include "vararraylist.h"
#include <assert.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include <time.h>

/**
 * @description: eqRes是相等比较的值，根据op类型将result设置真实值
 * @param op CompOp枚举值
 * @param eqRes 相等时的比较结果：负数表示左 < 右，0表示相等，正数表示左 > 右
 * @param result 经过op转换后的结果值
 * @return {*}
 */
#define SET_RESULT(op, eqRes, result)                         \
  do {                                                        \
    switch (op) {                                             \
      case CMPOP_EQUAL_TO: result = ((eqRes) == 0); break;    \
      case CMPOP_NOT_EQUAL: result = ((eqRes) != 0); break;   \
      case CMPOP_GREAT_THAN: result = ((eqRes) > 0); break;   \
      case CMPOP_GREAT_EQUAL: result = ((eqRes) >= 0); break; \
      case CMPOP_LESS_THAN: result = ((eqRes) < 0); break;    \
      case CMPOP_LESS_EQUAL: result = ((eqRes) <= 0); break;  \
      default: result = false; break;                         \
    }                                                         \
  } while (0)

/**
 * @description: 将连接条件中的左表和右表的连接字段ColMeta添加到对应的ColMetaList中
 * @param oper 连接算子
 * @param leftFields 左表连接字段ColMetaList
 * @param rightFields 右表连接字段ColMetaList
 * @param cmpExpr 连接条件，必须确保是ComparisonExpr类型
 * @return {*}
 */
#define FIND_JOIN_KEYS(oper, leftFields, rightFields, cmpExpr)                                          \
  do {                                                                                                  \
    for (int i = 0; i < (oper)->leftColMeta->elementCount; i++) {                                       \
      if (strcmp(((ColMeta *)varArrayListGetPointer((oper)->leftColMeta, i))->tableName,                \
              ((FieldExpr *)(cmpExpr)->left)->tableName) == 0 &&                                        \
          strcmp(((ColMeta *)varArrayListGetPointer((oper)->leftColMeta, i))->name,                     \
              ((FieldExpr *)(cmpExpr)->left)->fieldName) == 0) {                                        \
        varArrayListAddPointer((oper)->leftFields, varArrayListGetPointer((oper)->leftColMeta, i));     \
        break;                                                                                          \
      }                                                                                                 \
      if (strcmp(((ColMeta *)varArrayListGetPointer((oper)->leftColMeta, i))->tableName,                \
              ((FieldExpr *)(cmpExpr)->right)->tableName) == 0 &&                                       \
          strcmp(((ColMeta *)varArrayListGetPointer((oper)->leftColMeta, i))->name,                     \
              ((FieldExpr *)(cmpExpr)->right)->fieldName) == 0) {                                       \
        varArrayListAddPointer((oper)->leftJoinFields, varArrayListGetPointer((oper)->leftColMeta, i)); \
        break;                                                                                          \
      }                                                                                                 \
    }                                                                                                   \
    for (int i = 0; i < (oper)->rightColMeta->elementCount; i++) {                                      \
      if (strcmp(((ColMeta *)varArrayListGetPointer((oper)->rightColMeta, i))->tableName,               \
              ((FieldExpr *)(cmpExpr)->right)->tableName) == 0 &&                                       \
          strcmp(((ColMeta *)varArrayListGetPointer((oper)->rightColMeta, i))->name,                    \
              ((FieldExpr *)(cmpExpr)->right)->fieldName) == 0) {                                       \
        varArrayListAddPointer((oper)->rightFields, varArrayListGetPointer((oper)->rightColMeta, i));   \
        break;                                                                                          \
      }                                                                                                 \
      if (strcmp(((ColMeta *)varArrayListGetPointer((oper)->rightColMeta, i))->tableName,               \
              ((FieldExpr *)(cmpExpr)->left)->tableName) == 0 &&                                        \
          strcmp(((ColMeta *)varArrayListGetPointer((oper)->rightColMeta, i))->name,                    \
              ((FieldExpr *)(cmpExpr)->left)->fieldName) == 0) {                                        \
        varArrayListAddPointer((oper)->rightFields, varArrayListGetPointer((oper)->rightColMeta, i));   \
        break;                                                                                          \
      }                                                                                                 \
    }                                                                                                   \
  } while (0)

/**
 * @description: 填充左表的逻辑块
 * @param {NestedLoopJoinPhysicalOperator} *nestedLoopJoinPhysoper
 * @param {SQLStageEvent} *sqlEvent
 * @return {*}
 */
int fillLeftBlk(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent)
{
  int     rc        = GNCDB_SUCCESS;
  Record *record    = NULL;
  bnljoper->leftIdx = 0;
  while (bnljoper->leftIdx < bnljoper->blkCap) {
    rc = PhysicalOperatorNext(bnljoper->left, sqlEvent);
    //* 左表已经遍历完或出现异常，退出 */
    if (rc != GNCDB_SUCCESS) {
      break;
    }
    //* 先销毁上一块中的元组再设置新的元组 */
    record = (Record *)varArrayListGetPointer(bnljoper->leftBlock, bnljoper->leftIdx);
    RecordDestroy(&record);
    record = GetCurrentTuple(bnljoper->left);
    varArrayListSetByIndexPointer(bnljoper->leftBlock, bnljoper->leftIdx, RecordDeepCopy(record));
    bnljoper->leftIdx++;
  }
  bnljoper->leftTnum = bnljoper->leftIdx;
  if (!bnljoper->leftTnum) {
    return GNCDB_NEXT_EOF;
  } else {
    bnljoper->leftIdx = 0;
    return GNCDB_SUCCESS;
  }
}

int JoinSetColMeta(PhysicalOperator *joinPhyoper)
{
  PhysicalOperator *leftOper     = NULL;
  PhysicalOperator *rightOper    = NULL;
  varArrayList     *leftColMeta  = NULL;
  varArrayList     *rightColMeta = NULL;
  
  /*1.连接的算子数必须为2*/
  assert(joinPhyoper->children->elementCount == 2);
  
  /*2.分别获取左右孩子的列元信息*/
  leftOper  = (PhysicalOperator *)varArrayListGetPointer(joinPhyoper->children, 0);
  rightOper = (PhysicalOperator *)varArrayListGetPointer(joinPhyoper->children, 1);
  if (leftOper == NULL || rightOper == NULL)
    return GNCDB_MEM;
  leftColMeta  = getColMeta(leftOper);
  rightColMeta = getColMeta(rightOper);
  if (leftColMeta == NULL || rightColMeta == NULL)
    return GNCDB_MEM;

  /*3.合并结果阿列元信息是左右算子的列元信息合并的结果*/
  joinPhyoper->cols = colMetaListMerge(leftColMeta, rightColMeta);
  return GNCDB_SUCCESS;
}

/**
 * @description: 将连接的expr转换为colMeta
 * @param {HashJoinPhysicalOperator} *oper
 * @return {*}
 */
int NLJFindJoinKeys(NestedLoopJoinPhysicalOperator *oper, Expression *expr)
{
  int             rc      = GNCDB_SUCCESS;
  ComparisonExpr *cmpExpr = NULL;
  /*1.and连接的多个条件，递归处理*/
  if (expr->type == ETG_CONJUNCTION) {
    ConjunctionExpr *conjExpr = (ConjunctionExpr *)expr;
    for (int i = 0; i < conjExpr->children->elementCount; i++) {
      rc = NLJFindJoinKeys(oper, (Expression *)varArrayListGetPointer(conjExpr->children, i));
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  } else if (expr->type == ETG_COMPARISON) {
    /*2.必须确保左右表的连接字段都是field类型，且左字段对应左表，右字段对应右表*/
    cmpExpr = (ComparisonExpr *)expr;
    if (cmpExpr->left->type == ETG_FIELD && cmpExpr->right->type == ETG_FIELD) {
      /*添加连接操作符*/
      varArrayListAdd(oper->joinOps, &(cmpExpr->comp));
      /*这个宏实际上是把cmpExpr中的列对应的colmeta放到了oper对应的ColMetaList中了*/
      FIND_JOIN_KEYS(oper, leftJoinFields, rightJoinFields, cmpExpr);
    }
  }
  if (oper->leftJoinFields->elementCount == 0 || oper->rightJoinFields->elementCount == 0) {
    return GNCDB_JOIN_KEY_NOT_FOUND;
  }
  if (oper->leftJoinFields->elementCount != oper->rightJoinFields->elementCount) {
    return GNCDB_JOIN_KEY_NOT_FOUND;
  }
  return GNCDB_SUCCESS;
}

int NestedLoopJoinPhysOperInit(NestedLoopJoinPhysicalOperator *bnljoper, PhysicalOperatorType type)
{
  bnljoper->type        = type;
  bnljoper->children    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  bnljoper->parentTuple = NULL;

  bnljoper->left                    = NULL;
  bnljoper->right                   = NULL;
  bnljoper->leftJoinFields          = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  bnljoper->rightJoinFields         = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  bnljoper->joinOps                 = varArrayListCreate(DISORDER, INT_SIZE, 0, NULL, NULL);
  bnljoper->leftRecord              = NULL;
  bnljoper->rightRecord             = NULL;
  bnljoper->predicates              = NULL;
  bnljoper->leftIdx                 = 0;
  bnljoper->leftTnum                = 0;
  bnljoper->blkCap                  = JOIN_BLOCK_TNUM;
  bnljoper->leftBlock               = varArrayListCreate(DISORDER, BYTES_POINTER, bnljoper->blkCap, NULL, NULL);
  bnljoper->leftBlock->elementCount = bnljoper->blkCap;
  return 0;
}

int NestedLoopJoinPhysOperOpen(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent)
{
  int      rc          = GNCDB_SUCCESS;
  ColMeta *lastColMeta = NULL;
  
  /*1.子算子必须为2*/
  if (bnljoper->children->elementCount != 2) {
    return GNCDB_INTERNAL;
  }
  bnljoper->left         = (PhysicalOperator *)varArrayListGetPointer(bnljoper->children, 0);
  bnljoper->right        = (PhysicalOperator *)varArrayListGetPointer(bnljoper->children, 1);
  bnljoper->leftColMeta  = getColMeta(bnljoper->left);
  bnljoper->rightColMeta = getColMeta(bnljoper->right);
  bnljoper->leftRecord   = NULL;
  bnljoper->rightRecord  = NULL;
  lastColMeta            = (ColMeta *)varArrayListGetPointer(bnljoper->cols, bnljoper->cols->elementCount - 1);
  bnljoper->resRecord    = RecordCreateWithoutData(lastColMeta->offset + lastColMeta->len);
  
  /*1.左右算子open*/
  rc = PhysicalOperatorOpen(bnljoper->left, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = PhysicalOperatorOpen(bnljoper->right, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /*2.初始化*/
  bnljoper->roundDone = false;
  bnljoper->trx       = sqlEvent->txn;

  if (bnljoper->predicates == NULL || bnljoper->predicates->elementCount == 0) {
    return GNCDB_SUCCESS;
  }

  /*3.若有连接谓词则收集连接字段*/
  for (int i = 0; i < bnljoper->predicates->elementCount; i++) {                                    // 收集连接字段
    rc = NLJFindJoinKeys(bnljoper, (Expression *)varArrayListGetPointer(bnljoper->predicates, i));  // 收集连接字段
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  return rc;
}

int NestedLoopJoinPhysOperNextCurrent(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;

  //* 填充第一个左块 */
  if (!bnljoper->leftRecord) {
    rc = fillLeftBlk(bnljoper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  //* 获取第一个右元组 */
  if (!bnljoper->rightRecord) {
    rc = NestedLoopJoinPhysOperRightNext(bnljoper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  //* 左表数量为空，结束 */
  if (!bnljoper->leftTnum) {
    return GNCDB_NEXT_EOF;
  }
  //* 当前右元组连接完左块所有元组，重新获取下一个右元组 */
  if (bnljoper->leftIdx >= bnljoper->leftTnum) {
    //* 获取下一个右元组 */
    rc = NestedLoopJoinPhysOperRightNext(bnljoper, sqlEvent);
    //* 重置左表块的下标索引，从头遍历 */
    if (rc == GNCDB_SUCCESS) {
      bnljoper->leftIdx = 0;
    }
    //* 右元组遍历完，重新获取下一个左块 */
    if (rc == GNCDB_NEXT_EOF) {
      rc = fillLeftBlk(bnljoper, sqlEvent);
      //* 左块为空，结束 */
      if (rc == GNCDB_NEXT_EOF) {
        return GNCDB_NEXT_EOF;
      }
      //* 左块不为空，重新打开右算子从头获取右元组 */
      rc = NestedLoopJoinPhysOperRightNext(bnljoper, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  }

  bnljoper->leftRecord = (Record *)varArrayListGetPointer(bnljoper->leftBlock, bnljoper->leftIdx);
  bnljoper->leftIdx++;
  return rc;
}

int NestedLoopJoinPhysOperNext(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;

  do {
    /*获取一组连接后的元组，若不满足连接条件则返回下一组否则直接返回*/
    rc = NestedLoopJoinPhysOperNextCurrent(bnljoper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } while (!NestedLoopJoinPhysOperFilter(bnljoper));
  /*正常返回的情况下，左右表的记录应该都不为空*/
  assert(bnljoper->leftRecord != NULL && bnljoper->rightRecord != NULL);
  return rc;
}

int NestedLoopJoinPhysOperClose(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  rc     = PhysicalOperatorClose(bnljoper->left, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = PhysicalOperatorClose(bnljoper->right, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  if (bnljoper->leftJoinFields) {
    varArrayListClear(bnljoper->leftJoinFields);
  }
  if (bnljoper->rightJoinFields) {
    varArrayListClear(bnljoper->rightJoinFields);
  }
  if (bnljoper->resRecord != NULL) {
    RecordDestroy(&bnljoper->resRecord);
  }
  if (bnljoper->joinOps) {
    varArrayListClear(bnljoper->joinOps);
  }
  return rc;
}

/**
 * @description: 重置连接算子，必须在open之后使用，左右算子都需要重置，同时销毁左表块和右元组
 * @param {NestedLoopJoinPhysicalOperator} *bnljoper
 * @return {*}
 */
int NestedLoopJoinPhysOperReset(NestedLoopJoinPhysicalOperator *bnljoper)
{
  int rc = GNCDB_SUCCESS;

  rc = PhysicalOperatorReset(bnljoper->left);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = PhysicalOperatorReset(bnljoper->right);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  bnljoper->leftRecord = NULL;

  return GNCDB_SUCCESS;
}

int NestedLoopJoinPhysOperRightNext(NestedLoopJoinPhysicalOperator *bnljoper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  if (bnljoper->roundDone) {
    // 如果右表已经读取完毕，需要reset右算子
    PhysicalOperatorReset(bnljoper->right);
    bnljoper->roundDone = false;
  }

  rc = PhysicalOperatorNext(bnljoper->right, sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    if (rc == GNCDB_NEXT_EOF) {
      bnljoper->roundDone = true;
    }
    return rc;
  }
  bnljoper->rightRecord = GetCurrentTuple(bnljoper->right);
  return rc;
}

Record *NestedLoopJoinPhysOperGetCurrentTuple(NestedLoopJoinPhysicalOperator *oper)
{
  RecordSetData(oper->resRecord, 0, oper->leftRecord->data, oper->leftRecord->size);
  RecordSetData(oper->resRecord, oper->leftRecord->size, oper->rightRecord->data, oper->rightRecord->size);
  return oper->resRecord;
}

/**
 * @description: 假设predicates中都是and连接的field op field的形式
 * @param {NestedLoopJoinPhysicalOperator} *oper
 * @return {*}
 */
bool NestedLoopJoinPhysOperFilter(NestedLoopJoinPhysicalOperator *oper)
{
  bool   result = false;
  int    eqRes  = 0;  // 最开始按照计算的结果：负数表示左 < 右，0表示相等，正数表示左 > 右
  double eqResD = 0;
  // BYTE    *bitmapL     = NULL;
  // BYTE    *bitmapR     = NULL;
  ColMeta *leftColMeta = NULL, *rightColMeta = NULL;
  if (oper->predicates == NULL || oper->predicates->elementCount == 0) {
    return true;
  }

  for (int i = 0; i < oper->joinOps->elementCount; i++) {
    leftColMeta  = (ColMeta *)varArrayListGetPointer(oper->leftJoinFields, i);
    rightColMeta = (ColMeta *)varArrayListGetPointer(oper->rightJoinFields, i);
    assert(leftColMeta->fieldType == rightColMeta->fieldType);  // 连接字段的类型必须相同

    // bitmapL = (BYTE *)oper->leftRecord->data + leftColMeta->bitmapOffset;
    // bitmapR = (BYTE *)oper->rightRecord->data + rightColMeta->bitmapOffset;

    // if (leafTupleGetBitMap(bitmapL, leftColMeta->index, 0) == 0 ||
    //     leafTupleGetBitMap(bitmapR, rightColMeta->index, 0) == 0) {
    //   return false;
    // }
    switch (leftColMeta->fieldType) {  // 根据字段类型进行比较
      case FIELDTYPE_INTEGER:
      case FIELDTYPE_DATE:
        eqRes = *(int *)(oper->leftRecord->data + leftColMeta->offset) -
                *(int *)(oper->rightRecord->data + rightColMeta->offset);
        SET_RESULT(*(CompOp *)varArrayListGet(oper->joinOps, i), eqRes, result);
        break;
      case FIELDTYPE_REAL:
        eqResD = *(double *)(oper->leftRecord->data + leftColMeta->offset) -
                 *(double *)(oper->rightRecord->data + rightColMeta->offset);
        SET_RESULT(*(CompOp *)varArrayListGet(oper->joinOps, i), eqResD, result);
        break;
      case FIELDTYPE_VARCHAR:
        eqRes = strcmp((char *)(oper->leftRecord->data + leftColMeta->offset),
            (char *)(oper->rightRecord->data + rightColMeta->offset));
        if (eqRes == 0) {
          eqRes = 0;
        }
        SET_RESULT(*(CompOp *)varArrayListGet(oper->joinOps, i), eqRes, result);
        break;
      default: break;
    }

    if (!result) {  // 逻辑短路：只要有一个不满足，就返回false
      return false;
    }
  }
  return true;
}

void NestedLoopJoinPhysOperDestroy(NestedLoopJoinPhysicalOperator *bnljoper)
{
  Record *record = NULL;
  if (bnljoper == NULL) {
    return;
  }
  if (bnljoper->children != NULL) {
    varArrayListDestroy(&bnljoper->children);
  }
  if (bnljoper->cols != NULL) {
    varArrayListDestroy(&bnljoper->cols);
  }

  if (bnljoper->leftJoinFields != NULL) {
    varArrayListDestroy(&bnljoper->leftJoinFields);
  }
  if (bnljoper->rightJoinFields != NULL) {
    varArrayListDestroy(&bnljoper->rightJoinFields);
  }
  if (bnljoper->joinOps != NULL) {
    varArrayListDestroy(&bnljoper->joinOps);
  }
  if (bnljoper->predicates != NULL) {
    EXPRLIST_DESTROY(bnljoper->predicates);
  }
  for (int i = 0; i < bnljoper->blkCap; i++) {
    record = (Record *)varArrayListGetPointer(bnljoper->leftBlock, i);
    RecordDestroy(&record);
  }
  varArrayListDestroy(&bnljoper->leftBlock);

  my_free(bnljoper);
}

void NestedLoopJoinPhysOperPointerDestroy(void *data)
{
  NestedLoopJoinPhysicalOperator **bnljoper = NULL;
  bnljoper                                  = (NestedLoopJoinPhysicalOperator **)data;
  if (bnljoper == NULL || *bnljoper == NULL) {
    return;
  }
  NestedLoopJoinPhysOperDestroy(*bnljoper);
  *bnljoper = NULL;
}

/************************************************************
 * HashJoinPhysicalOperator
 ************************************************************/

/**
 * @description: 从哈希表中获取key对应的整个桶
 * @param {HashMap} *hashMap
 * @param {void} *key 类型应当是CombinedHashKey*结构体指针
 * @return {*}
 */
Entry *HashmapGetBucket(HashMap *hashMap, void *key)
{
  Entry *entry = NULL;
  int    index = hashMap->hashCode(hashMap, key);
  entry        = &hashMap->bucketList[index];
  if (entry->key == NULL) {
    return NULL;
  }
  return entry;
};

/**
 * @description: 将连接的expr转换为colMeta
 * @param {HashJoinPhysicalOperator} *oper
 * @return {*}
 */
int HJFindJoinKeys(HashJoinPhysicalOperator *oper, Expression *expr)
{
  int             rc      = GNCDB_SUCCESS;
  ComparisonExpr *cmpExpr = NULL;
  if (expr->type == ETG_CONJUNCTION) {  // and连接的多个条件，递归处理
    ConjunctionExpr *conjExpr = (ConjunctionExpr *)expr;
    for (int i = 0; i < conjExpr->children->elementCount; i++) {
      rc = HJFindJoinKeys(oper, (Expression *)varArrayListGetPointer(conjExpr->children, i));
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  } else if (expr->type == ETG_COMPARISON) {  // 必须确保左右表的连接字段都是field类型，且左字段对应左表，右字段对应右表
    cmpExpr = (ComparisonExpr *)expr;
    if (cmpExpr->comp != CMPOP_EQUAL_TO) {  // 只支持等值连接，前面阶段处理过，这里应该都是等于条件
      return GNCDB_JOIN_KEY_NOT_FOUND;
    } else {
      if (cmpExpr->left->type == ETG_FIELD && cmpExpr->right->type == ETG_FIELD) {  // 把FieldExpr转换为ColMeta
        FIND_JOIN_KEYS(oper, leftJoinFields, rightJoinFields, cmpExpr);
      } else {  // 按理来说经过谓词下推，此处的连接条件都是field op field形式，不会出现这其他情况
        return GNCDB_JOIN_KEY_NOT_FOUND;
      }
    }
  }
  if (oper->leftJoinFields->elementCount == 0 || oper->rightJoinFields->elementCount == 0) {
    return GNCDB_JOIN_KEY_NOT_FOUND;
  }
  if (oper->leftJoinFields->elementCount != oper->rightJoinFields->elementCount) {
    return GNCDB_JOIN_KEY_NOT_FOUND;
  }
  return GNCDB_SUCCESS;
}

/**
 * @description: 用以获取哈希连接中组合键的值
 * @param {varArrayList} *joinFields 连接字段,类型：ColMeta*
 * @param {Record} *record 记录
 * @param {char} *combinedKey 用于存储组合键的缓冲区
 * @param {int*} bufSize combinedKey的大小
 * @return {*}
 */
int CombineJoinKey(varArrayList *joinFields, Record *record, CombinedHashKey *combinedKey)
{
  int      strLen  = 0;
  ColMeta *colMeta = NULL;
  int      offset  = 4;     // 当前缓冲区偏移量，头部四个字节存储payLoadLen
  char    *newBuf  = NULL;  // 重新分配的新缓冲区

  if (combinedKey->data == NULL) {
    combinedKey->data = (char *)my_malloc(combinedKey->bufSize);
    if (combinedKey->data == NULL) {
      return GNCDB_MEM;
    }
  }
  memset(combinedKey->data, 0, combinedKey->payloadLen);

  // 一次遍历构建组合键
  for (int i = 0; i < joinFields->elementCount; i++) {
    colMeta = (ColMeta *)varArrayListGetPointer(joinFields, i);
    if (colMeta == NULL) {
      return GNCDB_INTERNAL;
    }

    // 检查字符串缓冲区大小是否足够
    while (offset + colMeta->len + 1 > combinedKey->bufSize) {
      combinedKey->bufSize *= 2;
      newBuf = (char *)my_realloc(combinedKey->data, combinedKey->bufSize);
      if (newBuf == NULL) {
        my_free(combinedKey->data);
        combinedKey->data = NULL;
        return GNCDB_MEM;
      }
      memset(newBuf + offset, 0, combinedKey->bufSize - offset);
      combinedKey->data = newBuf;
    }

    // 根据字段类型处理
    switch (colMeta->fieldType) {
      case FIELDTYPE_INTEGER:
      case FIELDTYPE_DATE:
      case FIELDTYPE_REAL:
        memcpy(combinedKey->data + offset, record->data + colMeta->offset, colMeta->len);
        offset += colMeta->len;
        break;
      case FIELDTYPE_VARCHAR: {
        strLen = strlen((char *)(record->data + colMeta->offset));
        memcpy(combinedKey->data + offset, record->data + colMeta->offset, strLen);
        offset += strLen;
        break;
      }
      default: break;
    }
  }
  // 首部加入payloadLen()
  combinedKey->payloadLen = offset;
  memcpy(combinedKey->data, &(combinedKey->payloadLen), 4);
  // 末尾加\0
  (combinedKey->data)[offset] = '\0';
  return GNCDB_SUCCESS;
}

int HashJoinPhysOperInit(HashJoinPhysicalOperator *hjoper, PhysicalOperatorType type)
{
  hjoper->type                = type;
  hjoper->children            = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  hjoper->parentTuple         = NULL;
  hjoper->leftJoinFields      = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  hjoper->rightJoinFields     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  hjoper->filterOps           = varArrayListCreate(DISORDER, INT_SIZE, 0, NULL, NULL);
  hjoper->filterLeftMeta      = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  hjoper->filterRightMeta     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  hjoper->trx                 = NULL;
  hjoper->hashTable           = NULL;
  hjoper->eqPredicates        = NULL;
  hjoper->combinedKey.bufSize = 256;
  hjoper->combinedKey.data    = (char *)my_malloc0(hjoper->combinedKey.bufSize);
  return 0;
}

int HashJoinPhysOperOpen(HashJoinPhysicalOperator *hjoper, SQLStageEvent *sqlEvent)
{
  int               rc          = GNCDB_SUCCESS;
  int               i           = 0;
  PhysicalOperator *child       = NULL;
  PhysicalOperator *left        = NULL;
  ComparisonExpr   *compExpr    = NULL;
  Record           *record      = NULL;
  ColMeta          *lastColMeta = NULL;

  /*1.哈希连接有且只有两个子算子*/
  if (hjoper->children->elementCount != 2) {
    return GNCDB_INTERNAL;
  }
  /*2.open两个子算子*/
  for (i = 0; i < hjoper->children->elementCount; i++) {
    child = (PhysicalOperator *)varArrayListGetPointer(hjoper->children, i);
    rc    = PhysicalOperatorOpen(child, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  /*3.获取两个子算子的列元数据*/
  hjoper->leftColMeta  = getColMeta((PhysicalOperator *)varArrayListGetPointer(hjoper->children, 0));
  hjoper->rightColMeta = getColMeta((PhysicalOperator *)varArrayListGetPointer(hjoper->children, 1));
  lastColMeta          = (ColMeta *)varArrayListGetPointer(hjoper->cols, hjoper->cols->elementCount - 1);
  hjoper->resRecord    = RecordCreateWithoutData(lastColMeta->offset + lastColMeta->len);

  left = (PhysicalOperator *)varArrayListGetPointer(hjoper->children, 0);

  /*4.创建一个用来存储驱动表哈希值的哈希表*/
  hjoper->hashTable = hashMapCreate(STRKEY, 0, CombinedKeyHashCode);

  /*5.收集连接字段*/
  for (int i = 0; i < hjoper->eqPredicates->elementCount; i++) {
    rc = HJFindJoinKeys(hjoper, (Expression *)varArrayListGetPointer(hjoper->eqPredicates, i));
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /*6.收集过滤条件，因为哈希连接通过等值条件连接，因此这里只收集非等值条件*/
  if (hjoper->otherPredicates != NULL) {
    for (int i = 0; i < hjoper->otherPredicates->elementCount; i++) {
      compExpr = (ComparisonExpr *)varArrayListGetPointer(hjoper->otherPredicates, i);
      varArrayListAdd(hjoper->filterOps, &(compExpr->comp));
      FIND_JOIN_KEYS(hjoper, filterLeftMeta, filterRightMeta, compExpr);
    }
  }

  /*7.left表是驱动表，这一步是遍历左表的每一行，用连接条件的等值条件计算哈希值放到哈希表中*/
  while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext(left, sqlEvent))) {
    /*7.1取出left返回的元组*/
    record = GetCurrentTuple(left);
    if (record == NULL) {
      return GNCDB_TUPLE_NOT_FOUND;
    }
    /*7.2计算该元组的连接条件的哈希值*/
    rc = CombineJoinKey(hjoper->leftJoinFields, record, &(hjoper->combinedKey));
    if (rc != GNCDB_SUCCESS) {
      my_free(hjoper->combinedKey.data);
      hjoper->combinedKey.data = NULL;
      return rc;
    }
    /*7.3将哈希值放入哈希表*/
    CombinedKeyHashMapPut(hjoper->hashTable, &(hjoper->combinedKey), RecordDeepCopy(record));
  }

  if (rc != GNCDB_NEXT_EOF) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

bool HashJoinPhysOperFilter(HashJoinPhysicalOperator *oper)
{
  bool     result      = false;
  int      eqRes       = 0;  // 最开始按照计算的结果：负数表示左 < 右，0表示相等，正数表示左 > 右
  double   eqResD      = 0;
  ColMeta *leftColMeta = NULL, *rightColMeta = NULL;
  if (oper->filterOps == NULL || oper->filterOps->elementCount == 0) {
    return true;
  }

  for (int i = 0; i < oper->filterOps->elementCount; i++) {
    leftColMeta  = (ColMeta *)varArrayListGetPointer(oper->filterLeftMeta, i);
    rightColMeta = (ColMeta *)varArrayListGetPointer(oper->filterRightMeta, i);
    assert(leftColMeta->fieldType == rightColMeta->fieldType);  // 连接字段的类型必须相同
    switch (leftColMeta->fieldType) {                           // 根据字段类型进行比较
      case FIELDTYPE_INTEGER:
      case FIELDTYPE_DATE:
        eqRes = *(int *)(oper->leftRecord->data + leftColMeta->offset) -
                *(int *)(oper->rightRecord->data + rightColMeta->offset);
        SET_RESULT(*(CompOp *)varArrayListGet(oper->filterOps, i), eqRes, result);
        break;
      case FIELDTYPE_REAL:
        eqResD = *(double *)(oper->leftRecord->data + leftColMeta->offset) -
                 *(double *)(oper->rightRecord->data + rightColMeta->offset);
        if (eqResD < 0) {
          eqResD = -1.0;
        }
        SET_RESULT(*(CompOp *)varArrayListGet(oper->filterOps, i), eqResD, result);
        break;
      case FIELDTYPE_VARCHAR:
        eqRes = strcmp((char *)(oper->leftRecord->data + leftColMeta->offset),
            (char *)(oper->rightRecord->data + rightColMeta->offset));
        if (eqRes == 0) {
          eqRes = 0;
        }
        SET_RESULT(*(CompOp *)varArrayListGet(oper->filterOps, i), eqRes, result);
        break;
      default: break;
    }

    if (!result) {  // 逻辑短路：只要有一个不满足，就返回false
      return false;
    }
  }
  return true;
}

int HashJoinPhysOperNextCurrent(HashJoinPhysicalOperator *hjoper, SQLStageEvent *sqlEvent)
{
  int               rc      = GNCDB_SUCCESS;
  PhysicalOperator *right   = NULL;
  bool              matched = false;

  if (hjoper == NULL || sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }

  right = (PhysicalOperator *)varArrayListGetPointer(hjoper->children, 1);

  //* 由于可能存在哈希冲突，需要遍历左表桶检查左右记录是否符合条件 */
  while (1) {
    //* 获取左表桶的迭代器 */
    while (!hjoper->leftBucktIter) {
      if (hjoper->rightRecord) {
        hjoper->rightRecord = NULL;
        memset(hjoper->combinedKey.data, 0, hjoper->combinedKey.payloadLen);
      }
      //* 获取第一个右元组 */
      if (!hjoper->rightRecord) {
        rc = PhysicalOperatorNext(right, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        hjoper->rightRecord = GetCurrentTuple(right);
        if (!hjoper->combinedKey.data) {
          hjoper->combinedKey.data = my_malloc0(hjoper->combinedKey.bufSize);
        }

        rc = CombineJoinKey(hjoper->rightJoinFields, hjoper->rightRecord, &(hjoper->combinedKey));
        if (rc != GNCDB_SUCCESS) {
          my_free(hjoper->combinedKey.data);
          hjoper->combinedKey.data = NULL;
          return rc;
        }
      }
      hjoper->leftBucktIter = HashmapGetBucket(hjoper->hashTable, &(hjoper->combinedKey));

      //* 当前右元组没有符合的记录，重新下一个右元组之前销毁当前右元组的内存 */
      if (!hjoper->leftBucktIter) {
        hjoper->rightRecord = NULL;
        memset(hjoper->combinedKey.data, 0, hjoper->combinedKey.payloadLen);
      }
    }

    //* 找到相等的记录
    if (memcmp(hjoper->leftBucktIter->key, hjoper->combinedKey.data, hjoper->combinedKey.payloadLen) == 0) {
      hjoper->leftRecord = (Record *)hjoper->leftBucktIter->value;
      matched            = true;
    }
    //* 无论找没找到都要迭代
    hjoper->leftBucktIter = hjoper->leftBucktIter->next;

    if (matched) {
      if (hjoper->leftRecord != NULL && hjoper->rightRecord == NULL) {
        printf("1");
      }
      return GNCDB_SUCCESS;
    }
  }

  return GNCDB_SUCCESS;
}

int HashJoinPhysOperNext(HashJoinPhysicalOperator *hjoper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  /*返回一个满足过滤条件的连接元组*/
  do {  /*这个while循环主要实现哈希连接的后过滤，将不是等值连接的条件放在这里进行过滤*/
    rc = HashJoinPhysOperNextCurrent(hjoper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } while (!HashJoinPhysOperFilter(hjoper));
  assert(hjoper->leftRecord != NULL && hjoper->rightRecord != NULL);  /*正常返回的情况下，左右表的记录应该都不为空*/
  return rc;
}

int HashJoinPhysOperClose(HashJoinPhysicalOperator *hjoper, SQLStageEvent *sqlEvent)
{
  int              rc   = GNCDB_SUCCESS;
  int              i    = 0;
  HashMapIterator *iter = NULL;
  for (i = 0; i < hjoper->children->elementCount; i++) {
    rc = PhysicalOperatorClose((PhysicalOperator *)varArrayListGetPointer(hjoper->children, i), sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  if (hjoper->leftJoinFields != NULL) {
    varArrayListClear(hjoper->leftJoinFields);
  }
  if (hjoper->rightJoinFields != NULL) {
    varArrayListClear(hjoper->rightJoinFields);
  }
  if (hjoper->hashTable != NULL) {
    iter = createHashMapIterator(hjoper->hashTable);
    while (hasNextHashMapIterator(iter)) {
      iter = nextHashMapIterator(iter);
      RecordDestroy((Record **)&(iter->entry->value));
    }
    if (hjoper->resRecord != NULL) {
      RecordDestroy(&hjoper->resRecord);
    }
    hashMapDestroy(&hjoper->hashTable);
    freeHashMapIterator(&iter);
  }
  hjoper->hashTable = NULL;
  return GNCDB_SUCCESS;
}

/**
 * @description: 将哈希连接算子重置，必须在open之后使用，由于open时左表元组已经加入到哈希表中，只需重置右算子
 * @param {HashJoinPhysicalOperator} *hjoper
 * @return {*}
 */
int HashJoinPhysOperReset(HashJoinPhysicalOperator *hjoper)
{
  PhysicalOperator *rightOper = varArrayListGetPointer(hjoper->children, 1);
  int               rc        = PhysicalOperatorReset(rightOper);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  hjoper->leftBucktIter = NULL;
  hjoper->rightRecord   = NULL;
  memset(hjoper->combinedKey.data, 0, hjoper->combinedKey.payloadLen);
  hjoper->combinedKey.payloadLen = 0;
  return GNCDB_SUCCESS;
}

Record *HashJoinPhysOperGetCurrentTuple(HashJoinPhysicalOperator *oper)
{
  RecordSetData(oper->resRecord, 0, oper->leftRecord->data, oper->leftRecord->size);
  RecordSetData(oper->resRecord, oper->leftRecord->size, oper->rightRecord->data, oper->rightRecord->size);
  return oper->resRecord;
}

void HashJoinPhysOperDestroy(HashJoinPhysicalOperator *hjoper)
{
  HashMapIterator *iter = NULL;
  if (hjoper == NULL) {
    return;
  }
  if (hjoper->cols != NULL) {
    varArrayListDestroy(&hjoper->cols);
  }
  if (hjoper->leftJoinFields != NULL) {
    varArrayListDestroy(&hjoper->leftJoinFields);
  }
  if (hjoper->rightJoinFields != NULL) {
    varArrayListDestroy(&hjoper->rightJoinFields);
  }
  if (hjoper->filterOps != NULL) {
    varArrayListDestroy(&hjoper->filterOps);
  }
  if (hjoper->filterLeftMeta != NULL) {
    varArrayListDestroy(&hjoper->filterLeftMeta);
  }
  if (hjoper->filterRightMeta != NULL) {
    varArrayListDestroy(&hjoper->filterRightMeta);
  }
  if (hjoper->combinedKey.data != NULL) {
    my_free(hjoper->combinedKey.data);
  }
  if (hjoper->children != NULL) {
    varArrayListDestroy(&hjoper->children);
  }
  if (hjoper->eqPredicates != NULL) {
    EXPRLIST_DESTROY(hjoper->eqPredicates);
  }
  if (hjoper->otherPredicates != NULL) {
    EXPRLIST_DESTROY(hjoper->otherPredicates);
  }
  if (hjoper->hashTable != NULL) {
    iter = createHashMapIterator(hjoper->hashTable);
    while (hasNextHashMapIterator(iter)) {
      iter = nextHashMapIterator(iter);
      RecordDestroy((Record **)&(iter->entry->value));
    }
    hashMapDestroy(&hjoper->hashTable);
    freeHashMapIterator(&iter);
  }
  my_free(hjoper);
}

void HashJoinPhysOperPointerDestroy(void *data)
{
  HashJoinPhysicalOperator **hjoper = NULL;

  hjoper = (HashJoinPhysicalOperator **)data;
  if (hjoper == NULL || *hjoper == NULL) {
    return;
  }
  HashJoinPhysOperDestroy(*hjoper);
  *hjoper = NULL;
}

/************************************************************
 * SortMergeJoinPhysicalOperator
 ************************************************************/
/**
 * @description: 同一个表的记录计较，用以排序
 * @param {varArrayList} *joinKey - 连接键，ColMeta列表
 * @param {Record} *rec1
 * @param {Record} *rec2
 * @return {*} 比较结果：负数(小于), 0(等于), 正数(大于)
 */
int oneTabRecCmp(varArrayList *joinKey, Record *rec1, Record *rec2)
{
  int      res  = 0;
  char    *str1 = NULL, *str2 = NULL;
  ColMeta *colMeta = NULL;
  for (int i = 0; i < joinKey->elementCount; i++) {
    colMeta = (ColMeta *)varArrayListGetPointer(joinKey, i);
    switch (colMeta->fieldType) {
      case FIELDTYPE_INTEGER:
      case FIELDTYPE_DATE: {
        res = *(int *)(rec1->data + colMeta->offset) - *(int *)(rec2->data + colMeta->offset);
        if (res == 0)
          continue;
        else
          return res;
      } break;
      case FIELDTYPE_REAL: {
        res = *(double *)(rec1->data + colMeta->offset) - *(double *)(rec2->data + colMeta->offset);
        if (res == 0)
          continue;
        else
          return res;
      } break;
      case FIELDTYPE_VARCHAR: {
        str1 = (char *)(rec1->data + colMeta->offset);
        str2 = (char *)(rec2->data + colMeta->offset);
        res  = strcmp(str1, str2);
        if (res == 0)
          continue;
        else
          return res;
      } break;
      default: return res; break;
    }
  }
  return res;
}

int lrRecCmp(SortMergeJoinPhysicalOperator *smjoper, Record *leftRec, Record *rightRec)
{
  ColMeta *leftColMeta = NULL, *rightColMeta = NULL;
  int      res  = 0;
  double   dres = 0;

  for (int i = 0; i < smjoper->leftJoinFields->elementCount; i++) {  // 逐个连接字段进行比较
    leftColMeta  = (ColMeta *)varArrayListGetPointer(smjoper->leftJoinFields, i);
    rightColMeta = (ColMeta *)varArrayListGetPointer(smjoper->rightJoinFields, i);
    assert(leftColMeta->fieldType == rightColMeta->fieldType);  // 左右连接字段类型必须相同
    switch (leftColMeta->fieldType) {
      case FIELDTYPE_INTEGER:
      case FIELDTYPE_DATE: {
        res = *(int *)(leftRec->data + leftColMeta->offset) - *(int *)(rightRec->data + rightColMeta->offset);
        if (res == 0)
          continue;
        else
          return res;
      } break;
      case FIELDTYPE_REAL: {
        dres = *(double *)(leftRec->data + leftColMeta->offset) - *(double *)(rightRec->data + rightColMeta->offset);
        if (dres == 0)
          continue;
        else
          return dres > 0 ? 1 : -1;
      } break;
      case FIELDTYPE_VARCHAR: {
        res = strcmp((char *)(leftRec->data + leftColMeta->offset), (char *)(rightRec->data + rightColMeta->offset));
        if (res == 0)
          continue;
        else
          return res;
      } break;
      default: break;
    }
  }

  return res;
}

/**
 * @description: 左右两个表记录比较
 * @param {SortMergeJoinPhysicalOperator*} smjoper -归并连接操作符
 * @param {int} leftIdx - 左表中的索引
 * @param {int} rightIdx - 右表中的索引
 * @return {int} - 比较结果：负数(小于), 0(等于), 正数(大于)
 */
int lrRecCmpByIdx(SortMergeJoinPhysicalOperator *smjoper, int leftIdx, int rightIdx)
{
  Record *leftRec = NULL, *rightRec = NULL;
  // ColMeta *leftColMeta = NULL, *rightColMeta = NULL;
  // int      res  = 0;
  // double   dres = 0;

  leftRec  = (Record *)varArrayListGetPointer(smjoper->leftRecs, leftIdx);
  rightRec = (Record *)varArrayListGetPointer(smjoper->rightRecs, rightIdx);

  return lrRecCmp(smjoper, leftRec, rightRec);
}

/**
 * @description: 使用快速排序算法对元组列表按连接键排序
 * @param {varArrayList*} tupleList - 要排序的元组列表
 * @param {varArrayList*} joinKey - 用于比较的连接键
 * @param {int} low - 排序起始索引（递归调用时使用）
 * @param {int} high - 排序结束索引（递归调用时使用）
 * @return {int} - 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int tupleListSort(varArrayList *tupleList, varArrayList *joinKey, int low, int high)
{
  int     rc    = 0;     /*返回码*/
  Record *pivot = NULL;  /*枢轴指针*/
  int     i     = 0;     /*左侧指针*/
  int     j     = 0;     /*右侧指针*/
  int     mid   = 0;     /*枢纽指针*/
  void   *temp  = NULL;  /*临时指针用于交换*/

  /*1.参数检查*/
  if (tupleList == NULL || joinKey == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.递归终止条件*/
  if (low >= high) {
    return GNCDB_SUCCESS;
  }

  /*3.选择中间元素作为枢轴*/
  mid   = low + (high - low) / 2;
  pivot = (Record *)varArrayListGetPointer(tupleList, mid);

  /*4.初始化Hoare分区指针*/
  i = low - 1;
  j = high + 1;

  /*5.Hoare分区过程*/
  while (1) {
    /*移动i，找到大于或等于枢轴的元素*/
    do {
      i++;
    } while (i < high && oneTabRecCmp(joinKey, (Record *)varArrayListGetPointer(tupleList, i), pivot) < 0);

    /*移动j，找到小于或等于枢轴的元素*/
    do {
      j--;
    } while (j > low && oneTabRecCmp(joinKey, (Record *)varArrayListGetPointer(tupleList, j), pivot) > 0);

    /*如果i >= j，分区结束*/
    if (i >= j) {
      break;
    }

    /*交换tupleList[i]和tupleList[j]*/
    temp = varArrayListGetPointer(tupleList, i);
    varArrayListSetByIndexPointer(tupleList, i, varArrayListGetPointer(tupleList, j));
    varArrayListSetByIndexPointer(tupleList, j, temp);
  }

  /*6.递归排序左右两部分*/
  rc = tupleListSort(tupleList, joinKey, low, j);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  rc = tupleListSort(tupleList, joinKey, j + 1, high);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return GNCDB_SUCCESS;
}
/**
 * @brief 根据连接条件匹配join key
 *
 * @param left
 * @param right
 * @param oper
 * @return true
 * @return false
 */
int SMJFindJoinKeys(SortMergeJoinPhysicalOperator *oper, Expression *expr)
{
  int             rc      = GNCDB_SUCCESS;
  ComparisonExpr *cmpExpr = NULL;
  if (expr->type == ETG_CONJUNCTION) {  // and连接的多个条件，递归处理
    ConjunctionExpr *conjExpr = (ConjunctionExpr *)expr;
    for (int i = 0; i < conjExpr->children->elementCount; i++) {
      rc = SMJFindJoinKeys(oper, (Expression *)varArrayListGetPointer(conjExpr->children, i));
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  } else if (expr->type == ETG_COMPARISON) {  // 必须确保左右表的连接字段都是field类型，且左字段对应左表，右字段对应右表
    cmpExpr = (ComparisonExpr *)expr;
    if (cmpExpr->comp != CMPOP_EQUAL_TO) {  // 只支持等值连接，如果连接条件不是=，添加到filters中，连接之后再进行过滤
      varArrayListAdd(oper->filterOps, &(cmpExpr->comp));
      FIND_JOIN_KEYS(oper, filterLeftMeta, filterRightMeta, cmpExpr);
    } else {
      if (cmpExpr->left->type == ETG_FIELD && cmpExpr->right->type == ETG_FIELD) {  // 把FieldExpr转换为ColMeta
        FIND_JOIN_KEYS(oper, leftJoinFields, rightJoinFields, cmpExpr);
      } else {  // 按理来说经过谓词下推，此处的连接条件都是field op field形式，不会出现这其他情况
        return GNCDB_JOIN_KEY_NOT_FOUND;
      }
    }
  }
  if (oper->leftJoinFields->elementCount == 0 || oper->rightJoinFields->elementCount == 0) {
    return GNCDB_JOIN_KEY_NOT_FOUND;
  }
  if (oper->leftJoinFields->elementCount != oper->rightJoinFields->elementCount) {
    return GNCDB_JOIN_KEY_NOT_FOUND;
  }
  return GNCDB_SUCCESS;
}

int SortMergeJoinPhysOperInit(SortMergeJoinPhysicalOperator *smjoper, PhysicalOperatorType type)
{
  smjoper->type        = type;
  smjoper->children    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  smjoper->parentTuple = NULL;

  smjoper->left            = NULL;
  smjoper->right           = NULL;
  smjoper->trx             = NULL;
  smjoper->eqPredicates    = NULL;
  smjoper->leftJoinFields  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  smjoper->rightJoinFields = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  smjoper->filterOps       = varArrayListCreate(DISORDER, INT_SIZE, 0, NULL, NULL);
  smjoper->filterLeftMeta  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  smjoper->filterRightMeta = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  smjoper->leftRecs        = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  smjoper->rightRecs       = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  smjoper->leftIdx         = 0;
  smjoper->rightIdx        = 0;
  smjoper->rightSearchIdx  = 0;
  smjoper->cmpRes          = 0;

  return GNCDB_SUCCESS;
}

int SortMergeJoinPhysOperOpen(SortMergeJoinPhysicalOperator *smjoper, SQLStageEvent *sqlEvent)
{
  int                            rc            = GNCDB_SUCCESS;
  PhysicalOperator              *child         = NULL;
  Record                        *record        = NULL;
  ComparisonExpr                *compExpr      = NULL;
  bool                           isLeftFullPK  = false;
  bool                           isRightFullPK = false;
  TableScanPhysicalOperator     *subTabOper    = NULL;
  SortMergeJoinPhysicalOperator *subSmjOper    = NULL;
  varArrayList                  *pkIdxArray    = NULL;
  ColMeta                       *lastColMeta   = NULL;

  /*1.归并连接算子的子算子有且只有两个*/
  if (smjoper->children->elementCount != 2) {
    return GNCDB_INTERNAL;
  }

  /*2.open子算子*/
  for (int i = 0; i < smjoper->children->elementCount; i++) {
    child = (PhysicalOperator *)varArrayListGetPointer(smjoper->children, i);
    rc    = PhysicalOperatorOpen(child, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /*3.设置左右子操作符。*/
  smjoper->left         = (PhysicalOperator *)varArrayListGetPointer(smjoper->children, 0);
  smjoper->leftColMeta  = getColMeta(smjoper->left);
  smjoper->right        = (PhysicalOperator *)varArrayListGetPointer(smjoper->children, 1);
  smjoper->rightColMeta = getColMeta(smjoper->right);
  lastColMeta           = (ColMeta *)varArrayListGetPointer(smjoper->cols, smjoper->cols->elementCount - 1);
  smjoper->resRecord    = RecordCreateWithoutData(lastColMeta->offset + lastColMeta->len);

  /*4.查找连接键。*/
  if (smjoper->isPKSorted) {
    for (int i = 0; i < smjoper->pkPredicates->elementCount; i++) {
      /*4.1收集主键连接字段,非等值条件在filterLeftMeta、filterRightMeta字段，等值条件在leftJoinFields、rightJoinFields*/
      rc = SMJFindJoinKeys(smjoper, (Expression *)varArrayListGetPointer(smjoper->pkPredicates, i));
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
    /*4.2如果是主键连接，继续根据左右算子判断是否是全主键连接 */
    if (smjoper->left->type == PO_TABLE_SCAN) {
      subTabOper   = (TableScanPhysicalOperator *)smjoper->left;
      pkIdxArray   = getPrimaryIndexArray(sqlEvent->db->catalog, subTabOper->table->tableName);
      /*这里认为如果主键等值条件的个数和主键数组的个数相同则认为是全主键连接*/
      isLeftFullPK = pkIdxArray->elementCount == smjoper->leftJoinFields->elementCount;
    } else if (smjoper->left->type == PO_SORT_MERGE_JOIN) {
      subSmjOper   = (SortMergeJoinPhysicalOperator *)smjoper->left;
      isLeftFullPK = subSmjOper->isFullPK;
    }
    if (smjoper->right->type == PO_TABLE_SCAN) {
      subTabOper    = (TableScanPhysicalOperator *)smjoper->right;
      pkIdxArray    = getPrimaryIndexArray(sqlEvent->db->catalog, subTabOper->table->tableName);
      isRightFullPK = pkIdxArray->elementCount == smjoper->rightJoinFields->elementCount;
    } else if (smjoper->right->type == PO_SORT_MERGE_JOIN) {
      subSmjOper    = (SortMergeJoinPhysicalOperator *)smjoper->right;
      isRightFullPK = subSmjOper->isFullPK;
    }
    /*4.3如果左右表都是全主键连接，那么就不需要反复遍历左右表 */
    smjoper->isFullPK = isLeftFullPK && isRightFullPK;
  } else {
    /*收集所有等值条件*/
    for (int i = 0; i < smjoper->eqPredicates->elementCount; i++) {
      rc = SMJFindJoinKeys(smjoper, (Expression *)varArrayListGetPointer(smjoper->eqPredicates, i));  // 收集连接字段
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  }

  /*5.收集过滤条件,这里收集的是非等值条件*/
  if (smjoper->otherPredicates != NULL) {
    for (int i = 0; i < smjoper->otherPredicates->elementCount; i++) {
      compExpr = (ComparisonExpr *)varArrayListGetPointer(smjoper->otherPredicates, i);
      varArrayListAdd(smjoper->filterOps, &(compExpr->comp));
      FIND_JOIN_KEYS(smjoper, filterLeftMeta, filterRightMeta, compExpr);
    }
  }

  /* 如果不是左右全主键连接，左右不是唯一，需要反复遍历，于是需要构造recs用来存放左右的元组 */
  if (!smjoper->isFullPK) {
    /* 左边如果是主键排序，只需要一遍顺序遍历即可，不需要将record复制一份并加入recs中 */
    /* 左算子如果不是已经主键有序的，遍历左子操作符的元组并将它们添加到关联列表中 */
    if (!smjoper->isPKSorted) {
      while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext(smjoper->left, sqlEvent))) {
        record = GetCurrentTuple(smjoper->left);
        if (record == NULL) {
          break;
        }
        rc = varArrayListAddPointer(smjoper->leftRecs, RecordDeepCopy(record));
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
    }
    /* 遍历右子操作符的元组并将它们添加到关联列表中 */
    while (GNCDB_SUCCESS == (rc = PhysicalOperatorNext(smjoper->right, sqlEvent))) {
      record = GetCurrentTuple(smjoper->right);
      if (record == NULL) {
        break;
      }
      /*7.1将元组存储在rightRecs中*/
      rc = varArrayListAddPointer(smjoper->rightRecs, RecordDeepCopy(record));
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  }

  /*TODO: 8.暂时只考虑内部排序，若不是主键已经排好序的了则对leftRecs、rightRecs中的记录进行排序*/
  if (!smjoper->isPKSorted) {
    /*8.1对左表元素在连接条件上排序*/
    rc = tupleListSort(smjoper->leftRecs, smjoper->leftJoinFields, 0, smjoper->leftRecs->elementCount - 1);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    /*8.2对右表元素在连接条件上排序*/
    rc = tupleListSort(smjoper->rightRecs, smjoper->rightJoinFields, 0, smjoper->rightRecs->elementCount - 1);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  if (rc != GNCDB_NEXT_EOF) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

// todo 优化主键连接时的比较操作：lrRecCmpByIdx
/**
 * @brief 进行合并连接操作，获取一条tuple，目前仅支持等值连接。
 *
 * @param sqlEvent
 * @return int
 */
int SortMergeJoinPhysOperNextCurrent(SortMergeJoinPhysicalOperator *smjoper, SQLStageEvent *sqlEvent)
{
  int     rc        = GNCDB_SUCCESS;
  int     cmpResult = 0;
  Record *tmpRecord = NULL;

  /* 全主键连接，无需通过List存放tuple，直接从左右获取，各一次遍历 */
  if (smjoper->isFullPK) {
    while (1) {
      if (smjoper->cmpRes == 0) {
        /* 左右各获取下一个元组 */
        rc = PhysicalOperatorNext(smjoper->left, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        rc = PhysicalOperatorNext(smjoper->right, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      } else if (smjoper->cmpRes < 0) {
        /* 左边小，左边获取下一个元组 */
        rc = PhysicalOperatorNext(smjoper->left, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      } else {
        /* 右边小，右边获取下一个元组 */
        rc = PhysicalOperatorNext(smjoper->right, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
      smjoper->leftRecord  = GetCurrentTuple(smjoper->left);
      smjoper->rightRecord = GetCurrentTuple(smjoper->right);
      if (smjoper->leftRecord == NULL || smjoper->rightRecord == NULL) {
        return GNCDB_NEXT_EOF;
      }
      smjoper->cmpRes = lrRecCmp(smjoper, smjoper->leftRecord, smjoper->rightRecord);
      if (smjoper->cmpRes == 0) {
        return GNCDB_SUCCESS;
      }
    }
  }

  /* 非全主键连接，但是主键连接，左元组只会遍历一遍，直接获取，右边还是通过数组获取 */
  if (smjoper->isPKSorted) {
    while (smjoper->rightIdx < smjoper->rightRecs->elementCount) {
      /* 初始化左右元组 */
      if (smjoper->leftRecord == NULL) {
        rc = PhysicalOperatorNext(smjoper->left, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        smjoper->leftRecord = GetCurrentTuple(smjoper->left);
      }
      if (smjoper->rightRecord == NULL) {
        smjoper->rightRecord = (Record *)varArrayListGetPointer(smjoper->rightRecs, smjoper->rightIdx);
      }

      /* 右表搜索完或者右表搜索的元组不符合条件，右表重置上一次位置并左表++ */
      if (smjoper->rightSearchIdx > smjoper->rightIdx) {
        tmpRecord = (Record *)varArrayListGetPointer(smjoper->rightRecs, smjoper->rightSearchIdx);
        if (smjoper->rightSearchIdx == smjoper->rightRecs->elementCount ||
            lrRecCmp(smjoper, smjoper->leftRecord, tmpRecord) != 0) {
          smjoper->rightSearchIdx = smjoper->rightIdx;
          rc                      = PhysicalOperatorNext(smjoper->left, sqlEvent);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }
          smjoper->leftRecord = GetCurrentTuple(smjoper->left);
        }
      }

      /* 比较结果 */
      cmpResult = lrRecCmp(smjoper, smjoper->leftRecord, smjoper->rightRecord);

      /* 根据结果分别步进 */
      if (cmpResult < 0) {
        rc = PhysicalOperatorNext(smjoper->left, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        smjoper->leftRecord = GetCurrentTuple(smjoper->left);
      } else if (cmpResult > 0) {
        smjoper->rightIdx++;
        smjoper->rightSearchIdx = smjoper->rightIdx;
        smjoper->rightRecord    = (Record *)varArrayListGetPointer(smjoper->rightRecs, smjoper->rightIdx);
      } else {
        if (smjoper->rightSearchIdx < smjoper->rightIdx) {
          smjoper->rightSearchIdx = smjoper->rightIdx;
        }
        smjoper->rightRecord = (Record *)varArrayListGetPointer(smjoper->rightRecs, smjoper->rightSearchIdx);
        smjoper->rightSearchIdx++;
        return GNCDB_SUCCESS;
      }
    }
    return GNCDB_NEXT_EOF;
  }

  /* 剩余情况，无法利用主键，直接通过下标左右遍历recs */
  while (smjoper->leftIdx < smjoper->leftRecs->elementCount && smjoper->rightIdx < smjoper->rightRecs->elementCount) {
    /*1.逐字段比较左右元组的连接字段，若返回0则相等，<0则左边元组偏小，若>0则右边元组偏小*/
    cmpResult = lrRecCmpByIdx(smjoper, smjoper->leftIdx, smjoper->rightIdx);
    if (cmpResult < 0) {
      smjoper->leftIdx++;
    } else if (cmpResult > 0) {
      smjoper->rightIdx++;
    } else {
      /*2.连接成功*/
      /*2.2定义一个index是用来测试右表的下一行元组是否满足连接条件的,用于提前重置右表*/
      if (smjoper->rightSearchIdx < smjoper->rightIdx) {
        smjoper->rightSearchIdx = smjoper->rightIdx;
      }

      /*2.3产生连接好的元组*/
      smjoper->leftRecord  = (Record *)varArrayListGetPointer(smjoper->leftRecs, smjoper->leftIdx);
      smjoper->rightRecord = (Record *)varArrayListGetPointer(smjoper->rightRecs, smjoper->rightSearchIdx);
      smjoper->rightSearchIdx++;

      /*2.4右表搜索完或者右表搜索的元组不符合条件，右表重置上一次位置并左表++,因为这是归并连接已经排好序，因此只要发现不满足连接条件的就直接
      * 驱动表移到下一行*/
      if (smjoper->rightSearchIdx == smjoper->rightRecs->elementCount ||
          lrRecCmpByIdx(smjoper, smjoper->leftIdx, smjoper->rightSearchIdx) != 0) {
        smjoper->rightSearchIdx = smjoper->rightIdx;
        smjoper->leftIdx++;
      }

      return GNCDB_SUCCESS;
    }
  }
  return GNCDB_NEXT_EOF;
}

bool SortMergeJoinPhysOperFilter(SortMergeJoinPhysicalOperator *oper)
{
  bool     result      = false;
  int      eqRes       = 0;  /*最开始按照计算的结果：负数表示左 < 右，0表示相等，正数表示左 > 右*/
  double   eqResD      = 0;
  ColMeta *leftColMeta = NULL, *rightColMeta = NULL;
  if (oper->filterOps == NULL || oper->filterOps->elementCount == 0) {
    return true;
  }

  for (int i = 0; i < oper->filterOps->elementCount; i++) {
    leftColMeta  = (ColMeta *)varArrayListGetPointer(oper->filterLeftMeta, i);
    rightColMeta = (ColMeta *)varArrayListGetPointer(oper->filterRightMeta, i);
    assert(leftColMeta->fieldType == rightColMeta->fieldType);  /*连接字段的类型必须相同*/
    switch (leftColMeta->fieldType) {                           /*根据字段类型进行比较*/
      case FIELDTYPE_INTEGER:
      case FIELDTYPE_DATE:
        eqRes = *(int *)(oper->leftRecord->data + leftColMeta->offset) -
                *(int *)(oper->rightRecord->data + rightColMeta->offset);
        SET_RESULT(*(CompOp *)varArrayListGet(oper->filterOps, i), eqRes, result);
        break;
      case FIELDTYPE_REAL:
        eqResD = *(double *)(oper->leftRecord->data + leftColMeta->offset) -
                 *(double *)(oper->rightRecord->data + rightColMeta->offset);
        if (eqResD < 0) {
          eqResD = -1.0;
        }
        SET_RESULT(*(CompOp *)varArrayListGet(oper->filterOps, i), eqResD, result);
        break;
      case FIELDTYPE_VARCHAR:
        eqRes = strcmp((char *)(oper->leftRecord->data + leftColMeta->offset),
            (char *)(oper->rightRecord->data + rightColMeta->offset));
        if (eqRes == 0) {
          eqRes = 0;
        }
        SET_RESULT(*(CompOp *)varArrayListGet(oper->filterOps, i), eqRes, result);
        break;
      default: break;
    }

    if (!result) {  /*逻辑短路：只要有一个不满足，就返回false*/
      return false;
    }
  }
  return true;
}

int SortMergeJoinPhysOperNext(SortMergeJoinPhysicalOperator *smjoper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;

  do {  /*这个while循环主要实现哈希连接的后过滤，将不是等值连接的条件放在这里进行过滤*/
    rc = SortMergeJoinPhysOperNextCurrent(smjoper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } while (!SortMergeJoinPhysOperFilter(smjoper));
  assert(smjoper->leftRecord != NULL && smjoper->rightRecord != NULL);  /*正常返回的情况下，左右表的记录应该都不为空*/
  return rc;
}

int SortMergeJoinPhysOperClose(SortMergeJoinPhysicalOperator *smjoper, SQLStageEvent *sqlEvent)
{
  int rc = GNCDB_SUCCESS;
  int i  = 0;

  for (i = 0; i < smjoper->children->elementCount; i++) {
    rc = PhysicalOperatorClose((PhysicalOperator *)varArrayListGetPointer(smjoper->children, i), sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  if (smjoper->leftJoinFields != NULL) {
    varArrayListClear(smjoper->leftJoinFields);
  }
  if (smjoper->rightJoinFields != NULL) {
    varArrayListClear(smjoper->rightJoinFields);
  }
  if (smjoper->resRecord != NULL) {
    RecordDestroy(&smjoper->resRecord);
  }
  return GNCDB_SUCCESS;
}

/**
 * @description: 将归并连接算子重置为初始状态，必须在open之后调用，由于open时已经将左右元组存储并排序，直接重置索引即可
 * @param {SortMergeJoinPhysicalOperator} *smjoper
 * @return {*}
 */
int SortMergeJoinPhysOperReset(SortMergeJoinPhysicalOperator *smjoper)
{
  smjoper->leftIdx        = 0;
  smjoper->rightIdx       = 0;
  smjoper->rightSearchIdx = 0;
  return GNCDB_SUCCESS;
}

Record *SortMergeJoinPhysOperGetCurrentTuple(SortMergeJoinPhysicalOperator *smjoper)
{
  RecordSetData(smjoper->resRecord, 0, smjoper->leftRecord->data, smjoper->leftRecord->size);
  RecordSetData(smjoper->resRecord, smjoper->leftRecord->size, smjoper->rightRecord->data, smjoper->rightRecord->size);
  return smjoper->resRecord;
}

void SortMergeJoinPhysOperDestroy(SortMergeJoinPhysicalOperator *smjoper)
{
  Record *rec = NULL;
  if (smjoper == NULL) {
    return;
  }
  if (smjoper->children != NULL) {
    varArrayListDestroy(&smjoper->children);
  }
  if (smjoper->cols != NULL) {
    varArrayListDestroy(&smjoper->cols);
  }
  if (smjoper->leftJoinFields != NULL) {
    varArrayListDestroy(&smjoper->leftJoinFields);
  }
  if (smjoper->rightJoinFields != NULL) {
    varArrayListDestroy(&smjoper->rightJoinFields);
  }
  if (smjoper->filterOps != NULL) {
    varArrayListDestroy(&smjoper->filterOps);
  }
  if (smjoper->filterLeftMeta != NULL) {
    varArrayListDestroy(&smjoper->filterLeftMeta);
  }
  if (smjoper->filterRightMeta != NULL) {
    varArrayListDestroy(&smjoper->filterRightMeta);
  }
  if (smjoper->pkPredicates != NULL) {
    EXPRLIST_DESTROY(smjoper->pkPredicates);
  }
  if (smjoper->eqPredicates != NULL) {
    EXPRLIST_DESTROY(smjoper->eqPredicates);
  }
  if (smjoper->otherPredicates != NULL) {
    EXPRLIST_DESTROY(smjoper->otherPredicates);
  }
  if (smjoper->leftRecs != NULL) {
    for (int i = 0; i < smjoper->leftRecs->elementCount; i++) {
      rec = (Record *)varArrayListGetPointer(smjoper->leftRecs, i);
      RecordDestroy(&rec);
    }
    varArrayListDestroy(&smjoper->leftRecs);
  }
  if (smjoper->rightRecs != NULL) {
    for (int i = 0; i < smjoper->rightRecs->elementCount; i++) {
      rec = (Record *)varArrayListGetPointer(smjoper->rightRecs, i);
      RecordDestroy(&rec);
    }
    varArrayListDestroy(&smjoper->rightRecs);
  }
  my_free(smjoper);
}