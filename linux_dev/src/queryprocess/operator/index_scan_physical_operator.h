/*
 * @Author: zql <EMAIL>
 * @Date: 2025-07-29 10:32:16
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-18 21:25:42
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/operator/index_scan_physical_operator.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef INDEX_SCAN_PHYSICAL_OPERATOR_H
#define INDEX_SCAN_PHYSICAL_OPERATOR_H
#include "btreecursor.h"
#include "catalog.h"
#include "exec_tuple.h"
#include "expression.h"
#include "hash.h"
#include "hashmap.h"
#include "parse_defs.h"
#include "vararraylist.h"
// todo 目前仅仅支持哈希索引，待扩展其他索引类型（Btree）
typedef struct IndexScanPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;  // element type:<PhysicalOperator*>
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;

  Transaction  *txn;
  BtreeTable   *table;
  TableSchema  *tableSchema;    // 表的schema，用以构造colmeta
  IndexType     indexType;      // 索引类型
  HashIndex    *index;          // 索引结构体
  varArrayList *indexExprs;     // 索引扫描条件，当前哈希索引只支持单列
  varArrayList *predicates;     // 索引扫描后过滤条件
  Column       *indexCol;       // 索引列Column
  Value        *indexVal;       // 索引列的值
  varArrayList *pkValues;       // 哈希值查找到的主键值
  int           pkValueIdx;     // 主键值个数
  BtreeCursor  *cursor;         // 回表扫描的btreecursor
  varArrayList *startKeyValue;  // 用以回表时构造btreecursor
  Record       *record;
} IndexScanPhysicalOperator;

int     IndexScanSetColMeta(IndexScanPhysicalOperator *tableScanPhysOper);
void    IndexScanPhysOperInit(IndexScanPhysicalOperator *indexScanPhysOper);
int     IndexScanPhysOperOpen(IndexScanPhysicalOperator *indexScanPhysOper, SQLStageEvent *sqlEvent);
int     IndexScanPhysOperNext(IndexScanPhysicalOperator *indexScanPhysOper, SQLStageEvent *sqlEvent);
int     IndexScanPhysOperReset(IndexScanPhysicalOperator *indexScanPhysOper);
int     IndexScanPhysOperClose(IndexScanPhysicalOperator *indexScanPhysOper, SQLStageEvent *sqlEvent);
Record *IndexScanPhysOperGetCurrentTuple(IndexScanPhysicalOperator *indexScanPhysOper);
void    IndexScanPhysOperDestroy(IndexScanPhysicalOperator *indexScanPhysOper);
void    IndexScanPhysOperPointerDestroy(void *data);
#endif  // INDEX_SCAN_PHYSICAL_OPERATOR_H
