#ifndef UPDATE_PHYSICAL_OPERATOR_H
#define UPDATE_PHYSICAL_OPERATOR_H
#include "hashmap.h"
#include "physical_operator.h"
typedef struct UpdatePhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  SQLStageEvent       *sqlEvent;  // SQL生命周期上下文
  varArrayList        *updateValues;
  varArrayList        *updateFieldNames;
  char                *tableName;
} UpdatePhysicalOperator;
void           UpdatePhysOperInit(UpdatePhysicalOperator *updatePhysicalOperator);
int            UpdatePhysOperOpen(UpdatePhysicalOperator *updatePhysicalOperator, SQLStageEvent *sqlEvent);
int            UpdatePhysOperNext(UpdatePhysicalOperator *updatePhysicalOperator, SQLStageEvent *sqlEvent);
int            UpdatePhysOperClose(UpdatePhysicalOperator *updatePhysicalOperator, SQLStageEvent *sqlEvent);
AbstractTuple *UpdatePhysOperGetCurrentTuple(UpdatePhysicalOperator *updatePhysicalOperator);
int            UpdateSetColMeta(UpdatePhysicalOperator *updatePhysicalOperator);
void           UpdatePhysOperDestroy(UpdatePhysicalOperator *updatePhysicalOperator);
void           UpdatePhysOperPointerDestroy(void *data);
#endif  // UPDATE_PHYSICAL_OPERATOR_H