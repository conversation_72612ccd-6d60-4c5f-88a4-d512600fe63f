#ifndef CALC_PHYSICAL_OPERATOR_H
#define CALC_PHYSICAL_OPERATOR_H
#include "physical_operator.h"
typedef struct CalcPhysicalOperator
{
  PhysicalOperatorType type;
  PhysicalOperatorType parentType;
  varArrayList        *children;  // element type:<PhysicalOperator*>
  Record              *parentTuple;
  varArrayList        *parentCols;
  varArrayList        *cols;
  varArrayList        *expressions;  // element type:<Expression*>
} CalcPhysicalOperator;
void CalcPhysicalOperatorDestroy(CalcPhysicalOperator *calcPhysOper);
void CalcPhysicalOperatorPointerDestroy(void *data);
#endif  // CALC_PHYSICAL_OPERATOR_H
