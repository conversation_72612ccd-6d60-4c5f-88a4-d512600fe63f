#include "sql_event.h"
#include "execute_stage.h"
#include "logical_operator.h"
#include "physical_operator.h"
#include "stmt.h"
#include <stdlib.h>
void SqlResultReset(SqlResult *res)
{
  int i = 0;
  if (res == NULL) {
    return;
  }
  if (res->fieldNames != NULL && res->fieldValues != NULL && res->fieldCount > 0) {
    for (i = 0; i < res->fieldCount; i++) {
      my_free(res->fieldNames[i]);
      my_free(res->fieldValues[i]);
    }
    my_free(res->fieldNames);
    my_free(res->fieldValues);
    res->fieldNames  = NULL;
    res->fieldValues = NULL;
    res->fieldCount  = 0;
  }
}

void SqlResultDestroy(SqlResult **res)
{
  if (res == NULL || *res == NULL) {
    return;
  }
  SqlResultReset(*res);
  my_free(*res);
  *res = NULL;
}

SQLStageEvent *SQLStageEventCreate()
{
  SQLStageEvent *sqlEvent = (SQLStageEvent *)my_malloc0(sizeof(SQLStageEvent));
  if (sqlEvent == NULL) {
    return NULL;
  }
  sqlEvent->sql           = NULL;
  sqlEvent->sqlNode       = NULL;
  sqlEvent->stmt          = NULL;
  sqlEvent->logicalPlan   = NULL;
  sqlEvent->plan          = NULL;
  sqlEvent->res           = NULL;
  sqlEvent->db            = NULL;
  sqlEvent->txn           = NULL;
  sqlEvent->affectedRows  = 0;
  sqlEvent->callback      = NULL;
  sqlEvent->subQueryLevel = 0;
  sqlEvent->isStep        = 0;
  sqlEvent->isOpen        = 0;
  sqlEvent->errmsg        = NULL;
  sqlEvent->data          = NULL;
  // sqlEvent->hasData = 0;
  return sqlEvent;
}

void SQLStageEventReset(SQLStageEvent *sqlEvent)
{
  sqlEvent->sql          = NULL;
  sqlEvent->affectedRows = 0;
  sqlEvent->isStep       = false;
  if (sqlEvent->isOpen) {
    PhysicalOperatorClose(sqlEvent->plan, sqlEvent);
    PhysicalPlanDestroy(sqlEvent->plan);
    sqlEvent->plan = NULL;
  }
  if (sqlEvent->stmt != NULL) {
    StmtDestroy(sqlEvent->stmt);
    sqlEvent->stmt = NULL;
  }
  if (sqlEvent->logicalPlan != NULL) {
    LogicalOperatorDestroy(&sqlEvent->logicalPlan);
    sqlEvent->logicalPlan = NULL;
  }
  if (sqlEvent->sqlNode != NULL) {
    ParsedSqlNodeDestroy(sqlEvent->sqlNode);
    sqlEvent->sqlNode = NULL;
  }
  if(sqlEvent->plan != NULL) {
    PhysicalPlanDestroy(sqlEvent->plan);
    sqlEvent->plan = NULL;
  }
  if (sqlEvent->sql != NULL) {
    my_free((void *)sqlEvent->sql);
    sqlEvent->sql = NULL;
  }
  sqlEvent->isOpen        = false;
  sqlEvent->stmt          = NULL;
  sqlEvent->subQueryLevel = 0;
  SqlResultReset(sqlEvent->res);
}

void SQLStageEventInit(SQLStageEvent *sqlEvent)
{
  sqlEvent->sql              = NULL;
  sqlEvent->sqlNode          = NULL;
  sqlEvent->stmt             = NULL;
  sqlEvent->plan             = NULL;
  sqlEvent->res              = (SqlResult *)my_malloc0(sizeof(SqlResult));
  sqlEvent->res->fieldNames  = NULL;
  sqlEvent->res->fieldValues = NULL;
  sqlEvent->res->fieldCount  = 0;
  sqlEvent->db               = NULL;
  sqlEvent->txn              = NULL;
  sqlEvent->affectedRows     = 0;
  sqlEvent->callback         = NULL;
  sqlEvent->subQueryLevel    = 0;
  sqlEvent->isStep           = false;
  sqlEvent->isOpen           = false;
}

void SQLStageEventDestroy(SQLStageEvent **sqlEvent)
{
  if (sqlEvent == NULL || *sqlEvent == NULL) {
    return;
  }
  if ((*sqlEvent)->sqlNode != NULL) {
    ParsedSqlNodeDestroy((*sqlEvent)->sqlNode);
    (*sqlEvent)->sqlNode = NULL;
  }
  if ((*sqlEvent)->stmt != NULL) {
    StmtDestroy((*sqlEvent)->stmt);
    (*sqlEvent)->stmt = NULL;
  }
  if ((*sqlEvent)->logicalPlan != NULL) {
    LogicalPlanDestroy(&(*sqlEvent)->logicalPlan);
    (*sqlEvent)->logicalPlan = NULL;
  }
  if ((*sqlEvent)->plan != NULL) {
    PhysicalPlanDestroy((*sqlEvent)->plan);
    (*sqlEvent)->plan = NULL;
  }
  if ((*sqlEvent)->res != NULL) {
    SqlResultDestroy(&(*sqlEvent)->res);
  }
  if ((*sqlEvent)->sql != NULL) {
    my_free((void *)(*sqlEvent)->sql);
    (*sqlEvent)->sql = NULL;
  }
  if ((*sqlEvent)->tabOperMap != NULL) {
    hashMapDestroy(&(*sqlEvent)->tabOperMap);
    (*sqlEvent)->tabOperMap = NULL;
  }
  my_free((*sqlEvent));
  *sqlEvent = NULL;
}