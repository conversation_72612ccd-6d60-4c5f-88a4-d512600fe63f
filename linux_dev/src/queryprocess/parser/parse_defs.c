#include "parse_defs.h"
#include "expression.h"
/**************************************结构体指针创建函数******************************************/
RelAttrSqlNode *RelAttrSqlNodeCreate(char *relationName, char *attributeName)
{
  RelAttrSqlNode *sqlNode = (RelAttrSqlNode *)my_malloc0(sizeof(RelAttrSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName  = relationName;
  sqlNode->attributeName = attributeName;
  return sqlNode;
}

ConditionSqlNode *ConditionSqlNodeCreate(Expression *leftExpr, CompOp comp, Expression *rightExpr)
{
  ConditionSqlNode *sqlNode = (ConditionSqlNode *)my_malloc0(sizeof(ConditionSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->lhs  = leftExpr;
  sqlNode->comp = comp;
  sqlNode->rhs  = rightExpr;
  return sqlNode;
}

OrderBySqlNode *OrderBySqlNodeCreate(Expression *expr, bool orderType)
{
  OrderBySqlNode *sqlNode = (OrderBySqlNode *)my_malloc0(sizeof(OrderBySqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->expr  = expr;
  sqlNode->isAsc = orderType;
  return sqlNode;
}

StringPair *StringPairCreate(char *first, char *second)
{
  StringPair *stringPair = (StringPair *)my_malloc0(sizeof(StringPair));
  if (stringPair == NULL) {
    return NULL;
  }
  stringPair->name  = first;
  stringPair->alias = second;
  return stringPair;
}

InnerJoinSqlNode *InnerJoinSqlNodeCreate(
    StringPair *baseRelation, varArrayList *joinRelations, varArrayList *conditions)
{
  InnerJoinSqlNode *sqlNode = (InnerJoinSqlNode *)my_malloc0(sizeof(InnerJoinSqlNode));
  if (sqlNode == NULL) {
    StringPairDestroy(baseRelation);
    varArrayListDestroy(&joinRelations);
    varArrayListDestroy(&conditions);
    return NULL;
  }
  sqlNode->baseRelation  = baseRelation;
  sqlNode->joinRelations = joinRelations;
  sqlNode->conditions    = conditions;
  return sqlNode;
}

LimitSqlNode *LimitSqlNodeCreate(int limit, int offset)
{
  LimitSqlNode *sqlNode = (LimitSqlNode *)my_malloc0(sizeof(LimitSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->limit  = limit;
  sqlNode->offset = offset;
  return sqlNode;
}

SelectSqlNode *SelectSqlNodeCreate(varArrayList *projectExprs, varArrayList *relations, Expression *conditions,
    varArrayList *groupbyExprs, varArrayList *orderbyExprs, Expression *havingConditions, LimitSqlNode *limit)
{
  SelectSqlNode *sqlNode = (SelectSqlNode *)my_malloc0(sizeof(SelectSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->projectExprs     = projectExprs;
  sqlNode->relations        = relations;
  sqlNode->conditions       = conditions;
  sqlNode->groupbyExprs     = groupbyExprs;
  sqlNode->orderbyExprs     = orderbyExprs;
  sqlNode->havingConditions = havingConditions;
  sqlNode->limit            = limit;
  sqlNode->isDistinct       = false;
  return sqlNode;
}

CalcSqlNode *CalcSqlNodeCreate(varArrayList *expressions)
{
  CalcSqlNode *sqlNode = (CalcSqlNode *)my_malloc0(sizeof(CalcSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->expressions = expressions;
  return sqlNode;
}

InsertSqlNode *InsertSqlNodeCreate(char *relationName, varArrayList *attributes, varArrayList *valuelists)
{
  InsertSqlNode *sqlNode = (InsertSqlNode *)my_malloc0(sizeof(InsertSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName = relationName;
  sqlNode->attributes   = attributes;
  sqlNode->valuelists   = valuelists;
  return sqlNode;
}

DeleteSqlNode *DeleteSqlNodeCreate(char *relationName, Expression *conditions)
{
  DeleteSqlNode *sqlNode = (DeleteSqlNode *)my_malloc0(sizeof(DeleteSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName = relationName;
  sqlNode->conditions   = conditions;
  return sqlNode;
}

UpdateKV *UpdateKVCreate(char *attrName, Expression *value)
{
  UpdateKV *kv = (UpdateKV *)my_malloc0(sizeof(UpdateKV));
  if (kv == NULL) {
    return NULL;
  }
  kv->attrName = attrName;
  kv->value    = value;
  return kv;
}

UpdateSqlNode *UpdateSqlNodeCreate(
    char *relationName, varArrayList *attributeNames, varArrayList *values, Expression *conditions)
{
  UpdateSqlNode *sqlNode = (UpdateSqlNode *)my_malloc0(sizeof(UpdateSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName   = relationName;
  sqlNode->attributeNames = attributeNames;
  sqlNode->values         = values;
  sqlNode->conditions     = conditions;
  return sqlNode;
}

AttrInfoSqlNode *AttrInfoSqlNodeCreate(AttrType type, char *name, size_t length, bool isPrimaryKey, bool canBeNull)
{
  AttrInfoSqlNode *sqlNode = (AttrInfoSqlNode *)my_malloc0(sizeof(AttrInfoSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->type         = type;
  sqlNode->name         = name;
  sqlNode->length       = length;
  sqlNode->isPrimaryKey = isPrimaryKey;
  sqlNode->canBeNull    = canBeNull;
  if (type == DOUBLES) {
    sqlNode->length = 8;
  }
  return sqlNode;
}

CreateTableSqlNode *CreateTableSqlNodeCreate(char *relationName, varArrayList *attrInfos)
{
  CreateTableSqlNode *sqlNode = (CreateTableSqlNode *)my_malloc0(sizeof(CreateTableSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName = relationName;
  sqlNode->attrInfos    = attrInfos;
  sqlNode->select       = NULL;
  return sqlNode;
}

DropTableSqlNode *DropTableSqlNodeCreate(char *relationName, bool ifExists)
{
  DropTableSqlNode *sqlNode = (DropTableSqlNode *)my_malloc0(sizeof(DropTableSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName = relationName;
  sqlNode->ifExists     = ifExists;
  return sqlNode;
}

CreateIndexSqlNode *CreateIndexSqlNodeCreate(char *indexName, char *relationName, char *attributeName)
{
  CreateIndexSqlNode *sqlNode = (CreateIndexSqlNode *)my_malloc0(sizeof(CreateIndexSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->indexName     = indexName;
  sqlNode->relationName  = relationName;
  sqlNode->attributeName = attributeName;
  return sqlNode;
}

DropIndexSqlNode *DropIndexSqlNodeCreate(char *indexName, char *relationName)
{
  DropIndexSqlNode *sqlNode = (DropIndexSqlNode *)my_malloc0(sizeof(DropIndexSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->indexName    = indexName;
  sqlNode->relationName = relationName;
  return sqlNode;
}

DescTableSqlNode *DescTableSqlNodeCreate(char *relationName)
{
  DescTableSqlNode *sqlNode = (DescTableSqlNode *)my_malloc0(sizeof(DescTableSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName = relationName;
  return sqlNode;
}

LoadDataSqlNode *LoadDataSqlNodeCreate(char *relationName, char *fileName)
{
  LoadDataSqlNode *sqlNode = (LoadDataSqlNode *)my_malloc0(sizeof(LoadDataSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->relationName = relationName;
  sqlNode->fileName     = fileName;
  return sqlNode;
}

SetVariableSqlNode *SetVariableSqlNodeCreate(char *name, Value *value)
{
  SetVariableSqlNode *sqlNode = (SetVariableSqlNode *)my_malloc0(sizeof(SetVariableSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->name  = name;
  sqlNode->value = value;
  return sqlNode;
}

ExplainSqlNode *ExplainSqlNodeCreate(ParsedSqlNode *sqlNode)
{
  ExplainSqlNode *explainSqlNode = (ExplainSqlNode *)my_malloc0(sizeof(ExplainSqlNode));
  if (explainSqlNode == NULL) {
    return NULL;
  }
  explainSqlNode->sqlNode = sqlNode;
  return explainSqlNode;
}

ErrorSqlNode *ErrorSqlNodeCreate(char *errorMsg)
{
  ErrorSqlNode *errorSqlNode = (ErrorSqlNode *)my_malloc0(sizeof(ErrorSqlNode));
  if (errorSqlNode == NULL) {
    return NULL;
  }
  errorSqlNode->errorMsg = errorMsg;
  return errorSqlNode;
}

ParsedSqlResult *ParsedSqlResultCreate(varArrayList *sqlNodes, size_t count)
{
  ParsedSqlResult *result = (ParsedSqlResult *)my_malloc0(sizeof(ParsedSqlResult));
  if (result == NULL) {
    return NULL;
  }
  result->sqlNodes = sqlNodes;
  return result;
}

ParsedSqlNode *ParsedSqlNodeCreate(SqlCommandFlag flag)
{
  ParsedSqlNode *sqlNode = (ParsedSqlNode *)my_malloc0(sizeof(ParsedSqlNode));
  if (sqlNode == NULL) {
    return NULL;
  }
  sqlNode->flag        = flag;
  sqlNode->error       = NULL;
  sqlNode->calc        = NULL;
  sqlNode->selection   = NULL;
  sqlNode->insertion   = NULL;
  sqlNode->update      = NULL;
  sqlNode->deletion    = NULL;
  sqlNode->createTable = NULL;
  sqlNode->dropTable   = NULL;
  sqlNode->createIndex = NULL;
  sqlNode->dropIndex   = NULL;
  sqlNode->descTable   = NULL;
  sqlNode->loadData    = NULL;
  sqlNode->setVariable = NULL;
  sqlNode->explain     = NULL;
  switch (flag) {
    case SCF_ERROR: {
      sqlNode->error = ErrorSqlNodeCreate(NULL);
      break;
    }
    case SCF_CALC: {
      sqlNode->calc = CalcSqlNodeCreate(NULL);
      break;
    }
    case SCF_SELECT: {
      sqlNode->selection = SelectSqlNodeCreate(NULL, NULL, NULL, NULL, NULL, NULL, NULL);
      break;
    }
    case SCF_INSERT: {
      sqlNode->insertion = InsertSqlNodeCreate(NULL, NULL, NULL);
      break;
    }
    case SCF_UPDATE: {
      sqlNode->update = UpdateSqlNodeCreate(NULL, NULL, NULL, NULL);
      break;
    }
    case SCF_DELETE: {
      sqlNode->deletion = DeleteSqlNodeCreate(NULL, NULL);
      break;
    }
    case SCF_CREATE_TABLE: {
      sqlNode->createTable = CreateTableSqlNodeCreate(NULL, NULL);
      break;
    }
    case SCF_DROP_TABLE: {
      sqlNode->dropTable = DropTableSqlNodeCreate(NULL, false);
      break;
    }
    case SCF_CREATE_INDEX: {
      sqlNode->createIndex = CreateIndexSqlNodeCreate(NULL, NULL, NULL);
      break;
    }
    case SCF_DROP_INDEX: {
      sqlNode->dropIndex = DropIndexSqlNodeCreate(NULL, NULL);
      break;
    }
    case SCF_DESC_TABLE: {
      sqlNode->descTable = DescTableSqlNodeCreate(NULL);
      break;
    }
    case SCF_LOAD_DATA: {
      sqlNode->loadData = LoadDataSqlNodeCreate(NULL, NULL);
      break;
    }
    case SCF_SET_VARIABLE: {
      sqlNode->setVariable = SetVariableSqlNodeCreate(NULL, NULL);
      break;
    }
    case SCF_EXPLAIN: {
      sqlNode->explain = ExplainSqlNodeCreate(NULL);
      break;
    }
    default: break;
  }
  return sqlNode;
}

/**************************************结构体销毁函数******************************************/
void RelAttrSqlNodeDestroy(RelAttrSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  if (sqlNode->attributeName != NULL) {
    my_free(sqlNode->attributeName);
    sqlNode->attributeName = NULL;
  }
  my_free(sqlNode);
}

void ConditionSqlNodeDestroy(ConditionSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->lhs != NULL) {
    exprDestroy(sqlNode->lhs);
    sqlNode->lhs = NULL;
  }
  if (sqlNode->rhs != NULL) {
    exprDestroy(sqlNode->rhs);
    sqlNode->rhs = NULL;
  }
  my_free(sqlNode);
}

void OrderBySqlNodeDestroy(OrderBySqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->expr != NULL) {
    exprDestroy(sqlNode->expr);
    sqlNode->expr = NULL;
  }
  my_free(sqlNode);
}

void StringPairDestroy(StringPair *stringPair)
{
  if (stringPair == NULL) {
    return;
  }
  if (stringPair->name != NULL) {
    my_free(stringPair->name);
    stringPair->name = NULL;
  }
  if (stringPair->alias != NULL) {
    my_free(stringPair->alias);
    stringPair->alias = NULL;
  }
  my_free(stringPair);
}

void InnerJoinSqlNodeDestroy(InnerJoinSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->baseRelation != NULL) {
    StringPairDestroy(sqlNode->baseRelation);
  }
  if (sqlNode->joinRelations != NULL) {
    varArrayListDestroy(&(sqlNode->joinRelations));
  }
  if (sqlNode->conditions != NULL) {
    varArrayListDestroy(&(sqlNode->conditions));
  }
  my_free(sqlNode);
}

void LimitSqlNodeDestroy(LimitSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  my_free(sqlNode);
}

void SelectSqlNodeDestroy(SelectSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->projectExprs != NULL) {
    varArrayListDestroy(&(sqlNode->projectExprs));
  }
  if (sqlNode->relations != NULL) {
    varArrayListDestroy(&(sqlNode->relations));
  }
  if (sqlNode->conditions != NULL) {
    exprDestroy((sqlNode->conditions));
  }
  if (sqlNode->groupbyExprs != NULL) {
    varArrayListDestroy(&(sqlNode->groupbyExprs));
  }
  if (sqlNode->orderbyExprs != NULL) {
    varArrayListDestroy(&(sqlNode->orderbyExprs));
  }
  if (sqlNode->havingConditions != NULL) {
    exprDestroy((sqlNode->havingConditions));
  }
  if (sqlNode->limit != NULL) {
    LimitSqlNodeDestroy((sqlNode->limit));
  }
  my_free(sqlNode);
}

void CalcSqlNodeDestroy(CalcSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->expressions != NULL) {
    varArrayListDestroy(&(sqlNode->expressions));
  }
  my_free(sqlNode);
}

void InsertSqlNodeDestroy(InsertSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  if (sqlNode->attributes != NULL) {
    varArrayListDestroy(&(sqlNode->attributes));
  }
  if (sqlNode->valuelists != NULL) {
    varArrayListDestroy(&(sqlNode->valuelists));
  }
  my_free(sqlNode);
}

void DeleteSqlNodeDestroy(DeleteSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  if (sqlNode->conditions != NULL) {
    exprDestroy((sqlNode->conditions));
  }
  my_free(sqlNode);
}

void UpdateKVDestroy(UpdateKV *kv)
{
  if (kv == NULL) {
    return;
  }
  if (kv->attrName != NULL) {
    my_free(kv->attrName);
    kv->attrName = NULL;
  }
  if (kv->value != NULL) {
    exprDestroy(kv->value);
  }
  my_free(kv);
}

void UpdateSqlNodeDestroy(UpdateSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  if (sqlNode->attributeNames != NULL) {
    varArrayListDestroy(&(sqlNode->attributeNames));
  }
  if (sqlNode->values != NULL) {
    varArrayListDestroy(&(sqlNode->values));
  }
  if (sqlNode->conditions != NULL) {
    exprDestroy((sqlNode->conditions));
  }
  my_free(sqlNode);
}

void AttrInfoSqlNodeDestroy(AttrInfoSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->name != NULL) {
    my_free(sqlNode->name);
    sqlNode->name = NULL;
  }
  my_free(sqlNode);
}

void CreateTableSqlNodeDestroy(CreateTableSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  if (sqlNode->attrInfos != NULL) {
    varArrayListDestroy(&(sqlNode->attrInfos));
  }
  if(sqlNode->select != NULL){
    SelectSqlNodeDestroy(sqlNode->select);
    sqlNode->select = NULL;
  }
  my_free(sqlNode);
}

void DropTableSqlNodeDestroy(DropTableSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  my_free(sqlNode);
}

void CreateIndexSqlNodeDestroy(CreateIndexSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->indexName != NULL) {
    my_free(sqlNode->indexName);
    sqlNode->indexName = NULL;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  if (sqlNode->attributeName != NULL) {
    my_free(sqlNode->attributeName);
    sqlNode->attributeName = NULL;
  }
  my_free(sqlNode);
}

void DropIndexSqlNodeDestroy(DropIndexSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->indexName != NULL) {
    my_free(sqlNode->indexName);
    sqlNode->indexName = NULL;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  my_free(sqlNode);
}

void DescTableSqlNodeDestroy(DescTableSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  my_free(sqlNode);
}

void LoadDataSqlNodeDestroy(LoadDataSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->relationName != NULL) {
    my_free(sqlNode->relationName);
    sqlNode->relationName = NULL;
  }
  if (sqlNode->fileName != NULL) {
    my_free(sqlNode->fileName);
    sqlNode->fileName = NULL;
  }
  my_free(sqlNode);
}

void SetVariableSqlNodeDestroy(SetVariableSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->name != NULL) {
    my_free(sqlNode->name);
    sqlNode->name = NULL;
  }
  /* 注意value如果是字符串类型时需要释放字符串的内存 */
  if (sqlNode->value->attrType == CHARS) {
    if (sqlNode->value->strValue != NULL)
      my_free(sqlNode->value->strValue);
    sqlNode->value->strValue = NULL;
  }
  my_free(sqlNode);
}

void ExplainSqlNodeDestroy(ExplainSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->sqlNode != NULL) {
    ParsedSqlNodeDestroy(sqlNode->sqlNode);
  }
  my_free(sqlNode);
}

void ErrorSqlNodeDestroy(ErrorSqlNode *sqlNode)
{
  if (sqlNode == NULL) {
    return;
  }
  if (sqlNode->errorMsg != NULL) {
    my_free(sqlNode->errorMsg);
    sqlNode->errorMsg = NULL;
  }
  my_free(sqlNode);
}

void ParsedSqlNodeDestroy(ParsedSqlNode *node)
{
  if (node == NULL) {
    return;
  }
  switch (node->flag) {
    case SCF_ERROR: ErrorSqlNodeDestroy((node->error)); break;
    case SCF_CALC: CalcSqlNodeDestroy((node->calc)); break;
    case SCF_SELECT: SelectSqlNodeDestroy((node->selection)); break;
    case SCF_INSERT: InsertSqlNodeDestroy((node->insertion)); break;
    case SCF_UPDATE: UpdateSqlNodeDestroy((node->update)); break;
    case SCF_DELETE: DeleteSqlNodeDestroy((node->deletion)); break;
    case SCF_CREATE_TABLE: CreateTableSqlNodeDestroy((node->createTable)); break;
    case SCF_DROP_TABLE: DropTableSqlNodeDestroy((node->dropTable)); break;
    case SCF_CREATE_INDEX: CreateIndexSqlNodeDestroy((node->createIndex)); break;
    case SCF_DROP_INDEX: DropIndexSqlNodeDestroy((node->dropIndex)); break;
    case SCF_DESC_TABLE: DescTableSqlNodeDestroy((node->descTable)); break;
    case SCF_LOAD_DATA: LoadDataSqlNodeDestroy((node->loadData)); break;
    case SCF_EXPLAIN: ExplainSqlNodeDestroy((node->explain)); break;
    case SCF_SET_VARIABLE: SetVariableSqlNodeDestroy((node->setVariable)); break;
    default: break;
  }
  my_free(node);
}

void ParsedSqlResultDestroy(ParsedSqlResult *result)
{
  if (result == NULL) {
    return;
  }
  if (result->sqlNodes != NULL) {
    varArrayListDestroy(&(result->sqlNodes));
  }
  my_free(result);
}

/**************************************结构体指针销毁函数******************************************/
void RelAttrSqlNodePointerDestroy(void *data)
{
  RelAttrSqlNode **attrSqlNode = (RelAttrSqlNode **)data;
  if (attrSqlNode == NULL || *attrSqlNode == NULL) {
    return;
  }
  RelAttrSqlNodeDestroy(*attrSqlNode);
  *attrSqlNode = NULL;
}

void ConditionSqlNodePointerDestroy(void *data)
{
  ConditionSqlNode **conditionSqlNode = (ConditionSqlNode **)data;
  if (conditionSqlNode == NULL || *conditionSqlNode == NULL) {
    return;
  }
  ConditionSqlNodeDestroy(*conditionSqlNode);
  *conditionSqlNode = NULL;
}

void OrderBySqlNodePointerDestroy(void *data)
{
  OrderBySqlNode **orderBySqlNode = (OrderBySqlNode **)data;
  if (orderBySqlNode == NULL || *orderBySqlNode == NULL) {
    return;
  }
  OrderBySqlNodeDestroy(*orderBySqlNode);
  *orderBySqlNode = NULL;
}

void StringPairPointerDestroy(void *data)
{
  StringPair **stringPair = (StringPair **)data;
  if (stringPair == NULL || *stringPair == NULL) {
    return;
  }
  StringPairDestroy(*stringPair);
  *stringPair = NULL;
}

void InnerJoinSqlNodePointerDestroy(void *data)
{
  InnerJoinSqlNode **innerJoinSqlNode = (InnerJoinSqlNode **)data;
  if (innerJoinSqlNode == NULL || *innerJoinSqlNode == NULL) {
    return;
  }
  InnerJoinSqlNodeDestroy(*innerJoinSqlNode);
  *innerJoinSqlNode = NULL;
}

void LimitSqlNodePointerDestroy(void *data)
{
  LimitSqlNode **limitSqlNode = (LimitSqlNode **)data;
  if (limitSqlNode == NULL || *limitSqlNode == NULL) {
    return;
  }
  LimitSqlNodeDestroy(*limitSqlNode);
  *limitSqlNode = NULL;
}

void SelectSqlNodePointerDestroy(void *data)
{
  SelectSqlNode **selectSqlNode = (SelectSqlNode **)data;
  if (selectSqlNode == NULL || *selectSqlNode == NULL) {
    return;
  }
  SelectSqlNodeDestroy(*selectSqlNode);
  *selectSqlNode = NULL;
}

void CalcSqlNodePointerDestroy(void *data)
{
  CalcSqlNode **calcSqlNode = (CalcSqlNode **)data;
  if (calcSqlNode == NULL || *calcSqlNode == NULL) {
    return;
  }
  CalcSqlNodeDestroy(*calcSqlNode);
  *calcSqlNode = NULL;
}

void InsertSqlNodePointerDestroy(void *data)
{
  InsertSqlNode **insertSqlNode = (InsertSqlNode **)data;
  if (insertSqlNode == NULL || *insertSqlNode == NULL) {
    return;
  }
  InsertSqlNodeDestroy(*insertSqlNode);
  *insertSqlNode = NULL;
}

void DeleteSqlNodePointerDestroy(void *data)
{
  DeleteSqlNode **deleteSqlNode = (DeleteSqlNode **)data;
  if (deleteSqlNode == NULL || *deleteSqlNode == NULL) {
    return;
  }
  DeleteSqlNodeDestroy(*deleteSqlNode);
  *deleteSqlNode = NULL;
}

void UpdateKVPointerDestroy(void *data)
{
  UpdateKV **kv = (UpdateKV **)data;
  if (kv == NULL || *kv == NULL) {
    return;
  }
  UpdateKVDestroy(*kv);
  *kv = NULL;
}

void UpdateSqlNodePointerDestroy(void *data)
{
  UpdateSqlNode **updateSqlNode = (UpdateSqlNode **)data;
  if (updateSqlNode == NULL || *updateSqlNode == NULL) {
    return;
  }
  UpdateSqlNodeDestroy(*updateSqlNode);
  *updateSqlNode = NULL;
}

void AttrInfoSqlNodePointerDestroy(void *data)
{
  AttrInfoSqlNode **attrInfoSqlNode = (AttrInfoSqlNode **)data;
  if (attrInfoSqlNode == NULL || *attrInfoSqlNode == NULL) {
    return;
  }
  AttrInfoSqlNodeDestroy(*attrInfoSqlNode);
  *attrInfoSqlNode = NULL;
}

void CreateTableSqlNodePointerDestroy(void *data)
{
  CreateTableSqlNode **createTableSqlNode = (CreateTableSqlNode **)data;
  if (createTableSqlNode == NULL || *createTableSqlNode == NULL) {
    return;
  }
  CreateTableSqlNodeDestroy(*createTableSqlNode);
  *createTableSqlNode = NULL;
}

void DropTableSqlNodePointerDestroy(void *data)
{
  DropTableSqlNode **dropTableSqlNode = (DropTableSqlNode **)data;
  if (dropTableSqlNode == NULL || *dropTableSqlNode == NULL) {
    return;
  }
  DropTableSqlNodeDestroy(*dropTableSqlNode);
  *dropTableSqlNode = NULL;
}

void CreateIndexSqlNodePointerDestroy(void *data)
{
  CreateIndexSqlNode **createIndexSqlNode = (CreateIndexSqlNode **)data;
  if (createIndexSqlNode == NULL || *createIndexSqlNode == NULL) {
    return;
  }
  CreateIndexSqlNodeDestroy(*createIndexSqlNode);
  *createIndexSqlNode = NULL;
}

void DropIndexSqlNodePointerDestroy(void *data)
{
  DropIndexSqlNode **dropIndexSqlNode = (DropIndexSqlNode **)data;
  if (dropIndexSqlNode == NULL || *dropIndexSqlNode == NULL) {
    return;
  }
  DropIndexSqlNodeDestroy(*dropIndexSqlNode);
  *dropIndexSqlNode = NULL;
}

void DescTableSqlNodePointerDestroy(void *data)
{
  DescTableSqlNode **descTableSqlNode = (DescTableSqlNode **)data;
  if (descTableSqlNode == NULL || *descTableSqlNode == NULL) {
    return;
  }
  DescTableSqlNodeDestroy(*descTableSqlNode);
  *descTableSqlNode = NULL;
}

void LoadDataSqlNodePointerDestroy(void *data)
{
  LoadDataSqlNode **loadDataSqlNode = (LoadDataSqlNode **)data;
  if (loadDataSqlNode == NULL || *loadDataSqlNode == NULL) {
    return;
  }
  LoadDataSqlNodeDestroy(*loadDataSqlNode);
  *loadDataSqlNode = NULL;
}

void SetVariableSqlNodePointerDestroy(void *data)
{
  SetVariableSqlNode **setVariableSqlNode = (SetVariableSqlNode **)data;
  if (setVariableSqlNode == NULL || *setVariableSqlNode == NULL) {
    return;
  }
  SetVariableSqlNodeDestroy(*setVariableSqlNode);
  *setVariableSqlNode = NULL;
}

void ExplainSqlNodePointerDestroy(void *data)
{
  ExplainSqlNode **explainSqlNode = (ExplainSqlNode **)data;
  if (explainSqlNode == NULL || *explainSqlNode == NULL) {
    return;
  }
  ExplainSqlNodeDestroy(*explainSqlNode);
  *explainSqlNode = NULL;
}

void ErrorSqlNodePointerDestroy(void *data)
{
  ErrorSqlNode **errorSqlNode = (ErrorSqlNode **)data;
  if (errorSqlNode == NULL || *errorSqlNode == NULL) {
    return;
  }
  ErrorSqlNodeDestroy(*errorSqlNode);
  *errorSqlNode = NULL;
}

void ParsedSqlNodePointerDestroy(void *data)
{
  ParsedSqlNode **sqlNode = (ParsedSqlNode **)data;
  if (sqlNode == NULL || *sqlNode == NULL) {
    return;
  }
  ParsedSqlNodeDestroy(*sqlNode);
  *sqlNode = NULL;
}

void ParsedSqlResultPointerDestroy(void *data)
{
  ParsedSqlResult **result = (ParsedSqlResult **)data;
  if (result == NULL || *result == NULL) {
    return;
  }
  ParsedSqlResultDestroy(*result);
  *result = NULL;
}

void valuelistsPointerDestroy(void *data)
{
  varArrayList **valuelists = (varArrayList **)data;
  if (valuelists == NULL || *valuelists == NULL) {
    return;
  }
  varArrayListDestroy(valuelists);
  *valuelists = NULL;
}

void charPointerDestroy(void *data)
{
  char **str = (char **)data;
  if (str == NULL || *str == NULL) {
    return;
  }
  my_free(*str);
  *str = NULL;
}

AttrInfoSqlNode *AttrInfoSqlNodeDeepCopy(AttrInfoSqlNode *sqlNode)
{
  AttrInfoSqlNode *newSqlNode = NULL;
  if (sqlNode == NULL) {
    return NULL;
  }
  newSqlNode = AttrInfoSqlNodeCreate(
      sqlNode->type, my_strdup(sqlNode->name), sqlNode->length, sqlNode->isPrimaryKey, sqlNode->canBeNull);
  return newSqlNode;
}
