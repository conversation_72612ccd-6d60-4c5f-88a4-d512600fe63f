#include "parse_stage.h"
#include "sql_event.h"
#include "parse_defs.h"
#include "gncdb.h"
#include "vararraylist.h"
#include "transaction.h"
extern int sqlParse(const char *s, ParsedSqlResult *sqlResult);

int ParseStageHandleRequest(SQLStageEvent *sqlEvent)
{
  int              rc              = GNCDB_SUCCESS;
  ParsedSqlResult *parsedSqlResult = NULL;
  ParsedSqlNode   *sqlNode         = NULL;

  /*1.对sql进行词法分析，单线程需要加锁*/
  parsedSqlResult =
      ParsedSqlResultCreate(varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, ParsedSqlNodePointerDestroy), 0);
  WriteLock(&(sqlEvent->db->latchSQLParse));
  rc = sqlParse(sqlEvent->sql, parsedSqlResult);
  WriteUnLock(&(sqlEvent->db->latchSQLParse));

  /*2.对词法分析的结果进行正确性判断，只有解析成一个stmt的时候才正确*/
  if (rc != 0) {
    ParsedSqlResultDestroy(parsedSqlResult);
    return GNCDB_PARSE_FALSE;
  }
  if (parsedSqlResult->sqlNodes->elementCount == 0) {
    ParsedSqlResultDestroy(parsedSqlResult);
    return GNCDB_PARSE_FALSE;
  }
  if (parsedSqlResult->sqlNodes->elementCount > 1) {
    ParsedSqlResultDestroy(parsedSqlResult);
    return GNCDB_PARSE_FALSE;
  }

  /*3.词法分析结果正确，将结果转移给sqlEvent的sqlNode字段*/
  sqlNode = (ParsedSqlNode *)varArrayListGetPointer(parsedSqlResult->sqlNodes, 0);
  if (sqlNode->flag == SCF_ERROR) {
    ParsedSqlResultDestroy(parsedSqlResult);
    rc = GNCDB_PARSE_FALSE;
    varArrayListDestroy(&parsedSqlResult->sqlNodes);
    return rc;
  }

  sqlEvent->sqlNode = sqlNode;
  /*这里是为了将 sqlNode 从 parsedSqlResult 中移除，其所有权转移给*/
  /*sqlEvent，避免销毁parsedSqlResult时重复销毁 sqlNode*/
  varArrayListRemoveByIndexPointer(
      parsedSqlResult->sqlNodes, 0);

  /*4.释放内存 */
  ParsedSqlResultDestroy(parsedSqlResult);
  return GNCDB_SUCCESS;
}
