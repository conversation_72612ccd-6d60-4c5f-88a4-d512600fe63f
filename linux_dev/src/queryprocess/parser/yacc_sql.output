Grammar

    0 $accept: commands $end

    1 commands: commandWrapper optSemicolon

    2 commandWrapper: calcStmt
    3               | selectStmt
    4               | insertStmt
    5               | updateStmt
    6               | deleteStmt
    7               | createTableStmt
    8               | dropTableStmt
    9               | showTablesStmt
   10               | descTableStmt
   11               | createIndexStmt
   12               | dropIndexStmt
   13               | syncStmt
   14               | beginStmt
   15               | commitStmt
   16               | rollbackStmt
   17               | loadDataStmt
   18               | explainStmt
   19               | setVariableStmt
   20               | helpStmt
   21               | exitStmt

   22 exitStmt: EXIT

   23 helpStmt: HELP

   24 syncStmt: SYNC

   25 beginStmt: TRX_BEGIN

   26 commitStmt: TRX_COMMIT

   27 rollbackStmt: TRX_ROLLBACK

   28 dropTableStmt: DROP TABLE IF EXISTS ID
   29              | DROP TABLE ID

   30 showTablesStmt: SHOW TABLES

   31 descTableStmt: DESC ID

   32 createIndexStmt: CREATE INDEX ID ON ID LBRACE ID RBRACE
   33                | CREATE INDEX ID ON ID LBRACE ID RBRACE USING HASH

   34 dropIndexStmt: DROP INDEX ID ON ID

   35 createTableStmt: CREATE TABLE ID LBRACE attrDef attrDefList RBRACE
   36                | CREATE TABLE ID LBRACE attrDef attrDefList RBRACE asOption selectStmt
   37                | CREATE TABLE ID asOption selectStmt

   38 attrDefList: %empty
   39            | COMMA attrDef attrDefList

   40 attrDef: ID type LBRACE number RBRACE
   41        | ID type LBRACE number RBRACE PRIMARY KEY
   42        | ID type
   43        | ID type PRIMARY KEY
   44        | ID type LBRACE number RBRACE NOT NULL_T
   45        | ID type LBRACE number RBRACE PRIMARY KEY NOT NULL_T
   46        | ID type NOT NULL_T
   47        | ID type PRIMARY KEY NOT NULL_T

   48 asOption: %empty
   49         | AS

   50 number: NUMBER

   51 type: INT_T
   52     | DATE_T
   53     | STRING_T
   54     | FLOAT_T
   55     | DATETIME_T
   56     | TEXT_T
   57     | BLOB_T

   58 insertStmt: INSERT INTO ID insertColList VALUES insertValue insertValueList

   59 insertColList: %empty
   60              | LBRACE ID idxColList RBRACE

   61 idxColList: %empty
   62           | COMMA ID idxColList

   63 insertValueList: %empty
   64                | COMMA insertValue insertValueList

   65 insertValue: LBRACE expression valueList RBRACE

   66 valueList: %empty
   67          | COMMA expression valueList

   68 value: NUMBER
   69      | FLOAT
   70      | DATE_STR
   71      | DATE_TIME_STR
   72      | SSS
   73      | NULL_T

   74 deleteStmt: DELETE FROM ID where

   75 updateStmt: UPDATE ID SET updateKv updateKvList where

   76 updateKvList: %empty
   77             | COMMA updateKv updateKvList

   78 updateKv: ID EQ expression

   79 selectStmt: SELECT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit
   80           | SELECT DISTINCT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit

   81 calcStmt: CALC expressionList

   82 expressionList: expression alias
   83               | expression alias COMMA expressionList

   84 expression: expression '+' expression
   85           | expression '-' expression
   86           | expression '*' expression
   87           | expression '/' expression
   88           | LBRACE expressionList RBRACE
   89           | '-' expression
   90           | value
   91           | relAttr
   92           | aggrFuncExpr
   93           | subQueryExpr

   94 aggrFuncType: AGGR_MAX
   95             | AGGR_MIN
   96             | AGGR_SUM
   97             | AGGR_AVG
   98             | AGGR_COUNT

   99 aggrFuncExpr: aggrFuncType LBRACE expression RBRACE
  100             | aggrFuncType LBRACE '*' RBRACE

  101 selectAttr: '*'
  102           | '*' DOT '*'
  103           | ID DOT '*'
  104           | expressionList

  105 relAttr: ID
  106        | ID DOT ID

  107 fromList: %empty
  108         | COMMA fromNode fromList

  109 alias: %empty
  110      | ID
  111      | AS ID

  112 fromNode: ID alias joinList

  113 joinList: %empty
  114         | INNER JOIN ID alias ON condition joinList

  115 subQueryExpr: LBRACE selectStmt RBRACE

  116 where: %empty
  117      | WHERE condition

  118 condition: LBRACE condition RBRACE
  119          | expression compOp expression
  120          | existsOp expression
  121          | condition AND condition
  122          | condition OR condition

  123 sortUnit: expression
  124         | expression DESC
  125         | expression ASC

  126 sortList: sortUnit
  127         | sortUnit COMMA sortList

  128 optOrderBy: %empty
  129           | ORDER_T BY sortList

  130 optGroupBy: %empty
  131           | GROUP BY expressionList

  132 optHaving: %empty
  133          | HAVING condition

  134 optLimit: LIMIT NUMBER OFFSET NUMBER
  135         | LIMIT NUMBER
  136         | %empty

  137 compOp: EQ
  138       | LT
  139       | GT
  140       | LE
  141       | GE
  142       | NE
  143       | LIKE
  144       | NOT LIKE
  145       | IN
  146       | NOT IN

  147 existsOp: EXISTS
  148         | NOT EXISTS

  149 loadDataStmt: LOAD DATA INFILE SSS INTO TABLE ID

  150 explainStmt: EXPLAIN commandWrapper

  151 setVariableStmt: SET ID EQ value

  152 optSemicolon: %empty
  153             | SEMICOLON


Terminals, with rules where they appear

    $end (0) 0
    '*' (42) 86 100 101 102 103
    '+' (43) 84
    '-' (45) 85 89
    '/' (47) 87
    error (256)
    SEMICOLON (258) 153
    CREATE (259) 32 33 35 36 37
    DROP (260) 28 29 34
    TABLE (261) 28 29 35 36 37 149
    TABLES (262) 30
    INDEX (263) 32 33 34
    CALC (264) 81
    SELECT (265) 79 80
    DESC (266) 31 124
    SHOW (267) 30
    SYNC (268) 24
    INSERT (269) 58
    DELETE (270) 74
    UPDATE (271) 75
    LBRACE (272) 32 33 35 36 40 41 44 45 60 65 88 99 100 115 118
    RBRACE (273) 32 33 35 36 40 41 44 45 60 65 88 99 100 115 118
    COMMA (274) 39 62 64 67 77 83 108 127
    TRX_BEGIN (275) 25
    TRX_COMMIT (276) 26
    TRX_ROLLBACK (277) 27
    INT_T (278) 51
    BLOB_T (279) 57
    DATE_T (280) 52
    DATETIME_T (281) 55
    TEXT_T (282) 56
    STRING_T (283) 53
    FLOAT_T (284) 54
    HELP (285) 23
    EXIT (286) 22
    DOT (287) 102 103 106
    INTO (288) 58 149
    VALUES (289) 58
    FROM (290) 74 79 80
    WHERE (291) 117
    AND (292) 121
    OR (293) 122
    SET (294) 75 151
    USING (295) 33
    HASH (296) 33
    ON (297) 32 33 34 114
    LOAD (298) 149
    DATA (299) 149
    INFILE (300) 149
    EXPLAIN (301) 150
    PRIMARY (302) 41 43 45 47
    KEY (303) 41 43 45 47
    NOT (304) 44 45 46 47 144 146 148
    LIKE (305) 143 144
    NULL_T (306) 44 45 46 47 73
    EQ (307) 78 137 151
    LT (308) 138
    GT (309) 139
    LE (310) 140
    GE (311) 141
    NE (312) 142
    INNER (313) 114
    JOIN (314) 114
    AGGR_MAX (315) 94
    AGGR_MIN (316) 95
    AGGR_SUM (317) 96
    AGGR_AVG (318) 97
    AGGR_COUNT (319) 98
    ORDER_T (320) 129
    GROUP (321) 131
    BY (322) 129 131
    ASC (323) 125
    HAVING (324) 133
    AS (325) 49 111
    IN (326) 145 146
    EXISTS (327) 28 147 148
    LIMIT (328) 134 135
    OFFSET (329) 134
    IF (330) 28
    DISTINCT (331) 80
    NUMBER <number> (332) 50 68 134 135
    FLOAT <floats> (333) 69
    ID <string> (334) 28 29 31 32 33 34 35 36 37 40 41 42 43 44 45 46 47 58 60 62 74 75 78 103 105 106 110 111 112 114 149 151
    DATE_TIME_STR <string> (335) 71
    DATE_STR <string> (336) 70
    SSS <string> (337) 72 149
    UMINUS (338)


Nonterminals, with rules where they appear

    $accept (88)
        on left: 0
    commands <sqlNode> (89)
        on left: 1
        on right: 0
    commandWrapper <sqlNode> (90)
        on left: 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21
        on right: 1 150
    exitStmt <sqlNode> (91)
        on left: 22
        on right: 21
    helpStmt <sqlNode> (92)
        on left: 23
        on right: 20
    syncStmt <sqlNode> (93)
        on left: 24
        on right: 13
    beginStmt <sqlNode> (94)
        on left: 25
        on right: 14
    commitStmt <sqlNode> (95)
        on left: 26
        on right: 15
    rollbackStmt <sqlNode> (96)
        on left: 27
        on right: 16
    dropTableStmt <sqlNode> (97)
        on left: 28 29
        on right: 8
    showTablesStmt <sqlNode> (98)
        on left: 30
        on right: 9
    descTableStmt <sqlNode> (99)
        on left: 31
        on right: 10
    createIndexStmt <sqlNode> (100)
        on left: 32 33
        on right: 11
    dropIndexStmt <sqlNode> (101)
        on left: 34
        on right: 12
    createTableStmt <sqlNode> (102)
        on left: 35 36 37
        on right: 7
    attrDefList <attrInfos> (103)
        on left: 38 39
        on right: 35 36 39
    attrDef <attrInfo> (104)
        on left: 40 41 42 43 44 45 46 47
        on right: 35 36 39
    asOption <boolean> (105)
        on left: 48 49
        on right: 36 37
    number <number> (106)
        on left: 50
        on right: 40 41 44 45
    type <number> (107)
        on left: 51 52 53 54 55 56 57
        on right: 40 41 42 43 44 45 46 47
    insertStmt <sqlNode> (108)
        on left: 58
        on right: 4
    insertColList <relationList> (109)
        on left: 59 60
        on right: 58
    idxColList <relationList> (110)
        on left: 61 62
        on right: 60 62
    insertValueList <insertValueList> (111)
        on left: 63 64
        on right: 58 64
    insertValue <valueList> (112)
        on left: 65
        on right: 58 64
    valueList <valueList> (113)
        on left: 66 67
        on right: 65 67
    value <value> (114)
        on left: 68 69 70 71 72 73
        on right: 90 151
    deleteStmt <sqlNode> (115)
        on left: 74
        on right: 6
    updateStmt <sqlNode> (116)
        on left: 75
        on right: 5
    updateKvList <updateKvList> (117)
        on left: 76 77
        on right: 75 77
    updateKv <updateKv> (118)
        on left: 78
        on right: 75 77
    selectStmt <sqlNode> (119)
        on left: 79 80
        on right: 3 36 37 115
    calcStmt <sqlNode> (120)
        on left: 81
        on right: 2
    expressionList <expressionList> (121)
        on left: 82 83
        on right: 81 83 88 104 131
    expression <expression> (122)
        on left: 84 85 86 87 88 89 90 91 92 93
        on right: 65 67 78 82 83 84 85 86 87 89 99 119 120 123 124 125
    aggrFuncType <number> (123)
        on left: 94 95 96 97 98
        on right: 99 100
    aggrFuncExpr <expression> (124)
        on left: 99 100
        on right: 92
    selectAttr <expressionList> (125)
        on left: 101 102 103 104
        on right: 79 80
    relAttr <relAttr> (126)
        on left: 105 106
        on right: 91
    fromList <innerJoinsList> (127)
        on left: 107 108
        on right: 79 80 108
    alias <string> (128)
        on left: 109 110 111
        on right: 82 83 112 114
    fromNode <innerJoins> (129)
        on left: 112
        on right: 79 80 108
    joinList <innerJoins> (130)
        on left: 113 114
        on right: 112 114
    subQueryExpr <expression> (131)
        on left: 115
        on right: 93
    where <expression> (132)
        on left: 116 117
        on right: 74 75 79 80
    condition <expression> (133)
        on left: 118 119 120 121 122
        on right: 114 117 118 121 122 133
    sortUnit <orderbyUnit> (134)
        on left: 123 124 125
        on right: 126 127
    sortList <orderbyUnitList> (135)
        on left: 126 127
        on right: 127 129
    optOrderBy <orderbyUnitList> (136)
        on left: 128 129
        on right: 79 80
    optGroupBy <expressionList> (137)
        on left: 130 131
        on right: 79 80
    optHaving <expression> (138)
        on left: 132 133
        on right: 79 80
    optLimit <limit> (139)
        on left: 134 135 136
        on right: 79 80
    compOp <comp> (140)
        on left: 137 138 139 140 141 142 143 144 145 146
        on right: 119
    existsOp <comp> (141)
        on left: 147 148
        on right: 120
    loadDataStmt <sqlNode> (142)
        on left: 149
        on right: 17
    explainStmt <sqlNode> (143)
        on left: 150
        on right: 18
    setVariableStmt <sqlNode> (144)
        on left: 151
        on right: 19
    optSemicolon (145)
        on left: 152 153
        on right: 1


State 0

    0 $accept: . commands $end

    CREATE        shift, and go to state 1
    DROP          shift, and go to state 2
    CALC          shift, and go to state 3
    SELECT        shift, and go to state 4
    DESC          shift, and go to state 5
    SHOW          shift, and go to state 6
    SYNC          shift, and go to state 7
    INSERT        shift, and go to state 8
    DELETE        shift, and go to state 9
    UPDATE        shift, and go to state 10
    TRX_BEGIN     shift, and go to state 11
    TRX_COMMIT    shift, and go to state 12
    TRX_ROLLBACK  shift, and go to state 13
    HELP          shift, and go to state 14
    EXIT          shift, and go to state 15
    SET           shift, and go to state 16
    LOAD          shift, and go to state 17
    EXPLAIN       shift, and go to state 18

    commands         go to state 19
    commandWrapper   go to state 20
    exitStmt         go to state 21
    helpStmt         go to state 22
    syncStmt         go to state 23
    beginStmt        go to state 24
    commitStmt       go to state 25
    rollbackStmt     go to state 26
    dropTableStmt    go to state 27
    showTablesStmt   go to state 28
    descTableStmt    go to state 29
    createIndexStmt  go to state 30
    dropIndexStmt    go to state 31
    createTableStmt  go to state 32
    insertStmt       go to state 33
    deleteStmt       go to state 34
    updateStmt       go to state 35
    selectStmt       go to state 36
    calcStmt         go to state 37
    loadDataStmt     go to state 38
    explainStmt      go to state 39
    setVariableStmt  go to state 40


State 1

   32 createIndexStmt: CREATE . INDEX ID ON ID LBRACE ID RBRACE
   33                | CREATE . INDEX ID ON ID LBRACE ID RBRACE USING HASH
   35 createTableStmt: CREATE . TABLE ID LBRACE attrDef attrDefList RBRACE
   36                | CREATE . TABLE ID LBRACE attrDef attrDefList RBRACE asOption selectStmt
   37                | CREATE . TABLE ID asOption selectStmt

    TABLE  shift, and go to state 41
    INDEX  shift, and go to state 42


State 2

   28 dropTableStmt: DROP . TABLE IF EXISTS ID
   29              | DROP . TABLE ID
   34 dropIndexStmt: DROP . INDEX ID ON ID

    TABLE  shift, and go to state 43
    INDEX  shift, and go to state 44


State 3

   81 calcStmt: CALC . expressionList

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value           go to state 59
    expressionList  go to state 60
    expression      go to state 61
    aggrFuncType    go to state 62
    aggrFuncExpr    go to state 63
    relAttr         go to state 64
    subQueryExpr    go to state 65


State 4

   79 selectStmt: SELECT . selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit
   80           | SELECT . DISTINCT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    DISTINCT       shift, and go to state 66
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 67
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58
    '*'            shift, and go to state 68

    value           go to state 59
    expressionList  go to state 69
    expression      go to state 61
    aggrFuncType    go to state 62
    aggrFuncExpr    go to state 63
    selectAttr      go to state 70
    relAttr         go to state 64
    subQueryExpr    go to state 65


State 5

   31 descTableStmt: DESC . ID

    ID  shift, and go to state 71


State 6

   30 showTablesStmt: SHOW . TABLES

    TABLES  shift, and go to state 72


State 7

   24 syncStmt: SYNC .

    $default  reduce using rule 24 (syncStmt)


State 8

   58 insertStmt: INSERT . INTO ID insertColList VALUES insertValue insertValueList

    INTO  shift, and go to state 73


State 9

   74 deleteStmt: DELETE . FROM ID where

    FROM  shift, and go to state 74


State 10

   75 updateStmt: UPDATE . ID SET updateKv updateKvList where

    ID  shift, and go to state 75


State 11

   25 beginStmt: TRX_BEGIN .

    $default  reduce using rule 25 (beginStmt)


State 12

   26 commitStmt: TRX_COMMIT .

    $default  reduce using rule 26 (commitStmt)


State 13

   27 rollbackStmt: TRX_ROLLBACK .

    $default  reduce using rule 27 (rollbackStmt)


State 14

   23 helpStmt: HELP .

    $default  reduce using rule 23 (helpStmt)


State 15

   22 exitStmt: EXIT .

    $default  reduce using rule 22 (exitStmt)


State 16

  151 setVariableStmt: SET . ID EQ value

    ID  shift, and go to state 76


State 17

  149 loadDataStmt: LOAD . DATA INFILE SSS INTO TABLE ID

    DATA  shift, and go to state 77


State 18

  150 explainStmt: EXPLAIN . commandWrapper

    CREATE        shift, and go to state 1
    DROP          shift, and go to state 2
    CALC          shift, and go to state 3
    SELECT        shift, and go to state 4
    DESC          shift, and go to state 5
    SHOW          shift, and go to state 6
    SYNC          shift, and go to state 7
    INSERT        shift, and go to state 8
    DELETE        shift, and go to state 9
    UPDATE        shift, and go to state 10
    TRX_BEGIN     shift, and go to state 11
    TRX_COMMIT    shift, and go to state 12
    TRX_ROLLBACK  shift, and go to state 13
    HELP          shift, and go to state 14
    EXIT          shift, and go to state 15
    SET           shift, and go to state 16
    LOAD          shift, and go to state 17
    EXPLAIN       shift, and go to state 18

    commandWrapper   go to state 78
    exitStmt         go to state 21
    helpStmt         go to state 22
    syncStmt         go to state 23
    beginStmt        go to state 24
    commitStmt       go to state 25
    rollbackStmt     go to state 26
    dropTableStmt    go to state 27
    showTablesStmt   go to state 28
    descTableStmt    go to state 29
    createIndexStmt  go to state 30
    dropIndexStmt    go to state 31
    createTableStmt  go to state 32
    insertStmt       go to state 33
    deleteStmt       go to state 34
    updateStmt       go to state 35
    selectStmt       go to state 36
    calcStmt         go to state 37
    loadDataStmt     go to state 38
    explainStmt      go to state 39
    setVariableStmt  go to state 40


State 19

    0 $accept: commands . $end

    $end  shift, and go to state 79


State 20

    1 commands: commandWrapper . optSemicolon

    SEMICOLON  shift, and go to state 80

    $default  reduce using rule 152 (optSemicolon)

    optSemicolon  go to state 81


State 21

   21 commandWrapper: exitStmt .

    $default  reduce using rule 21 (commandWrapper)


State 22

   20 commandWrapper: helpStmt .

    $default  reduce using rule 20 (commandWrapper)


State 23

   13 commandWrapper: syncStmt .

    $default  reduce using rule 13 (commandWrapper)


State 24

   14 commandWrapper: beginStmt .

    $default  reduce using rule 14 (commandWrapper)


State 25

   15 commandWrapper: commitStmt .

    $default  reduce using rule 15 (commandWrapper)


State 26

   16 commandWrapper: rollbackStmt .

    $default  reduce using rule 16 (commandWrapper)


State 27

    8 commandWrapper: dropTableStmt .

    $default  reduce using rule 8 (commandWrapper)


State 28

    9 commandWrapper: showTablesStmt .

    $default  reduce using rule 9 (commandWrapper)


State 29

   10 commandWrapper: descTableStmt .

    $default  reduce using rule 10 (commandWrapper)


State 30

   11 commandWrapper: createIndexStmt .

    $default  reduce using rule 11 (commandWrapper)


State 31

   12 commandWrapper: dropIndexStmt .

    $default  reduce using rule 12 (commandWrapper)


State 32

    7 commandWrapper: createTableStmt .

    $default  reduce using rule 7 (commandWrapper)


State 33

    4 commandWrapper: insertStmt .

    $default  reduce using rule 4 (commandWrapper)


State 34

    6 commandWrapper: deleteStmt .

    $default  reduce using rule 6 (commandWrapper)


State 35

    5 commandWrapper: updateStmt .

    $default  reduce using rule 5 (commandWrapper)


State 36

    3 commandWrapper: selectStmt .

    $default  reduce using rule 3 (commandWrapper)


State 37

    2 commandWrapper: calcStmt .

    $default  reduce using rule 2 (commandWrapper)


State 38

   17 commandWrapper: loadDataStmt .

    $default  reduce using rule 17 (commandWrapper)


State 39

   18 commandWrapper: explainStmt .

    $default  reduce using rule 18 (commandWrapper)


State 40

   19 commandWrapper: setVariableStmt .

    $default  reduce using rule 19 (commandWrapper)


State 41

   35 createTableStmt: CREATE TABLE . ID LBRACE attrDef attrDefList RBRACE
   36                | CREATE TABLE . ID LBRACE attrDef attrDefList RBRACE asOption selectStmt
   37                | CREATE TABLE . ID asOption selectStmt

    ID  shift, and go to state 82


State 42

   32 createIndexStmt: CREATE INDEX . ID ON ID LBRACE ID RBRACE
   33                | CREATE INDEX . ID ON ID LBRACE ID RBRACE USING HASH

    ID  shift, and go to state 83


State 43

   28 dropTableStmt: DROP TABLE . IF EXISTS ID
   29              | DROP TABLE . ID

    IF  shift, and go to state 84
    ID  shift, and go to state 85


State 44

   34 dropIndexStmt: DROP INDEX . ID ON ID

    ID  shift, and go to state 86


State 45

   88 expression: LBRACE . expressionList RBRACE
  115 subQueryExpr: LBRACE . selectStmt RBRACE

    SELECT         shift, and go to state 4
    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value           go to state 59
    selectStmt      go to state 87
    expressionList  go to state 88
    expression      go to state 61
    aggrFuncType    go to state 62
    aggrFuncExpr    go to state 63
    relAttr         go to state 64
    subQueryExpr    go to state 65


State 46

   73 value: NULL_T .

    $default  reduce using rule 73 (value)


State 47

   94 aggrFuncType: AGGR_MAX .

    $default  reduce using rule 94 (aggrFuncType)


State 48

   95 aggrFuncType: AGGR_MIN .

    $default  reduce using rule 95 (aggrFuncType)


State 49

   96 aggrFuncType: AGGR_SUM .

    $default  reduce using rule 96 (aggrFuncType)


State 50

   97 aggrFuncType: AGGR_AVG .

    $default  reduce using rule 97 (aggrFuncType)


State 51

   98 aggrFuncType: AGGR_COUNT .

    $default  reduce using rule 98 (aggrFuncType)


State 52

   68 value: NUMBER .

    $default  reduce using rule 68 (value)


State 53

   69 value: FLOAT .

    $default  reduce using rule 69 (value)


State 54

  105 relAttr: ID .
  106        | ID . DOT ID

    DOT  shift, and go to state 89

    $default  reduce using rule 105 (relAttr)


State 55

   71 value: DATE_TIME_STR .

    $default  reduce using rule 71 (value)


State 56

   70 value: DATE_STR .

    $default  reduce using rule 70 (value)


State 57

   72 value: SSS .

    $default  reduce using rule 72 (value)


State 58

   89 expression: '-' . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 90
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 59

   90 expression: value .

    $default  reduce using rule 90 (expression)


State 60

   81 calcStmt: CALC expressionList .

    $default  reduce using rule 81 (calcStmt)


State 61

   82 expressionList: expression . alias
   83               | expression . alias COMMA expressionList
   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression

    AS   shift, and go to state 91
    ID   shift, and go to state 92
    '+'  shift, and go to state 93
    '-'  shift, and go to state 94
    '*'  shift, and go to state 95
    '/'  shift, and go to state 96

    $default  reduce using rule 109 (alias)

    alias  go to state 97


State 62

   99 aggrFuncExpr: aggrFuncType . LBRACE expression RBRACE
  100             | aggrFuncType . LBRACE '*' RBRACE

    LBRACE  shift, and go to state 98


State 63

   92 expression: aggrFuncExpr .

    $default  reduce using rule 92 (expression)


State 64

   91 expression: relAttr .

    $default  reduce using rule 91 (expression)


State 65

   93 expression: subQueryExpr .

    $default  reduce using rule 93 (expression)


State 66

   80 selectStmt: SELECT DISTINCT . selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 67
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58
    '*'            shift, and go to state 68

    value           go to state 59
    expressionList  go to state 69
    expression      go to state 61
    aggrFuncType    go to state 62
    aggrFuncExpr    go to state 63
    selectAttr      go to state 99
    relAttr         go to state 64
    subQueryExpr    go to state 65


State 67

  103 selectAttr: ID . DOT '*'
  105 relAttr: ID .
  106        | ID . DOT ID

    DOT  shift, and go to state 100

    $default  reduce using rule 105 (relAttr)


State 68

  101 selectAttr: '*' .
  102           | '*' . DOT '*'

    DOT  shift, and go to state 101

    $default  reduce using rule 101 (selectAttr)


State 69

  104 selectAttr: expressionList .

    $default  reduce using rule 104 (selectAttr)


State 70

   79 selectStmt: SELECT selectAttr . FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit

    FROM  shift, and go to state 102


State 71

   31 descTableStmt: DESC ID .

    $default  reduce using rule 31 (descTableStmt)


State 72

   30 showTablesStmt: SHOW TABLES .

    $default  reduce using rule 30 (showTablesStmt)


State 73

   58 insertStmt: INSERT INTO . ID insertColList VALUES insertValue insertValueList

    ID  shift, and go to state 103


State 74

   74 deleteStmt: DELETE FROM . ID where

    ID  shift, and go to state 104


State 75

   75 updateStmt: UPDATE ID . SET updateKv updateKvList where

    SET  shift, and go to state 105


State 76

  151 setVariableStmt: SET ID . EQ value

    EQ  shift, and go to state 106


State 77

  149 loadDataStmt: LOAD DATA . INFILE SSS INTO TABLE ID

    INFILE  shift, and go to state 107


State 78

  150 explainStmt: EXPLAIN commandWrapper .

    $default  reduce using rule 150 (explainStmt)


State 79

    0 $accept: commands $end .

    $default  accept


State 80

  153 optSemicolon: SEMICOLON .

    $default  reduce using rule 153 (optSemicolon)


State 81

    1 commands: commandWrapper optSemicolon .

    $default  reduce using rule 1 (commands)


State 82

   35 createTableStmt: CREATE TABLE ID . LBRACE attrDef attrDefList RBRACE
   36                | CREATE TABLE ID . LBRACE attrDef attrDefList RBRACE asOption selectStmt
   37                | CREATE TABLE ID . asOption selectStmt

    LBRACE  shift, and go to state 108
    AS      shift, and go to state 109

    $default  reduce using rule 48 (asOption)

    asOption  go to state 110


State 83

   32 createIndexStmt: CREATE INDEX ID . ON ID LBRACE ID RBRACE
   33                | CREATE INDEX ID . ON ID LBRACE ID RBRACE USING HASH

    ON  shift, and go to state 111


State 84

   28 dropTableStmt: DROP TABLE IF . EXISTS ID

    EXISTS  shift, and go to state 112


State 85

   29 dropTableStmt: DROP TABLE ID .

    $default  reduce using rule 29 (dropTableStmt)


State 86

   34 dropIndexStmt: DROP INDEX ID . ON ID

    ON  shift, and go to state 113


State 87

  115 subQueryExpr: LBRACE selectStmt . RBRACE

    RBRACE  shift, and go to state 114


State 88

   88 expression: LBRACE expressionList . RBRACE

    RBRACE  shift, and go to state 115


State 89

  106 relAttr: ID DOT . ID

    ID  shift, and go to state 116


State 90

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
   89           | '-' expression .

    $default  reduce using rule 89 (expression)


State 91

  111 alias: AS . ID

    ID  shift, and go to state 117


State 92

  110 alias: ID .

    $default  reduce using rule 110 (alias)


State 93

   84 expression: expression '+' . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 118
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 94

   85 expression: expression '-' . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 119
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 95

   86 expression: expression '*' . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 120
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 96

   87 expression: expression '/' . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 121
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 97

   82 expressionList: expression alias .
   83               | expression alias . COMMA expressionList

    COMMA  shift, and go to state 122

    $default  reduce using rule 82 (expressionList)


State 98

   99 aggrFuncExpr: aggrFuncType LBRACE . expression RBRACE
  100             | aggrFuncType LBRACE . '*' RBRACE

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58
    '*'            shift, and go to state 123

    value         go to state 59
    expression    go to state 124
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 99

   80 selectStmt: SELECT DISTINCT selectAttr . FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit

    FROM  shift, and go to state 125


State 100

  103 selectAttr: ID DOT . '*'
  106 relAttr: ID DOT . ID

    ID   shift, and go to state 116
    '*'  shift, and go to state 126


State 101

  102 selectAttr: '*' DOT . '*'

    '*'  shift, and go to state 127


State 102

   79 selectStmt: SELECT selectAttr FROM . fromNode fromList where optGroupBy optHaving optOrderBy optLimit

    ID  shift, and go to state 128

    fromNode  go to state 129


State 103

   58 insertStmt: INSERT INTO ID . insertColList VALUES insertValue insertValueList

    LBRACE  shift, and go to state 130

    $default  reduce using rule 59 (insertColList)

    insertColList  go to state 131


State 104

   74 deleteStmt: DELETE FROM ID . where

    WHERE  shift, and go to state 132

    $default  reduce using rule 116 (where)

    where  go to state 133


State 105

   75 updateStmt: UPDATE ID SET . updateKv updateKvList where

    ID  shift, and go to state 134

    updateKv  go to state 135


State 106

  151 setVariableStmt: SET ID EQ . value

    NULL_T         shift, and go to state 46
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57

    value  go to state 136


State 107

  149 loadDataStmt: LOAD DATA INFILE . SSS INTO TABLE ID

    SSS  shift, and go to state 137


State 108

   35 createTableStmt: CREATE TABLE ID LBRACE . attrDef attrDefList RBRACE
   36                | CREATE TABLE ID LBRACE . attrDef attrDefList RBRACE asOption selectStmt

    ID  shift, and go to state 138

    attrDef  go to state 139


State 109

   49 asOption: AS .

    $default  reduce using rule 49 (asOption)


State 110

   37 createTableStmt: CREATE TABLE ID asOption . selectStmt

    SELECT  shift, and go to state 4

    selectStmt  go to state 140


State 111

   32 createIndexStmt: CREATE INDEX ID ON . ID LBRACE ID RBRACE
   33                | CREATE INDEX ID ON . ID LBRACE ID RBRACE USING HASH

    ID  shift, and go to state 141


State 112

   28 dropTableStmt: DROP TABLE IF EXISTS . ID

    ID  shift, and go to state 142


State 113

   34 dropIndexStmt: DROP INDEX ID ON . ID

    ID  shift, and go to state 143


State 114

  115 subQueryExpr: LBRACE selectStmt RBRACE .

    $default  reduce using rule 115 (subQueryExpr)


State 115

   88 expression: LBRACE expressionList RBRACE .

    $default  reduce using rule 88 (expression)


State 116

  106 relAttr: ID DOT ID .

    $default  reduce using rule 106 (relAttr)


State 117

  111 alias: AS ID .

    $default  reduce using rule 111 (alias)


State 118

   84 expression: expression . '+' expression
   84           | expression '+' expression .
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression

    '*'  shift, and go to state 95
    '/'  shift, and go to state 96

    $default  reduce using rule 84 (expression)


State 119

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   85           | expression '-' expression .
   86           | expression . '*' expression
   87           | expression . '/' expression

    '*'  shift, and go to state 95
    '/'  shift, and go to state 96

    $default  reduce using rule 85 (expression)


State 120

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   86           | expression '*' expression .
   87           | expression . '/' expression

    $default  reduce using rule 86 (expression)


State 121

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
   87           | expression '/' expression .

    $default  reduce using rule 87 (expression)


State 122

   83 expressionList: expression alias COMMA . expressionList

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value           go to state 59
    expressionList  go to state 144
    expression      go to state 61
    aggrFuncType    go to state 62
    aggrFuncExpr    go to state 63
    relAttr         go to state 64
    subQueryExpr    go to state 65


State 123

  100 aggrFuncExpr: aggrFuncType LBRACE '*' . RBRACE

    RBRACE  shift, and go to state 145


State 124

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
   99 aggrFuncExpr: aggrFuncType LBRACE expression . RBRACE

    RBRACE  shift, and go to state 146
    '+'     shift, and go to state 93
    '-'     shift, and go to state 94
    '*'     shift, and go to state 95
    '/'     shift, and go to state 96


State 125

   80 selectStmt: SELECT DISTINCT selectAttr FROM . fromNode fromList where optGroupBy optHaving optOrderBy optLimit

    ID  shift, and go to state 128

    fromNode  go to state 147


State 126

  103 selectAttr: ID DOT '*' .

    $default  reduce using rule 103 (selectAttr)


State 127

  102 selectAttr: '*' DOT '*' .

    $default  reduce using rule 102 (selectAttr)


State 128

  112 fromNode: ID . alias joinList

    AS  shift, and go to state 91
    ID  shift, and go to state 92

    $default  reduce using rule 109 (alias)

    alias  go to state 148


State 129

   79 selectStmt: SELECT selectAttr FROM fromNode . fromList where optGroupBy optHaving optOrderBy optLimit

    COMMA  shift, and go to state 149

    $default  reduce using rule 107 (fromList)

    fromList  go to state 150


State 130

   60 insertColList: LBRACE . ID idxColList RBRACE

    ID  shift, and go to state 151


State 131

   58 insertStmt: INSERT INTO ID insertColList . VALUES insertValue insertValueList

    VALUES  shift, and go to state 152


State 132

  117 where: WHERE . condition

    LBRACE         shift, and go to state 153
    NOT            shift, and go to state 154
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    EXISTS         shift, and go to state 155
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 156
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65
    condition     go to state 157
    existsOp      go to state 158


State 133

   74 deleteStmt: DELETE FROM ID where .

    $default  reduce using rule 74 (deleteStmt)


State 134

   78 updateKv: ID . EQ expression

    EQ  shift, and go to state 159


State 135

   75 updateStmt: UPDATE ID SET updateKv . updateKvList where

    COMMA  shift, and go to state 160

    $default  reduce using rule 76 (updateKvList)

    updateKvList  go to state 161


State 136

  151 setVariableStmt: SET ID EQ value .

    $default  reduce using rule 151 (setVariableStmt)


State 137

  149 loadDataStmt: LOAD DATA INFILE SSS . INTO TABLE ID

    INTO  shift, and go to state 162


State 138

   40 attrDef: ID . type LBRACE number RBRACE
   41        | ID . type LBRACE number RBRACE PRIMARY KEY
   42        | ID . type
   43        | ID . type PRIMARY KEY
   44        | ID . type LBRACE number RBRACE NOT NULL_T
   45        | ID . type LBRACE number RBRACE PRIMARY KEY NOT NULL_T
   46        | ID . type NOT NULL_T
   47        | ID . type PRIMARY KEY NOT NULL_T

    INT_T       shift, and go to state 163
    BLOB_T      shift, and go to state 164
    DATE_T      shift, and go to state 165
    DATETIME_T  shift, and go to state 166
    TEXT_T      shift, and go to state 167
    STRING_T    shift, and go to state 168
    FLOAT_T     shift, and go to state 169

    type  go to state 170


State 139

   35 createTableStmt: CREATE TABLE ID LBRACE attrDef . attrDefList RBRACE
   36                | CREATE TABLE ID LBRACE attrDef . attrDefList RBRACE asOption selectStmt

    COMMA  shift, and go to state 171

    $default  reduce using rule 38 (attrDefList)

    attrDefList  go to state 172


State 140

   37 createTableStmt: CREATE TABLE ID asOption selectStmt .

    $default  reduce using rule 37 (createTableStmt)


State 141

   32 createIndexStmt: CREATE INDEX ID ON ID . LBRACE ID RBRACE
   33                | CREATE INDEX ID ON ID . LBRACE ID RBRACE USING HASH

    LBRACE  shift, and go to state 173


State 142

   28 dropTableStmt: DROP TABLE IF EXISTS ID .

    $default  reduce using rule 28 (dropTableStmt)


State 143

   34 dropIndexStmt: DROP INDEX ID ON ID .

    $default  reduce using rule 34 (dropIndexStmt)


State 144

   83 expressionList: expression alias COMMA expressionList .

    $default  reduce using rule 83 (expressionList)


State 145

  100 aggrFuncExpr: aggrFuncType LBRACE '*' RBRACE .

    $default  reduce using rule 100 (aggrFuncExpr)


State 146

   99 aggrFuncExpr: aggrFuncType LBRACE expression RBRACE .

    $default  reduce using rule 99 (aggrFuncExpr)


State 147

   80 selectStmt: SELECT DISTINCT selectAttr FROM fromNode . fromList where optGroupBy optHaving optOrderBy optLimit

    COMMA  shift, and go to state 149

    $default  reduce using rule 107 (fromList)

    fromList  go to state 174


State 148

  112 fromNode: ID alias . joinList

    INNER  shift, and go to state 175

    $default  reduce using rule 113 (joinList)

    joinList  go to state 176


State 149

  108 fromList: COMMA . fromNode fromList

    ID  shift, and go to state 128

    fromNode  go to state 177


State 150

   79 selectStmt: SELECT selectAttr FROM fromNode fromList . where optGroupBy optHaving optOrderBy optLimit

    WHERE  shift, and go to state 132

    $default  reduce using rule 116 (where)

    where  go to state 178


State 151

   60 insertColList: LBRACE ID . idxColList RBRACE

    COMMA  shift, and go to state 179

    $default  reduce using rule 61 (idxColList)

    idxColList  go to state 180


State 152

   58 insertStmt: INSERT INTO ID insertColList VALUES . insertValue insertValueList

    LBRACE  shift, and go to state 181

    insertValue  go to state 182


State 153

   88 expression: LBRACE . expressionList RBRACE
  115 subQueryExpr: LBRACE . selectStmt RBRACE
  118 condition: LBRACE . condition RBRACE

    SELECT         shift, and go to state 4
    LBRACE         shift, and go to state 153
    NOT            shift, and go to state 154
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    EXISTS         shift, and go to state 155
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value           go to state 59
    selectStmt      go to state 87
    expressionList  go to state 88
    expression      go to state 183
    aggrFuncType    go to state 62
    aggrFuncExpr    go to state 63
    relAttr         go to state 64
    subQueryExpr    go to state 65
    condition       go to state 184
    existsOp        go to state 158


State 154

  148 existsOp: NOT . EXISTS

    EXISTS  shift, and go to state 185


State 155

  147 existsOp: EXISTS .

    $default  reduce using rule 147 (existsOp)


State 156

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
  119 condition: expression . compOp expression

    NOT   shift, and go to state 186
    LIKE  shift, and go to state 187
    EQ    shift, and go to state 188
    LT    shift, and go to state 189
    GT    shift, and go to state 190
    LE    shift, and go to state 191
    GE    shift, and go to state 192
    NE    shift, and go to state 193
    IN    shift, and go to state 194
    '+'   shift, and go to state 93
    '-'   shift, and go to state 94
    '*'   shift, and go to state 95
    '/'   shift, and go to state 96

    compOp  go to state 195


State 157

  117 where: WHERE condition .
  121 condition: condition . AND condition
  122          | condition . OR condition

    AND  shift, and go to state 196
    OR   shift, and go to state 197

    $default  reduce using rule 117 (where)


State 158

  120 condition: existsOp . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 198
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 159

   78 updateKv: ID EQ . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 199
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 160

   77 updateKvList: COMMA . updateKv updateKvList

    ID  shift, and go to state 134

    updateKv  go to state 200


State 161

   75 updateStmt: UPDATE ID SET updateKv updateKvList . where

    WHERE  shift, and go to state 132

    $default  reduce using rule 116 (where)

    where  go to state 201


State 162

  149 loadDataStmt: LOAD DATA INFILE SSS INTO . TABLE ID

    TABLE  shift, and go to state 202


State 163

   51 type: INT_T .

    $default  reduce using rule 51 (type)


State 164

   57 type: BLOB_T .

    $default  reduce using rule 57 (type)


State 165

   52 type: DATE_T .

    $default  reduce using rule 52 (type)


State 166

   55 type: DATETIME_T .

    $default  reduce using rule 55 (type)


State 167

   56 type: TEXT_T .

    $default  reduce using rule 56 (type)


State 168

   53 type: STRING_T .

    $default  reduce using rule 53 (type)


State 169

   54 type: FLOAT_T .

    $default  reduce using rule 54 (type)


State 170

   40 attrDef: ID type . LBRACE number RBRACE
   41        | ID type . LBRACE number RBRACE PRIMARY KEY
   42        | ID type .
   43        | ID type . PRIMARY KEY
   44        | ID type . LBRACE number RBRACE NOT NULL_T
   45        | ID type . LBRACE number RBRACE PRIMARY KEY NOT NULL_T
   46        | ID type . NOT NULL_T
   47        | ID type . PRIMARY KEY NOT NULL_T

    LBRACE   shift, and go to state 203
    PRIMARY  shift, and go to state 204
    NOT      shift, and go to state 205

    $default  reduce using rule 42 (attrDef)


State 171

   39 attrDefList: COMMA . attrDef attrDefList

    ID  shift, and go to state 138

    attrDef  go to state 206


State 172

   35 createTableStmt: CREATE TABLE ID LBRACE attrDef attrDefList . RBRACE
   36                | CREATE TABLE ID LBRACE attrDef attrDefList . RBRACE asOption selectStmt

    RBRACE  shift, and go to state 207


State 173

   32 createIndexStmt: CREATE INDEX ID ON ID LBRACE . ID RBRACE
   33                | CREATE INDEX ID ON ID LBRACE . ID RBRACE USING HASH

    ID  shift, and go to state 208


State 174

   80 selectStmt: SELECT DISTINCT selectAttr FROM fromNode fromList . where optGroupBy optHaving optOrderBy optLimit

    WHERE  shift, and go to state 132

    $default  reduce using rule 116 (where)

    where  go to state 209


State 175

  114 joinList: INNER . JOIN ID alias ON condition joinList

    JOIN  shift, and go to state 210


State 176

  112 fromNode: ID alias joinList .

    $default  reduce using rule 112 (fromNode)


State 177

  108 fromList: COMMA fromNode . fromList

    COMMA  shift, and go to state 149

    $default  reduce using rule 107 (fromList)

    fromList  go to state 211


State 178

   79 selectStmt: SELECT selectAttr FROM fromNode fromList where . optGroupBy optHaving optOrderBy optLimit

    GROUP  shift, and go to state 212

    $default  reduce using rule 130 (optGroupBy)

    optGroupBy  go to state 213


State 179

   62 idxColList: COMMA . ID idxColList

    ID  shift, and go to state 214


State 180

   60 insertColList: LBRACE ID idxColList . RBRACE

    RBRACE  shift, and go to state 215


State 181

   65 insertValue: LBRACE . expression valueList RBRACE

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 216
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 182

   58 insertStmt: INSERT INTO ID insertColList VALUES insertValue . insertValueList

    COMMA  shift, and go to state 217

    $default  reduce using rule 63 (insertValueList)

    insertValueList  go to state 218


State 183

   82 expressionList: expression . alias
   83               | expression . alias COMMA expressionList
   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
  119 condition: expression . compOp expression

    NOT   shift, and go to state 186
    LIKE  shift, and go to state 187
    EQ    shift, and go to state 188
    LT    shift, and go to state 189
    GT    shift, and go to state 190
    LE    shift, and go to state 191
    GE    shift, and go to state 192
    NE    shift, and go to state 193
    AS    shift, and go to state 91
    IN    shift, and go to state 194
    ID    shift, and go to state 92
    '+'   shift, and go to state 93
    '-'   shift, and go to state 94
    '*'   shift, and go to state 95
    '/'   shift, and go to state 96

    $default  reduce using rule 109 (alias)

    alias   go to state 97
    compOp  go to state 195


State 184

  118 condition: LBRACE condition . RBRACE
  121          | condition . AND condition
  122          | condition . OR condition

    RBRACE  shift, and go to state 219
    AND     shift, and go to state 196
    OR      shift, and go to state 197


State 185

  148 existsOp: NOT EXISTS .

    $default  reduce using rule 148 (existsOp)


State 186

  144 compOp: NOT . LIKE
  146       | NOT . IN

    LIKE  shift, and go to state 220
    IN    shift, and go to state 221


State 187

  143 compOp: LIKE .

    $default  reduce using rule 143 (compOp)


State 188

  137 compOp: EQ .

    $default  reduce using rule 137 (compOp)


State 189

  138 compOp: LT .

    $default  reduce using rule 138 (compOp)


State 190

  139 compOp: GT .

    $default  reduce using rule 139 (compOp)


State 191

  140 compOp: LE .

    $default  reduce using rule 140 (compOp)


State 192

  141 compOp: GE .

    $default  reduce using rule 141 (compOp)


State 193

  142 compOp: NE .

    $default  reduce using rule 142 (compOp)


State 194

  145 compOp: IN .

    $default  reduce using rule 145 (compOp)


State 195

  119 condition: expression compOp . expression

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 222
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 196

  121 condition: condition AND . condition

    LBRACE         shift, and go to state 153
    NOT            shift, and go to state 154
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    EXISTS         shift, and go to state 155
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 156
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65
    condition     go to state 223
    existsOp      go to state 158


State 197

  122 condition: condition OR . condition

    LBRACE         shift, and go to state 153
    NOT            shift, and go to state 154
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    EXISTS         shift, and go to state 155
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 156
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65
    condition     go to state 224
    existsOp      go to state 158


State 198

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
  120 condition: existsOp expression .

    '+'  shift, and go to state 93
    '-'  shift, and go to state 94
    '*'  shift, and go to state 95
    '/'  shift, and go to state 96

    $default  reduce using rule 120 (condition)


State 199

   78 updateKv: ID EQ expression .
   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression

    '+'  shift, and go to state 93
    '-'  shift, and go to state 94
    '*'  shift, and go to state 95
    '/'  shift, and go to state 96

    $default  reduce using rule 78 (updateKv)


State 200

   77 updateKvList: COMMA updateKv . updateKvList

    COMMA  shift, and go to state 160

    $default  reduce using rule 76 (updateKvList)

    updateKvList  go to state 225


State 201

   75 updateStmt: UPDATE ID SET updateKv updateKvList where .

    $default  reduce using rule 75 (updateStmt)


State 202

  149 loadDataStmt: LOAD DATA INFILE SSS INTO TABLE . ID

    ID  shift, and go to state 226


State 203

   40 attrDef: ID type LBRACE . number RBRACE
   41        | ID type LBRACE . number RBRACE PRIMARY KEY
   44        | ID type LBRACE . number RBRACE NOT NULL_T
   45        | ID type LBRACE . number RBRACE PRIMARY KEY NOT NULL_T

    NUMBER  shift, and go to state 227

    number  go to state 228


State 204

   43 attrDef: ID type PRIMARY . KEY
   47        | ID type PRIMARY . KEY NOT NULL_T

    KEY  shift, and go to state 229


State 205

   46 attrDef: ID type NOT . NULL_T

    NULL_T  shift, and go to state 230


State 206

   39 attrDefList: COMMA attrDef . attrDefList

    COMMA  shift, and go to state 171

    $default  reduce using rule 38 (attrDefList)

    attrDefList  go to state 231


State 207

   35 createTableStmt: CREATE TABLE ID LBRACE attrDef attrDefList RBRACE .
   36                | CREATE TABLE ID LBRACE attrDef attrDefList RBRACE . asOption selectStmt

    AS  shift, and go to state 109

    SELECT    reduce using rule 48 (asOption)
    $default  reduce using rule 35 (createTableStmt)

    asOption  go to state 232


State 208

   32 createIndexStmt: CREATE INDEX ID ON ID LBRACE ID . RBRACE
   33                | CREATE INDEX ID ON ID LBRACE ID . RBRACE USING HASH

    RBRACE  shift, and go to state 233


State 209

   80 selectStmt: SELECT DISTINCT selectAttr FROM fromNode fromList where . optGroupBy optHaving optOrderBy optLimit

    GROUP  shift, and go to state 212

    $default  reduce using rule 130 (optGroupBy)

    optGroupBy  go to state 234


State 210

  114 joinList: INNER JOIN . ID alias ON condition joinList

    ID  shift, and go to state 235


State 211

  108 fromList: COMMA fromNode fromList .

    $default  reduce using rule 108 (fromList)


State 212

  131 optGroupBy: GROUP . BY expressionList

    BY  shift, and go to state 236


State 213

   79 selectStmt: SELECT selectAttr FROM fromNode fromList where optGroupBy . optHaving optOrderBy optLimit

    HAVING  shift, and go to state 237

    $default  reduce using rule 132 (optHaving)

    optHaving  go to state 238


State 214

   62 idxColList: COMMA ID . idxColList

    COMMA  shift, and go to state 179

    $default  reduce using rule 61 (idxColList)

    idxColList  go to state 239


State 215

   60 insertColList: LBRACE ID idxColList RBRACE .

    $default  reduce using rule 60 (insertColList)


State 216

   65 insertValue: LBRACE expression . valueList RBRACE
   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression

    COMMA  shift, and go to state 240
    '+'    shift, and go to state 93
    '-'    shift, and go to state 94
    '*'    shift, and go to state 95
    '/'    shift, and go to state 96

    $default  reduce using rule 66 (valueList)

    valueList  go to state 241


State 217

   64 insertValueList: COMMA . insertValue insertValueList

    LBRACE  shift, and go to state 181

    insertValue  go to state 242


State 218

   58 insertStmt: INSERT INTO ID insertColList VALUES insertValue insertValueList .

    $default  reduce using rule 58 (insertStmt)


State 219

  118 condition: LBRACE condition RBRACE .

    $default  reduce using rule 118 (condition)


State 220

  144 compOp: NOT LIKE .

    $default  reduce using rule 144 (compOp)


State 221

  146 compOp: NOT IN .

    $default  reduce using rule 146 (compOp)


State 222

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
  119 condition: expression compOp expression .

    '+'  shift, and go to state 93
    '-'  shift, and go to state 94
    '*'  shift, and go to state 95
    '/'  shift, and go to state 96

    $default  reduce using rule 119 (condition)


State 223

  121 condition: condition . AND condition
  121          | condition AND condition .
  122          | condition . OR condition

    $default  reduce using rule 121 (condition)


State 224

  121 condition: condition . AND condition
  122          | condition . OR condition
  122          | condition OR condition .

    AND  shift, and go to state 196

    $default  reduce using rule 122 (condition)


State 225

   77 updateKvList: COMMA updateKv updateKvList .

    $default  reduce using rule 77 (updateKvList)


State 226

  149 loadDataStmt: LOAD DATA INFILE SSS INTO TABLE ID .

    $default  reduce using rule 149 (loadDataStmt)


State 227

   50 number: NUMBER .

    $default  reduce using rule 50 (number)


State 228

   40 attrDef: ID type LBRACE number . RBRACE
   41        | ID type LBRACE number . RBRACE PRIMARY KEY
   44        | ID type LBRACE number . RBRACE NOT NULL_T
   45        | ID type LBRACE number . RBRACE PRIMARY KEY NOT NULL_T

    RBRACE  shift, and go to state 243


State 229

   43 attrDef: ID type PRIMARY KEY .
   47        | ID type PRIMARY KEY . NOT NULL_T

    NOT  shift, and go to state 244

    $default  reduce using rule 43 (attrDef)


State 230

   46 attrDef: ID type NOT NULL_T .

    $default  reduce using rule 46 (attrDef)


State 231

   39 attrDefList: COMMA attrDef attrDefList .

    $default  reduce using rule 39 (attrDefList)


State 232

   36 createTableStmt: CREATE TABLE ID LBRACE attrDef attrDefList RBRACE asOption . selectStmt

    SELECT  shift, and go to state 4

    selectStmt  go to state 245


State 233

   32 createIndexStmt: CREATE INDEX ID ON ID LBRACE ID RBRACE .
   33                | CREATE INDEX ID ON ID LBRACE ID RBRACE . USING HASH

    USING  shift, and go to state 246

    $default  reduce using rule 32 (createIndexStmt)


State 234

   80 selectStmt: SELECT DISTINCT selectAttr FROM fromNode fromList where optGroupBy . optHaving optOrderBy optLimit

    HAVING  shift, and go to state 237

    $default  reduce using rule 132 (optHaving)

    optHaving  go to state 247


State 235

  114 joinList: INNER JOIN ID . alias ON condition joinList

    AS  shift, and go to state 91
    ID  shift, and go to state 92

    $default  reduce using rule 109 (alias)

    alias  go to state 248


State 236

  131 optGroupBy: GROUP BY . expressionList

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value           go to state 59
    expressionList  go to state 249
    expression      go to state 61
    aggrFuncType    go to state 62
    aggrFuncExpr    go to state 63
    relAttr         go to state 64
    subQueryExpr    go to state 65


State 237

  133 optHaving: HAVING . condition

    LBRACE         shift, and go to state 153
    NOT            shift, and go to state 154
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    EXISTS         shift, and go to state 155
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 156
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65
    condition     go to state 250
    existsOp      go to state 158


State 238

   79 selectStmt: SELECT selectAttr FROM fromNode fromList where optGroupBy optHaving . optOrderBy optLimit

    ORDER_T  shift, and go to state 251

    $default  reduce using rule 128 (optOrderBy)

    optOrderBy  go to state 252


State 239

   62 idxColList: COMMA ID idxColList .

    $default  reduce using rule 62 (idxColList)


State 240

   67 valueList: COMMA . expression valueList

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 253
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65


State 241

   65 insertValue: LBRACE expression valueList . RBRACE

    RBRACE  shift, and go to state 254


State 242

   64 insertValueList: COMMA insertValue . insertValueList

    COMMA  shift, and go to state 217

    $default  reduce using rule 63 (insertValueList)

    insertValueList  go to state 255


State 243

   40 attrDef: ID type LBRACE number RBRACE .
   41        | ID type LBRACE number RBRACE . PRIMARY KEY
   44        | ID type LBRACE number RBRACE . NOT NULL_T
   45        | ID type LBRACE number RBRACE . PRIMARY KEY NOT NULL_T

    PRIMARY  shift, and go to state 256
    NOT      shift, and go to state 257

    $default  reduce using rule 40 (attrDef)


State 244

   47 attrDef: ID type PRIMARY KEY NOT . NULL_T

    NULL_T  shift, and go to state 258


State 245

   36 createTableStmt: CREATE TABLE ID LBRACE attrDef attrDefList RBRACE asOption selectStmt .

    $default  reduce using rule 36 (createTableStmt)


State 246

   33 createIndexStmt: CREATE INDEX ID ON ID LBRACE ID RBRACE USING . HASH

    HASH  shift, and go to state 259


State 247

   80 selectStmt: SELECT DISTINCT selectAttr FROM fromNode fromList where optGroupBy optHaving . optOrderBy optLimit

    ORDER_T  shift, and go to state 251

    $default  reduce using rule 128 (optOrderBy)

    optOrderBy  go to state 260


State 248

  114 joinList: INNER JOIN ID alias . ON condition joinList

    ON  shift, and go to state 261


State 249

  131 optGroupBy: GROUP BY expressionList .

    $default  reduce using rule 131 (optGroupBy)


State 250

  121 condition: condition . AND condition
  122          | condition . OR condition
  133 optHaving: HAVING condition .

    AND  shift, and go to state 196
    OR   shift, and go to state 197

    $default  reduce using rule 133 (optHaving)


State 251

  129 optOrderBy: ORDER_T . BY sortList

    BY  shift, and go to state 262


State 252

   79 selectStmt: SELECT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy . optLimit

    LIMIT  shift, and go to state 263

    $default  reduce using rule 136 (optLimit)

    optLimit  go to state 264


State 253

   67 valueList: COMMA expression . valueList
   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression

    COMMA  shift, and go to state 240
    '+'    shift, and go to state 93
    '-'    shift, and go to state 94
    '*'    shift, and go to state 95
    '/'    shift, and go to state 96

    $default  reduce using rule 66 (valueList)

    valueList  go to state 265


State 254

   65 insertValue: LBRACE expression valueList RBRACE .

    $default  reduce using rule 65 (insertValue)


State 255

   64 insertValueList: COMMA insertValue insertValueList .

    $default  reduce using rule 64 (insertValueList)


State 256

   41 attrDef: ID type LBRACE number RBRACE PRIMARY . KEY
   45        | ID type LBRACE number RBRACE PRIMARY . KEY NOT NULL_T

    KEY  shift, and go to state 266


State 257

   44 attrDef: ID type LBRACE number RBRACE NOT . NULL_T

    NULL_T  shift, and go to state 267


State 258

   47 attrDef: ID type PRIMARY KEY NOT NULL_T .

    $default  reduce using rule 47 (attrDef)


State 259

   33 createIndexStmt: CREATE INDEX ID ON ID LBRACE ID RBRACE USING HASH .

    $default  reduce using rule 33 (createIndexStmt)


State 260

   80 selectStmt: SELECT DISTINCT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy . optLimit

    LIMIT  shift, and go to state 263

    $default  reduce using rule 136 (optLimit)

    optLimit  go to state 268


State 261

  114 joinList: INNER JOIN ID alias ON . condition joinList

    LBRACE         shift, and go to state 153
    NOT            shift, and go to state 154
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    EXISTS         shift, and go to state 155
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 156
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65
    condition     go to state 269
    existsOp      go to state 158


State 262

  129 optOrderBy: ORDER_T BY . sortList

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 270
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65
    sortUnit      go to state 271
    sortList      go to state 272


State 263

  134 optLimit: LIMIT . NUMBER OFFSET NUMBER
  135         | LIMIT . NUMBER

    NUMBER  shift, and go to state 273


State 264

   79 selectStmt: SELECT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit .

    $default  reduce using rule 79 (selectStmt)


State 265

   67 valueList: COMMA expression valueList .

    $default  reduce using rule 67 (valueList)


State 266

   41 attrDef: ID type LBRACE number RBRACE PRIMARY KEY .
   45        | ID type LBRACE number RBRACE PRIMARY KEY . NOT NULL_T

    NOT  shift, and go to state 274

    $default  reduce using rule 41 (attrDef)


State 267

   44 attrDef: ID type LBRACE number RBRACE NOT NULL_T .

    $default  reduce using rule 44 (attrDef)


State 268

   80 selectStmt: SELECT DISTINCT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit .

    $default  reduce using rule 80 (selectStmt)


State 269

  114 joinList: INNER JOIN ID alias ON condition . joinList
  121 condition: condition . AND condition
  122          | condition . OR condition

    AND    shift, and go to state 196
    OR     shift, and go to state 197
    INNER  shift, and go to state 175

    $default  reduce using rule 113 (joinList)

    joinList  go to state 275


State 270

   84 expression: expression . '+' expression
   85           | expression . '-' expression
   86           | expression . '*' expression
   87           | expression . '/' expression
  123 sortUnit: expression .
  124         | expression . DESC
  125         | expression . ASC

    DESC  shift, and go to state 276
    ASC   shift, and go to state 277
    '+'   shift, and go to state 93
    '-'   shift, and go to state 94
    '*'   shift, and go to state 95
    '/'   shift, and go to state 96

    $default  reduce using rule 123 (sortUnit)


State 271

  126 sortList: sortUnit .
  127         | sortUnit . COMMA sortList

    COMMA  shift, and go to state 278

    $default  reduce using rule 126 (sortList)


State 272

  129 optOrderBy: ORDER_T BY sortList .

    $default  reduce using rule 129 (optOrderBy)


State 273

  134 optLimit: LIMIT NUMBER . OFFSET NUMBER
  135         | LIMIT NUMBER .

    OFFSET  shift, and go to state 279

    $default  reduce using rule 135 (optLimit)


State 274

   45 attrDef: ID type LBRACE number RBRACE PRIMARY KEY NOT . NULL_T

    NULL_T  shift, and go to state 280


State 275

  114 joinList: INNER JOIN ID alias ON condition joinList .

    $default  reduce using rule 114 (joinList)


State 276

  124 sortUnit: expression DESC .

    $default  reduce using rule 124 (sortUnit)


State 277

  125 sortUnit: expression ASC .

    $default  reduce using rule 125 (sortUnit)


State 278

  127 sortList: sortUnit COMMA . sortList

    LBRACE         shift, and go to state 45
    NULL_T         shift, and go to state 46
    AGGR_MAX       shift, and go to state 47
    AGGR_MIN       shift, and go to state 48
    AGGR_SUM       shift, and go to state 49
    AGGR_AVG       shift, and go to state 50
    AGGR_COUNT     shift, and go to state 51
    NUMBER         shift, and go to state 52
    FLOAT          shift, and go to state 53
    ID             shift, and go to state 54
    DATE_TIME_STR  shift, and go to state 55
    DATE_STR       shift, and go to state 56
    SSS            shift, and go to state 57
    '-'            shift, and go to state 58

    value         go to state 59
    expression    go to state 270
    aggrFuncType  go to state 62
    aggrFuncExpr  go to state 63
    relAttr       go to state 64
    subQueryExpr  go to state 65
    sortUnit      go to state 271
    sortList      go to state 281


State 279

  134 optLimit: LIMIT NUMBER OFFSET . NUMBER

    NUMBER  shift, and go to state 282


State 280

   45 attrDef: ID type LBRACE number RBRACE PRIMARY KEY NOT NULL_T .

    $default  reduce using rule 45 (attrDef)


State 281

  127 sortList: sortUnit COMMA sortList .

    $default  reduce using rule 127 (sortList)


State 282

  134 optLimit: LIMIT NUMBER OFFSET NUMBER .

    $default  reduce using rule 134 (optLimit)
