#include "stmt.h"
int ResolveStageHandleRequest(SQLStageEvent *sqlEvent)
{
  int    rc   = GNCDB_SUCCESS;
  GNCDB *db   = NULL;
  Stmt  *stmt = NULL;

  if (sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }
  db = sqlEvent->db;
  if (NULL == db) {
    rc = GNCDB_NO_VALID;
    return rc;
  }

  rc = StmtConstruct(sqlEvent, &stmt);
  if (rc != GNCDB_SUCCESS && rc != GNCDB_UNIMPLENMENT) {
    return rc;
  }
  sqlEvent->stmt = stmt;
  return rc;
}
