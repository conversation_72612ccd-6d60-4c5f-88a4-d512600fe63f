/* A Bison parser, made by GNU Bison 3.5.1.  */

/* Bison implementation for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015, 2018-2020 Free Software Foundation,
   Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* C LALR(1) parser skeleton written by <PERSON>, by
   simplifying the original so-called "semantic" parser.  */

/* All symbols defined below should begin with yy or YY, to avoid
   infringing on user name space.  This should be done even for local
   variables, as they might otherwise be expanded by user macros.
   There are some unavoidable exceptions within include files to
   define necessary library symbols; they are noted "INFRINGES ON
   USER NAME SPACE" below.  */

/* Undocumented macros, especially those whose name start with YY_,
   are private implementation details.  Do not rely on them.  */

/* Identify Bison output.  */
#define YYBISON 1

/* Bison version.  */
#define YYBISON_VERSION "3.5.1"

/* Skeleton name.  */
#define YYSKELETON_NAME "yacc.c"

/* Pure parsers.  */
#define YYPURE 2

/* Push parsers.  */
#define YYPUSH 0

/* Pull parsers.  */
#define YYPULL 1




/* First part of user prologue.  */


#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "yacc_sql.h"
#include "lex_sql.h"
#include "parse_defs.h"
#include "expression.h"
#define YYMALLOC my_malloc0
#define YYREALLOC my_realloc
#define YYFREE my_free
Value* value = NULL;
char* str = NULL;
int date;
DateTime dateTime;
int j = 0;
UpdateKV* kv = NULL;
ExprListExpr* exprListExpr = NULL;
ValueExpr* valueExpr = NULL;
ComparisonExpr* cmpExpr = NULL;
varArrayList* children = NULL;
ConjunctionExpr* conjExpr = NULL;
StringPair *sp = NULL;
// varArrayList* joinRelations = NULL;
// varArrayList* conditions = NULL;
// StringPair* baseRelation = NULL;
FieldExpr *fieldExpr = NULL;
AggrFuncExpr *afexpr = NULL;

char *substr(const char *str, int start, int length) {
    char *result = (char *)my_malloc0(length + 1); // +1 for the null terminator
    if (result != NULL) {
        strncpy(result, str + start, length);
        result[length] = '\0'; // null-terminate the string
    }
    return result;
}

char* tokenName(const char *sqlString, YYLTYPE *llocp) {
    // 计算子串的长度
    int length = llocp->last_column - llocp->first_column + 1;

    // 分配内存以存储子串
    char *substring = (char*)my_malloc0((length + 1) * sizeof(char));

    // 复制子串到新分配的内存中
    strncpy(substring, sqlString + llocp->first_column, length);
    substring[length] = '\0'; // 在字符串末尾添加 null 终止字符

    return substring;
}

int yyerror(YYLTYPE *llocp, const char *sqlString, ParsedSqlResult *sqlResult, yyscan_t scanner, const char *msg)
{
    // 创建 errorSqlNode 对象，并分配内存
    ParsedSqlNode *errorSqlNode = ParsedSqlNodeCreate(SCF_ERROR);


    // 初始化 errorSqlNode 对象
    errorSqlNode->error->errorMsg = strdup(msg);
    errorSqlNode->error->line = llocp->first_line;
    errorSqlNode->error->column = llocp->first_column;

    // 添加 errorSqlNode 到 sqlResult 中
    varArrayListAddPointer(sqlResult->sqlNodes, errorSqlNode);
    sqlResult->count++;

    // 返回成功
    return 0;
}

ArithmeticExpr* createArithmeticExpression(ArithmeticExprType type,
                                              Expression* left,
                                              Expression* right,
                                              const char* sqlString,
                                              YYLTYPE* llocp) {
    // 分配内存以存储 ArithmeticExpr 对象
    ArithmeticExpr* expr = NULL;
    if(left != NULL &&  left->type == ETG_EXPRLIST && ((ExprListExpr*)left)->exprs->elementCount == 1){
      left = varArrayListGetPointer(((ExprListExpr*)left)->exprs, 0);
      varArrayListRemoveByIndexPointer(((ExprListExpr*)left)->exprs, 0);
      varArrayListDestroy(&(((ExprListExpr*)left)->exprs));
    }

    if(right != NULL && right->type == ETG_EXPRLIST && ((ExprListExpr*)right)->exprs->elementCount == 1){
      right = varArrayListGetPointer(((ExprListExpr*)right)->exprs, 0);
      varArrayListRemoveByIndexPointer(((ExprListExpr*)left)->exprs, 0);
      varArrayListDestroy(&(((ExprListExpr*)right)->exprs));
    }
    expr = exprCreate(ETG_ARITHMETIC);
    expr->arithmeticType = type;
    expr->left = left;
    expr->right = right;
    expr->name = tokenName(sqlString, llocp);
    return expr;
}




# ifndef YY_CAST
#  ifdef __cplusplus
#   define YY_CAST(Type, Val) static_cast<Type> (Val)
#   define YY_REINTERPRET_CAST(Type, Val) reinterpret_cast<Type> (Val)
#  else
#   define YY_CAST(Type, Val) ((Type) (Val))
#   define YY_REINTERPRET_CAST(Type, Val) ((Type) (Val))
#  endif
# endif
# ifndef YY_NULLPTR
#  if defined __cplusplus
#   if 201103L <= __cplusplus
#    define YY_NULLPTR nullptr
#   else
#    define YY_NULLPTR 0
#   endif
#  else
#   define YY_NULLPTR ((void*)0)
#  endif
# endif

/* Enabling verbose error messages.  */
#ifdef YYERROR_VERBOSE
# undef YYERROR_VERBOSE
# define YYERROR_VERBOSE 1
#else
# define YYERROR_VERBOSE 1
#endif

/* Use api.header.include to #include this header
   instead of duplicating it here.  */
#ifndef YY_YY_YACC_SQL_H_INCLUDED
# define YY_YY_YACC_SQL_H_INCLUDED
/* Debug traces.  */
#ifndef YYDEBUG
# define YYDEBUG 1
#endif
#if YYDEBUG
extern int yydebug;
#endif
/* "%code requires" blocks.  */

#include "parse_defs.h" // 这里是为了在生成的.h文件包含一些所需的头文件


/* Token type.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
  enum yytokentype
  {
    SEMICOLON = 258,
    CREATE = 259,
    DROP = 260,
    TABLE = 261,
    TABLES = 262,
    INDEX = 263,
    CALC = 264,
    SELECT = 265,
    DESC = 266,
    SHOW = 267,
    SYNC = 268,
    INSERT = 269,
    DELETE = 270,
    UPDATE = 271,
    LBRACE = 272,
    RBRACE = 273,
    COMMA = 274,
    TRX_BEGIN = 275,
    TRX_COMMIT = 276,
    TRX_ROLLBACK = 277,
    INT_T = 278,
    BLOB_T = 279,
    DATE_T = 280,
    DATETIME_T = 281,
    TEXT_T = 282,
    STRING_T = 283,
    FLOAT_T = 284,
    HELP = 285,
    EXIT = 286,
    DOT = 287,
    INTO = 288,
    VALUES = 289,
    FROM = 290,
    WHERE = 291,
    AND = 292,
    OR = 293,
    SET = 294,
    USING = 295,
    HASH = 296,
    ON = 297,
    LOAD = 298,
    DATA = 299,
    INFILE = 300,
    EXPLAIN = 301,
    PRIMARY = 302,
    KEY = 303,
    NOT = 304,
    LIKE = 305,
    NULL_T = 306,
    EQ = 307,
    LT = 308,
    GT = 309,
    LE = 310,
    GE = 311,
    NE = 312,
    INNER = 313,
    JOIN = 314,
    AGGR_MAX = 315,
    AGGR_MIN = 316,
    AGGR_SUM = 317,
    AGGR_AVG = 318,
    AGGR_COUNT = 319,
    ORDER_T = 320,
    GROUP = 321,
    BY = 322,
    ASC = 323,
    HAVING = 324,
    AS = 325,
    IN = 326,
    EXISTS = 327,
    LIMIT = 328,
    OFFSET = 329,
    IF = 330,
    DISTINCT = 331,
    NUMBER = 332,
    FLOAT = 333,
    ID = 334,
    DATE_TIME_STR = 335,
    DATE_STR = 336,
    SSS = 337,
    UMINUS = 338
  };
#endif

/* Value type.  */
#if ! defined YYSTYPE && ! defined YYSTYPE_IS_DECLARED
union YYSTYPE
{

  ParsedSqlNode *                   sqlNode;
  Value *                           value;
  enum CompOp                       comp;
  RelAttrSqlNode *                  relAttr;
  varArrayList *                    attrInfos;       
  AttrInfoSqlNode *                 attrInfo;
  Expression *                      expression;
  varArrayList *                    expressionList;  
  varArrayList *                    valueList; 
  varArrayList *                    insertValueList;        /* element type:varArrayList<element type: Value> */     
  varArrayList *                    conditionList;   
  varArrayList *                    relAttrList;    
  varArrayList *                    relationList;   
  InnerJoinSqlNode *                innerJoins; 
  varArrayList*                     innerJoinsList;
  OrderBySqlNode*                   orderbyUnit;
  varArrayList*                     orderbyUnitList;
  char *                            string;
  int                               number;
  double                             floats;
  bool                              boolean;
  LimitSqlNode*                     limit;
  UpdateKV*                         updateKv;
  varArrayList*                     updateKvList;


};
typedef union YYSTYPE YYSTYPE;
# define YYSTYPE_IS_TRIVIAL 1
# define YYSTYPE_IS_DECLARED 1
#endif

/* Location type.  */
#if ! defined YYLTYPE && ! defined YYLTYPE_IS_DECLARED
typedef struct YYLTYPE YYLTYPE;
struct YYLTYPE
{
  int first_line;
  int first_column;
  int last_line;
  int last_column;
};
# define YYLTYPE_IS_DECLARED 1
# define YYLTYPE_IS_TRIVIAL 1
#endif



int yyparse (const char * sqlString, ParsedSqlResult * sqlResult, void * scanner);

#endif /* !YY_YY_YACC_SQL_H_INCLUDED  */



#ifdef short
# undef short
#endif

/* On compilers that do not define __PTRDIFF_MAX__ etc., make sure
   <limits.h> and (if available) <stdint.h> are included
   so that the code can choose integer types of a good width.  */

#ifndef __PTRDIFF_MAX__
# include <limits.h> /* INFRINGES ON USER NAME SPACE */
# if defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stdint.h> /* INFRINGES ON USER NAME SPACE */
#  define YY_STDINT_H
# endif
#endif

/* Narrow types that promote to a signed type and that can represent a
   signed or unsigned integer of at least N bits.  In tables they can
   save space and decrease cache pressure.  Promoting to a signed type
   helps avoid bugs in integer arithmetic.  */

#ifdef __INT_LEAST8_MAX__
typedef __INT_LEAST8_TYPE__ yytype_int8;
#elif defined YY_STDINT_H
typedef int_least8_t yytype_int8;
#else
typedef signed char yytype_int8;
#endif

#ifdef __INT_LEAST16_MAX__
typedef __INT_LEAST16_TYPE__ yytype_int16;
#elif defined YY_STDINT_H
typedef int_least16_t yytype_int16;
#else
typedef short yytype_int16;
#endif

#if defined __UINT_LEAST8_MAX__ && __UINT_LEAST8_MAX__ <= __INT_MAX__
typedef __UINT_LEAST8_TYPE__ yytype_uint8;
#elif (!defined __UINT_LEAST8_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST8_MAX <= INT_MAX)
typedef uint_least8_t yytype_uint8;
#elif !defined __UINT_LEAST8_MAX__ && UCHAR_MAX <= INT_MAX
typedef unsigned char yytype_uint8;
#else
typedef short yytype_uint8;
#endif

#if defined __UINT_LEAST16_MAX__ && __UINT_LEAST16_MAX__ <= __INT_MAX__
typedef __UINT_LEAST16_TYPE__ yytype_uint16;
#elif (!defined __UINT_LEAST16_MAX__ && defined YY_STDINT_H \
       && UINT_LEAST16_MAX <= INT_MAX)
typedef uint_least16_t yytype_uint16;
#elif !defined __UINT_LEAST16_MAX__ && USHRT_MAX <= INT_MAX
typedef unsigned short yytype_uint16;
#else
typedef int yytype_uint16;
#endif

#ifndef YYPTRDIFF_T
# if defined __PTRDIFF_TYPE__ && defined __PTRDIFF_MAX__
#  define YYPTRDIFF_T __PTRDIFF_TYPE__
#  define YYPTRDIFF_MAXIMUM __PTRDIFF_MAX__
# elif defined PTRDIFF_MAX
#  ifndef ptrdiff_t
#   include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  endif
#  define YYPTRDIFF_T ptrdiff_t
#  define YYPTRDIFF_MAXIMUM PTRDIFF_MAX
# else
#  define YYPTRDIFF_T long
#  define YYPTRDIFF_MAXIMUM LONG_MAX
# endif
#endif

#ifndef YYSIZE_T
# ifdef __SIZE_TYPE__
#  define YYSIZE_T __SIZE_TYPE__
# elif defined size_t
#  define YYSIZE_T size_t
# elif defined __STDC_VERSION__ && 199901 <= __STDC_VERSION__
#  include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  define YYSIZE_T size_t
# else
#  define YYSIZE_T unsigned
# endif
#endif

#define YYSIZE_MAXIMUM                                  \
  YY_CAST (YYPTRDIFF_T,                                 \
           (YYPTRDIFF_MAXIMUM < YY_CAST (YYSIZE_T, -1)  \
            ? YYPTRDIFF_MAXIMUM                         \
            : YY_CAST (YYSIZE_T, -1)))

#define YYSIZEOF(X) YY_CAST (YYPTRDIFF_T, sizeof (X))

/* Stored state numbers (used for stacks). */
typedef yytype_int16 yy_state_t;

/* State numbers in computations.  */
typedef int yy_state_fast_t;

#ifndef YY_
# if defined YYENABLE_NLS && YYENABLE_NLS
#  if ENABLE_NLS
#   include <libintl.h> /* INFRINGES ON USER NAME SPACE */
#   define YY_(Msgid) dgettext ("bison-runtime", Msgid)
#  endif
# endif
# ifndef YY_
#  define YY_(Msgid) Msgid
# endif
#endif

#ifndef YY_ATTRIBUTE_PURE
# if defined __GNUC__ && 2 < __GNUC__ + (96 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_PURE __attribute__ ((__pure__))
# else
#  define YY_ATTRIBUTE_PURE
# endif
#endif

#ifndef YY_ATTRIBUTE_UNUSED
# if defined __GNUC__ && 2 < __GNUC__ + (7 <= __GNUC_MINOR__)
#  define YY_ATTRIBUTE_UNUSED __attribute__ ((__unused__))
# else
#  define YY_ATTRIBUTE_UNUSED
# endif
#endif

/* Suppress unused-variable warnings by "using" E.  */
#if ! defined lint || defined __GNUC__
# define YYUSE(E) ((void) (E))
#else
# define YYUSE(E) /* empty */
#endif

#if defined __GNUC__ && ! defined __ICC && 407 <= __GNUC__ * 100 + __GNUC_MINOR__
/* Suppress an incorrect diagnostic about yylval being uninitialized.  */
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN                            \
    _Pragma ("GCC diagnostic push")                                     \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")              \
    _Pragma ("GCC diagnostic ignored \"-Wmaybe-uninitialized\"")
# define YY_IGNORE_MAYBE_UNINITIALIZED_END      \
    _Pragma ("GCC diagnostic pop")
#else
# define YY_INITIAL_VALUE(Value) Value
#endif
#ifndef YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_END
#endif
#ifndef YY_INITIAL_VALUE
# define YY_INITIAL_VALUE(Value) /* Nothing. */
#endif

#if defined __cplusplus && defined __GNUC__ && ! defined __ICC && 6 <= __GNUC__
# define YY_IGNORE_USELESS_CAST_BEGIN                          \
    _Pragma ("GCC diagnostic push")                            \
    _Pragma ("GCC diagnostic ignored \"-Wuseless-cast\"")
# define YY_IGNORE_USELESS_CAST_END            \
    _Pragma ("GCC diagnostic pop")
#endif
#ifndef YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_BEGIN
# define YY_IGNORE_USELESS_CAST_END
#endif


#define YY_ASSERT(E) ((void) (0 && (E)))

#if ! defined yyoverflow || YYERROR_VERBOSE

/* The parser invokes alloca or malloc; define the necessary symbols.  */

# ifdef YYSTACK_USE_ALLOCA
#  if YYSTACK_USE_ALLOCA
#   ifdef __GNUC__
#    define YYSTACK_ALLOC __builtin_alloca
#   elif defined __BUILTIN_VA_ARG_INCR
#    include <alloca.h> /* INFRINGES ON USER NAME SPACE */
#   elif defined _AIX
#    define YYSTACK_ALLOC __alloca
#   elif defined _MSC_VER
#    include <malloc.h> /* INFRINGES ON USER NAME SPACE */
#    define alloca _alloca
#   else
#    define YYSTACK_ALLOC alloca
#    if ! defined _ALLOCA_H && ! defined EXIT_SUCCESS
#     include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
      /* Use EXIT_SUCCESS as a witness for stdlib.h.  */
#     ifndef EXIT_SUCCESS
#      define EXIT_SUCCESS 0
#     endif
#    endif
#   endif
#  endif
# endif

# ifdef YYSTACK_ALLOC
   /* Pacify GCC's 'empty if-body' warning.  */
#  define YYSTACK_FREE(Ptr) do { /* empty */; } while (0)
#  ifndef YYSTACK_ALLOC_MAXIMUM
    /* The OS might guarantee only one guard page at the bottom of the stack,
       and a page size can be as small as 4096 bytes.  So we cannot safely
       invoke alloca (N) if N exceeds 4096.  Use a slightly smaller number
       to allow for a few compiler-allocated temporary stack slots.  */
#   define YYSTACK_ALLOC_MAXIMUM 4032 /* reasonable circa 2006 */
#  endif
# else
#  define YYSTACK_ALLOC YYMALLOC
#  define YYSTACK_FREE YYFREE
#  ifndef YYSTACK_ALLOC_MAXIMUM
#   define YYSTACK_ALLOC_MAXIMUM YYSIZE_MAXIMUM
#  endif
#  if (defined __cplusplus && ! defined EXIT_SUCCESS \
       && ! ((defined YYMALLOC || defined malloc) \
             && (defined YYFREE || defined free)))
#   include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
#   ifndef EXIT_SUCCESS
#    define EXIT_SUCCESS 0
#   endif
#  endif
#  ifndef YYMALLOC
#   define YYMALLOC malloc
#   if ! defined malloc && ! defined EXIT_SUCCESS
void *malloc (YYSIZE_T); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
#  ifndef YYFREE
#   define YYFREE free
#   if ! defined free && ! defined EXIT_SUCCESS
void free (void *); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
# endif
#endif /* ! defined yyoverflow || YYERROR_VERBOSE */


#if (! defined yyoverflow \
     && (! defined __cplusplus \
         || (defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL \
             && defined YYSTYPE_IS_TRIVIAL && YYSTYPE_IS_TRIVIAL)))

/* A type that is properly aligned for any stack member.  */
union yyalloc
{
  yy_state_t yyss_alloc;
  YYSTYPE yyvs_alloc;
  YYLTYPE yyls_alloc;
};

/* The size of the maximum gap between one aligned stack and the next.  */
# define YYSTACK_GAP_MAXIMUM (YYSIZEOF (union yyalloc) - 1)

/* The size of an array large to enough to hold all stacks, each with
   N elements.  */
# define YYSTACK_BYTES(N) \
     ((N) * (YYSIZEOF (yy_state_t) + YYSIZEOF (YYSTYPE) \
             + YYSIZEOF (YYLTYPE)) \
      + 2 * YYSTACK_GAP_MAXIMUM)

# define YYCOPY_NEEDED 1

/* Relocate STACK from its old location to the new one.  The
   local variables YYSIZE and YYSTACKSIZE give the old and new number of
   elements in the stack, and YYPTR gives the new location of the
   stack.  Advance YYPTR to a properly aligned location for the next
   stack.  */
# define YYSTACK_RELOCATE(Stack_alloc, Stack)                           \
    do                                                                  \
      {                                                                 \
        YYPTRDIFF_T yynewbytes;                                         \
        YYCOPY (&yyptr->Stack_alloc, Stack, yysize);                    \
        Stack = &yyptr->Stack_alloc;                                    \
        yynewbytes = yystacksize * YYSIZEOF (*Stack) + YYSTACK_GAP_MAXIMUM; \
        yyptr += yynewbytes / YYSIZEOF (*yyptr);                        \
      }                                                                 \
    while (0)

#endif

#if defined YYCOPY_NEEDED && YYCOPY_NEEDED
/* Copy COUNT objects from SRC to DST.  The source and destination do
   not overlap.  */
# ifndef YYCOPY
#  if defined __GNUC__ && 1 < __GNUC__
#   define YYCOPY(Dst, Src, Count) \
      __builtin_memcpy (Dst, Src, YY_CAST (YYSIZE_T, (Count)) * sizeof (*(Src)))
#  else
#   define YYCOPY(Dst, Src, Count)              \
      do                                        \
        {                                       \
          YYPTRDIFF_T yyi;                      \
          for (yyi = 0; yyi < (Count); yyi++)   \
            (Dst)[yyi] = (Src)[yyi];            \
        }                                       \
      while (0)
#  endif
# endif
#endif /* !YYCOPY_NEEDED */

/* YYFINAL -- State number of the termination state.  */
#define YYFINAL  79
/* YYLAST -- Last index in YYTABLE.  */
#define YYLAST   449

/* YYNTOKENS -- Number of terminals.  */
#define YYNTOKENS  88
/* YYNNTS -- Number of nonterminals.  */
#define YYNNTS  58
/* YYNRULES -- Number of rules.  */
#define YYNRULES  154
/* YYNSTATES -- Number of states.  */
#define YYNSTATES  283

#define YYUNDEFTOK  2
#define YYMAXUTOK   338


/* YYTRANSLATE(TOKEN-NUM) -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex, with out-of-bounds checking.  */
#define YYTRANSLATE(YYX)                                                \
  (0 <= (YYX) && (YYX) <= YYMAXUTOK ? yytranslate[YYX] : YYUNDEFTOK)

/* YYTRANSLATE[TOKEN-NUM] -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex.  */
static const yytype_int8 yytranslate[] =
{
       0,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,    85,    83,     2,    84,     2,    86,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     1,     2,     3,     4,
       5,     6,     7,     8,     9,    10,    11,    12,    13,    14,
      15,    16,    17,    18,    19,    20,    21,    22,    23,    24,
      25,    26,    27,    28,    29,    30,    31,    32,    33,    34,
      35,    36,    37,    38,    39,    40,    41,    42,    43,    44,
      45,    46,    47,    48,    49,    50,    51,    52,    53,    54,
      55,    56,    57,    58,    59,    60,    61,    62,    63,    64,
      65,    66,    67,    68,    69,    70,    71,    72,    73,    74,
      75,    76,    77,    78,    79,    80,    81,    82,    87
};

#if YYDEBUG
  /* YYRLINE[YYN] -- Source line where rule number YYN was defined.  */
static const yytype_int16 yyrline[] =
{
       0,   352,   352,   360,   361,   362,   363,   364,   365,   366,
     367,   368,   369,   370,   371,   372,   373,   374,   375,   376,
     377,   378,   379,   383,   389,   394,   400,   406,   412,   419,
     424,   431,   437,   444,   452,   463,   471,   484,   501,   512,
     515,   527,   531,   535,   539,   543,   547,   551,   555,   562,
     565,   571,   574,   575,   576,   577,   578,   579,   580,   583,
     613,   616,   630,   633,   646,   649,   661,   684,   687,   709,
     715,   721,   739,   757,   765,   773,   781,   813,   816,   828,
     848,   897,   948,   957,   963,   976,   979,   982,   985,   988,
    1006,  1010,  1015,  1021,  1024,  1030,  1033,  1036,  1039,  1042,
    1048,  1056,  1078,  1087,  1096,  1106,  1112,  1115,  1122,  1125,
    1137,  1140,  1143,  1149,  1172,  1175,  1198,  1208,  1211,  1216,
    1220,  1229,  1246,  1257,  1270,  1275,  1280,  1288,  1294,  1302,
    1305,  1313,  1316,  1324,  1327,  1334,  1338,  1343,  1348,  1349,
    1350,  1351,  1352,  1353,  1354,  1355,  1356,  1357,  1360,  1361,
    1364,  1375,  1383,  1392,  1393
};
#endif

#if YYDEBUG || YYERROR_VERBOSE || 1
/* YYTNAME[SYMBOL-NUM] -- String name of the symbol SYMBOL-NUM.
   First, the terminals, then, starting at YYNTOKENS, nonterminals.  */
static const char *const yytname[] =
{
  "$end", "error", "$undefined", "SEMICOLON", "CREATE", "DROP", "TABLE",
  "TABLES", "INDEX", "CALC", "SELECT", "DESC", "SHOW", "SYNC", "INSERT",
  "DELETE", "UPDATE", "LBRACE", "RBRACE", "COMMA", "TRX_BEGIN",
  "TRX_COMMIT", "TRX_ROLLBACK", "INT_T", "BLOB_T", "DATE_T", "DATETIME_T",
  "TEXT_T", "STRING_T", "FLOAT_T", "HELP", "EXIT", "DOT", "INTO", "VALUES",
  "FROM", "WHERE", "AND", "OR", "SET", "USING", "HASH", "ON", "LOAD",
  "DATA", "INFILE", "EXPLAIN", "PRIMARY", "KEY", "NOT", "LIKE", "NULL_T",
  "EQ", "LT", "GT", "LE", "GE", "NE", "INNER", "JOIN", "AGGR_MAX",
  "AGGR_MIN", "AGGR_SUM", "AGGR_AVG", "AGGR_COUNT", "ORDER_T", "GROUP",
  "BY", "ASC", "HAVING", "AS", "IN", "EXISTS", "LIMIT", "OFFSET", "IF",
  "DISTINCT", "NUMBER", "FLOAT", "ID", "DATE_TIME_STR", "DATE_STR", "SSS",
  "'+'", "'-'", "'*'", "'/'", "UMINUS", "$accept", "commands",
  "commandWrapper", "exitStmt", "helpStmt", "syncStmt", "beginStmt",
  "commitStmt", "rollbackStmt", "dropTableStmt", "showTablesStmt",
  "descTableStmt", "createIndexStmt", "dropIndexStmt", "createTableStmt",
  "attrDefList", "attrDef", "asOption", "number", "type", "insertStmt",
  "insertColList", "idxColList", "insertValueList", "insertValue",
  "valueList", "value", "deleteStmt", "updateStmt", "updateKvList",
  "updateKv", "selectStmt", "calcStmt", "expressionList", "expression",
  "aggrFuncType", "aggrFuncExpr", "selectAttr", "relAttr", "fromList",
  "alias", "fromNode", "joinList", "subQueryExpr", "where", "condition",
  "sortUnit", "sortList", "optOrderBy", "optGroupBy", "optHaving",
  "optLimit", "compOp", "existsOp", "loadDataStmt", "explainStmt",
  "setVariableStmt", "optSemicolon", YY_NULLPTR
};
#endif

# ifdef YYPRINT
/* YYTOKNUM[NUM] -- (External) token number corresponding to the
   (internal) symbol number NUM (which must be that of a token).  */
static const yytype_int16 yytoknum[] =
{
       0,   256,   257,   258,   259,   260,   261,   262,   263,   264,
     265,   266,   267,   268,   269,   270,   271,   272,   273,   274,
     275,   276,   277,   278,   279,   280,   281,   282,   283,   284,
     285,   286,   287,   288,   289,   290,   291,   292,   293,   294,
     295,   296,   297,   298,   299,   300,   301,   302,   303,   304,
     305,   306,   307,   308,   309,   310,   311,   312,   313,   314,
     315,   316,   317,   318,   319,   320,   321,   322,   323,   324,
     325,   326,   327,   328,   329,   330,   331,   332,   333,   334,
     335,   336,   337,    43,    45,    42,    47,   338
};
# endif

#define YYPACT_NINF (-151)

#define yypact_value_is_default(Yyn) \
  ((Yyn) == YYPACT_NINF)

#define YYTABLE_NINF (-50)

#define yytable_value_is_error(Yyn) \
  0

  /* YYPACT[STATE-NUM] -- Index in YYTABLE of the portion describing
     STATE-NUM.  */
static const yytype_int16 yypact[] =
{
     403,    43,    62,   322,   179,   -58,    24,  -151,    32,    45,
       6,  -151,  -151,  -151,  -151,  -151,    18,    51,   403,    99,
     114,  -151,  -151,  -151,  -151,  -151,  -151,  -151,  -151,  -151,
    -151,  -151,  -151,  -151,  -151,  -151,  -151,  -151,  -151,  -151,
    -151,    41,    42,   -13,    44,   147,  -151,  -151,  -151,  -151,
    -151,  -151,  -151,  -151,    86,  -151,  -151,  -151,   322,  -151,
    -151,    30,   105,  -151,  -151,  -151,   215,    93,    94,  -151,
      92,  -151,  -151,    49,    50,    95,    79,    88,  -151,  -151,
    -151,  -151,   -10,   104,    63,  -151,   106,   118,   119,    68,
    -151,    72,  -151,   322,   322,   322,   322,   130,   250,   120,
     -65,    67,    75,   139,   132,    96,    12,    97,    98,  -151,
     166,   102,   103,   107,  -151,  -151,  -151,  -151,   -70,   -70,
    -151,  -151,   322,   160,   -12,    75,  -151,  -151,   -15,   165,
     108,   151,   287,  -151,   138,   172,  -151,   159,   189,   174,
    -151,   177,  -151,  -151,  -151,  -151,  -151,   165,   137,    75,
     132,   178,   182,    81,   134,  -151,   117,    70,   322,   322,
      96,   132,   213,  -151,  -151,  -151,  -151,  -151,  -151,  -151,
       1,    98,   202,   143,   132,   164,  -151,   165,   168,   156,
     218,   322,   219,   -27,    16,  -151,   -26,  -151,  -151,  -151,
    -151,  -151,  -151,  -151,  -151,   322,   287,   287,    20,    20,
     172,  -151,   158,   167,   197,   195,   174,    -1,   229,   168,
     169,  -151,   183,   180,   178,  -151,    -2,   182,  -151,  -151,
    -151,  -151,    20,  -151,   214,  -151,  -151,  -151,   234,   204,
    -151,  -151,   166,   222,   180,   -15,   322,   287,   200,  -151,
     322,   236,   219,    39,   217,  -151,   228,   200,   230,  -151,
      70,   203,   198,    -2,  -151,  -151,   225,   223,  -151,  -151,
     198,   287,   322,   205,  -151,  -151,   231,  -151,  -151,   -25,
      -7,   262,  -151,   209,   233,  -151,  -151,  -151,   322,   208,
    -151,  -151,  -151
};

  /* YYDEFACT[STATE-NUM] -- Default reduction number in state STATE-NUM.
     Performed when YYTABLE does not specify something else to do.  Zero
     means the default is an error.  */
static const yytype_uint8 yydefact[] =
{
       0,     0,     0,     0,     0,     0,     0,    25,     0,     0,
       0,    26,    27,    28,    24,    23,     0,     0,     0,     0,
     153,    22,    21,    14,    15,    16,    17,     9,    10,    11,
      12,    13,     8,     5,     7,     6,     4,     3,    18,    19,
      20,     0,     0,     0,     0,     0,    74,    95,    96,    97,
      98,    99,    69,    70,   106,    72,    71,    73,     0,    91,
      82,   110,     0,    93,    92,    94,     0,   106,   102,   105,
       0,    32,    31,     0,     0,     0,     0,     0,   151,     1,
     154,     2,    49,     0,     0,    30,     0,     0,     0,     0,
      90,     0,   111,     0,     0,     0,     0,    83,     0,     0,
       0,     0,     0,    60,   117,     0,     0,     0,     0,    50,
       0,     0,     0,     0,   116,    89,   107,   112,    85,    86,
      87,    88,     0,     0,     0,     0,   104,   103,   110,   108,
       0,     0,     0,    75,     0,    77,   152,     0,     0,    39,
      38,     0,    29,    35,    84,   101,   100,   108,   114,     0,
     117,    62,     0,     0,     0,   148,     0,   118,     0,     0,
       0,   117,     0,    52,    58,    53,    56,    57,    54,    55,
      43,     0,     0,     0,   117,     0,   113,   108,   131,     0,
       0,     0,    64,   110,     0,   149,     0,   144,   138,   139,
     140,   141,   142,   143,   146,     0,     0,     0,   121,    79,
      77,    76,     0,     0,     0,     0,    39,    36,     0,   131,
       0,   109,     0,   133,    62,    61,    67,     0,    59,   119,
     145,   147,   120,   122,   123,    78,   150,    51,     0,    44,
      47,    40,     0,    33,   133,   110,     0,     0,   129,    63,
       0,     0,    64,    41,     0,    37,     0,   129,     0,   132,
     134,     0,   137,    67,    66,    65,     0,     0,    48,    34,
     137,     0,     0,     0,    80,    68,    42,    45,    81,   114,
     124,   127,   130,   136,     0,   115,   125,   126,     0,     0,
      46,   128,   135
};

  /* YYPGOTO[NTERM-NUM].  */
static const yytype_int16 yypgoto[] =
{
    -151,  -151,   268,  -151,  -151,  -151,  -151,  -151,  -151,  -151,
    -151,  -151,  -151,  -151,  -151,    82,   116,    83,  -151,  -151,
    -151,  -151,    77,    47,    85,    52,   192,  -151,  -151,   109,
     146,   -43,  -151,    -3,   -57,  -151,  -151,   237,  -151,  -137,
    -123,  -114,    38,  -151,  -142,  -150,  -151,    37,    61,   110,
      84,    56,  -151,  -151,  -151,  -151,  -151,  -151
};

  /* YYDEFGOTO[NTERM-NUM].  */
static const yytype_int16 yydefgoto[] =
{
      -1,    19,    20,    21,    22,    23,    24,    25,    26,    27,
      28,    29,    30,    31,    32,   172,   139,   110,   228,   170,
      33,   131,   180,   218,   182,   241,    59,    34,    35,   161,
     135,    36,    37,    69,    61,    62,    63,    70,    64,   150,
      97,   129,   176,    65,   133,   157,   271,   272,   252,   213,
     238,   264,   195,   158,    38,    39,    40,    81
};

  /* YYTABLE[YYPACT[STATE-NUM]] -- What to do in state STATE-NUM.  If
     positive, shift that token.  If negative, reduce the rule whose
     number is the opposite.  If YYTABLE_NINF, syntax error.  */
static const yytype_int16 yytable[] =
{
      60,    90,    87,   184,   276,   148,   146,   108,   178,   -49,
     174,   147,   196,   197,   116,    95,    96,   240,   203,   201,
     126,    71,   186,   187,   220,   188,   189,   190,   191,   192,
     193,    72,   209,   175,   219,   177,   118,   119,   120,   121,
     211,   124,    88,    91,   194,   221,   223,   224,   204,    41,
     205,    42,    92,   196,   197,    91,    93,    94,    95,    96,
     109,   277,    84,    46,    92,    73,    85,   140,    43,   109,
      44,    93,    94,    95,    96,   156,    93,    94,    95,    96,
      74,    93,    94,    95,    96,    75,   256,   250,   257,    52,
      53,     4,    55,    56,    57,    77,   183,    76,   153,    79,
      91,   198,   199,    93,    94,    95,    96,   196,   197,    92,
      87,   269,   248,    93,    94,    95,    96,    80,    89,   144,
      82,    83,    98,    86,   216,   100,   101,   102,   103,   104,
     154,   106,    46,   107,   105,   112,   114,   115,   222,   156,
     156,    47,    48,    49,    50,    51,   111,   116,   113,   122,
      88,   117,   127,   155,   128,   125,   130,     4,    52,    53,
      54,    55,    56,    57,    45,    58,   186,   187,   132,   188,
     189,   190,   191,   192,   193,   134,     4,   138,   145,   137,
     156,   141,   142,   253,   149,   152,   143,   151,   194,   245,
     159,   160,   162,   171,   173,   175,    45,   179,    46,   181,
      93,    94,    95,    96,   156,   270,   185,    47,    48,    49,
      50,    51,   163,   164,   165,   166,   167,   168,   169,   202,
     207,   270,   208,   210,    52,    53,    54,    55,    56,    57,
      46,    58,    45,   249,   212,   214,   215,   226,   217,    47,
      48,    49,    50,    51,   227,   229,   230,   233,   235,   237,
     236,   196,   243,   244,   254,    66,    52,    53,    67,    55,
      56,    57,   246,    58,    68,   251,    46,    45,   258,   259,
     262,   263,   261,   266,   267,    47,    48,    49,    50,    51,
     274,   278,   273,   279,   280,   282,    78,   206,   231,   255,
     232,   239,    52,    53,    67,    55,    56,    57,   136,    58,
      68,    46,   242,    99,   153,   265,   200,   275,   260,   225,
      47,    48,    49,    50,    51,   281,   268,     0,   247,   234,
       0,     0,     0,     0,     0,     0,     0,    52,    53,    54,
      55,    56,    57,     0,    58,   123,   154,     0,    46,    45,
       0,     0,     0,     0,     0,     0,     0,    47,    48,    49,
      50,    51,     0,     0,     0,     0,     0,     0,     0,   155,
       0,     0,     0,     0,    52,    53,    54,    55,    56,    57,
       0,    58,     0,    46,     0,     0,     0,     0,     0,     0,
       0,     0,    47,    48,    49,    50,    51,     0,     0,     0,
       0,     0,     0,     0,     0,     0,     0,     0,     0,    52,
      53,    54,    55,    56,    57,     0,    58,     1,     2,     0,
       0,     0,     3,     4,     5,     6,     7,     8,     9,    10,
       0,     0,     0,    11,    12,    13,     0,     0,     0,     0,
       0,     0,     0,    14,    15,     0,     0,     0,     0,     0,
       0,     0,    16,     0,     0,     0,    17,     0,     0,    18
};

static const yytype_int16 yycheck[] =
{
       3,    58,    45,   153,    11,   128,    18,    17,   150,    10,
     147,   125,    37,    38,    79,    85,    86,    19,    17,   161,
      85,    79,    49,    50,    50,    52,    53,    54,    55,    56,
      57,     7,   174,    58,    18,   149,    93,    94,    95,    96,
     177,    98,    45,    70,    71,    71,   196,   197,    47,     6,
      49,     8,    79,    37,    38,    70,    83,    84,    85,    86,
      70,    68,    75,    51,    79,    33,    79,   110,     6,    70,
       8,    83,    84,    85,    86,   132,    83,    84,    85,    86,
      35,    83,    84,    85,    86,    79,    47,   237,    49,    77,
      78,    10,    80,    81,    82,    44,   153,    79,    17,     0,
      70,   158,   159,    83,    84,    85,    86,    37,    38,    79,
     153,   261,   235,    83,    84,    85,    86,     3,    32,   122,
      79,    79,    17,    79,   181,    32,    32,    35,    79,    79,
      49,    52,    51,    45,    39,    72,    18,    18,   195,   196,
     197,    60,    61,    62,    63,    64,    42,    79,    42,    19,
     153,    79,    85,    72,    79,    35,    17,    10,    77,    78,
      79,    80,    81,    82,    17,    84,    49,    50,    36,    52,
      53,    54,    55,    56,    57,    79,    10,    79,    18,    82,
     237,    79,    79,   240,    19,    34,    79,    79,    71,   232,
      52,    19,    33,    19,    17,    58,    17,    19,    51,    17,
      83,    84,    85,    86,   261,   262,    72,    60,    61,    62,
      63,    64,    23,    24,    25,    26,    27,    28,    29,     6,
      18,   278,    79,    59,    77,    78,    79,    80,    81,    82,
      51,    84,    17,   236,    66,    79,    18,    79,    19,    60,
      61,    62,    63,    64,    77,    48,    51,    18,    79,    69,
      67,    37,    18,    49,    18,    76,    77,    78,    79,    80,
      81,    82,    40,    84,    85,    65,    51,    17,    51,    41,
      67,    73,    42,    48,    51,    60,    61,    62,    63,    64,
      49,    19,    77,    74,    51,    77,    18,   171,   206,   242,
     207,   214,    77,    78,    79,    80,    81,    82,   106,    84,
      85,    51,   217,    66,    17,   253,   160,   269,   247,   200,
      60,    61,    62,    63,    64,   278,   260,    -1,   234,   209,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    77,    78,    79,
      80,    81,    82,    -1,    84,    85,    49,    -1,    51,    17,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    60,    61,    62,
      63,    64,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    72,
      -1,    -1,    -1,    -1,    77,    78,    79,    80,    81,    82,
      -1,    84,    -1,    51,    -1,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    60,    61,    62,    63,    64,    -1,    -1,    -1,
      -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    -1,    77,
      78,    79,    80,    81,    82,    -1,    84,     4,     5,    -1,
      -1,    -1,     9,    10,    11,    12,    13,    14,    15,    16,
      -1,    -1,    -1,    20,    21,    22,    -1,    -1,    -1,    -1,
      -1,    -1,    -1,    30,    31,    -1,    -1,    -1,    -1,    -1,
      -1,    -1,    39,    -1,    -1,    -1,    43,    -1,    -1,    46
};

  /* YYSTOS[STATE-NUM] -- The (internal number of the) accessing
     symbol of state STATE-NUM.  */
static const yytype_uint8 yystos[] =
{
       0,     4,     5,     9,    10,    11,    12,    13,    14,    15,
      16,    20,    21,    22,    30,    31,    39,    43,    46,    89,
      90,    91,    92,    93,    94,    95,    96,    97,    98,    99,
     100,   101,   102,   108,   115,   116,   119,   120,   142,   143,
     144,     6,     8,     6,     8,    17,    51,    60,    61,    62,
      63,    64,    77,    78,    79,    80,    81,    82,    84,   114,
     121,   122,   123,   124,   126,   131,    76,    79,    85,   121,
     125,    79,     7,    33,    35,    79,    79,    44,    90,     0,
       3,   145,    79,    79,    75,    79,    79,   119,   121,    32,
     122,    70,    79,    83,    84,    85,    86,   128,    17,   125,
      32,    32,    35,    79,    79,    39,    52,    45,    17,    70,
     105,    42,    72,    42,    18,    18,    79,    79,   122,   122,
     122,   122,    19,    85,   122,    35,    85,    85,    79,   129,
      17,   109,    36,   132,    79,   118,   114,    82,    79,   104,
     119,    79,    79,    79,   121,    18,    18,   129,   128,    19,
     127,    79,    34,    17,    49,    72,   122,   133,   141,    52,
      19,   117,    33,    23,    24,    25,    26,    27,    28,    29,
     107,    19,   103,    17,   127,    58,   130,   129,   132,    19,
     110,    17,   112,   122,   133,    72,    49,    50,    52,    53,
      54,    55,    56,    57,    71,   140,    37,    38,   122,   122,
     118,   132,     6,    17,    47,    49,   104,    18,    79,   132,
      59,   127,    66,   137,    79,    18,   122,    19,   111,    18,
      50,    71,   122,   133,   133,   117,    79,    77,   106,    48,
      51,   103,   105,    18,   137,    79,    67,    69,   138,   110,
      19,   113,   112,    18,    49,   119,    40,   138,   128,   121,
     133,    65,   136,   122,    18,   111,    47,    49,    51,    41,
     136,    42,    67,    73,   139,   113,    48,    51,   139,   133,
     122,   134,   135,    77,    49,   130,    11,    68,    19,    74,
      51,   135,    77
};

  /* YYR1[YYN] -- Symbol number of symbol that rule YYN derives.  */
static const yytype_uint8 yyr1[] =
{
       0,    88,    89,    90,    90,    90,    90,    90,    90,    90,
      90,    90,    90,    90,    90,    90,    90,    90,    90,    90,
      90,    90,    90,    91,    92,    93,    94,    95,    96,    97,
      97,    98,    99,   100,   100,   101,   102,   102,   102,   103,
     103,   104,   104,   104,   104,   104,   104,   104,   104,   105,
     105,   106,   107,   107,   107,   107,   107,   107,   107,   108,
     109,   109,   110,   110,   111,   111,   112,   113,   113,   114,
     114,   114,   114,   114,   114,   115,   116,   117,   117,   118,
     119,   119,   120,   121,   121,   122,   122,   122,   122,   122,
     122,   122,   122,   122,   122,   123,   123,   123,   123,   123,
     124,   124,   125,   125,   125,   125,   126,   126,   127,   127,
     128,   128,   128,   129,   130,   130,   131,   132,   132,   133,
     133,   133,   133,   133,   134,   134,   134,   135,   135,   136,
     136,   137,   137,   138,   138,   139,   139,   139,   140,   140,
     140,   140,   140,   140,   140,   140,   140,   140,   141,   141,
     142,   143,   144,   145,   145
};

  /* YYR2[YYN] -- Number of symbols on the right hand side of rule YYN.  */
static const yytype_int8 yyr2[] =
{
       0,     2,     2,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     1,     1,     5,
       3,     2,     2,     8,    10,     5,     7,     9,     5,     0,
       3,     5,     7,     2,     4,     7,     9,     4,     6,     0,
       1,     1,     1,     1,     1,     1,     1,     1,     1,     7,
       0,     4,     0,     3,     0,     3,     4,     0,     3,     1,
       1,     1,     1,     1,     1,     4,     6,     0,     3,     3,
      10,    11,     2,     2,     4,     3,     3,     3,     3,     3,
       2,     1,     1,     1,     1,     1,     1,     1,     1,     1,
       4,     4,     1,     3,     3,     1,     1,     3,     0,     3,
       0,     1,     2,     3,     0,     7,     3,     0,     2,     3,
       3,     2,     3,     3,     1,     2,     2,     1,     3,     0,
       3,     0,     3,     0,     2,     4,     2,     0,     1,     1,
       1,     1,     1,     1,     1,     2,     1,     2,     1,     2,
       7,     2,     4,     0,     1
};


#define yyerrok         (yyerrstatus = 0)
#define yyclearin       (yychar = YYEMPTY)
#define YYEMPTY         (-2)
#define YYEOF           0

#define YYACCEPT        goto yyacceptlab
#define YYABORT         goto yyabortlab
#define YYERROR         goto yyerrorlab


#define YYRECOVERING()  (!!yyerrstatus)

#define YYBACKUP(Token, Value)                                    \
  do                                                              \
    if (yychar == YYEMPTY)                                        \
      {                                                           \
        yychar = (Token);                                         \
        yylval = (Value);                                         \
        YYPOPSTACK (yylen);                                       \
        yystate = *yyssp;                                         \
        goto yybackup;                                            \
      }                                                           \
    else                                                          \
      {                                                           \
        yyerror (&yylloc, sqlString, sqlResult, scanner, YY_("syntax error: cannot back up")); \
        YYERROR;                                                  \
      }                                                           \
  while (0)

/* Error token number */
#define YYTERROR        1
#define YYERRCODE       256


/* YYLLOC_DEFAULT -- Set CURRENT to span from RHS[1] to RHS[N].
   If N is 0, then set CURRENT to the empty location which ends
   the previous symbol: RHS[0] (always defined).  */

#ifndef YYLLOC_DEFAULT
# define YYLLOC_DEFAULT(Current, Rhs, N)                                \
    do                                                                  \
      if (N)                                                            \
        {                                                               \
          (Current).first_line   = YYRHSLOC (Rhs, 1).first_line;        \
          (Current).first_column = YYRHSLOC (Rhs, 1).first_column;      \
          (Current).last_line    = YYRHSLOC (Rhs, N).last_line;         \
          (Current).last_column  = YYRHSLOC (Rhs, N).last_column;       \
        }                                                               \
      else                                                              \
        {                                                               \
          (Current).first_line   = (Current).last_line   =              \
            YYRHSLOC (Rhs, 0).last_line;                                \
          (Current).first_column = (Current).last_column =              \
            YYRHSLOC (Rhs, 0).last_column;                              \
        }                                                               \
    while (0)
#endif

#define YYRHSLOC(Rhs, K) ((Rhs)[K])


/* Enable debugging if requested.  */
#if YYDEBUG

# ifndef YYFPRINTF
#  include <stdio.h> /* INFRINGES ON USER NAME SPACE */
#  define YYFPRINTF fprintf
# endif

# define YYDPRINTF(Args)                        \
do {                                            \
  if (yydebug)                                  \
    YYFPRINTF Args;                             \
} while (0)


/* YY_LOCATION_PRINT -- Print the location on the stream.
   This macro was not mandated originally: define only if we know
   we won't break user code: when these are the locations we know.  */

#ifndef YY_LOCATION_PRINT
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL

/* Print *YYLOCP on YYO.  Private, do not rely on its existence. */

YY_ATTRIBUTE_UNUSED
static int
yy_location_print_ (FILE *yyo, YYLTYPE const * const yylocp)
{
  int res = 0;
  int end_col = 0 != yylocp->last_column ? yylocp->last_column - 1 : 0;
  if (0 <= yylocp->first_line)
    {
      res += YYFPRINTF (yyo, "%d", yylocp->first_line);
      if (0 <= yylocp->first_column)
        res += YYFPRINTF (yyo, ".%d", yylocp->first_column);
    }
  if (0 <= yylocp->last_line)
    {
      if (yylocp->first_line < yylocp->last_line)
        {
          res += YYFPRINTF (yyo, "-%d", yylocp->last_line);
          if (0 <= end_col)
            res += YYFPRINTF (yyo, ".%d", end_col);
        }
      else if (0 <= end_col && yylocp->first_column < end_col)
        res += YYFPRINTF (yyo, "-%d", end_col);
    }
  return res;
 }

#  define YY_LOCATION_PRINT(File, Loc)          \
  yy_location_print_ (File, &(Loc))

# else
#  define YY_LOCATION_PRINT(File, Loc) ((void) 0)
# endif
#endif


# define YY_SYMBOL_PRINT(Title, Type, Value, Location)                    \
do {                                                                      \
  if (yydebug)                                                            \
    {                                                                     \
      YYFPRINTF (stderr, "%s ", Title);                                   \
      yy_symbol_print (stderr,                                            \
                  Type, Value, Location, sqlString, sqlResult, scanner); \
      YYFPRINTF (stderr, "\n");                                           \
    }                                                                     \
} while (0)


/*-----------------------------------.
| Print this symbol's value on YYO.  |
`-----------------------------------*/

static void
yy_symbol_value_print (FILE *yyo, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp, const char * sqlString, ParsedSqlResult * sqlResult, void * scanner)
{
  FILE *yyoutput = yyo;
  YYUSE (yyoutput);
  YYUSE (yylocationp);
  YYUSE (sqlString);
  YYUSE (sqlResult);
  YYUSE (scanner);
  if (!yyvaluep)
    return;
# ifdef YYPRINT
  if (yytype < YYNTOKENS)
    YYPRINT (yyo, yytoknum[yytype], *yyvaluep);
# endif
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YYUSE (yytype);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}


/*---------------------------.
| Print this symbol on YYO.  |
`---------------------------*/

static void
yy_symbol_print (FILE *yyo, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp, const char * sqlString, ParsedSqlResult * sqlResult, void * scanner)
{
  YYFPRINTF (yyo, "%s %s (",
             yytype < YYNTOKENS ? "token" : "nterm", yytname[yytype]);

  YY_LOCATION_PRINT (yyo, *yylocationp);
  YYFPRINTF (yyo, ": ");
  yy_symbol_value_print (yyo, yytype, yyvaluep, yylocationp, sqlString, sqlResult, scanner);
  YYFPRINTF (yyo, ")");
}

/*------------------------------------------------------------------.
| yy_stack_print -- Print the state stack from its BOTTOM up to its |
| TOP (included).                                                   |
`------------------------------------------------------------------*/

static void
yy_stack_print (yy_state_t *yybottom, yy_state_t *yytop)
{
  YYFPRINTF (stderr, "Stack now");
  for (; yybottom <= yytop; yybottom++)
    {
      int yybot = *yybottom;
      YYFPRINTF (stderr, " %d", yybot);
    }
  YYFPRINTF (stderr, "\n");
}

# define YY_STACK_PRINT(Bottom, Top)                            \
do {                                                            \
  if (yydebug)                                                  \
    yy_stack_print ((Bottom), (Top));                           \
} while (0)


/*------------------------------------------------.
| Report that the YYRULE is going to be reduced.  |
`------------------------------------------------*/

static void
yy_reduce_print (yy_state_t *yyssp, YYSTYPE *yyvsp, YYLTYPE *yylsp, int yyrule, const char * sqlString, ParsedSqlResult * sqlResult, void * scanner)
{
  int yylno = yyrline[yyrule];
  int yynrhs = yyr2[yyrule];
  int yyi;
  YYFPRINTF (stderr, "Reducing stack by rule %d (line %d):\n",
             yyrule - 1, yylno);
  /* The symbols being reduced.  */
  for (yyi = 0; yyi < yynrhs; yyi++)
    {
      YYFPRINTF (stderr, "   $%d = ", yyi + 1);
      yy_symbol_print (stderr,
                       yystos[+yyssp[yyi + 1 - yynrhs]],
                       &yyvsp[(yyi + 1) - (yynrhs)]
                       , &(yylsp[(yyi + 1) - (yynrhs)])                       , sqlString, sqlResult, scanner);
      YYFPRINTF (stderr, "\n");
    }
}

# define YY_REDUCE_PRINT(Rule)          \
do {                                    \
  if (yydebug)                          \
    yy_reduce_print (yyssp, yyvsp, yylsp, Rule, sqlString, sqlResult, scanner); \
} while (0)

/* Nonzero means print parse trace.  It is left uninitialized so that
   multiple parsers can coexist.  */
int yydebug;
#else /* !YYDEBUG */
# define YYDPRINTF(Args)
# define YY_SYMBOL_PRINT(Title, Type, Value, Location)
# define YY_STACK_PRINT(Bottom, Top)
# define YY_REDUCE_PRINT(Rule)
#endif /* !YYDEBUG */


/* YYINITDEPTH -- initial size of the parser's stacks.  */
#ifndef YYINITDEPTH
# define YYINITDEPTH 200
#endif

/* YYMAXDEPTH -- maximum size the stacks can grow to (effective only
   if the built-in stack extension method is used).

   Do not make this value too large; the results are undefined if
   YYSTACK_ALLOC_MAXIMUM < YYSTACK_BYTES (YYMAXDEPTH)
   evaluated with infinite-precision integer arithmetic.  */

#ifndef YYMAXDEPTH
# define YYMAXDEPTH 10000
#endif


#if YYERROR_VERBOSE

# ifndef yystrlen
#  if defined __GLIBC__ && defined _STRING_H
#   define yystrlen(S) (YY_CAST (YYPTRDIFF_T, strlen (S)))
#  else
/* Return the length of YYSTR.  */
static YYPTRDIFF_T
yystrlen (const char *yystr)
{
  YYPTRDIFF_T yylen;
  for (yylen = 0; yystr[yylen]; yylen++)
    continue;
  return yylen;
}
#  endif
# endif

# ifndef yystpcpy
#  if defined __GLIBC__ && defined _STRING_H && defined _GNU_SOURCE
#   define yystpcpy stpcpy
#  else
/* Copy YYSRC to YYDEST, returning the address of the terminating '\0' in
   YYDEST.  */
static char *
yystpcpy (char *yydest, const char *yysrc)
{
  char *yyd = yydest;
  const char *yys = yysrc;

  while ((*yyd++ = *yys++) != '\0')
    continue;

  return yyd - 1;
}
#  endif
# endif

# ifndef yytnamerr
/* Copy to YYRES the contents of YYSTR after stripping away unnecessary
   quotes and backslashes, so that it's suitable for yyerror.  The
   heuristic is that double-quoting is unnecessary unless the string
   contains an apostrophe, a comma, or backslash (other than
   backslash-backslash).  YYSTR is taken from yytname.  If YYRES is
   null, do not copy; instead, return the length of what the result
   would have been.  */
static YYPTRDIFF_T
yytnamerr (char *yyres, const char *yystr)
{
  if (*yystr == '"')
    {
      YYPTRDIFF_T yyn = 0;
      char const *yyp = yystr;

      for (;;)
        switch (*++yyp)
          {
          case '\'':
          case ',':
            goto do_not_strip_quotes;

          case '\\':
            if (*++yyp != '\\')
              goto do_not_strip_quotes;
            else
              goto append;

          append:
          default:
            if (yyres)
              yyres[yyn] = *yyp;
            yyn++;
            break;

          case '"':
            if (yyres)
              yyres[yyn] = '\0';
            return yyn;
          }
    do_not_strip_quotes: ;
    }

  if (yyres)
    return yystpcpy (yyres, yystr) - yyres;
  else
    return yystrlen (yystr);
}
# endif

/* Copy into *YYMSG, which is of size *YYMSG_ALLOC, an error message
   about the unexpected token YYTOKEN for the state stack whose top is
   YYSSP.

   Return 0 if *YYMSG was successfully written.  Return 1 if *YYMSG is
   not large enough to hold the message.  In that case, also set
   *YYMSG_ALLOC to the required number of bytes.  Return 2 if the
   required number of bytes is too large to store.  */
static int
yysyntax_error (YYPTRDIFF_T *yymsg_alloc, char **yymsg,
                yy_state_t *yyssp, int yytoken)
{
  enum { YYERROR_VERBOSE_ARGS_MAXIMUM = 5 };
  /* Internationalized format string. */
  const char *yyformat = YY_NULLPTR;
  /* Arguments of yyformat: reported tokens (one for the "unexpected",
     one per "expected"). */
  char const *yyarg[YYERROR_VERBOSE_ARGS_MAXIMUM];
  /* Actual size of YYARG. */
  int yycount = 0;
  /* Cumulated lengths of YYARG.  */
  YYPTRDIFF_T yysize = 0;

  /* There are many possibilities here to consider:
     - If this state is a consistent state with a default action, then
       the only way this function was invoked is if the default action
       is an error action.  In that case, don't check for expected
       tokens because there are none.
     - The only way there can be no lookahead present (in yychar) is if
       this state is a consistent state with a default action.  Thus,
       detecting the absence of a lookahead is sufficient to determine
       that there is no unexpected or expected token to report.  In that
       case, just report a simple "syntax error".
     - Don't assume there isn't a lookahead just because this state is a
       consistent state with a default action.  There might have been a
       previous inconsistent state, consistent state with a non-default
       action, or user semantic action that manipulated yychar.
     - Of course, the expected token list depends on states to have
       correct lookahead information, and it depends on the parser not
       to perform extra reductions after fetching a lookahead from the
       scanner and before detecting a syntax error.  Thus, state merging
       (from LALR or IELR) and default reductions corrupt the expected
       token list.  However, the list is correct for canonical LR with
       one exception: it will still contain any token that will not be
       accepted due to an error action in a later state.
  */
  if (yytoken != YYEMPTY)
    {
      int yyn = yypact[+*yyssp];
      YYPTRDIFF_T yysize0 = yytnamerr (YY_NULLPTR, yytname[yytoken]);
      yysize = yysize0;
      yyarg[yycount++] = yytname[yytoken];
      if (!yypact_value_is_default (yyn))
        {
          /* Start YYX at -YYN if negative to avoid negative indexes in
             YYCHECK.  In other words, skip the first -YYN actions for
             this state because they are default actions.  */
          int yyxbegin = yyn < 0 ? -yyn : 0;
          /* Stay within bounds of both yycheck and yytname.  */
          int yychecklim = YYLAST - yyn + 1;
          int yyxend = yychecklim < YYNTOKENS ? yychecklim : YYNTOKENS;
          int yyx;

          for (yyx = yyxbegin; yyx < yyxend; ++yyx)
            if (yycheck[yyx + yyn] == yyx && yyx != YYTERROR
                && !yytable_value_is_error (yytable[yyx + yyn]))
              {
                if (yycount == YYERROR_VERBOSE_ARGS_MAXIMUM)
                  {
                    yycount = 1;
                    yysize = yysize0;
                    break;
                  }
                yyarg[yycount++] = yytname[yyx];
                {
                  YYPTRDIFF_T yysize1
                    = yysize + yytnamerr (YY_NULLPTR, yytname[yyx]);
                  if (yysize <= yysize1 && yysize1 <= YYSTACK_ALLOC_MAXIMUM)
                    yysize = yysize1;
                  else
                    return 2;
                }
              }
        }
    }

  switch (yycount)
    {
# define YYCASE_(N, S)                      \
      case N:                               \
        yyformat = S;                       \
      break
    default: /* Avoid compiler warnings. */
      YYCASE_(0, YY_("syntax error"));
      YYCASE_(1, YY_("syntax error, unexpected %s"));
      YYCASE_(2, YY_("syntax error, unexpected %s, expecting %s"));
      YYCASE_(3, YY_("syntax error, unexpected %s, expecting %s or %s"));
      YYCASE_(4, YY_("syntax error, unexpected %s, expecting %s or %s or %s"));
      YYCASE_(5, YY_("syntax error, unexpected %s, expecting %s or %s or %s or %s"));
# undef YYCASE_
    }

  {
    /* Don't count the "%s"s in the final size, but reserve room for
       the terminator.  */
    YYPTRDIFF_T yysize1 = yysize + (yystrlen (yyformat) - 2 * yycount) + 1;
    if (yysize <= yysize1 && yysize1 <= YYSTACK_ALLOC_MAXIMUM)
      yysize = yysize1;
    else
      return 2;
  }

  if (*yymsg_alloc < yysize)
    {
      *yymsg_alloc = 2 * yysize;
      if (! (yysize <= *yymsg_alloc
             && *yymsg_alloc <= YYSTACK_ALLOC_MAXIMUM))
        *yymsg_alloc = YYSTACK_ALLOC_MAXIMUM;
      return 1;
    }

  /* Avoid sprintf, as that infringes on the user's name space.
     Don't have undefined behavior even if the translation
     produced a string with the wrong number of "%s"s.  */
  {
    char *yyp = *yymsg;
    int yyi = 0;
    while ((*yyp = *yyformat) != '\0')
      if (*yyp == '%' && yyformat[1] == 's' && yyi < yycount)
        {
          yyp += yytnamerr (yyp, yyarg[yyi++]);
          yyformat += 2;
        }
      else
        {
          ++yyp;
          ++yyformat;
        }
  }
  return 0;
}
#endif /* YYERROR_VERBOSE */

/*-----------------------------------------------.
| Release the memory associated to this symbol.  |
`-----------------------------------------------*/

static void
yydestruct (const char *yymsg, int yytype, YYSTYPE *yyvaluep, YYLTYPE *yylocationp, const char * sqlString, ParsedSqlResult * sqlResult, void * scanner)
{
  YYUSE (yyvaluep);
  YYUSE (yylocationp);
  YYUSE (sqlString);
  YYUSE (sqlResult);
  YYUSE (scanner);
  if (!yymsg)
    yymsg = "Deleting";
  YY_SYMBOL_PRINT (yymsg, yytype, yyvaluep, yylocationp);

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  switch (yytype)
    {
    case 79: /* ID  */
            { my_free(((*yyvaluep).string)); }
        break;

    case 80: /* DATE_TIME_STR  */
            { my_free(((*yyvaluep).string)); }
        break;

    case 81: /* DATE_STR  */
            { my_free(((*yyvaluep).string)); }
        break;

    case 82: /* SSS  */
            { my_free(((*yyvaluep).string)); }
        break;

    case 90: /* commandWrapper  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 91: /* exitStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 92: /* helpStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 93: /* syncStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 94: /* beginStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 95: /* commitStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 96: /* rollbackStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 97: /* dropTableStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 98: /* showTablesStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 99: /* descTableStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 100: /* createIndexStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 101: /* dropIndexStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 102: /* createTableStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 103: /* attrDefList  */
            { varArrayListDestroy(&((*yyvaluep).attrInfos)); }
        break;

    case 104: /* attrDef  */
            { AttrInfoSqlNodeDestroy(((*yyvaluep).attrInfo)); }
        break;

    case 108: /* insertStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 109: /* insertColList  */
            { varArrayListDestroy(&((*yyvaluep).relationList)); }
        break;

    case 110: /* idxColList  */
            { varArrayListDestroy(&((*yyvaluep).relationList)); }
        break;

    case 111: /* insertValueList  */
            { varArrayListDestroy(&((*yyvaluep).insertValueList)); }
        break;

    case 112: /* insertValue  */
            { varArrayListDestroy(&((*yyvaluep).valueList)); }
        break;

    case 113: /* valueList  */
            { varArrayListDestroy(&((*yyvaluep).valueList)); }
        break;

    case 114: /* value  */
            { valueDestroy(&((*yyvaluep).value)); }
        break;

    case 115: /* deleteStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 116: /* updateStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 117: /* updateKvList  */
            { varArrayListDestroy(&((*yyvaluep).updateKvList)); }
        break;

    case 118: /* updateKv  */
            { UpdateKVDestroy(((*yyvaluep).updateKv)); }
        break;

    case 119: /* selectStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 120: /* calcStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 121: /* expressionList  */
            { varArrayListDestroy(&((*yyvaluep).expressionList)); }
        break;

    case 122: /* expression  */
            { exprDestroy(((*yyvaluep).expression)); }
        break;

    case 124: /* aggrFuncExpr  */
            { exprDestroy(((*yyvaluep).expression)); }
        break;

    case 125: /* selectAttr  */
            { varArrayListDestroy(&((*yyvaluep).expressionList)); }
        break;

    case 126: /* relAttr  */
            { RelAttrSqlNodeDestroy(((*yyvaluep).relAttr)); }
        break;

    case 127: /* fromList  */
            { varArrayListDestroy(&((*yyvaluep).innerJoinsList)); }
        break;

    case 128: /* alias  */
            { my_free(((*yyvaluep).string)); }
        break;

    case 129: /* fromNode  */
            { InnerJoinSqlNodeDestroy(((*yyvaluep).innerJoins)); }
        break;

    case 130: /* joinList  */
            { InnerJoinSqlNodeDestroy(((*yyvaluep).innerJoins)); }
        break;

    case 131: /* subQueryExpr  */
            { exprDestroy(((*yyvaluep).expression)); }
        break;

    case 132: /* where  */
            { exprDestroy(((*yyvaluep).expression)); }
        break;

    case 133: /* condition  */
            { exprDestroy(((*yyvaluep).expression)); }
        break;

    case 134: /* sortUnit  */
            { OrderBySqlNodeDestroy(((*yyvaluep).orderbyUnit)); }
        break;

    case 135: /* sortList  */
            { varArrayListDestroy(&((*yyvaluep).orderbyUnitList)); }
        break;

    case 136: /* optOrderBy  */
            { varArrayListDestroy(&((*yyvaluep).orderbyUnitList)); }
        break;

    case 137: /* optGroupBy  */
            { varArrayListDestroy(&((*yyvaluep).expressionList)); }
        break;

    case 138: /* optHaving  */
            { exprDestroy(((*yyvaluep).expression)); }
        break;

    case 139: /* optLimit  */
            { LimitSqlNodeDestroy(((*yyvaluep).limit)); }
        break;

    case 142: /* loadDataStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 143: /* explainStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

    case 144: /* setVariableStmt  */
            { ParsedSqlNodeDestroy(((*yyvaluep).sqlNode)); }
        break;

      default:
        break;
    }
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}




/*----------.
| yyparse.  |
`----------*/

int
yyparse (const char * sqlString, ParsedSqlResult * sqlResult, void * scanner)
{
/* The lookahead symbol.  */
int yychar;


/* The semantic value of the lookahead symbol.  */
/* Default value used for initialization, for pacifying older GCCs
   or non-GCC compilers.  */
YY_INITIAL_VALUE (static YYSTYPE yyval_default;)
YYSTYPE yylval YY_INITIAL_VALUE (= yyval_default);

/* Location data for the lookahead symbol.  */
static YYLTYPE yyloc_default
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL
  = { 1, 1, 1, 1 }
# endif
;
YYLTYPE yylloc = yyloc_default;

    /* Number of syntax errors so far.  */
    int yynerrs;

    yy_state_fast_t yystate;
    /* Number of tokens to shift before error messages enabled.  */
    int yyerrstatus;

    /* The stacks and their tools:
       'yyss': related to states.
       'yyvs': related to semantic values.
       'yyls': related to locations.

       Refer to the stacks through separate pointers, to allow yyoverflow
       to reallocate them elsewhere.  */

    /* The state stack.  */
    yy_state_t yyssa[YYINITDEPTH];
    yy_state_t *yyss;
    yy_state_t *yyssp;

    /* The semantic value stack.  */
    YYSTYPE yyvsa[YYINITDEPTH];
    YYSTYPE *yyvs;
    YYSTYPE *yyvsp;

    /* The location stack.  */
    YYLTYPE yylsa[YYINITDEPTH];
    YYLTYPE *yyls;
    YYLTYPE *yylsp;

    /* The locations where the error started and ended.  */
    YYLTYPE yyerror_range[3];

    YYPTRDIFF_T yystacksize;

  int yyn;
  int yyresult;
  /* Lookahead token as an internal (translated) token number.  */
  int yytoken = 0;
  /* The variables used to return semantic value and location from the
     action routines.  */
  YYSTYPE yyval;
  YYLTYPE yyloc;

#if YYERROR_VERBOSE
  /* Buffer for error messages, and its allocated size.  */
  char yymsgbuf[128];
  char *yymsg = yymsgbuf;
  YYPTRDIFF_T yymsg_alloc = sizeof yymsgbuf;
#endif

#define YYPOPSTACK(N)   (yyvsp -= (N), yyssp -= (N), yylsp -= (N))

  /* The number of symbols on the RHS of the reduced rule.
     Keep to zero when no symbol should be popped.  */
  int yylen = 0;

  yyssp = yyss = yyssa;
  yyvsp = yyvs = yyvsa;
  yylsp = yyls = yylsa;
  yystacksize = YYINITDEPTH;

  YYDPRINTF ((stderr, "Starting parse\n"));

  yystate = 0;
  yyerrstatus = 0;
  yynerrs = 0;
  yychar = YYEMPTY; /* Cause a token to be read.  */
  yylsp[0] = yylloc;
  goto yysetstate;


/*------------------------------------------------------------.
| yynewstate -- push a new state, which is found in yystate.  |
`------------------------------------------------------------*/
yynewstate:
  /* In all cases, when you get here, the value and location stacks
     have just been pushed.  So pushing a state here evens the stacks.  */
  yyssp++;


/*--------------------------------------------------------------------.
| yysetstate -- set current state (the top of the stack) to yystate.  |
`--------------------------------------------------------------------*/
yysetstate:
  YYDPRINTF ((stderr, "Entering state %d\n", yystate));
  YY_ASSERT (0 <= yystate && yystate < YYNSTATES);
  YY_IGNORE_USELESS_CAST_BEGIN
  *yyssp = YY_CAST (yy_state_t, yystate);
  YY_IGNORE_USELESS_CAST_END

  if (yyss + yystacksize - 1 <= yyssp)
#if !defined yyoverflow && !defined YYSTACK_RELOCATE
    goto yyexhaustedlab;
#else
    {
      /* Get the current used size of the three stacks, in elements.  */
      YYPTRDIFF_T yysize = yyssp - yyss + 1;

# if defined yyoverflow
      {
        /* Give user a chance to reallocate the stack.  Use copies of
           these so that the &'s don't force the real ones into
           memory.  */
        yy_state_t *yyss1 = yyss;
        YYSTYPE *yyvs1 = yyvs;
        YYLTYPE *yyls1 = yyls;

        /* Each stack pointer address is followed by the size of the
           data in use in that stack, in bytes.  This used to be a
           conditional around just the two extra args, but that might
           be undefined if yyoverflow is a macro.  */
        yyoverflow (YY_("memory exhausted"),
                    &yyss1, yysize * YYSIZEOF (*yyssp),
                    &yyvs1, yysize * YYSIZEOF (*yyvsp),
                    &yyls1, yysize * YYSIZEOF (*yylsp),
                    &yystacksize);
        yyss = yyss1;
        yyvs = yyvs1;
        yyls = yyls1;
      }
# else /* defined YYSTACK_RELOCATE */
      /* Extend the stack our own way.  */
      if (YYMAXDEPTH <= yystacksize)
        goto yyexhaustedlab;
      yystacksize *= 2;
      if (YYMAXDEPTH < yystacksize)
        yystacksize = YYMAXDEPTH;

      {
        yy_state_t *yyss1 = yyss;
        union yyalloc *yyptr =
          YY_CAST (union yyalloc *,
                   YYSTACK_ALLOC (YY_CAST (YYSIZE_T, YYSTACK_BYTES (yystacksize))));
        if (! yyptr)
          goto yyexhaustedlab;
        YYSTACK_RELOCATE (yyss_alloc, yyss);
        YYSTACK_RELOCATE (yyvs_alloc, yyvs);
        YYSTACK_RELOCATE (yyls_alloc, yyls);
# undef YYSTACK_RELOCATE
        if (yyss1 != yyssa)
          YYSTACK_FREE (yyss1);
      }
# endif

      yyssp = yyss + yysize - 1;
      yyvsp = yyvs + yysize - 1;
      yylsp = yyls + yysize - 1;

      YY_IGNORE_USELESS_CAST_BEGIN
      YYDPRINTF ((stderr, "Stack size increased to %ld\n",
                  YY_CAST (long, yystacksize)));
      YY_IGNORE_USELESS_CAST_END

      if (yyss + yystacksize - 1 <= yyssp)
        YYABORT;
    }
#endif /* !defined yyoverflow && !defined YYSTACK_RELOCATE */

  if (yystate == YYFINAL)
    YYACCEPT;

  goto yybackup;


/*-----------.
| yybackup.  |
`-----------*/
yybackup:
  /* Do appropriate processing given the current state.  Read a
     lookahead token if we need one and don't already have one.  */

  /* First try to decide what to do without reference to lookahead token.  */
  yyn = yypact[yystate];
  if (yypact_value_is_default (yyn))
    goto yydefault;

  /* Not known => get a lookahead token if don't already have one.  */

  /* YYCHAR is either YYEMPTY or YYEOF or a valid lookahead symbol.  */
  if (yychar == YYEMPTY)
    {
      YYDPRINTF ((stderr, "Reading a token: "));
      yychar = yylex (&yylval, &yylloc, scanner);
    }

  if (yychar <= YYEOF)
    {
      yychar = yytoken = YYEOF;
      YYDPRINTF ((stderr, "Now at end of input.\n"));
    }
  else
    {
      yytoken = YYTRANSLATE (yychar);
      YY_SYMBOL_PRINT ("Next token is", yytoken, &yylval, &yylloc);
    }

  /* If the proper action on seeing token YYTOKEN is to reduce or to
     detect an error, take that action.  */
  yyn += yytoken;
  if (yyn < 0 || YYLAST < yyn || yycheck[yyn] != yytoken)
    goto yydefault;
  yyn = yytable[yyn];
  if (yyn <= 0)
    {
      if (yytable_value_is_error (yyn))
        goto yyerrlab;
      yyn = -yyn;
      goto yyreduce;
    }

  /* Count tokens shifted since error; after three, turn off error
     status.  */
  if (yyerrstatus)
    yyerrstatus--;

  /* Shift the lookahead token.  */
  YY_SYMBOL_PRINT ("Shifting", yytoken, &yylval, &yylloc);
  yystate = yyn;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END
  *++yylsp = yylloc;

  /* Discard the shifted token.  */
  yychar = YYEMPTY;
  goto yynewstate;


/*-----------------------------------------------------------.
| yydefault -- do the default action for the current state.  |
`-----------------------------------------------------------*/
yydefault:
  yyn = yydefact[yystate];
  if (yyn == 0)
    goto yyerrlab;
  goto yyreduce;


/*-----------------------------.
| yyreduce -- do a reduction.  |
`-----------------------------*/
yyreduce:
  /* yyn is the number of a rule to reduce with.  */
  yylen = yyr2[yyn];

  /* If YYLEN is nonzero, implement the default value of the action:
     '$$ = $1'.

     Otherwise, the following line sets YYVAL to garbage.
     This behavior is undocumented and Bison
     users should not rely upon it.  Assigning to YYVAL
     unconditionally makes the parser a bit smaller, and it avoids a
     GCC warning that YYVAL may be used uninitialized.  */
  yyval = yyvsp[1-yylen];

  /* Default location. */
  YYLLOC_DEFAULT (yyloc, (yylsp - yylen), yylen);
  yyerror_range[1] = yyloc;
  YY_REDUCE_PRINT (yyn);
  switch (yyn)
    {
  case 2:
  {
    varArrayListAddPointer(sqlResult->sqlNodes, (yyvsp[-1].sqlNode));
    sqlResult->count++;
  }
    break;

  case 23:
         {
      (void)yynerrs;  
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_EXIT);
    }
    break;

  case 24:
         {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_HELP);
    }
    break;

  case 25:
         {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_SYNC);
    }
    break;

  case 26:
               {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_BEGIN);
    }
    break;

  case 27:
               {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_COMMIT);
    }
    break;

  case 28:
                  {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_ROLLBACK);
    }
    break;

  case 29:
                            {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_DROP_TABLE);
      (yyval.sqlNode)->dropTable->relationName = ((yyvsp[0].string));
      (yyval.sqlNode)->dropTable->ifExists = true; /* 添加一个标志来表示 'IF EXISTS' */
    }
    break;

  case 30:
                  {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_DROP_TABLE);
      (yyval.sqlNode)->dropTable->relationName = ((yyvsp[0].string));
      (yyval.sqlNode)->dropTable->ifExists = false; /* 默认情况下没有 'IF EXISTS' */
    }
    break;

  case 31:
                {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_SHOW_TABLES);
    }
    break;

  case 32:
             {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_DESC_TABLE);
      (yyval.sqlNode)->descTable->relationName = ((yyvsp[0].string));
    }
    break;

  case 33:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_CREATE_INDEX);
      (yyval.sqlNode)->createIndex->indexName = ((yyvsp[-5].string));
      (yyval.sqlNode)->createIndex->relationName = ((yyvsp[-3].string));
      (yyval.sqlNode)->createIndex->attributeName = ((yyvsp[-1].string));
      (yyval.sqlNode)->createIndex->indexType = INDEX_TYPE_BTREE; // 默认B+树索引
    }
    break;

  case 34:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_CREATE_INDEX);
      (yyval.sqlNode)->createIndex->indexName = ((yyvsp[-7].string));
      (yyval.sqlNode)->createIndex->relationName = ((yyvsp[-5].string));
      (yyval.sqlNode)->createIndex->attributeName = ((yyvsp[-3].string));
      (yyval.sqlNode)->createIndex->indexType = INDEX_TYPE_HASH; // 哈希索引
    }
    break;

  case 35:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_DROP_INDEX);
      (yyval.sqlNode)->dropIndex->indexName = ((yyvsp[-2].string));
      (yyval.sqlNode)->dropIndex->relationName = ((yyvsp[0].string));
    }
    break;

  case 36:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_CREATE_TABLE);
      (yyval.sqlNode)->createTable->relationName = ((yyvsp[-4].string));
      if((yyvsp[-1].attrInfos) != NULL){
        (yyval.sqlNode)->createTable->attrInfos = (yyvsp[-1].attrInfos);
      }
      else{
        (yyval.sqlNode)->createTable->attrInfos = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AttrInfoSqlNodePointerDestroy);
      }
      varArrayListAddPointer((yyval.sqlNode)->createTable->attrInfos, (yyvsp[-2].attrInfo));
      varArrayListReverse((yyval.sqlNode)->createTable->attrInfos);
    }
    break;

  case 37:
    {
      // CreateTableSqlNode* createTable = NULL;
      (yyval.sqlNode) = (yyvsp[0].sqlNode);
      (yyval.sqlNode)->flag = SCF_CREATE_TABLE;
      (yyval.sqlNode)->createTable = CreateTableSqlNodeCreate(NULL, NULL);
      ((yyval.sqlNode)->createTable)->relationName = ((yyvsp[-6].string));
      if((yyvsp[-3].attrInfos) != NULL){
        ((yyval.sqlNode)->createTable)->attrInfos = (yyvsp[-3].attrInfos);
      }
      else{
        ((yyval.sqlNode)->createTable)->attrInfos = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AttrInfoSqlNodePointerDestroy);
      }
      varArrayListAddPointer(((yyval.sqlNode)->createTable)->attrInfos, (yyvsp[-4].attrInfo));
      varArrayListReverse(((yyval.sqlNode)->createTable)->attrInfos);
      ((yyval.sqlNode)->createTable)->select = ptrMove((void**)&(yyvsp[0].sqlNode));
    }
    break;

  case 38:
    {
      (yyval.sqlNode) = (yyvsp[0].sqlNode);
      (yyval.sqlNode)->flag = SCF_CREATE_TABLE;
      (yyval.sqlNode)->createTable = CreateTableSqlNodeCreate(NULL, NULL);
      (yyval.sqlNode)->createTable->relationName = ((yyvsp[-2].string));
      ((yyval.sqlNode)->createTable)->select = ptrMove((void**)&(yyvsp[0].sqlNode));
    }
    break;

  case 39:
    {
      (yyval.attrInfos) = NULL;
    }
    break;

  case 40:
    {
      if ((yyvsp[0].attrInfos) != NULL) {
        (yyval.attrInfos) = (yyvsp[0].attrInfos);
      } else {
        (yyval.attrInfos) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AttrInfoSqlNodePointerDestroy);
      }
      varArrayListAddPointer((yyval.attrInfos), (yyvsp[-1].attrInfo));
    }
    break;

  case 41:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[-3].number), (yyvsp[-4].string), (yyvsp[-1].number), false, true);
    }
    break;

  case 42:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[-5].number), (yyvsp[-6].string), (yyvsp[-3].number), true, false);
    }
    break;

  case 43:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[0].number), (yyvsp[-1].string), 4, false, true);
    }
    break;

  case 44:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[-2].number), (yyvsp[-3].string), 4, true, false);
    }
    break;

  case 45:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[-5].number), (yyvsp[-6].string), (yyvsp[-3].number), false, false);
    }
    break;

  case 46:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[-7].number), (yyvsp[-8].string), (yyvsp[-5].number), true, false);
    }
    break;

  case 47:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[-2].number), (yyvsp[-3].string), 4, false, false);
    }
    break;

  case 48:
    {
      (yyval.attrInfo) = AttrInfoSqlNodeCreate((AttrType)(yyvsp[-4].number), (yyvsp[-5].string), 4, true, false);
    }
    break;

  case 49:
    {
      (yyval.boolean) = false;
    }
    break;

  case 50:
    {
      (yyval.boolean) = false;
    }
    break;

  case 51:
           {(yyval.number) = (yyvsp[0].number);}
    break;

  case 52:
               { (yyval.number)=INTS; }
    break;

  case 53:
               { (yyval.number)=DATES; }
    break;

  case 54:
               { (yyval.number)=CHARS; }
    break;

  case 55:
               { (yyval.number)=DOUBLES; }
    break;

  case 56:
                 { (yyval.number)=DATETIMES; }
    break;

  case 57:
              {(yyval.number) = TEXTS;}
    break;

  case 58:
              {(yyval.number) = BLOB;}
    break;

  case 59:
    { 
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_INSERT);
      (yyval.sqlNode)->insertion->relationName = ((yyvsp[-4].string));

      /* 插入的值lists */
      if((yyvsp[0].insertValueList) != NULL){
        (yyval.sqlNode)->insertion->valuelists = (yyvsp[0].insertValueList);
      }
      else{
        (yyval.sqlNode)->insertion->valuelists = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, varArrayListPointerDestroy);
      }
      varArrayListAddPointer((yyval.sqlNode)->insertion->valuelists, (yyvsp[-1].valueList));
      varArrayListReverse((yyval.sqlNode)->insertion->valuelists);

      /* 插入的列list */
      if((yyvsp[-3].relationList) != NULL){
        (yyval.sqlNode)->insertion->attributes = (yyvsp[-3].relationList);
        varArrayListReverse((yyval.sqlNode)->insertion->attributes);
      }
      else{
        (yyval.sqlNode)->insertion->attributes = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      }
    }
    break;

  case 60:
    {
      (yyval.relationList) = NULL;
    }
    break;

  case 61:
    {
      if ((yyvsp[-1].relationList) != NULL) {
        (yyval.relationList) = (yyvsp[-1].relationList);
      } else {
        (yyval.relationList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      }
      varArrayListAddPointer((yyval.relationList), (yyvsp[-2].string));
    }
    break;

  case 62:
    {
      (yyval.relationList) = NULL;
    }
    break;

  case 63:
    {
      if ((yyvsp[0].relationList) != NULL) {
        (yyval.relationList) = (yyvsp[0].relationList);
      } else {
        (yyval.relationList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      }
      varArrayListAddPointer((yyval.relationList), (yyvsp[-1].string));
    }
    break;

  case 64:
    {
      (yyval.insertValueList) = NULL;
    }
    break;

  case 65:
    {
      if ((yyvsp[0].insertValueList) != NULL) {
        (yyval.insertValueList) = (yyvsp[0].insertValueList);
      } else {
        (yyval.insertValueList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, varArrayListPointerDestroy);
      }
      varArrayListAddPointer((yyval.insertValueList), (yyvsp[-1].valueList));
    }
    break;

  case 66:
    {
      value = valueCreate();
      if(!insertExprToValue((yyvsp[-2].expression), value)) {
        exprDestroy((yyvsp[-2].expression));
        varArrayListDestroy(&(yyvsp[-1].valueList));
        valueDestroy(&value);
        yyerror(&(yyloc), sqlString, sqlResult, scanner, "error");
        YYERROR;
      }
      if ((yyvsp[-1].valueList) != NULL) {
        (yyval.valueList) = (yyvsp[-1].valueList);
      } else {
        (yyval.valueList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, valuePointerDestroy);
      }
      varArrayListAddPointer((yyval.valueList), value);
      varArrayListReverse((yyval.valueList));
      exprDestroy((yyvsp[-2].expression));
    }
    break;

  case 67:
    {
      (yyval.valueList) = NULL;
    }
    break;

  case 68:
                                  { 
      value = valueCreate();
      // memset(value, 0, sizeof(Value));
      if(!insertExprToValue((yyvsp[-1].expression), value)) {
        exprDestroy((yyvsp[-1].expression));
        varArrayListDestroy(&(yyvsp[0].valueList));
        valueDestroy(&value);
        yyerror(&(yyloc), sqlString, sqlResult, scanner, "error");
        YYERROR;
      }
      if ((yyvsp[0].valueList) != NULL) {
        (yyval.valueList) = (yyvsp[0].valueList);
      } else {
        (yyval.valueList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, valuePointerDestroy);
      }

      varArrayListAddPointer((yyval.valueList), value);
      // TODO: free $2
      exprDestroy((yyvsp[-1].expression));
    }
    break;

  case 69:
           {
      (yyval.value) = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetInt((yyval.value), (int)(yyvsp[0].number));
      (yyloc) = (yylsp[0]);
    }
    break;

  case 70:
           {
      (yyval.value) = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetDouble((yyval.value), (double)(yyvsp[0].floats));
      (yyloc) = (yylsp[0]);
    }
    break;

  case 71:
              {
      str = substr((yyvsp[0].string),1,strlen((yyvsp[0].string))-2);
      
      (yyval.value) = valueCreate();
      // memset($$, 0, sizeof(Value));
      if(stringToDate((str), &date) == GNCDB_SUCCESS){
        valueSetDate((yyval.value), date);
      }
      else{
        my_free((yyvsp[0].string));
        my_free(str);
        valueDestroy(&(yyval.value));
        yyerror(&(yyloc), sqlString, sqlResult, scanner, "invalid date format");
        YYERROR;
      }
      my_free((yyvsp[0].string));
      my_free(str);
    }
    break;

  case 72:
                   {
      str = substr((yyvsp[0].string),1,strlen((yyvsp[0].string))-2);
      // DateTime date;
      (yyval.value) = valueCreate();
      // memset($$, 0, sizeof(Value));
      if(stringToDatetime((str), &dateTime) == GNCDB_SUCCESS){
        valueSetDatetime((yyval.value), dateTime);
      }
      else{
        my_free((yyvsp[0].string));
        my_free(str);
        valueDestroy(&(yyval.value));
        yyerror(&(yyloc), sqlString, sqlResult, scanner, "invalid date format");
        YYERROR;
      }
      my_free((yyvsp[0].string));
      my_free(str);
    }
    break;

  case 73:
         {
      str = substr((yyvsp[0].string),1,strlen((yyvsp[0].string))-2);
      (yyval.value) = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetString((yyval.value), (str));
      my_free((yyvsp[0].string));
      my_free(str);
    }
    break;

  case 74:
            {
      (yyval.value) = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetNull((yyval.value));
    }
    break;

  case 75:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_DELETE);
      (yyval.sqlNode)->deletion->relationName = ((yyvsp[-1].string));
      (yyval.sqlNode)->deletion->conditions = (yyvsp[0].expression);
    }
    break;

  case 76:
    {
      // varArrayList* src = NULL;
      // j = 0;
      kv = NULL;
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_UPDATE);
      (yyval.sqlNode)->update->relationName = (yyvsp[-4].string);
      (yyval.sqlNode)->update->attributeNames = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      (yyval.sqlNode)->update->values = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      varArrayListAddPointer((yyval.sqlNode)->update->attributeNames, strdup((yyvsp[-2].updateKv)->attrName));
      varArrayListAddPointer((yyval.sqlNode)->update->values, exprDeepCopy((yyvsp[-2].updateKv)->value));
      UpdateKVDestroy((yyvsp[-2].updateKv));
      if((yyvsp[-1].updateKvList) != NULL){
        // src = $5;
        for(j = 0; j < (yyvsp[-1].updateKvList)->elementCount; j++){
          kv = varArrayListGetPointer((yyvsp[-1].updateKvList), j);
          // TODO: 更改add逻辑，避免重复分配内存
          // varArrayListAddPointer($$->update->attributeNames, strdup(kv->attrName));
          // varArrayListAddPointer($$->update->values, exprDeepCopy(kv->value));          
          varArrayListAddPointer((yyval.sqlNode)->update->attributeNames, (kv->attrName));
          varArrayListAddPointer((yyval.sqlNode)->update->values, (kv->value));
          kv->attrName = NULL;
          kv->value = NULL;
        }
        varArrayListDestroy(&(yyvsp[-1].updateKvList));
      }
      (yyval.sqlNode)->update->conditions = (yyvsp[0].expression);
    }
    break;

  case 77:
    {
      (yyval.updateKvList) = NULL;
    }
    break;

  case 78:
    {
      if ((yyvsp[0].updateKvList) != NULL) {
        (yyval.updateKvList) = (yyvsp[0].updateKvList);
      } else {
        (yyval.updateKvList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, UpdateKVPointerDestroy);
      }
      varArrayListAddPointer((yyval.updateKvList), (yyvsp[-1].updateKv));
    }
    break;

  case 79:
    {
      (yyval.updateKv) = UpdateKVCreate((yyvsp[-2].string), (yyvsp[0].expression));
    }
    break;

  case 80:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_SELECT);
      // projects
      if((yyvsp[-8].expressionList) != NULL){
        (yyval.sqlNode)->selection->projectExprs = (yyvsp[-8].expressionList);
      }
      else{
        (yyval.sqlNode)->selection->projectExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }
      (yyval.sqlNode)->selection->isDistinct = false;

      // from clause
      if((yyvsp[-5].innerJoinsList) != NULL){
        (yyval.sqlNode)->selection->relations = (yyvsp[-5].innerJoinsList);
      }
      else{
        (yyval.sqlNode)->selection->relations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, InnerJoinSqlNodePointerDestroy);
      }
      varArrayListAddPointer((yyval.sqlNode)->selection->relations, (yyvsp[-6].innerJoins));
      varArrayListReverse((yyval.sqlNode)->selection->relations);

      // where clause
      (yyval.sqlNode)->selection->conditions = (yyvsp[-4].expression);
      

      // group by clause
      if(NULL != (yyvsp[-3].expressionList)){
        (yyval.sqlNode)->selection->groupbyExprs = (yyvsp[-3].expressionList);
      }
      else{
        (yyval.sqlNode)->selection->groupbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }

      // having condition
      (yyval.sqlNode)->selection->havingConditions = (yyvsp[-2].expression);

      // order by clause
      if(NULL != (yyvsp[-1].orderbyUnitList)){
        (yyval.sqlNode)->selection->orderbyExprs = (yyvsp[-1].orderbyUnitList);
      }
      else{
        (yyval.sqlNode)->selection->orderbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, OrderBySqlNodePointerDestroy);
      }

      // limit clause
      (yyval.sqlNode)->selection->limit = (yyvsp[0].limit);

    }
    break;

  case 81:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_SELECT);
      // projects
      if((yyvsp[-8].expressionList) != NULL){
        (yyval.sqlNode)->selection->projectExprs = (yyvsp[-8].expressionList);
      }
      else{
        (yyval.sqlNode)->selection->projectExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }
      (yyval.sqlNode)->selection->isDistinct = true;

      // from clause
      if((yyvsp[-5].innerJoinsList) != NULL){
        (yyval.sqlNode)->selection->relations = (yyvsp[-5].innerJoinsList);
      }
      else{
        (yyval.sqlNode)->selection->relations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, InnerJoinSqlNodePointerDestroy);
      }
      varArrayListAddPointer((yyval.sqlNode)->selection->relations, (yyvsp[-6].innerJoins));
      varArrayListReverse((yyval.sqlNode)->selection->relations);

      // where clause
      (yyval.sqlNode)->selection->conditions = (yyvsp[-4].expression);
      

      // group by clause
      if(NULL != (yyvsp[-3].expressionList)){
        (yyval.sqlNode)->selection->groupbyExprs = (yyvsp[-3].expressionList);
      }
      else{
        (yyval.sqlNode)->selection->groupbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }

      // having condition
      (yyval.sqlNode)->selection->havingConditions = (yyvsp[-2].expression);

      // order by clause
      if(NULL != (yyvsp[-1].orderbyUnitList)){
        (yyval.sqlNode)->selection->orderbyExprs = (yyvsp[-1].orderbyUnitList);
      }
      else{
        (yyval.sqlNode)->selection->orderbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, OrderBySqlNodePointerDestroy);
      }

      // limit clause
      (yyval.sqlNode)->selection->limit = (yyvsp[0].limit);

    }
    break;

  case 82:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_CALC);
      varArrayListReverse((yyvsp[0].expressionList));
      (yyval.sqlNode)->calc->expressions = (yyvsp[0].expressionList);
    }
    break;

  case 83:
    {
      (yyval.expressionList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      (yyvsp[-1].expression)->alias = (yyvsp[0].string) ;
      varArrayListAddPointer((yyval.expressionList), (yyvsp[-1].expression));
    }
    break;

  case 84:
    {
      if((yyvsp[0].expressionList) != NULL){
        (yyval.expressionList) = (yyvsp[0].expressionList);
      }
      else{
        (yyval.expressionList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }
      (yyvsp[-3].expression)->alias = ((yyvsp[-2].string));
      varArrayListAddPointer((yyval.expressionList), (yyvsp[-3].expression));
    }
    break;

  case 85:
                              {
      (yyval.expression) = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_ADD, (yyvsp[-2].expression), (yyvsp[0].expression), sqlString, &(yyloc));
    }
    break;

  case 86:
                                {
      (yyval.expression) = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_SUB, (yyvsp[-2].expression), (yyvsp[0].expression), sqlString, &(yyloc));
    }
    break;

  case 87:
                                {
      (yyval.expression) = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_MUL, (yyvsp[-2].expression), (yyvsp[0].expression), sqlString, &(yyloc));
    }
    break;

  case 88:
                                {
      (yyval.expression) = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_DIV, (yyvsp[-2].expression), (yyvsp[0].expression), sqlString, &(yyloc));
    }
    break;

  case 89:
                                   {
      exprListExpr = NULL;
      if((yyvsp[-1].expressionList)->elementCount == 1){
         (yyval.expression) = varArrayListGetPointer((yyvsp[-1].expressionList), 0);
         varArrayListRemoveByIndexPointer((yyvsp[-1].expressionList), 0);
         varArrayListDestroy(&(yyvsp[-1].expressionList));
      }
      else{
        exprListExpr = exprCreate(ETG_EXPRLIST);
        exprListExpr->exprs = ptrMove((void**)&(yyvsp[-1].expressionList));

        (yyval.expression) = (Expression*)exprListExpr;
      }
      if((yyval.expression)->name != NULL){
        my_free((yyval.expression)->name);
      }
      (yyval.expression)->name = tokenName(sqlString, &(yyloc));
    }
    break;

  case 90:
                                  {
      (yyval.expression) = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_NEGATIVE, (yyvsp[0].expression), NULL, sqlString, &(yyloc));

    }
    break;

  case 91:
            {
      (yyval.expression) = exprCreate(ETG_VALUE);
      ((ValueExpr*)((yyval.expression)))->value = (yyvsp[0].value);
      ((ValueExpr*)((yyval.expression)))->name = tokenName(sqlString, &(yyloc));
    }
    break;

  case 92:
             {
      (yyval.expression) = exprCreate(ETG_FIELD);
      ((FieldExpr*)((yyval.expression)))->tableName = (yyvsp[0].relAttr)->relationName == NULL ? NULL : strdup((yyvsp[0].relAttr)->relationName);
      ((FieldExpr*)((yyval.expression)))->fieldName = strdup((yyvsp[0].relAttr)->attributeName);
      RelAttrSqlNodeDestroy((yyvsp[0].relAttr));
    }
    break;

  case 93:
                  {
      (yyval.expression) = (yyvsp[0].expression);
    }
    break;

  case 94:
                  {
      (yyval.expression) = (yyvsp[0].expression);
    }
    break;

  case 95:
             {
      (yyval.number) = AGG_MAX;
    }
    break;

  case 96:
               {
      (yyval.number) = AGG_MIN;
    }
    break;

  case 97:
               {
      (yyval.number) = AGG_SUM;
    }
    break;

  case 98:
               {
      (yyval.number) = AGG_AVG;
    }
    break;

  case 99:
                 {
      (yyval.number) = AGG_COUNT;
    }
    break;

  case 100:
    {
      afexpr = exprCreate(ETG_AGGRFUNC);
      afexpr->name = tokenName(sqlString, &(yyloc));
      afexpr->aggrType = (AggrFuncType)(yyvsp[-3].number);
      afexpr->param = (yyvsp[-1].expression);
      (yyval.expression) = (Expression*)afexpr;
    }
    break;

  case 101:
    {
      valueExpr = NULL;
      afexpr = NULL;
      if((yyvsp[-3].number) != AGG_COUNT){
        yyerror(&(yyloc), sqlString, sqlResult, scanner, "only support count(*)");
        YYERROR;
      }
      value = valueCreate();
      // memset(value, 0, sizeof(Value));
      valueSetInt(value, 1);  /* 这里用int值1代表是COUNT(*) */
      valueExpr = exprCreate(ETG_VALUE);
      valueExpr->value = value;
      valueExpr->name = strdup("*");
      afexpr = exprCreate(ETG_AGGRFUNC);
      afexpr->name = tokenName(sqlString, &(yyloc));
      afexpr->aggrType = (AggrFuncType)(yyvsp[-3].number);
      afexpr->param = (Expression*)valueExpr;
      (yyval.expression) = (Expression*)afexpr;
    }
    break;

  case 102:
        {
      fieldExpr = NULL;
      (yyval.expressionList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);// Expression *
      fieldExpr = exprCreate(ETG_FIELD);
      fieldExpr->tableName = strdup("*");
      fieldExpr->fieldName = strdup("*");
      fieldExpr->name = tokenName(sqlString, &(yyloc));
      varArrayListAddPointer((yyval.expressionList), (Expression*)fieldExpr);
    }
    break;

  case 103:
                  {
      fieldExpr = NULL;
      (yyval.expressionList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);// Expression *
      fieldExpr = exprCreate(ETG_FIELD);
      fieldExpr->tableName = strdup("*");
      fieldExpr->fieldName = strdup("*");
      fieldExpr->name = tokenName(sqlString, &(yyloc));
      varArrayListAddPointer((yyval.expressionList), (Expression*)fieldExpr);
    }
    break;

  case 104:
                {
      fieldExpr = NULL;
      (yyval.expressionList) = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);// Expression *
      fieldExpr = exprCreate(ETG_FIELD);
      fieldExpr->tableName = strdup((yyvsp[-2].string));
      fieldExpr->fieldName = strdup("*");
      fieldExpr->name = tokenName(sqlString, &(yyloc));
      varArrayListAddPointer((yyval.expressionList), (Expression*)fieldExpr);
      my_free((yyvsp[-2].string));
    }
    break;

  case 105:
                    {
      (yyval.expressionList) = (yyvsp[0].expressionList);
    }
    break;

  case 106:
       {
      (yyval.relAttr) = RelAttrSqlNodeCreate(NULL, (yyvsp[0].string));
    }
    break;

  case 107:
                {
      (yyval.relAttr) = RelAttrSqlNodeCreate((yyvsp[-2].string), (yyvsp[0].string));
    }
    break;

  case 108:
    {
      (yyval.innerJoinsList) = NULL;
    }
    break;

  case 109:
                              {
      if(NULL == (yyvsp[0].innerJoinsList)){
        (yyval.innerJoinsList) = varArrayListCreate(DISORDER, sizeof(InnerJoinSqlNode*), 0, NULL, InnerJoinSqlNodePointerDestroy);
      }
      else{
        (yyval.innerJoinsList) = (yyvsp[0].innerJoinsList);
      }
      varArrayListAddPointer((yyval.innerJoinsList), (yyvsp[-1].innerJoins));
    }
    break;

  case 110:
                {
      (yyval.string) = NULL;
    }
    break;

  case 111:
         {
      (yyval.string) = (yyvsp[0].string);
    }
    break;

  case 112:
            {
      (yyval.string) = (yyvsp[0].string);
    }
    break;

  case 113:
                     {
      // joinRelations = NULL;
      // conditions = NULL;
      // baseRelation = NULL;
      if(NULL != (yyvsp[0].innerJoins)){
        (yyval.innerJoins) = (yyvsp[0].innerJoins);
      }
      else{
        // $$ = (InnerJoinSqlNode*)my_malloc(sizeof(InnerJoinSqlNode));
        // $$->joinRelations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy);
        // $$->conditions = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
        // $$->baseRelation = (StringPair*)my_malloc(sizeof(StringPair));
        // // $$ = InnerJoinSqlNodeCreate(baseRelation, joinRelations, conditions);
        (yyval.innerJoins) = InnerJoinSqlNodeCreate(StringPairCreate(NULL, NULL), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy));
      }
      (yyval.innerJoins)->baseRelation->name = ((yyvsp[-2].string));
      (yyval.innerJoins)->baseRelation->alias =  ((yyvsp[-1].string));
      varArrayListReverse((yyval.innerJoins)->joinRelations);
      varArrayListReverse((yyval.innerJoins)->conditions);
    }
    break;

  case 114:
    {
      (yyval.innerJoins) = NULL;
    }
    break;

  case 115:
                                               {
      sp = NULL;
      if(NULL != (yyvsp[0].innerJoins)){
        (yyval.innerJoins) = (yyvsp[0].innerJoins);
      }
      else{
        // $$ = (InnerJoinSqlNode*)my_malloc0(sizeof(InnerJoinSqlNode));
        // $$->joinRelations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy);
        // $$->conditions = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
        // $$->baseRelation = StringPairCreate(NULL, NULL);
        (yyval.innerJoins) = InnerJoinSqlNodeCreate(StringPairCreate(NULL, NULL), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy));
      }
      // sp = (StringPair*)my_malloc0(sizeof(StringPair));
      // sp->name = ($3);
      // sp->alias = ($4);
      sp = StringPairCreate((yyvsp[-4].string), (yyvsp[-3].string));

      varArrayListAddPointer((yyval.innerJoins)->joinRelations, sp);
      varArrayListAddPointer((yyval.innerJoins)->conditions, (yyvsp[-1].expression));
    }
    break;

  case 116:
    {
      (yyval.expression) = exprCreate(ETG_SUBQUERY);
      ((SubQueryExpr*)(yyval.expression))->sqlNode = ptrMove((void**)&(yyvsp[-1].sqlNode)->selection);
      ((SubQueryExpr*)(yyval.expression))->name = tokenName(sqlString, &(yyloc));
      ParsedSqlNodeDestroy((yyvsp[-1].sqlNode));
    }
    break;

  case 117:
    {
      (yyval.expression) = NULL;
    }
    break;

  case 118:
                      {
      (yyval.expression) = (yyvsp[0].expression);  
    }
    break;

  case 119:
    {
      (yyval.expression) = (yyvsp[-1].expression);
    }
    break;

  case 120:
    {
      cmpExpr = exprCreate(ETG_COMPARISON);
      cmpExpr->comp = (yyvsp[-1].comp);
      cmpExpr->left = (yyvsp[-2].expression);
      cmpExpr->right = (yyvsp[0].expression);
      cmpExpr->name = tokenName(sqlString, &(yyloc));
      (yyval.expression) = (Expression*)cmpExpr;
    }
    break;

  case 121:
    {
      value = NULL;
      valueExpr = NULL;
      cmpExpr = NULL;
      value = valueCreate();
      valueSetNull(value);
      valueExpr = exprCreate(ETG_VALUE);
      valueExpr->value = value;

      cmpExpr = exprCreate(ETG_COMPARISON);
      cmpExpr->comp = (yyvsp[-1].comp);
      cmpExpr->left = (Expression*)valueExpr;
      cmpExpr->right = (yyvsp[0].expression);
      cmpExpr->name = tokenName(sqlString, &(yyloc));
      (yyval.expression) = (Expression*)cmpExpr;
    }
    break;

  case 122:
    {
      children = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      conjExpr = exprCreate(ETG_CONJUNCTION);
      conjExpr->conjunctionType = CJET_AND;
      conjExpr->name = tokenName(sqlString, &(yyloc));
      conjExpr->children = children;
      varArrayListAddPointer(conjExpr->children, (yyvsp[-2].expression));
      varArrayListAddPointer(conjExpr->children, (yyvsp[0].expression));
      (yyval.expression) = (Expression*)conjExpr;
    }
    break;

  case 123:
    {
      conjExpr = exprCreate(ETG_CONJUNCTION);
      conjExpr->conjunctionType = CJET_OR;
      conjExpr->name = tokenName(sqlString, &(yyloc));
      conjExpr->children = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      varArrayListAddPointer(conjExpr->children, (yyvsp[-2].expression));
      varArrayListAddPointer(conjExpr->children, (yyvsp[0].expression));
      (yyval.expression) = (Expression*)conjExpr;
    }
    break;

  case 124:
        {
    (yyval.orderbyUnit) = OrderBySqlNodeCreate((yyvsp[0].expression), true);
	}
    break;

  case 125:
        {
    (yyval.orderbyUnit) = OrderBySqlNodeCreate((yyvsp[-1].expression), false);
	}
    break;

  case 126:
        {
    (yyval.orderbyUnit) = OrderBySqlNodeCreate((yyvsp[-1].expression), true);
	}
    break;

  case 127:
        {
    (yyval.orderbyUnitList) = varArrayListCreate(DISORDER, sizeof(OrderBySqlNode*), 0, NULL, OrderBySqlNodePointerDestroy);
    varArrayListAddPointer((yyval.orderbyUnitList), (yyvsp[0].orderbyUnit));
	}
    break;

  case 128:
        {
    varArrayListAddPointer((yyvsp[0].orderbyUnitList), (yyvsp[-2].orderbyUnit));
    (yyval.orderbyUnitList) = (yyvsp[0].orderbyUnitList);
	}
    break;

  case 129:
                    {
   (yyval.orderbyUnitList) = NULL;
  }
    break;

  case 130:
        {
    (yyval.orderbyUnitList) = (yyvsp[0].orderbyUnitList);
    varArrayListReverse((yyval.orderbyUnitList));
	}
    break;

  case 131:
                    {
   (yyval.expressionList) = NULL;
  }
    break;

  case 132:
        {
      (yyval.expressionList) = (yyvsp[0].expressionList);
      varArrayListReverse((yyval.expressionList));
	}
    break;

  case 133:
              {
   (yyval.expression) = NULL;
  }
    break;

  case 134:
        {   
    (yyval.expression) = (yyvsp[0].expression);
	}
    break;

  case 135:
    {
      (yyval.limit) = LimitSqlNodeCreate((yyvsp[-2].number), (yyvsp[0].number));
    }
    break;

  case 136:
    {
      (yyval.limit) = LimitSqlNodeCreate((yyvsp[0].number), -1);
    }
    break;

  case 137:
    {
      (yyval.limit) = NULL; 
    }
    break;

  case 138:
         { (yyval.comp) = CMPOP_EQUAL_TO; }
    break;

  case 139:
         { (yyval.comp) = CMPOP_LESS_THAN; }
    break;

  case 140:
         { (yyval.comp) = CMPOP_GREAT_THAN; }
    break;

  case 141:
         { (yyval.comp) = CMPOP_LESS_EQUAL; }
    break;

  case 142:
         { (yyval.comp) = CMPOP_GREAT_EQUAL; }
    break;

  case 143:
         { (yyval.comp) = CMPOP_NOT_EQUAL; }
    break;

  case 144:
           { (yyval.comp) = CMPOP_LIKE_OP;}
    break;

  case 145:
               {(yyval.comp) = CMPOP_NOT_LIKE_OP;}
    break;

  case 146:
         { (yyval.comp) = CMPOP_IN_OP;}
    break;

  case 147:
             {(yyval.comp) = CMPOP_NOT_IN_OP;}
    break;

  case 148:
           { (yyval.comp) = CMPOP_EXISTS_OP; }
    break;

  case 149:
                 { (yyval.comp) = CMPOP_NOT_EXISTS_OP; }
    break;

  case 150:
    {
      // char *tmpFileName = substr($4, 1, strlen($4) - 2);
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_LOAD_DATA);
      (yyval.sqlNode)->loadData->relationName = (yyvsp[0].string);
      (yyval.sqlNode)->loadData->fileName = substr((yyvsp[-3].string), 1, strlen((yyvsp[-3].string)) - 2);;
      my_free((yyvsp[-3].string));
    }
    break;

  case 151:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_EXPLAIN);
      (yyval.sqlNode)->explain->sqlNode = (ParsedSqlNode*)(yyvsp[0].sqlNode);
    }
    break;

  case 152:
    {
      (yyval.sqlNode) = ParsedSqlNodeCreate(SCF_SET_VARIABLE);
      (yyval.sqlNode)->setVariable->name  = ((yyvsp[-2].string));
      (yyval.sqlNode)->setVariable->value = valueCopy((yyvsp[0].value));
      valueDestroy(&(yyvsp[0].value));
    }
    break;



      default: break;
    }
  /* User semantic actions sometimes alter yychar, and that requires
     that yytoken be updated with the new translation.  We take the
     approach of translating immediately before every use of yytoken.
     One alternative is translating here after every semantic action,
     but that translation would be missed if the semantic action invokes
     YYABORT, YYACCEPT, or YYERROR immediately after altering yychar or
     if it invokes YYBACKUP.  In the case of YYABORT or YYACCEPT, an
     incorrect destructor might then be invoked immediately.  In the
     case of YYERROR or YYBACKUP, subsequent parser actions might lead
     to an incorrect destructor call or verbose syntax error message
     before the lookahead is translated.  */
  YY_SYMBOL_PRINT ("-> $$ =", yyr1[yyn], &yyval, &yyloc);

  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);

  *++yyvsp = yyval;
  *++yylsp = yyloc;

  /* Now 'shift' the result of the reduction.  Determine what state
     that goes to, based on the state we popped back to and the rule
     number reduced by.  */
  {
    const int yylhs = yyr1[yyn] - YYNTOKENS;
    const int yyi = yypgoto[yylhs] + *yyssp;
    yystate = (0 <= yyi && yyi <= YYLAST && yycheck[yyi] == *yyssp
               ? yytable[yyi]
               : yydefgoto[yylhs]);
  }

  goto yynewstate;


/*--------------------------------------.
| yyerrlab -- here on detecting error.  |
`--------------------------------------*/
yyerrlab:
  /* Make sure we have latest lookahead translation.  See comments at
     user semantic actions for why this is necessary.  */
  yytoken = yychar == YYEMPTY ? YYEMPTY : YYTRANSLATE (yychar);

  /* If not already recovering from an error, report this error.  */
  if (!yyerrstatus)
    {
      ++yynerrs;
#if ! YYERROR_VERBOSE
      yyerror (&yylloc, sqlString, sqlResult, scanner, YY_("syntax error"));
#else
# define YYSYNTAX_ERROR yysyntax_error (&yymsg_alloc, &yymsg, \
                                        yyssp, yytoken)
      {
        char const *yymsgp = YY_("syntax error");
        int yysyntax_error_status;
        yysyntax_error_status = YYSYNTAX_ERROR;
        if (yysyntax_error_status == 0)
          yymsgp = yymsg;
        else if (yysyntax_error_status == 1)
          {
            if (yymsg != yymsgbuf)
              YYSTACK_FREE (yymsg);
            yymsg = YY_CAST (char *, YYSTACK_ALLOC (YY_CAST (YYSIZE_T, yymsg_alloc)));
            if (!yymsg)
              {
                yymsg = yymsgbuf;
                yymsg_alloc = sizeof yymsgbuf;
                yysyntax_error_status = 2;
              }
            else
              {
                yysyntax_error_status = YYSYNTAX_ERROR;
                yymsgp = yymsg;
              }
          }
        yyerror (&yylloc, sqlString, sqlResult, scanner, yymsgp);
        if (yysyntax_error_status == 2)
          goto yyexhaustedlab;
      }
# undef YYSYNTAX_ERROR
#endif
    }

  yyerror_range[1] = yylloc;

  if (yyerrstatus == 3)
    {
      /* If just tried and failed to reuse lookahead token after an
         error, discard it.  */

      if (yychar <= YYEOF)
        {
          /* Return failure if at end of input.  */
          if (yychar == YYEOF)
            YYABORT;
        }
      else
        {
          yydestruct ("Error: discarding",
                      yytoken, &yylval, &yylloc, sqlString, sqlResult, scanner);
          yychar = YYEMPTY;
        }
    }

  /* Else will try to reuse lookahead token after shifting the error
     token.  */
  goto yyerrlab1;


/*---------------------------------------------------.
| yyerrorlab -- error raised explicitly by YYERROR.  |
`---------------------------------------------------*/
yyerrorlab:
  /* Pacify compilers when the user code never invokes YYERROR and the
     label yyerrorlab therefore never appears in user code.  */
  if (0)
    YYERROR;

  /* Do not reclaim the symbols of the rule whose action triggered
     this YYERROR.  */
  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);
  yystate = *yyssp;
  goto yyerrlab1;


/*-------------------------------------------------------------.
| yyerrlab1 -- common code for both syntax error and YYERROR.  |
`-------------------------------------------------------------*/
yyerrlab1:
  yyerrstatus = 3;      /* Each real token shifted decrements this.  */

  for (;;)
    {
      yyn = yypact[yystate];
      if (!yypact_value_is_default (yyn))
        {
          yyn += YYTERROR;
          if (0 <= yyn && yyn <= YYLAST && yycheck[yyn] == YYTERROR)
            {
              yyn = yytable[yyn];
              if (0 < yyn)
                break;
            }
        }

      /* Pop the current state because it cannot handle the error token.  */
      if (yyssp == yyss)
        YYABORT;

      yyerror_range[1] = *yylsp;
      yydestruct ("Error: popping",
                  yystos[yystate], yyvsp, yylsp, sqlString, sqlResult, scanner);
      YYPOPSTACK (1);
      yystate = *yyssp;
      YY_STACK_PRINT (yyss, yyssp);
    }

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END

  yyerror_range[2] = yylloc;
  /* Using YYLLOC is tempting, but would change the location of
     the lookahead.  YYLOC is available though.  */
  YYLLOC_DEFAULT (yyloc, yyerror_range, 2);
  *++yylsp = yyloc;

  /* Shift the error token.  */
  YY_SYMBOL_PRINT ("Shifting", yystos[yyn], yyvsp, yylsp);

  yystate = yyn;
  goto yynewstate;


/*-------------------------------------.
| yyacceptlab -- YYACCEPT comes here.  |
`-------------------------------------*/
yyacceptlab:
  yyresult = 0;
  goto yyreturn;


/*-----------------------------------.
| yyabortlab -- YYABORT comes here.  |
`-----------------------------------*/
yyabortlab:
  yyresult = 1;
  goto yyreturn;


#if !defined yyoverflow || YYERROR_VERBOSE
/*-------------------------------------------------.
| yyexhaustedlab -- memory exhaustion comes here.  |
`-------------------------------------------------*/
yyexhaustedlab:
  yyerror (&yylloc, sqlString, sqlResult, scanner, YY_("memory exhausted"));
  yyresult = 2;
  /* Fall through.  */
#endif


/*-----------------------------------------------------.
| yyreturn -- parsing is finished, return the result.  |
`-----------------------------------------------------*/
yyreturn:
  if (yychar != YYEMPTY)
    {
      /* Make sure we have latest lookahead translation.  See comments at
         user semantic actions for why this is necessary.  */
      yytoken = YYTRANSLATE (yychar);
      yydestruct ("Cleanup: discarding lookahead",
                  yytoken, &yylval, &yylloc, sqlString, sqlResult, scanner);
    }
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYABORT or YYACCEPT.  */
  YYPOPSTACK (yylen);
  YY_STACK_PRINT (yyss, yyssp);
  while (yyssp != yyss)
    {
      yydestruct ("Cleanup: popping",
                  yystos[+*yyssp], yyvsp, yylsp, sqlString, sqlResult, scanner);
      YYPOPSTACK (1);
    }
#ifndef yyoverflow
  if (yyss != yyssa)
    YYSTACK_FREE (yyss);
#endif
#if YYERROR_VERBOSE
  if (yymsg != yymsgbuf)
    YYSTACK_FREE (yymsg);
#endif
  return yyresult;
}

//_____________________________________________________________________
extern void scan_string(const char *str, yyscan_t scanner);
/* extern void valueSetInt(Value *value, int val); */
int sqlParse(const char *s, ParsedSqlResult *sqlResult) {
  int result;
  yyscan_t scanner;
  yylex_init(&scanner);
  scan_string(s, scanner);
  result = yyparse(s, sqlResult, scanner);
  yylex_destroy(scanner);
  return result;
}
