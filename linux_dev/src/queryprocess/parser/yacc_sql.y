
%{

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "yacc_sql.h"
#include "lex_sql.h"
#include "parse_defs.h"
#include "expression.h"
#define YYMALLOC my_malloc0
#define YYREALLOC my_realloc
#define YYFREE my_free
Value* value = NULL;
char* str = NULL;
int date;
DateTime dateTime;
int j = 0;
UpdateKV* kv = NULL;
ExprListExpr* exprListExpr = NULL;
ValueExpr* valueExpr = NULL;
ComparisonExpr* cmpExpr = NULL;
varArrayList* children = NULL;
ConjunctionExpr* conjExpr = NULL;
StringPair *sp = NULL;
// varArrayList* joinRelations = NULL;
// varArrayList* conditions = NULL;
// StringPair* baseRelation = NULL;
FieldExpr *fieldExpr = NULL;
AggrFuncExpr *afexpr = NULL;

char *substr(const char *str, int start, int length) {
    char *result = (char *)my_malloc0(length + 1); // +1 for the null terminator
    if (result != NULL) {
        strncpy(result, str + start, length);
        result[length] = '\0'; // null-terminate the string
    }
    return result;
}

char* tokenName(const char *sqlString, YYLTYPE *llocp) {
    // 计算子串的长度
    int length = llocp->last_column - llocp->first_column + 1;

    // 分配内存以存储子串
    char *substring = (char*)my_malloc0((length + 1) * sizeof(char));

    // 复制子串到新分配的内存中
    strncpy(substring, sqlString + llocp->first_column, length);
    substring[length] = '\0'; // 在字符串末尾添加 null 终止字符

    return substring;
}

int yyerror(YYLTYPE *llocp, const char *sqlString, ParsedSqlResult *sqlResult, yyscan_t scanner, const char *msg)
{
    // 创建 errorSqlNode 对象，并分配内存
    ParsedSqlNode *errorSqlNode = ParsedSqlNodeCreate(SCF_ERROR);


    // 初始化 errorSqlNode 对象
    errorSqlNode->error->errorMsg = strdup(msg);
    errorSqlNode->error->line = llocp->first_line;
    errorSqlNode->error->column = llocp->first_column;

    // 添加 errorSqlNode 到 sqlResult 中
    varArrayListAddPointer(sqlResult->sqlNodes, errorSqlNode);
    sqlResult->count++;

    // 返回成功
    return 0;
}

ArithmeticExpr* createArithmeticExpression(ArithmeticExprType type,
                                              Expression* left,
                                              Expression* right,
                                              const char* sqlString,
                                              YYLTYPE* llocp) {
    // 分配内存以存储 ArithmeticExpr 对象
    ArithmeticExpr* expr = NULL;
    if(left != NULL &&  left->type == ETG_EXPRLIST && ((ExprListExpr*)left)->exprs->elementCount == 1){
      left = varArrayListGetPointer(((ExprListExpr*)left)->exprs, 0);
      varArrayListRemoveByIndexPointer(((ExprListExpr*)left)->exprs, 0);
      varArrayListDestroy(&(((ExprListExpr*)left)->exprs));
    }

    if(right != NULL && right->type == ETG_EXPRLIST && ((ExprListExpr*)right)->exprs->elementCount == 1){
      right = varArrayListGetPointer(((ExprListExpr*)right)->exprs, 0);
      varArrayListRemoveByIndexPointer(((ExprListExpr*)left)->exprs, 0);
      varArrayListDestroy(&(((ExprListExpr*)right)->exprs));
    }
    expr = exprCreate(ETG_ARITHMETIC);
    expr->arithmeticType = type;
    expr->left = left;
    expr->right = right;
    expr->name = tokenName(sqlString, llocp);
    return expr;
}


%}

%code requires {
#include "parse_defs.h" // 这里是为了在生成的.h文件包含一些所需的头文件
}

%define api.pure full
%define parse.error verbose
/** 启用位置标识 **/
%locations
%lex-param { yyscan_t scanner }
/** 这些定义了在yyparse函数中的参数 **/
%parse-param { const char * sqlString }
%parse-param { ParsedSqlResult * sqlResult }
%parse-param { void * scanner }

//标识tokens
%token  SEMICOLON
        CREATE
        DROP
        TABLE
        TABLES
        INDEX
        CALC
        SELECT
        DESC
        SHOW
        SYNC
        INSERT
        DELETE
        UPDATE
        LBRACE
        RBRACE
        COMMA
        TRX_BEGIN
        TRX_COMMIT
        TRX_ROLLBACK
        INT_T
        BLOB_T
        DATE_T
        DATETIME_T
        TEXT_T
        STRING_T
        FLOAT_T
        HELP
        EXIT
        DOT //QUOTE
        INTO
        VALUES
        FROM
        WHERE
        AND
        OR
        SET
        USING
        HASH
        ON
        LOAD
        DATA
        INFILE
        EXPLAIN
        PRIMARY
        KEY
        NOT
        LIKE     
        NULL_T
        EQ
        LT
        GT
        LE
        GE
        NE
        INNER
        JOIN
        AGGR_MAX
        AGGR_MIN
        AGGR_SUM
        AGGR_AVG
        AGGR_COUNT
        ORDER_T
        GROUP
        BY
        ASC
        HAVING
        AS
        IN
        EXISTS
        LIMIT
        OFFSET
        IF
        DISTINCT
/** union 中定义各种数据类型，真实生成的代码也是union类型，所以不能有非POD类型的数据 **/
%union {
  ParsedSqlNode *                   sqlNode;
  Value *                           value;
  enum CompOp                       comp;
  RelAttrSqlNode *                  relAttr;
  varArrayList *                    attrInfos;       
  AttrInfoSqlNode *                 attrInfo;
  Expression *                      expression;
  varArrayList *                    expressionList;  
  varArrayList *                    valueList; 
  varArrayList *                    insertValueList;        /* element type:varArrayList<element type: Value> */     
  varArrayList *                    conditionList;   
  varArrayList *                    relAttrList;    
  varArrayList *                    relationList;   
  InnerJoinSqlNode *                innerJoins; 
  varArrayList*                     innerJoinsList;
  OrderBySqlNode*                   orderbyUnit;
  varArrayList*                     orderbyUnitList;
  char *                            string;
  int                               number;
  double                             floats;
  bool                              boolean;
  LimitSqlNode*                     limit;
  UpdateKV*                         updateKv;
  varArrayList*                     updateKvList;
};

%token <number> NUMBER
%token <floats> FLOAT
%token <string> ID
%token <string> DATE_TIME_STR
%token <string> DATE_STR
%token <string> SSS
//非终结符

/** type 定义了各种解析后的结果输出的是什么类型。类型对应了 union 中的定义的成员变量名称 **/
%type <string>              alias
%type <innerJoins>         joinList
%type <innerJoins>         fromNode
%type <innerJoinsList>    fromList
%type <number>              type
%type <expression>          condition
%type <value>               value
%type <number>              number
%type <boolean>             asOption
%type <comp>                compOp
%type <comp>                existsOp
%type <relAttr>            relAttr
%type <attrInfos>          attrDefList
%type <attrInfo>           attrDef
%type <relationList>       idxColList
%type <relationList>       insertColList
%type <valueList>          valueList
%type <valueList>          insertValue
%type <insertValueList>   insertValueList
%type <updateKv>           updateKv
%type <updateKvList>      updateKvList
%type <expression>          where
/* %type <conditionList>      conditionList */
%type <expressionList>     selectAttr
%type <expression>          subQueryExpr
%type <expression>          aggrFuncExpr
%type <number>              aggrFuncType
%type <expression>          expression
%type <expressionList>     expressionList
%type <expressionList>     optGroupBy
%type <expression>          optHaving
%type <limit>               optLimit
%type <orderbyUnit>        sortUnit
%type <orderbyUnitList>   sortList
%type <orderbyUnitList>   optOrderBy
%type <sqlNode>            calcStmt
%type <sqlNode>            selectStmt
%type <sqlNode>            insertStmt
%type <sqlNode>            updateStmt
%type <sqlNode>            deleteStmt
%type <sqlNode>            createTableStmt
%type <sqlNode>            dropTableStmt
%type <sqlNode>            showTablesStmt
%type <sqlNode>            descTableStmt
%type <sqlNode>            createIndexStmt
%type <sqlNode>            dropIndexStmt
%type <sqlNode>            syncStmt
%type <sqlNode>            beginStmt
%type <sqlNode>            commitStmt
%type <sqlNode>            rollbackStmt
%type <sqlNode>            loadDataStmt
%type <sqlNode>            explainStmt
%type <sqlNode>            setVariableStmt
%type <sqlNode>            helpStmt
%type <sqlNode>            exitStmt
%type <sqlNode>            commandWrapper
// commands should be a list but I use a single command instead
%type <sqlNode>            commands

%destructor { my_free($$); } ID
%destructor { my_free($$); } DATE_TIME_STR
%destructor { my_free($$); } DATE_STR
%destructor { my_free($$); } SSS

// 增加 %destructor 指令，确保符号被丢弃时自动释放内存
%destructor { my_free($$); } alias
%destructor { InnerJoinSqlNodeDestroy($$); } joinList
%destructor { InnerJoinSqlNodeDestroy($$); } fromNode
%destructor { varArrayListDestroy(&$$); } fromList
%destructor { exprDestroy($$); } condition
%destructor { valueDestroy(&$$); } value
%destructor { RelAttrSqlNodeDestroy($$); } relAttr
%destructor { varArrayListDestroy(&$$); } attrDefList
%destructor { AttrInfoSqlNodeDestroy($$); } attrDef
%destructor { varArrayListDestroy(&$$); } idxColList
%destructor { varArrayListDestroy(&$$); } insertColList
%destructor { varArrayListDestroy(&$$); } valueList
%destructor { varArrayListDestroy(&$$); } insertValue
%destructor { varArrayListDestroy(&$$); } insertValueList
%destructor { UpdateKVDestroy($$); } updateKv
%destructor { varArrayListDestroy(&$$); } updateKvList
%destructor { exprDestroy($$); } where
%destructor { varArrayListDestroy(&$$); } selectAttr
%destructor { exprDestroy($$); } subQueryExpr
%destructor { exprDestroy($$); } aggrFuncExpr
%destructor { exprDestroy($$); } expression
%destructor { varArrayListDestroy(&$$); } expressionList
%destructor { varArrayListDestroy(&$$); } optGroupBy
%destructor { exprDestroy($$); } optHaving
%destructor { LimitSqlNodeDestroy($$); } optLimit
%destructor { OrderBySqlNodeDestroy($$); } sortUnit
%destructor { varArrayListDestroy(&$$); } sortList
%destructor { varArrayListDestroy(&$$); } optOrderBy
%destructor { ParsedSqlNodeDestroy($$); } calcStmt
%destructor { ParsedSqlNodeDestroy($$); } selectStmt
%destructor { ParsedSqlNodeDestroy($$); } insertStmt
%destructor { ParsedSqlNodeDestroy($$); } updateStmt
%destructor { ParsedSqlNodeDestroy($$); } deleteStmt
%destructor { ParsedSqlNodeDestroy($$); } createTableStmt
%destructor { ParsedSqlNodeDestroy($$); } dropTableStmt
%destructor { ParsedSqlNodeDestroy($$); } showTablesStmt
%destructor { ParsedSqlNodeDestroy($$); } descTableStmt
%destructor { ParsedSqlNodeDestroy($$); } createIndexStmt
%destructor { ParsedSqlNodeDestroy($$); } dropIndexStmt
%destructor { ParsedSqlNodeDestroy($$); } syncStmt
%destructor { ParsedSqlNodeDestroy($$); } beginStmt
%destructor { ParsedSqlNodeDestroy($$); } commitStmt
%destructor { ParsedSqlNodeDestroy($$); } rollbackStmt
%destructor { ParsedSqlNodeDestroy($$); } loadDataStmt
%destructor { ParsedSqlNodeDestroy($$); } explainStmt
%destructor { ParsedSqlNodeDestroy($$); } setVariableStmt
%destructor { ParsedSqlNodeDestroy($$); } helpStmt
%destructor { ParsedSqlNodeDestroy($$); } exitStmt
%destructor { ParsedSqlNodeDestroy($$); } commandWrapper

%left OR
%left AND
%left EQ LT GT LE GE NE
%left '+' '-'
%left '*' '/'
%nonassoc UMINUS
%%

commands: commandWrapper optSemicolon  //commands or sqls. parser starts here.
  {
    varArrayListAddPointer(sqlResult->sqlNodes, $1);
    sqlResult->count++;
  }
  ;

commandWrapper:
    calcStmt
  | selectStmt
  | insertStmt
  | updateStmt
  | deleteStmt
  | createTableStmt
  | dropTableStmt
  | showTablesStmt
  | descTableStmt
  | createIndexStmt
  | dropIndexStmt
  | syncStmt
  | beginStmt
  | commitStmt
  | rollbackStmt
  | loadDataStmt
  | explainStmt
  | setVariableStmt
  | helpStmt
  | exitStmt
    ;

exitStmt:      
    EXIT {
      (void)yynerrs;  
      $$ = ParsedSqlNodeCreate(SCF_EXIT);
    };

helpStmt:
    HELP {
      $$ = ParsedSqlNodeCreate(SCF_HELP);
    };

syncStmt:
    SYNC {
      $$ = ParsedSqlNodeCreate(SCF_SYNC);
    }
    ;

beginStmt:
    TRX_BEGIN  {
      $$ = ParsedSqlNodeCreate(SCF_BEGIN);
    }
    ;

commitStmt:
    TRX_COMMIT {
      $$ = ParsedSqlNodeCreate(SCF_COMMIT);
    }
    ;

rollbackStmt:
    TRX_ROLLBACK  {
      $$ = ParsedSqlNodeCreate(SCF_ROLLBACK);
    }
    ;

dropTableStmt:    /*drop table 语句的语法解析树*/
    /* 支持 'DROP TABLE IF EXISTS' 语句 */
    DROP TABLE IF EXISTS ID {
      $$ = ParsedSqlNodeCreate(SCF_DROP_TABLE);
      $$->dropTable->relationName = ($5);
      $$->dropTable->ifExists = true; /* 添加一个标志来表示 'IF EXISTS' */
    }
  | DROP TABLE ID {
      $$ = ParsedSqlNodeCreate(SCF_DROP_TABLE);
      $$->dropTable->relationName = ($3);
      $$->dropTable->ifExists = false; /* 默认情况下没有 'IF EXISTS' */
    };

showTablesStmt:
    SHOW TABLES {
      $$ = ParsedSqlNodeCreate(SCF_SHOW_TABLES);
    }
    ;

descTableStmt:
    DESC ID  {
      $$ = ParsedSqlNodeCreate(SCF_DESC_TABLE);
      $$->descTable->relationName = ($2);
    }
    ;

createIndexStmt:    /*create index 语句的语法解析树*/
    CREATE INDEX ID ON ID LBRACE ID RBRACE
    {
      $$ = ParsedSqlNodeCreate(SCF_CREATE_INDEX);
      $$->createIndex->indexName = ($3);
      $$->createIndex->relationName = ($5);
      $$->createIndex->attributeName = ($7);
      $$->createIndex->indexType = INDEX_TYPE_BTREE; // 默认B+树索引
    }
    | CREATE INDEX ID ON ID LBRACE ID RBRACE USING HASH
    {
      $$ = ParsedSqlNodeCreate(SCF_CREATE_INDEX);
      $$->createIndex->indexName = ($3);
      $$->createIndex->relationName = ($5);
      $$->createIndex->attributeName = ($7);
      $$->createIndex->indexType = INDEX_TYPE_HASH; // 哈希索引
    }
    ;

dropIndexStmt:      /*drop index 语句的语法解析树*/
    DROP INDEX ID ON ID
    {
      $$ = ParsedSqlNodeCreate(SCF_DROP_INDEX);
      $$->dropIndex->indexName = ($3);
      $$->dropIndex->relationName = ($5);
    }
    ;
createTableStmt:    /*create table 语句的语法解析树*/
    CREATE TABLE ID LBRACE attrDef attrDefList RBRACE
    {
      $$ = ParsedSqlNodeCreate(SCF_CREATE_TABLE);
      $$->createTable->relationName = ($3);
      if($6 != NULL){
        $$->createTable->attrInfos = $6;
      }
      else{
        $$->createTable->attrInfos = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AttrInfoSqlNodePointerDestroy);
      }
      varArrayListAddPointer($$->createTable->attrInfos, $5);
      varArrayListReverse($$->createTable->attrInfos);
    }
    | CREATE TABLE ID LBRACE attrDef attrDefList RBRACE asOption selectStmt
    {
      // CreateTableSqlNode* createTable = NULL;
      $$ = $9;
      $$->flag = SCF_CREATE_TABLE;
      $$->createTable = CreateTableSqlNodeCreate(NULL, NULL);
      ($$->createTable)->relationName = ($3);
      if($6 != NULL){
        ($$->createTable)->attrInfos = $6;
      }
      else{
        ($$->createTable)->attrInfos = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AttrInfoSqlNodePointerDestroy);
      }
      varArrayListAddPointer(($$->createTable)->attrInfos, $5);
      varArrayListReverse(($$->createTable)->attrInfos);
      ($$->createTable)->select = ptrMove((void**)&$9);
    }
    | CREATE TABLE ID asOption selectStmt
    {
      $$ = $5;
      $$->flag = SCF_CREATE_TABLE;
      $$->createTable = CreateTableSqlNodeCreate(NULL, NULL);
      $$->createTable->relationName = ($3);
      ($$->createTable)->select = ptrMove((void**)&$5);
    }
    ;
attrDefList:
    /* empty */
    {
      $$ = NULL;
    }
    | COMMA attrDef attrDefList
    {
      if ($3 != NULL) {
        $$ = $3;
      } else {
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AttrInfoSqlNodePointerDestroy);
      }
      varArrayListAddPointer($$, $2);
    }
    ;
    
attrDef:
    ID type LBRACE number RBRACE 
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, $4, false, true);
    }
    | ID type LBRACE number RBRACE PRIMARY KEY
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, $4, true, false);
    }
    | ID type
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, 4, false, true);
    }
    | ID type PRIMARY KEY
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, 4, true, false);
    }
    | ID type LBRACE number RBRACE NOT NULL_T
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, $4, false, false);
    }
    | ID type LBRACE number RBRACE PRIMARY KEY NOT NULL_T
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, $4, true, false);
    }
    | ID type NOT NULL_T
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, 4, false, false);
    }
    | ID type PRIMARY KEY NOT NULL_T
    {
      $$ = AttrInfoSqlNodeCreate((AttrType)$2, $1, 4, true, false);
    }
    ;
  asOption:
    /* empty */
    {
      $$ = false;
    }
    | AS
    {
      $$ = false;
    }
    ;
number:
    NUMBER {$$ = $1;}
    ;
type:
    INT_T      { $$=INTS; }
    | DATE_T   { $$=DATES; }
    | STRING_T { $$=CHARS; }
    | FLOAT_T  { $$=DOUBLES; }
    | DATETIME_T { $$=DATETIMES; }
    | TEXT_T  {$$ = TEXTS;}
    | BLOB_T  {$$ = BLOB;}
    ;
insertStmt:        /*insert   语句的语法解析树*/
    INSERT INTO ID insertColList VALUES insertValue insertValueList
    { 
      $$ = ParsedSqlNodeCreate(SCF_INSERT);
      $$->insertion->relationName = ($3);

      /* 插入的值lists */
      if($7 != NULL){
        $$->insertion->valuelists = $7;
      }
      else{
        $$->insertion->valuelists = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, varArrayListPointerDestroy);
      }
      varArrayListAddPointer($$->insertion->valuelists, $6);
      varArrayListReverse($$->insertion->valuelists);

      /* 插入的列list */
      if($4 != NULL){
        $$->insertion->attributes = $4;
        varArrayListReverse($$->insertion->attributes);
      }
      else{
        $$->insertion->attributes = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      }
    }
    ;



insertColList:
    /* empty */
    {
      $$ = NULL;
    }
    | LBRACE ID idxColList RBRACE
    {
      if ($3 != NULL) {
        $$ = $3;
      } else {
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      }
      varArrayListAddPointer($$, $2);
    }
    ;


idxColList:
    /* empty */
    {
      $$ = NULL;
    }
    | COMMA ID idxColList
    {
      if ($3 != NULL) {
        $$ = $3;
      } else {
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      }
      varArrayListAddPointer($$, $2);
    }
    ;

insertValueList:
    /* empty */
    {
      $$ = NULL;
    }
    | COMMA insertValue insertValueList
    {
      if ($3 != NULL) {
        $$ = $3;
      } else {
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, varArrayListPointerDestroy);
      }
      varArrayListAddPointer($$, $2);
    }
    ;

insertValue:
    LBRACE expression valueList RBRACE 
    {
      value = valueCreate();
      if(!insertExprToValue($2, value)) {
        exprDestroy($2);
        varArrayListDestroy(&$3);
        valueDestroy(&value);
        yyerror(&@$, sqlString, sqlResult, scanner, "error");
        YYERROR;
      }
      if ($3 != NULL) {
        $$ = $3;
      } else {
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, valuePointerDestroy);
      }
      varArrayListAddPointer($$, value);
      varArrayListReverse($$);
      exprDestroy($2);
    }
    ;

valueList:
    /* empty */
    {
      $$ = NULL;
    }
    | COMMA expression valueList  { 
      value = valueCreate();
      // memset(value, 0, sizeof(Value));
      if(!insertExprToValue($2, value)) {
        exprDestroy($2);
        varArrayListDestroy(&$3);
        valueDestroy(&value);
        yyerror(&@$, sqlString, sqlResult, scanner, "error");
        YYERROR;
      }
      if ($3 != NULL) {
        $$ = $3;
      } else {
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, valuePointerDestroy);
      }

      varArrayListAddPointer($$, value);
      // TODO: free $2
      exprDestroy($2);
    }
    ;
value:
    NUMBER {
      $$ = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetInt($$, (int)$1);
      @$ = @1;
    }
    |FLOAT {
      $$ = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetDouble($$, (double)$1);
      @$ = @1;
    }
    |DATE_STR {
      str = substr($1,1,strlen($1)-2);
      
      $$ = valueCreate();
      // memset($$, 0, sizeof(Value));
      if(stringToDate((str), &date) == GNCDB_SUCCESS){
        valueSetDate($$, date);
      }
      else{
        my_free($1);
        my_free(str);
        valueDestroy(&$$);
        yyerror(&@$, sqlString, sqlResult, scanner, "invalid date format");
        YYERROR;
      }
      my_free($1);
      my_free(str);
    }
    |DATE_TIME_STR {
      str = substr($1,1,strlen($1)-2);
      // DateTime date;
      $$ = valueCreate();
      // memset($$, 0, sizeof(Value));
      if(stringToDatetime((str), &dateTime) == GNCDB_SUCCESS){
        valueSetDatetime($$, dateTime);
      }
      else{
        my_free($1);
        my_free(str);
        valueDestroy(&$$);
        yyerror(&@$, sqlString, sqlResult, scanner, "invalid date format");
        YYERROR;
      }
      my_free($1);
      my_free(str);
    }
    |SSS {
      str = substr($1,1,strlen($1)-2);
      $$ = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetString($$, (str));
      my_free($1);
      my_free(str);
    }
    | NULL_T{
      $$ = valueCreate();
      // memset($$, 0, sizeof(Value));
      valueSetNull($$);
    }
    ;
    
deleteStmt:    /*  delete 语句的语法解析树*/
    DELETE FROM ID where 
    {
      $$ = ParsedSqlNodeCreate(SCF_DELETE);
      $$->deletion->relationName = ($3);
      $$->deletion->conditions = $4;
    }
    ;
updateStmt:      /*  update 语句的语法解析树*/
    UPDATE ID SET updateKv updateKvList where 
    {
      // varArrayList* src = NULL;
      // j = 0;
      kv = NULL;
      $$ = ParsedSqlNodeCreate(SCF_UPDATE);
      $$->update->relationName = $2;
      $$->update->attributeNames = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, charPointerDestroy);
      $$->update->values = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      varArrayListAddPointer($$->update->attributeNames, strdup($4->attrName));
      varArrayListAddPointer($$->update->values, exprDeepCopy($4->value));
      UpdateKVDestroy($4);
      if($5 != NULL){
        // src = $5;
        for(j = 0; j < $5->elementCount; j++){
          kv = varArrayListGetPointer($5, j);
          // TODO: 更改add逻辑，避免重复分配内存
          // varArrayListAddPointer($$->update->attributeNames, strdup(kv->attrName));
          // varArrayListAddPointer($$->update->values, exprDeepCopy(kv->value));          
          varArrayListAddPointer($$->update->attributeNames, (kv->attrName));
          varArrayListAddPointer($$->update->values, (kv->value));
          kv->attrName = NULL;
          kv->value = NULL;
        }
        varArrayListDestroy(&$5);
      }
      $$->update->conditions = $6;
    }
    ;

updateKvList:
    /* empty */
    {
      $$ = NULL;
    }
    | COMMA updateKv updateKvList
    {
      if ($3 != NULL) {
        $$ = $3;
      } else {
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, UpdateKVPointerDestroy);
      }
      varArrayListAddPointer($$, $2);
    }
    ;

updateKv:
    ID EQ expression
    {
      $$ = UpdateKVCreate($1, $3);
    }
    ;

selectStmt:        /*  select 语句的语法解析树*/

    /* SELECT selectAttr
    {
      $$ = ParsedSqlNodeCreate(SCF_SELECT);
      if ($2 != NULL) {
        $$->selection->projectExprs = $2;
      }
      else{
        $$->selection->projectExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }
      $$->selection->isDistinct = false;
    }
    |  */
    SELECT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit
    {
      $$ = ParsedSqlNodeCreate(SCF_SELECT);
      // projects
      if($2 != NULL){
        $$->selection->projectExprs = $2;
      }
      else{
        $$->selection->projectExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }
      $$->selection->isDistinct = false;

      // from clause
      if($5 != NULL){
        $$->selection->relations = $5;
      }
      else{
        $$->selection->relations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, InnerJoinSqlNodePointerDestroy);
      }
      varArrayListAddPointer($$->selection->relations, $4);
      varArrayListReverse($$->selection->relations);

      // where clause
      $$->selection->conditions = $6;
      

      // group by clause
      if(NULL != $7){
        $$->selection->groupbyExprs = $7;
      }
      else{
        $$->selection->groupbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }

      // having condition
      $$->selection->havingConditions = $8;

      // order by clause
      if(NULL != $9){
        $$->selection->orderbyExprs = $9;
      }
      else{
        $$->selection->orderbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, OrderBySqlNodePointerDestroy);
      }

      // limit clause
      $$->selection->limit = $10;

    }
    | SELECT DISTINCT selectAttr FROM fromNode fromList where optGroupBy optHaving optOrderBy optLimit
    {
      $$ = ParsedSqlNodeCreate(SCF_SELECT);
      // projects
      if($3 != NULL){
        $$->selection->projectExprs = $3;
      }
      else{
        $$->selection->projectExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }
      $$->selection->isDistinct = true;

      // from clause
      if($6 != NULL){
        $$->selection->relations = $6;
      }
      else{
        $$->selection->relations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, InnerJoinSqlNodePointerDestroy);
      }
      varArrayListAddPointer($$->selection->relations, $5);
      varArrayListReverse($$->selection->relations);

      // where clause
      $$->selection->conditions = $7;
      

      // group by clause
      if(NULL != $8){
        $$->selection->groupbyExprs = $8;
      }
      else{
        $$->selection->groupbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }

      // having condition
      $$->selection->havingConditions = $9;

      // order by clause
      if(NULL != $10){
        $$->selection->orderbyExprs = $10;
      }
      else{
        $$->selection->orderbyExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, OrderBySqlNodePointerDestroy);
      }

      // limit clause
      $$->selection->limit = $11;

    }
    ;
calcStmt:
    CALC expressionList
    {
      $$ = ParsedSqlNodeCreate(SCF_CALC);
      varArrayListReverse($2);
      $$->calc->expressions = $2;
    }
    ;

expressionList:
    expression alias
    {
      $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      $1->alias = $2 ;
      varArrayListAddPointer($$, $1);
    }
    | expression alias COMMA expressionList
    {
      if($4 != NULL){
        $$ = $4;
      }
      else{
        $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      }
      $1->alias = ($2);
      varArrayListAddPointer($$, $1);
    }
    ;
expression:
    expression '+' expression {
      $$ = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_ADD, $1, $3, sqlString, &@$);
    }
    | expression '-' expression {
      $$ = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_SUB, $1, $3, sqlString, &@$);
    }
    | expression '*' expression {
      $$ = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_MUL, $1, $3, sqlString, &@$);
    }
    | expression '/' expression {
      $$ = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_DIV, $1, $3, sqlString, &@$);
    }
    | LBRACE expressionList RBRACE {
      exprListExpr = NULL;
      if($2->elementCount == 1){
         $$ = varArrayListGetPointer($2, 0);
         varArrayListRemoveByIndexPointer($2, 0);
         varArrayListDestroy(&$2);
      }
      else{
        exprListExpr = exprCreate(ETG_EXPRLIST);
        exprListExpr->exprs = ptrMove((void**)&$2);

        $$ = (Expression*)exprListExpr;
      }
      if($$->name != NULL){
        my_free($$->name);
      }
      $$->name = tokenName(sqlString, &@$);
    }
    | '-' expression %prec UMINUS {
      $$ = (Expression*)createArithmeticExpression((ArithmeticExprType)ARITH_NEGATIVE, $2, NULL, sqlString, &@$);

    }
    | value {
      $$ = exprCreate(ETG_VALUE);
      ((ValueExpr*)($$))->value = $1;
      ((ValueExpr*)($$))->name = tokenName(sqlString, &@$);
    }
    | relAttr{
      $$ = exprCreate(ETG_FIELD);
      ((FieldExpr*)($$))->tableName = $1->relationName == NULL ? NULL : strdup($1->relationName);
      ((FieldExpr*)($$))->fieldName = strdup($1->attributeName);
      RelAttrSqlNodeDestroy($1);
    }
    | aggrFuncExpr{
      $$ = $1;
    }
    | subQueryExpr{
      $$ = $1;
    }
    ;

aggrFuncType:
    AGGR_MAX {
      $$ = AGG_MAX;
    }
    | AGGR_MIN {
      $$ = AGG_MIN;
    }
    | AGGR_SUM {
      $$ = AGG_SUM;
    }
    | AGGR_AVG {
      $$ = AGG_AVG;
    }
    | AGGR_COUNT {
      $$ = AGG_COUNT;
    }
    ;

aggrFuncExpr:
    aggrFuncType LBRACE expression RBRACE
    {
      afexpr = exprCreate(ETG_AGGRFUNC);
      afexpr->name = tokenName(sqlString, &@$);
      afexpr->aggrType = (AggrFuncType)$1;
      afexpr->param = $3;
      $$ = (Expression*)afexpr;
    }
    | aggrFuncType LBRACE '*' RBRACE
    {
      valueExpr = NULL;
      afexpr = NULL;
      if($1 != AGG_COUNT){
        yyerror(&@$, sqlString, sqlResult, scanner, "only support count(*)");
        YYERROR;
      }
      value = valueCreate();
      // memset(value, 0, sizeof(Value));
      valueSetInt(value, 1);  /* 这里用int值1代表是COUNT(*) */
      valueExpr = exprCreate(ETG_VALUE);
      valueExpr->value = value;
      valueExpr->name = strdup("*");
      afexpr = exprCreate(ETG_AGGRFUNC);
      afexpr->name = tokenName(sqlString, &@$);
      afexpr->aggrType = (AggrFuncType)$1;
      afexpr->param = (Expression*)valueExpr;
      $$ = (Expression*)afexpr;
    }
    ;
selectAttr:
    '*' {
      fieldExpr = NULL;
      $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);// Expression *
      fieldExpr = exprCreate(ETG_FIELD);
      fieldExpr->tableName = strdup("*");
      fieldExpr->fieldName = strdup("*");
      fieldExpr->name = tokenName(sqlString, &@$);
      varArrayListAddPointer($$, (Expression*)fieldExpr);
    }
    | '*' DOT '*' {
      fieldExpr = NULL;
      $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);// Expression *
      fieldExpr = exprCreate(ETG_FIELD);
      fieldExpr->tableName = strdup("*");
      fieldExpr->fieldName = strdup("*");
      fieldExpr->name = tokenName(sqlString, &@$);
      varArrayListAddPointer($$, (Expression*)fieldExpr);
    }
    | ID DOT '*'{
      fieldExpr = NULL;
      $$ = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);// Expression *
      fieldExpr = exprCreate(ETG_FIELD);
      fieldExpr->tableName = strdup($1);
      fieldExpr->fieldName = strdup("*");
      fieldExpr->name = tokenName(sqlString, &@$);
      varArrayListAddPointer($$, (Expression*)fieldExpr);
      my_free($1);
    }
    | expressionList{
      $$ = $1;
    }
    ;

relAttr:
    ID {
      $$ = RelAttrSqlNodeCreate(NULL, $1);
    }
    | ID DOT ID {
      $$ = RelAttrSqlNodeCreate($1, $3);
    }
    ;

fromList:
    /* empty */
    {
      $$ = NULL;
    }
    | COMMA fromNode fromList {
      if(NULL == $3){
        $$ = varArrayListCreate(DISORDER, sizeof(InnerJoinSqlNode*), 0, NULL, InnerJoinSqlNodePointerDestroy);
      }
      else{
        $$ = $3;
      }
      varArrayListAddPointer($$, $2);
    }
    ;

alias:
    /* empty */ {
      $$ = NULL;
    }
    | ID {
      $$ = $1;
    }
    | AS ID {
      $$ = $2;
    }


fromNode:
    ID alias joinList{
      // joinRelations = NULL;
      // conditions = NULL;
      // baseRelation = NULL;
      if(NULL != $3){
        $$ = $3;
      }
      else{
        // $$ = (InnerJoinSqlNode*)my_malloc(sizeof(InnerJoinSqlNode));
        // $$->joinRelations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy);
        // $$->conditions = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
        // $$->baseRelation = (StringPair*)my_malloc(sizeof(StringPair));
        // // $$ = InnerJoinSqlNodeCreate(baseRelation, joinRelations, conditions);
        $$ = InnerJoinSqlNodeCreate(StringPairCreate(NULL, NULL), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy));
      }
      $$->baseRelation->name = ($1);
      $$->baseRelation->alias =  ($2);
      varArrayListReverse($$->joinRelations);
      varArrayListReverse($$->conditions);
    }
    ;
joinList:
    /* empty */
    {
      $$ = NULL;
    }
    | INNER JOIN ID alias ON condition joinList{
      sp = NULL;
      if(NULL != $7){
        $$ = $7;
      }
      else{
        // $$ = (InnerJoinSqlNode*)my_malloc0(sizeof(InnerJoinSqlNode));
        // $$->joinRelations = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy);
        // $$->conditions = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
        // $$->baseRelation = StringPairCreate(NULL, NULL);
        $$ = InnerJoinSqlNodeCreate(StringPairCreate(NULL, NULL), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, StringPairPointerDestroy), varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy));
      }
      // sp = (StringPair*)my_malloc0(sizeof(StringPair));
      // sp->name = ($3);
      // sp->alias = ($4);
      sp = StringPairCreate($3, $4);

      varArrayListAddPointer($$->joinRelations, sp);
      varArrayListAddPointer($$->conditions, $6);
    }
    ;
    
subQueryExpr:
    LBRACE selectStmt RBRACE
    {
      $$ = exprCreate(ETG_SUBQUERY);
      ((SubQueryExpr*)$$)->sqlNode = ptrMove((void**)&$2->selection);
      ((SubQueryExpr*)$$)->name = tokenName(sqlString, &@$);
      ParsedSqlNodeDestroy($2);
    }
    ;
where:
    /* empty */
    {
      $$ = NULL;
    }
    | WHERE condition {
      $$ = $2;  
    }
    ;
condition:
    LBRACE condition RBRACE
    {
      $$ = $2;
    }
    | expression compOp expression 
    {
      cmpExpr = exprCreate(ETG_COMPARISON);
      cmpExpr->comp = $2;
      cmpExpr->left = $1;
      cmpExpr->right = $3;
      cmpExpr->name = tokenName(sqlString, &@$);
      $$ = (Expression*)cmpExpr;
    }
    | existsOp expression
    {
      value = NULL;
      valueExpr = NULL;
      cmpExpr = NULL;
      value = valueCreate();
      valueSetNull(value);
      valueExpr = exprCreate(ETG_VALUE);
      valueExpr->value = value;

      cmpExpr = exprCreate(ETG_COMPARISON);
      cmpExpr->comp = $1;
      cmpExpr->left = (Expression*)valueExpr;
      cmpExpr->right = $2;
      cmpExpr->name = tokenName(sqlString, &@$);
      $$ = (Expression*)cmpExpr;
    }
    | condition AND condition
    {
      children = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      conjExpr = exprCreate(ETG_CONJUNCTION);
      conjExpr->conjunctionType = CJET_AND;
      conjExpr->name = tokenName(sqlString, &@$);
      conjExpr->children = children;
      varArrayListAddPointer(conjExpr->children, $1);
      varArrayListAddPointer(conjExpr->children, $3);
      $$ = (Expression*)conjExpr;
    }
    | condition OR condition
    {
      conjExpr = exprCreate(ETG_CONJUNCTION);
      conjExpr->conjunctionType = CJET_OR;
      conjExpr->name = tokenName(sqlString, &@$);
      conjExpr->children = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      varArrayListAddPointer(conjExpr->children, $1);
      varArrayListAddPointer(conjExpr->children, $3);
      $$ = (Expression*)conjExpr;
    }
    ;

sortUnit:
	expression
	{
    $$ = OrderBySqlNodeCreate($1, true);
	}
	|
	expression DESC
	{
    $$ = OrderBySqlNodeCreate($1, false);
	}
	|
	expression ASC
	{
    $$ = OrderBySqlNodeCreate($1, true);
	}
	;


sortList:
	sortUnit
	{
    $$ = varArrayListCreate(DISORDER, sizeof(OrderBySqlNode*), 0, NULL, OrderBySqlNodePointerDestroy);
    varArrayListAddPointer($$, $1);
	}
  |
	sortUnit COMMA sortList
	{
    varArrayListAddPointer($3, $1);
    $$ = $3;
	}
	;

optOrderBy:
	/* empty */ {
   $$ = NULL;
  }
	| ORDER_T BY sortList
	{
    $$ = $3;
    varArrayListReverse($$);
	}
	;

optGroupBy:
	/* empty */ {
   $$ = NULL;
  }
	| GROUP BY expressionList
	{
      $$ = $3;
      varArrayListReverse($$);
	}
	;

optHaving:
  /* empty */ {
   $$ = NULL;
  }
	| HAVING condition
	{   
    $$ = $2;
	}
	;

optLimit:
    LIMIT NUMBER OFFSET NUMBER
    {
      $$ = LimitSqlNodeCreate($2, $4);
    }
    | LIMIT NUMBER
    {
      $$ = LimitSqlNodeCreate($2, -1);
    }
    | /* empty */
    {
      $$ = NULL; 
    }
    ;
compOp:
      EQ { $$ = CMPOP_EQUAL_TO; }
    | LT { $$ = CMPOP_LESS_THAN; }
    | GT { $$ = CMPOP_GREAT_THAN; }
    | LE { $$ = CMPOP_LESS_EQUAL; }
    | GE { $$ = CMPOP_GREAT_EQUAL; }
    | NE { $$ = CMPOP_NOT_EQUAL; }
    | LIKE { $$ = CMPOP_LIKE_OP;}
    | NOT LIKE {$$ = CMPOP_NOT_LIKE_OP;}
    | IN { $$ = CMPOP_IN_OP;}
    | NOT IN {$$ = CMPOP_NOT_IN_OP;}
    ;
existsOp:
    EXISTS { $$ = CMPOP_EXISTS_OP; }
    | NOT EXISTS { $$ = CMPOP_NOT_EXISTS_OP; }
    ;
loadDataStmt:
    LOAD DATA INFILE SSS INTO TABLE ID 
    {
      // char *tmpFileName = substr($4, 1, strlen($4) - 2);
      $$ = ParsedSqlNodeCreate(SCF_LOAD_DATA);
      $$->loadData->relationName = $7;
      $$->loadData->fileName = substr($4, 1, strlen($4) - 2);;
      my_free($4);
    }
    ;

explainStmt:
    EXPLAIN commandWrapper
    {
      $$ = ParsedSqlNodeCreate(SCF_EXPLAIN);
      $$->explain->sqlNode = (ParsedSqlNode*)$2;
    }
    ;

setVariableStmt:
    SET ID EQ value
    {
      $$ = ParsedSqlNodeCreate(SCF_SET_VARIABLE);
      $$->setVariable->name  = ($2);
      $$->setVariable->value = valueCopy($4);
      valueDestroy(&$4);
    }
    ;

optSemicolon: /*empty*/
    | SEMICOLON
    ;
%%
//_____________________________________________________________________
extern void scan_string(const char *str, yyscan_t scanner);
/* extern void valueSetInt(Value *value, int val); */
int sqlParse(const char *s, ParsedSqlResult *sqlResult) {
  int result;
  yyscan_t scanner;
  yylex_init(&scanner);
  scan_string(s, scanner);
  result = yyparse(s, sqlResult, scanner);
  yylex_destroy(scanner);
  return result;
}
