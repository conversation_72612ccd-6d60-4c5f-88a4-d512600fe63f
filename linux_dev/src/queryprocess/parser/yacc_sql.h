/* A Bison parser, made by GNU Bison 3.5.1.  */

/* Bison interface for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015, 2018-2020 Free Software Foundation,
   Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* Undocumented macros, especially those whose name start with YY_,
   are private implementation details.  Do not rely on them.  */

#ifndef YY_YY_YACC_SQL_H_INCLUDED
# define YY_YY_YACC_SQL_H_INCLUDED
/* Debug traces.  */
#ifndef YYDEBUG
# define YYDEBUG 1
#endif
#if YYDEBUG
extern int yydebug;
#endif
/* "%code requires" blocks.  */

#include "parse_defs.h" // 这里是为了在生成的.h文件包含一些所需的头文件


/* Token type.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
  enum yytokentype
  {
    SEMICOLON = 258,
    CREATE = 259,
    DROP = 260,
    TABLE = 261,
    TABLES = 262,
    INDEX = 263,
    CALC = 264,
    SELECT = 265,
    DESC = 266,
    SHOW = 267,
    SYNC = 268,
    INSERT = 269,
    DELETE = 270,
    UPDATE = 271,
    LBRACE = 272,
    RBRACE = 273,
    COMMA = 274,
    TRX_BEGIN = 275,
    TRX_COMMIT = 276,
    TRX_ROLLBACK = 277,
    INT_T = 278,
    BLOB_T = 279,
    DATE_T = 280,
    DATETIME_T = 281,
    TEXT_T = 282,
    STRING_T = 283,
    FLOAT_T = 284,
    HELP = 285,
    EXIT = 286,
    DOT = 287,
    INTO = 288,
    VALUES = 289,
    FROM = 290,
    WHERE = 291,
    AND = 292,
    OR = 293,
    SET = 294,
    USING = 295,
    HASH = 296,
    ON = 297,
    LOAD = 298,
    DATA = 299,
    INFILE = 300,
    EXPLAIN = 301,
    PRIMARY = 302,
    KEY = 303,
    NOT = 304,
    LIKE = 305,
    NULL_T = 306,
    EQ = 307,
    LT = 308,
    GT = 309,
    LE = 310,
    GE = 311,
    NE = 312,
    INNER = 313,
    JOIN = 314,
    AGGR_MAX = 315,
    AGGR_MIN = 316,
    AGGR_SUM = 317,
    AGGR_AVG = 318,
    AGGR_COUNT = 319,
    ORDER_T = 320,
    GROUP = 321,
    BY = 322,
    ASC = 323,
    HAVING = 324,
    AS = 325,
    IN = 326,
    EXISTS = 327,
    LIMIT = 328,
    OFFSET = 329,
    IF = 330,
    DISTINCT = 331,
    NUMBER = 332,
    FLOAT = 333,
    ID = 334,
    DATE_TIME_STR = 335,
    DATE_STR = 336,
    SSS = 337,
    UMINUS = 338
  };
#endif

/* Value type.  */
#if ! defined YYSTYPE && ! defined YYSTYPE_IS_DECLARED
union YYSTYPE
{

  ParsedSqlNode *                   sqlNode;
  Value *                           value;
  enum CompOp                       comp;
  RelAttrSqlNode *                  relAttr;
  varArrayList *                    attrInfos;       
  AttrInfoSqlNode *                 attrInfo;
  Expression *                      expression;
  varArrayList *                    expressionList;  
  varArrayList *                    valueList; 
  varArrayList *                    insertValueList;        /* element type:varArrayList<element type: Value> */     
  varArrayList *                    conditionList;   
  varArrayList *                    relAttrList;    
  varArrayList *                    relationList;   
  InnerJoinSqlNode *                innerJoins; 
  varArrayList*                     innerJoinsList;
  OrderBySqlNode*                   orderbyUnit;
  varArrayList*                     orderbyUnitList;
  char *                            string;
  int                               number;
  double                             floats;
  bool                              boolean;
  LimitSqlNode*                     limit;
  UpdateKV*                         updateKv;
  varArrayList*                     updateKvList;


};
typedef union YYSTYPE YYSTYPE;
# define YYSTYPE_IS_TRIVIAL 1
# define YYSTYPE_IS_DECLARED 1
#endif

/* Location type.  */
#if ! defined YYLTYPE && ! defined YYLTYPE_IS_DECLARED
typedef struct YYLTYPE YYLTYPE;
struct YYLTYPE
{
  int first_line;
  int first_column;
  int last_line;
  int last_column;
};
# define YYLTYPE_IS_DECLARED 1
# define YYLTYPE_IS_TRIVIAL 1
#endif



int yyparse (const char * sqlString, ParsedSqlResult * sqlResult, void * scanner);

#endif /* !YY_YY_YACC_SQL_H_INCLUDED  */
