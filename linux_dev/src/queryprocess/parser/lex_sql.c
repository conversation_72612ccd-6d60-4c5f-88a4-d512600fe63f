#line 2 "lex_sql.c"
/*
这里的代码会被复制到lex_sql.cpp的最开始位置
定义yy_size_t的原因是因为flex生成的代码，会使用yy_size_t与其他类型的数字
做比较，导致编译报警
*/
#define YY_TYPEDEF_YY_SIZE_T
typedef int yy_size_t;

/* 参考生成的lex_sql.cpp代码，这个宏定义会放在每次运行yylex()最开始的地方 */
#define YY_USER_INIT                                         \
  yycolumn = 0;

/* 参考生成的lex_sql.cpp代码，这个宏定义会放在解析一个token之后，也可以在网上找到大量的参考资料 */
/* 我们在这里设置当前解析的token的位置信息，这样在yacc中就可以使用这些信息了 */
#define YY_USER_ACTION                                       \
do {                                                         \
  yylloc->first_line   = yylloc->last_line = yylineno;       \
  yylloc->first_column = yycolumn;                           \
  yylloc->last_column  = yylloc->first_column + yyleng - 1;  \
  yycolumn += yyleng;                                        \
}                                                            \
while (0);

#line 26 "lex_sql.c"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

#ifdef yyget_lval
#define yyget_lval_ALREADY_DEFINED
#else
#define yyget_lval yyget_lval
#endif

#ifdef yyset_lval
#define yyset_lval_ALREADY_DEFINED
#else
#define yyset_lval yyset_lval
#endif

#ifdef yyget_lloc
#define yyget_lloc_ALREADY_DEFINED
#else
#define yyget_lloc yyget_lloc
#endif

#ifdef yyset_lloc
#define yyset_lloc_ALREADY_DEFINED
#else
#define yyset_lloc yyset_lloc
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* An opaque pointer. */
#ifndef YY_TYPEDEF_YY_SCANNER_T
#define YY_TYPEDEF_YY_SCANNER_T
typedef void* yyscan_t;
#endif

/* For convenience, these vars (plus the bison vars far below)
   are macros in the reentrant scanner. */
#define yyin yyg->yyin_r
#define yyout yyg->yyout_r
#define yyextra yyg->yyextra_r
#define yyleng yyg->yyleng_r
#define yytext yyg->yytext_r
#define yylineno (YY_CURRENT_BUFFER_LVALUE->yy_bs_lineno)
#define yycolumn (YY_CURRENT_BUFFER_LVALUE->yy_bs_column)
#define yy_flex_debug yyg->yy_flex_debug_r

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN yyg->yy_start = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START ((yyg->yy_start - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin , yyscanner )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = yyg->yy_hold_char; \
		YY_RESTORE_YY_MORE_OFFSET \
		yyg->yy_c_buf_p = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, yyg->yytext_ptr , yyscanner )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( yyg->yy_buffer_stack \
                          ? yyg->yy_buffer_stack[yyg->yy_buffer_stack_top] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE yyg->yy_buffer_stack[yyg->yy_buffer_stack_top]

void yyrestart ( FILE *input_file , yyscan_t yyscanner );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size , yyscan_t yyscanner );
void yy_delete_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yy_flush_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
void yypop_buffer_state ( yyscan_t yyscanner );

static void yyensure_buffer_stack ( yyscan_t yyscanner );
static void yy_load_buffer_state ( yyscan_t yyscanner );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file , yyscan_t yyscanner );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER , yyscanner)

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len , yyscan_t yyscanner );

void *yyalloc ( yy_size_t , yyscan_t yyscanner );
void *yyrealloc ( void *, yy_size_t , yyscan_t yyscanner );
void yyfree ( void * , yyscan_t yyscanner );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */

#define yywrap(yyscanner) (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP
typedef flex_uint8_t YY_CHAR;

typedef int yy_state_type;

#define yytext_ptr yytext_r

static yy_state_type yy_get_previous_state ( yyscan_t yyscanner );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  , yyscan_t yyscanner);
static int yy_get_next_buffer ( yyscan_t yyscanner );
static void yynoreturn yy_fatal_error ( const char* msg , yyscan_t yyscanner );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	yyg->yytext_ptr = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	yyg->yy_hold_char = *yy_cp; \
	*yy_cp = '\0'; \
	yyg->yy_c_buf_p = yy_cp;
#define YY_NUM_RULES 87
#define YY_END_OF_BUFFER 88
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[256] =
    {   0,
        0,    0,    0,    0,   88,   87,    1,    2,   87,   87,
       87,   71,   72,   83,   81,   73,   82,    6,   84,    3,
        5,   78,   74,   80,   70,   70,   70,   70,   70,   70,
       70,   70,   70,   70,   70,   70,   70,   70,   70,   70,
       70,   70,   70,   70,   70,   70,   70,   77,    0,   85,
        0,   86,    0,    3,   75,   76,   79,   70,   70,   70,
       62,   70,   70,   70,   60,   70,   70,   70,   70,   70,
       70,   70,   70,   70,   70,   70,   70,   70,   70,   67,
       63,   70,   70,   70,   70,   70,   70,   70,   70,   70,
       15,   23,   70,   70,   70,   70,   70,   70,   70,   70,

       70,   70,   70,   70,    4,   22,   57,   46,   70,   70,
       70,   70,   70,   70,   70,   70,   70,   70,   70,   70,
       70,   70,   70,   70,   70,   70,   70,   70,   70,   70,
       70,   70,   35,   70,   51,   70,   70,   70,   43,   44,
       52,   70,   70,   70,   70,   70,   70,   29,   70,   45,
       70,   70,   70,   70,   70,   70,   70,   70,   36,   19,
       39,   70,   70,   70,   42,   37,   70,    9,   70,   11,
       70,    7,   70,   70,   20,   70,   31,   70,    8,   70,
       70,   70,   70,   25,   56,   53,   70,   41,   54,   70,
       70,   70,   70,   70,   16,   17,   70,   68,   70,   70,

       70,   70,   32,   70,   47,   70,   70,   70,   70,   70,
       70,   40,   59,   70,   14,   70,   55,   70,   65,   70,
       58,   70,   70,   70,   12,   70,   30,   70,   21,   33,
       10,   70,   27,   70,   64,   70,   61,   48,   24,   66,
       70,   70,   18,   13,   28,   26,   70,   70,   49,   50,
       70,   38,   69,   34,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    2,    2,    3,
        1,    2,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    4,    5,    1,    1,    1,    1,    6,    7,
        8,    9,   10,   11,   12,   13,   14,   15,   15,   15,
       15,   15,   15,   15,   15,   15,   15,    1,   16,   17,
       18,   19,    1,    1,   20,   21,   22,   23,   24,   25,
       26,   27,   28,   29,   30,   31,   32,   33,   34,   35,
       36,   37,   38,   39,   40,   41,   42,   43,   44,   36,
        1,    1,    1,    1,   36,    1,   45,   46,   47,   48,

       49,   50,   51,   52,   53,   54,   55,   56,   57,   58,
       59,   60,   36,   61,   62,   63,   64,   65,   66,   67,
       68,   36,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[69] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    2,    1,    1,    1,    1,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2,    2,    2,
        2,    2,    2,    2,    2,    2,    2,    2
    } ;

static const flex_int16_t yy_base[261] =
    {   0,
        0,    0,    0,    0,  697,  698,  698,  698,  678,  690,
      688,  698,  698,  698,  698,  698,  698,  698,  698,   56,
      698,   54,  698,  675,   55,   59,   60,   61,   64,   87,
       62,   80,   76,   77,  113,  117,  118,  101,  124,  127,
      677,  125,  129,  171,  152,  132,  153,  698,  686,  698,
      684,  698,  674,   69,  698,  698,  698,    0,  673,  135,
      157,  174,  177,  160,  672,  184,  181,  191,  183,  194,
      196,  198,  187,  209,  195,  207,  215,  223,  220,  671,
      274,  228,  203,  235,  248,  227,  244,  257,  260,  258,
      670,  230,  272,  267,  288,  271,  269,  295,  294,  291,

      306,  289,  316,  324,  669,  668,  667,  666,  318,  320,
      296,  323,  330,  334,  335,  337,  341,  348,  344,  353,
      359,  354,  361,  362,  360,  364,  374,  388,  381,  384,
      393,  394,  386,  378,  665,  410,  400,  416,  664,  663,
      662,  418,  414,  411,  423,  425,  417,  654,  429,  652,
      432,  431,  436,  443,  435,  442,  446,  452,  651,  650,
      649,  450,  455,  457,  648,  458,  462,  647,  474,  645,
      469,  643,  471,  475,  642,  477,  641,  489,  640,  483,
      498,  494,  496,  639,  637,  636,  485,  634,  633,  504,
      502,  515,  519,  521,  630,  628,  527,  627,  530,  526,

      534,  546,  626,  541,  625,  547,  531,  549,  552,  551,
      559,  622,  620,  560,  616,  557,  615,  563,  594,  564,
      529,  577,  579,  576,  567,  573,  508,  585,  500,  490,
      454,  575,  399,  586,  389,  601,  363,  349,  299,  280,
      602,  603,  265,  264,  224,  202,  604,  605,  193,  151,
      606,  140,  139,  115,  698,  670,  672,  674,   84,   76
    } ;

static const flex_int16_t yy_def[261] =
    {   0,
      255,    1,  256,  256,  255,  255,  255,  255,  255,  257,
      258,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  255,  257,  255,
      258,  255,  255,  255,  255,  255,  255,  260,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,

      259,  259,  259,  259,  255,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,

      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,  259,  259,  259,  259,  259,  259,
      259,  259,  259,  259,    0,  255,  255,  255,  255,  255
    } ;

static const flex_int16_t yy_nxt[767] =
    {   0,
        6,    7,    8,    9,   10,   11,   12,   13,   14,   15,
       16,   17,   18,   19,   20,   21,   22,   23,   24,   25,
       26,   27,   28,   29,   30,   31,   32,   33,   34,   35,
       36,   37,   38,   39,   40,   41,   42,   43,   44,   45,
       46,   47,   41,   41,   25,   26,   27,   28,   29,   30,
       31,   32,   33,   34,   35,   36,   37,   38,   39,   40,
       42,   43,   44,   45,   46,   47,   41,   41,   53,   58,
       54,   55,   56,   58,   58,   58,   58,   58,   58,   66,
       70,   53,   63,   54,   71,   59,   67,   60,   72,   64,
       58,   58,   61,   68,   58,   62,   69,   73,   77,   78,

       80,   58,   65,   79,   66,   70,   74,   63,   81,   71,
       82,   67,   60,   72,   64,   58,   61,   75,   68,   62,
       69,   73,   77,   76,   78,   80,   65,   58,   79,   58,
       74,   58,   58,   81,   88,   82,   83,   86,   58,   58,
       89,   58,   75,   58,   84,   87,   58,   76,   90,   58,
       85,  103,   95,   58,   58,   96,   91,  106,   94,   88,
       92,   83,   86,   93,   89,   58,   58,   58,   97,   84,
       87,   58,   98,   90,   58,   85,  103,   95,  107,  104,
       96,   91,  106,   94,   92,   58,  101,   93,   58,  102,
       99,   58,   97,  110,  100,   58,   98,   58,   58,  108,

      112,   58,  109,  107,  104,   58,  115,   58,   58,   58,
       58,  101,   58,  102,  111,   99,   58,   58,  110,  100,
      120,   58,  113,   58,  108,  112,  117,  109,  123,   58,
      114,  115,  116,  118,   58,  119,  121,   58,   58,  111,
      124,   58,   58,  122,   58,  120,  135,  113,  125,   58,
      128,  117,  144,  123,  114,  134,  116,  118,   58,  119,
      126,  121,   58,  127,  136,  124,  137,  138,  122,  139,
      135,   58,   58,  125,   58,  128,  140,  144,   58,   58,
      134,   58,  143,   58,  126,   58,   58,  127,   58,  136,
      142,  137,  138,  139,   58,  141,  129,  146,  130,  145,

      150,  140,   58,   58,  149,   58,  131,  143,   58,   58,
       58,  132,  133,   58,  152,  142,  155,  160,  147,  141,
       58,  129,  146,  130,  145,  150,  148,  151,  154,  149,
       58,  131,   58,  153,   58,  132,  133,   58,   58,  152,
      159,  155,  160,  147,   58,  158,  156,  157,   58,   58,
      148,   58,  151,  154,  164,   58,  165,  153,   58,  161,
      166,  162,   58,   58,  167,  159,  163,   58,   58,  168,
      158,  156,  157,   58,   58,   58,   58,   58,   58,  164,
      174,  165,  169,  161,  173,  166,  162,  170,   58,  167,
      177,  163,   58,  175,  168,   58,  171,  172,   58,  176,

       58,  178,   58,   58,  180,  174,  169,   58,   58,  173,
      185,  181,  170,   58,   58,  177,  182,  183,  175,  184,
      171,  172,  179,  176,   58,   58,  178,  187,   58,  180,
       58,   58,   58,  186,  191,  185,  181,   58,  188,   58,
      194,  182,  183,   58,  184,   58,   58,  179,  189,   58,
       58,  190,  187,  196,  192,  193,   58,   58,  186,  191,
       58,  197,  199,  188,   58,  194,   58,  200,   58,   58,
      195,   58,   58,  189,  198,  190,   58,  204,  196,  192,
      193,  201,  202,   58,  203,   58,  197,  199,   58,   58,
      211,   58,  200,  205,  195,  206,  207,   58,  198,   58,

      208,  209,  204,   58,   58,  201,  202,  210,   58,  203,
       58,  213,   58,  212,   58,  211,   58,  205,   58,  206,
      207,  214,   58,  219,  208,  215,  209,  220,  216,   58,
      217,  210,  218,   58,  222,   58,  213,  212,  221,  223,
       58,   58,  224,   58,   58,   58,  214,  219,   58,  215,
      225,  227,  220,  216,  217,   58,  218,  228,  232,  222,
       58,   58,  221,   58,  223,   58,   58,  224,  226,  229,
      231,   58,  233,   58,   58,  225,  227,   58,   58,  230,
      238,   58,  228,  232,  234,  237,  236,   58,  235,   58,
       58,   58,  226,   58,  229,  231,  245,  233,  242,   58,

       58,  239,  240,  230,  244,  238,  247,  248,   58,  234,
      237,  236,  235,  241,  243,   58,   58,   58,   58,   58,
       58,  245,  246,  242,  251,  239,  240,  252,  244,   58,
       58,  247,  248,  249,   58,  254,   58,  241,  243,   58,
       58,   58,   58,  253,   58,  250,  246,   58,   58,  251,
       58,   58,  252,   58,   58,   58,   58,   58,  249,   58,
      254,   58,   58,   58,   58,   58,   58,  253,   58,  250,
        6,    6,   49,   49,   51,   51,   58,   58,   58,   58,
       58,   58,   58,  105,   58,   58,   58,   58,  105,   52,
       50,   58,   57,   52,   50,   48,  255,    5,  255,  255,

      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255
    } ;

static const flex_int16_t yy_chk[767] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,   20,   25,
       20,   22,   22,   26,   27,   28,   31,  260,   29,   27,
       28,   54,   26,   54,   28,  259,   27,   25,   28,   26,
       33,   34,   25,   27,   32,   25,   27,   28,   31,   32,

       33,   30,   26,   32,   27,   28,   29,   26,   33,   28,
       34,   27,   25,   28,   26,   38,   25,   30,   27,   25,
       27,   28,   31,   30,   32,   33,   26,   35,   32,  254,
       29,   36,   37,   33,   38,   34,   35,   37,   39,   42,
       38,   40,   30,   43,   36,   37,   46,   30,   39,   60,
       36,   46,   43,  253,  252,   43,   39,   60,   42,   38,
       39,   35,   37,   40,   38,  250,   45,   47,   43,   36,
       37,   61,   43,   39,   64,   36,   46,   43,   61,   47,
       43,   39,   60,   42,   39,   44,   45,   40,   62,   45,
       44,   63,   43,   64,   44,   67,   43,   69,   66,   62,

       67,   73,   63,   61,   47,   68,   69,  249,   70,   75,
       71,   45,   72,   45,   66,   44,  246,   83,   64,   44,
       73,   76,   68,   74,   62,   67,   71,   63,   75,   77,
       68,   69,   70,   71,   79,   72,   74,   78,  245,   66,
       76,   86,   82,   74,   92,   73,   83,   68,   77,   84,
       79,   71,   92,   75,   68,   82,   70,   71,   87,   72,
       78,   74,   85,   78,   84,   76,   84,   85,   74,   86,
       83,   88,   90,   77,   89,   79,   87,   92,  244,  243,
       82,   94,   90,   97,   78,   96,   93,   78,   81,   84,
       89,   84,   85,   86,  240,   88,   81,   94,   81,   93,

       97,   87,   95,  102,   96,  100,   81,   90,   99,   98,
      111,   81,   81,  239,   99,   89,  102,  111,   95,   88,
      101,   81,   94,   81,   93,   97,   95,   98,  101,   96,
      103,   81,  109,  100,  110,   81,   81,  112,  104,   99,
      110,  102,  111,   95,  113,  109,  103,  104,  114,  115,
       95,  116,   98,  101,  115,  117,  116,  100,  119,  112,
      116,  113,  118,  238,  117,  110,  114,  120,  122,  118,
      109,  103,  104,  121,  125,  123,  124,  237,  126,  115,
      123,  116,  119,  112,  122,  116,  113,  120,  127,  117,
      126,  114,  134,  124,  118,  129,  121,  121,  130,  125,

      133,  127,  128,  235,  129,  123,  119,  131,  132,  122,
      134,  130,  120,  233,  137,  126,  131,  132,  124,  133,
      121,  121,  128,  125,  136,  144,  127,  137,  143,  129,
      138,  147,  142,  136,  144,  134,  130,  145,  138,  146,
      147,  131,  132,  149,  133,  152,  151,  128,  142,  155,
      153,  143,  137,  151,  145,  146,  156,  154,  136,  144,
      157,  152,  154,  138,  162,  147,  158,  155,  231,  163,
      149,  164,  166,  142,  153,  143,  167,  162,  151,  145,
      146,  156,  157,  171,  158,  173,  152,  154,  169,  174,
      173,  176,  155,  163,  149,  164,  166,  180,  153,  187,

      167,  169,  162,  178,  230,  156,  157,  171,  182,  158,
      183,  176,  181,  174,  229,  173,  191,  163,  190,  164,
      166,  178,  227,  187,  167,  180,  169,  190,  181,  192,
      182,  171,  183,  193,  192,  194,  176,  174,  191,  193,
      200,  197,  194,  221,  199,  207,  178,  187,  201,  180,
      197,  200,  190,  181,  182,  204,  183,  201,  207,  192,
      202,  206,  191,  208,  193,  210,  209,  194,  199,  202,
      206,  216,  208,  211,  214,  197,  200,  218,  220,  204,
      216,  225,  201,  207,  209,  214,  211,  226,  210,  232,
      224,  222,  199,  223,  202,  206,  226,  208,  223,  228,

      234,  218,  220,  204,  225,  216,  232,  234,  219,  209,
      214,  211,  210,  222,  224,  236,  241,  242,  247,  248,
      251,  226,  228,  223,  242,  218,  220,  247,  225,  217,
      215,  232,  234,  236,  213,  251,  212,  222,  224,  205,
      203,  198,  196,  248,  195,  241,  228,  189,  188,  242,
      186,  185,  247,  184,  179,  177,  175,  172,  236,  170,
      251,  168,  165,  161,  160,  159,  150,  248,  148,  241,
      256,  256,  257,  257,  258,  258,  141,  140,  139,  135,
      108,  107,  106,  105,   91,   80,   65,   59,   53,   51,
       49,   41,   24,   11,   10,    9,    5,  255,  255,  255,

      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255,  255,  255,  255,  255,
      255,  255,  255,  255,  255,  255
    } ;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
#line 1 "lex_sql.l"

#line 28 "lex_sql.l"
#include<string.h>
#include<stdio.h>

/**
 * flex 代码包含三个部分，使用 %% 分隔
 * 第一个部分的代码是C代码，flex会原样复制到目标文件中
 * 第二个部分是规则部分，使用正则表达式定义一系列规则
 * 第三个部分还是C代码，flex 会复制此段代码
 */

#include "parse_defs.h"
#include "yacc_sql.h"

#ifndef register
#define register 
#endif // register

// extern int atoi();
// extern double atof();

// #define RETURN_TOKEN(token) LOG_DEBUG("%s", #token);return token
#define RETURN_TOKEN(token) return token

#line 746 "lex_sql.c"
/* 防止使用 -lfl 进行链接 */
#define YY_NO_INPUT 1
/* 禁止默认的内存管理，提供自己的内存分配器 */
/* 不区分大小写 */

/* 规则匹配的优先级：*/
/* 1. 匹配的规则长的优先 */
/* 2. 写在最前面的优先 */
/* yylval 就可以认为是 yacc 中 %union 定义的结构体(union 结构) */
#line 756 "lex_sql.c"

#define INITIAL 0
#define STR 1

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

/* Holds the entire state of the reentrant scanner. */
struct yyguts_t
    {

    /* User-defined. Not touched by flex. */
    YY_EXTRA_TYPE yyextra_r;

    /* The rest are the same as the globals declared in the non-reentrant scanner. */
    FILE *yyin_r, *yyout_r;
    size_t yy_buffer_stack_top; /**< index of top of stack. */
    size_t yy_buffer_stack_max; /**< capacity of stack. */
    YY_BUFFER_STATE * yy_buffer_stack; /**< Stack as an array. */
    char yy_hold_char;
    int yy_n_chars;
    int yyleng_r;
    char *yy_c_buf_p;
    int yy_init;
    int yy_start;
    int yy_did_buffer_switch_on_eof;
    int yy_start_stack_ptr;
    int yy_start_stack_depth;
    int *yy_start_stack;
    yy_state_type yy_last_accepting_state;
    char* yy_last_accepting_cpos;

    int yylineno_r;
    int yy_flex_debug_r;

    char *yytext_r;
    int yy_more_flag;
    int yy_more_len;

    YYSTYPE * yylval_r;

    YYLTYPE * yylloc_r;

    }; /* end struct yyguts_t */

static int yy_init_globals ( yyscan_t yyscanner );

    /* This must go here because YYSTYPE and YYLTYPE are included
     * from bison output in section 1.*/
    #    define yylval yyg->yylval_r
    
    #    define yylloc yyg->yylloc_r
    
int yylex_init (yyscan_t* scanner);

int yylex_init_extra ( YY_EXTRA_TYPE user_defined, yyscan_t* scanner);

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( yyscan_t yyscanner );

int yyget_debug ( yyscan_t yyscanner );

void yyset_debug ( int debug_flag , yyscan_t yyscanner );

YY_EXTRA_TYPE yyget_extra ( yyscan_t yyscanner );

void yyset_extra ( YY_EXTRA_TYPE user_defined , yyscan_t yyscanner );

FILE *yyget_in ( yyscan_t yyscanner );

void yyset_in  ( FILE * _in_str , yyscan_t yyscanner );

FILE *yyget_out ( yyscan_t yyscanner );

void yyset_out  ( FILE * _out_str , yyscan_t yyscanner );

			int yyget_leng ( yyscan_t yyscanner );

char *yyget_text ( yyscan_t yyscanner );

int yyget_lineno ( yyscan_t yyscanner );

void yyset_lineno ( int _line_number , yyscan_t yyscanner );

int yyget_column  ( yyscan_t yyscanner );

void yyset_column ( int _column_no , yyscan_t yyscanner );

YYSTYPE * yyget_lval ( yyscan_t yyscanner );

void yyset_lval ( YYSTYPE * yylval_param , yyscan_t yyscanner );

       YYLTYPE *yyget_lloc ( yyscan_t yyscanner );
    
        void yyset_lloc ( YYLTYPE * yylloc_param , yyscan_t yyscanner );
    
/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( yyscan_t yyscanner );
#else
extern int yywrap ( yyscan_t yyscanner );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int , yyscan_t yyscanner);
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * , yyscan_t yyscanner);
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( yyscan_t yyscanner );
#else
static int input ( yyscan_t yyscanner );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg , yyscanner)
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param , yyscan_t yyscanner);

#define YY_DECL int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param , yyscan_t yyscanner)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    yylval = yylval_param;

    yylloc = yylloc_param;

	if ( !yyg->yy_init )
		{
		yyg->yy_init = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! yyg->yy_start )
			yyg->yy_start = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack (yyscanner);
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner);
		}

		yy_load_buffer_state( yyscanner );
		}

	{
#line 77 "lex_sql.l"


#line 1042 "lex_sql.c"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = yyg->yy_c_buf_p;

		/* Support of yytext. */
		*yy_cp = yyg->yy_hold_char;

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = yyg->yy_start;
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				yyg->yy_last_accepting_state = yy_current_state;
				yyg->yy_last_accepting_cpos = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 256 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_base[yy_current_state] != 698 );

yy_find_action:
		yy_act = yy_accept[yy_current_state];
		if ( yy_act == 0 )
			{ /* have to back up */
			yy_cp = yyg->yy_last_accepting_cpos;
			yy_current_state = yyg->yy_last_accepting_state;
			yy_act = yy_accept[yy_current_state];
			}

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = yyg->yy_hold_char;
			yy_cp = yyg->yy_last_accepting_cpos;
			yy_current_state = yyg->yy_last_accepting_state;
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 79 "lex_sql.l"
// ignore whitespace
	YY_BREAK
case 2:
/* rule 2 can match eol */
YY_RULE_SETUP
#line 80 "lex_sql.l"
;
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 82 "lex_sql.l"
yylval->number=atoi(yytext); RETURN_TOKEN(NUMBER);
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 83 "lex_sql.l"
yylval->floats=(double)(atof(yytext)); RETURN_TOKEN(FLOAT);
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 84 "lex_sql.l"
RETURN_TOKEN(SEMICOLON);
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 85 "lex_sql.l"
RETURN_TOKEN(DOT);
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 86 "lex_sql.l"
RETURN_TOKEN(EXIT);
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 87 "lex_sql.l"
RETURN_TOKEN(HELP);
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 88 "lex_sql.l"
RETURN_TOKEN(DESC);
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 89 "lex_sql.l"
RETURN_TOKEN(CREATE);
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 90 "lex_sql.l"
RETURN_TOKEN(DROP);
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 91 "lex_sql.l"
RETURN_TOKEN(TABLE);
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 92 "lex_sql.l"
RETURN_TOKEN(TABLES);
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 93 "lex_sql.l"
RETURN_TOKEN(INDEX);
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 94 "lex_sql.l"
RETURN_TOKEN(ON);
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 95 "lex_sql.l"
RETURN_TOKEN(SHOW);
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 96 "lex_sql.l"
RETURN_TOKEN(SYNC);
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 97 "lex_sql.l"
RETURN_TOKEN(SELECT);
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 98 "lex_sql.l"
RETURN_TOKEN(CALC);
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 99 "lex_sql.l"
RETURN_TOKEN(FROM);
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 100 "lex_sql.l"
RETURN_TOKEN(WHERE);
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 101 "lex_sql.l"
RETURN_TOKEN(AND);
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 102 "lex_sql.l"
RETURN_TOKEN(OR);
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 103 "lex_sql.l"
RETURN_TOKEN(INSERT);
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 104 "lex_sql.l"
RETURN_TOKEN(INTO);
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 105 "lex_sql.l"
RETURN_TOKEN(VALUES);
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 106 "lex_sql.l"
RETURN_TOKEN(DELETE);
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 107 "lex_sql.l"
RETURN_TOKEN(UPDATE);
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 108 "lex_sql.l"
RETURN_TOKEN(SET);
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 109 "lex_sql.l"
RETURN_TOKEN(USING);
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 110 "lex_sql.l"
RETURN_TOKEN(HASH);
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 111 "lex_sql.l"
RETURN_TOKEN(TRX_BEGIN);
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 112 "lex_sql.l"
RETURN_TOKEN(TRX_COMMIT);
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 113 "lex_sql.l"
RETURN_TOKEN(TRX_ROLLBACK);
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 114 "lex_sql.l"
RETURN_TOKEN(INT_T);
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 115 "lex_sql.l"
RETURN_TOKEN(BLOB_T );
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 116 "lex_sql.l"
RETURN_TOKEN(DATE_T);
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 117 "lex_sql.l"
RETURN_TOKEN(DATETIME_T);
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 118 "lex_sql.l"
RETURN_TOKEN(STRING_T);
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 119 "lex_sql.l"
RETURN_TOKEN(FLOAT_T);
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 120 "lex_sql.l"
RETURN_TOKEN(LOAD);
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 121 "lex_sql.l"
RETURN_TOKEN(DATA);
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 122 "lex_sql.l"
RETURN_TOKEN(AGGR_MAX);
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 123 "lex_sql.l"
RETURN_TOKEN(AGGR_MIN);
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 124 "lex_sql.l"
RETURN_TOKEN(AGGR_SUM);
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 125 "lex_sql.l"
RETURN_TOKEN(AGGR_AVG);
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 126 "lex_sql.l"
RETURN_TOKEN(AGGR_COUNT);
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 127 "lex_sql.l"
RETURN_TOKEN(INFILE);
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 128 "lex_sql.l"
RETURN_TOKEN(EXPLAIN);
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 129 "lex_sql.l"
RETURN_TOKEN(PRIMARY);
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 130 "lex_sql.l"
RETURN_TOKEN(KEY);
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 131 "lex_sql.l"
RETURN_TOKEN(NOT);
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 132 "lex_sql.l"
RETURN_TOKEN(LIKE);
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 133 "lex_sql.l"
RETURN_TOKEN(NULL_T);
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 134 "lex_sql.l"
RETURN_TOKEN(INNER);
	YY_BREAK
case 56:
YY_RULE_SETUP
#line 135 "lex_sql.l"
RETURN_TOKEN(JOIN);
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 136 "lex_sql.l"
RETURN_TOKEN(ASC);
	YY_BREAK
case 58:
YY_RULE_SETUP
#line 137 "lex_sql.l"
RETURN_TOKEN(ORDER_T);
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 138 "lex_sql.l"
RETURN_TOKEN(GROUP);
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 139 "lex_sql.l"
RETURN_TOKEN(BY);
	YY_BREAK
case 61:
YY_RULE_SETUP
#line 140 "lex_sql.l"
RETURN_TOKEN(HAVING);
	YY_BREAK
case 62:
YY_RULE_SETUP
#line 141 "lex_sql.l"
RETURN_TOKEN(AS);
	YY_BREAK
case 63:
YY_RULE_SETUP
#line 142 "lex_sql.l"
RETURN_TOKEN(IN);
	YY_BREAK
case 64:
YY_RULE_SETUP
#line 143 "lex_sql.l"
RETURN_TOKEN(EXISTS);
	YY_BREAK
case 65:
YY_RULE_SETUP
#line 144 "lex_sql.l"
RETURN_TOKEN(LIMIT);
	YY_BREAK
case 66:
YY_RULE_SETUP
#line 145 "lex_sql.l"
RETURN_TOKEN(OFFSET);
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 146 "lex_sql.l"
RETURN_TOKEN(IF);
	YY_BREAK
case 68:
YY_RULE_SETUP
#line 147 "lex_sql.l"
RETURN_TOKEN(TEXT_T);
	YY_BREAK
case 69:
YY_RULE_SETUP
#line 148 "lex_sql.l"
RETURN_TOKEN(DISTINCT);
	YY_BREAK
case 70:
YY_RULE_SETUP
#line 149 "lex_sql.l"
yylval->string=strdup(yytext); RETURN_TOKEN(ID);
	YY_BREAK
case 71:
YY_RULE_SETUP
#line 150 "lex_sql.l"
RETURN_TOKEN(LBRACE);
	YY_BREAK
case 72:
YY_RULE_SETUP
#line 151 "lex_sql.l"
RETURN_TOKEN(RBRACE);
	YY_BREAK
case 73:
YY_RULE_SETUP
#line 153 "lex_sql.l"
RETURN_TOKEN(COMMA);
	YY_BREAK
case 74:
YY_RULE_SETUP
#line 154 "lex_sql.l"
RETURN_TOKEN(EQ);
	YY_BREAK
case 75:
YY_RULE_SETUP
#line 155 "lex_sql.l"
RETURN_TOKEN(LE);
	YY_BREAK
case 76:
YY_RULE_SETUP
#line 156 "lex_sql.l"
RETURN_TOKEN(NE);
	YY_BREAK
case 77:
YY_RULE_SETUP
#line 157 "lex_sql.l"
RETURN_TOKEN(NE);
	YY_BREAK
case 78:
YY_RULE_SETUP
#line 158 "lex_sql.l"
RETURN_TOKEN(LT);
	YY_BREAK
case 79:
YY_RULE_SETUP
#line 159 "lex_sql.l"
RETURN_TOKEN(GE);
	YY_BREAK
case 80:
YY_RULE_SETUP
#line 160 "lex_sql.l"
RETURN_TOKEN(GT);
	YY_BREAK
case 81:
#line 163 "lex_sql.l"
case 82:
#line 164 "lex_sql.l"
case 83:
#line 165 "lex_sql.l"
case 84:
YY_RULE_SETUP
#line 165 "lex_sql.l"
{ return yytext[0]; }
	YY_BREAK
case 85:
/* rule 85 can match eol */
YY_RULE_SETUP
#line 166 "lex_sql.l"
yylval->string = strdup(yytext); RETURN_TOKEN(SSS);
	YY_BREAK
case 86:
/* rule 86 can match eol */
YY_RULE_SETUP
#line 167 "lex_sql.l"
yylval->string = strdup(yytext); RETURN_TOKEN(SSS);
	YY_BREAK
case 87:
YY_RULE_SETUP
#line 171 "lex_sql.l"
ECHO;
	YY_BREAK
#line 1528 "lex_sql.c"
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(STR):
	yyterminate();

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - yyg->yytext_ptr) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = yyg->yy_hold_char;
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( yyg->yy_c_buf_p <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			yyg->yy_c_buf_p = yyg->yytext_ptr + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state( yyscanner );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state , yyscanner);

			yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++yyg->yy_c_buf_p;
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = yyg->yy_c_buf_p;
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer( yyscanner ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				yyg->yy_did_buffer_switch_on_eof = 0;

				if ( yywrap( yyscanner ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					yyg->yy_c_buf_p = yyg->yytext_ptr + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				yyg->yy_c_buf_p =
					yyg->yytext_ptr + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				yyg->yy_c_buf_p =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars];

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = yyg->yytext_ptr;
	int number_to_move, i;
	int ret_val;

	if ( yyg->yy_c_buf_p > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( yyg->yy_c_buf_p - yyg->yytext_ptr - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) (yyg->yy_c_buf_p - yyg->yytext_ptr - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) (yyg->yy_c_buf_p - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2) , yyscanner );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			yyg->yy_c_buf_p = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			yyg->yy_n_chars, num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	if ( yyg->yy_n_chars == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  , yyscanner);
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if ((yyg->yy_n_chars + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = yyg->yy_n_chars + number_to_move + (yyg->yy_n_chars >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size , yyscanner );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	yyg->yy_n_chars += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] = YY_END_OF_BUFFER_CHAR;

	yyg->yytext_ptr = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (yyscan_t yyscanner)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	yy_current_state = yyg->yy_start;

	for ( yy_cp = yyg->yytext_ptr + YY_MORE_ADJ; yy_cp < yyg->yy_c_buf_p; ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			yyg->yy_last_accepting_state = yy_current_state;
			yyg->yy_last_accepting_cpos = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 256 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state , yyscan_t yyscanner)
{
	int yy_is_jam;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner; /* This var may be unused depending upon options. */
	char *yy_cp = yyg->yy_c_buf_p;

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		yyg->yy_last_accepting_state = yy_current_state;
		yyg->yy_last_accepting_cpos = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 256 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 255);

	(void)yyg;
	return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (yyscan_t yyscanner)
#else
    static int input  (yyscan_t yyscanner)
#endif

{
	int c;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	*yyg->yy_c_buf_p = yyg->yy_hold_char;

	if ( *yyg->yy_c_buf_p == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( yyg->yy_c_buf_p < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			/* This was really a NUL. */
			*yyg->yy_c_buf_p = '\0';

		else
			{ /* need more input */
			int offset = (int) (yyg->yy_c_buf_p - yyg->yytext_ptr);
			++yyg->yy_c_buf_p;

			switch ( yy_get_next_buffer( yyscanner ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin , yyscanner);

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap( yyscanner ) )
						return 0;

					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput(yyscanner);
#else
					return input(yyscanner);
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					yyg->yy_c_buf_p = yyg->yytext_ptr + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) yyg->yy_c_buf_p;	/* cast for 8-bit char's */
	*yyg->yy_c_buf_p = '\0';	/* preserve yytext */
	yyg->yy_hold_char = *++yyg->yy_c_buf_p;

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * @param yyscanner The scanner object.
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack (yyscanner);
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner);
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file , yyscanner);
	yy_load_buffer_state( yyscanner );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * @param yyscanner The scanner object.
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack (yyscanner);
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state( yyscanner );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	yyg->yy_did_buffer_switch_on_eof = 1;
}

static void yy_load_buffer_state  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	yyg->yytext_ptr = yyg->yy_c_buf_p = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	yyg->yy_hold_char = *yyg->yy_c_buf_p;
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * @param yyscanner The scanner object.
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state ) , yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2) , yyscanner );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file , yyscanner);

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * @param yyscanner The scanner object.
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf , yyscanner );

	yyfree( (void *) b , yyscanner );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file , yyscan_t yyscanner)

{
	int oerrno = errno;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	yy_flush_buffer( b , yyscanner);

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = file ? (isatty( fileno(file) ) > 0) : 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * @param yyscanner The scanner object.
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state( yyscanner );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  @param yyscanner The scanner object.
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack(yyscanner);

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		yyg->yy_buffer_stack_top++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state( yyscanner );
	yyg->yy_did_buffer_switch_on_eof = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  @param yyscanner The scanner object.
 */
void yypop_buffer_state (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER , yyscanner);
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if (yyg->yy_buffer_stack_top > 0)
		--yyg->yy_buffer_stack_top;

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state( yyscanner );
		yyg->yy_did_buffer_switch_on_eof = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (yyscan_t yyscanner)
{
	yy_size_t num_to_alloc;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if (!yyg->yy_buffer_stack) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		yyg->yy_buffer_stack = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset(yyg->yy_buffer_stack, 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		yyg->yy_buffer_stack_max = num_to_alloc;
		yyg->yy_buffer_stack_top = 0;
		return;
	}

	if (yyg->yy_buffer_stack_top >= (yyg->yy_buffer_stack_max) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = yyg->yy_buffer_stack_max + grow_size;
		yyg->yy_buffer_stack = (struct yy_buffer_state**)yyrealloc
								(yyg->yy_buffer_stack,
								num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset(yyg->yy_buffer_stack + yyg->yy_buffer_stack_max, 0, grow_size * sizeof(struct yy_buffer_state*));
		yyg->yy_buffer_stack_max = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state ) , yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b , yyscanner );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr , yyscan_t yyscanner)
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) , yyscanner);
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n , yyscanner );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n , yyscanner);
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = yyg->yy_hold_char; \
		yyg->yy_c_buf_p = yytext + yyless_macro_arg; \
		yyg->yy_hold_char = *yyg->yy_c_buf_p; \
		*yyg->yy_c_buf_p = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the user-defined data for this scanner.
 * @param yyscanner The scanner object.
 */
YY_EXTRA_TYPE yyget_extra  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyextra;
}

/** Get the current line number.
 * @param yyscanner The scanner object.
 */
int yyget_lineno  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yylineno;
}

/** Get the current column number.
 * @param yyscanner The scanner object.
 */
int yyget_column  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yycolumn;
}

/** Get the input stream.
 * @param yyscanner The scanner object.
 */
FILE *yyget_in  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyin;
}

/** Get the output stream.
 * @param yyscanner The scanner object.
 */
FILE *yyget_out  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyout;
}

/** Get the length of the current token.
 * @param yyscanner The scanner object.
 */
int yyget_leng  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyleng;
}

/** Get the current token.
 * @param yyscanner The scanner object.
 */

char *yyget_text  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yytext;
}

/** Set the user-defined data. This data is never touched by the scanner.
 * @param user_defined The data to be associated with this scanner.
 * @param yyscanner The scanner object.
 */
void yyset_extra (YY_EXTRA_TYPE  user_defined , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyextra = user_defined ;
}

/** Set the current line number.
 * @param _line_number line number
 * @param yyscanner The scanner object.
 */
void yyset_lineno (int  _line_number , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* lineno is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "yyset_lineno called with no buffer" );
    
    yylineno = _line_number;
}

/** Set the current column.
 * @param _column_no column number
 * @param yyscanner The scanner object.
 */
void yyset_column (int  _column_no , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* column is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "yyset_column called with no buffer" );
    
    yycolumn = _column_no;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * @param yyscanner The scanner object.
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyin = _in_str ;
}

void yyset_out (FILE *  _out_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyout = _out_str ;
}

int yyget_debug  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yy_flex_debug;
}

void yyset_debug (int  _bdebug , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yy_flex_debug = _bdebug ;
}

/* Accessor methods for yylval and yylloc */

YYSTYPE * yyget_lval  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylval;
}

void yyset_lval (YYSTYPE *  yylval_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylval = yylval_param;
}

YYLTYPE *yyget_lloc  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylloc;
}
    
void yyset_lloc (YYLTYPE *  yylloc_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylloc = yylloc_param;
}
    
/* User-visible API */

/* yylex_init is special because it creates the scanner itself, so it is
 * the ONLY reentrant function that doesn't take the scanner as the last argument.
 * That's why we explicitly handle the declaration, instead of using our macros.
 */
int yylex_init(yyscan_t* ptr_yy_globals)
{
    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }

    *ptr_yy_globals = (yyscan_t) yyalloc ( sizeof( struct yyguts_t ), NULL );

    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }

    /* By setting to 0xAA, we expose bugs in yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));

    return yy_init_globals ( *ptr_yy_globals );
}

/* yylex_init_extra has the same functionality as yylex_init, but follows the
 * convention of taking the scanner as the last argument. Note however, that
 * this is a *pointer* to a scanner, as it will be allocated by this call (and
 * is the reason, too, why this function also must handle its own declaration).
 * The user defined value in the first argument will be available to yyalloc in
 * the yyextra field.
 */
int yylex_init_extra( YY_EXTRA_TYPE yy_user_defined, yyscan_t* ptr_yy_globals )
{
    struct yyguts_t dummy_yyguts;

    yyset_extra (yy_user_defined, &dummy_yyguts);

    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }

    *ptr_yy_globals = (yyscan_t) yyalloc ( sizeof( struct yyguts_t ), &dummy_yyguts );

    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }

    /* By setting to 0xAA, we expose bugs in
    yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));

    yyset_extra (yy_user_defined, *ptr_yy_globals);

    return yy_init_globals ( *ptr_yy_globals );
}

static int yy_init_globals (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    yyg->yy_buffer_stack = NULL;
    yyg->yy_buffer_stack_top = 0;
    yyg->yy_buffer_stack_max = 0;
    yyg->yy_c_buf_p = NULL;
    yyg->yy_init = 0;
    yyg->yy_start = 0;

    yyg->yy_start_stack_ptr = 0;
    yyg->yy_start_stack_depth = 0;
    yyg->yy_start_stack =  NULL;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER , yyscanner );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state(yyscanner);
	}

	/* Destroy the stack itself. */
	yyfree(yyg->yy_buffer_stack , yyscanner);
	yyg->yy_buffer_stack = NULL;

    /* Destroy the start condition stack. */
        yyfree( yyg->yy_start_stack , yyscanner );
        yyg->yy_start_stack = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( yyscanner);

    /* Destroy the main struct (reentrant only). */
    yyfree ( yyscanner , yyscanner );
    yyscanner = NULL;
    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;

	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s , yyscan_t yyscanner)
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

#define YYTABLES_NAME "yytables"

#line 171 "lex_sql.l"


void scan_string(const char *str, yyscan_t scanner) {
  yy_switch_to_buffer(yy_scan_string(str, scanner), scanner);
}

void *yyalloc (yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	return my_malloc0(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	return my_realloc(ptr, size);
}

void yyfree (void * ptr , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	my_free( (char *) ptr );
}

/* .                                       LOG_DEBUG("Unknown character [%c]",yytext[0]); return yytext[0];
 */
/* .                                       return yytext[0]; */
