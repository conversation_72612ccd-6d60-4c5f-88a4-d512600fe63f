#include "value.h"
#include "expression.h"
#include <string.h>
#include "gncdbconstant.h"
#include "math.h"
const char *ATTR_TYPE_NAME[] = {"undefined", "chars", "ints", "dates", "floats", "booleans"};

const char *attrTypeToString(AttrType type)
{
  if (type >= UNDEFINED && type <= DOUBLES) {
    return ATTR_TYPE_NAME[type];
  }
  return "unknown";
}

AttrType stringTOattrType(const char *s)
{
  int i = 0;
  for (i = 0; i < sizeof(ATTR_TYPE_NAME) / sizeof(ATTR_TYPE_NAME[0]); i++) {
    if (0 == strcmp(ATTR_TYPE_NAME[i], s)) {
      return (AttrType)i;
    }
  }
  return UNDEFINED;
}

void valueSetData(Value *value, char *data, int length)
{
  // Implementation
}

void valueSetInt(Value *value, int val)
{
  value->attrType          = INTS;
  value->length            = sizeof(val);
  value->numValue.intValue = val;
  value->isMalloc          = false;
  value->strValue          = NULL;
}

void valueSetDate(Value *value, int val)
{
  value->attrType          = DATES;
  value->length            = sizeof(val);
  value->numValue.intValue = val;
  value->isMalloc          = false;
  value->strValue          = NULL;
}

void valueSetDatetime(Value *value, DateTime val)
{
  value->attrType               = DATETIMES;
  value->length                 = sizeof(val);
  value->numValue.datetimeValue = val;
  value->isMalloc               = false;
  value->strValue               = NULL;
}

void valueSetDouble(Value *value, double val)
{
  value->attrType             = DOUBLES;
  value->length               = sizeof(val);
  value->numValue.doubleValue = val;
  value->isMalloc             = false;
  value->strValue             = NULL;
}

void valueSetBoolean(Value *value, int val)
{
  // Implementation
  value->attrType           = BOOLEANS;
  value->numValue.boolValue = val;
  value->length             = sizeof(bool);
  value->isMalloc           = false;
  value->strValue           = NULL;
}

/**
 * @brief 设置字符串类型的值（拷贝）
 *
 * @param value
 * @param s
 */
void valueSetString(Value *value, char *s)
{
  value->attrType = CHARS;
  value->length   = strlen(s);
  value->strValue = my_strdup(s);
  value->isMalloc = true;
}

/**
 * @brief   设置字符串类型的值（不拷贝）
 *
 * @param value
 */
void valueSetStringNoCopy(Value *value, char *s)
{
  value->attrType = CHARS;
  value->length   = strlen(s);
  value->strValue = s;
  value->isMalloc = false;
}

void valueSetNull(Value *value)
{
  value->attrType = NULLS;
  value->length   = 0;
  value->strValue = NULL;
  value->isMalloc = false;
}

// TODO: 完善blob类型
void valueSetBlob(Value *value)
{
  value->attrType = BLOB;
  value->length   = sizeof(int) * 2;
  value->overFlowPageId = 0;
  value->numValue.intValue = 0;
  value->strValue = NULL;
  value->isMalloc = false;
}

void valueSet(Value *dest, void *data)
{
  switch (dest->attrType) {
    case INTS: valueSetInt(dest, *(int *)data); break;
    case DATES: valueSetDate(dest, *(int *)data); break;
    case DATETIMES: valueSetDatetime(dest, *(DateTime *)data); break;
    case DOUBLES: valueSetDouble(dest, *(float *)data); break;
    case CHARS: valueSetString(dest, (char *)data); break;
    case BOOLEANS: valueSetBoolean(dest, *(int *)data); break;
    default: break;
  }
}

// const char *valueToString(const Value *value) {
//   // Implementation
//   return NULL;
// }

// int valueCompare(const Value *value1, const Value *value2) {
//   // Implementation
//   return 0;
// }

int length(const Value *value)
{
  // Implementation
  return 0;
}

AttrType attrType(const Value *value)
{
  // Implementation
  return UNDEFINED;
}

char *valueToString(const Value *value)
{
  char *str = NULL;
  int   len = 0;
  switch (value->attrType) {
    case CHARS: {
      return my_strdup(value->strValue);
    } break;
    case INTS: {
      len = snprintf(NULL, 0, "%d", value->numValue.intValue);
      str = (char *)my_malloc0(len + 1);
      if (str == NULL) {
        return NULL;
      }
      snprintf(str, len + 1, "%d", value->numValue.intValue);
      str[len] = '\0';
      return str;
    } break;
    case DATES: {
      len = snprintf(NULL, 0, "%d", value->numValue.intValue);
      str = (char *)my_malloc0(len + 1);
      if (str == NULL) {
        return NULL;
      }
      snprintf(str, len + 1, "%d", value->numValue.intValue);
      str[len] = '\0';
      return str;
    } break;
    case DATETIMES: {
      str = datetimeToString(&value->numValue.datetimeValue);
      return str;
    } break;
    case DOUBLES: {
      len = snprintf(NULL, 0, "%.13g", value->numValue.doubleValue);
      str = (char *)my_malloc0(len + 1);
      if (str == NULL) {
        return NULL;
      }
      snprintf(str, len + 1, "%.13g", value->numValue.doubleValue);
      str[len] = '\0';
      return str;
    } break;
    case BOOLEANS: {
      if (value->numValue.boolValue) {
        return my_strdup("true");
      } else {
        return my_strdup("false");
      }
    } break;
    case NULLS: {
      return my_strdup("NULL");
    } break;
    case BLOB: {
      return my_strdup("0");
    } break;
    default: {
    }
  }
  return NULL;
}

int valueGetInt(const Value *value)
{
  switch (value->attrType) {
    case CHARS: {
      return atoi(value->strValue);
    }
    case INTS: {
      return value->numValue.intValue;
    }
    //增加为dates类型的情况
    case DATES: {
      return value->numValue.intValue;
    }
    case DATETIMES: {
      // TODO 日期时间类型转换为int?
      // return value->num_value_.datetime_value_.date;
      return 0;
    }
    case DOUBLES: {
      return (int)(value->numValue.doubleValue + 0.5);
    }
    case BOOLEANS: {
      return (int)(value->numValue.boolValue);
    }
    default: {
      // printf("unknown data type. type=%d", value->attr_type_);
      return 0;
    }
  }
  return 0;
}

float valueGetFloat(const Value *value)
{
  switch (value->attrType) {
    case CHARS: {
      return atof(value->strValue);
    } break;
    case INTS: {
      return (float)(value->numValue.intValue);  // NOLINT
    } break;

    //增加为dates类型的情况
    case DATES: {
      // return float(num_value_.date_value_);
      return (float)(value->numValue.intValue);  // NOLINT
    } break;
    case DATETIMES: {
      // TODO 日期时间类型转换为float?
      return (float)(value->numValue.datetimeValue.date);  // NOLINT
    } break;

    case DOUBLES: {
      // return num_value_.floatValue;
      return value->numValue.doubleValue;
    } break;
    case BOOLEANS: {
      // return float(num_value_.bool_value_);
      return (float)(value->numValue.boolValue);  // NOLINT
    } break;
    default: {
      // printf("unknown data type. type=%d", value->attr_type_);
      return 0;
    }
  }
  return 0;
}

double valueGetDouble(const Value *value)
{
  switch (value->attrType) {
    case CHARS: {
      return atof(value->strValue);
    } break;
    case INTS: {
      return (double)(value->numValue.intValue);  // NOLINT
    } break;

    //增加为dates类型的情况
    case DATES: {
      // return float(num_value_.date_value_);
      return (double)(value->numValue.intValue);  // NOLINT
    } break;
    case DATETIMES: {
      // TODO 日期时间类型转换为float?
      return (double)(value->numValue.datetimeValue.date);  // NOLINT
    } break;

    case DOUBLES: {
      // return num_value_.floatValue;
      return value->numValue.doubleValue;
    } break;
    case BOOLEANS: {
      // return float(num_value_.bool_value_);
      return (double)(value->numValue.boolValue);  // NOLINT
    } break;
    default: {
      // printf("unknown data type. type=%d", value->attr_type_);
      return 0;
    }
  }
  return 0;
}

/**
 * @brief   获取value的字符串表示(动态分配内存，需要调用者释放)
 *
 * @param value
 * @return char*
 */
char *valueGetString(const Value *value)
{
  char *str = NULL;
  switch (value->attrType) {
    case CHARS: {
      return my_strdup(value->strValue);
    } break;
    case INTS: {
      str = (char *)my_malloc(20);
      sprintf(str, "%d", value->numValue.intValue);
      return str;
    } break;
    case DATES: {
      str = (char *)my_malloc0(20);
      sprintf(str, "%d", value->numValue.intValue);
      return str;
    } break;
    case DATETIMES: {
      str = datetimeToString(&value->numValue.datetimeValue);
      return str;
    } break;
    case DOUBLES: {
      str = (char *)my_malloc0(20);
      sprintf(str, "%f", value->numValue.doubleValue);
      return str;
    } break;
    case BOOLEANS: {
      str = (char *)my_malloc0(20);
      sprintf(str, "%d", value->numValue.boolValue);
      return str;
    } break;
    default: {
    }
  }
  return NULL;
}

int valueGetBoolean(const Value *value)
{

  // Implementation
  float floatVal = 0.0;
  int   intVal   = 0;
  switch (value->attrType) {
    case BOOLEANS: return value->numValue.boolValue; break;
    case CHARS: {
      floatVal = atof(value->strValue);
      if (floatVal >= EPSILON || floatVal <= -EPSILON) {
        return true;
      }
      intVal = atoi(value->strValue);
      if (intVal != 0) {
        return true;
      }
      return !(strlen(value->strValue) == 0);
    } break;
    case DOUBLES: {
      floatVal = value->numValue.doubleValue;
      return floatVal >= EPSILON || floatVal <= -EPSILON;
    } break;
    case INTS: {
      return value->numValue.intValue != 0;
    } break;
    default: {
    }
  }
  return false;
}

// 打印AttrType类型
void printAttrType(AttrType type)
{
  switch (type) {
    case UNDEFINED:
      // printf("UNDEFINED\n");
      break;
    case CHARS:
      // printf("CHARS\n");
      break;
    case INTS:
      // printf("INTS\n");
      break;
    case DATES:
      // printf("DATES\n");
      break;
    case DATETIMES:
      // printf("DATETIMES\n");
      break;
    case DOUBLES:
      // printf("FLOATS\n");
      break;
    case BOOLEANS:
      // printf("BOOLEANS\n");
      break;
    default:
      // printf("Unknown AttrType\n");
      break;
  }
}

/*Value 比较*/
int valueCompareInt(Value *left, Value *right)
{
  return (left->numValue.intValue > right->numValue.intValue) - (left->numValue.intValue < right->numValue.intValue);
}

int valueCompareFloat(Value *left, Value *right)
{
  double res;
  if (left->numValue.doubleValue > 0 && right->numValue.doubleValue < 0) {
    return 1;
  }
  if (left->numValue.doubleValue < 0 && right->numValue.doubleValue > 0) {
    return -1;
  }
  res = left->numValue.doubleValue - right->numValue.doubleValue;
  if (fabs(res) < EPSILON) {
    return 0;
  } else if (res < 0) {
    return -1;
  } else {
    return 1;
  }
}

int valueCompareChar(Value *left, Value *right) { return strcmp(left->strValue, right->strValue); }

int ValueCompareDateTime(Value *left, Value *right)
{
  if (left->numValue.datetimeValue.date == right->numValue.datetimeValue.date) {
    if (left->numValue.datetimeValue.time == right->numValue.datetimeValue.time) {
      return 0;
    } else if (left->numValue.datetimeValue.time < right->numValue.datetimeValue.time) {
      return -1;
    } else {
      return 1;
    }
  } else if (left->numValue.datetimeValue.date < right->numValue.datetimeValue.date) {
    return -1;
  } else {
    return 1;
  }
}

int valueCompare(Value *left, Value *right)
{
  double leftData;
  double rightData;
  int   res = 0;
  // if (left->attrType == NULLS || right->attrType == NULLS) {
  //   if (left->attrType == NULLS && right->attrType == NULLS) {
  //     return 0;
  //   } else if (left->attrType == NULLS) {
  //     return -1;
  //   } else {
  //     return 1;
  //   }
  // }
  if (left->attrType == right->attrType) {
    switch (left->attrType) {
      case DATES:
      case INTS: res = valueCompareInt(left, right); break;
      case DOUBLES: res = valueCompareFloat(left, right); break;
      case CHARS: res = valueCompareChar(left, right); break;
      case BOOLEANS: res = valueCompareInt(left, right); break;
      case DATETIMES: res = ValueCompareDateTime(left, right); break;
      default: {
        PRINT("Unsupport AttrType\n");
      }
    }
  } else if (left->attrType == INTS && right->attrType == DOUBLES) {
    leftData = left->numValue.intValue;
    if (leftData == right->numValue.doubleValue) {
      res = 0;
    } else if (leftData < right->numValue.doubleValue) {
      res = -1;
    } else {
      res = 1;
    }
  } else if (left->attrType == DOUBLES && right->attrType == INTS) {
    rightData = right->numValue.intValue;
    if (left->numValue.doubleValue == rightData) {
      res = 0;
    } else if (left->numValue.doubleValue < rightData) {
      res = -1;
    } else {
      res = 1;
    }
  }
  return res;
}

Value *valueAdd(Value *left, Value *right)
{
  // Value* result = (Value*)my_malloc0(sizeof(Value));
  // 1.判断是否为空？？（null暂未实现）

  if (left->attrType == INTS && right->attrType == INTS) {
    left->attrType          = INTS;
    left->numValue.intValue = left->numValue.intValue + right->numValue.intValue;
  } else if (left->attrType == DOUBLES && right->attrType == DOUBLES) {
    left->attrType             = DOUBLES;
    left->numValue.doubleValue = left->numValue.doubleValue + right->numValue.doubleValue;
  } else if (left->attrType == INTS && right->attrType == DOUBLES) {
    left->attrType             = DOUBLES;
    left->numValue.doubleValue = left->numValue.intValue + right->numValue.doubleValue;
  } else if (left->attrType == DOUBLES && right->attrType == INTS) {
    left->attrType             = DOUBLES;
    left->numValue.doubleValue = left->numValue.doubleValue + right->numValue.intValue;
  } else {
    printf("Unsupport type:%d and %d\n", left->attrType, right->attrType);
    return NULL;
  }
  return left;
}

// int valueCompare(Value* left, Value* right){
//   if(left->attr_type_ == INTS && right->attr_type_ == INTS){
//     return left->num_value_.intValue - right->num_value_.intValue;
//   }else if(left->attr_type_ == FLOATS && right->attr_type_ == FLOATS){
//     if(left->num_value_.floatValue - right->num_value_.floatValue > 0){
//       return 1;
//     }else if(left->num_value_.floatValue - right->num_value_.floatValue < 0){
//       return -1;
//     }else{
//       return 0;
//     }
//   }else if(left->attr_type_ == INTS && right->attr_type_ == FLOATS){
//     if(left->num_value_.intValue - right->num_value_.floatValue > 0){
//       return 1;
//     }else if(left->num_value_.intValue - right->num_value_.floatValue < 0){
//       return -1;
//     }else{
//       return 0;
//     }
//   }else if(left->attr_type_ == FLOATS && right->attr_type_ == INTS){
//     if(left->num_value_.floatValue - right->num_value_.intValue > 0){
//       return 1;
//     }else if(left->num_value_.floatValue - right->num_value_.intValue < 0){
//       return -1;
//     }else{
//       return 0;
//     }
//   }else{
//     // printf("Unsupport type:%d and %d\n", left->attr_type_, right->attr_type_);
//   }
//   return -1;
// }

Value *valueDiv(Value *value, int cnt)
{
  if (value->attrType == INTS) {
    value->attrType             = DOUBLES;
    value->numValue.doubleValue = (float)value->numValue.intValue / (float)cnt;
  } else if (value->attrType == DOUBLES) {
    value->attrType             = DOUBLES;
    value->numValue.doubleValue = (float)value->numValue.doubleValue / (float)cnt;
  } else {
    printf("Unsupport type:%d\n", value->attrType);
  }
  return value;
}

/************************************date************************************/
bool checkDate(int year, int month, int day)
{
  static int mon[] = {0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
  // printf("check_date: year %d,month %d,day %d\n", year, month, day);
  bool leap = (year % 400 == 0 || (year % 100 && year % 4 == 0));
  if (year > 0 && (month > 0) && (month <= 12) && (day > 0) && (day <= ((month == 2 && leap) ? 1 : 0) + mon[month]))
    return true;
  else
    return false;
}

int stringToDate(char *str, int *date)
{
  int year, month, day;
  if (sscanf(str, "%d-%d-%d", &year, &month, &day) != 3) {
    return GNCDB_DATE_TYPE_INVALID;
  }
  if (!checkDate(year, month, day)) {
    return GNCDB_DATE_TYPE_INVALID;
  }
  *date = year * 10000 + month * 100 + day;
  return GNCDB_SUCCESS;
}

char *dateToString(int date)
{
  static char str[20];
  sprintf(str, "%d-%02d-%02d", date / 10000, date % 10000 / 100, date % 100);
  return my_strdup(str);
}

/************************************datetime************************************/
int stringToDatetime(char *str, DateTime *datetime)
{
  int year, month, day, hour, minute, second;
  if (sscanf(str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second) != 6) {
    return GNCDB_DATETIME_TYPE_INVALID;
  }
  if (!checkDate(year, month, day)) {
    return GNCDB_DATETIME_TYPE_INVALID;
  }
  if (hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59) {
    return GNCDB_DATETIME_TYPE_INVALID;
  }
  datetime->date = year * 10000 + month * 100 + day;
  datetime->time = hour * 10000 + minute * 100 + second;
  return GNCDB_SUCCESS;
}

/*  datetime转换为字符串 */
char *datetimeToString(const DateTime *datetime)
{
  static char str[40];
  sprintf(str,
      "%d-%02d-%02d %02d:%02d:%02d",
      datetime->date / 10000,
      datetime->date % 10000 / 100,
      datetime->date % 100,
      datetime->time / 10000,
      datetime->time % 10000 / 100,
      datetime->time % 100);
  return my_strdup(str);
}

int datetimeCompare(DateTime *left, DateTime *right)
{
  if (left->date == right->date) {
    if (left->time == right->time) {
      return 0;
    } else if (left->time < right->time) {
      return -1;
    } else {
      return 1;
    }
  } else if (left->date < right->date) {
    return -1;
  } else {
    return 1;
  }
}

int datetimeStringCompare(char *left, char *right)
{
  DateTime leftDatetime, rightDatetime;
  if (stringToDatetime(left, &leftDatetime) != GNCDB_SUCCESS) {
    return GNCDB_DATETIME_TYPE_INVALID;
  }
  if (stringToDatetime(right, &rightDatetime) != GNCDB_SUCCESS) {
    return GNCDB_DATETIME_TYPE_INVALID;
  }
  return datetimeCompare(&leftDatetime, &rightDatetime);
}

int valueTypeCast(Value *value, AttrType targetType)
{
  int   intTmp   = 0;
  float floatTmp = 0.0;
  char *strTmp   = NULL;

  if (value->attrType == targetType) {
    return GNCDB_SUCCESS;
  }
  if (targetType == DATES || value->attrType == NULLS) {
    return GNCDB_TYPE_CAST_INVALID;
  }
  if (targetType == INTS && value->attrType == DATES) {
    return GNCDB_TYPE_CAST_INVALID;
  }

  if (targetType == INTS && value->attrType == CHARS) {
    return GNCDB_TYPE_CAST_INVALID;
  }
  if (targetType == CHARS && value->attrType == INTS) {
    return GNCDB_TYPE_CAST_INVALID;
  }
  switch (targetType) {
    case INTS: {
      intTmp = valueGetInt(value);
      valueSetInt(value, intTmp);
    } break;

    case DOUBLES: {
      floatTmp = valueGetFloat(value);
      valueSetDouble(value, floatTmp);
    } break;

    case CHARS: {
      strTmp = valueGetString(value);
      valueSetString(value, strTmp);
      my_free(strTmp);
    } break;

    case TEXTS: {
      // Q: 如何处理text类型？
      strTmp = valueGetString(value);
      valueSetText(value, strTmp);
      my_free(strTmp);
    } break;

    default: return GNCDB_TYPE_CAST_INVALID;
  }
  return GNCDB_SUCCESS;
}

void valueSetText(Value *value, char *str)
{
  value->attrType = TEXTS;
  value->length   = strlen(str);
  value->strValue = my_strdup(str);
  value->isMalloc = true;
}

/**
 * @brief 将insert values中的Expression转换为Value，并进行合法性检查
 *
 * @param insertExpr
 * @param value
 * @return true
 * @return false
 */
bool insertExprToValue(Expression *insertExpr, Value *value)
{
  ValueExpr      *valueExpr      = NULL;
  ArithmeticExpr *arithmeticExpr = NULL;
  if (insertExpr->type == ETG_VALUE) {
    valueExpr = (ValueExpr *)insertExpr;
    copyValueTo(valueExpr->value, value);
    return true;
  }
  /* 如果是算数表达式类型，只能是取负，其他算数类型均不合法 */
  if (insertExpr->type == ETG_ARITHMETIC) {
    arithmeticExpr = (ArithmeticExpr *)insertExpr;
    if (arithmeticExpr->arithmeticType != ARITH_NEGATIVE || arithmeticExpr->left->type != ETG_VALUE) {
      return false;
    }
    valueExpr = (ValueExpr *)arithmeticExpr->left;
    /* 取负的值只能是int或者float类型 */
    if (valueExpr->value->attrType != INTS && valueExpr->value->attrType != DOUBLES) {
      return false;
    }
    /* 负数取值 */
    if (valueExpr->value->attrType == INTS) {
      valueSetInt(value, -1 * valueExpr->value->numValue.intValue);
    } else {
      valueSetDouble(value, -1 * valueExpr->value->numValue.doubleValue);
    }
    return true;
  }
  return false;
}

// Value* valueCreate(AttrType type, void* val){
//   Value* value = (Value*)my_malloc0(sizeof(Value));
//   switch (type) {
//   case DOUBLES: {
//     valueSetDouble(value, *(float *)val);
//   } break;
//   case CHARS: {
//     valueSetString(value, (char *)val);
//   } break;
//   case INTS: {
//     valueSetInt(value, *(int *)(val));
//   }
//   // TODO 其他类型？
//   default: {
//   } break;
//   }
//   return value;
// }

Value *valueCreate()
{
  Value *value = (Value *)my_malloc0(sizeof(Value));
  if (value == NULL) {
    return NULL;
  }
  value->attrType          = UNDEFINED;
  value->length            = 0;
  value->numValue.intValue = 0;
  value->strValue          = NULL;
  value->isMalloc          = false;
  return value;
}

/* value结构体销毁函数 */
void valueDestroy(Value **value)
{
  if (value == NULL || *value == NULL) {
    return;
  }
  // TODO: 什么类型会使用strValue？？
  if ((*value)->isMalloc && (*value)->attrType == CHARS && (*value)->strValue != NULL) {
    my_free((*value)->strValue);
    (*value)->strValue = NULL;
  }
  my_free((*value));

  *value = NULL;
}

/* value指针销毁函数 */
void valuePointerDestroy(void *data)
{
  Value **value = (Value **)data;
  if (value == NULL || *value == NULL) {
    return;
  }
  valueDestroy(value);
  *value = NULL;
}

void valuefreeStr(Value *value)
{
  if(value){
    if (value->isMalloc && value->attrType == CHARS && value->strValue != NULL) {
      my_free(value->strValue);
      value->strValue = NULL;
    }
  }
}

void valueReset(Value *value)
{
  if (value == NULL) {
    return;
  }
  valuefreeStr(value);
  // value->attrType          = UNDEFINED;
  // value->length            = 0;
  // value->numValue.intValue = 0;
  // value->isMalloc          = false;
  memset(value, 0, sizeof(Value));
}

Value *valueCopy(Value *value)
{
  Value *newValue = (Value *)my_malloc0(sizeof(Value));
  if (newValue == NULL) {
    return NULL;
  }
  newValue->attrType = value->attrType;
  newValue->length   = value->length;
  newValue->numValue = value->numValue;
  newValue->isMalloc = false;
  if (value->attrType == CHARS && value->strValue != NULL) {
    newValue->strValue = my_strdup(value->strValue);
    newValue->isMalloc = true;
  } else {
    newValue->strValue = NULL;
  }
  return newValue;
}

/**
 * @brief   将src的值拷贝到dest（不拷贝字符串）
 *
 * @param src
 * @param dest
 */
void copyValueTo(Value *src, Value *dest)
{
  dest->attrType = src->attrType;
  dest->length   = src->length;
  dest->numValue = src->numValue;
  dest->isMalloc = false;
  if (src->attrType == CHARS && src->strValue != NULL) {
    dest->strValue = my_strdup(src->strValue);
    dest->isMalloc = true;
  } else {
    dest->strValue = NULL;
  }
}

void *valueGetPointer(Value *value)
{
  switch (value->attrType) {
    case INTS: return &value->numValue.intValue;
    case DOUBLES: return &value->numValue.doubleValue;
    case CHARS: return value->strValue;
    case DATES: return &value->numValue.intValue;
    case DATETIMES: return &value->numValue.datetimeValue;
    case BOOLEANS: return &value->numValue.boolValue;
    default: return NULL;
  }
}