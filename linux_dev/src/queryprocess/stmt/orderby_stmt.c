#include "orderby_stmt.h"
#include "expression.h"
#include "vararraylist.h"
/**
 * @brief   创建一个OrderByStmt
 *
 * @param db    数据库
 * @param defaultTable 默认表
 * @param tables    表集合
 * @param orderbySqlNodes   order by 子句
 * @param stmt  OrderByStmt
 * @param exprs     select clause 后的 field_expr (非a gg_expr 中的)和 agg_expr(内存所有权转移给函数内部处理)
 * @param aggrAliasSet  聚合表达式别名集合
 * @return int  状态码
 */
int OrderByStmtConstruct(GNCDB *db, BtreeTable *defaultTable, HashMap *tables, varArrayList *orderbySqlNodes,
    OrderByStmt **stmt, varArrayList *exprs, HashMap *aggrAliasSet)
{
  int             rc       = GNCDB_SUCCESS;
  varArrayList   *tmpUnits = NULL;
  OrderBySqlNode *node     = NULL;
  OrderByUnit    *unit     = NULL;
  OrderByStmt    *newStmt  = NULL;
  int             i        = 0;

  *stmt    = NULL;
  tmpUnits = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, OrderByUnitPointerDestroy);
  if (tmpUnits == NULL) {
    varArrayListDestroy(&exprs);
    return GNCDB_MEM;
  }

  for (i = 0; i < orderbySqlNodes->elementCount; i++) {
    node = (OrderBySqlNode *)varArrayListGetPointer(orderbySqlNodes, i);
    unit = OrderByUnitCreate();
    if (unit == NULL) {
      varArrayListDestroy(&tmpUnits);
      varArrayListDestroy(&exprs);
      return GNCDB_MEM;
    }
    /* 如果order by的字段名是聚合表达式别名 */
    if (hashMapExists(aggrAliasSet, node->expr->name)) {
      unit->expr = exprDeepCopy((Expression *)hashMapGet(aggrAliasSet, node->expr->name));
    }
    /* 如果order by的字段非聚合表达式别名 */
    else {
      unit->expr = exprDeepCopy(node->expr);
    }
    unit->isAsc = node->isAsc;
    varArrayListAddPointer(tmpUnits, unit);
  }
  newStmt = (OrderByStmt *)my_malloc0(sizeof(OrderByStmt));
  if (newStmt == NULL) {
    varArrayListDestroy(&tmpUnits);
    varArrayListDestroy(&exprs);
    return GNCDB_MEM;
  }
  newStmt->type         = ST_ORDER_BY;
  newStmt->orderByUnits = tmpUnits;
  newStmt->exprs        = exprs;
  *stmt                 = newStmt;

  return rc;
}

void OrderByStmtDestroy(OrderByStmt *stmt)
{
  if (stmt == NULL) {
    return;
  }
  if (stmt->orderByUnits != NULL) {
    varArrayListDestroy(&stmt->orderByUnits);
  }
  if (stmt->exprs != NULL) {
    varArrayListDestroy(&stmt->exprs);
  }
  my_free(stmt);
}

OrderByUnit *OrderByUnitCreate()
{
  OrderByUnit *unit = (OrderByUnit *)my_malloc0(sizeof(OrderByUnit));
  if (unit == NULL) {
    return NULL;
  }
  unit->expr  = NULL;
  unit->isAsc = true;
  return unit;
}

void OrderByUnitDestroy(OrderByUnit *unit)
{
  if (unit == NULL) {
    return;
  }
  if (unit->expr != NULL) {
    exprDestroy(unit->expr);
    unit->expr = NULL;
  }
  my_free(unit);
}

void OrderByUnitPointerDestroy(void *data)
{
  OrderByUnit **unit = (OrderByUnit **)data;
  if (unit == NULL || *unit == NULL) {
    return;
  }
  OrderByUnitDestroy(*unit);
  *unit = NULL;
}
