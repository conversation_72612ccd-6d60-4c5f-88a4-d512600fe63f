#include <errno.h>
#include <unistd.h>
#include "gncdbconstant.h"
#include "load_data_stmt.h"
#include "utils.h"
#include "parse_defs.h"
#include "gncdb.h"
LoadDataStmt *LoadDataStmtCreate(){
  LoadDataStmt *loadDataStmt = (LoadDataStmt *)my_malloc0(sizeof(LoadDataStmt));
  if (loadDataStmt == NULL) {
    return NULL;
  }
  loadDataStmt->type = ST_LOAD_DATA;
  loadDataStmt->filename = NULL;
  loadDataStmt->table = NULL;
  return loadDataStmt;
}

int LoadDataStmtConstruct(GNCDB *db, LoadDataSqlNode *loadData, Stmt **stmt)
{
  int           rc           = GNCDB_SUCCESS;
  BtreeTable   *table        = NULL;
  char         *tableName    = NULL;
  LoadDataStmt *loadDataStmt = NULL;

  /*1.检查load指令的表名和文件名是否合法*/
  tableName = loadData->relationName;
  if (isBlank(tableName) || isBlank(loadData->fileName)) {
    printf("invalid argument. db=%p, table_name=%p, file name=%s", db, tableName, loadData->fileName);
    return GNCDB_PARAMNULL;
  }

  /*2.检查load指令的表是否存在*/
  catalogGetTable(db->catalog, &table, loadData->relationName);
  if (NULL == table) {
    printf("no such table. db=%s, table_name=%s\n", db->fileName, tableName);
    return GNCDB_TABLE_NOT_FOUND;
  }

  if (0 != access(loadData->fileName, R_OK)) {
    printf("no such file to load. file name=%s\n, error=%s", loadData->fileName, strerror(errno));
    return GNCDB_FILE_NOT_FOUND;
  }

  loadDataStmt = LoadDataStmtCreate();
  if (loadDataStmt == NULL) {
    return GNCDB_MEM;
  }
  loadDataStmt->filename = PTR_MOVE((void **)&loadData->fileName);
  loadDataStmt->table    = table;
  *stmt                  = (Stmt *)loadDataStmt;

  return rc;
}

void LoadDataStmtDestroy(LoadDataStmt *loadDataStmt)
{
  if (loadDataStmt == NULL) {
    return;
  }
  if (loadDataStmt->filename != NULL) {
    my_free(loadDataStmt->filename);
  }
  loadDataStmt->table = NULL;
  my_free(loadDataStmt);
}
