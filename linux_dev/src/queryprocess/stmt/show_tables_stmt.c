#include "show_tables_stmt.h"
#include "gncdbconstant.h"
ShowTablesStmt *ShowTablesStmtCreate(){
  ShowTablesStmt *showTablesStmt = NULL;
  showTablesStmt = (ShowTablesStmt *)my_malloc0(sizeof(ShowTablesStmt));
  showTablesStmt->type = ST_SHOW_TABLES;
  return showTablesStmt;
}

int ShowTablesStmtConstruct(GNCDB *db, Stmt **stmt)
{
  ShowTablesStmt *showTablesStmt = NULL;

  showTablesStmt = ShowTablesStmtCreate();
  if (showTablesStmt == NULL)
  {
    return GNCDB_MEM;
  }
  *stmt                = (Stmt *)showTablesStmt;
  return GNCDB_SUCCESS;
}

void ShowTablesStmtDestroy(ShowTablesStmt *showTablesStmt) { my_free(showTablesStmt); }
