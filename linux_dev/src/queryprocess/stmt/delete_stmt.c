
#include "delete_stmt.h"
#include "hashmap.h"
#include "expression.h"
#include "gncdb.h"
int DeleteStmtConstruct(SQLStageEvent *sqlEvent, DeleteSqlNode *deleteSql, Stmt **stmt)
{
  int           rc         = GNCDB_SUCCESS;
  GNCDB        *db         = NULL;
  char         *tableName  = NULL;
  BtreeTable   *table      = NULL;
  HashMap      *tableMap   = NULL;
  FilterStmt   *filterStmt = NULL;
  varArrayList *tables     = NULL;
  DeleteStmt   *deleteStmt = NULL;

  db        = sqlEvent->db;
  tableName = deleteSql->relationName;

  /*1.参数检查*/
  if (NULL == db || NULL == tableName) {
    return GNCDB_PARAMNULL;
  }

  /*2.根据表名在目录获取表*/
  rc = catalogGetTable(db->catalog, &table, tableName);
  if (NULL == table) {
    return GNCDB_TABLE_NOT_FOUND;
  }

  /*3.将表放进定义的hashMap，vector中用于构造过滤算子*/
  tableMap = hashMapCreate(STRKEY, 0, NULL);
  if (tableMap == NULL) {
    return GNCDB_MAP_CREATE_FALSE;
  }
  hashMapPut(tableMap, tableName, table);
  tables = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (tables == NULL) {
    hashMapDestroy(&tableMap);
    return GNCDB_MEM;
  }
  varArrayListAddPointer(tables, table);

  /*4.根据where条件生成过滤stmt*/
  if (deleteSql->conditions != NULL) {
    rc = FilterStmtConstruct(sqlEvent, table, tables, tableMap, PTR_MOVE((void **)&deleteSql->conditions), &filterStmt);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to create filter statement. rc=%d", rc);
      hashMapDestroy(&tableMap);
      varArrayListDestroy(&tables);
      return rc;
    }
  }

  /*5.生成delete对应的stmt*/
  deleteStmt = (DeleteStmt *)my_malloc0(sizeof(DeleteStmt));
  if (deleteStmt == NULL) {
    hashMapDestroy(&tableMap);
    varArrayListDestroy(&tables);
    return GNCDB_MEM; /*malloc失败*/
  }
  deleteStmt->type       = ST_DELETE;
  deleteStmt->table      = table;
  deleteStmt->filterStmt = filterStmt;
  *stmt                  = (Stmt *)deleteStmt;

  hashMapDestroy(&tableMap);
  varArrayListDestroy(&tables);
  return rc;
}

void DeleteStmtDestroy(DeleteStmt *deleteStmt)
{
  if (deleteStmt == NULL) {
    return;
  }
  if (deleteStmt->filterStmt != NULL) {
    FilterStmtDestroy(deleteStmt->filterStmt);
  }
  my_free(deleteStmt);
}