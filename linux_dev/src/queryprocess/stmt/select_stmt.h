/*
 * @Author: zql <EMAIL>
 * @Date: 2025-05-29 20:59:38
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-07-30 16:27:32
 * @FilePath: /gncdbflr/linux_dev/src/queryprocess/stmt/select_stmt.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef SELECT_STMT_H
#define SELECT_STMT_H
#include "hashmap.h"
#include "stmt.h"
#include "filter_stmt.h"
#include "vararraylist.h"
#include "groupby_stmt.h"
#include "orderby_stmt.h"
#include "limit_stmt.h"
#include <stdarg.h>
#include <string.h>

/*------------------------------------ JoinNode ------------------------------------*/
typedef struct JoinNode
{
  JoinType      joinType;   /* 连接类型 */
  char         *tableName1; /* 左表名 */
  char         *tableName2; /* 右表名 */
  varArrayList *eqPKExprs;  /* 相等主键列连接条件 */
  varArrayList *eqExprs;    /* 相等连接条件 */
  varArrayList *otherExprs; /* 非相等连接条件 */
  int           leftPKNum;  /* 左边的主键个数 */
  int           rightPKNum; /* 右边的主键个数 */
} JoinNode;

/**
 * @description: 创建并初始化 JoinNode 结构体，内部的List都不设置销毁函数
 * @param {JoinNode*} joinNode 目标指针
 * @note: 该宏会将joinNode指针指向一个新的JoinNode结构体，并初始化其成员变量
 * @note: 该宏会将joinNode中的eqExprs、eqPKExprs和otherExprs初始化，但是没有销毁函数，需要手动销毁内容
 */
#define JOIN_NODE_CREATE(joinNode)                                                         \
  do {                                                                                     \
    (joinNode) = (JoinNode *)my_malloc0(sizeof(JoinNode));                                 \
    if ((joinNode) != NULL) {                                                              \
      (joinNode)->joinType   = JT_NESTED_LOOP;                                             \
      (joinNode)->tableName1 = NULL;                                                       \
      (joinNode)->tableName2 = NULL;                                                       \
      (joinNode)->eqExprs    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
      (joinNode)->eqPKExprs  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
      (joinNode)->otherExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
      (joinNode)->leftPKNum  = 0;                                                          \
      (joinNode)->rightPKNum = 0;                                                          \
    }                                                                                      \
  } while (0)

/**
 * @description: 销毁一个JoinNode，保留其中的所有表达式和字符串
 * @param {JoinNode*} joinNode 目标指针
 * @note: 该宏会释放joinNode指针，并将其置为NULL
 * @note: 该宏不会释放joinNode中的所有表达式，包括eqExprs、eqPKExprs和otherExprs
 * @note: 该宏不会释放joinNode中的表名字符串
 * @return {*}
 */
#define JOIN_NODE_SHALLOW_DESTROY(joinNode)             \
  do {                                                  \
    if ((joinNode) != NULL) {                           \
      if ((joinNode)->eqExprs) {                        \
        varArrayListDestroy(&((joinNode)->eqExprs));    \
      }                                                 \
      if ((joinNode)->eqPKExprs) {                      \
        varArrayListDestroy(&((joinNode)->eqPKExprs));  \
      }                                                 \
      if ((joinNode)->otherExprs) {                     \
        varArrayListDestroy(&((joinNode)->otherExprs)); \
      }                                                 \
      my_free(joinNode);                                \
      (joinNode) = NULL;                                \
    }                                                   \
  } while (0)

/**
 * @description: 销毁一个JoinNode，保留其中的所有表达式
 * @param {JoinNode*} joinNode 目标指针
 * @note: 该宏会释放joinNode指针，并将其置为NULL
 * @note: 该宏不会释放joinNode中的所有表达式，包括eqExprs、eqPKExprs和otherExprs
 * @note: 该宏会释放joinNode中的表名字符串
 * @return {*}
 */
#define JOIN_NODE_DESTROY(joinNode)                     \
  do {                                                  \
    if ((joinNode) != NULL) {                           \
      if ((joinNode)->tableName1) {                     \
        my_free((joinNode)->tableName1);                \
      }                                                 \
      if ((joinNode)->tableName2) {                     \
        my_free((joinNode)->tableName2);                \
      }                                                 \
      if ((joinNode)->eqExprs) {                        \
        varArrayListDestroy(&((joinNode)->eqExprs));    \
      }                                                 \
      if ((joinNode)->eqPKExprs) {                      \
        varArrayListDestroy(&((joinNode)->eqPKExprs));  \
      }                                                 \
      if ((joinNode)->otherExprs) {                     \
        varArrayListDestroy(&((joinNode)->otherExprs)); \
      }                                                 \
      my_free(joinNode);                                \
      (joinNode) = NULL;                                \
    }                                                   \
  } while (0)

/**
 * @description: 销毁一个JoinNode，深度销毁其中的所有表达式
 * @param {JoinNode*} joinNode 目标指针
 * @note: 该宏会释放joinNode指针，并将其置为NULL
 * @note: 该宏会释放joinNode中的所有表达式，包括eqExprs、eqPKExprs和otherExprs
 * @note: 该宏会释放joinNode中的表名字符串
 * @return {*}
 */
#define JOIN_NODE_EXPR_DESTROY(joinNode)                                  \
  do {                                                                    \
    if ((joinNode) != NULL) {                                             \
      if ((joinNode)->tableName1) {                                       \
        my_free((joinNode)->tableName1);                                  \
      }                                                                   \
      if ((joinNode)->tableName2) {                                       \
        my_free((joinNode)->tableName2);                                  \
      }                                                                   \
      if ((joinNode)->eqExprs) {                                          \
        for (int i = 0; i < (joinNode)->eqExprs->elementCount; i++) {     \
          exprDestroy(varArrayListGetPointer((joinNode)->eqExprs, i));    \
        }                                                                 \
        varArrayListDestroy(&((joinNode)->eqExprs));                      \
      }                                                                   \
      if ((joinNode)->eqPKExprs) {                                        \
        for (int i = 0; i < (joinNode)->eqPKExprs->elementCount; i++) {   \
          exprDestroy(varArrayListGetPointer((joinNode)->eqPKExprs, i));  \
        }                                                                 \
        varArrayListDestroy(&((joinNode)->eqPKExprs));                    \
      }                                                                   \
      if ((joinNode)->otherExprs) {                                       \
        for (int i = 0; i < (joinNode)->otherExprs->elementCount; i++) {  \
          exprDestroy(varArrayListGetPointer((joinNode)->otherExprs, i)); \
        }                                                                 \
        varArrayListDestroy(&((joinNode)->otherExprs));                   \
      }                                                                   \
      my_free(joinNode);                                                  \
      (joinNode) = NULL;                                                  \
    }                                                                     \
  } while (0)

/**
 * @description: 浅拷贝一个JoinNode，仅仅拷贝了字符串和表达式指针
 * @param {JoinNode*} dest 目标指针
 * @param {JoinNode*} src 源指针
 * @note: 该宏会将dest指针指向一个新的JoinNode结构体，并初始化其成员变量
 * @return {*}
 */
#define JOIN_NODE_SHALLOWCOPY(dest, src)                                                            \
  do {                                                                                              \
    if ((src) != NULL) {                                                                            \
      (dest) = (JoinNode *)my_malloc0(sizeof(JoinNode));                                            \
      if ((dest) != NULL) {                                                                         \
        (dest)->joinType   = (src)->joinType;                                                       \
        (dest)->tableName1 = (src)->tableName1 ? (src)->tableName1 : NULL;                          \
        (dest)->tableName2 = (src)->tableName2 ? (src)->tableName2 : NULL;                          \
        (dest)->eqExprs    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);            \
        for (int i = 0; i < (src)->eqExprs->elementCount; i++) {                                    \
          varArrayListAddPointer((dest)->eqExprs, varArrayListGetPointer((src)->eqExprs, i));       \
        }                                                                                           \
        (dest)->eqPKExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);             \
        for (int i = 0; i < (src)->eqPKExprs->elementCount; i++) {                                  \
          varArrayListAddPointer((dest)->eqPKExprs, varArrayListGetPointer((src)->eqPKExprs, i));   \
        }                                                                                           \
        (dest)->otherExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);            \
        for (int i = 0; i < (src)->otherExprs->elementCount; i++) {                                 \
          varArrayListAddPointer((dest)->otherExprs, varArrayListGetPointer((src)->otherExprs, i)); \
        }                                                                                           \
        (dest)->leftPKNum  = (src)->leftPKNum;                                                      \
        (dest)->rightPKNum = (src)->rightPKNum;                                                     \
      }                                                                                             \
    } else {                                                                                        \
      (dest) = NULL;                                                                                \
    }                                                                                               \
  } while (0)

/**
 * @description: 深拷贝一个JoinNode，深拷贝了其中的条件表达式
 * @param {JoinNode*} dest 目标指针
 * @param {JoinNode*} src 源指针
 * @note: 该宏会将dest指针指向一个新的JoinNode结构体，并初始化其成员变量
 * @note: 该宏会将src中的eqExprs、eqPKExprs和otherExprs中的表达式深拷贝到dest中
 * @return {*}
 */
#define JOIN_NODE_DEEPCOPY(dest, src)                                                                             \
  do {                                                                                                            \
    if ((src) != NULL) {                                                                                          \
      (dest) = (JoinNode *)my_malloc0(sizeof(JoinNode));                                                          \
      if ((dest) != NULL) {                                                                                       \
        (dest)->joinType   = (src)->joinType;                                                                     \
        (dest)->tableName1 = (src)->tableName1 ? strdup((src)->tableName1) : NULL;                                \
        (dest)->tableName2 = (src)->tableName2 ? strdup((src)->tableName2) : NULL;                                \
        (dest)->eqExprs    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);                          \
        for (int i = 0; i < (src)->eqExprs->elementCount; i++) {                                                  \
          varArrayListAddPointer((dest)->eqExprs, exprDeepCopy(varArrayListGetPointer((src)->eqExprs, i)));       \
        }                                                                                                         \
        (dest)->eqPKExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);                           \
        for (int i = 0; i < (src)->eqPKExprs->elementCount; i++) {                                                \
          varArrayListAddPointer((dest)->eqPKExprs, exprDeepCopy(varArrayListGetPointer((src)->eqPKExprs, i)));   \
        }                                                                                                         \
        (dest)->otherExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);                          \
        for (int i = 0; i < (src)->otherExprs->elementCount; i++) {                                               \
          varArrayListAddPointer((dest)->otherExprs, exprDeepCopy(varArrayListGetPointer((src)->otherExprs, i))); \
        }                                                                                                         \
        (dest)->leftPKNum  = (src)->leftPKNum;                                                                    \
        (dest)->rightPKNum = (src)->rightPKNum;                                                                   \
      }                                                                                                           \
    } else {                                                                                                      \
      (dest) = NULL;                                                                                              \
    }                                                                                                             \
  } while (0)

/**
 * @description: 深拷贝一个JoinNode List
 * @param {varArrayList*} dest 目标List指针
 * @param {varArrayList*} src 源List指针
 * @param {JoinNode*} destNode 目标JoinNode指针
 * @param {JoinNode*} srcNode 源JoinNode指针
 * @note: 该宏会将dest指针指向一个新的JoinNode List
 * @note: 该宏会将src中的JoinNode使用宏@JOIN_NODE_DEEPCOPY深拷贝到dest中
 * @return {*}
 */
#define JOIN_LIST_DEEPCOPY(dest, src)                                                  \
  JoinNode *destNode = NULL;                                                           \
  JoinNode *srcNode  = NULL;                                                           \
  do {                                                                                 \
    if (dest == NULL)                                                                  \
      dest = varArrayListCreate(DISORDER, BYTES_POINTER, 0, JoinNodePtrCompare, NULL); \
    for (int i = 0; i < (src)->elementCount; i++) {                                    \
      srcNode = (JoinNode *)varArrayListGetPointer((src), i);                          \
      JOIN_NODE_DEEPCOPY((destNode), (srcNode));                                       \
      varArrayListAddPointer((dest), (destNode));                                      \
    }                                                                                  \
  } while (0)

#define JOIN_LIST_SHALLOWCOPY(dest, src)                                               \
  do {                                                                                 \
    if (dest == NULL)                                                                  \
      dest = varArrayListCreate(DISORDER, BYTES_POINTER, 0, JoinNodePtrCompare, NULL); \
    for (int i = 0; i < (src)->elementCount; i++) {                                    \
      varArrayListAddPointer((dest), varArrayListGetPointer((src), i));                \
    }                                                                                  \
  } while (0)

#define JOIN_LIST_DEEPDESTROY(list)                      \
  do {                                                   \
    JoinNode *joinNode = NULL;                           \
    if ((list) != NULL) {                                \
      if (list->destroy == NULL) {                       \
        for (int i = 0; i < (list)->elementCount; i++) { \
          joinNode = varArrayListGetPointer((list), i);  \
          JOIN_NODE_EXPR_DESTROY(joinNode);              \
        }                                                \
      }                                                  \
      varArrayListDestroy(&(list));                      \
    }                                                    \
  } while (0)

#define REVERSE_COMP(comp)                              \
  ((comp) == CMPOP_EQUAL_TO         ? CMPOP_EQUAL_TO    \
      : (comp) == CMPOP_NOT_EQUAL   ? CMPOP_NOT_EQUAL   \
      : (comp) == CMPOP_GREAT_THAN  ? CMPOP_LESS_THAN   \
      : (comp) == CMPOP_LESS_THAN   ? CMPOP_GREAT_THAN  \
      : (comp) == CMPOP_GREAT_EQUAL ? CMPOP_LESS_EQUAL  \
      : (comp) == CMPOP_LESS_EQUAL  ? CMPOP_GREAT_EQUAL \
                                    : (comp))

/**
 * @description: 反转一个比较表达式：5 > a -> a < 5
 * @param expr 必须是ComparisonExpr*类型
 * @return {*}
 */
#define REVERSE_COMPARISON_EXPR(expr)              \
  do {                                             \
    Expression *temp = NULL;                       \
    (temp)           = (expr)->left;               \
    (expr)->left     = (expr)->right;              \
    (expr)->right    = (temp);                     \
    (expr)->comp     = REVERSE_COMP((expr)->comp); \
  } while (0)

void ReverseJoinNode(JoinNode *joinNode);
int  JoinNodePtrCompare(varArrayList *array, void *a, void *b);
void JoinNodeDestroy(JoinNode *joinNode);
void JoinNodePtrDestroy(void *data);
void JoinListPtrDestroy(void *data);

/*------------------------------------ SubJoin ------------------------------------*/

//* from后面的一个子连接：t1 inner join t2 on cond1 inner join t3 on cond2 */
typedef struct SubJoin
{
  HashMap      *tabMap;     // 该subjoin中的所有表，hashMap<tableName, BtreeTable*>
  varArrayList *joinNodes;  // 连接条件
} SubJoin;

/**
 * @description: 创建并初始化 SubJoin 结构体
 * @param {SubJoin*} subJoin 目标指针
 */
#define SUB_JOIN_CREATE(subJoin)                                                         \
  do {                                                                                   \
    (subJoin) = (SubJoin *)my_malloc0(sizeof(SubJoin));                                  \
    if ((subJoin) != NULL) {                                                             \
      (subJoin)->tabMap    = hashMapCreate(STRKEY, 0, NULL);                             \
      (subJoin)->joinNodes = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
    }                                                                                    \
  } while (0)

/**
 * @description: 销毁 SubJoin 结构体
 * @param {SubJoin*} subJoin 目标指针
 */
#define SUB_JOIN_DESTROY(subJoin)                     \
  do {                                                \
    if ((subJoin) != NULL) {                          \
      if ((subJoin)->tabMap) {                        \
        hashMapDestroy(&((subJoin)->tabMap));         \
      }                                               \
      if ((subJoin)->joinNodes) {                     \
        varArrayListDestroy(&((subJoin)->joinNodes)); \
      }                                               \
      my_free(subJoin);                               \
      (subJoin) = NULL;                               \
    }                                                 \
  } while (0)

/*------------------------------------ JoinTables ------------------------------------*/

typedef struct JoinTables
{
  StmtType      type;
  varArrayList *tables;       /* from后面所有涉及的表的集合 */
  HashMap      *joinedTables; /* 存在于JoinNodes中的表,是tables中的子集 */
  varArrayList *joinNodes;    /* 所有连接条件的集合 */
  varArrayList *subJoins;     /* from后面子连接集合 */
  varArrayList *topJoinNode;  /* 仅仅是where后面的连接结点，用以连接多个subJoin */
} JoinTables;

/**
 * @description: 创建并初始化 JoinTables 结构体
 * @param {JoinTables*} joinTables 目标指针
 */
#define JOIN_TABLES_CREATE(joinTables)                                                         \
  do {                                                                                         \
    (joinTables) = (JoinTables *)my_malloc0(sizeof(JoinTables));                               \
    if ((joinTables) != NULL) {                                                                \
      (joinTables)->type         = ST_JOIN_TABLES;                                             \
      (joinTables)->tables       = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
      (joinTables)->joinedTables = hashMapCreate(STRKEY, 0, NULL);                             \
      (joinTables)->joinNodes    = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
      (joinTables)->subJoins     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
      (joinTables)->topJoinNode  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
    }                                                                                          \
  } while (0)

/**
 * @description: 销毁 JoinTables 结构体
 * @param {JoinTables*} joinTables 目标指针
 */
#define JOIN_TABLES_DESTROY(joinTables)                                     \
  do {                                                                      \
    SubJoin  *subJoin  = NULL;                                              \
    JoinNode *joinNode = NULL;                                              \
    if ((joinTables) != NULL) {                                             \
      if ((joinTables)->tables) {                                           \
        varArrayListDestroy(&((joinTables)->tables));                       \
      }                                                                     \
      if ((joinTables)->joinedTables) {                                     \
        hashMapDestroy(&((joinTables)->joinedTables));                      \
      }                                                                     \
      if ((joinTables)->joinNodes) {                                        \
        for (int i = 0; i < ((joinTables)->joinNodes)->elementCount; i++) { \
          joinNode = varArrayListGetPointer(((joinTables)->joinNodes), i);  \
          JOIN_NODE_DESTROY(joinNode);                                      \
        }                                                                   \
        varArrayListDestroy(&((joinTables)->joinNodes));                    \
      }                                                                     \
      if ((joinTables)->subJoins) {                                         \
        for (int i = 0; i < ((joinTables)->subJoins)->elementCount; i++) {  \
          subJoin = varArrayListGetPointer(((joinTables)->subJoins), i);    \
          SUB_JOIN_DESTROY(subJoin);                                        \
        }                                                                   \
        varArrayListDestroy(&((joinTables)->subJoins));                     \
      }                                                                     \
      if ((joinTables)->topJoinNode) {                                      \
        varArrayListDestroy(&((joinTables)->topJoinNode));                  \
      }                                                                     \
      my_free(joinTables);                                                  \
      (joinTables) = NULL;                                                  \
    }                                                                       \
  } while (0)

JoinTables *JoinTablesCreate();
void        JoinTablesDestroy(JoinTables *joinTables);
void        JoinTablesPtrrDestroy(void *data);

/*------------------------------------ SelectStmt ------------------------------------*/

typedef struct SelectStmt
{
  StmtType      type;
  varArrayList *queryFields;
  JoinTables   *joinTables;
  FilterStmt   *filterStmt;
  GroupByStmt  *groupbyStmt;
  OrderByStmt  *orderbyStmt;
  FilterStmt   *havingStmt;
  LimitStmt    *limitStmt;
  int           isDistinct;
} SelectStmt;
SelectStmt *SelectStmtCreate();
int         SelectStmtConstruct(SQLStageEvent *sqlEvent, Stmt **stmt, HashMap *parentTableMap);
void        SelectStmtDestroy(SelectStmt *stmt);
#endif