#include "trx_end_stmt.h"
#include "parse_defs.h"
int TrxEndStmtConstruct(SqlCommandFlag flag, Stmt **stmt)
{
  StmtType    type       = -1;
  TrxEndStmt *trxEndStmt = NULL;

  type             = flag == SCF_COMMIT ? ST_COMMIT : ST_ROLLBACK;
  trxEndStmt       = (TrxEndStmt *)my_malloc0(sizeof(TrxEndStmt));
  if(trxEndStmt == NULL)
  {
    return GNCDB_MEM;
  }
  trxEndStmt->type = type;
  *stmt            = (Stmt *)trxEndStmt;
  return GNCDB_SUCCESS;
}

void TrxEndStmtDestroy(TrxEndStmt *trxEndStmt) { my_free(trxEndStmt); }
