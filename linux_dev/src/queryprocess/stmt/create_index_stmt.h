#ifndef CREATE_INDEX_STMT_H
#define CREATE_INDEX_STMT_H
#include "stmt.h"
#include "parse_defs.h"
typedef struct CreateIndexStmt {
  StmtType type;
  char* indexName;        // 索引名
  char* relationName;     // 表名
  char* attributeName;    // 列名
  IndexType indexType;    // 索引类型
} CreateIndexStmt;
CreateIndexStmt *CreateIndexStmtCreate();
int              CreateIndexStmtConstruct(GNCDB *db, CreateIndexSqlNode *createIndex, Stmt **stmt);
void             CreateIndexStmtDestroy(CreateIndexStmt *createIndexStmt);
#endif