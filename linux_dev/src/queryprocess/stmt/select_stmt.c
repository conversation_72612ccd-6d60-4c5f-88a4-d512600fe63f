#include <assert.h>
#include <stdbool.h>
#include <string.h>
#include "btreetable.h"
#include "catalog.h"
#include "hashmap.h"
#include "exec_tuple.h"
#include "filter_stmt.h"
#include "gncdbconstant.h"
#include "lookaside_mem.h"
#include "select_stmt.h"
#include "sql_event.h"
#include "typedefine.h"
#include "utils.h"
#include "expression.h"
#include "value.h"
#include "vararraylist.h"
extern int checkCond(
    Expression *expr, varArrayList *tables, HashMap *tableMap, SQLStageEvent *sqlEvent, BtreeTable *defaultTable);

extern int checkConditionSubQueryExpr(Expression *expr, varArrayList *list, int additionalParamsCount, va_list args);

extern int logicalPlanConstruct(SQLStageEvent *sqlEvent, Stmt *stmt, LogicalOperator **logicalOperator);

extern int physicalPlanConstruct(
    SQLStageEvent *sqlEvent, LogicalOperator *logicalOper, PhysicalOperator **physicalPlan);

extern int setColMeta(PhysicalOperator *physicalOper);

int JoinNodePtrCompare(varArrayList *array, void *a, void *b)
{
  JoinNode *node1 = NULL;
  JoinNode *node2 = NULL;
  int       cmp   = 0;

  if (a == NULL && b == NULL) {
    return 0;
  }
  if (a == NULL || b == NULL) {
    return 1;
  }
  node1 = *(JoinNode **)a;
  node2 = *(JoinNode **)b;
  if (node1 == NULL && node2 == NULL) {
    return 0;
  }
  if (node1 == NULL || node2 == NULL) {
    return 1;
  }
  cmp = strcmp(node1->tableName1, node2->tableName1);
  if (cmp != 0)
    return cmp;
  cmp = strcmp(node1->tableName2, node2->tableName2);
  return cmp;
}

void ReverseJoinNode(JoinNode *joinNode)
{
  int             tempNum  = joinNode->leftPKNum;
  char           *tempName = joinNode->tableName1;
  ComparisonExpr *compExpr = NULL;

  joinNode->rightPKNum = joinNode->leftPKNum;
  joinNode->leftPKNum  = tempNum;
  joinNode->tableName1 = joinNode->tableName2;
  joinNode->tableName2 = tempName;
  if (joinNode->eqExprs != NULL) {
    for (int i = 0; i < joinNode->eqExprs->elementCount; i++) {
      compExpr = (ComparisonExpr *)varArrayListGetPointer(joinNode->eqExprs, i);
      REVERSE_COMPARISON_EXPR(compExpr);
    }
  }
  if (joinNode->eqPKExprs != NULL) {
    for (int i = 0; i < joinNode->eqPKExprs->elementCount; i++) {
      compExpr = (ComparisonExpr *)varArrayListGetPointer(joinNode->eqPKExprs, i);
      REVERSE_COMPARISON_EXPR(compExpr);
    }
  }
  if (joinNode->otherExprs != NULL) {
    for (int i = 0; i < joinNode->otherExprs->elementCount; i++) {
      compExpr = (ComparisonExpr *)varArrayListGetPointer(joinNode->otherExprs, i);
      REVERSE_COMPARISON_EXPR(compExpr);
    }
  }
}

SelectStmt *SelectStmtCreate()
{
  SelectStmt *stmt = (SelectStmt *)my_malloc0(sizeof(SelectStmt));
  if (stmt == NULL) {
    return NULL;
  }
  stmt->type        = ST_SELECT;
  stmt->queryFields = NULL;
  stmt->joinTables  = NULL;
  stmt->filterStmt  = NULL;
  stmt->groupbyStmt = NULL;
  stmt->orderbyStmt = NULL;
  stmt->havingStmt  = NULL;
  stmt->limitStmt   = NULL;
  stmt->isDistinct  = 0;
  return stmt;
}

void fillField(Field *field, Column *column)
{
  switch (column->fieldType) {
    case FIELDTYPE_INTEGER:
      field->fieldType = (FieldType)INTS;
      field->fieldSize = INT_SIZE;
      break;
    case FIELDTYPE_REAL:
      field->fieldType = (FieldType)DOUBLES;
      field->fieldSize = DOUBLE_SIZE;
      break;
    case FIELDTYPE_VARCHAR:
      field->fieldType = (FieldType)CHARS;
      field->fieldSize = strlen(column->fieldName);
      break;
    case FIELDTYPE_BLOB:
      field->fieldType = (FieldType)BLOB;
      field->fieldSize = INT_SIZE;
      break;
    case FIELDTYPE_DATE:
      field->fieldType = (FieldType)DATES;
      field->fieldSize = INT_SIZE;
      break;
    case FIELDTYPE_DATETIME:
      field->fieldType = (FieldType)DATETIMES;
      field->fieldSize = INT_SIZE * 2;
      break;
    default:
      field->fieldType = (FieldType)UNDEFINED;
      field->fieldSize = -1;
      break;
  }
}

void fieldExprSetName(const char *tableName, const char *fieldName, FieldExpr *fieldExpr, bool isSingleTable)
{
  char  *dest = NULL;
  size_t tableLen;
  size_t fieldLen;
  size_t totalLen;
  /* 释放之前的内存 */
  if (fieldExpr->name != NULL) {
    my_free(fieldExpr->name);
    fieldExpr->name = NULL;
  }
  if (isSingleTable) {
    fieldExpr->name  = my_strdup(fieldName);
    fieldExpr->alias = NULL;
  } else {
    /* 计算所需内存大小：表名长度 + "." + 字段名长度 + 结束符 */
    tableLen = strlen(tableName);
    fieldLen = strlen(fieldName);
    totalLen = tableLen + 1 + fieldLen + 1; /* +1 for '.' +1 for '\0' */

    /* 一次性分配足够的内存 */
    fieldExpr->name = (char *)my_malloc(totalLen);
    if (fieldExpr->name == NULL) {
      return;
    }

    /* 高效字符串拼接：使用memcpy避免重复的strlen调用 */
    dest = fieldExpr->name;
    memcpy(dest, tableName, tableLen);
    dest += tableLen;
    *dest++ = '.';
    memcpy(dest, fieldName, fieldLen);
    dest += fieldLen;
    *dest = '\0';
  }
}

void wildcardFields(GNCDB *db, BtreeTable *table, char *alias, varArrayList *projects, bool isSingleTable)
{
  TableSchema *tableSchema = NULL;
  Column      *column      = NULL;
  FieldExpr   *fieldExpr   = NULL;
  int          i           = 0;
  int          fieldCount  = 0;
  char        *prefixName  = NULL;

  if (strcmp(table->tableName, "master") == 0) {
    tableSchema = db->catalog->masterTableSchema;
  } else if (strcmp(table->tableName, "schema") == 0) {
    tableSchema = db->catalog->schemaTableSchema;
  } else {
    tableSchema = hashMapGet(db->catalog->tableSchemaMap, table->tableName);
  }
  fieldCount = tableSchema->columnList->elementCount - 3;  /*-3是因为有三个属性是隐藏的*/
  /*生成每一个字段的名称：prefixName.filedname*/
  for (i = 0; i < fieldCount; i++) {
    column               = varArrayListGetPointer(tableSchema->columnList, i);
    fieldExpr            = my_new0(FieldExpr);
    fieldExpr->type      = ETG_FIELD;
    fieldExpr->tableName = my_strdup(table->tableName);
    fieldExpr->fieldName = my_strdup(column->fieldName);
    fieldExpr->fieldType = column->fieldType;
    /*若只有单个表或没有别名则前缀为表本名否则别名*/
    if (isSingleTable || isBlank(alias)) {
      prefixName = table->tableName;
    } else {
      prefixName = alias;
    }
    fieldExprSetName(prefixName, column->fieldName, fieldExpr, isSingleTable);
    varArrayListAddPointer(projects, (Expression *)fieldExpr);
  }
}

int checkAndCollectTables(StringPair *tableNamePair, GNCDB *db, HashMap *tableMap, HashMap *tableAliasMap)
{
  BtreeTable *table   = NULL;
  char       *srcName = NULL;
  char       *alias   = NULL;
  srcName             = tableNamePair->name;
  alias               = tableNamePair->alias;
  if (NULL == srcName) {
    return GNCDB_PARAMNULL;
  }
  catalogGetTable(db->catalog, &table, srcName);
  if (NULL == table) {
    return GNCDB_TABLE_NOT_FOUND;
  }
  myHashMapPut(tableMap, srcName, table);
  if (!isBlank(alias)) {
    myHashMapPut(tableAliasMap, srcName, alias);
    myHashMapPut(tableMap, alias, table);
  }
  return GNCDB_SUCCESS;
}

/**
 * @description: 处理比较条件和and连接的联结条件，如果符合连接规则则加入到JoinNode中并将原来删除
 * @param {Expression} * 待处理的表达式
 * @param {JoinTables} *joinTables 所有from后面的表
 * @param {HashMap} *tableMap 所有from后面的表的map，用来检查条件合法性
 * @param {SQLStageEvent} *sqlEvent
 * @return {*}
 */
int onCondToJoinNode(Expression **cond, JoinTables *joinTables, SubJoin *subJoin, HashMap *tableMap,
    SQLStageEvent *sqlEvent, bool isTopCond)
{
  ComparisonExpr  *compExpr   = NULL;
  ComparisonExpr  *eqExpr     = NULL;
  ComparisonExpr  *otherExpr  = NULL;
  ConjunctionExpr *conjExpr   = NULL;
  FieldExpr       *leftField  = NULL;
  FieldExpr       *rightField = NULL;
  Expression      *expr       = NULL;
  ExprTag          oldTag     = ETG_BASE;
  ExprTag          newTag     = ETG_BASE;
  JoinNode        *joinNode   = NULL;
  CompOp           swapOp     = 0;
  int              i          = 0;
  bool             swap       = false;  // 条件是否需要反转

  // 判断条件类型
  if ((*cond)->type != ETG_CONJUNCTION && (*cond)->type != ETG_COMPARISON) {
    return GNCDB_SUCCESS;
  }

  if ((*cond)->type == ETG_CONJUNCTION) {
    //* and连接的多个条件
    conjExpr = (ConjunctionExpr *)(*cond);
    for (i = 0; i < conjExpr->children->elementCount; i++) {
      expr   = varArrayListGetPointer(conjExpr->children, i);
      oldTag = expr->type;
      onCondToJoinNode(&expr, joinTables, subJoin, tableMap, sqlEvent, isTopCond);

      if (expr == NULL) {
        // 如果是子条件表达式被释放了，直接从父表达式数组删除
        varArrayListRemoveByIndexPointer(conjExpr->children, i);
        i--;
      } else {
        // 如果旧表达式是and连接的联结表达式，简化后只剩下一个比较表达式，替换
        newTag = expr->type;
        if (oldTag == ETG_CONJUNCTION && newTag == ETG_COMPARISON) {
          varArrayListSetByIndexPointer(conjExpr->children, i, expr);
        }
      }
    }
    // 处理剩余的条件
    if (conjExpr->children->elementCount == 0) {
      // 如果没有条件了，则删除这个连接条件
      exprDestroy((Expression *)conjExpr);
      *cond = NULL;
    } else if (conjExpr->children->elementCount == 1) {
      // 如果只有一个条件，则直接返回这个条件
      *cond = varArrayListGetPointer(conjExpr->children, 0);
      varArrayListRemoveByIndexPointer(conjExpr->children, 0);
      exprDestroy((Expression *)conjExpr);
    } else {
      *cond = (Expression *)conjExpr;
    }
  } else {
    //* 单个比较条件
    compExpr = (ComparisonExpr *)(*cond);

    // 连接条件仅仅支持field op field
    if (compExpr->left->type != ETG_FIELD || compExpr->right->type != ETG_FIELD) {
      return GNCDB_SUCCESS;
    }

    leftField  = (FieldExpr *)compExpr->left;
    rightField = (FieldExpr *)compExpr->right;

    // 1.检查当前是否存在相同的JoinNode
    for (i = 0; i < joinTables->joinNodes->elementCount; i++) {
      joinNode = varArrayListGetPointer(joinTables->joinNodes, i);
      if (strcmp(joinNode->tableName1, leftField->tableName) == 0 &&
          strcmp(joinNode->tableName2, rightField->tableName) == 0) {
        break;
      }
      if (strcmp(joinNode->tableName1, rightField->tableName) == 0 &&
          strcmp(joinNode->tableName2, leftField->tableName) == 0) {
        swap = true;
        break;
      }
    }

    // 2.如果没有找到相同的JoinNode，则创建一个新的JoinNode并加入
    if (i >= joinTables->joinNodes->elementCount) {
      JOIN_NODE_CREATE(joinNode);
      if (joinNode == NULL) {
        return GNCDB_MEM;
      }
      joinNode->tableName1 = my_strdup(leftField->tableName);
      joinNode->tableName2 = my_strdup(rightField->tableName);
      if (compExpr->comp == CMPOP_EQUAL_TO) {
        varArrayListAddPointer(joinNode->eqExprs, compExpr);
      } else {
        varArrayListAddPointer(joinNode->otherExprs, compExpr);
      }
      if (!hashMapExists(joinTables->joinedTables, joinNode->tableName1)) {
        hashMapPut(joinTables->joinedTables, joinNode->tableName1, NULL);
      }
      if (!hashMapExists(joinTables->joinedTables, joinNode->tableName2)) {
        hashMapPut(joinTables->joinedTables, joinNode->tableName2, NULL);
      }
      varArrayListAddPointer(joinTables->joinNodes, joinNode);
      if (subJoin != NULL) {
        varArrayListAddPointer(subJoin->joinNodes, joinNode);
      }
      if (isTopCond) {
        varArrayListAddPointer(joinTables->topJoinNode, joinNode);
      }
      *cond = NULL;
      return GNCDB_SUCCESS;
    }

    // 3.如果存在相同的JoinNode，检查JoinNode中是否已经存在相同的条件
    if (compExpr->comp == CMPOP_EQUAL_TO) {
      for (i = 0; i < joinNode->eqExprs->elementCount; i++) {
        eqExpr = (ComparisonExpr *)varArrayListGetPointer(joinNode->eqExprs, i);
        if (swap) {
          if (strcmp(((FieldExpr *)eqExpr->left)->fieldName, (rightField)->fieldName) == 0 &&
              strcmp(((FieldExpr *)eqExpr->right)->fieldName, (leftField)->fieldName) == 0) {
            return GNCDB_SUCCESS;
          }
        } else {
          if (strcmp(((FieldExpr *)eqExpr->left)->fieldName, (leftField)->fieldName) == 0 &&
              strcmp(((FieldExpr *)eqExpr->right)->fieldName, (rightField)->fieldName) == 0) {
            return GNCDB_SUCCESS;
          }
        }
      }
      if (swap) {
        REVERSE_COMPARISON_EXPR(compExpr);
      }
      varArrayListAddPointer(joinNode->eqExprs, compExpr);
      *cond = NULL;
      return GNCDB_SUCCESS;
    } else {
      for (i = 0; i < joinNode->otherExprs->elementCount; i++) {
        otherExpr = (ComparisonExpr *)varArrayListGetPointer(joinNode->otherExprs, i);
        if (swap) {
          swapOp = REVERSE_COMP(compExpr->comp);
          if (swapOp == otherExpr->comp &&
              strcmp(((FieldExpr *)otherExpr->left)->fieldName, (rightField)->fieldName) == 0 &&
              strcmp(((FieldExpr *)otherExpr->right)->fieldName, (leftField)->fieldName) == 0) {
            return GNCDB_SUCCESS;
          }
        } else {
          if (compExpr->comp == otherExpr->comp &&
              strcmp(((FieldExpr *)otherExpr->left)->fieldName, ((FieldExpr *)compExpr->left)->fieldName) == 0 &&
              strcmp(((FieldExpr *)otherExpr->right)->fieldName, ((FieldExpr *)compExpr->right)->fieldName) == 0) {
            return GNCDB_SUCCESS;
          }
        }
      }
      if (swap) {
        REVERSE_COMPARISON_EXPR(compExpr);
      }
      varArrayListAddPointer(joinNode->otherExprs, compExpr);
      *cond = NULL;
      return GNCDB_SUCCESS;
    }
  }

  return GNCDB_SUCCESS;
}

int checkSubQueryExpr(SubQueryExpr *subqueryExpr, HashMap *tableMap, SQLStageEvent *sqlEvent)
{
  int               rc                  = GNCDB_SUCCESS;
  Expression       *projectExpr         = NULL;
  Stmt             *selectStmt          = NULL;
  SelectStmt       *ss                  = NULL;
  LogicalOperator  *subLogicalOperator  = NULL;
  PhysicalOperator *subPhysicalOperator = NULL;
  FieldExpr        *fieldExpr           = NULL;
  int               i                   = 0;
  bool              subqueryIsNot1      = false;

  /* 这里判断是否之前已经处理子查询，如果处理过了就不需要再处理了 */
  if (!subqueryExpr->subEvent) {
    subqueryExpr->subEvent = SQLStageEventCreate();
    if (subqueryExpr->subEvent == NULL) {
      return GNCDB_MEM;
    }
    subqueryExpr->subEvent->affectedRows = 0;
    subqueryExpr->subEvent->sqlNode      = (ParsedSqlNode *)my_malloc0(sizeof(ParsedSqlNode));
    if (subqueryExpr->subEvent->sqlNode == NULL) {
      SQLStageEventDestroy(&subqueryExpr->subEvent);
      subqueryExpr->subEvent = NULL;
      return GNCDB_MEM;
    }
    subqueryExpr->subEvent->sqlNode->flag      = SCF_SELECT;
    subqueryExpr->subEvent->sqlNode->selection = (PTR_MOVE((void **)&subqueryExpr->sqlNode));
    assert(subqueryExpr->subEvent->sqlNode->selection != NULL);
    for (i = 0; i < subqueryExpr->subEvent->sqlNode->selection->projectExprs->elementCount; i++) {
      projectExpr = (Expression *)varArrayListGetPointer(subqueryExpr->subEvent->sqlNode->selection->projectExprs, i);
      if (projectExpr->type == ETG_FIELD) {
        fieldExpr = (FieldExpr *)projectExpr;
        if (fieldExpr->tableName && fieldExpr->fieldName && strcmp(fieldExpr->tableName, "*") == 0 &&
            strcmp(fieldExpr->fieldName, "*") == 0) {
          break;
        }
      }
    }

    subqueryExpr->subEvent->callback      = sqlEvent->callback;
    subqueryExpr->subEvent->db            = sqlEvent->db;
    subqueryExpr->subEvent->sql           = strdup(subqueryExpr->name);
    subqueryExpr->subEvent->txn           = sqlEvent->txn;
    subqueryExpr->subEvent->subQueryLevel = sqlEvent->subQueryLevel + 1;

    // 生成子查询stmt
    if (GNCDB_SUCCESS != (rc = SelectStmtConstruct(subqueryExpr->subEvent, &selectStmt, tableMap))) {
      SQLStageEventDestroy(&subqueryExpr->subEvent);
      subqueryExpr->subEvent = NULL;
      return rc;
    }
    if (selectStmt->type != ST_SELECT) {
      SQLStageEventDestroy(&subqueryExpr->subEvent);
      StmtDestroy(selectStmt);
      subqueryExpr->subEvent = NULL;
      return GNCDB_INTERNAL;
    }
    ss = (SelectStmt *)selectStmt;
    if (ss->queryFields == NULL) {
      printf("subSelect query field nums is null\n");
      SQLStageEventDestroy(&subqueryExpr->subEvent);
      subqueryExpr->subEvent = NULL;
      StmtDestroy(selectStmt);
      return GNCDB_INTERNAL;
    }

    if ((ss->queryFields->elementCount > 1)) {
      subqueryIsNot1 = true;
    }

    subqueryExpr->stmt           = (SelectStmt *)selectStmt;
    subqueryExpr->subEvent->stmt = selectStmt;

    // 生成子查询logical operator
    if (GNCDB_SUCCESS !=
        (rc = logicalPlanConstruct(subqueryExpr->subEvent, (Stmt *)subqueryExpr->stmt, &subLogicalOperator))) {
      SQLStageEventDestroy(&subqueryExpr->subEvent);
      subqueryExpr->subEvent = NULL;
      printf("subquery logical plan construct failed\n");
      return rc;
    }
    subqueryExpr->logicalOper           = subLogicalOperator;
    subqueryExpr->subEvent->logicalPlan = subLogicalOperator;
    // 生成子查询physical operator
    if (GNCDB_SUCCESS !=
        (rc = physicalPlanConstruct(subqueryExpr->subEvent, subLogicalOperator, &subPhysicalOperator))) {
      SQLStageEventDestroy(&subqueryExpr->subEvent);
      subqueryExpr->subEvent = NULL;
      printf("subquery physical plan construct failed\n");
      return rc;
    }
    subqueryExpr->physicalOper   = subPhysicalOperator;
    subqueryExpr->subEvent->plan = subPhysicalOperator;

    rc = setColMeta(subPhysicalOperator);
    if (rc != GNCDB_SUCCESS) {
      /* setColMeta failed, clean up the subquery */
      SQLStageEventDestroy(&subqueryExpr->subEvent);
      subqueryExpr->subEvent = NULL;
      printf("setColMeta failed for subquery\n");
      return rc;
    }

    if (subqueryIsNot1) {
      return GNCDB_SUBQUERY_FIELD_IS_NOT_ONE;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @description: 检查条件表达式expr是否合法
 *               1. 检查expr是否是合法的表达式
 *               2. 检查expr中涉及的表是否在tableMap中
 *               3. 检查expr中涉及的字段是否在tableMap中
 *               4. 检查expr中涉及的字段是否在表中
 * @param {Expression} *expr
 * @param {HashMap} *tableMap
 * @param {SQLStageEvent} *sqlEvent
 * @return {*}
 */
int checkFieldExpr(Expression *expr, HashMap *tableMap, SQLStageEvent *sqlEvent)
{
  int              rc           = GNCDB_SUCCESS;
  FieldExpr       *fieldExpr    = NULL;
  CastExpr        *castExpr     = NULL;
  ComparisonExpr  *compExpr     = NULL;
  ConjunctionExpr *conjExpr     = NULL;
  ArithmeticExpr  *arithExpr    = NULL;
  AggrFuncExpr    *aggrExpr     = NULL;
  SubQueryExpr    *subQueryExpr = NULL;
  ExprListExpr    *exprListExpr = NULL;
  Expression      *childExpr    = NULL;

  switch (expr->type) {
    case ETG_FIELD: {
      fieldExpr = (FieldExpr *)expr;
      rc        = fieldExprCheckField(tableMap, NULL, sqlEvent->db, fieldExpr, NULL, NULL);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      break;
    }

    case ETG_VALUE: {
      rc = GNCDB_SUCCESS;  // 值类型不需要检查
      break;
    }

    case ETG_CAST: {
      castExpr = (CastExpr *)expr;
      // todo 检查转换类型是否正确，但是目前只有转换后的类型，无转换前的类型
      rc = checkFieldExpr(castExpr->child, tableMap, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      break;
    }

    case ETG_COMPARISON: {
      compExpr = (ComparisonExpr *)expr;
      /* 非法的比较操作符 */
      if (compExpr->comp < CMPOP_EQUAL_TO || compExpr->comp > CMPOP_NO_OP) {
        return GNCDB_CONDITION_INVALID;
      }
      rc = checkFieldExpr(compExpr->left, tableMap, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      rc = checkFieldExpr(compExpr->right, tableMap, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      break;
    }

    case ETG_CONJUNCTION: {
      conjExpr = (ConjunctionExpr *)expr;
      /* 非法的连接类型 */
      if (conjExpr->conjunctionType > CJET_OR || conjExpr->conjunctionType < CJET_AND) {
        return GNCDB_CONDITION_INVALID;
      }
      /* 空的条件 */
      if (conjExpr->children == NULL || conjExpr->children->elementCount == 0) {
        return GNCDB_SUCCESS;
      }
      for (int i = 0; i < conjExpr->children->elementCount; i++) {
        childExpr = varArrayListGetPointer(conjExpr->children, i);
        rc        = checkFieldExpr(childExpr, tableMap, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
      break;
    }

    case ETG_ARITHMETIC: {
      arithExpr = (ArithmeticExpr *)expr;
      /* 非法的算数操作符 */
      if (arithExpr->arithmeticType < ARITH_ADD || arithExpr->arithmeticType > ARITH_NEGATIVE) {
        return GNCDB_CONDITION_INVALID;
      }
      /* 检查左操作数 */
      rc = checkFieldExpr(arithExpr->left, tableMap, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      /* 检查右操作数 */
      if (arithExpr->arithmeticType != ARITH_NEGATIVE) {
        rc = checkFieldExpr(arithExpr->right, tableMap, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        break;
      }
    }

    case ETG_AGGRFUNC: {
      aggrExpr = (AggrFuncExpr *)expr;
      /* 非法的聚合类型 */
      if (aggrExpr->aggrType < AGG_MAX || aggrExpr->aggrType > AGGR_FUNC_TYPE_NUM) {
        return GNCDB_CONDITION_INVALID;
      }
      rc = checkFieldExpr(aggrExpr->param, tableMap, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      break;
    }

    case ETG_SUBQUERY: {
      subQueryExpr = (SubQueryExpr *)expr;
      rc           = checkSubQueryExpr(subQueryExpr, tableMap, sqlEvent);
      break;
    }

    case ETG_EXPRLIST: {
      exprListExpr = (ExprListExpr *)expr;
      for (int i = 0; i < exprListExpr->exprs->elementCount; i++) {
        childExpr = varArrayListGetPointer(exprListExpr->exprs, i);
        rc        = checkFieldExpr(childExpr, tableMap, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
      break;
    }

    default:
      // 其他类型的表达式
      return GNCDB_CONDITION_INVALID;
  }
  return rc;
}

/**
 * @description: 处理一个inner join table on cond结点
 * @param {StringPair} *relation join后的table
 * @param {Expression} *condition on后面的条件
 * @param {JoinTables} *joinTables 所有连接表和结点的集合
 * @param {varArrayList} *tables 搜集的表列表
 * @param {HashMap} *tableMap from后面所有表的map
 * @param {HashMap} *tableAliasMap from后面所有表的别名map
 * @param {SQLStageEvent} *sqlEvent
 * @return {*}
 */
int processOneRelation(StringPair *relation, Expression **condition, JoinTables *joinTables, SubJoin *subJoin,
    HashMap *tableMap, HashMap *tableAliasMap, SQLStageEvent *sqlEvent, bool isTopCond)
{
  int         rc    = GNCDB_SUCCESS;
  BtreeTable *table = NULL;

  /* 1.先收集表relation */
  if (relation != NULL) {
    rc = checkAndCollectTables(relation, sqlEvent->db, tableMap, tableAliasMap);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    table = hashMapGet(tableMap, relation->name);
    varArrayListAddPointer(joinTables->tables, table);
    hashMapPut(subJoin->tabMap, relation->name, table);
  }

  /* 2.收集的条件*condition */
  if (condition == NULL || *condition == NULL) {
    return GNCDB_SUCCESS;
  }
  /* 2.1.条件检查 */
  if (checkFieldExpr(*condition, tableMap, sqlEvent) != GNCDB_SUCCESS) {
    return GNCDB_CONDITION_INVALID;
  }
  /* 2.2.构造JoinNode */
  rc = onCondToJoinNode(condition, joinTables, subJoin, tableMap, sqlEvent, isTopCond);
  return rc;
}

char *removeDotAndBefore(char *str)
{
  char *p = strchr(str, '.');
  if (p != NULL) {
    // 返回 '.' 之后的字符串
    return p + 1;
  } else {
    // 如果没有找到 '.'，返回原字符串
    return str;
  }
}

int exprCheckInGroupby(varArrayList *groupbyExprs, Expression *fieldExpr)
{
  Expression *groupbyExpr = NULL;
  int         i           = 0;
  for (i = 0; i < groupbyExprs->elementCount; i++) {
    groupbyExpr = varArrayListGetPointer(groupbyExprs, i);
    if (strcmp(removeDotAndBefore(groupbyExpr->name), removeDotAndBefore(fieldExpr->name)) == 0) {
      return true;
    }
  }
  return false;
}

int collectExprsNotAggexpr(Expression *expr, varArrayList *fieldExprsNotInAggr, int nums, va_list args)
{
  if (expr->type == ETG_AGGRFUNC) {
    return GNCDB_NOT_FOUND;
  }
  if (expr->type == ETG_FIELD) {
    varArrayListAddPointer(fieldExprsNotInAggr, exprDeepCopy(expr));
    return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

int collectAggrExprs(Expression *expr, varArrayList *aggrExprs, int nums, va_list args)
{
  if (expr->type == ETG_AGGRFUNC) {
    varArrayListAddPointer(aggrExprs, exprDeepCopy(expr));
    return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

int collectFieldExprs(Expression *expr, varArrayList *fieldExprs, int nums, va_list args)
{
  if (expr->type == ETG_FIELD) {
    varArrayListAddPointer(fieldExprs, exprDeepCopy(expr));
    return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

int collectCondToConjExpr(Expression *expr, ConjunctionExpr *conjExpr)
{
  ConjunctionExpr *conj = NULL;
  if (expr == NULL) {
    return GNCDB_SUCCESS;
  }
  if ((expr)->type == ETG_CONJUNCTION) {
    conj = (ConjunctionExpr *)(expr);
    for (int i = 0; i < conj->children->elementCount; i++) {
      collectCondToConjExpr(varArrayListGetPointer(conj->children, i), conjExpr);
      varArrayListRemoveByIndexPointer(conj->children, i);
      i--;
    }
    if (conj->children->elementCount == 0) {
      exprDestroy((Expression *)conj);
      return GNCDB_SUCCESS;
    }
  } else {
    varArrayListAddPointer(conjExpr->children, expr);
  }
  return GNCDB_SUCCESS;
}

// parent_table_map 是父查询中的 table_map，这里只需要父查询的 table map 即可
// tables table_alias_map local_table_map 都不需要
// 嵌套子查询 的情况下在 parent table map 中累积
int SelectStmtConstruct(SQLStageEvent *sqlEvent, Stmt **stmt, HashMap *parentTableMap)
{
  int               rc                  = GNCDB_SUCCESS;
  GNCDB            *db                  = NULL;
  SelectSqlNode    *selectSql           = NULL;
  HashMap          *tableMap            = NULL;
  HashMap          *tableAliasMap       = NULL;
  HashMap          *aggrAliasSet        = NULL;
  HashMapIterator  *iter                = NULL;
  InnerJoinSqlNode *relations           = NULL;
  JoinTables       *joinTables          = NULL;
  SubJoin          *subJoin             = NULL;
  bool              isSingleTable       = false;
  bool              hasAggrFuncExpr     = false;
  varArrayList     *projects            = NULL;
  Expression       *expr                = NULL;
  ConjunctionExpr  *newConjExpr         = NULL;
  FieldExpr        *fieldExpr           = NULL;
  BtreeTable       *table               = NULL;
  FilterStmt       *filterStmt          = NULL;
  GroupByStmt      *groupbyStmt         = NULL;
  FilterStmt       *havingFilterStmt    = NULL;
  varArrayList     *aggrExprs           = NULL;
  varArrayList     *fieldExprsNotInAggr = NULL;
  varArrayList     *fieldExprs          = NULL;
  OrderByStmt      *orderbyStmt         = NULL;
  varArrayList     *exprForOrderby      = NULL;
  OrderBySqlNode   *orderbyNode         = NULL;
  LimitStmt        *limitStmt           = NULL;
  SelectStmt       *selectStmt          = NULL;
  char             *tableName           = NULL;
  char             *fieldName           = NULL;
  AggrFuncExpr     *aggrExpr            = NULL;
  int               moveCnt             = 0;
  int               i = 0, j = 0;

  /*1.参数检查*/
  if (sqlEvent == NULL || stmt == NULL || sqlEvent->db == NULL || sqlEvent->sqlNode == NULL ||
      sqlEvent->sqlNode->selection == NULL) {
    return GNCDB_PARAMNULL;
  }

  db        = sqlEvent->db;
  selectSql = sqlEvent->sqlNode->selection;

  /*2.tables：table列表，tableMap：若有父tableMap则继承若无则新建*/
  if (parentTableMap == NULL) {
    tableMap = hashMapCreate(STRKEY, 0, NULL);
    if (tableMap == NULL) {
      return GNCDB_MEM;
    }
  } else {
    tableMap = parentTableMap;
  }
  /*2.1存储表名和表的别名*/
  tableAliasMap = hashMapCreate(STRKEY, 0, NULL);
  /*2.2存储聚合表达式的别名*/
  aggrAliasSet  = hashMapCreate(STRKEY, 0, NULL);
  /*2.3存储所有表和连接结点*/
  JOIN_TABLES_CREATE(joinTables);

  /*3.处理所有的join */
  for (i = 0; i < selectSql->relations->elementCount; i++) {
    relations = varArrayListGetPointer(selectSql->relations, i);
    SUB_JOIN_CREATE(subJoin);
    /*3.1收集join的基表*/
    rc = processOneRelation(relations->baseRelation,
        NULL,  /*这里传入NULL,函数内部只会收集表，不会进行条件处理*/
        joinTables,
        subJoin,
        tableMap,
        tableAliasMap,
        sqlEvent,
        false);
    if (rc != GNCDB_SUCCESS) {
      if (sqlEvent->subQueryLevel == 0) {
        hashMapDestroy(&tableMap);
      }
      hashMapDestroy(&tableAliasMap);
      hashMapDestroy(&aggrAliasSet);
      JOIN_TABLES_DESTROY(joinTables);
      varArrayListDestroy(&projects);
      SUB_JOIN_DESTROY(subJoin);
      return rc;
    }

    /*3.2处理每个inner join结点*/
    moveCnt = 0;  /*统计连接条件中转移的个数，用来当作修改获取表的下标*/
    for (j = 0; j < relations->conditions->elementCount; j++) {
      expr = varArrayListGetPointer(relations->conditions, j);
      /*3.2.1显式连接未指定连接条件报错（不支持笛卡尔积）*/
      if (expr == NULL) {
        if (sqlEvent->subQueryLevel == 0) {
          hashMapDestroy(&tableMap);
        }
        hashMapDestroy(&tableAliasMap);
        hashMapDestroy(&aggrAliasSet);
        JOIN_TABLES_DESTROY(joinTables);
        varArrayListDestroy(&projects);
        SUB_JOIN_DESTROY(subJoin);
        return GNCDB_PARAMNULL;
      }
      /*3.2.2将连接条件：联结条件（A and/or B）、单个条件转换成Joinnode,*/
      /*将joinnode放入jointable、subjoin、topjoin的joinnodes中*/
      rc = processOneRelation(varArrayListGetPointer(relations->joinRelations, j + moveCnt),
          &expr,
          joinTables,
          subJoin,
          tableMap,
          tableAliasMap,
          sqlEvent,
          false);
      if (rc != GNCDB_SUCCESS) {
        if (sqlEvent->subQueryLevel == 0) {
          hashMapDestroy(&tableMap);
        }
        hashMapDestroy(&tableAliasMap);
        hashMapDestroy(&aggrAliasSet);
        JOIN_TABLES_DESTROY(joinTables);
        varArrayListDestroy(&projects);
        SUB_JOIN_DESTROY(subJoin);
        return rc;
      }
      if (expr == NULL) {
        varArrayListRemoveByIndexPointer(relations->conditions, j);
        j--;
        moveCnt++;
      } else {
        varArrayListSetByIndexPointer(relations->conditions, j, expr);
      }
    }
    varArrayListAddPointer(joinTables->subJoins, subJoin);
  }

  /*4.处理投影
  语义解析 check & 设置表达式 name alias 用于客户端输出的表头
  要处理 *, *.*, t1.* 这种情况
  要 check field 判断**所有** FieldExpr 是否合法：有一个唯一对应的 table 即合法 不能没有表 也不能出现歧义*/
  isSingleTable = (tableMap->entryCount == 1);
  projects      = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);  /*深拷贝表达式*/
  /*4.1遍历每一个投影表达式*/
  for (i = selectSql->projectExprs->elementCount - 1; i >= 0; i--) {
    expr = varArrayListGetPointer(selectSql->projectExprs, i);
    if (expr->type == ETG_FIELD) {
      /*4.1.0投影表达式是列*/
      fieldExpr = (FieldExpr *)expr;
      tableName = fieldExpr->tableName == NULL ? "" : fieldExpr->tableName;
      fieldName = fieldExpr->fieldName;
      /*4.1.1表达式是 *.* */
      if ((0 == strcmp(tableName, "*") && 0 == strcmp(fieldName, "*"))) {
        iter = createHashMapIterator(tableMap);
        while (hasNextHashMapIterator(iter)) {
          iter  = nextHashMapIterator(iter);
          table = (BtreeTable *)(iter->entry->value);
          /*将*.*转换成具体的表名.列名*/
          if (myhashMapCount(tableAliasMap, table->tableName) != 0) {
            wildcardFields(db, table, hashMapGet(tableAliasMap, table->tableName), projects, isSingleTable);
          } else {
            wildcardFields(db, table, "", projects, isSingleTable);
          }
        }
        freeHashMapIterator(&iter);
      } else if (0 == strcmp(fieldName, "*")) {
        /*4.1.1表达式是t1.* */
        table = hashMapGet(tableMap, tableName);
        /*非法表*/
        if (NULL == table) {
          if (sqlEvent->subQueryLevel == 0) {
            hashMapDestroy(&tableMap);
          }
          hashMapDestroy(&tableAliasMap);
          hashMapDestroy(&aggrAliasSet);
          JOIN_TABLES_DESTROY(joinTables);
          varArrayListDestroy(&projects);
          return GNCDB_TABLE_NOT_FOUND;
        }
        if (myhashMapCount(tableAliasMap, table->tableName) != 0) {
          wildcardFields(db, table, hashMapGet(tableAliasMap, table->tableName), projects, isSingleTable);
        } else {
          wildcardFields(db, table, "", projects, isSingleTable);
        }

      } else {
        /*4.2表达式是t1.c1 or c1*/
        if (exprCheckField(tableMap, NULL, db, (Expression *)fieldExpr, NULL, tableAliasMap) != GNCDB_SUCCESS) {
          if (sqlEvent->subQueryLevel == 0) {
            hashMapDestroy(&tableMap);
          }
          hashMapDestroy(&tableAliasMap);
          hashMapDestroy(&aggrAliasSet);
          JOIN_TABLES_DESTROY(joinTables);
          varArrayListDestroy(&projects);
          return GNCDB_NOT_FOUND;
        }
        varArrayListAddPointer(projects, exprDeepCopy((Expression *)fieldExpr));
      }
    } else {
      /*4.2投影表达式是聚合表达式*/
      if (expr->type == ETG_AGGRFUNC) {
        aggrExpr = (AggrFuncExpr *)expr;
        if (!isBlank(expr->alias)) {
          hashMapPut(aggrAliasSet, expr->alias, expr);
        }
        if (aggrExprCheckIsConstexpr(aggrExpr)) {
          aggrExpr->paramIsConstexpr = true;
        }
        hasAggrFuncExpr = true;
      }
      if (GNCDB_SUCCESS != (rc = exprCheckField(tableMap, NULL, db, expr, NULL, tableAliasMap))) {
        if (sqlEvent->subQueryLevel == 0) {
          hashMapDestroy(&tableMap);
        }
        hashMapDestroy(&tableAliasMap);
        hashMapDestroy(&aggrAliasSet);
        JOIN_TABLES_DESTROY(joinTables);
        varArrayListDestroy(&projects);
        return rc;
      }
      varArrayListAddPointer(projects, exprDeepCopy((Expression *)expr));
    }
  }

  /*5. 处理where后面的条件,涉及多表的条件在jointable中，单表的构造过滤算子*/
  /*处理where条件中的连接,这里的topjoin是true*/
  if (selectSql->conditions != NULL) {
    /*先进行语义检查，按照on条件的形式对where中的条件进行同样处理，*/
    /*这里表传入NULL，不会处理表，仅仅处理where后面的条件*/
    rc = processOneRelation(
        NULL,
        &(selectSql->conditions),
        joinTables,
        NULL,
        tableMap,
        tableAliasMap,
        sqlEvent,
        true);
    if (rc != GNCDB_SUCCESS) {
      if (sqlEvent->subQueryLevel == 0) {
        hashMapDestroy(&tableMap);
      }
      hashMapDestroy(&tableAliasMap);
      hashMapDestroy(&aggrAliasSet);
      JOIN_TABLES_DESTROY(joinTables);
      varArrayListDestroy(&projects);
      return rc;
    }
  }

  /*5.1创建一个新的and联结表达式，将on中剩余非连接条件加入到where以便后面统一处理
  * 这一步就是把join和where中单表过滤条件提取出来进行谓词下推*/
  newConjExpr                  = my_new0(ConjunctionExpr);
  newConjExpr->type            = ETG_CONJUNCTION;
  newConjExpr->conjunctionType = CJET_AND;
  newConjExpr->children        = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  /*把join on的非连接条件放到newConjExpr*/
  for (i = 0; i < selectSql->relations->elementCount; i++) {
    relations = varArrayListGetPointer(selectSql->relations, i);
    /*遍历每个inner join结点的on*/
    for (j = relations->conditions->elementCount - 1; j >= 0; j--) {
      expr = varArrayListGetPointer(relations->conditions, j);
      varArrayListRemoveByIndexPointer(relations->conditions, j);
      collectCondToConjExpr(expr, newConjExpr);
    }
  }
  /*把where的非连接条件放到newConjExpr*/
  collectCondToConjExpr(selectSql->conditions, newConjExpr);
  /*把where的非连接条件放到newConjExpr*/
  if (newConjExpr->children->elementCount == 0) {
    exprDestroy((Expression *)newConjExpr);
  } else if (newConjExpr->children->elementCount == 1) {
    selectSql->conditions = varArrayListGetPointer(newConjExpr->children, 0);
    varArrayListRemoveByIndexPointer(newConjExpr->children, 0);
    exprDestroy((Expression *)newConjExpr);
  } else {
    selectSql->conditions = (Expression *)newConjExpr;
  }

  /*根据过滤条件构造过滤stmt*/
  rc = FilterStmtConstruct(sqlEvent, NULL, NULL, tableMap, PTR_MOVE((void **)&selectSql->conditions), &filterStmt);
  if (rc != GNCDB_SUCCESS) {
    if (sqlEvent->subQueryLevel == 0) {
      hashMapDestroy(&tableMap);
    }
    hashMapDestroy(&tableAliasMap);
    hashMapDestroy(&aggrAliasSet);
    JOIN_TABLES_DESTROY(joinTables);
    varArrayListDestroy(&projects);
    return rc;
  }

  /*6.有聚集函数表达式 或者有 group by clause 就要添加 group by stmt*/
  if (hasAggrFuncExpr || (selectSql->groupbyExprs && selectSql->groupbyExprs->elementCount > 0)) {
    /*用于从 project exprs 中提取所有聚集函数的agg expr*/
    aggrExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
    /*用于从 project exprs 中提取所有非聚集函数的field expr,用来判断语句是否合法*/
    fieldExprsNotInAggr = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
    /*用于从 project exprs 中提取所有field expr,传递给 order by 算子*/
    fieldExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);

    /*提取 AggrFuncExpr(深拷贝表达式)*/
    for (i = 0; i < projects->elementCount; i++) {
      exprTraverseCheckPrepare(varArrayListGetPointer(projects, i), collectAggrExprs, aggrExprs, 0);
    }
    /*提取所有的 不包括在聚集函数中的field expr，(深拷贝表达式)*/
    for (i = 0; i < projects->elementCount; i++) {
      exprTraverseCheckPrepare(varArrayListGetPointer(projects, i), collectExprsNotAggexpr, fieldExprsNotInAggr, 0);
    }
    /*提取所有的 field expr，包括在聚集函数中的(深拷贝表达式)*/
    for (i = 0; i < projects->elementCount; i++) {
      exprTraverseCheckPrepare(varArrayListGetPointer(projects, i), collectFieldExprs, fieldExprs, 0);
    }

    /*6.1针对 having 后的表达式，需要做和上面相同的三个提取过程(深拷贝表达式)*/
    /*select id, max(score) from t_group_by group by id having count(*)>5;*/
    if (selectSql->havingConditions != NULL) {
      rc = FilterStmtConstruct(
          sqlEvent, NULL, NULL, tableMap, PTR_MOVE((void **)&selectSql->havingConditions), &havingFilterStmt);
      if (rc != GNCDB_SUCCESS) {
        if (sqlEvent->subQueryLevel == 0) {
          hashMapDestroy(&tableMap);
        }
        hashMapDestroy(&tableAliasMap);
        hashMapDestroy(&aggrAliasSet);
        JOIN_TABLES_DESTROY(joinTables);
        varArrayListDestroy(&projects);
        varArrayListDestroy(&aggrExprs);
        varArrayListDestroy(&fieldExprsNotInAggr);
        varArrayListDestroy(&fieldExprs);
        return rc;
      }
      exprTraverseCheckPrepare(havingFilterStmt->condition, collectAggrExprs, aggrExprs, 0);
      exprTraverseCheckPrepare(havingFilterStmt->condition, collectExprsNotAggexpr, fieldExprsNotInAggr, 0);
      exprTraverseCheckPrepare(havingFilterStmt->condition, collectFieldExprs, fieldExprs, 0);
    }

    /*6.2语义检查 check:*/
    /*- 聚集函数参数个数、参数为 * 的检查是在 syntax parser 完成*/
    /*- 聚集函数中的字段 OK*/
    /*- 非聚集函数中的字段应该 必须在 group by 的字段中*/
    /*- 没有 group by clause 时，不应该有非聚集函数中的字段*/
    if (fieldExprsNotInAggr->elementCount != 0 && selectSql->groupbyExprs->elementCount == 0) {
      printf("No Group By. But Has Fields Not In Aggr Func\n");
      if (sqlEvent->subQueryLevel == 0) {
        hashMapDestroy(&tableMap);
      }
      hashMapDestroy(&tableAliasMap);
      hashMapDestroy(&aggrAliasSet);
      JOIN_TABLES_DESTROY(joinTables);
      varArrayListDestroy(&projects);
      varArrayListDestroy(&aggrExprs);
      varArrayListDestroy(&fieldExprsNotInAggr);
      varArrayListDestroy(&fieldExprs);
      return GNCDB_PARAM_INVALID;
    }
    varArrayListDestroy(&fieldExprsNotInAggr);

    /*6.3分组聚合表达式检查*/
    if (selectSql->groupbyExprs->elementCount > 0) {
      for (i = 0; i < selectSql->groupbyExprs->elementCount; i++) {
        expr = varArrayListGetPointer(selectSql->groupbyExprs, i);
        if (GNCDB_SUCCESS != (rc = exprCheckField(tableMap, NULL, db, expr, NULL, tableAliasMap))) {
          // printf("group by expr check field failed\n");
          if (sqlEvent->subQueryLevel == 0) {
            hashMapDestroy(&tableMap);
          }
          hashMapDestroy(&tableAliasMap);
          hashMapDestroy(&aggrAliasSet);
          JOIN_TABLES_DESTROY(joinTables);
          varArrayListDestroy(&projects);
          varArrayListDestroy(&aggrExprs);
          varArrayListDestroy(&fieldExprs);
          return rc;
        }
      }

      /*先提取 select 后的非 aggexpr ，然后判断其是否是 groupby 中*/
      for (i = 0; i < projects->elementCount; i++) {
        expr = varArrayListGetPointer(projects, i);
        if (expr->type != ETG_AGGRFUNC) {
          /*判断逻辑：遍历groupbyExprs，判断是否有name相同的列*/
          if (!exprCheckInGroupby(selectSql->groupbyExprs, expr)) {
            printf("field not in group by\n");
            if (sqlEvent->subQueryLevel == 0) {
              hashMapDestroy(&tableMap);
            }
            hashMapDestroy(&tableAliasMap);
            hashMapDestroy(&aggrAliasSet);
            JOIN_TABLES_DESTROY(joinTables);
            varArrayListDestroy(&projects);
            varArrayListDestroy(&aggrExprs);
            varArrayListDestroy(&fieldExprs);
            return GNCDB_PARAM_INVALID;
          }
        }
      }
    }

    /*6.4create groupby stmt*/
    rc = GroupByStmtConstructor(db,
        NULL,
        tableMap,
        PTR_MOVE((void **)&selectSql->groupbyExprs),
        &groupbyStmt,
        PTR_MOVE((void **)&aggrExprs),
        PTR_MOVE((void **)&fieldExprs));
    if (rc != GNCDB_SUCCESS) {
      if (sqlEvent->subQueryLevel == 0) {
        hashMapDestroy(&tableMap);
      }
      hashMapDestroy(&tableAliasMap);
      hashMapDestroy(&aggrAliasSet);
      JOIN_TABLES_DESTROY(joinTables);
      varArrayListDestroy(&projects);
      varArrayListDestroy(&aggrExprs);
      varArrayListDestroy(&fieldExprs);
      return rc;
    }
  }

  /*7. 处理 order by*/
  if (selectSql->orderbyExprs && selectSql->orderbyExprs->elementCount > 0) {
    /*提取 AggrFuncExpr 以及不在 AggrFuncExpr 中的 FieldExpr*/
    exprForOrderby = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
    // 用于从 project exprs 中提取所有 aggr func exprs. e.g. min(c1 + 1) + 1(深拷贝表达式)
    // for (i = 0; i < projects->elementCount; i++) {
    //   exprTraverseCheckPrepare(varArrayListGetPointer(projects, i), collectAggrExprs, exprForOrderby, 0);
    // }
    // // 提取所有的 不包括在聚集函数中的field expr，(深拷贝表达式)
    // for (i = 0; i < projects->elementCount; i++) {
    //   exprTraverseCheckPrepare(varArrayListGetPointer(projects, i), collectExprsNotAggexpr, exprForOrderby, 0);
    // }

    /*7.1 语义检查 check:*/
    for (i = 0; i < selectSql->orderbyExprs->elementCount; i++) {
      orderbyNode = varArrayListGetPointer(selectSql->orderbyExprs, i);
      expr        = orderbyNode->expr;
      if (GNCDB_SUCCESS != exprCheckField(tableMap, NULL, db, expr, NULL, tableAliasMap)) {
        if (!hashMapExists(aggrAliasSet, expr->name)) {
          printf("orderby expr check field failed\n");
          if (sqlEvent->subQueryLevel == 0) {
            hashMapDestroy(&tableMap);
          }
          hashMapDestroy(&tableAliasMap);
          hashMapDestroy(&aggrAliasSet);
          JOIN_TABLES_DESTROY(joinTables);
          varArrayListDestroy(&projects);
          varArrayListDestroy(&exprForOrderby);
          return GNCDB_ORDER_BY_INVALID;
        } else {
        }
      }
    }

    /*7.2create orderby stmt*/
    rc = OrderByStmtConstruct(
        db, NULL, tableMap, (selectSql->orderbyExprs), &orderbyStmt, PTR_MOVE((void **)&exprForOrderby), aggrAliasSet);
    if (rc != GNCDB_SUCCESS) {
      if (sqlEvent->subQueryLevel == 0) {
        hashMapDestroy(&tableMap);
      }
      hashMapDestroy(&tableAliasMap);
      hashMapDestroy(&aggrAliasSet);
      JOIN_TABLES_DESTROY(joinTables);
      varArrayListDestroy(&projects);
      varArrayListDestroy(&exprForOrderby);
      return rc;
    }
  }

  /*8.处理limit*/
  if (selectSql->limit != NULL) {
    /*8.1limit的offset和limit必须合法*/
    if (selectSql->limit->offset < -1 || selectSql->limit->limit < -1) {
      printf("limit offset or limit is invalid\n");
      if (sqlEvent->subQueryLevel == 0) {
        hashMapDestroy(&tableMap);
      }
      hashMapDestroy(&tableAliasMap);
      hashMapDestroy(&aggrAliasSet);
      JOIN_TABLES_DESTROY(joinTables);
      varArrayListDestroy(&projects);
      return GNCDB_PARAM_INVALID;
    }
    rc = LimitStmtConstruct(selectSql->limit, &limitStmt);
    ;
    if (rc != GNCDB_SUCCESS) {
      printf("create limit stmt failed\n");
      if (sqlEvent->subQueryLevel == 0) {
        hashMapDestroy(&tableMap);
      }
      hashMapDestroy(&tableAliasMap);
      hashMapDestroy(&aggrAliasSet);
      JOIN_TABLES_DESTROY(joinTables);
      varArrayListDestroy(&projects);
      return rc;
    }
  }

  /*9.构造select stmt*/
  selectStmt = SelectStmtCreate();
  if (selectStmt == NULL) {
    if (sqlEvent->subQueryLevel == 0) {
      hashMapDestroy(&tableMap);
    }
    hashMapDestroy(&tableAliasMap);
    hashMapDestroy(&aggrAliasSet);
    JOIN_TABLES_DESTROY(joinTables);
    varArrayListDestroy(&projects);
    return GNCDB_MEM;
  }
  selectStmt->joinTables  = joinTables;
  selectStmt->queryFields = projects;
  selectStmt->filterStmt  = filterStmt;        /*maybe NULL*/
  selectStmt->groupbyStmt = groupbyStmt;       /*maybe NULL*/
  selectStmt->orderbyStmt = orderbyStmt;       /*maybe NULL*/
  selectStmt->havingStmt  = havingFilterStmt;  /*maybe NULL*/
  selectStmt->limitStmt   = limitStmt;         /*maybe NULL*/
  selectStmt->isDistinct  = selectSql->isDistinct;
  *stmt                   = (Stmt *)selectStmt;

  /*9.释放内存*/
  if (sqlEvent->subQueryLevel == 0) {
    hashMapDestroy(&tableMap);
  }
  hashMapDestroy(&tableAliasMap);
  hashMapDestroy(&aggrAliasSet);

  return GNCDB_SUCCESS;
}

void SelectStmtDestroy(SelectStmt *stmt)
{
  if (stmt == NULL) {
    return;
  }
  if (stmt->joinTables != NULL) {
    JOIN_TABLES_DESTROY(stmt->joinTables);
  }
  if (stmt->queryFields != NULL) {
    varArrayListDestroy(&stmt->queryFields);
  }
  if (stmt->filterStmt != NULL) {
    FilterStmtDestroy(stmt->filterStmt);
  }
  if (stmt->groupbyStmt != NULL) {
    GroupByStmtDestroy(&stmt->groupbyStmt);
  }
  if (stmt->orderbyStmt != NULL) {
    OrderByStmtDestroy(stmt->orderbyStmt);
  }
  if (stmt->havingStmt != NULL) {
    FilterStmtDestroy(stmt->havingStmt);
  }
  if (stmt->limitStmt != NULL) {
    LimitStmtDestroy(stmt->limitStmt);
  }
  my_free(stmt);
}

/**
 * @description: 销毁一个JoinNode结点
 * @param {JoinNode} *joinNode
 * @note: 该函数同时会释放joinNode的所有表达式，包括eqExprs, eqPKExprs, otherExprs等
 * @return {*}
 */
void JoinNodeDestroy(JoinNode *joinNode)
{
  Expression *expr = NULL;
  if (joinNode == NULL)
    return;
  if (joinNode->tableName1) {
    my_free(joinNode->tableName1);
  }
  if (joinNode->tableName2) {
    my_free(joinNode->tableName2);
  }
  if (joinNode->eqExprs) {
    for (int i = 0; i < joinNode->eqExprs->elementCount; i++) {
      expr = varArrayListGetPointer(joinNode->eqExprs, i);
      if (expr != NULL) {
        exprDestroy(expr);
      }
    }
    varArrayListDestroy(&(joinNode->eqExprs));
  }
  if (joinNode->eqPKExprs) {
    for (int i = 0; i < joinNode->eqPKExprs->elementCount; i++) {
      expr = varArrayListGetPointer(joinNode->eqPKExprs, i);
      if (expr != NULL) {
        exprDestroy(expr);
      }
    }
    varArrayListDestroy(&(joinNode->eqPKExprs));
  }
  if (joinNode->otherExprs) {
    for (int i = 0; i < joinNode->otherExprs->elementCount; i++) {
      expr = varArrayListGetPointer(joinNode->otherExprs, i);
      if (expr != NULL) {
        exprDestroy(expr);
      }
    }
    varArrayListDestroy(&(joinNode->otherExprs));
  }
  my_free(joinNode);
}

void JoinNodePtrDestroy(void *data)
{
  JoinNode **joinNode = (JoinNode **)data;
  if (joinNode == NULL || *joinNode == NULL) {
    return;
  }
  JoinNodeDestroy(*joinNode);
  *joinNode = NULL;
}

void JoinListPtrDestroy(void *data)
{
  varArrayList **joinList = (varArrayList **)data;
  if (joinList == NULL || *joinList == NULL) {
    return;
  }
  varArrayListDestroy(joinList);
  *joinList = NULL;
}

void JoinTablesDestroy(JoinTables *joinTables)
{
  if (joinTables == NULL) {
    return;
  }
  if (joinTables->joinNodes != NULL) {
    varArrayListDestroy(&joinTables->joinNodes);
  }
  if (joinTables->tables != NULL) {
    varArrayListDestroy(&joinTables->tables);
  }
  my_free(joinTables);
}

void JoinTablesPointerDestroy(void *data)
{
  JoinTables **joinTables = (JoinTables **)data;
  if (joinTables == NULL || *joinTables == NULL) {
    return;
  }
  JoinTablesDestroy(*joinTables);
  *joinTables = NULL;
}