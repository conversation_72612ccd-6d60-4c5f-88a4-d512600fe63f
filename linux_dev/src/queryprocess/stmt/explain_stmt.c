#include "explain_stmt.h"
int ExplainStmtConstruct(SQLStageEvent *sqlEvent, Stmt **stmt)
{
  int          rc          = GNCDB_SUCCESS;
  Stmt        *childStmt   = NULL;
  ExplainStmt *explainStmt = NULL;

  rc = StmtConstruct(sqlEvent, &childStmt);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  explainStmt = (ExplainStmt *)my_malloc0(sizeof(ExplainStmt));
  if (explainStmt == NULL) {
    return GNCDB_MEM; /*malloc失败*/
  }
  explainStmt->type      = ST_EXPLAIN;
  explainStmt->childStmt = childStmt;
  *stmt                  = (Stmt *)explainStmt;
  return rc;
}

void ExplainStmtDestroy(ExplainStmt *explainStmt)
{
  if (explainStmt == NULL) {
    return;
  }
  StmtDestroy(explainStmt->childStmt);
  my_free(explainStmt);
}