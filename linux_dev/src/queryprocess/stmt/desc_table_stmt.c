#include "desc_table_stmt.h"
#include "utils.h"
#include "parse_defs.h"
#include "gncdb.h"
int DescTableStmtConstruct(GNCDB *db, DescTableSqlNode *descTable, Stmt **stmt)
{
  BtreeTable    *table         = NULL;
  int            rc            = GNCDB_SUCCESS;
  DescTableStmt *descTableStmt = NULL;

  rc = catalogGetTable(db->catalog, &table, descTable->relationName);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_TABLE_NOT_FOUND;
  }
  descTableStmt            = (DescTableStmt *)my_malloc0(sizeof(DescTableStmt));
  if(descTableStmt == NULL) {
    return GNCDB_MEM;
  }
  descTableStmt->type      = ST_DESC_TABLE;
  descTableStmt->tableName = PTR_MOVE((void**)&descTable->relationName);
  *stmt                    = (Stmt *)descTableStmt;

  return GNCDB_SUCCESS;
}

void DescTableStmtDestroy(DescTableStmt *descTableStmt)
{
  if (descTableStmt == NULL) {
    return;
  }

  if (descTableStmt->tableName != NULL) {
    my_free(descTableStmt->tableName);
  }
  my_free(descTableStmt);
}
