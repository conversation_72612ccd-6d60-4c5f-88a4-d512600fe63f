#include "update_stmt.h"
#include "hashmap.h"
#include "expression.h"
#include "parse_defs.h"
#include "sql_event.h"
#include "utils.h"
#include "assert.h"
#include "vararraylist.h"
#include "gncdb.h"
UpdateStmt *UpdateStmtCreate()
{
  UpdateStmt *stmt = (UpdateStmt *)my_malloc0(sizeof(UpdateStmt));
  if (stmt == NULL) {
    return NULL;
  }
  stmt->type             = ST_UPDATE;
  stmt->table            = NULL;
  stmt->updateFieldNames = NULL;
  stmt->updateValues     = NULL;
  stmt->filterStmt       = NULL;
  stmt->valueCount       = 0;
  return stmt;
}

Column *attributeGetMatchColumn(varArrayList *columnList, char *attributeName, int size)
{
  Column *column = NULL;
  int     i      = 0;
  for (i = 0; i < columnList->elementCount; i++) {
    column = (Column *)varArrayListGetPointer(columnList, i);
    if (strcmp(column->fieldName, attributeName) == 0) {
      return column;
    }
  }
  return NULL;
}

int updateValueCheck(Expression *expr, varArrayList *list, int addtionalParamNum, va_list args)
{
  SQLStageEvent *sqlEvent     = NULL;
  FieldExpr     *fieldExpr    = NULL;
  Column        *column       = NULL;
  SubQueryExpr  *subQueryExpr = NULL;
  Stmt          *selectStmt   = NULL;
  SelectStmt    *ss           = NULL;

  if (expr->type == ETG_FIELD) {
    // TODO: fieldExpr->tableName和fieldExpr->fieldName的可能有已经分配的内存，需要释放
    sqlEvent  = va_arg(args, SQLStageEvent *);
    fieldExpr = (FieldExpr *)expr;
    column    = attributeGetMatchColumn(list, fieldExpr->fieldName, list->elementCount);
    if (column == NULL) {
      return GNCDB_COLUMN_NOT_FOUND;
    }
    if(fieldExpr->tableName != NULL) {
      my_free(fieldExpr->tableName);
    }
    if(fieldExpr->fieldName != NULL) {
      my_free(fieldExpr->fieldName);
    }
    fieldExpr->tableName = my_strdup(sqlEvent->sqlNode->update->relationName);
    fieldExpr->fieldName = my_strdup(column->fieldName);
    fieldExpr->fieldType = column->fieldType;
    return GNCDB_SUCCESS;
  }
  if (expr->type == ETG_SUBQUERY) {
    // TODO: 优化代码逻辑
    int rc = GNCDB_SUCCESS;
    assert(addtionalParamNum == 1);
    subQueryExpr = (SubQueryExpr *)expr;
    sqlEvent     = va_arg(args, SQLStageEvent *);
    // subQueryExpr->subEvent                     = (SQLStageEvent *)my_malloc0(sizeof(SQLStageEvent));
    subQueryExpr->subEvent                     = SQLStageEventCreate();
    subQueryExpr->subEvent->affectedRows       = 0;
    subQueryExpr->subEvent->sqlNode            = (ParsedSqlNode *)my_malloc0(sizeof(ParsedSqlNode));
    subQueryExpr->subEvent->sqlNode->selection = subQueryExpr->sqlNode;
    subQueryExpr->subEvent->plan               = NULL;
    subQueryExpr->subEvent->callback           = sqlEvent->callback;
    subQueryExpr->subEvent->db                 = sqlEvent->db;
    subQueryExpr->subEvent->sql                = my_strdup(expr->name);
    subQueryExpr->subEvent->txn                = sqlEvent->txn;
    subQueryExpr->subEvent->subQueryLevel      = sqlEvent->subQueryLevel + 1;

    if (GNCDB_SUCCESS != (rc = SelectStmtConstruct(subQueryExpr->subEvent, &selectStmt, NULL))) {
      SQLStageEventDestroy(&subQueryExpr->subEvent);
      subQueryExpr->subEvent = NULL;
      return rc;
    }
    if (selectStmt->type != ST_SELECT) {
      printf("subSelect stmt type error\n");
      SQLStageEventDestroy(&subQueryExpr->subEvent);
      subQueryExpr->subEvent = NULL;
      return GNCDB_INTERNAL;
    }
    ss = (SelectStmt *)selectStmt;
    if (ss->queryFields == NULL) {
      printf("subSelect query field null\n");
      SQLStageEventDestroy(&subQueryExpr->subEvent);
      subQueryExpr->subEvent = NULL;
      return GNCDB_PARAM_INVALID;
    }
    subQueryExpr->stmt           = (SelectStmt *)selectStmt;
    subQueryExpr->subEvent->stmt = selectStmt;
    return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

int UpdateStmtConstruct(SQLStageEvent *sqlEvent, Stmt **stmt)
{
  int            rc            = GNCDB_SUCCESS;
  GNCDB         *db            = NULL;
  BtreeTable    *table         = NULL;
  char          *tableName     = NULL;
  TableSchema   *tableSchema   = NULL;
  char          *attributeName = NULL;
  Expression    *updateValue   = NULL;
  Column        *column        = NULL;
  Value         *val           = NULL;
  HashMap       *tableMap      = NULL;
  FilterStmt    *filterStmt    = NULL;
  varArrayList  *tables        = NULL;
  UpdateStmt    *updateStmt    = NULL;
  int            attrType      = -1;
  int            fieldType     = -1;
  int            i             = 0;
  UpdateSqlNode *updateSql     = NULL;

  /*1.参数检验，主要检查表名是否为空、更新的列数和更新的值数是否匹配*/
  if (sqlEvent == NULL || stmt == NULL) {
    return GNCDB_PARAMNULL;
  }
  updateSql = sqlEvent->sqlNode->update;
  db        = sqlEvent->db;
  tableName = updateSql->relationName;
  if (db == NULL || isBlank(tableName)) {
    return GNCDB_PARAM_INVALID;
  }
  if (updateSql->attributeNames->elementCount != updateSql->values->elementCount) {
    return GNCDB_PARAM_INVALID;
  }

  /*2.获取表信息*/
  rc = catalogGetTable(db->catalog, &table, tableName);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return GNCDB_TABLESCHEMA_NOT_FOUND;
  }

  /*3.主要检查更新条件的左列和右值是否匹配以及是，否满足列约束条件*/
  for (i = 0; i < updateSql->attributeNames->elementCount; i++) {
    attributeName = (char *)varArrayListGetPointer(updateSql->attributeNames, i);
    updateValue   = (Expression *)varArrayListGetPointer(updateSql->values, i);
    column        = attributeGetMatchColumn(
        tableSchema->columnList, attributeName, tableSchema->columnList->elementCount - SYS_FIELD_NUM);
    if (column == NULL) {
      return GNCDB_COLUMN_NOT_FOUND;
    }

    /*3.1若是值则判断数据类型是否合法，如果合法则判断是否满足列约束*/
    if (updateValue->type == ETG_VALUE) {
      val       = ((ValueExpr *)updateValue)->value;
      attrType  = val->attrType;
      fieldType = column->fieldType;
      if (attrType != fieldType) {
        if (valueTypeCast(val, fieldType) != GNCDB_SUCCESS) {
          if (attrType == (AttrType)NULLS && column->columnConstraint->canBeNull == true) {
            continue;
          } else if (attrType == CHARS && fieldType == TEXTS) {
            if (val->length <= column->columnConstraint->maxValue) {
              continue;
            }
          }
          return GNCDB_SCHEMA_FIELD_TYPE_MISMATCH;
        }
      } else {
        if (attrType == CHARS) {
          if (val->length > column->columnConstraint->maxValue) {
            return GNCDB_SCHEMA_FIELD_TYPE_MISMATCH;
          }
        }
      }
      // TODO 同类型的合法性检查？
    }
    /*3.2如果不是ValueExpr，需要递归进行检查 */
    else {
      if (exprTraverseCheckPrepare(updateValue, updateValueCheck, tableSchema->columnList, 1, sqlEvent) !=
          GNCDB_SUCCESS) {
        return GNCDB_PARAM_INVALID;
      }
    }
  }

  /*4.构造update语句的过滤条件*/
  tableMap = hashMapCreate(STRKEY, 0, NULL);
  if (tableMap == NULL) {
    return GNCDB_MAP_CREATE_FALSE;
  }
  tables = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (tables == NULL) {
    hashMapDestroy(&tableMap);
    return GNCDB_MEM;
  }
  hashMapPut(tableMap, tableName, table);
  varArrayListAddPointer(tables, table);
  rc = FilterStmtConstruct(sqlEvent, table, tables, tableMap, PTR_MOVE((void **)&updateSql->conditions), &filterStmt);
  if (rc != GNCDB_SUCCESS) {
    hashMapDestroy(&tableMap);
    varArrayListDestroy(&tables);
    return rc;
  }
  
  /*5.构造updatestmt*/
  updateStmt = UpdateStmtCreate();
  if (updateStmt == NULL) {
    hashMapDestroy(&tableMap);
    varArrayListDestroy(&tables);
    return GNCDB_MEM;
  }
  updateStmt->table            = table;
  updateStmt->valueCount       = updateSql->attributeNames->elementCount;
  updateStmt->updateFieldNames = PTR_MOVE((void **)&updateSql->attributeNames);
  updateStmt->updateValues     = PTR_MOVE((void **)&updateSql->values);
  updateStmt->filterStmt       = filterStmt;
  *stmt                        = (Stmt *)updateStmt;

  varArrayListDestroy(&tables);
  hashMapDestroy(&tableMap);
  return GNCDB_SUCCESS;
}

void UpdateStmtDestroy(UpdateStmt *updateStmt)
{
  if (updateStmt == NULL) {
    return;
  }
  if (updateStmt->updateFieldNames != NULL) {
    varArrayListDestroy(&updateStmt->updateFieldNames);
  }
  if (updateStmt->updateValues != NULL) {
    varArrayListDestroy(&updateStmt->updateValues);
  }
  if (updateStmt->filterStmt != NULL) {
    FilterStmtDestroy(updateStmt->filterStmt);
  }
  updateStmt->table = NULL;
  my_free(updateStmt);
}