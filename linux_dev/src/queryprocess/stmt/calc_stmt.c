#include "calc_stmt.h"
#include "parse_defs.h"
#include "vararraylist.h"
CalcStmt *CalcStmtCreate()
{
  CalcStmt *stmt = (CalcStmt *)my_malloc0(sizeof(CalcStmt));
  if (stmt == NULL) {
    return NULL;
  }
  stmt->type        = ST_CALC;
  stmt->expressions = NULL;
  return stmt;
}

int CalcStmtConstruct(CalcSqlNode *calcSql, Stmt **stmt)
{
  CalcStmt *calcStmt = NULL;
  calcStmt           = CalcStmtCreate();
  if (calcStmt == NULL) {
    return GNCDB_MEM;
  }
  calcStmt->expressions = PTR_MOVE((void **)&calcSql->expressions);
  *stmt                 = (Stmt *)calcStmt;
  return GNCDB_SUCCESS;
}

void CalcStmtDestroy(CalcStmt *calcStmt)
{
  if(calcStmt == NULL) {
    return;
  }
  if (calcStmt->expressions != NULL) {
    varArrayListDestroy(&calcStmt->expressions);
  }
  my_free(calcStmt);
}
