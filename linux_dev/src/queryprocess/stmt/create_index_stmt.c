#include "create_index_stmt.h"
#include "gncdbconstant.h"
#include "parse_defs.h"
CreateIndexStmt *CreateIndexStmtCreate()
{
  CreateIndexStmt *createIndexStmt = (CreateIndexStmt *)my_malloc0(sizeof(CreateIndexStmt));
  if (createIndexStmt == NULL) {
    return NULL;
  }
  createIndexStmt->type          = ST_CREATE_INDEX;
  createIndexStmt->indexName     = NULL;
  createIndexStmt->relationName  = NULL;
  createIndexStmt->attributeName = NULL;
  createIndexStmt->indexType     = INDEX_TYPE_HASH;  /*默认为哈希索引*/
  return createIndexStmt;
}

int CreateIndexStmtConstruct(GNCDB *db, CreateIndexSqlNode *createIndex, Stmt **stmt)
{
  CreateIndexStmt *createIndexStmt = NULL;
  /*1.参数检查*/
  if (db == NULL || createIndex == NULL || stmt == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.创建索引语句对象*/
  createIndexStmt = CreateIndexStmtCreate();
  if (!createIndexStmt) {
    return GNCDB_MEM;
  }
  
  /*3.设置索引属性*/
  createIndexStmt->indexName     = strdup(createIndex->indexName);
  createIndexStmt->relationName  = strdup(createIndex->relationName);
  createIndexStmt->attributeName = strdup(createIndex->attributeName);
  createIndexStmt->indexType     = createIndex->indexType;  // 设置索引类型

  /*4.检查内存分配是否成功*/
  if (createIndexStmt->indexName == NULL || createIndexStmt->relationName == NULL ||
      createIndexStmt->attributeName == NULL) {
    CreateIndexStmtDestroy(createIndexStmt);
    return GNCDB_MEM;
  }

  *stmt = (Stmt *)createIndexStmt;

  /*5.清理内存*/
  // CreateIndexSqlNodeDestroy(createIndex);
  return 0;
}

void CreateIndexStmtDestroy(CreateIndexStmt *createIndexStmt)
{
  if (createIndexStmt) {
    if (createIndexStmt->indexName) {
      my_free(createIndexStmt->indexName);
    }
    if (createIndexStmt->relationName) {
      my_free(createIndexStmt->relationName);
    }
    if (createIndexStmt->attributeName) {
      my_free(createIndexStmt->attributeName);
    }
    my_free(createIndexStmt);
  }
}
