#include "insert_stmt.h"
#include "typedefine.h"
#include "vararraylist.h"
int checkFullRows(GNCDB *db, BtreeTable *table, InsertSqlNode *inserts, varArrayList *rows)
{
  int           rc          = GNCDB_SUCCESS;
  TableSchema  *tableSchema = NULL;
  int           fieldCount  = 0;
  int           i = 0, j = 0;
  varArrayList *valueList = NULL;
  varArrayList *row       = NULL;
  Column       *column    = NULL;
  Value        *value     = NULL;
  int           fieldType = -1;
  int           valueType = -1;

  /*1.参数检查*/
  tableSchema = getTableSchema(db->catalog, table->tableName);
  if (tableSchema == NULL) {
    return GNCDB_TABLESCHEMA_NOT_FOUND;
  }

  /*2.对每组插入值进行检查并根据value的值构建插入元组*/
  fieldCount = tableSchema->columnList->elementCount - SYS_FIELD_NUM;
  for (i = 0; i < inserts->valuelists->elementCount; i++) {
    valueList = varArrayListGetPointer(inserts->valuelists, i);
    /*2.1检查插入value的数量是否匹配 */
    if (valueList->elementCount != fieldCount) {
      return GNCDB_SCHEMA_NUM_MISMATCH;
    }
    row = varArrayListCreate(DISORDER, sizeof(Value *), fieldCount, NULL, valuePointerDestroy);
    if (row == NULL) {
      return GNCDB_MEM;
    }
    /*2.2检查插入value的类型是否匹配 */
    for (j = 0; j < fieldCount; j++) {
      column    = varArrayListGetPointer(tableSchema->columnList, j);
      value     = varArrayListGetPointer(valueList, j);
      fieldType = column->fieldType;
      valueType = value->attrType;
      if (valueType == NULLS && column->columnConstraint->canBeNull == true) {
        varArrayListAddPointer(row, valueCopy(value));
        continue;
      }
      if (valueType == BOOLEANS) {
        varArrayListDestroy(&row);
        return false;
      }
      if (fieldType != valueType) {
        if (fieldType == BLOB) {
          valueSetNull(value);
          varArrayListAddPointer(row, valueCopy(value));
          continue;
        }
        if (fieldType == TEXTS && CHARS == valueType) {
          if (MAX_TEXT_LENGTH < value->length) {
            varArrayListDestroy(&row);
            return GNCDB_TEXT_LENGTH_EXCEED;
          }
        } else if (valueTypeCast(value, fieldType) != GNCDB_SUCCESS) {
          varArrayListDestroy(&row);
          return GNCDB_SCHEMA_FIELD_TYPE_MISMATCH;
        }
      }
      if (fieldType == CHARS && value->length > column->columnConstraint->maxValue) {
        varArrayListDestroy(&row);
        return GNCDB_PARAM_INVALID;
      }
      // TODO 将不确定长度的 char 改为固定长度的 char？
      varArrayListAddPointer(row, valueCopy(value));
    }
    varArrayListAddPointer(rows, row);
  }
  return rc;
}

int checkIncompleteRows(GNCDB *db, BtreeTable *table, InsertSqlNode *inserts, varArrayList *rows)
{
  int           rc          = GNCDB_SUCCESS;
  TableSchema  *tableSchema = NULL;
  varArrayList *attributes  = NULL;
  int           i = 0, j = 0, k = 0;
  varArrayList *valueList  = NULL;
  int           fieldCount = 0;
  int          *colIdx     = NULL;
  int           col        = -1;
  int           fieldType  = -1;
  int           valueType  = -1;
  Column       *column     = NULL;
  Value        *value      = NULL;
  Value        *nullValue  = NULL;
  varArrayList *row        = NULL;
  char         *attribute  = NULL;

  tableSchema = getTableSchema(db->catalog, table->tableName);
  if (tableSchema == NULL) {
    return GNCDB_TABLESCHEMA_NOT_FOUND;
  }
  fieldCount = tableSchema->columnList->elementCount - SYS_FIELD_NUM;
  attributes = inserts->attributes;

  /*1.记录行中每一列是values中第几个，不存在的为-1 */
  colIdx = (int *)my_malloc0(sizeof(int) * fieldCount);
  if (colIdx == NULL) {
    return GNCDB_MEM;
  }
  for (i = 0; i < fieldCount; ++i) {
    colIdx[i] = -1;
  }

  /*2.最外层代表插入的每一组值，内层循环代表每组值的具体的value*/
  for (i = 0; i < inserts->valuelists->elementCount; i++) {
    /*2.1检查每组插入值的个数和插入列的个数是否相同*/
    valueList = varArrayListGetPointer(inserts->valuelists, i);
    if (valueList->elementCount != attributes->elementCount) {
      my_free(colIdx);
      return GNCDB_SCHEMA_NUM_MISMATCH;
    }

    /*2.2检查插入列是否是表中的列，并记录是表中的第几列*/
    for (j = 0; j < attributes->elementCount; j++) {
      attribute = varArrayListGetPointer(attributes, j);
      col       = -1;
      for (k = 0; k < fieldCount; k++) {
        column = varArrayListGetPointer(tableSchema->columnList, k);
        if (strcmp(column->fieldName, attribute) == 0) {
          col = k;
          break;
        }
      }
      if (col == -1) {
        my_free(colIdx);
        return GNCDB_FIELD_NOT_EXIST;
      }
      colIdx[col] = j;
    }
  }

  /*3.为每一组插入值构造插入元组*/
  for (i = 0; i < inserts->valuelists->elementCount; i++) {
    /*3.1首先创建一个空元组*/
    valueList = varArrayListGetPointer(inserts->valuelists, i);
    row       = varArrayListCreate(DISORDER, sizeof(Value *), fieldCount, NULL, valuePointerDestroy);
    for (k = 0; k < fieldCount; k++) {
      nullValue = (Value *)my_malloc0(sizeof(Value));
      valueSetNull(nullValue);
      varArrayListAddPointer(row, nullValue);
    }
    /*3.2根据表中的列对value的值进行数据检查*/
    for (j = 0; j < fieldCount; j++) {
      column = varArrayListGetPointer(tableSchema->columnList, j);
      col    = colIdx[j];
      if (col == -1) {
        if (column->columnConstraint->canBeNull == false) {
          my_free(colIdx);
          varArrayListDestroy(&row);
          return GNCDB_SCHEMA_NUM_MISMATCH;
        }
      } else {
        value     = varArrayListGetPointer(valueList, col);
        fieldType = column->fieldType;
        valueType = value->attrType;
        if (valueType == NULLS && column->columnConstraint->canBeNull == true) {
          continue;
        }
        if (valueType == BOOLEANS) {
          my_free(colIdx);
          varArrayListDestroy(&row);
          return false;
        }
        if (fieldType != valueType) {
          if (column->fieldType == FIELDTYPE_BLOB) {
            continue;
          }
          if (fieldType == TEXTS && CHARS == valueType) {
            if (MAX_TEXT_LENGTH < value->length) {
              my_free(colIdx);
              varArrayListDestroy(&row);
              return GNCDB_TEXT_LENGTH_EXCEED;
            }
          } else if ((fieldType == BLOB && valueType == NULLS) ||
                     (valueType == NULLS && column->columnConstraint->canBeNull == true)) {
          } else if (valueTypeCast(value, fieldType) != GNCDB_SUCCESS) {
            my_free(colIdx);
            varArrayListDestroy(&row);
            return GNCDB_SCHEMA_FIELD_TYPE_MISMATCH;
          }
        }

        if (fieldType == CHARS && value->length > column->columnConstraint->maxValue) {
          my_free(colIdx);
          varArrayListDestroy(&row);
          return GNCDB_PARAM_INVALID;
        }

        /*3.2.1将合法的value放进空元组的对应的位置形成要插入的元组*/
        // TODO 将不确定长度的 char 改为固定长度的 char？
        valueDestroy(varArrayListGet(row, j));  /*varArrayListSetByIndexPointer不会释放原来的内存，所以这里需要手动释放*/
        varArrayListSetByIndexPointer(row, j, valueCopy(value));
      }
    }
    varArrayListAddPointer(rows, row);
  }
  my_free(colIdx);
  return rc;
}

InsertStmt *InsertStmtCreate()
{
  InsertStmt *insertStmt = (InsertStmt *)my_malloc0(sizeof(InsertStmt));
  if (insertStmt == NULL) {
    return NULL;
  }
  insertStmt->type       = ST_INSERT;
  insertStmt->table      = NULL;
  insertStmt->valuelists = NULL;
  insertStmt->valueCount = 0;
  return insertStmt;
}

int InsertStmtConstruct(GNCDB *db, InsertSqlNode *inserts, Stmt **stmt)
{
  int           rc          = GNCDB_SUCCESS;
  char         *tableName   = NULL;
  BtreeTable   *table       = NULL;
  TableSchema  *tableSchema = NULL;
  varArrayList *rows        = NULL;
  InsertStmt   *insertStmt  = NULL;
  
  /*1.参数检查*/
  tableName = inserts->relationName;
  if (NULL == db || NULL == tableName || inserts->attributes == NULL || inserts->valuelists == NULL ||
      inserts->valuelists->elementCount == 0) {
    return GNCDB_PARAM_INVALID;
  }
  /*1.1不能往系统表插入数据*/
  if (strcmp(tableName, "master") == 0 || strcmp(tableName, "schema") == 0) {
    return GNCDB_NOT_REFACTOR;
  }

  /*2.获取表、表元信息*/
  rc = catalogGetTable(db->catalog, &table, tableName);
  if (GNCDB_SUCCESS != rc) {
    return GNCDB_TABLE_NOT_FOUND;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (NULL == tableSchema) {
    return GNCDB_TABLESCHEMA_NOT_FOUND;
  }

  /*3.插入列合法性检查，主要检查列类型和表模式是否匹配以及一些列约束*/
  rows = varArrayListCreate(DISORDER, sizeof(varArrayList *), 0, NULL, varArrayListPointerDestroy);
  /*3.1没有指定具体列名 */
  if (inserts->attributes->elementCount == 0) {
    rc = checkFullRows(db, table, inserts, rows);
  }
  /*3.2指定了具体列名 */
  else {
    rc = checkIncompleteRows(db, table, inserts, rows);
  }

  /*4.构造insertstmt*/
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&rows);
    return rc;
  }
  insertStmt = InsertStmtCreate();
  if (insertStmt == NULL) {
    varArrayListDestroy(&rows);
    return GNCDB_MEM;
  }
  insertStmt->table      = table;
  insertStmt->valuelists = rows;
  insertStmt->valueCount = tableSchema->columnList->elementCount - SYS_FIELD_NUM;
  *stmt                  = (Stmt *)insertStmt;

  return rc;
}

/**
 * @brief 销毁insertStmt
 *
 * @param stmt
 */
void InsertStmtDestroy(InsertStmt **stmt)
{
  if (stmt == NULL || *stmt == NULL) {
    return;
  }
  if ((*stmt)->valuelists != NULL) {
    varArrayListDestroy(&(*stmt)->valuelists);
  }
  (*stmt)->table = NULL;
  my_free(*stmt);
  *stmt = NULL;
}
