#include "set_variable_stmt.h"
#include "parse_defs.h"
int SetVariableStmtConstruct(struct SetVariableSqlNode *setVariable, Stmt **stmt)
{
  SetVariableStmt *setVariableStmt = NULL;

  setVariableStmt                  = (SetVariableStmt *)my_malloc0(sizeof(SetVariableStmt));
  setVariableStmt->type            = ST_SET_VARIABLE;
  setVariableStmt->setVariableNode = setVariable;
  *stmt                            = (Stmt *)setVariableStmt;
  return GNCDB_SUCCESS;
}

void SetVariableStmtDestroy(SetVariableStmt *setVariableStmt)
{
  SetVariableSqlNodeDestroy(setVariableStmt->setVariableNode);
  my_free(setVariableStmt);
}
