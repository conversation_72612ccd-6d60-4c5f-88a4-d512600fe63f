#include "filter_stmt.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "logical_plan_generator.h"
#include "physical_plan_generator.h"
#include "assert.h"
#include "sql_event.h"
#include "stmt.h"
#include "value.h"
int FillField(Field **field, Column *column)
{
  *field = (Field *)my_malloc0(sizeof(Field));
  if (*field == NULL) {
    return GNCDB_MEM;
  }
  switch (column->fieldType) {
    case FIELDTYPE_INTEGER:
      (*field)->fieldType = (FieldType)INTS;
      (*field)->fieldSize = INT_SIZE;
      break;
    case FIELDTYPE_REAL:
      (*field)->fieldType = (FieldType)DOUBLES;
      (*field)->fieldSize = DOUBLE_SIZE;
      break;
    case FIELDTYPE_VARCHAR:
      (*field)->fieldType = (FieldType)CHARS;
      (*field)->fieldSize = strlen(column->fieldName);
      break;
    case FIELDTYPE_BLOB:
      (*field)->fieldType = (FieldType)BLOB;
      (*field)->fieldSize = INT_SIZE;
      break;
    case FIELDTYPE_DATE:
      (*field)->fieldType = (FieldType)DATES;
      (*field)->fieldSize = DATE_SIZE;
      break;
    case FIELDTYPE_DATETIME:
      (*field)->fieldType = (FieldType)DATETIMES;
      (*field)->fieldSize = DATETIME_SIZE;
      break;
    default:
      (*field)->fieldType = (FieldType)UNDEFINED;
      (*field)->fieldSize = -1;
      break;
  }
  return GNCDB_SUCCESS;
}

/**
 * @brief   检查条件表达式中的子查询(分配的内存由上层函数管理)
 *
 * @param expr
 * @param list
 * @param additionalParamsCount
 * @param args
 * @return int
 */
int checkConditionSubQueryExpr(Expression *expr, varArrayList *list, int additionalParamsCount, va_list args)
{
  int            rc = GNCDB_SUCCESS;
  SQLStageEvent *sqlEvent;
  // BtreeTable       *defaultTable;
  HashMap *tables;
  // varArrayList     *tableArr;
  SubQueryExpr *subqueryExpr = NULL;
  // bool              fieldIsStar         = false;
  Expression       *projectExpr         = NULL;
  Stmt             *selectStmt          = NULL;
  SelectStmt       *ss                  = NULL;
  LogicalOperator  *subLogicalOperator  = NULL;
  PhysicalOperator *subPhysicalOperator = NULL;
  FieldExpr        *fieldExpr           = NULL;
  int               i                   = 0;
  bool              subqueryIsNot1      = false;

  assert(additionalParamsCount == 4);
  if (expr->type == ETG_SUBQUERY) {
    tables = va_arg(args, HashMap *);
    va_arg(args, varArrayList *);
    sqlEvent = va_arg(args, SQLStageEvent *);
    va_arg(args, BtreeTable *);
    subqueryExpr = (SubQueryExpr *)expr;

    /* 这里判断是否之前已经处理子查询，如果处理过了就不需要再处理了 */
    if (!subqueryExpr->subEvent) {
      subqueryExpr->subEvent = SQLStageEventCreate();
      if (subqueryExpr->subEvent == NULL) {
        return GNCDB_MEM;
      }
      subqueryExpr->subEvent->affectedRows = 0;
      subqueryExpr->subEvent->sqlNode      = (ParsedSqlNode *)my_malloc0(sizeof(ParsedSqlNode));
      if (subqueryExpr->subEvent->sqlNode == NULL) {
        SQLStageEventDestroy(&subqueryExpr->subEvent);
        subqueryExpr->subEvent = NULL;
        return GNCDB_MEM;
      }
      subqueryExpr->subEvent->sqlNode->flag      = SCF_SELECT;
      subqueryExpr->subEvent->sqlNode->selection = (PTR_MOVE((void **)&subqueryExpr->sqlNode));
      assert(subqueryExpr->subEvent->sqlNode->selection != NULL);
      for (i = 0; i < subqueryExpr->subEvent->sqlNode->selection->projectExprs->elementCount; i++) {
        projectExpr = (Expression *)varArrayListGetPointer(subqueryExpr->subEvent->sqlNode->selection->projectExprs, i);
        if (projectExpr->type == ETG_FIELD) {
          fieldExpr = (FieldExpr *)projectExpr;
          if (fieldExpr->tableName && fieldExpr->fieldName && strcmp(fieldExpr->tableName, "*") == 0 &&
              strcmp(fieldExpr->fieldName, "*") == 0) {
            break;
          }
        }
      }

      subqueryExpr->subEvent->callback      = sqlEvent->callback;
      subqueryExpr->subEvent->db            = sqlEvent->db;
      subqueryExpr->subEvent->sql           = my_strdup(expr->name);
      subqueryExpr->subEvent->txn           = sqlEvent->txn;
      subqueryExpr->subEvent->subQueryLevel = sqlEvent->subQueryLevel + 1;

      // 生成子查询stmt
      if (GNCDB_SUCCESS != (rc = SelectStmtConstruct(subqueryExpr->subEvent, &selectStmt, tables))) {
        SQLStageEventDestroy(&subqueryExpr->subEvent);
        subqueryExpr->subEvent = NULL;
        return rc;
      }
      if (selectStmt->type != ST_SELECT) {
        SQLStageEventDestroy(&subqueryExpr->subEvent);
        StmtDestroy(selectStmt);
        subqueryExpr->subEvent = NULL;
        return GNCDB_INTERNAL;
      }
      ss = (SelectStmt *)selectStmt;
      if (ss->queryFields == NULL) {
        printf("subSelect query field nums is null\n");
        SQLStageEventDestroy(&subqueryExpr->subEvent);
        subqueryExpr->subEvent = NULL;
        StmtDestroy(selectStmt);
        return GNCDB_INTERNAL;
      }

      if ((ss->queryFields->elementCount > 1)) {
        subqueryIsNot1 = true;
      }

      subqueryExpr->stmt           = (SelectStmt *)selectStmt;
      subqueryExpr->subEvent->stmt = selectStmt;

      // 生成子查询logical operator
      if (GNCDB_SUCCESS !=
          (rc = logicalPlanConstruct(subqueryExpr->subEvent, (Stmt *)subqueryExpr->stmt, &subLogicalOperator))) {
        SQLStageEventDestroy(&subqueryExpr->subEvent);
        subqueryExpr->subEvent = NULL;
        printf("subquery logical plan construct failed\n");
        return rc;
      }
      subqueryExpr->logicalOper           = subLogicalOperator;
      subqueryExpr->subEvent->logicalPlan = subLogicalOperator;
      // 生成子查询physical operator
      if (GNCDB_SUCCESS !=
          (rc = physicalPlanConstruct(subqueryExpr->subEvent, subLogicalOperator, &subPhysicalOperator))) {
        SQLStageEventDestroy(&subqueryExpr->subEvent);
        subqueryExpr->subEvent = NULL;
        printf("subquery physical plan construct failed\n");
        return rc;
      }
      subqueryExpr->physicalOper   = subPhysicalOperator;
      subqueryExpr->subEvent->plan = subPhysicalOperator;

      rc = setColMeta(subPhysicalOperator);
      if (rc != GNCDB_SUCCESS) {
        /* setColMeta failed, clean up the subquery */
        SQLStageEventDestroy(&subqueryExpr->subEvent);
        subqueryExpr->subEvent = NULL;
        printf("setColMeta failed for subquery\n");
        return rc;
      }

      if (subqueryIsNot1) {
        return GNCDB_SUBQUERY_FIELD_IS_NOT_ONE;
      }
    }

    return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

int checkConditionCompExpr(Expression *expr, varArrayList *list, int additionalParamsCount, va_list args)
{
  ComparisonExpr *cmpExpr = NULL;
  if (expr->type == ETG_COMPARISON) {
    cmpExpr = (ComparisonExpr *)expr;
    if (cmpExpr->comp < CMPOP_EQUAL_TO || cmpExpr->comp >= CMPOP_NO_OP) {
      printf("invalid compare operator\n");
      return GNCDB_PARAM_INVALID;
    }
    return GNCDB_SUCCESS;
  }
  return GNCDB_SUCCESS;
}

int checkConditionFieldExpr(Expression *expr, varArrayList *list, int additionalParamsCount, va_list args)
{
  SQLStageEvent *sqlEvent     = NULL;
  BtreeTable    *defaultTable = NULL;
  HashMap       *tableMap     = NULL;
  varArrayList  *tables       = NULL;
  FieldExpr     *fieldExpr    = NULL;
  assert(additionalParamsCount == 4);

  if (expr->type == ETG_FIELD) {
    tableMap     = va_arg(args, HashMap *);
    tables       = va_arg(args, varArrayList *);
    sqlEvent     = va_arg(args, SQLStageEvent *);
    defaultTable = va_arg(args, BtreeTable *);
    fieldExpr    = (FieldExpr *)expr;
    // 条件表达式检查时可以不用 table_alias_map 因为不需要设置它的 alias
    return exprCheckField(tableMap, tables, sqlEvent->db, (Expression *)fieldExpr, defaultTable, NULL);
  }
  return GNCDB_SUCCESS;
}

int checkCond(
    Expression *expr, varArrayList *tables, HashMap *tableMap, SQLStageEvent *sqlEvent, BtreeTable *defaultTable)
{
  int rc = GNCDB_SUCCESS;
  if (GNCDB_SUCCESS != (rc = exprTraverseCheckPrepare(
                            expr, checkConditionFieldExpr, NULL, 4, tableMap, tables, sqlEvent, defaultTable))) {
    return rc;
  }
  if (GNCDB_SUCCESS != (rc = exprTraverseCheckPrepare(
                            expr, checkConditionCompExpr, NULL, 4, tableMap, tables, sqlEvent, defaultTable))) {
    return rc;
  }
  if (GNCDB_SUCCESS != (rc = exprTraverseCheckPrepare(
                            expr, checkConditionSubQueryExpr, NULL, 4, tableMap, tables, sqlEvent, defaultTable))) {
    return rc;
  }
  return rc;
}

/**
 * @brief
 *
 * @param sqlEvent
 * @param defaultTable
 * @param tables
 * @param tableMap
 * @param condition   条件表达式(内存由该函数管理)
 * @param stmt
 * @return int
 */
int FilterStmtConstruct(SQLStageEvent *sqlEvent, BtreeTable *defaultTable, varArrayList *tables, HashMap *tableMap,
    Expression *condition, FilterStmt **stmt)
{
  int         rc      = GNCDB_SUCCESS;
  FilterStmt *tmpStmt = NULL;

  /*1.参数检查*/
  if (condition == NULL) {
    return rc;
  }

  /*2.检查过滤条件*/
  if (GNCDB_SUCCESS !=
      (rc = exprCheckField(tableMap, tables, sqlEvent->db, condition, defaultTable, NULL))) {  // 检查条件表达式是否合法
    exprDestroy(condition);
    return rc;
  }

  /*3.构造过滤stmt*/
  tmpStmt = (FilterStmt *)my_malloc0(sizeof(FilterStmt));
  if (tmpStmt == NULL) {
    exprDestroy(condition);
    return GNCDB_MEM;
  }
  tmpStmt->condition = condition;
  *stmt              = tmpStmt;

  return rc;
}

void FilterStmtDestroy(FilterStmt *stmt)
{
  if (stmt == NULL) {
    return;
  }
  if (stmt->condition != NULL) {
    exprDestroy(stmt->condition);
  }
  my_free(stmt);
}

void FilterStmtPointerDestroy(void *data)
{
  FilterStmt **stmt = (FilterStmt **)data;
  if (stmt == NULL || *stmt == NULL) {
    return;
  }
  FilterStmtDestroy(*stmt);
  *stmt = NULL;
}