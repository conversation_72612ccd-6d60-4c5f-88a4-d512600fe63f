#include "stmt.h"
#include "insert_stmt.h"
#include "delete_stmt.h"
#include "parse_defs.h"
#include "select_stmt.h"
#include "explain_stmt.h"
#include "create_index_stmt.h"
#include "create_table_stmt.h"
#include "desc_table_stmt.h"
#include "help_stmt.h"
#include "show_tables_stmt.h"
#include "trx_begin_stmt.h"
#include "trx_end_stmt.h"
#include "exit_stmt.h"
#include "set_variable_stmt.h"
#include "load_data_stmt.h"
#include "calc_stmt.h"
#include "update_stmt.h"
#include "gncdbconstant.h"
#include "drop_table_stmt.h"
#include "drop_index_stmt.h"
int StmtConstruct(SQLStageEvent *sqlEvent, Stmt **stmt)
{

  int            rc      = GNCDB_SUCCESS;
  GNCDB         *db      = NULL;
  ParsedSqlNode *sqlNode = NULL;

  db      = sqlEvent->db;
  sqlNode = sqlEvent->sqlNode;
  *stmt   = NULL;
  switch (sqlEvent->sqlNode->flag) {
    case SCF_DROP_TABLE: {
      /* 构造删表语句 */
      rc = DropTableStmtConstruct(sqlEvent, stmt);
    } break;
    case SCF_DROP_INDEX: {
      /* 构造hash索引删除语句 */
      rc = DropIndexStmtConstruct(sqlEvent, stmt);
    } break;
    case SCF_UPDATE: {
      /* 构造更新语句 */
      rc = UpdateStmtConstruct(sqlEvent, stmt);
    } break;

    case SCF_INSERT: {
      /* 构造插入语句 */
      rc = InsertStmtConstruct(db, sqlNode->insertion, stmt);
    } break;
    case SCF_DELETE: {
      /* 构造删除语句 */
      rc = DeleteStmtConstruct(sqlEvent, sqlNode->deletion, stmt);
    } break;
    case SCF_SELECT: {
      /* 构造查询语句 */
      rc = SelectStmtConstruct(sqlEvent, stmt, NULL);
    } break;

    case SCF_EXPLAIN: {
      /* 构造解释语句 */
      rc = ExplainStmtConstruct(sqlEvent, stmt);
    } break;

    case SCF_CREATE_INDEX: {
      /* 构造创建索引语句 */
      rc = CreateIndexStmtConstruct(db, sqlNode->createIndex, stmt);
    } break;

    case SCF_CREATE_TABLE: {
      /* 构造创建表语句 */
      rc = CreateTableStmtConstruct(sqlEvent, sqlNode->createTable, stmt, sqlNode->createTable->select);
    } break;

    case SCF_DESC_TABLE: {
      /* 构造描述表语句 */
      rc = DescTableStmtConstruct(db, sqlNode->descTable, stmt);
    } break;

    case SCF_HELP: {
      /* 构造帮助语句 */
      rc = HelpStmtConstruct(stmt);
    } break;

    case SCF_SHOW_TABLES: {
      /* 构造显示表语句 */
      rc = ShowTablesStmtConstruct(db, stmt);
    } break;

    case SCF_BEGIN: {
      /* 构造事务开始语句 */
      rc = TrxBeginStmtConstruct(stmt);
    } break;

    case SCF_COMMIT:
    case SCF_ROLLBACK: {
      /* 构造事务结束语句 */
      rc = TrxEndStmtConstruct(sqlNode->flag, stmt);
    } break;

    case SCF_EXIT: {
      /* 构造退出语句 */
      rc = ExitStmtConstruct(stmt);
    } break;

    case SCF_SET_VARIABLE: {
      /* 构造设置变量语句 */
      rc = SetVariableStmtConstruct(sqlNode->setVariable, stmt);
    } break;

    case SCF_LOAD_DATA: {
      /* 构造加载数据语句 */
      rc = LoadDataStmtConstruct(db, sqlNode->loadData, stmt);
    } break;

    case SCF_CALC: {
      /* 构造计算语句 */
      rc = CalcStmtConstruct(sqlNode->calc, stmt);
    } break;

    default: {
    } break;
  }

  return rc;
}

void StmtDestroy(Stmt *stmt)
{
  if (stmt == NULL) {
    return;
  }
  switch (stmt->type) {
    case ST_CALC: {
      CalcStmtDestroy((CalcStmt *)stmt);
      break;
    }
    case ST_SELECT: {
      SelectStmtDestroy((SelectStmt *)stmt);
      break;
    }
    case ST_INSERT: {
      InsertStmtDestroy((InsertStmt **)&stmt);
      break;
    }
    case ST_UPDATE: {
      UpdateStmtDestroy((UpdateStmt *)stmt);
      break;
    }
    case ST_DELETE: {
      DeleteStmtDestroy((DeleteStmt *)stmt);
      break;
    }
    case ST_CREATE_TABLE: {
      CreateTableStmtDestroy((CreateTableStmt *)stmt);
      break;
    }
    case ST_DROP_TABLE: {
      DropTableStmtDestroy((DropTableStmt *)stmt);
      break;
    }
    case ST_CREATE_INDEX: {
      CreateIndexStmtDestroy((CreateIndexStmt *)stmt);
      break;
    }
    case ST_DROP_INDEX: {
      DropIndexStmtDestroy((DropIndexStmt *)stmt);
      break;
    }
    case ST_SHOW_TABLES: {
      ShowTablesStmtDestroy((ShowTablesStmt *)stmt);
      break;
    }
    case ST_DESC_TABLE: {
      DescTableStmtDestroy((DescTableStmt *)stmt);
      break;
    }
    case ST_BEGIN: {
      TrxBeginStmtDestroy((TrxBeginStmt *)stmt);
      break;
    }
    case ST_COMMIT:
    case ST_ROLLBACK: {
      TrxEndStmtDestroy((TrxEndStmt *)stmt);
      break;
    }
    case ST_LOAD_DATA: {
      LoadDataStmtDestroy((LoadDataStmt *)stmt);
      break;
    }
    case ST_HELP: {
      HelpStmtDestroy((HelpStmt *)stmt);
      break;
    }
    case ST_EXIT: {
      ExitStmtDestroy((ExitStmt *)stmt);
      break;
    }
    case ST_EXPLAIN: {
      ExplainStmtDestroy((ExplainStmt *)stmt);
      break;
    }
    case ST_SET_VARIABLE: {
      SetVariableStmtDestroy((SetVariableStmt *)stmt);
      break;
    }
    case ST_GROUP_BY: {
      GroupByStmtDestroy((GroupByStmt **)&stmt);
      break;
    }
    case ST_ORDER_BY: {
      OrderByStmtDestroy((OrderByStmt *)stmt);
      break;
    }
    case ST_LIMIT: {
      LimitStmtDestroy((LimitStmt *)stmt);
      break;
    }
    default: {
      printf("Stmt type %d doesn't need to destroy.\n", stmt->type);
      break;
    }
  }
}