#include "trx_begin_stmt.h"

int TrxBeginStmtConstruct(Stmt **stmt)
{
  TrxBeginStmt *trxBeginStmt = NULL;

  trxBeginStmt       = (TrxBeginStmt *)my_malloc0(sizeof(TrxBeginStmt));
  if(trxBeginStmt == NULL)
  {
    return GNCDB_MEM;
  }
  trxBeginStmt->type = ST_BEGIN;
  *stmt              = (Stmt *)trxBeginStmt;
  return GNCDB_SUCCESS;
}

void TrxBeginStmtDestroy(TrxBeginStmt *txnBeginStmt) { my_free(txnBeginStmt); }
