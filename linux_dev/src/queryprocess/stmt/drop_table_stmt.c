#include "drop_table_stmt.h"
#include "gncdbconstant.h"
#include "parse_defs.h"
#include "stmt.h"
#include "utils.h"
int DropTableStmtConstruct(SQLStageEvent *sqlEvent, Stmt **stmt)
{
  DropTableStmt    *dropTableStmt = NULL;
  DropTableSqlNode *dropTable     = NULL;

  dropTable     = sqlEvent->sqlNode->dropTable;
  dropTableStmt = (DropTableStmt *)my_malloc0(sizeof(DropTableStmt));
  /*malloc失败*/
  if (dropTableStmt == NULL) {
    return GNCDB_MEM;
  }
  dropTableStmt->type      = ST_DROP_TABLE;
  dropTableStmt->tableName = PTR_MOVE((void**)&dropTable->relationName);
  dropTableStmt->ifExist   = dropTable->ifExists;
  *stmt                    = (Stmt *)dropTableStmt;

  return GNCDB_SUCCESS;
}

void DropTableStmtDestroy(DropTableStmt *dropTableStmt)
{
  if (dropTableStmt == NULL) {
    return;
  }
  if (dropTableStmt->tableName != NULL) {
    my_free(dropTableStmt->tableName);
  }
  my_free(dropTableStmt);
}
