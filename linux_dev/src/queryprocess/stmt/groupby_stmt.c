#include "groupby_stmt.h"
#include "hashmap.h"
#include "btreetable.h"
#include "vararraylist.h"
GroupByStmt *GroupByStmtCreate(){
  GroupByStmt *stmt = (GroupByStmt *)my_malloc0(sizeof(GroupByStmt));
  if(stmt == NULL){
    return NULL;
  }
  stmt->type = ST_GROUP_BY;
  stmt->aggExprs = NULL;
  stmt->fieldExprs = NULL;
  stmt->groupbyFields = NULL;
  return stmt;
}

int GroupByStmtConstructor(GNCDB *db, BtreeTable *defaultTable, HashMap *tables, varArrayList *groupbyExpr,
    GroupByStmt **stmt, varArrayList *aggExprs, varArrayList *fieldExprs)
{
  int rc                 = GNCDB_SUCCESS;
  *stmt                  = NULL;
  *stmt = GroupByStmtCreate();
  if(*stmt == NULL){
    return GNCDB_MEM;
  }
  (*stmt)->aggExprs      = aggExprs;
  (*stmt)->fieldExprs    = fieldExprs;
  (*stmt)->groupbyFields = groupbyExpr;
  return rc;
}

void GroupByStmtDestroy(GroupByStmt **groupbyStmt)
{
  if (groupbyStmt == NULL || *groupbyStmt == NULL) {
    return;
  }
  if ((*groupbyStmt)->aggExprs != NULL) {
    varArrayListDestroy(&(*groupbyStmt)->aggExprs);
  }
  if ((*groupbyStmt)->fieldExprs != NULL) {
    varArrayListDestroy(&(*groupbyStmt)->fieldExprs);
  }
  if ((*groupbyStmt)->groupbyFields != NULL) {
    varArrayListDestroy(&(*groupbyStmt)->groupbyFields);
  }
  my_free((*groupbyStmt));
  *groupbyStmt = NULL;
}
