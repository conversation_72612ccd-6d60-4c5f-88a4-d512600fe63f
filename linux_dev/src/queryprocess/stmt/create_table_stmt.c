#include "create_table_stmt.h"
#include "expression.h"
#include "utils.h"
#include "vararraylist.h"
#include "gncdb.h"
static int ATTR_TYPE_LENGTH[] = {-1, 4, 4, 4, -1, 4, -1};

CreateTableStmt *CreateTableStmtCreate()
{
  CreateTableStmt *stmt = (CreateTableStmt *)my_malloc0(sizeof(CreateTableStmt));
  if (stmt == NULL) {
    return NULL;
  }
  stmt->type       = ST_CREATE_TABLE;
  stmt->tableName  = NULL;
  stmt->attrInfos  = NULL;
  stmt->selectStmt = NULL;
  return stmt;
}

int CreateTableStmtConstruct(
    SQLStageEvent *sqlEvent, CreateTableSqlNode *createTable, Stmt **stmt, SelectSqlNode *select)
{
  int              rc             = GNCDB_SUCCESS;
  Stmt            *selectStmt     = NULL;
  varArrayList    *attrInfos      = NULL;
  SelectStmt      *selectStmtCast = NULL;
  AttrInfoSqlNode  attrInfo1;
  Expression      *attrExpr = NULL;
  char             toFind;
  char            *pos                 = NULL;
  char            *attrName            = NULL;
  FieldExpr       *fieldExpr           = NULL;
  TableSchema     *tableSchema         = NULL;
  Column          *column              = NULL;
  AttrInfoSqlNode *attrInfo2           = NULL;
  AttrInfoSqlNode *createTableAttrInfo = NULL;
  CreateTableStmt *createTableStmt     = NULL;
  int              i                   = 0;

  /* 1.如果create语句带有select语句，需要根据select语句的结果来构造create_table语句 */
  if (select != NULL && select->projectExprs != NULL && select->projectExprs->elementCount > 0) {
    attrInfos = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, AttrInfoSqlNodePointerDestroy);
    /* 1.1 根据select语句构造select_stmt */
    rc = SelectStmtConstruct(sqlEvent, &selectStmt, NULL);
    if (rc != GNCDB_SUCCESS) {
      // printf("CreateTableStmtConstruct: SelectStmtConstruct failed\n");
      varArrayListDestroy(&attrInfos);
      return rc;
    }
    selectStmtCast = (SelectStmt *)selectStmt;
    /* 1.2 根据select语句的结果构造attr_infos */
    for (i = 0; i < selectStmtCast->queryFields->elementCount; i++) {

      attrExpr = (Expression *)varArrayListGetPointer(selectStmtCast->queryFields, i);
      /* 1.2.1 根据attr_expr的alias或者name来设置attr_info的name */
      if (attrExpr->alias != NULL && strlen(attrExpr->alias) != 0) {
        /* 如果alias中包含'.'，说明是表名＋属性名的形式，则取'.'后面的字符串作为name */
        toFind = '.';
        pos    = strchr(attrExpr->alias, toFind);
        if (pos != NULL) {
          attrName = (char *)my_malloc0(sizeof(char) * (strlen(attrExpr->alias) - (pos - attrExpr->alias) + 1));
          if (attrName == NULL) {
            varArrayListDestroy(&attrInfos);
            return GNCDB_MEM;
          }
          strcpy(attrName, pos + 1);
          attrInfo1.name = attrName;

        }
        /* 如果alias中不包含'.'，则直接取alias作为name */
        else {
          attrInfo1.name = attrExpr->alias;
        }
      }
      /* 如果alias为空，则取name作为name */
      else {
        attrInfo1.name = attrExpr->name;
      }
      /* 1.2.2 根据attr_expr的类型来设置attr_info的type */
      attrInfo1.type = expressionGetValueType(attrExpr);
      /* 1.2.3 根据attr_expr的类型来设置attr_info的length */
      if (attrExpr->type == ETG_FIELD) {
        fieldExpr = (FieldExpr *)attrExpr;
        // fieldInfo = fieldExpr->fieldInfo;
        // attrInfo1.length = fieldInfo->field->fieldSize;
        // attr_info.can_be_null = field_info.field
        tableSchema = hashMapGet(sqlEvent->db->catalog->tableSchemaMap, fieldExpr->tableName);
        for (i = 0; i < tableSchema->columnList->elementCount - 3; i++) {
          column = (Column *)varArrayListGetPointer(tableSchema->columnList, i);
          if (strcmp(column->fieldName, fieldExpr->fieldName) == 0) {
            attrInfo1.length       = column->columnConstraint->maxValue;
            attrInfo1.canBeNull    = column->columnConstraint->canBeNull;
            attrInfo1.isPrimaryKey = column->columnConstraint->isPrimaryKey;
            break;
          }
        }
      } else {
        if (attrExpr->type == ETG_VALUE) {
          attrInfo1.length = ((ValueExpr *)attrExpr)->value->length;
        } else {
          attrInfo1.length = ATTR_TYPE_LENGTH[attrInfo1.type];
        }
      }
      varArrayListAddPointer(attrInfos, AttrInfoSqlNodeDeepCopy(&attrInfo1));
    }

    /* 1.3 如果create table指定了列属性，需要比较create_table中的attr_infos和select_stmt中的attr_infos是否匹配 */
    if (createTable->attrInfos != NULL && createTable->attrInfos->elementCount > 0) {
      if (attrInfos->elementCount != createTable->attrInfos->elementCount) {
        printf("CreateTableStmtConstruct: attr_infos count not match\n");
        varArrayListDestroy(&attrInfos);
        return GNCDB_INTERNAL;
      }

      for (i = 0; i < attrInfos->elementCount; i++) {
        attrInfo2           = (AttrInfoSqlNode *)varArrayListGetPointer(attrInfos, i);
        createTableAttrInfo = (AttrInfoSqlNode *)varArrayListGetPointer(createTable->attrInfos, i);
        if (attrInfo2->type != createTableAttrInfo->type) {
          printf("CreateTableStmtConstruct: attr_infos type not match\n");
          varArrayListDestroy(&attrInfos);
          return GNCDB_INTERNAL;
        }
      }
      createTableStmt = (CreateTableStmt *)my_malloc0(sizeof(CreateTableStmt));
      if (createTableStmt == NULL) {
        varArrayListDestroy(&attrInfos);
        return GNCDB_MEM; /*malloc失败*/
      }
      createTableStmt->type       = ST_CREATE_TABLE;
      createTableStmt->tableName  = PTR_MOVE((void **)&createTable->relationName);
      createTableStmt->attrInfos  = PTR_MOVE((void **)&createTable->attrInfos);
      createTableStmt->selectStmt = selectStmt;
      *stmt                       = (Stmt *)createTableStmt;

      // 释放内存资源
      varArrayListDestroy(&attrInfos);
    }
    /* 1.4 如果create table没有指定列属性，直接使用select_stmt中的attr_infos */
    else {
      createTableStmt = (CreateTableStmt *)my_malloc0(sizeof(CreateTableStmt));
      if (createTableStmt == NULL) {
        varArrayListDestroy(&attrInfos);
        return GNCDB_MEM; /*malloc失败*/
      }
      /*未指定列属性、带select语句*/
      createTableStmt->type       = ST_CREATE_TABLE;
      createTableStmt->tableName  = PTR_MOVE((void **)&createTable->relationName);
      createTableStmt->attrInfos  = PTR_MOVE((void **)&attrInfos);
      createTableStmt->selectStmt = selectStmt;
      *stmt                       = (Stmt *)createTableStmt;
    }

  }
  /* 2.如果create语句没有带select语句，直接使用create_table中的attr_infos */
  else {

    /* 指定了列属性、不带select语句 */
    createTableStmt = CreateTableStmtCreate();
    if (createTableStmt == NULL) {
      return GNCDB_MEM; /*malloc失败*/
    }
    createTableStmt->tableName  = PTR_MOVE((void **)&createTable->relationName);
    createTableStmt->attrInfos  = PTR_MOVE((void **)&createTable->attrInfos);
    createTableStmt->selectStmt = NULL;
    *stmt                       = (Stmt *)createTableStmt;
  }

  return rc;
}

void CreateTableStmtDestroy(CreateTableStmt *createTableStmt)
{
  if (createTableStmt == NULL) {
    return;
  }
  if (createTableStmt->attrInfos != NULL) {
    varArrayListDestroy(&createTableStmt->attrInfos);
  }
  if (createTableStmt->tableName != NULL) {
    my_free(createTableStmt->tableName);
  }
  if (createTableStmt->selectStmt != NULL) {
    SelectStmtDestroy((SelectStmt *)createTableStmt->selectStmt);
  }
  my_free(createTableStmt);
}