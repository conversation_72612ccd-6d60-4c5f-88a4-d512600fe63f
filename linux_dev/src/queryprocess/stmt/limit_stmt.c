#include "limit_stmt.h"
#include "gncdbconstant.h"
#include "parse_defs.h"
int LimitStmtConstruct(LimitSqlNode *limitNode, LimitStmt **stmt)
{
  LimitStmt *limitStmt = (LimitStmt *)my_malloc0(sizeof(LimitStmt));
  if (limitStmt == NULL) {
    return GNCDB_MEM;
  }
  limitStmt->type   = ST_LIMIT;
  limitStmt->limit  = limitNode->limit;
  limitStmt->offset = limitNode->offset;
  *stmt             = limitStmt;

  return GNCDB_SUCCESS;
}

void LimitStmtDestroy(LimitStmt *limitStmt) { my_free(limitStmt); }
