#include "drop_index_stmt.h"
#include "gncdbconstant.h"
#include "parse_defs.h"
#include "stmt.h"
#include "utils.h"
int DropIndexStmtConstruct(SQLStageEvent *sqlEvent, Stmt **stmt)
{
  DropIndexStmt    *dropIndexStmt = NULL;
  DropIndexSqlNode *dropIndex     = NULL;

  dropIndex     = sqlEvent->sqlNode->dropIndex;
  dropIndexStmt = (DropIndexStmt *)my_malloc0(sizeof(DropIndexStmt));
  /*malloc失败*/
  if (dropIndexStmt == NULL) {
    return GNCDB_MEM;
  }
  dropIndexStmt->type         = ST_DROP_INDEX;
  dropIndexStmt->indexName    = PTR_MOVE((void**)&dropIndex->indexName); /*这个未定义,应该是用这个*/
  dropIndexStmt->relationName = PTR_MOVE((void**)&dropIndex->relationName);
  *stmt                       = (Stmt *)dropIndexStmt;

  return GNCDB_SUCCESS;
}

void DropIndexStmtDestroy(DropIndexStmt *dropIndexStmt)
{
  if (dropIndexStmt == NULL) {
    return;
  }
  if (dropIndexStmt->indexName != NULL) {
    my_free(dropIndexStmt->indexName);
  }
  if (dropIndexStmt->relationName != NULL) {
    my_free(dropIndexStmt->relationName);
  }
  my_free(dropIndexStmt);
}