cmake_minimum_required(VERSION 3.10)

# 设置可执行文件输出目录
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/build)
set(HOME ${CMAKE_SOURCE_DIR}/src/queryprocess)

# 添加目录下的所有源文件
aux_source_directory(${HOME}/parser  SRC_LIST)
aux_source_directory(${HOME}/expr  SRC_LIST)
aux_source_directory(${HOME}/stmt  SRC_LIST)
aux_source_directory(${HOME}/operator SRC_LIST)
aux_source_directory(${HOME}/executor SRC_LIST)
aux_source_directory(${HOME}/event SRC_LIST)
aux_source_directory(${HOME}/../optimizer SRC_LIST)
aux_source_directory(${HOME}/../optimizer/RBO SRC_LIST)
aux_source_directory(${HOME}/../optimizer/CBO SRC_LIST)

# 添加当前目录的所有源文件
aux_source_directory(. SRC_LIST)

# 添加库
add_library(gncdb_qp ${SRC_LIST})