#include "btreecursor.h"
#include "btreepage.h"
#include "btreetable.h"
#include "catalog.h"
#include "delete_physical_operator.h"
#include "exec_tuple.h"
#include "execute_stage.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "hash.h"
#include "index_scan_physical_operator.h"
#include "join_physical_operator.h"
#include "lockmanager.h"
#include "physical_operator.h"
#include "project_physical_operator.h"
#include "queryexecutor.h"
#include "pagepool.h"
#include "readwritelock.h"
#include "sql_event.h"
#include "table_scan_physical_operator.h"
#include "typedefine.h"
#include "update_physical_operator.h"
#include "hash.h"
#include "math.h"
#include <stdio.h>
#include <string.h>
#include "assert.h"
#include "utils.h"
#include "vararraylist.h"
#include "hashpage.h"

/**
 * @description: 计算哈希索引中的高位掩码，m需初始化为：m = bucketCount - 1;
 * @return {*}
 */
#define GET_MASK(m) \
  {                 \
    m |= m >> 1;    \
    m |= m >> 2;    \
    m |= m >> 4;    \
    m |= m >> 8;    \
    m |= m >> 16;   \
  }

extern CompOp swapOp(CompOp op);
extern int printHeader;

extern void wildcardFields(GNCDB* db,BtreeTable *table, char* alias , varArrayList* projects, bool isSingleTable);
int sortCondition(varArrayList* conditionArray, TableSchema* tableSchema);
int mostLeftMatch(TableSchema* tableSchema, varArrayList* conditionArray, varArrayList* minArray, varArrayList* maxArray);


/**
 * @brief  检查字段是否合法
 * @param  fieldExpr:
 * @param  fieldName:
 * @param  db:
 * @param  tableArray:
 * @return int:
 */
extern int fieldCheck(FieldExpr *fieldExpr, char *fieldName, GNCDB *db, varArrayList *tableArray);

extern int valueCheck(ValueExpr *valueExpr, void *value, GNCDB *db, FieldExpr *fieldExpr);

CompOp convertOp(Predicate predicate)
{
  switch (predicate) {
    case EQUAL: {
      return CMPOP_EQUAL_TO;
    }
    case GREATER_THAN: {
      return CMPOP_GREAT_THAN;
    }
    case GREATER_THAN_OR_EQUAL: {
      return CMPOP_GREAT_EQUAL;
    }
    case NOT_EQUAL: {
      return CMPOP_NOT_EQUAL;
    }
    case LESS_THAN: {
      return CMPOP_LESS_THAN;
    }
    case LESS_THAN_OR_EQUAL: {
      return CMPOP_LESS_EQUAL;
    }
    default: return CMPOP_INVALID;
  }
}

extern bool isField(varArrayList *tableArray, GNCDB *db, char *str);

ComparisonExpr *conditionToComparisonExpr(struct Condition *condition, GNCDB *db, varArrayList *tableArray)
{
  FieldExpr      *leftFieldExpr  = NULL;
  FieldExpr      *rightFieldExpr = NULL;
  ValueExpr      *rightValueExpr = NULL;
  ComparisonExpr *comparisonExpr = NULL;
  comparisonExpr                 = (ComparisonExpr *)my_malloc(sizeof(ComparisonExpr));
  if (comparisonExpr == NULL) {
    return NULL;
  }
  comparisonExpr->type  = ETG_COMPARISON;
  comparisonExpr->alias = NULL;
  comparisonExpr->name  = NULL;
  /* 1.处理lhs */
  leftFieldExpr = exprCreate(ETG_FIELD);
  if (leftFieldExpr == NULL) {
    my_free(comparisonExpr);
    return NULL;
  }
  if (fieldCheck(leftFieldExpr, condition->fieldName, db, tableArray) != GNCDB_SUCCESS) {
    my_free(comparisonExpr);
    my_free(leftFieldExpr);
    return NULL;
  }
  comparisonExpr->left = (Expression *)leftFieldExpr;

  /* 2.处理rhs */
  if (checkStringType(condition->value) == 3 && isField(tableArray, db, condition->value)) {
    /* 2.1 rhs是字段 */
    rightFieldExpr = exprCreate(ETG_FIELD);
    if (rightFieldExpr == NULL) {
      my_free(comparisonExpr);
      return NULL;
    }
    if (fieldCheck(rightFieldExpr, condition->value, db, tableArray) != GNCDB_SUCCESS) {
      my_free(comparisonExpr);
      my_free(rightFieldExpr);
      return NULL;
    }
    comparisonExpr->right = (Expression *)rightFieldExpr;
  } else {
    /* 2.2 rhs是常量值 */
    rightValueExpr = exprCreate(ETG_VALUE);
    if (rightValueExpr == NULL) {
      my_free(comparisonExpr);
      my_free(leftFieldExpr);
      return NULL;
    }
    rightValueExpr->value = condition->value;
    if (valueCheck(rightValueExpr, condition->value, db, leftFieldExpr) != GNCDB_SUCCESS) {
      my_free(comparisonExpr);
      my_free(leftFieldExpr);
      my_free(rightValueExpr);
      return NULL;
    }
    comparisonExpr->right = (Expression *)rightValueExpr;
  }
  /* 3.处理操作符 */
  comparisonExpr->comp = convertOp(condition->predicate);
  return comparisonExpr;
}

/**
 * @brief  将tableName对应的表中的条件下推到scan中
 * @param  cmpExprs:
 * @param  tableName:
 * @return varArrayList*:
 */
varArrayList *popComparisonExpr(varArrayList *cmpExprs, char *tableName)
{
  int             i              = 0;
  varArrayList   *result         = NULL;
  ComparisonExpr *comparisonExpr = NULL;
  if (cmpExprs == NULL || tableName == NULL) {
    return NULL;
  }
  result = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  if (result == NULL) {
    return NULL;
  }
  for (i = 0; i < cmpExprs->elementCount; ++i) {
    comparisonExpr = varArrayListGetPointer(cmpExprs, i);
    if (strcmp(((FieldExpr *)comparisonExpr->left)->tableName, tableName) == 0 &&
        comparisonExpr->right->type == ETG_VALUE) {
      varArrayListAddPointer(result, comparisonExpr);
      varArrayListRemoveByIndexPointer(cmpExprs, i);
      --i; /* 下标回退，以免跳过下一个元素 */
    }
  }
  return result;
}

bool tableIsExist(char *tableName, varArrayList *joinedTbl)
{
  int i = 0;
  for (i = 0; i < joinedTbl->elementCount; ++i) {
    if (strcmp(varArrayListGetPointer(joinedTbl, i), tableName) == 0) {
      return true;
    }
  }
  return false;
}

TableScanPhysicalOperator *popScan(int *scanTbl, char *tableName, varArrayList *joinedTbl, varArrayList *scans)
{
  int                        i         = 0;
  TableScanPhysicalOperator *tableScan = NULL;
  for (i = 0; i < scans->elementCount; ++i) {
    tableScan = (TableScanPhysicalOperator *)varArrayListGetPointer(scans, i);
    if (strcmp(tableScan->table->tableName, tableName) == 0) {
      scanTbl[i] = 1;
      // varArrayListRemoveByIndexPointer(scans, i);
      varArrayListAddPointer(joinedTbl, tableName);
      return tableScan;
    }
  }
  return NULL;
}

int pushJoinConds1(ComparisonExpr *cmpExpr, PhysicalOperator *oper)
{
  NestedLoopJoinPhysicalOperator *join      = NULL;
  TableScanPhysicalOperator      *tblScan   = NULL;
  IndexScanPhysicalOperator      *idxScan   = NULL;
  PhysicalOperator               *joinLeft  = NULL;
  PhysicalOperator               *joinRight = NULL;
  Expression                     *lhs       = NULL;
  Expression                     *rhs       = NULL;
  int                             leftRes   = 0;
  int                             rightRes  = 0;
  Expression                     *tmpExpr   = NULL;

  lhs = cmpExpr->left;
  rhs = cmpExpr->right;

  if (oper->type == PO_TABLE_SCAN) {
    tblScan = (TableScanPhysicalOperator *)oper;
    if (strcmp(tblScan->table->tableName, ((FieldExpr *)lhs)->tableName) == 0) {
      return 1;
    } else if (strcmp(tblScan->table->tableName, ((FieldExpr *)rhs)->tableName) == 0) {
      return 2;
    } else {
      return 0;
    }
  } else if (oper->type == PO_INDEX_SCAN) {
    idxScan = (IndexScanPhysicalOperator *)oper;
    if (strcmp(idxScan->table->tableName, ((FieldExpr *)lhs)->tableName) == 0) {
      return 1;
    } else if (strcmp(idxScan->table->tableName, ((FieldExpr *)rhs)->tableName) == 0) {
      return 2;
    } else {
      return 0;
    }
  } else if (oper->type == PO_NESTED_LOOP_JOIN) { /* 只用了hash join */
    join      = (NestedLoopJoinPhysicalOperator *)oper;
    joinLeft  = varArrayListGetPointer(join->children, 0);
    joinRight = varArrayListGetPointer(join->children, 1);
    leftRes   = pushJoinConds1(cmpExpr, joinLeft);
    /* 条件已经下推到左子节点 */
    if (leftRes == 3) {
      return 3;
    }
    rightRes = pushJoinConds1(cmpExpr, joinRight);
    /* 条件已经下推到右子节点 */
    if (rightRes == 3) {
      return 3;
    }

    if (leftRes == 0 || rightRes == 0) {
      return leftRes + rightRes;
    }
    /* 左子节点匹配到条件的右边，需要进行调整 */
    if (leftRes == 2) {
      /* 将左右两边的条件变换位置 */
      tmpExpr = lhs;
      lhs     = rhs;
      rhs     = tmpExpr;
      /* 将比较操作符变换位置 */
      cmpExpr->comp = swapOp(cmpExpr->comp);
    }
    varArrayListAddPointer(join->predicates, cmpExpr);
    return 3; /* 匹配到条件 */
  }
  return false;
}

/// <summary>
/// 加表
/// </summary>
/// <param name="db"></param>
/// <param name="tableName">表名</param>
/// <param name="primaryKeyArray">主键</param>
/// <param name="tableSchema"></param>
/// <param name="ts"></param>
/// <returns></returns>
int executorCreateTable(struct GNCDB *db, char *tableName, struct TableSchema *tableSchema,
    varArrayList *primaryKeyIndex, varArrayList *primaryKeyType, varArrayList *primaryKeyOffset,
    varArrayList *primaryKeyVarcharLen, struct Transaction *tx)
{
  int         rc        = 0;
  BtreePage  *btreePage = NULL;
  BtreeTable *table     = NULL;
  if (db == NULL || tableSchema == NULL || primaryKeyIndex == NULL || primaryKeyType == NULL ||
      primaryKeyOffset == NULL || primaryKeyVarcharLen == NULL) {
    return GNCDB_PARAM_INVALID;
  }
  LOG(LOG_TRACE, "SLOCKing:%s", "tableSchemaMap");
  WriteLock(&(db->catalog->tableSchemaLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tableSchemaMap");
  rc = hashMapPut(db->catalog->tableSchemaMap, tableName, tableSchema);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tableSchemaMap");
  WriteUnLock(&(db->catalog->tableSchemaLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tableSchemaMap");

  LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
  WriteLock(&(db->catalog->keyIndexLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
  hashMapPut(db->catalog->tablePrimaryKeyIndexMap, tableName, primaryKeyIndex);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
  WriteUnLock(&(db->catalog->keyIndexLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");

  LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyOffsetMap");
  WriteLock(&(db->catalog->keyOffsetLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyOffsetMap");
  hashMapPut(db->catalog->tablePrimaryKeyOffsetMap, tableName, primaryKeyOffset);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyOffsetMap");
  WriteUnLock(&(db->catalog->keyOffsetLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyOffsetMap");

  LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
  WriteLock(&(db->catalog->keyVarcharLenLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyVarcharLenMap");
  hashMapPut(db->catalog->tablePrimaryKeyVarcharLenMap, tableName, primaryKeyVarcharLen);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
  WriteUnLock(&(db->catalog->keyVarcharLenLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyVarcharLenMap");

  /* 保存主键类型 */
  LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
  WriteLock(&(db->catalog->keyTypeLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
  hashMapPut(db->catalog->tablePrimaryKeyTypeMap, tableName, primaryKeyType);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
  WriteUnLock(&(db->catalog->keyTypeLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");
  /* 创建一个Btree页 */
  rc = pagePoolCreateBtreePage(&btreePage, LEAF_PAGE, tableName, db, tx);
  if (rc != GNCDB_SUCCESS) {
    WriteLock(&(db->catalog->tableSchemaLatch));
    hashMapRemove(db->catalog->tableSchemaMap, tableName);
    WriteUnLock(&(db->catalog->tableSchemaLatch));

    WriteLock(&(db->catalog->keyIndexLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyIndexMap, tableName);
    WriteUnLock(&(db->catalog->keyIndexLatch));

    WriteLock(&(db->catalog->keyOffsetLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyOffsetMap, tableName);
    WriteUnLock(&(db->catalog->keyOffsetLatch));

    WriteLock(&(db->catalog->keyVarcharLenLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyVarcharLenMap, tableName);
    WriteUnLock(&(db->catalog->keyVarcharLenLatch));

    /* 保存主键类型 */
    WriteLock(&(db->catalog->keyTypeLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyTypeMap, tableName);
    WriteUnLock(&(db->catalog->keyTypeLatch));
    return rc;
  }

	/* 获取该页状态并将该页设置为脏页 */
	// pageStatus = getPageStatus(db->pagePool, btreePage->page.id);
	// if (pageStatus == NULL) {
	// 	return GNCDB_SPACE_LACK;
	// }
	rc = setPageStatusDirty(db->pagePool, btreePage->page.id, NULL);
	if (rc != GNCDB_SUCCESS) {
		btreePageDestroy(&btreePage);
		WriteLock(&(db->catalog->tableSchemaLatch));
		hashMapRemove(db->catalog->tableSchemaMap, tableName);
		WriteUnLock(&(db->catalog->tableSchemaLatch));

    WriteLock(&(db->catalog->keyIndexLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyIndexMap, tableName);
    WriteUnLock(&(db->catalog->keyIndexLatch));

    WriteLock(&(db->catalog->keyOffsetLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyOffsetMap, tableName);
    WriteUnLock(&(db->catalog->keyOffsetLatch));

    WriteLock(&(db->catalog->keyVarcharLenLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyVarcharLenMap, tableName);
    WriteUnLock(&(db->catalog->keyVarcharLenLatch));

    WriteLock(&(db->catalog->keyTypeLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyTypeMap, tableName);
    WriteUnLock(&(db->catalog->keyTypeLatch));
    return rc;
  }

  // * 新创建的btreePage以freepage的形式加入oldPageMap中，因为相当于从空白页变成了btreePage
  rc = produceOldPageData(db, (Page *)btreePage, FREE_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    WriteLock(&(db->catalog->tableSchemaLatch));
    hashMapRemove(db->catalog->tableSchemaMap, tableName);
    WriteUnLock(&(db->catalog->tableSchemaLatch));

    WriteLock(&(db->catalog->keyIndexLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyIndexMap, tableName);
    WriteUnLock(&(db->catalog->keyIndexLatch));

    WriteLock(&(db->catalog->keyOffsetLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyOffsetMap, tableName);
    WriteUnLock(&(db->catalog->keyOffsetLatch));

    WriteLock(&(db->catalog->keyVarcharLenLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyVarcharLenMap, tableName);
    WriteUnLock(&(db->catalog->keyVarcharLenLatch));

    WriteLock(&(db->catalog->keyTypeLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyTypeMap, tableName);
    WriteUnLock(&(db->catalog->keyTypeLatch));
    return rc;
  }

  /* 将新创建的页添加到newCreatedPageSet中 */
  rc = addIntoNewCreatedPageSet(tx, (Page *)btreePage);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  table = btreeTableConstruct(tableName, btreePage->page.id, tableSchema);
  rc    = catalogAddTable(tableName, table, tx, db);
  // setPageStatusPinDown(db->pagePool,btreePage->page.id);
  if (rc != GNCDB_SUCCESS) {
    btreeTableDestroy(&table);
    WriteLock(&(db->catalog->tableSchemaLatch));
    hashMapRemove(db->catalog->tableSchemaMap, tableName);
    WriteUnLock(&(db->catalog->tableSchemaLatch));

    WriteLock(&(db->catalog->keyIndexLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyIndexMap, tableName);
    WriteUnLock(&(db->catalog->keyIndexLatch));

    WriteLock(&(db->catalog->keyOffsetLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyOffsetMap, tableName);
    WriteUnLock(&(db->catalog->keyOffsetLatch));

    WriteLock(&(db->catalog->keyVarcharLenLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyVarcharLenMap, tableName);
    WriteUnLock(&(db->catalog->keyVarcharLenLatch));

    WriteLock(&(db->catalog->keyTypeLatch));
    hashMapRemove(db->catalog->tablePrimaryKeyTypeMap, tableName);
    WriteUnLock(&(db->catalog->keyTypeLatch));
  }
  return rc;
}

/// <summary>
/// 删表
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <returns></returns>
int executorDropeTable(struct GNCDB *db, char *tableName, struct Transaction *tx)
{
  int         rc    = 0;
  BtreeTable *table = NULL;

  rc = catalogGetTable(db->catalog, &table, tableName);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_SUCCESS;
  }
  rc = btreeTableDropTable(table, db, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = catalogDeleteTable(tableName, tx, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  btreeTableDestroy(&table);
  return GNCDB_SUCCESS;
}

/// <summary>
/// 判断某表中是否有该属性
/// </summary>
/// <param name="tableSchema"></param>
/// <param name="fieldName"></param>
/// <returns></returns>
bool tableSchemaExistsFileName(struct TableSchema *tableSchema, char *fieldName)
{
  int     i      = 0;
  Column *column = NULL;
  int     flag   = 0;

  for (i = 0; i < tableSchema->columnList->elementCount; ++i) {
    column = varArrayListGetPointer(tableSchema->columnList, i);
    flag   = strcmp(fieldName, column->fieldName);
    if (!flag) {
      return true;
    }
  }
  return false;
}

/// <summary>
/// 判断一个属性在一个表array中是否出现，出现返回表名，不出现返回NULL
/// </summary>
/// <param name="db"></param>
/// <param name="tableArray"></param>
/// <param name="fieldName"></param>
/// <returns></returns>
char *tableArrayExistsFileName(struct GNCDB *db, struct varArrayList *tableArray, char *fieldName)
{
  int          i           = 0;
  char        *tableName   = NULL;
  TableSchema *tableSchema = NULL;

  for (i = 0; i < tableArray->elementCount; ++i) {
    tableName   = varArrayListGetPointer(tableArray, i);
    tableSchema = getTableSchema(db->catalog, tableName);
    if (tableSchemaExistsFileName(tableSchema, fieldName) == true) {
      return tableName;
    }
  }
  return NULL;
}

/// <summary>
/// 插入一条记录
/// </summary>
/// <param name="db"></param>
/// <param name="affectedRows">记录执行成功的数量,在执行插入时只有0或1</param>
/// <param name="tableName"></param>
/// <param name="table"></param>
/// <param name="tableSchema"></param>
/// <param name="tuple"></param>
/// <param name="tid"></param>
/// <returns></returns>
int executorInsert(struct GNCDB *db, int *affectedRows, char *tableName, struct BtreeTable *table,
    struct TableSchema *tableSchema, BYTE *record, struct Transaction *tx)
{
  int rows = 0;
  int rc   = 0;

  rc = btreeTableInsertTuple(table, record, tableSchema, db, tx);
  if (rc != GNCDB_SUCCESS) {
    // return rc;
  } else {
    rows += 1;
  }

  if (affectedRows != NULL) {
    *affectedRows = rows;
  }
  return rc;
}

/// <summary>
/// 删除一条记录
/// </summary>
/// <param name="db"></param>
/// <param name="affectedRows">记录删除操作删除掉的记录数量</param>
/// <param name="tableName"></param>
/// <param name="conditionArray"></param>
/// <param name="tid"></param>
/// <returns></returns>
int executorDelete(
    struct GNCDB *db, int *affectedRows, char *tableName, struct varArrayList *conditionArray, struct Transaction *tx)
{
  int                        rc         = 0;
  int                        i          = 0;
  varArrayList              *cmpExprs   = NULL;
  varArrayList              *tableArray = NULL;
  TableScanPhysicalOperator *scan       = NULL;
  SQLStageEvent             *sqlEvent   = NULL;
  DeletePhysicalOperator    *deleteOper = NULL;

  sqlEvent                   = SQLStageEventCreate();
  sqlEvent->db               = db;
  sqlEvent->txn              = tx;
  sqlEvent->callback         = NULL;
  sqlEvent->res              = (SqlResult *)my_malloc(sizeof(SqlResult));
  sqlEvent->res->fieldNames  = NULL;
  sqlEvent->res->fieldValues = NULL;
  sqlEvent->res->fieldCount  = 0;

  tableArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (tableArray == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_SPACE_LACK;
  }
  varArrayListAddPointer(tableArray, tableName);
  /* 1 处理conditionArray */
  cmpExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  if (cmpExprs == NULL) {
    varArrayListDestroy(&tableArray);
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_SPACE_LACK;
  }
  /* 1 处理conditionArray */
  for (i = 0; i < conditionArray->elementCount; ++i) {
    struct Condition *condition      = (struct Condition *)varArrayListGetPointer(conditionArray, i);
    ComparisonExpr   *comparisonExpr = conditionToComparisonExpr(condition, db, tableArray);
    if (comparisonExpr == NULL) {
      varArrayListDestroy(&tableArray);
      SQLStageEventDestroy(&sqlEvent);
      varArrayListDestroy(&cmpExprs);
      return GNCDB_CONDITION_INVALID;
    }
    varArrayListAddPointer(cmpExprs, comparisonExpr);
  }

	/* 2.处理from从句 */
	{
		varArrayList* cmpExprsTmp = popComparisonExpr(cmpExprs, tableName);
		if(cmpExprsTmp == NULL){
			varArrayListDestroy(&tableArray);
			SQLStageEventDestroy(&sqlEvent);
			varArrayListDestroy(&cmpExprs);
			return GNCDB_MEM;
		}
		scan = TableScanPhysOperCreate();
		catalogGetTable(db->catalog, &scan->table, tableName);
		scan->predicates = cmpExprsTmp;
		scan->startKeyValue = NULL;
		scan->txn = tx;
		// scan->tuple.tableName = tableName;
		// scan->tuple.tableSchema = getTableSchema(db->catalog, tableName);
		// scan->cursor =  btreeCursorConstruct(scan->table->tableName, db, NULL, tx);
		// if(scan->cursor != NULL){
		// 	setPageStatusPinDown(db->pagePool, scan->cursor->currentLeafPageId);
		// }
	}

  /* 3.构建delete的物理计划 */
  deleteOper = (DeletePhysicalOperator *)my_malloc(sizeof(DeletePhysicalOperator));
  DeletePhysOperInit(deleteOper, PO_DELETE);
  deleteOper->table = scan->table;
  deleteOper->type  = PO_DELETE;

  if (scan == NULL) {
    varArrayListDestroy(&tableArray);
    SQLStageEventDestroy(&sqlEvent);
    varArrayListDestroy(&cmpExprs);
    return GNCDB_INTERNAL;
  }

  varArrayListAddPointer(deleteOper->children, scan);

  /* 4.执行物理计划 */
  sqlEvent->plan = (PhysicalOperator *)deleteOper;
  rc             = ExecutePhysicalPlan(sqlEvent);
  *affectedRows  = sqlEvent->affectedRows;
  SQLStageEventDestroy(&sqlEvent);
  varArrayListDestroy(&cmpExprs);
  varArrayListDestroy(&tableArray);
  return rc;
}

/// <summary>
/// 更新记录
/// </summary>
/// <param name="db"></param>
/// <param name="affectedRows">记录此次操作会修改多少条tuple</param>
/// <param name="tableName"></param>
/// <param name="updateAttrArray"></param>
/// <param name="updateFieldArray"></param>
/// <param name="conditionArray"></param>
/// <param name="tid"></param>
/// <returns></returns>
int executorUpdate(struct GNCDB *db, int *affectedRows, char *tableName, struct varArrayList *updateAttrArray,
    struct varArrayList *updateFieldArray, struct varArrayList *conditionArray, struct Transaction *tx)
{
  int                        rc             = 0;
  int                        i              = 0;
  varArrayList              *cmpExprs       = NULL;
  varArrayList              *tableArray     = NULL;
  TableScanPhysicalOperator *scan           = NULL;
  UpdatePhysicalOperator    *UpdatePhysOper = NULL;
  SQLStageEvent             *sqlEvent       = NULL;

  sqlEvent                   = SQLStageEventCreate();
  sqlEvent->db               = db;
  sqlEvent->txn              = tx;
  sqlEvent->callback         = NULL;
  sqlEvent->res              = (SqlResult *)my_malloc(sizeof(SqlResult));
  sqlEvent->res->fieldNames  = NULL;
  sqlEvent->res->fieldValues = NULL;
  sqlEvent->res->fieldCount  = 0;

  tableArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (tableArray == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    varArrayListDestroy(&updateAttrArray);
    varArrayListDestroy(&updateFieldArray);
    return GNCDB_SPACE_LACK;
  }
  varArrayListAddPointer(tableArray, tableName);
  /* 1 处理conditionArray */
  cmpExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  if (cmpExprs == NULL) {
    varArrayListDestroy(&tableArray);
    SQLStageEventDestroy(&sqlEvent);
    varArrayListDestroy(&updateAttrArray);
    varArrayListDestroy(&updateFieldArray);
    return GNCDB_SPACE_LACK;
  }
  /* 1 处理conditionArray */
  for (i = 0; i < conditionArray->elementCount; ++i) {
    struct Condition *condition      = (struct Condition *)varArrayListGetPointer(conditionArray, i);
    ComparisonExpr   *comparisonExpr = conditionToComparisonExpr(condition, db, tableArray);
    if (comparisonExpr == NULL) {
      varArrayListDestroy(&tableArray);
      varArrayListDestroy(&cmpExprs);
      SQLStageEventDestroy(&sqlEvent);
      varArrayListDestroy(&updateAttrArray);
      varArrayListDestroy(&updateFieldArray);
      return GNCDB_CONDITION_INVALID;
    }
    varArrayListAddPointer(cmpExprs, comparisonExpr);
  }

	/* 2.处理from从句 */
	{
		varArrayList* cmpExprsTmp = popComparisonExpr(cmpExprs, tableName);
		if(cmpExprsTmp == NULL){
			varArrayListDestroy(&tableArray);
			varArrayListDestroy(&cmpExprs);
			SQLStageEventDestroy(&sqlEvent);
			varArrayListDestroy(&updateAttrArray);
			varArrayListDestroy(&updateFieldArray);
			return GNCDB_MEM;
		}
		scan = TableScanPhysOperCreate();
		catalogGetTable(db->catalog, &scan->table, tableName);
		scan->predicates = cmpExprsTmp;
		scan->startKeyValue = NULL;
		scan->txn = tx;
		// scan->tuple.tableName = tableName;
		// // scan->tuple.tableSchema = hashMapGet(db->catalog->tableSchemaMap, tableName);
		// scan->tuple.tableSchema = getTableSchema(db->catalog, tableName);
		// scan->cursor =  btreeCursorConstruct(scan->table->tableName, db, NULL, tx);
		// if(scan->cursor != NULL)
		// {
		// 	setPageStatusPinDown(db->pagePool, scan->cursor->currentLeafPageId);
		// }
	}

  /* 3.构建update的物理计划 */
  UpdatePhysOper = (UpdatePhysicalOperator *)my_malloc(sizeof(UpdatePhysicalOperator));
  UpdatePhysOperInit(UpdatePhysOper);
  UpdatePhysOper->type             = PO_UPDATE;
  UpdatePhysOper->updateValues     = updateFieldArray;
  UpdatePhysOper->updateFieldNames = updateAttrArray;

  if (scan == NULL) {
    varArrayListDestroy(&tableArray);
    varArrayListDestroy(&cmpExprs);
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_INTERNAL;
  }

  varArrayListAddPointer(UpdatePhysOper->children, scan);

  /* 4.执行物理计划 */
  sqlEvent->plan = (PhysicalOperator *)UpdatePhysOper;
  rc             = ExecutePhysicalPlan(sqlEvent);
  *affectedRows  = sqlEvent->affectedRows;
  varArrayListDestroy(&cmpExprs);
  varArrayListDestroy(&tableArray);
  SQLStageEventDestroy(&sqlEvent);
  return rc;
}

/// <summary>
/// 解析条件中数据并进行转化 传入的参数均为字符串,解析属性以及类型,将数据转化为对应的数据类型
/// </summary>
/// <param name="tableSchema"></param>
/// <param name="condition"></param>
/// <returns></returns>
int exectorParseCondition(struct TableSchema *tableSchema, struct Condition *condition)
{
  int     index   = 0;
  Column *column  = NULL;
  int    *pInt    = NULL;
  double *pDouble = NULL;

  index = tableSchemaGetIndex(tableSchema, condition->fieldName);
  if (index == GNCDB_FIELD_NOT_EXIST) {
    return GNCDB_FIELD_NOT_EXIST;
  }
  column = varArrayListGetPointer(tableSchema->columnList, index);
  if (column->fieldType == FIELDTYPE_INTEGER) {
    pInt = my_malloc(sizeof(int));
    if (pInt == NULL) {
      return GNCDB_SPACE_LACK;
    }
    *pInt = atoi(condition->value);
    my_free(condition->value);
    condition->value = pInt;
  } else if (column->fieldType == FIELDTYPE_REAL) {
    pDouble = my_malloc(sizeof(double));
    if (pDouble == NULL) {
      return GNCDB_SPACE_LACK;
    }
    *pDouble = atof(condition->value);
    my_free(condition->value);
    condition->value = pDouble;
  } else if (column->fieldType == FIELDTYPE_VARCHAR) {
    //        pStr = my_malloc(sizeof(double));
    //        if (pDouble == NULL)
    //        {
    //            return GNCDB_SPACE_LACK;
    //        }
    //        strcpy(pStr, condition->value);
    //        my_free(condition->value);
    //        condition->value = pStr;
  } else if (column->fieldType == FIELDTYPE_BLOB) {
  }
  return GNCDB_SUCCESS;
}

/// <summary>
/// 对condition按照tableSchema的属性顺序进行排序
/// </summary>
/// <param name="conditionArray"></param>
/// <param name="tableSchema"></param>
/// <returns></returns>
int sortCondition(varArrayList *conditionArray, TableSchema *tableSchema)
{
  int rc = 0;
  // Condition* condition = NULL;
  Condition *conditionFirst   = NULL;
  Condition *conditionSecond  = NULL;
  int        fieldIndexFirst  = 0;
  int        fieldIndexSecond = 0;
  int        i                = 0;
  int        j                = 0;
  if (conditionArray == NULL || conditionArray->elementCount == 0) {
    return GNCDB_SUCCESS;
  }
  for (i = 0; i < conditionArray->elementCount - 1; i++) {

    for (j = 0; j < conditionArray->elementCount - 1 - i; j++) {
      conditionFirst = (Condition *)varArrayListGetPointer(conditionArray, j);
      if (conditionFirst == NULL) {
        return GNCDB_NOT_FOUND;
      }
      fieldIndexFirst = tableSchemaGetIndex(tableSchema, conditionFirst->fieldName);
      if (fieldIndexFirst < 0) {
        return GNCDB_NOT_FOUND;
      }

      conditionSecond = (Condition *)varArrayListGetPointer(conditionArray, j + 1);
      if (conditionSecond == NULL) {
        return GNCDB_NOT_FOUND;
      }
      fieldIndexSecond = tableSchemaGetIndex(tableSchema, conditionSecond->fieldName);
      if (fieldIndexSecond < 0) {
        return GNCDB_NOT_FOUND;
      }

      /* 如果fieldIndexFirst>fieldIndexSecond，则需要交换两个condition */
      if (fieldIndexFirst > fieldIndexSecond) {
        // condition = conditionFirst;
        rc = varArrayListSetByIndexPointer(conditionArray, j, conditionSecond);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        rc = varArrayListSetByIndexPointer(conditionArray, j + 1, conditionFirst);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
    }
  }

  return GNCDB_SUCCESS;
}

/// <summary>
/// 最左匹配原则寻找扫描范围
/// </summary>
/// <param name="tableSchema"></param>
/// <param name="conditionArray"></param>
/// <param name="minArray">保存扫描起始位置的array</param>
/// <param name="maxArray">保存最终位置的array</param>
/// <returns></returns>
int mostLeftMatch(
    TableSchema *tableSchema, varArrayList *conditionArray, varArrayList *minArray, varArrayList *maxArray)
{
	int rc = 0;
	int i = 0;
	int j = 0;
	int location = 0;
	Column* column = NULL;
	HashMap* minMap = NULL;
	HashMap* maxMap = NULL;
	Condition* condition = NULL;
	void* keyValue = NULL;
	bool exist = false;
	// void* existValue = NULL;
	int accordFlag = 0;

	/* 创建两个保存上下界的哈希表 */
	minMap = hashMapCreate(STRKEY, 0, NULL);
	if (minMap == NULL)
	{
		return GNCDB_SPACE_LACK;
	}
	maxMap = hashMapCreate(STRKEY, 0, NULL);
	if (maxMap == NULL)
	{
		hashMapDestroy(&minMap);
		return GNCDB_SPACE_LACK;
	}
	/* 遍历tableschema寻找主键 */
	for (; i < tableSchema->columnNum; ++i)
	{
		keyValue = NULL;
		column = varArrayListGetPointer(tableSchema->columnList, i);
		if (column == NULL)
		{
			hashMapDestroy(&minMap);
			hashMapDestroy(&maxMap);
			return GNCDB_NOT_FOUND;
		}
		/* 如果该属性为主键 */
		if (column->columnConstraint->isPrimaryKey)
		{
			for (j = location; j < conditionArray->elementCount; ++j)
			{
				condition = varArrayListGetPointer(conditionArray, j);
				if(condition == NULL)
				{
					hashMapDestroy(&minMap);
					hashMapDestroy(&maxMap);
					return GNCDB_NOT_FOUND;
				}
				/* 条件是主键值属性 */
				if (strcmp(column->fieldName, condition->fieldName) == 0)
				{
					keyValue = condition->value;
					location = j + 1;
					if (condition->predicate == EQUAL)
					{
						rc = hashMapPut(minMap, column->fieldName, condition->value);
						if (rc != GNCDB_SUCCESS)
						{
							hashMapDestroy(&minMap);
							hashMapDestroy(&maxMap);
							return rc;
						}
						rc = hashMapPut(maxMap, column->fieldName, condition->value);
						if (rc != GNCDB_SUCCESS)
						{
							hashMapDestroy(&minMap);
							hashMapDestroy(&maxMap);
							return rc;
						}
						/* 如果某个主键出现了等于,那么该主键不需要寻找其他的条件 */
						break;
					}
					else if (condition->predicate == GREATER_THAN || condition->predicate == GREATER_THAN_OR_EQUAL)
					{
						/* 判断该主键是否出现过,若是出现需要判断两个值之后更新新的界限 */
						exist = hashMapExists(minMap, column->fieldName);
						if (exist)
						{
							/* 已经存在数据,判断是否要更新数据 */
							// existValue = hashMapGet(minMap, column->fieldName);
							// accordFlag = internalTupleCompareCertainFieldValue(existValue, condition->value, column->fieldType, condition->predicate);
							if (accordFlag)
							{
								/* 说明旧的值比新的值大,不需要更新 */
								continue;
							}
						}
						rc = hashMapPut(minMap, column->fieldName, condition->value);
						if (rc != GNCDB_SUCCESS)
						{
							hashMapDestroy(&minMap);
							hashMapDestroy(&maxMap);
							return rc;
						}
					}
					else if (condition->predicate == LESS_THAN || condition->predicate == LESS_THAN_OR_EQUAL)
					{
						/* 判断该主键是否出现过,若是出现需要判断两个值之后更新新的界限 */
						exist = hashMapExists(maxMap, column->fieldName);
						if (exist)
						{
							/* 已经存在数据,判断是否要更新数据 */
							// existValue = hashMapGet(maxMap, column->fieldName);
							// accordFlag = internalTupleCompareCertainFieldValue(existValue, condition->value, column->fieldType, condition->predicate);
							if (accordFlag)
							{
								/* 说明旧的值比新的值小,不需要更新 */
								continue;
							}
						}
						rc = hashMapPut(maxMap, column->fieldName, condition->value);
						if (rc != GNCDB_SUCCESS)
						{
							hashMapDestroy(&minMap);
							hashMapDestroy(&maxMap);
							return rc;
						}
					}
					else if (condition->predicate == NOT_EQUAL)
					{
						/* 如果在某个主键属性出现不等,那么需要对全表进行扫描 */
						keyValue = NULL;
						break;
					}
				}
				
			}
			if (keyValue == NULL)
			{
				break;
			}
		}
	}
	/* 此时已经将所有关于主键的条件遍历完毕,先拿取最小值 */
	for (i = 0; i < tableSchema->columnNum; ++i)
	{
		column = varArrayListGetPointer(tableSchema->columnList, i);
		if (column == NULL)
		{
			hashMapDestroy(&minMap);
			hashMapDestroy(&maxMap);
			return GNCDB_NOT_FOUND;
		}
		if (column->columnConstraint->isPrimaryKey)
		{
			keyValue = hashMapGet(minMap, column->fieldName);
			if (keyValue == NULL)
			{
				break;
			}
			rc = varArrayListAddPointer(minArray, keyValue);
			if (rc != GNCDB_SUCCESS)
			{
				hashMapDestroy(&minMap);
				hashMapDestroy(&maxMap);
				return rc;
			}
		}
	}
	/* 拿取最大值 */
	for (i = 0; i < tableSchema->columnNum; ++i)
	{
		column = varArrayListGetPointer(tableSchema->columnList, i);
		if (column == NULL)
		{
			hashMapDestroy(&minMap);
			hashMapDestroy(&maxMap);
			return GNCDB_NOT_FOUND;
		}
		if (column->columnConstraint->isPrimaryKey)
		{
			keyValue = hashMapGet(maxMap, column->fieldName);
			if (keyValue == NULL)
			{
				break;
			}
			rc = varArrayListAddPointer(maxArray, keyValue);
			if (rc != GNCDB_SUCCESS)
			{
				hashMapDestroy(&minMap);
				hashMapDestroy(&maxMap);
				return rc;
			}
		}
	}

  hashMapDestroy(&minMap);
  hashMapDestroy(&maxMap);
  return GNCDB_SUCCESS;
}

varArrayList *primarySelectCheck(
    struct GNCDB *db, struct TableSchema *tableSchema, char *tableName, struct varArrayList *conditionArray)
{
  varArrayList *primaryArray  = NULL;
  varArrayList *keyValueArray = NULL;
  int           i             = 0;
  int          *index         = NULL;
  Column       *column        = NULL;
  Condition    *condition     = NULL;
  primaryArray                = getPrimaryIndexArray(db->catalog, tableName);
  keyValueArray               = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (keyValueArray == NULL) {
    return NULL;
  }
  if (primaryArray->elementCount != conditionArray->elementCount) {
    varArrayListDestroy(&keyValueArray);
    return NULL;
  }
  for (i = 0; i < conditionArray->elementCount; ++i) {
    condition = varArrayListGetPointer(conditionArray, i);
    if (condition == NULL) {
      varArrayListDestroy(&keyValueArray);
      return NULL;
    }
    if (condition->predicate != EQUAL) {
      varArrayListDestroy(&keyValueArray);
      return NULL;
    }
    index = varArrayListGet(primaryArray, i);
    if (index == NULL) {
      varArrayListDestroy(&keyValueArray);
      return NULL;
    }
    column = varArrayListGetPointer(tableSchema->columnList, *index);
    if (column == NULL) {
      varArrayListDestroy(&keyValueArray);
      return NULL;
    }
    if (column->columnConstraint->isPrimaryKey != 1 || strcmp(column->fieldName, condition->fieldName)) {
      varArrayListDestroy(&keyValueArray);
      return NULL;
    }
    varArrayListAddPointer(keyValueArray, condition->value);
  }

  return keyValueArray;
}

/// <summary>
/// 查询
/// </summary>
/// <param name="db"></param>
/// <param name="callback"></param>
/// <param name="tableArray"></param>
/// <param name="columnNameArray"></param>
/// <param name="conditionArray"></param>
/// <param name="tid"></param>
/// <returns></returns>
int executorSelect(struct GNCDB *db, CallBack2 callback, int *affectedRows, void *data, struct varArrayList *tableArray,
    struct varArrayList *columnNameArray, struct varArrayList *conditionArray, struct Transaction *tx)
{
  // TODO: 及时清理释放内存
  int                             rc              = 0;
  int                             i               = 0;
  varArrayList                   *cmpExprs        = NULL;
  varArrayList                   *scans           = NULL;
  varArrayList                   *joinedTbl       = NULL;
  HashMap                        *scanMap         = NULL;
  int                            *scanTbl         = NULL;
  SQLStageEvent                  *sqlEvent        = NULL;
  TableScanPhysicalOperator      *scan            = NULL;
  NestedLoopJoinPhysicalOperator *join            = NULL;
  NestedLoopJoinPhysicalOperator *joinTemp1       = NULL;
  NestedLoopJoinPhysicalOperator *joinTemp2       = NULL;
  char                           *leftTableName   = NULL;
  char                           *rightTableName  = NULL;
  TableScanPhysicalOperator      *leftScan        = NULL;
  TableScanPhysicalOperator      *rightScan       = NULL;
  bool                            isNeedReverse   = false;
  ProjectPhysicalOperator        *project         = NULL;
  Condition                      *condition       = NULL;
  ComparisonExpr                 *comparisonExpr  = NULL;
  char                           *tableName       = NULL;
  varArrayList                   *cmpExprsTmp     = NULL;
  PhysicalOperator               *leftNeedToJoin  = NULL;
  PhysicalOperator               *rightNeedToJoin = NULL;
  char                           *fieldName       = NULL;
  FieldExpr                      *fieldExpr       = NULL;
  Expression                     *temp            = NULL;
  BtreeTable                     *table           = NULL;
  bool                            isSingleTable   = true;

  printHeader                = true;
  sqlEvent                   = SQLStageEventCreate();
  sqlEvent->db               = db;
  sqlEvent->txn              = tx;
  sqlEvent->callback         = callback;
  sqlEvent->res              = (SqlResult *)my_malloc(sizeof(SqlResult));
  sqlEvent->res->fieldNames  = NULL;
  sqlEvent->res->fieldValues = NULL;
  sqlEvent->res->fieldCount  = 0;
  sqlEvent->data             = data;

  scanMap = hashMapCreate(STRKEY, 0, NULL);
  if (scanMap == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    return GNCDB_MEM;
  }
  joinedTbl = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (joinedTbl == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    hashMapDestroy(&scanMap);
    return GNCDB_MEM;
  }
  scans = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (scans == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    hashMapDestroy(&scanMap);
    varArrayListDestroy(&joinedTbl);
    return GNCDB_MEM;
  }
  cmpExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
  if (cmpExprs == NULL) {
    SQLStageEventDestroy(&sqlEvent);
    hashMapDestroy(&scanMap);
    varArrayListDestroy(&joinedTbl);
    varArrayListDestroy(&scans);
    return GNCDB_MEM;
  }
  /* 1 处理conditionArray */
  for (i = 0; i < conditionArray->elementCount; ++i) {
    condition      = (struct Condition *)varArrayListGetPointer(conditionArray, i);
    comparisonExpr = conditionToComparisonExpr(condition, db, tableArray);
    if (comparisonExpr == NULL) {
      SQLStageEventDestroy(&sqlEvent);
      hashMapDestroy(&scanMap);
      varArrayListDestroy(&joinedTbl);
      varArrayListDestroy(&scans);
      varArrayListDestroy(&cmpExprs);
      return GNCDB_CONDITION_INVALID;
    }
    varArrayListAddPointer(cmpExprs, comparisonExpr);
  }

	scanTbl = (int*)my_malloc(sizeof(int) * tableArray->elementCount);
	/* 2.处理from从句 */
	/* 2.1 构建scan算子 */
	for(i = 0; i < tableArray->elementCount; ++i){
		tableName = (char*)varArrayListGetPointer(tableArray, i);
		cmpExprsTmp = popComparisonExpr(cmpExprs, tableName);
		if(cmpExprsTmp == NULL){
			SQLStageEventDestroy(&sqlEvent);
			hashMapDestroy(&scanMap);
			varArrayListDestroy(&joinedTbl);
			varArrayListDestroy(&scans);
			my_free(scanTbl);
			varArrayListDestroy(&cmpExprs);
			return GNCDB_MEM;
		}
		scan = TableScanPhysOperCreate();
		catalogGetTable(db->catalog, &scan->table, tableName);
		scan->predicates = cmpExprsTmp;
		scan->startKeyValue = NULL;
		scan->txn = tx;
		// scan->tuple.tableName = tableName;
		// scan->tuple.tableSchema = getTableSchema(db->catalog, tableName);
		// scan->cursor =  btreeCursorConstruct(scan->table->tableName, db, NULL, tx);
		// if(scan->cursor != NULL)
		// {
		// 	setPageStatusPinDown(db->pagePool, scan->cursor->currentLeafPageId);
		// }
		varArrayListAddPointer(scans, scan);
		hashMapPut(scanMap, tableName, scan);
		scanTbl[i] = -1;
	}

  /* 3.处理join */
  /* 3.1 如果存在join，生成第一层join */
  if (tableArray->elementCount > 1 && cmpExprs->elementCount > 0) {
    for (i = 0; i < cmpExprs->elementCount; ++i) {
      comparisonExpr = varArrayListGetPointer(cmpExprs, i);
      /* 剩下的应该全是连接条件 */
      assert(comparisonExpr->left->type == ETG_FIELD && comparisonExpr->right->type == ETG_FIELD);
      leftTableName  = ((FieldExpr *)comparisonExpr->left)->tableName;
      rightTableName = ((FieldExpr *)comparisonExpr->right)->tableName;
      leftScan       = hashMapGet(scanMap, leftTableName);
      rightScan      = hashMapGet(scanMap, rightTableName);

      /* 这里默认使用hash join */
      join = (NestedLoopJoinPhysicalOperator *)my_malloc(sizeof(NestedLoopJoinPhysicalOperator));
      NestedLoopJoinPhysOperInit(join, PO_NESTED_LOOP_JOIN);
      join->predicates = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      varArrayListAddPointer(join->predicates, comparisonExpr);
      varArrayListAddPointer(join->children, (leftScan));
      varArrayListAddPointer(join->children, (rightScan));

      // join->left = (PhysicalOperator*)leftScan;
      // join->right = (PhysicalOperator*)rightScan;

      varArrayListAddPointer(joinedTbl, leftTableName);
      varArrayListAddPointer(joinedTbl, rightTableName);
      scanTbl[varArrayListIndexOf(tableArray, leftTableName)]  = 1;
      scanTbl[varArrayListIndexOf(tableArray, rightTableName)] = 1;
      varArrayListRemoveByIndexPointer(cmpExprs, i);
      break;
    }
    /* 3.2 生成第2-n层join */
    while (cmpExprs->elementCount > 0) {
      comparisonExpr  = varArrayListGetPointer(cmpExprs, 0);
      leftNeedToJoin  = NULL;
      rightNeedToJoin = NULL;
      leftTableName   = ((FieldExpr *)comparisonExpr->left)->tableName;
      rightTableName  = ((FieldExpr *)comparisonExpr->right)->tableName;
      assert(comparisonExpr->left->type == ETG_FIELD && comparisonExpr->right->type == ETG_FIELD);

      if (!tableIsExist(((FieldExpr *)comparisonExpr->left)->tableName, joinedTbl)) {
        leftNeedToJoin = (PhysicalOperator *)popScan(scanTbl, leftTableName, joinedTbl, scans);
      }

      if (!tableIsExist(((FieldExpr *)comparisonExpr->right)->tableName, joinedTbl)) {
        rightNeedToJoin = (PhysicalOperator *)popScan(scanTbl, rightTableName, joinedTbl, scans);
        isNeedReverse   = true;
      }
      /* 左右都需要join */
      if (leftNeedToJoin != NULL && rightNeedToJoin != NULL) {
        joinTemp1 = (NestedLoopJoinPhysicalOperator *)my_malloc(sizeof(NestedLoopJoinPhysicalOperator));
        NestedLoopJoinPhysOperInit(joinTemp1, PO_NESTED_LOOP_JOIN);
        varArrayListAddPointer(joinTemp1->children, (leftNeedToJoin));
        varArrayListAddPointer(joinTemp1->children, (rightNeedToJoin));
        // joinTemp1->left = leftNeedToJoin;
        // joinTemp1->right = rightNeedToJoin;
        joinTemp1->predicates = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
        varArrayListAddPointer(joinTemp1->predicates, comparisonExpr);
        joinTemp2 = join;
        join      = (NestedLoopJoinPhysicalOperator *)my_malloc(sizeof(NestedLoopJoinPhysicalOperator));
        NestedLoopJoinPhysOperInit(join, PO_NESTED_LOOP_JOIN);
        varArrayListAddPointer(join->children, (joinTemp1));
        varArrayListAddPointer(join->children, (joinTemp2));
        // join->left = (PhysicalOperator*)joinTemp1;
        // join->right = (PhysicalOperator*)joinTemp2;
        join->predicates = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      } else if (leftNeedToJoin != NULL || rightNeedToJoin != NULL) {
        if (isNeedReverse) {
          /* swap lhs、rhs */
          temp                  = comparisonExpr->left;
          comparisonExpr->left  = comparisonExpr->right;
          comparisonExpr->right = temp;
          /* swap op */
          comparisonExpr->comp = swapOp(comparisonExpr->comp);
          assert(comparisonExpr->comp != -1);
          leftNeedToJoin  = rightNeedToJoin;
          rightNeedToJoin = NULL;
        }
        joinTemp1 = (NestedLoopJoinPhysicalOperator *)my_malloc(sizeof(NestedLoopJoinPhysicalOperator));
        NestedLoopJoinPhysOperInit(join, PO_NESTED_LOOP_JOIN);
        // joinTemp1->left = leftNeedToJoin;
        // joinTemp1->right = (PhysicalOperator*)join;
        varArrayListAddPointer(joinTemp1->children, leftNeedToJoin);
        varArrayListAddPointer(joinTemp1->children, (PhysicalOperator *)join);
        joinTemp1->predicates = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
        varArrayListAddPointer(joinTemp1->predicates, comparisonExpr);
        join = joinTemp1;
      } else {
        /* 此时cond需要push到join的predicates中 */
        pushJoinConds1(comparisonExpr, (PhysicalOperator *)join);
      }
      varArrayListRemoveByIndexPointer(cmpExprs, 0);
    }
  } else {
    join       = varArrayListGetPointer(scans, 0);
    scanTbl[0] = 1;
  }

  /* 连接剩余表 */
  for (i = 0; i < tableArray->elementCount; i++) {
    if (scanTbl[i] == -1) {
      joinTemp1 = (NestedLoopJoinPhysicalOperator *)my_malloc(sizeof(NestedLoopJoinPhysicalOperator));
      NestedLoopJoinPhysOperInit(joinTemp1, PO_NESTED_LOOP_JOIN);
      varArrayListAddPointer(joinTemp1->children, join);
      varArrayListAddPointer(joinTemp1->children, (PhysicalOperator *)varArrayListGetPointer(scans, i));

      // joinTemp1->left = (PhysicalOperator*)join;
      // joinTemp1->right = (PhysicalOperator*)varArrayListGetPointer(scans, i);
      joinTemp1->predicates = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      join                  = joinTemp1;
    }
  }

  /* 4.处理投影 */
  project = (ProjectPhysicalOperator *)my_malloc(sizeof(ProjectPhysicalOperator));
  ProjectPhysOperInit(project);
  isSingleTable = tableArray->elementCount == 1;
  if (columnNameArray->elementCount == 0) {
    for (i = 0; i < tableArray->elementCount; ++i) {
      tableName = (char *)varArrayListGetPointer(tableArray, i);
      if (strcmp(tableName, "schema") == 0) {
        table = db->catalog->schemaTable;
      } else if (strcmp(tableName, "master") == 0) {
        table = db->catalog->masterTable;
      } else {
        table = hashMapGet(db->catalog->tableMap, tableName);
      }
      wildcardFields(db, table, NULL, project->tuple->exprs, isSingleTable);
    }
  } else {
    for (i = 0; i < columnNameArray->elementCount; ++i) {
      fieldName = (char *)varArrayListGetPointer(columnNameArray, i);
      fieldExpr = exprCreate(ETG_FIELD);
      rc        = fieldCheck(fieldExpr, fieldName, db, tableArray);
      if (rc != GNCDB_SUCCESS) {
        SQLStageEventDestroy(&sqlEvent);
        hashMapDestroy(&scanMap);
        varArrayListDestroy(&joinedTbl);
        varArrayListDestroy(&scans);
        my_free(scanTbl);
        varArrayListDestroy(&cmpExprs);
        return rc;
      }
      varArrayListAddPointer(project->tuple->exprs, fieldExpr);
    }
  }
  project->projectedFields = PTR_MOVE((void **)&project->tuple->exprs);
  varArrayListAddPointer(project->children, join);

  /* 5.执行算子 */
  sqlEvent->plan = (PhysicalOperator *)project;
  rc             = ExecutePhysicalPlan(sqlEvent);
  hashMapDestroy(&scanMap);
  varArrayListDestroy(&joinedTbl);
  varArrayListDestroy(&scans);
  varArrayListDestroy(&cmpExprs);
  my_free(scanTbl);

  *affectedRows = sqlEvent->affectedRows;
  SQLStageEventDestroy(&sqlEvent);
  return rc;
}

/// <summary>
/// 设置keyValueArray对应的tuple的第column列Blob字段的内容
/// </summary>
/// <param name="btreeTable"></param>
/// <param name="column"></param>
/// <param name="conditionArray">获取对应元组的条件（将keyValue转换成了condition）</param>
/// <param name="buf">Blob数据缓存区</param>
/// <param name="size">Blob数据的大小</param>
/// <param name="tid">事务id</param>
/// <returns></returns>
int executorSetBlob(struct GNCDB *db, char *tableName, int columnNum, struct varArrayList *keyValueArray, BYTE *buf,
    int size, struct Transaction *tx)
{
  int           rc                  = 0;
  BtreeTable   *btreeTable          = NULL;
  BtreePage    *btreePage           = NULL;
  TableSchema  *tableSchema         = NULL;
  varArrayList *overflowPageIdArray = NULL;
  // Tuple* tuple = NULL;
  BYTE *leafRecord         = NULL;
  int  *tempOverflowPageId = NULL;
  // BlobField* field = NULL;
  Column *blobColumn     = NULL;
  BYTE   *blobField      = NULL;
  int     overFlowPageId = 0;

  if (db == NULL || tableName == NULL || keyValueArray == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 获取tableSchema */
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return GNCDB_NOT_FOUND;
  }

  /* 获取btreeTable */
  rc = catalogGetTable(db->catalog, &btreeTable, tableName);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_NOT_FOUND;
  }
  /* 创建一个用于记录overflowPageId的varArrayList，便于在添加Blob数据失败的时候进行回滚 */
  overflowPageIdArray = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (overflowPageIdArray == NULL) {
    return GNCDB_MEM;
  }
  /* 先获取表锁 */
  LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", btreeTable->tableName);
  ReadLock(&btreeTable->rwlock_t);
  LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", btreeTable->tableName);
  //	addIntoLatchedPageSet(tx, NULL);
  //    rc = addIntoLatchedPageSet(tx, (void*)btreeTable);
  //	if (rc != GNCDB_SUCCESS) {
  //		return rc;
  //	}

  btreePage = btreeTableFindTupleInLeafPage(btreeTable, keyValueArray, tableSchema, db, tx, OP_BLOB_SET, true, NULL);
  if (btreePage == NULL) {
    varArrayListDestroy(&overflowPageIdArray);
    return GNCDB_NOT_FOUND;
  }
  leafRecord =
      leafPageFindEntryByKeyvalue(btreePage, keyValueArray, tableSchema, tableName, db->catalog, EQUAL, btreeTable);
  if (leafRecord == NULL) {
    releaseWLatches(db->pagePool, tx, false, NULL);
    varArrayListDestroy(&overflowPageIdArray);
    return GNCDB_NOT_FOUND;
  }
  blobColumn = varArrayListGetPointer(tableSchema->columnList, columnNum);
  if (blobColumn == NULL) {
    releaseWLatches(db->pagePool, tx, false, NULL);
    varArrayListDestroy(&overflowPageIdArray);
    return GNCDB_NOT_FOUND;
  }

  // tuple = leafPageFindEntryByKeyvalue(btreePage, keyValueArray, tableSchema, tableName, db->catalog, EQUAL);
  // if (tuple == NULL)
  // {
  // 	releaseWLatches(db->pagePool, tx, false, NULL);
  // 	varArrayListDestroy(&overflowPageIdArray);
  // 	return GNCDB_NOT_FOUND;
  // }
  blobField = leafRecord + blobColumn->offset;
  // field = varArrayListGetPointer(tuple->fieldArray, columnNum);
  // if (field == NULL) {
  // 	releaseWLatches(db->pagePool, tx, false, NULL);
  // 	return GNCDB_NOT_FOUND;
  // }
  memcpy(&overFlowPageId, blobField, INT_SIZE);
  if (overFlowPageId != 0) {
    releaseWLatches(db->pagePool, tx, false, NULL);
    varArrayListDestroy(&overflowPageIdArray);
    return GNCDB_BLOB_EXIST;
  }
  // if (field->overflowPageId != 0)
  // {
  // 	releaseWLatches(db->pagePool, tx, false, NULL);
  // 	varArrayListDestroy(&overflowPageIdArray);
  // 	return GNCDB_BLOB_EXIST;
  // }

  rc = btreeTableSetBlob(db, btreeTable, buf, size, overflowPageIdArray, tx);
  if (rc != GNCDB_SUCCESS) {
    releaseWLatches(db->pagePool, tx, true, NULL);
    varArrayListDestroy(&overflowPageIdArray);
    return rc;
  }

  /* 更新tuple中Blob字段对应的内容 */
  tempOverflowPageId = (int *)varArrayListGet(overflowPageIdArray, 0);
  if (tempOverflowPageId == NULL) {
    releaseWLatches(db->pagePool, tx, true, NULL);
    varArrayListDestroy(&overflowPageIdArray);
    return GNCDB_NOT_FOUND;
  }

  /* todo? overflowPageIdArray如何使用到事务回滚中 */
  memcpy(blobField, tempOverflowPageId, INT_SIZE);
  memcpy(blobField + INT_SIZE, &size, INT_SIZE);
  // field->overflowPageId = *tempOverflowPageId;
  // field->size = size;
  // if(field->size == 0)
  // {
  // 	printf("error:size is 0\n");
  // }

  /* 做后续处理工作 */
  rc = releaseWLatches(db->pagePool, tx, true, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  varArrayListDestroy(&overflowPageIdArray);
  return GNCDB_SUCCESS;
}

/// <summary>
/// 获取keyValueArray对应的tuple的第column列Blob字段的内容
/// </summary>
/// <param name="btreeTable"></param>
/// <param name="columnNum"></param>
/// <param name="conditionArray"></param>
/// <param name="buf"></param>
/// <param name="size"></param>
/// <param name="tid">事务id</param>
/// <returns></returns>
int executorGetBlob(struct GNCDB *db, char *tableName, int columnNum, struct varArrayList *keyValueArray, BYTE *buf,
    int size, struct Transaction *tx)
{
  int         rc         = 0;
  BtreeTable *btreeTable = NULL;
  BtreePage  *btreePage  = NULL;
  // Tuple* tuple = NULL;
  BYTE        *leafRecord  = NULL;
  TableSchema *tableSchema = NULL;
  BYTE        *blobField   = NULL;
  Column      *blobColumn  = NULL;

  if (db == NULL || tableName == NULL || keyValueArray == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  tableSchema = getTableSchema(db->catalog, tableName);

  /* 大对象使用主键进行查询,并且只有一条记录满足条件 */
  /* 获取btreeTable */

  LOG(LOG_TRACE, "SLOCKing:%s", "tableMap");
  WriteLock(&(db->catalog->tableMapLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "tableMap");
  btreeTable = hashMapGet(db->catalog->tableMap, tableName);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "tableMap");
  WriteUnLock(&(db->catalog->tableMapLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "tableMap");
  if (btreeTable == NULL) {
    return GNCDB_NOT_FOUND;
  }

  /* 先获取表锁 */
  LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", btreeTable->tableName);
  ReadLock(&btreeTable->rwlock_t);
  LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", btreeTable->tableName);
  //    addIntoLatchedPageSet(tx, NULL);
  //    rc = addIntoLatchedPageSet(tx, (void*)btreeTable);
  //	if (rc != GNCDB_SUCCESS) {
  //		return rc;
  //	}

  btreePage = btreeTableFindTupleInLeafPage(btreeTable, keyValueArray, tableSchema, db, tx, OP_BLOB_GET, true, NULL);
  if (btreePage == NULL) {
    return GNCDB_NOT_FOUND;
  }
  leafRecord =
      leafPageFindEntryByKeyvalue(btreePage, keyValueArray, tableSchema, tableName, db->catalog, EQUAL, btreeTable);
  if (leafRecord == NULL) {
    LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreePage->page.id);
    ReadUnLock(&btreePage->page.rwlock_t);
    LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreePage->page.id);
    return GNCDB_NOT_FOUND;
  }
  blobColumn = varArrayListGetPointer(tableSchema->columnList, columnNum);
  if (blobColumn == NULL) {
    LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreePage->page.id);
    ReadUnLock(&btreePage->page.rwlock_t);
    LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreePage->page.id);
    return GNCDB_NOT_FOUND;
  }
  blobField = leafRecord + blobColumn->offset;

	// tuple = leafPageFindEntryByKeyvalue(btreePage, keyValueArray, tableSchema, tableName, db->catalog, EQUAL);
	// if (tuple == NULL)
	// {
	// 	LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreePage->page.id);
    //     ReadUnLock(&btreePage->page.rwlock_t);
	// 	LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreePage->page.id);
	// 	return GNCDB_NOT_FOUND;
	// }
	rc = btreeTableGetBlob(db, btreeTable, blobField, columnNum, size, buf);
	if (rc != GNCDB_SUCCESS) {
		LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreePage->page.id);
        ReadUnLock(&btreePage->page.rwlock_t);
		LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreePage->page.id);
		return rc;
	}
	/* 解锁 + pin */
	LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreePage->page.id);
    ReadUnLock(&btreePage->page.rwlock_t);
	LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreePage->page.id);
	setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);

  return GNCDB_SUCCESS;
}

int executorDeleteBlob(
    struct GNCDB *db, char *tableName, int columnNum, struct varArrayList *keyValueArray, struct Transaction *tx)
{
  int           rc                  = 0;
  BtreeTable   *btreeTable          = NULL;
  BtreePage    *btreePage           = NULL;
  TableSchema  *tableSchema         = NULL;
  varArrayList *overflowPageIdArray = NULL;
  // Tuple* tuple = NULL;
  BYTE *leafRecord = NULL;
  // BlobField* field = NULL;
  Column *blobColumn = NULL;
  BYTE   *blobField  = NULL;
  int     zero       = 0;

  if (db == NULL || tableName == NULL || keyValueArray == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 获取tableSchema */
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return GNCDB_NOT_FOUND;
  }

  /* 大对象使用主键进行查询,并且只有一条记录满足条件 */
  /* 获取btreeTable */
  rc = catalogGetTable(db->catalog, &btreeTable, tableName);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_NOT_FOUND;
  }
  /* 创建一个用于记录overflowPageId的varArrayList，便于在添加Blob数据失败的时候进行回滚 */
  overflowPageIdArray = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
  if (overflowPageIdArray == NULL) {
    return GNCDB_MEM;
  }
  /* 先获取表锁 */
  LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", btreeTable->tableName);
  ReadLock(&btreeTable->rwlock_t);
  LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", btreeTable->tableName);
  //    addIntoLatchedPageSet(tx, NULL);
  //    rc = addIntoLatchedPageSet(tx, (void*)btreeTable);
  //	if (rc != GNCDB_SUCCESS) {
  //		return rc;
  //	}

  btreePage = btreeTableFindTupleInLeafPage(btreeTable, keyValueArray, tableSchema, db, tx, OP_BLOB_DELETE, true, NULL);
  if (btreePage == NULL) {
    varArrayListDestroy(&overflowPageIdArray);
    return GNCDB_NOT_FOUND;
  }
  blobColumn = varArrayListGetPointer(tableSchema->columnList, columnNum);
  if (blobColumn == NULL) {
    varArrayListDestroy(&overflowPageIdArray);
    releaseWLatches(db->pagePool, tx, false, NULL);
    return GNCDB_NOT_FOUND;
  }
  leafRecord =
      leafPageFindEntryByKeyvalue(btreePage, keyValueArray, tableSchema, tableName, db->catalog, EQUAL, btreeTable);
  if (leafRecord == NULL) {
    varArrayListDestroy(&overflowPageIdArray);
    releaseWLatches(db->pagePool, tx, false, NULL);
    return GNCDB_NOT_FOUND;
  }
  blobField = leafRecord + blobColumn->offset;

  // tuple = leafPageFindEntryByKeyvalue(btreePage, keyValueArray, tableSchema, tableName, db->catalog, EQUAL);
  // if (tuple == NULL)
  // {
  // 	varArrayListDestroy(&overflowPageIdArray);
  // 	releaseWLatches(db->pagePool, tx, false, NULL);
  // 	return GNCDB_NOT_FOUND;
  // }

  /* 需要做判断，如果tuple中的Blob字段记录的溢出页的id和Blob内容的大小不为0的话，需要将对应的overflowPage进行删除 */
  rc = checkBlobAndDeleteOverflowPage(blobField, columnNum, db, tx);
  if (rc != GNCDB_SUCCESS) {
    releaseWLatches(db->pagePool, tx, true, NULL);
    varArrayListDestroy(&overflowPageIdArray);
    return rc;
  }

  // field = varArrayListGetPointer(tuple->fieldArray, columnNum);
  memcpy(blobField, &zero, INT_SIZE);
  memcpy(blobField + INT_SIZE, &zero, INT_SIZE);
  // field->overflowPageId = 0;
  // field->size = 0;

  /* 做后续处理工作 */
  rc = releaseWLatches(db->pagePool, tx, true, NULL);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&overflowPageIdArray);
    return rc;
  }

  varArrayListDestroy(&overflowPageIdArray);
  return GNCDB_SUCCESS;
}

int executorSetTableParam(struct GNCDB *db, char *tableName, struct TableSchema *tableSchema, struct Transaction *tx)
{

  int           rc              = 0;
  Condition    *masterCondition = NULL;
  Tuple        *masterTuple     = NULL;
  varArrayList *fileNameArray   = NULL;
  char         *fileName        = NULL;
  varArrayList *fileValueArray  = NULL;
  Scan         *scan            = NULL;
  varArrayList *conditionArray  = NULL;
  varArrayList *minArray        = NULL;
  varArrayList *maxArray        = NULL;

	tableSchema->tableParam = 1;
	masterCondition = conditionConstruct(tableName, "tableName", EQUAL);
	if (masterCondition==NULL)
	{
		return GNCDB_CONDITION_CREATE_FALSE;
	}
	conditionArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (conditionArray == NULL)
	{
		return GNCDB_SPACE_LACK;
	}
	varArrayListAddPointer(conditionArray, masterCondition);
	minArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (minArray == NULL)
	{
		varArrayListDestroy(&conditionArray);
		return GNCDB_SPACE_LACK;
	}
	maxArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (maxArray == NULL)
	{
		varArrayListDestroy(&conditionArray);
		varArrayListDestroy(&minArray);
		return GNCDB_SPACE_LACK;
	}
	rc = mostLeftMatch(tableSchema, conditionArray, minArray, maxArray);
	if (rc != GNCDB_SUCCESS)
	{
		return rc;
	}
	/* 然后构建scan算子 */
	scan = scanConstruct(db, tableName, minArray, maxArray, tx);
	if (scan == NULL)
	{
		return GNCDB_SPACE_LACK;
	}
	// masterTuple = operatorScan(db, scan);
	if (masterTuple == NULL)
	{
		scanDestroy(scan);
		return GNCDB_MASTERTUPLE_NOT_FOUND;
	}
	fileNameArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (fileNameArray == NULL)
	{
		scanDestroy(scan);
		return GNCDB_ARRAY_CREATE_FALSE;
	}
	fileName = "tableParam";
	rc = varArrayListAddPointer(fileNameArray, fileName);
	if (rc)
	{
		varArrayListDestroy(&fileNameArray);
		scanDestroy(scan);
		return GNCDB_ARRAY_ADD_FALSE;
	}
	fileValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (fileValueArray == NULL)
	{
		varArrayListDestroy(&fileNameArray);
		scanDestroy(scan);
		return GNCDB_ARRAY_CREATE_FALSE;
	}
	rc = varArrayListAdd(fileNameArray, &(tableSchema->tableParam));
	if (rc)
	{
		varArrayListDestroy(&fileValueArray);
		varArrayListDestroy(&fileNameArray);
		scanDestroy(scan);
		return GNCDB_ARRAY_ADD_FALSE;
	}
	//未维护
	// rc = btreeTableUpdateTuple(db->catalog->masterTable, fileNameArray, fileValueArray, tableSchema, masterTuple, db, tx);
	if (rc)
	{
		varArrayListDestroy(&fileValueArray);
		varArrayListDestroy(&fileNameArray);
		scanDestroy(scan);
		conditonDestroy(&masterCondition);
		return rc;
	}
	return GNCDB_SUCCESS;
}
/// <summary>
/// Rtree插入
/// 插入新节点类似于B+树接口参数中keyValue为R树索引关键字，primValue为该元组对应的主键值
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name="indexName"></param>
/// <param name="tuple"></param>
/// <param name="tid"></param>
/// <returns></returns>
int executorRtreeInsert(struct GNCDB *db, char *tableName, char *indexName, struct Tuple *tuple, struct Transaction *tx)
{
    int rc = 0;
    // TableSchema* tableSchema = NULL;
    // TableSchema* indexSchema = NULL;
    // RtreeTable* rtreetable = NULL;
    // varArrayList* keyValueArray = NULL;
    // varArrayList* primValueArray = NULL;

    // /* 初始化tableSchema */
    // tableSchema = getTableSchema(db->catalog, tableName);
    // if (tableSchema == NULL) {
    //     return GNCDB_MEM;
    // }

    // /* 初始化indexSchema */
    // indexSchema = getIndexTableSchema(db->catalog, indexName);
    // if (indexSchema == NULL) {
    //     return GNCDB_MEM;
    // }

    // /* 初始化rtreetable */
    // // rc = catalogGetRtreeTable(db->catalog, &rtreetable, indexName);
    // if (rc != GNCDB_SUCCESS) {
    //     return rc;
    // }

    // /* 创建keyValueArray和primValueArray */
    // keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    // primValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    // if (keyValueArray == NULL || primValueArray == NULL) {
    //     varArrayListDestroy(&keyValueArray);
    //     varArrayListDestroy(&primValueArray);
    //     return GNCDB_NOT_FOUND;
    // }

    // /* 获取primValueArray */
    // // rc = leafTupleGetKeyValue(primValueArray, tuple, tableSchema);
    // if (rc != GNCDB_SUCCESS) {
    //     varArrayListDestroy(&keyValueArray);
    //     varArrayListDestroy(&primValueArray);
    //     return rc;
    // }

    // /* 获取keyValueArray */
    // rc = leafTupleGetIndexKeyValue(keyValueArray, tuple, indexSchema);
    // if (rc != GNCDB_SUCCESS) {
    //     varArrayListDestroy(&keyValueArray);
    //     varArrayListDestroy(&primValueArray);
    //     return rc;
    // }

    // /* 索引插入 */
    // /* v_si32_level值设置为0？ */
    // rc = rtreeInsert(rtreetable, keyValueArray, primValueArray, 0, indexSchema, db, tx, 0);
    
    // varArrayListDestroy(&keyValueArray);
    // varArrayListDestroy(&primValueArray);

  return rc;
}

int executorCreateRtreeIndex(struct GNCDB* db, char* tableName, char* indexName, int v_si32_dim, int maxrownum, 
							 struct TableSchema* indexSchema, struct Transaction* tx) {
	int rc = 0;
	

	// RtreePage* RtreePage = NULL;
	// RtreeTable* rtreeTable = NULL;
	// Tuple* tuple = NULL;
	// varArrayList* primValueArray = NULL;
	// varArrayList* keyValueArray = NULL;
	// BtreeTable* table = NULL;
	// TableSchema* tableSchema = NULL;
	// BtreeCursor* cursor = NULL;

	// // rc = pagePoolCreateRtreePage(&RtreePage, LEAF_PAGE, tableName, 0, db, tx);
	// if (rc != GNCDB_SUCCESS) {
	// 	return rc;
	// }

	// rtreeTable = rtreeTableConstruct(indexName, RtreePage->id, v_si32_dim, indexSchema);

	// /* ???????? */
	// primValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// if (primValueArray == NULL)
	// {
	// 	return GNCDB_SPACE_LACK;
	// }
	// keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// if (keyValueArray == NULL)
	// {
	// 	return GNCDB_SPACE_LACK;
	// }
	// table = (BtreeTable*)hashMapGet(db->catalog->tableMap, tableName);
	// if (table == NULL) {
	// 	return GNCDB_MEM;
	// }
	// tableSchema = getTableSchema(db->catalog, tableName);
	// if (tableSchema == NULL) {
	// 	return GNCDB_MEM;
	// }
	// cursor = btreeCursorConstruct(tableName, db, NULL, tx);
	// if (cursor == NULL)
	// {
	// 	return GNCDB_SPACE_LACK;
	// }

	// while (btreeTableHasNextTuple(cursor))
	// {
	// 	// tuple = btreeTableGetNextTuple(table, cursor, db);
	// 	if (tuple == NULL) {
	// 		return GNCDB_SUCCESS;
	// 	}

	// 	// rc = leafTupleGetKeyValue(primValueArray, tuple, tableSchema);
	// 	if (rc != GNCDB_SUCCESS)
	// 	{
	// 		varArrayListDestroy(&primValueArray);
	// 		return rc;
	// 	}
	// 	rc = leafTupleGetIndexKeyValue(keyValueArray, tuple, indexSchema);
	// 	if (rc != GNCDB_SUCCESS) {
	// 		return rc;
	// 	}
	// 	rc = rtreeInsert(rtreeTable, keyValueArray, primValueArray, 0, indexSchema, db, tx, 0);
	// 	if (rc != GNCDB_SUCCESS)
	// 	{
	// 		varArrayListDestroy(&primValueArray);
	// 		return rc;
	// 	}
	// 	varArrayListClear(primValueArray);
	// 	varArrayListClear(keyValueArray);
	// }

	// WriteLock(&(db->catalog->indexSchemaLatch));
	// rc = hashMapPut(db->catalog->indexSchemaMap, indexName, indexSchema);
	// WriteUnLock(&(db->catalog->indexSchemaLatch));

	// if(rc) return rc;
	// rc = catalogAddRtreeTable(db, tableName, indexName, rtreeTable, tx);
	return rc;
}
/// <summary>
/// r树删除表
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <returns></returns>
int executorDropeRtreeTable(struct GNCDB *db, char *indexName, struct Transaction *tx)
{
  int         rc    = 0;
  RtreeTable *table = NULL;
  // rc = catalogGetRtreeTable(db->catalog, &table, indexName);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* rtreeTableDropTable? */
  rc = rtreeTableDropTable(table, db, tx);
  if (rc != GNCDB_SUCCESS)
    return rc;
  return catalogDeleteTable(indexName, tx, db);
}
/// <summary>
/// 表删除对应的所有索引列
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name="keyValueArray"></param>
/// <param name="primkeyArray"></param>
/// <param name="tid"></param>
/// <returns></returns>
int executorRtreeDelete(struct GNCDB* db, char* tableName, Tuple* tuple, struct Transaction* tx) {
    // int rc = 0;
	// int i = 0;
    // varArrayList* primkeyArray;
    // varArrayList* keyValueArray;
    // TableSchema* tableSchema;
    // varArrayList* indexNameArray;
    // char** indexName;
    // TableSchema* indexSchema;
    // RtreeTable* rtreetable = NULL;

    // indexNameArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    // if (indexNameArray == NULL) {
    //     return GNCDB_SPACE_LACK;
    // }

    // rc = catalogGetIndexName(db, indexNameArray, tableName, tx);
    // if (rc != GNCDB_SUCCESS) {
    //     varArrayListDestroy(&indexNameArray);
    //     return rc;
    // }
	// if(indexNameArray->elementCount == 0){
	// 	varArrayListDestroy(&indexNameArray);
	// 	return GNCDB_SUCCESS;
	// }

    // primkeyArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    // if (primkeyArray == NULL) {
	// 	varArrayListDestroy(&indexNameArray);
    //     return GNCDB_SPACE_LACK;
    // }

    // keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    // if (keyValueArray == NULL) {
	// 	varArrayListDestroy(&indexNameArray);
    //     varArrayListDestroy(&primkeyArray);
    //     return GNCDB_SPACE_LACK;
    // }

    // tableSchema = getTableSchema(db->catalog,tableName);
    // if (tableSchema == NULL) {
	// 	varArrayListDestroy(&indexNameArray);
    //     varArrayListDestroy(&primkeyArray);
    //     varArrayListDestroy(&keyValueArray);
    //     return GNCDB_MEM;
    // }

    // // rc = leafTupleGetKeyValue(primkeyArray, tuple, tableSchema);
    // if (rc != GNCDB_SUCCESS) {
	// 	varArrayListDestroy(&indexNameArray);
    //     varArrayListDestroy(&primkeyArray);
    //     varArrayListDestroy(&keyValueArray);
    //     return rc;
    // }


    // for (i = 0; i < indexNameArray->elementCount; i++) {
    //     indexName = varArrayListGet(indexNameArray, i);
	// 	indexSchema = getIndexTableSchema(db->catalog, *indexName);
	// 	if (indexSchema == NULL) {
    //         varArrayListDestroy(&primkeyArray);
    //         varArrayListDestroy(&keyValueArray);
    //         varArrayListDestroy(&indexNameArray);
	// 		return GNCDB_MEM;
	// 	}

	// 	// rc = catalogGetRtreeTable(db->catalog, &rtreetable, *indexName);
	// 	if (rc != GNCDB_SUCCESS) {
    //         varArrayListDestroy(&primkeyArray);
    //         varArrayListDestroy(&keyValueArray);
    //         varArrayListDestroy(&indexNameArray);
	// 		return rc;
	// 	}

    //     rc = leafTupleGetIndexKeyValue(keyValueArray, tuple, indexSchema);
    //     if (rc != GNCDB_SUCCESS) {
    //         varArrayListDestroy(&primkeyArray);
    //         varArrayListDestroy(&keyValueArray);
    //         varArrayListDestroy(&indexNameArray);
    //         return rc;
    //     }

    //     rc = rtreeDelete(rtreetable, keyValueArray, primkeyArray, indexSchema, db, tx);
    //     if (rc != GNCDB_SUCCESS) {
    //         varArrayListDestroy(&primkeyArray);
    //         varArrayListDestroy(&keyValueArray);
    //         varArrayListDestroy(&indexNameArray);
    //         return rc;
    //     }

    //     varArrayListClear(keyValueArray);
    // }

    // varArrayListDestroy(&primkeyArray);
    // varArrayListDestroy(&keyValueArray);
    // varArrayListDestroy(&indexNameArray);

  return GNCDB_SUCCESS;
}
/// <summary>
/// 更新该表对应的索引，应在表更新后调用
/// </summary>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name="updateAttrArray"></param> 需要更新的值
/// <param name="updateFieldArray"></param> 需要更新的列名
/// <param name="tuple"></param> 更新表之前的旧tuple
/// <param name="tx">事务</param>
/// <returns></returns>
int executorUpdateRtree(struct GNCDB* db, char* tableName, struct varArrayList* updateAttrArray, struct varArrayList* updateFieldArray,
						Tuple* tuple, struct Transaction* tx) {
	// int rc = 0;
	// int i = 0, j = 0, p = 0;
	// int fieldNameExist = 0;
	// int newValueFlag = 0;
	// int tupleIndex = 0;
	// varArrayList* indexNameArray = NULL;
	// varArrayList* primValueArray = NULL;
	// varArrayList* indexkeyValueArray = NULL;
	// TableSchema* tableSchema = NULL;
	// varArrayList* updateKeyValueArray = NULL;
	// char** indexName = NULL;
	// TableSchema* indexSchema = NULL;
	// Column** column = NULL;
	// char* fieldName = NULL;
	// char** upFieldName = NULL;
	// RtreeTable* rtreetable = NULL;

	// if (updateAttrArray == NULL || updateFieldArray == NULL || tuple == NULL) {
	// 	return GNCDB_PARAMNULL;
	// }

	// indexNameArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// if (indexNameArray == NULL) {
	// 	return GNCDB_SPACE_LACK;
	// }
	// rc = catalogGetIndexName(db, indexNameArray, tableName, tx);
	// if (indexNameArray == NULL) {
	// 	varArrayListDestroy(&indexNameArray);
	// 	return rc;
	// }
	// /* 无索引直接跳出 */
	// if(indexNameArray->elementCount == 0){
	// 	varArrayListDestroy(&indexNameArray);
	// 	return GNCDB_SUCCESS;
	// }

	// primValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// if (primValueArray == NULL)
	// {
	// 	varArrayListDestroy(&indexNameArray);
	// 	return GNCDB_SPACE_LACK;
	// }
	// indexkeyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// if (indexkeyValueArray == NULL)
	// {
	// 	varArrayListDestroy(&primValueArray);
	// 	varArrayListDestroy(&indexNameArray);
	// 	return GNCDB_SPACE_LACK;
	// }
	// tableSchema = getTableSchema(db->catalog,tableName);
	// if (tableSchema == NULL) {
	// 	varArrayListDestroy(&primValueArray);
	// 	varArrayListDestroy(&indexNameArray);
	// 	varArrayListDestroy(&indexkeyValueArray);
	// 	return GNCDB_MEM;
	// }
	// // rc = leafTupleGetKeyValue(primValueArray, tuple, tableSchema);
	// if (rc != GNCDB_SUCCESS)
	// {
	// 	varArrayListDestroy(&primValueArray);
	// 	varArrayListDestroy(&indexNameArray);
	// 	varArrayListDestroy(&indexkeyValueArray);
	// 	return rc;
	// }

	// /* 索引更新 */
	// updateKeyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	// if (updateKeyValueArray == NULL) {
	// 	varArrayListDestroy(&primValueArray);
	// 	varArrayListDestroy(&indexNameArray);
	// 	varArrayListDestroy(&indexkeyValueArray);
	// 	return GNCDB_SPACE_LACK;
	// }
	// /* 依次更新该表对应的所有索引 */
	// for (i = 0; i < indexNameArray->elementCount; i++) {

	// 	indexName = (char**)varArrayListGet(indexNameArray, i);
	// 	indexSchema = getIndexTableSchema(db->catalog, *indexName);
	// 	if (indexSchema == NULL) {
	// 		return GNCDB_MEM;
	// 	}		
	// 	rc = leafTupleGetIndexKeyValue(indexkeyValueArray, tuple, indexSchema);
	// 	if (rc != GNCDB_SUCCESS) {
	// 		varArrayListDestroy(&updateKeyValueArray);
	// 		varArrayListDestroy(&primValueArray);
	// 		varArrayListDestroy(&indexNameArray);
	// 		varArrayListDestroy(&indexkeyValueArray);
	// 		return rc;
	// 	}

	// 	/* 检查需更新的属性是否在索引中 */
	// 	fieldNameExist = 0;
	// 	/* 顺序遍历索引的属性，旧值从tuple里取，新值从updateAttrArray里取，组成索引所需键值对 */
	// 	for (j = 0; j < indexSchema->columnNum; j++) {
	// 		column = (Column**)varArrayListGet(indexSchema->columnList, j);
	// 		fieldName = (*column)->fieldName;
	// 		newValueFlag = 0;

	// 		for (p = 0; p < updateFieldArray->elementCount; p++) {
	// 			upFieldName = (char**)varArrayListGet(updateFieldArray, p);
	// 			if (upFieldName == NULL) {
	// 				varArrayListDestroy(&primValueArray);
	// 				varArrayListDestroy(&indexkeyValueArray);
	// 				varArrayListDestroy(&updateKeyValueArray);
	// 				varArrayListDestroy(&indexNameArray);
	// 				return GNCDB_NOT_FOUND;
	// 			}
	// 			/* 更新的列在索引中存在，则从updateAttrArray取新值 */
	// 			if (!strcmp(fieldName, *upFieldName)) {
	// 				fieldNameExist = 1;
	// 				newValueFlag = 1;
	// 				rc = varArrayListAdd(updateKeyValueArray, varArrayListGet(updateAttrArray, j));
	// 				if(rc != GNCDB_SUCCESS){
	// 				varArrayListDestroy(&primValueArray);
	// 				varArrayListDestroy(&indexkeyValueArray);
	// 				varArrayListDestroy(&updateKeyValueArray);
	// 				varArrayListDestroy(&indexNameArray);
	// 				return rc;
	// 				}
	// 				break;
	// 			}
	// 		}
	// 		if (newValueFlag) continue;
	// 		tupleIndex = tableSchemaGetIndex(tableSchema, fieldName);
	// 		if(tupleIndex < 0) {
	// 			varArrayListDestroy(&updateKeyValueArray);
	// 			varArrayListDestroy(&primValueArray);
	// 			varArrayListDestroy(&indexNameArray);
	// 			varArrayListDestroy(&indexkeyValueArray);
	// 			return GNCDB_NOT_FOUND;
	// 		}
	// 		rc = leafTupleGetValueByIndex(updateKeyValueArray, tuple, tupleIndex);
	// 		if (rc != GNCDB_SUCCESS) {
	// 			varArrayListDestroy(&primValueArray);
	// 			varArrayListDestroy(&indexkeyValueArray);
	// 			varArrayListDestroy(&updateKeyValueArray);
	// 			varArrayListDestroy(&indexNameArray);
	// 			return rc;
	// 		}
	// 	}
	// 	if (!fieldNameExist) continue;

	// 	// rc = catalogGetRtreeTable(db->catalog, &rtreetable, *indexName);
	// 	if (rc != GNCDB_SUCCESS) {
	// 		varArrayListDestroy(&updateKeyValueArray);
	// 		varArrayListDestroy(&primValueArray);
	// 		varArrayListDestroy(&indexNameArray);
	// 		varArrayListDestroy(&indexkeyValueArray);
	// 		return rc;
	// 	}
	// 	/* 删除索引对应列 */
	// 	rc = rtreeDelete(rtreetable, indexkeyValueArray, primValueArray, indexSchema, db, tx);
	// 	if (rc != GNCDB_SUCCESS) {
	// 		varArrayListDestroy(&primValueArray);
	// 		varArrayListDestroy(&indexkeyValueArray);
	// 		varArrayListDestroy(&updateKeyValueArray);
	// 		varArrayListDestroy(&indexNameArray);
	// 		return rc;
	// 	}
	// 	/* 插入索引对应列 */
	// 	rc = rtreeInsert(rtreetable, updateKeyValueArray, primValueArray, 0, indexSchema, db, tx, 0);
	// 	if (rc != GNCDB_SUCCESS) {
	// 		varArrayListDestroy(&primValueArray);
	// 		varArrayListDestroy(&indexkeyValueArray);
	// 		varArrayListDestroy(&updateKeyValueArray);
	// 		varArrayListDestroy(&indexNameArray);
	// 		return rc;
	// 	}
	// 	/* 清空更新索引键值列表 */
	// 	varArrayListClear(indexkeyValueArray);
	// 	varArrayListClear(updateKeyValueArray);
	// }
	// varArrayListDestroy(&indexNameArray);
	// varArrayListDestroy(&primValueArray);
	// varArrayListDestroy(&indexkeyValueArray);
	// varArrayListDestroy(&updateKeyValueArray);
	return GNCDB_SUCCESS;
}

/// <summary>
/// R树查询
/// </summary>
/// <param name="db"></param>
/// <param name="callback"></param>
/// <param name="tableArray"></param>
/// <param name="columnNameArray"></param>
/// <param name="conditionArray"></param>
/// <param name="tid"></param>
/// <returns></returns>
int executorRtreeSelect(struct GNCDB *db, CallBack callback, int *affectedRows, struct varArrayList *tableArray,
    struct varArrayList *columnNameArray, struct varArrayList *conditionArray, struct Transaction *tx)
{
  int           rc                 = 0;
  int           i                  = 0;
  Queryplan    *queryplan          = NULL;
  Projection   *projection         = NULL;
  char         *primaryTable       = NULL;
  char         *indexName          = NULL;
  char         *tableName          = NULL;
  TableSchema  *indexSchema        = NULL;
  TableSchema  *tableSchema        = NULL;
  Condition    *condition          = NULL;
  RtreeScan    *scan               = NULL;
  Filter       *filter             = NULL;
  char         *indexName1         = NULL;
  char         *indexName2         = NULL;
  char         *tableName1         = NULL;
  char         *tableName2         = NULL;
  varArrayList *conditionArrayJoin = NULL;
  varArrayList *searchvalue        = NULL;
  Join         *join               = NULL;
  char         *left[2]            = {NULL, NULL};
  char         *right[2]           = {NULL, NULL};
  char         *fieldName1         = NULL;
  char         *fieldName2         = NULL;
  char          c                  = '-';
  TableSchema  *tableSchemaS       = NULL;
  char         *valueStr           = NULL;
  Filter       *filterCC           = NULL;
  TableSchema  *indexSchemaSort    = NULL;
  varArrayList *conditionJoin      = NULL;
  Tuple        *tupleReturn        = NULL;

  /* queryplan中的tableArray以及scan算子中均保存的索引名，其余为索引所属表名 */

  /* 创建一个查询计划 */
  queryplan = queryplanConstruct(callback, tableArray, tx);
  if (queryplan == NULL) {
    return GNCDB_SPACE_LACK;
  }
  /* 判断是否需要投影 */
  if (columnNameArray->elementCount != 0) {
    projection = projectionConstruct(db, columnNameArray);
    if (projection == NULL) {
      queryplanDestroy(queryplan);
      return GNCDB_SPACE_LACK;
    }
    queryplanPushProjectionOperator(queryplan, projection);
  } else {
    varArrayListDestroy(&columnNameArray);
  }
  /* 拿取表名 */
  indexName = varArrayListGetPointer(queryplan->tableArray, 0);
  if (indexName == NULL) {
    queryplanDestroy(queryplan);
    return GNCDB_PARAMNULL;
  }
  rc = catalogGetDependentTableName(db, indexName, &tableName, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  primaryTable = tableName;

  /* 通过需要查询的表名判断单表查询或者多表查询 */
  if (tableArray->elementCount == 1) {
    /* 单表查询 */
    indexSchema = getIndexSchema(db->catalog, indexName);
    if (indexSchema == NULL) {
      queryplanDestroy(queryplan);
      return GNCDB_PARAMNULL;
    }
    for (i = 0; i < conditionArray->elementCount; ++i) {
      condition = varArrayListGetPointer(conditionArray, i);
      rc        = exectorParseCondition(indexSchema, condition);
      if (rc) {
        queryplanDestroy(queryplan);
        return rc;
      }
    }
    // 编辑满足条件的searchvalue
    searchvalue = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    if (searchvalue == NULL) {
      queryplanDestroy(queryplan);
      return GNCDB_SPACE_LACK;
    }
    rc = getRtreeSearchValue(conditionArray, searchvalue, indexName, indexSchema);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&searchvalue);
      queryplanDestroy(queryplan);
      return rc;
    }
    // 对conditionArray按照索引schema属性排序
    tableSchema = getTableSchema(db->catalog, tableName);
    rc          = sortCondition(conditionArray, tableSchema);
    if (rc != GNCDB_SUCCESS) {
      queryplanDestroy(queryplan);
      return rc;
    }
    /* 然后构建scan算子 */
    scan = rtreeScanConstruct(db, indexName, searchvalue, tx);
    if (scan == NULL) {
      varArrayListDestroy(&searchvalue);
      queryplanDestroy(queryplan);
      return GNCDB_SPACE_LACK;
    }
    hashMapPut(queryplan->scanMap, indexName, scan);

    filter = filterConstruct(db, conditionArray, tableName);
    if (filter == NULL) {
      queryplanDestroy(queryplan);
      return GNCDB_SPACE_LACK;
    }
    queryplanPushFilterOperator(queryplan, tableName, filter);
  } else {
    /* 多表查询 */
    /* 对每个表创建filter */
    for (i = 0; i < tableArray->elementCount; ++i) {
      indexName = varArrayListGetPointer(queryplan->tableArray, i);
      if (indexName == NULL) {
        queryplanDestroy(queryplan);
        return GNCDB_PARAMNULL;
      }
      rc = catalogGetDependentTableName(db, indexName, &tableName, NULL);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      /* 在进行连接时分别对每个表进行创建filter算子， conditionJoin为filter中的conditionArray */
      conditionJoin = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
      if (conditionJoin == NULL) {
        queryplanDestroy(queryplan);
        return GNCDB_SPACE_LACK;
      }
      filter = filterConstruct(db, conditionJoin, tableName);
      if (filter == NULL) {
        queryplanDestroy(queryplan);
        return GNCDB_SPACE_LACK;
      }
      queryplanPushFilterOperator(queryplan, tableName, filter);
    }
    /* 目前编写支持对两个表连接 */
    /* 拿取表名 */
    indexName1 = varArrayListGetPointer(queryplan->tableArray, 0);
    indexName2 = varArrayListGetPointer(queryplan->tableArray, 1);
    rc         = catalogGetDependentTableName(db, indexName1, &tableName1, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = catalogGetDependentTableName(db, indexName2, &tableName2, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    conditionArrayJoin = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    if (conditionArrayJoin == NULL) {
      queryplanDestroy(queryplan);
      return GNCDB_SPACE_LACK;
    }
    /* 创建join算子 */
    join = joinConstruct(db, tableName1, tableName2, conditionArrayJoin);
    if (join == NULL) {
      varArrayListDestroy(&conditionArrayJoin);
      queryplanDestroy(queryplan);
      return GNCDB_SPACE_LACK;
    }
    queryplanPushJoinOperator(queryplan, join);
    /* 将每个条件进行分析，寻找连接条件 */
    for (i = 0; i < conditionArray->elementCount; ++i) {
      condition = varArrayListGetPointer(conditionArray, i);
      if (hasOneDots(condition->fieldName) == GNCDB_SUCCESS) {
        /* 左值有. */
        rc = split_string(condition->fieldName, left);
        if (left[0] == NULL || left[1] == NULL) {
          queryplanDestroy(queryplan);
          return rc;
        }
        if (hasOneDots(condition->value) == GNCDB_SUCCESS) {
          /* 右值有. */
          rc = split_string(condition->value, right);
          if (right[0] == NULL || right[1] == NULL) {
            queryplanDestroy(queryplan);
            return rc;
          }
          fieldName1 = my_malloc(strlen(left[1]) + 1);
          if (fieldName1 == NULL) {
            queryplanDestroy(queryplan);
            return GNCDB_SPACE_LACK;
          }
          strcpy(fieldName1, left[1]);
          my_free(condition->fieldName);
          condition->fieldName = fieldName1;
          c                    = *right[0];
          if (c >= '0' || c <= '9' || c == '-') {
            tableSchemaS = getIndexSchema(db->catalog, left[0]);
            rc           = exectorParseCondition(tableSchemaS, condition);
            if (rc) {
              queryplanDestroy(queryplan);
              return rc;
            }
            rc = catalogGetDependentTableName(db, left[0], &tableName, NULL);
            if (rc != GNCDB_SUCCESS) {
              return rc;
            }
            filterCC = hashMapGet(queryplan->filterMap, tableName);
            rc       = varArrayListAddPointer(filterCC->conditionArray, condition);
            if (rc) {
              queryplanDestroy(queryplan);
              return rc;
            }
          } else {
            fieldName2 = my_malloc(strlen(right[1]) + 1);
            if (fieldName2 == NULL) {
              queryplanDestroy(queryplan);
              return GNCDB_SPACE_LACK;
            }
            strcpy(fieldName2, right[1]);
            my_free(condition->value);
            condition->value = fieldName2;
            // condition = NULL;
            rc = varArrayListAddPointer(conditionArrayJoin, condition);
            if (rc) {
              queryplanDestroy(queryplan);
              return rc;
            }
          }

        } else if (tableArrayExistsFileName(db, tableArray, condition->value) != NULL) {
          /* 右值为属性 */
          fieldName1 = my_malloc(strlen(left[1]) + 1);
          if (fieldName1 == NULL) {
            queryplanDestroy(queryplan);
            return GNCDB_SPACE_LACK;
          }
          strcpy(fieldName1, left[1]);
          my_free(condition->fieldName);
          condition->fieldName = fieldName1;

          rc = varArrayListAddPointer(conditionArrayJoin, condition);
          if (rc) {
            queryplanDestroy(queryplan);
            return rc;
          }
        } else {
          /* 右值为数据 */
          fieldName1 = my_malloc(strlen(left[1]) + 1);
          if (fieldName1 == NULL) {
            queryplanDestroy(queryplan);
            return GNCDB_SPACE_LACK;
          }
          strcpy(fieldName1, left[1]);
          my_free(condition->fieldName);
          condition->fieldName = fieldName1;

          /* 右值为数据时解析条件并放入左值表名对应的filter */
          tableSchemaS = getIndexSchema(db->catalog, left[0]);
          rc           = exectorParseCondition(tableSchemaS, condition);
          if (rc) {
            queryplanDestroy(queryplan);
            return rc;
          }
          rc = catalogGetDependentTableName(db, left[0], &tableName, NULL);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }
          filterCC = hashMapGet(queryplan->filterMap, tableName);
          rc       = varArrayListAddPointer(filterCC->conditionArray, condition);
          if (rc) {
            queryplanDestroy(queryplan);
            return rc;
          }
        }
      } else {
        /* 左值无. */
        if (hasOneDots(condition->value) == GNCDB_SUCCESS) {
          /* 右值有. */
          /* 首先判断右值是否是浮点型数据 */
          valueStr = (char *)condition->value;
          c        = valueStr[0];
          if (c >= '0' || c <= '9' || c == '-') {
            indexName    = tableArrayExistsFileName(db, tableArray, condition->fieldName);
            tableSchemaS = getIndexSchema(db->catalog, indexName);
            rc           = exectorParseCondition(tableSchemaS, condition);
            if (rc) {
              queryplanDestroy(queryplan);
              return rc;
            }
            rc = catalogGetDependentTableName(db, indexName, &tableName, NULL);
            if (rc != GNCDB_SUCCESS) {
              return rc;
            }
            filterCC = hashMapGet(queryplan->filterMap, tableName);
            rc       = varArrayListAddPointer(filterCC->conditionArray, condition);
            if (rc) {
              queryplanDestroy(queryplan);
              return rc;
            }
          } else {
            rc = split_string(condition->value, right);
            if (right[0] == NULL || right[1] == NULL) {
              queryplanDestroy(queryplan);
              return rc;
            }
            fieldName2 = my_malloc(strlen(right[1]) + 1);
            if (fieldName2 == NULL) {
              queryplanDestroy(queryplan);
              return GNCDB_SPACE_LACK;
            }
            strcpy(fieldName2, right[1]);
            my_free(condition->value);
            condition->value = fieldName2;

            rc = varArrayListAddPointer(conditionArrayJoin, condition);
            if (rc) {
              queryplanDestroy(queryplan);
              return rc;
            }
          }
        } else if (tableArrayExistsFileName(db, tableArray, condition->value) != NULL) {
          /* 右值为属性 */
          rc = varArrayListAddPointer(conditionArrayJoin, condition);
          if (rc) {
            queryplanDestroy(queryplan);
            return rc;
          }
        } else {
          /* 右值为数据 */
          indexName    = tableArrayExistsFileName(db, tableArray, condition->fieldName);
          tableSchemaS = getIndexSchema(db->catalog, indexName);
          rc           = exectorParseCondition(tableSchemaS, condition);
          if (rc) {
            queryplanDestroy(queryplan);
            return rc;
          }
          rc = catalogGetDependentTableName(db, indexName, &tableName, NULL);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }
          filterCC = hashMapGet(queryplan->filterMap, tableName);
          rc       = varArrayListAddPointer(filterCC->conditionArray, condition);
          if (rc) {
            queryplanDestroy(queryplan);
            return rc;
          }
        }
      }
    }

    /* 遍历所有的filter算子将conditionarray进行排序,并获取是否可以使用indexscan,对所有的表创建scan算子 */
    for (i = 0; i < tableArray->elementCount; ++i) {
      indexName       = varArrayListGetPointer(queryplan->tableArray, i);
      indexSchemaSort = getIndexSchema(db->catalog, indexName);

      // 编辑满足条件的searchvalue
      searchvalue = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
      if (searchvalue == NULL) {
        queryplanDestroy(queryplan);
        return GNCDB_SPACE_LACK;
      }
      rc = getRtreeSearchValue(conditionArray, searchvalue, indexName, indexSchemaSort);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&searchvalue);
        queryplanDestroy(queryplan);
        return rc;
      }

      rc = catalogGetDependentTableName(db, indexName, &tableName, NULL);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      filter      = hashMapGet(queryplan->filterMap, tableName);
      tableSchema = getTableSchema(db->catalog, tableName);
      sortCondition(filter->conditionArray, tableSchema);

      /* 然后构建scan算子 */
      scan = rtreeScanConstruct(db, indexName, searchvalue, tx);
      if (scan == NULL) {
        varArrayListDestroy(&searchvalue);
        queryplanDestroy(queryplan);
        return GNCDB_SPACE_LACK;
      }
      hashMapPut(queryplan->scanMap, indexName, scan);
    }
  }

  /* 拿取主表的scan */
  scan = hashMapGet(queryplan->scanMap, primaryTable);

  while (rtreeHasNextTuple(scan)) {
    rc = queryplanExecuteRtree(queryplan, affectedRows, &tupleReturn, db);

    if (rc != GNCDB_SUCCESS) {
      queryplanDestroy(queryplan);
      return rc;
    }
  }

  queryplanDestroy(queryplan);

  return GNCDB_SUCCESS;
}
/// <summary>
/// 整合R树游标所需参数
/// </summary>
/// <param name="conditionArray"></param>
/// <param name="searchvalue"></param>
/// <param name="indexName"></param>
/// <param name="tableSchema"></param>
/// <returns></returns>
int getRtreeSearchValue(
    varArrayList *conditionArray, varArrayList *searchvalue, char *indexName, TableSchema *indexSchema)
{
  int rc = GNCDB_SUCCESS;
  int i = 0, j = 0;
  // int rtreeDim = 0;
  HashMap   *minMap    = NULL;
  HashMap   *maxMap    = NULL;
  Condition *condition = NULL;
  Column    *column    = NULL;

  /* 按照indexSchema将条件排序 */
  rc = sortCondition(conditionArray, indexSchema);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 创建两个保存上下界的哈希表 */
  minMap = hashMapCreate(STRKEY, 0, NULL);
  if (minMap == NULL) {
    return GNCDB_SPACE_LACK;
  }
  maxMap = hashMapCreate(STRKEY, 0, NULL);
  if (maxMap == NULL) {
    hashMapDestroy(&minMap);
    return GNCDB_SPACE_LACK;
  }

  /* 顺序遍历conditionArray，若condition中存在上下界直接加入searchvalue；若不存在，用indexSchema上下界补全 */
  /* 第一步遍历conditionArray并根据符号加入minMap和maxMap，若相等俩都加 */
  for (i = 0; i < conditionArray->elementCount; i++) {
    condition = (Condition *)varArrayListGetPointer(conditionArray, i);

    /* 条件冗余判断在外层条件处理中，该层不考虑 */
    if (condition->predicate == GREATER_THAN || condition->predicate == GREATER_THAN_OR_EQUAL) {
      rc = hashMapPut(maxMap, condition->fieldName, condition->value);
    } else if (condition->predicate == LESS_THAN || condition->predicate == LESS_THAN_OR_EQUAL) {
      rc = hashMapPut(minMap, condition->fieldName, condition->value);
    } else {
      rc = hashMapPut(minMap, condition->fieldName, condition->value);
      if (rc != GNCDB_SUCCESS) {
        hashMapDestroy(&minMap);
        hashMapDestroy(&maxMap);
        return rc;
      }
      rc = hashMapPut(maxMap, condition->fieldName, condition->value);
    }
    if (rc != GNCDB_SUCCESS) {
      hashMapDestroy(&minMap);
      hashMapDestroy(&maxMap);
      return rc;
    }
  }
  /* 第二步遍历indexSchema，根据fieldName判断condition中是否存在上下界 */

  for (j = 0; j < indexSchema->columnNum; j++) {
    column = varArrayListGetPointer(indexSchema->columnList, j);
    if (column == NULL) {
      hashMapDestroy(&minMap);
      hashMapDestroy(&maxMap);
      return GNCDB_NOT_FOUND;
    }
    /* 非R树key则继续循环 */
    if (column->columnConstraint->isPrimaryKey == 0) {
      continue;
    }
    /* 上界 */
    if (hashMapExists(minMap, column->fieldName)) {
      rc = varArrayListAdd(searchvalue, hashMapGet(minMap, column->fieldName));
    } else {
      rc = varArrayListAdd(searchvalue, &(column->columnConstraint->minValue));
    }
    if (rc != GNCDB_SUCCESS) {
      hashMapDestroy(&minMap);
      hashMapDestroy(&maxMap);
      return rc;
    }
    /* 下界 */
    if (hashMapExists(maxMap, column->fieldName)) {
      rc = varArrayListAdd(searchvalue, hashMapGet(maxMap, column->fieldName));
    } else {
      rc = varArrayListAdd(searchvalue, &(column->columnConstraint->minValue));
    }
    if (rc != GNCDB_SUCCESS) {
      hashMapDestroy(&minMap);
      hashMapDestroy(&maxMap);
      return rc;
    }
  }
  return GNCDB_SUCCESS;
}

/**
 * @description: 创建哈希索引
 * @param {GNCDB} *db
 * @param {char} *tableName 表名
 * @param {char} *indexName 索引名
 * @param {TableSchema} *indexSchema 调用者构造好的索引schema
 * @param {Transaction} *tx
 * @return {*}
 */
int executorCreateHashIndex(
    struct GNCDB *db, char *tableName, char *indexName, struct TableSchema *indexSchema, struct Transaction *tx)
{
  int           rc                                          = 0;
  MetaPage     *metaPage                                    = NULL;
  BucketPage   *bucketPage                                  = NULL;
  BtreeTable   *table                                       = NULL;
  TableSchema  *tableSchema                                 = NULL;
  BtreeCursor  *cursor                                      = NULL;
  BYTE         *record                                      = NULL;
  HashIndex    *hashIndex                                   = NULL;
  varArrayList *pkValueArray                                = NULL;
  varArrayList *hashKeyValueArray                           = NULL;
  varArrayList *pkIdxArray                                  = NULL;
  varArrayList *pkCols                                      = NULL;
  Column       *indexColumn                                 = NULL;
  int           pkIndex                                     = 0;
  Column       *pkColumn                                    = NULL;
  void         *hashKeyValue                                = NULL;
  int           tupleCount                                  = 0;
  int           bucketCount                                 = 0;
  const double  FILL_FACTOR                                 = 0.7;
  int           BUCkET_MAX_ENTRY                            = 0;
  int           hashValue                                   = 0;
  int           bucketPageId                                = 0;
  int           i                                           = 0;
  char          fullIdxName[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};  // tabName.idxName + '\0'
  int           m                                           = 0;
  // 参数检查
  if (db == NULL || tableName == NULL || indexName == NULL || indexSchema == NULL) {
    return GNCDB_PARAMNULL;
  }

  sprintf(fullIdxName, "%s.%s", tableName, indexName);
  LOG(LOG_TRACE, "SLOCKing:%s", "indexMap");
  WriteLock(&(db->catalog->indexMapLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "indexMap");
  rc = hashMapPut(db->catalog->indexMap, fullIdxName, hashIndex);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "indexMap");
  WriteUnLock(&(db->catalog->indexMapLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "indexMap");

  LOG(LOG_TRACE, "SLOCKing:%s", "indexSchemaMap");
  WriteLock(&(db->catalog->indexSchemaLatch));
  LOG(LOG_TRACE, "SLOCKend:%s", "indexSchemaMap");
  rc = hashMapPut(db->catalog->indexSchemaMap, fullIdxName, indexSchema);
  LOG(LOG_TRACE, "SUNLOCKing:%s", "indexSchemaMap");
  WriteUnLock(&(db->catalog->indexSchemaLatch));
  LOG(LOG_TRACE, "SUNLOCKend:%s", "indexSchemaMap");


  /* 1. 获取原表信息 */
  rc = catalogGetTable(db->catalog, &table, tableName);
  if (table == NULL) {
    return GNCDB_TABLE_NOT_FOUND;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return GNCDB_TABLE_NOT_FOUND;
  }
  indexColumn = varArrayListGetPointer(indexSchema->columnList, 0);

  /* 2. 统计原表数据量并计算桶数 */
  cursor = btreeCursorConstruct(tableName, db, NULL, tx);
  if (cursor == NULL) {
    return GNCDB_SPACE_LACK;
  }
  while (btreeTableHasNextTuple(cursor)) {
    record = btreeTableGetNextRecord(table, cursor, db);
    if (record == NULL)
      break;
    tupleCount++;
  }
  btreeCursorDestroy(&cursor);

  /* 3. 创建并初始化元数据页 */
  metaPage = CreateHashMetaPage(db, tx);
  if (metaPage == NULL) {
    return GNCDB_MEM;
  }

  /* 4. 构造哈希索引对象 */
  hashIndex = hashIndexConstruct(metaPage->page.id, indexSchema->columnList, tableName, indexName, db->catalog);
  if (hashIndex == NULL) {
    MetaPageDestroy(&metaPage);
    return GNCDB_MEM;
  }
  WriteLock(&(hashIndex->rwlock));

  //* 获取主键列并计算主键长度
  pkIdxArray = getPrimaryKeyIndex(db->catalog, tableName);
  pkCols     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  for (int i = 0; i < pkIdxArray->elementCount; i++) {
    pkIndex  = *(int *)varArrayListGet(pkIdxArray, i);
    pkColumn = (Column *)varArrayListGetPointer(tableSchema->columnList, pkIndex);
    varArrayListAddPointer(pkCols, pkColumn);
    switch (pkColumn->fieldType) {
      case FIELDTYPE_INTEGER: hashIndex->primaryKeyLenth += INT_SIZE; break;
      case FIELDTYPE_REAL: hashIndex->primaryKeyLenth += DOUBLE_SIZE; break;
      case FIELDTYPE_VARCHAR: hashIndex->primaryKeyLenth += pkColumn->columnConstraint->maxValue; break;
      default: return GNCDB_FIELD_NOT_EXIST;
    }
  }
  hashIndex->pkCols = pkCols;
  BUCkET_MAX_ENTRY  = GET_BUCKET_PAIR_MAX_COUNT(hashIndex, db->pageCurrentSize);

  /* 计算桶数 */
  bucketCount = (int)ceil((double)tupleCount / (FILL_FACTOR * BUCkET_MAX_ENTRY));
  if (bucketCount < 2) {
    bucketCount = 2;
  }
  if (bucketCount > MAX_BUCKET_COUNT(db->pageCurrentSize)) {
    bucketCount = MAX_BUCKET_COUNT(db->pageCurrentSize);
  }
  m = bucketCount - 1;

  /* 初始化元数据页 */
  metaPage->hashFunctionId  = 0;
  metaPage->bucketCount     = bucketCount;
  metaPage->maxBucketNumber = bucketCount - 1;
  metaPage->fillFactor      = FILL_FACTOR;
  GET_MASK(m);
  metaPage->highMask   = m;
  metaPage->lowMask    = (metaPage->highMask >> 1);
  metaPage->splitCount = ceil(log2(bucketCount));

  /* 创建并初始化所有桶页 */
  for (i = 0; i < bucketCount; i++) {
    bucketPage = CreateHashBucketPage(db, hashIndex, i, tx);
    if (bucketPage == NULL) {
      MetaPageDestroy(&metaPage);
      hashIndexDestroy(&hashIndex);
      return GNCDB_MEM;
    }
    /* 将桶页ID添加到元数据页的桶页地址数组中 */
    bucketPageId = bucketPage->page.id;
    memcpy(metaPage->page.pData + META_PAGE_HEADER_SIZE + i * INT_SIZE, &bucketPageId, INT_SIZE);
    bucketPage->primaryKeyLenth = hashIndex->primaryKeyLenth;
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (rc != GNCDB_SUCCESS) {
      MetaPageDestroy(&metaPage);
      return rc;
    }
  }

  /* 6. 重新遍历原表数据，插入索引 */
  cursor = btreeCursorConstruct(tableName, db, NULL, tx);
  if (cursor == NULL) {
    MetaPageDestroy(&metaPage);
    hashIndexDestroy(&hashIndex);
    return GNCDB_SPACE_LACK;
  }

  //* 这是主键值array
  pkValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  //* 这是哈希索引键array
  hashKeyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (pkValueArray == NULL || hashKeyValueArray == NULL) {
    MetaPageDestroy(&metaPage);
    hashIndexDestroy(&hashIndex);
    varArrayListDestroy(&pkValueArray);
    varArrayListDestroy(&hashKeyValueArray);
    return GNCDB_SPACE_LACK;
  }
  while (btreeTableHasNextTuple(cursor)) {
    record = btreeTableGetNextRecord(table, cursor, db);
    if (record == NULL)
      break;

    // *分别从tuple获取主键值和哈希键值
    rc = leafTupleGetKeyValue(pkValueArray, record, db->catalog, tableSchema, tableName);
    if (rc != GNCDB_SUCCESS)
      goto cleanup;
    rc = leafTupleGetHashIndexKeyValue(hashKeyValueArray, record, indexSchema->columnList);
    if (rc != GNCDB_SUCCESS)
      goto cleanup;

    hashKeyValue = varArrayListGetPointer(hashKeyValueArray, 0);
    hashValue    = getHashValue(hashKeyValue, indexColumn->fieldType);

    // 创建时插入已经对元数据页、现有所有桶页、所有溢出页上了逻辑写锁，对索引上了物理互斥锁
    rc = hashIndexInsert(hashIndex, hashValue, pkValueArray, true, db, tx);
    if (rc != GNCDB_SUCCESS)
      goto cleanup;
    varArrayListClear(hashKeyValueArray);
    varArrayListClear(pkValueArray);
  }

  // 插入完成释放索引的互斥锁
  WriteUnLock(&(hashIndex->rwlock));

  if (cursor != NULL)
    btreeCursorDestroy(&cursor);
  if (pkValueArray != NULL)
    varArrayListDestroy(&pkValueArray);
  if (hashKeyValueArray != NULL)
    varArrayListDestroy(&hashKeyValueArray);
  // rc = unPinNewCreatePages(db->pagePool, tx);
  // if (rc != GNCDB_SUCCESS) {
  //   destoryMetaPage(&metaPage);
  //   hashIndexDestroy(&hashIndex);
  //   return rc;
  // }

  /* 7. 更新目录信息 */
  sprintf(fullIdxName, "%s.%s", tableName, indexName);
  rc = catalogAddHashIndex(fullIdxName, hashIndex, tx, db);
  if (rc != GNCDB_SUCCESS)
    goto cleanup;

  return GNCDB_SUCCESS;

cleanup:
  printf("进入cleanup阶段\n");
  if (cursor != NULL)
    btreeCursorDestroy(&cursor);
  if (pkValueArray != NULL)
    varArrayListDestroy(&pkValueArray);
  if (hashKeyValueArray != NULL)
    varArrayListDestroy(&hashKeyValueArray);
  if (hashIndex != NULL)
    hashIndexDestroy(&hashIndex);
  if (metaPage != NULL)
    MetaPageDestroy(&metaPage);
  return rc;
}

int executorDropeHashIndex(struct GNCDB *db, char *tableName, char *indexName, struct Transaction *tx)
{
  int               rc                                          = 0;
  HashIndex        *hashIndex                                   = NULL;
  MetaPage         *metaPage                                    = NULL;
  BucketPage       *bucketPage                                  = NULL;
  HashOverflowPage *overflowPage                                = NULL;
  int               nextOverflowPageId                          = 0;
  int               i                                           = 0;
  int               bucketPageId                                = 0;
  char              fullIdxName[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};  // tabName.idxName + '\0'

  sprintf(fullIdxName, "%s.%s", tableName, indexName);
  rc = catalogGetHashIndex(db->catalog, &hashIndex, fullIdxName);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  WriteLock(&(hashIndex->rwlock));
  rc = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, indexName, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = produceOldPageData(db, (Page *)metaPage, META_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  setPageStatusDirty(db->pagePool, metaPage->page.id, NULL);
  for (i = 0; i < metaPage->bucketCount; i++) {
    bucketPageId = *(int *)(metaPage->page.pData + META_PAGE_HEADER_SIZE + i * INT_SIZE);
    rc           = pagePoolGetPage((Page **)&bucketPage, bucketPageId, hashIndex->tableName, db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = produceOldPageData(db, (Page *)bucketPage, BUCKET_PAGE, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    nextOverflowPageId = bucketPage->firstOverflowPageId;
    while (nextOverflowPageId > 1) {
      rc = pagePoolGetPage((Page **)&overflowPage, nextOverflowPageId, indexName, db);
      if (rc != GNCDB_SUCCESS) {
        break;
      }
      rc                 = produceOldPageData(db, (Page *)overflowPage, OVERFLOW_PAGE, tx);
      nextOverflowPageId = overflowPage->nextPageId;
      setPageStatusDirty(db->pagePool, overflowPage->page.id, NULL);
      hashOverflowPageToFreePage(overflowPage, db);
    }
    setPageStatusDirty(db->pagePool, bucketPage->page.id, NULL);
    bucketPageToFreePage(bucketPage, db);
  }
  metaPageToFreePage(metaPage, db);
  WriteUnLock(&(hashIndex->rwlock));
  //* 删除catalog中的索引信息并销毁hashIndex
  rc = catalogDeleteHashIndex(fullIdxName, hashIndex, tx, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

/**
 * @description: 插入tuple时插入一个表中所有哈希索引的记录
 * @param {GNCDB} *db
 * @param {char} *tableName
 * @param {BYTE} *record
 * @param {Transaction} *tx
 * @return {*}
 */
int executorInsertHashIndex(struct GNCDB *db, char *tableName, BYTE *record, struct Transaction *tx)
{
  int           rc             = 0;
  varArrayList *hashIndexList  = NULL;
  HashIndex    *hashIndex      = NULL;
  void         *keyValue       = NULL;
  int           hashValue      = 0;
  TableSchema  *tableSchema    = NULL;
  Column       *indexColumn    = NULL;
  varArrayList *primValueArray = NULL;
  varArrayList *keyValueArray  = NULL;
  int           i;

  // 获取表的所有哈希索引
  hashIndexList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  rc            = catalogGetHashIndexList(db, hashIndexList, tableName, tx);
  if (rc != GNCDB_SUCCESS || hashIndexList == NULL) {
    varArrayListDestroy(&hashIndexList);
    return rc;
  }
  /* 如果没有哈希索引，直接返回成功 */
  if (hashIndexList->elementCount == 0) {
    varArrayListDestroy(&hashIndexList);
    return GNCDB_SUCCESS;
  }

  // 获取表的Schema
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return GNCDB_TABLE_NOT_FOUND;
  }

  primValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  keyValueArray  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  rc             = leafTupleGetKeyValue(primValueArray, record, db->catalog, tableSchema, tableName);

  // 遍历所有哈希索引进行插入操作
  for (i = 0; i < hashIndexList->elementCount; i++) {
    hashIndex = varArrayListGetPointer(hashIndexList, i);
    // 获取索引列的值
    indexColumn = (Column *)varArrayListGetPointer(hashIndex->index_columns, 0);
    rc          = leafTupleGetHashIndexKeyValue(keyValueArray, record, hashIndex->index_columns);
    keyValue    = varArrayListGetPointer(keyValueArray, 0);
    hashValue   = getHashValue(keyValue, indexColumn->fieldType);

    // 插入到哈希索引
    rc = hashIndexInsert(hashIndex, hashValue, primValueArray, false, db, tx);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&keyValueArray);
      varArrayListDestroy(&primValueArray);
      varArrayListDestroy(&hashIndexList);
      return rc;
    }
    varArrayListClear(keyValueArray);
  }
  varArrayListDestroy(&keyValueArray);
  varArrayListDestroy(&primValueArray);
  varArrayListDestroy(&hashIndexList);
  return GNCDB_SUCCESS;
}

/**
 * @description: 此函数接口主要是为了避免批量删除（比如delete语句）时，多次获取和构建重复资源
 * @param {GNCDB} *db 数据库实例指针
 * @param {TableSchema} *tableSchema 外部获取并传入的表schema
 * @param {BYTE} *tuple 待删除的tuple字节流
 * @param {varArrayList} *hashIndexList 对应表上的所有哈希索引实例指针列表
 * @param {varArrayList} *pkValueArray 外部创建的主键值列表
 * @param {varArrayList} *hkValueArray 外部创建的哈希索引键列表
 * @return {*}
 */
int executorBatchDeleteHashIndex(struct GNCDB *db, TableSchema *tableSchema, BYTE *tuple, varArrayList *hashIndexList,
    varArrayList *pkValueArray, varArrayList *hkValueArray, Transaction *tx)
{
  int        rc          = 0;
  HashIndex *hashIndex   = NULL;
  void      *keyValue    = NULL;
  int        hashValue   = 0;
  Column    *indexColumn = NULL;
  MetaPage  *metaPage    = NULL;
  int        prevCnt     = 0;
  int        postCnt     = 0;
  int        i;

  // 遍历所有哈希索引进行删除操作
  for (i = 0; i < hashIndexList->elementCount; i++) {
    hashIndex = varArrayListGetPointer(hashIndexList, i);
    rc        = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, hashIndex->tableName, db);
    prevCnt   = metaPage->keyTidPairCount;
    // 获取索引列的值
    indexColumn = (Column *)varArrayListGetPointer(hashIndex->index_columns, 0);
    rc          = leafTupleGetHashIndexKeyValue(hkValueArray, tuple, hashIndex->index_columns);
    keyValue    = varArrayListGetPointer(hkValueArray, 0);
    hashValue   = getHashValue(keyValue, indexColumn->fieldType);

    // 从哈希索引中删除
    rc = hashIndexDelete(hashIndex, hashValue, pkValueArray, db, tx);
    varArrayListClear(hkValueArray);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    postCnt = metaPage->keyTidPairCount;
    if (prevCnt == postCnt) {
      printf("哈希索引删除失败\n");
      printf("删除的主键值为：%d\n", *(int *)varArrayListGetPointer(pkValueArray, 0));
    }
  }
  varArrayListClear(hkValueArray);
  return GNCDB_SUCCESS;
}