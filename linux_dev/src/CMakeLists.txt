cmake_minimum_required(VERSION 3.10)
project(PROJECT_GNCDB C)

# 设置可执行文件输出目录
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)
set(CMAKE_BINARY_DIR ${CMAKE_SOURCE_DIR}/build)

# 编译子文件夹的CMakeLists.txt
add_subdirectory(cachemanager)
add_subdirectory(common)
add_subdirectory(queryprocess)
add_subdirectory(storage)
add_subdirectory(system)
add_subdirectory(tranmanager)
add_subdirectory(utils)
add_subdirectory(index)



# 获取当前路径下所有的.h/.c文件，并赋值给变量SRC_LIST中
# aux_source_directory(. SRC_LIST)
# add_executable(gncdb ${SRC_LIST})

# # find_program(iwyu_path NAMES include-what-you-use iwyu)
# # if(NOT iwyu_path)
# #   message(FATAL_ERROR "Could not find the program include-what-you-use")
# # else()
# #   set_property(TARGET gncdb PROPERTY C_INCLUDE_WHAT_YOU_USE ${iwyu_path})
# # endif()
# # target_link_options(gncdb PRIVATE -no-pie)

# # find_package(Thread REQUIRED)
# target_link_libraries(gncdb
    
#     gncdb_dbtest
#     gncdb_multicon
#     gncdb_concurrence
#     gncdb_tpch1
#     gncdb_demo
#     gncdb_performance
#     gncdb_dbpertest
#     gncdb_tpcctest

#     gncdb_common
#     gncdb_qp
#     gncdb_cachemanager
#     gncdb_tranmanager
#     gncdb_index
#     gncdb_storage
#     gncdb_system
#     gncdb_utils
#     )

# # 链接第三方库
# target_link_libraries(gncdb -lpthread)
# target_link_libraries(gncdb ${CMAKE_SOURCE_DIR}/malloc/mallocLib.a)
# target_link_libraries(gncdb m)
