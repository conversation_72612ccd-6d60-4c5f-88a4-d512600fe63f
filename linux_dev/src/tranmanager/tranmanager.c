/*
 * @Descripttion: 
 * @version: 
 * @Author: Alan
 * @Date: 2023-01-30 09:19:39
 * @LastEditors: Alan
 * @LastEditTime: 2023-01-30 11:00:35
 */
#include "tranmanager.h"
#include "utils.h"
#include "transaction.h"
/// <summary>
/// 
/// </summary>
/// <param name="transrMan"></param>
/// <param name="db"></param>
/// <param name="lockManager"></param>
/// <param name="logger"></param>
/// <returns></returns>
TransactionManager* txManagerConstruct(struct GNCDB* db)
{
	TransactionManager* transrMan = my_malloc(sizeof(TransactionManager));
	if (transrMan == NULL)
	{
		return NULL;
	}
	ReadWriteLockInit(&(transrMan->latch));
	ReadWriteLockInit(&(transrMan->tidLatch));
	transrMan->txMap = hashMapCreate(INTKEY, 0, NULL);
	transrMan->lockManager = lockManagerConstruct();
	transrMan->logger = loggerConstruct(db);
	transrMan->tid = 0;
	db->transactionManager = transrMan;

	return transrMan;
}

void txManagerDestroy(struct TransactionManager** txManager)
{
	hashMapDestroy(&(*txManager)->txMap);
	ReadWriteLockDestroy(&(*txManager)->latch);
	lockManagerDestroy(&(*txManager)->lockManager);
	loggerDestroy(&(*txManager)->logger);
	my_free(*txManager);

}

int transrManageGetMemPage(struct TransactionManager* txManager, HashMap** page_map, Transaction* tx)
{

	LOG(LOG_TRACE, "RLOCKing:%s", "txManager");
	ReadLock(&(txManager->latch));
	LOG(LOG_TRACE, "RLOCKend:%s", "txManager");

	
	if (tx->tidPidMap != NULL)
	{
		*page_map = tx->tidPidMap;
		ReadUnLock(&(txManager->latch));
		return GNCDB_SUCCESS;
	}
	
	LOG(LOG_TRACE, "RUNLOCKing:%s", "txManager");
	ReadUnLock(&(txManager->latch));
	LOG(LOG_TRACE, "RUNLOCKend:%s", "txManager");

	return GNCDB_NOT_FOUND;
}

int transrManageMenTransaction(struct TransactionManager* txManager, struct Transaction* tx)
{

	LOG(LOG_TRACE, "WLOCKIing:%s", "txManager");
	WriteLock(&(txManager->latch));
	LOG(LOG_TRACE, "WLOCKend:%s", "txManager");

	hashMapPut(txManager->txMap, &(tx->id), tx);

	WriteUnLock(&(txManager->latch));
	LOG(LOG_TRACE, "WUNLOCKend:%s", "txManager");

	return GNCDB_SUCCESS;
}
int transrManageDelTransaction(struct TransactionManager* txManager, struct Transaction* tx)
{

	LOG(LOG_TRACE, "WLOCKIing:%s", "txManager");
	WriteLock(&(txManager->latch));
	LOG(LOG_TRACE, "WLOCKend:%s", "txManager");

	hashMapRemove(txManager->txMap, &(tx->id));

	WriteUnLock(&(txManager->latch));
	LOG(LOG_TRACE, "WUNLOCKend:%s", "txManager");

	return GNCDB_SUCCESS;
}
int transrManageGetTransaction(struct TransactionManager* txManager, struct Transaction** tx, int tid)
{
	//ReadLock(&(txManager->latch));

	*tx = hashMapGet(txManager->txMap, &tid);

	//WriteUnLock(&(txManager->latch));
	return GNCDB_SUCCESS;
}

int transrManageGetPreLogger(struct TransactionManager* txManager, int** preLogger, struct Transaction* tx) {

	varArrayList* preLoggers = NULL;
	

	LOG(LOG_TRACE, "RLOCKing:%s", "txManager");
	ReadLock(&(txManager->latch));
	LOG(LOG_TRACE, "RLOCKend:%s", "txManager");


	preLoggers = tx->preLSN;

	*preLogger = varArrayListGet(preLoggers, preLoggers->elementCount - 1);

	ReadUnLock(&(txManager->latch));
	LOG(LOG_TRACE, "RUNLOCKend:%s", "txManager");

	return GNCDB_SUCCESS;
}
