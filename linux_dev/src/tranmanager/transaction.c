/*
 * @Descripttion: 
 * @version: 
 * @Author: Alan
 * @Date: 2023-01-30 09:19:39
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-26 15:45:53
 */
#include "transaction.h"
#include "btreepage.h"
#include "hashpage.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "os.h"
#include "tranmanager.h"
#include "pagepool.h"
#include "typedefine.h"
#include <string.h>


/// <summary>
/// 事务构造函数
/// </summary>
/// <param name="db"></param>
/// <returns></returns>
Transaction* transcationConstrcut(struct GNCDB* db) {
	/* 1.变量的定义 */
	int rc = 0;
	Transaction* tx = NULL;

	/* 2.f分配事务的内存空间 */
	tx = (Transaction*)my_malloc(sizeof(Transaction));
	if (tx == NULL){
		return NULL;
	}

	/* 3.事务初始化 */
	WriteLock(&(db->transactionManager->tidLatch));
	tx->id = db->transactionManager->tid++;
	WriteUnLock(&(db->transactionManager->tidLatch));
    tx->transactionManager = db->transactionManager;
	tx->status = GROWING;
	tx->isolationLevel = READ_COMMITTED;
	tx->preLSN = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
	tx->oldPageData = hashMapCreate(INTKEY, 0, NULL);
	if (tx->oldPageData == NULL) {
		return NULL;
	}
	tx->latchedPageSet = varArrayListCreate(DISORDER,BYTES_POINTER,0,NULL,NULL);
	if (tx->latchedPageSet == NULL) {
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->newCreatedPageSet = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
	if (tx->newCreatedPageSet == NULL) {
		varArrayListDestroy(&(tx->latchedPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->deletedPageSet = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (tx->deletedPageSet == NULL) {
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->overflowPageSet = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (tx->overflowPageSet == NULL) {
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		varArrayListDestroy(&(tx->deletedPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->tidPidMap = hashMapCreate(INTKEY, 0, NULL);
	if (tx->tidPidMap == NULL) {
		varArrayListDestroy(&(tx->deletedPageSet));
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		varArrayListDestroy(&(tx->overflowPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->txFlushMap = hashMapCreate(INTKEY, 0, NULL);
	if (tx->txFlushMap == NULL) {
		varArrayListDestroy(&(tx->deletedPageSet));
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		varArrayListDestroy(&(tx->overflowPageSet));
		hashMapDestroy(&(tx->oldPageData));
		hashMapDestroy(&(tx->txFlushMap));
		hashMapDestroy(&(tx->tidPidMap));
		return NULL;
	}
	/* 4.事务开始 */
	rc = transactionStart(tx, db);
	if (rc != GNCDB_SUCCESS)
	{
		return NULL;
	}
	
	return tx;
}

Transaction* transcationConstrcutSelect(GNCDB* db, bool isSelect) 
{
	/* 1.变量的定义 */
	int rc = 0;
	Transaction* tx = NULL;

	/* 2.f分配事务的内存空间 */
	tx = (Transaction*)my_malloc(sizeof(Transaction));
	if (tx == NULL){
		return NULL;
	}

	/* 3.事务初始化 */
	WriteLock(&(db->transactionManager->tidLatch));
	tx->id = db->transactionManager->tid++;
	WriteUnLock(&(db->transactionManager->tidLatch));
    tx->transactionManager = db->transactionManager;
	tx->status = GROWING;
	tx->isolationLevel = READ_COMMITTED;
	tx->preLSN = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
	tx->oldPageData = hashMapCreate(INTKEY, 0, NULL);
	if (tx->oldPageData == NULL) {
		return NULL;
	}
	tx->latchedPageSet = varArrayListCreate(DISORDER,BYTES_POINTER,0,NULL,NULL);
	if (tx->latchedPageSet == NULL) {
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->newCreatedPageSet = varArrayListCreate(DISORDER, sizeof(int), 0, NULL, NULL);
	if (tx->newCreatedPageSet == NULL) {
		varArrayListDestroy(&(tx->latchedPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->deletedPageSet = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (tx->deletedPageSet == NULL) {
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->overflowPageSet = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
	if (tx->overflowPageSet == NULL) {
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		varArrayListDestroy(&(tx->deletedPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->tidPidMap = hashMapCreate(INTKEY, 0, NULL);
	if (tx->tidPidMap == NULL) {
		varArrayListDestroy(&(tx->deletedPageSet));
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		varArrayListDestroy(&(tx->overflowPageSet));
		hashMapDestroy(&(tx->oldPageData));
		return NULL;
	}
	tx->txFlushMap = hashMapCreate(INTKEY, 0, NULL);
	if (tx->txFlushMap == NULL) {
		varArrayListDestroy(&(tx->deletedPageSet));
		varArrayListDestroy(&(tx->newCreatedPageSet));
		varArrayListDestroy(&(tx->latchedPageSet));
		varArrayListDestroy(&(tx->overflowPageSet));
		hashMapDestroy(&(tx->oldPageData));
		hashMapDestroy(&(tx->txFlushMap));
		hashMapDestroy(&(tx->tidPidMap));
		return NULL;
	}
	/* 4.事务开始 */
	if (isSelect) {
		rc = transrManageMenTransaction(db->transactionManager, tx);
		if (rc != GNCDB_SUCCESS) {
			varArrayListDestroy(&(tx->deletedPageSet));
			varArrayListDestroy(&(tx->newCreatedPageSet));
			varArrayListDestroy(&(tx->latchedPageSet));
			varArrayListDestroy(&(tx->overflowPageSet));
			hashMapDestroy(&(tx->oldPageData));
			hashMapDestroy(&(tx->txFlushMap));
			hashMapDestroy(&(tx->tidPidMap));
			return NULL;
		}
	} else {
		rc = transactionStart(tx, db);
		if (rc != GNCDB_SUCCESS) {
			varArrayListDestroy(&(tx->deletedPageSet));
			varArrayListDestroy(&(tx->newCreatedPageSet));
			varArrayListDestroy(&(tx->latchedPageSet));
			varArrayListDestroy(&(tx->overflowPageSet));
			hashMapDestroy(&(tx->oldPageData));
			hashMapDestroy(&(tx->txFlushMap));
			hashMapDestroy(&(tx->tidPidMap));
			return NULL;
		}
	}


	return tx;
}


/// <summary>
/// 
/// </summary>
/// <param name="tx"></param>
void transcationDestroy(struct Transaction** tx, struct GNCDB* db) {
	HashMapIterator* iterator = NULL;
	TidOld* tidOld = NULL;
	Page* page = NULL;
	// PageControlNode * pcnInFlushMap = NULL;   // 获取缓冲池中的flushMap的PCN
	// int * pid = NULL;
    int rc = 0;
	
	/* 1.判断参数是否为空 */
	if (tx == NULL || *tx == NULL) {
		return;
		/* 2.释放内存空间 */
	}
	varArrayListDestroy(&(*tx)->preLSN);

	/* 刷新pagecontrolmap */
	rc = pagePoolAddFlushNew(db, *tx);
	if (rc)
	{
		// return ;
	}

	// 释放oldPageData的页,此时若要回滚也已经回滚完，可以安全free备份的页
	if ((*tx)->oldPageData->entryCount != 0)
	{
		iterator = createHashMapIterator((*tx)->oldPageData);
		if (iterator == NULL)
		{
			return;
		}			
		/* 循环遍历将每个深拷贝的页都释放空间 */
		while (hasNextHashMapIterator(iterator))
		{
			iterator = nextHashMapIterator(iterator);
			if (iterator == NULL)
			{
				return;
			}
			// pid = (int *)iterator->entry->key;
			tidOld = iterator->entry->value;
			page = tidOld->oldPage;

			if (page->pageType == LEAF_PAGE || page->pageType == INTERNAL_PAGE)
			{
				btreePageDestroyMalloc((BtreePage**) & page);
			}
			else if (page->pageType == OVERFLOW_PAGE)
			{
				overflowPageDestroyMalloc((OverflowPage**) & page);
			}
			else if (page->pageType == FREE_PAGE)
			{
				freePageDestroyMalloc((FreePage**) & page);
			}
			else if (page->pageType == META_PAGE) {
				MetaPageDestroyMalloc((MetaPage**)&page);
			}
			else if (page->pageType == BUCKET_PAGE) {
				BucketPageDestroyMalloc((BucketPage**)&page);
			}
			else if (page->pageType == HASH_OVERFLOW_PAGE) {
				HashOverflowPageDestroyMalloc((HashOverflowPage**)&page);
			}
      my_free(tidOld);
		}
		freeHashMapIterator(&iterator);
	}

	/* 释放所有的逻辑锁 */
    rc = lockManagerReleaseAllLock(db->transactionManager->lockManager, *tx);
    if(rc != GNCDB_SUCCESS)
    {
		// transcationDestroy(tx, db);
        // return ;
    }

    rc = cancelTargetedPageofTransaction(db, *tx);
    if (rc)
    {
        // transcationDestroy(tx, db);
        // return ;
    }
	// if((*tx)->tidPidMap->cnt!=0)
	// 	printf("%d\n",((*tx)->tidPidMap->sum)/((*tx)->tidPidMap->cnt));
	hashMapDestroy(&(*tx)->oldPageData);
	varArrayListDestroy(&(*tx)->latchedPageSet);
	varArrayListDestroy(&(*tx)->newCreatedPageSet);
	varArrayListDestroy(&(*tx)->deletedPageSet);
	varArrayListDestroy(&(*tx)->overflowPageSet);
	hashMapDestroy(&(*tx)->tidPidMap);
	hashMapDestroy(&(*tx)->txFlushMap);
	my_free(*tx);
	*tx = NULL;
}
/// <summary>
/// 记录加锁的页
/// </summary>
/// <param name="tx"></param>
/// <param name="page"></param>
/// <returns></returns>
int addIntoLatchedPageSet(struct Transaction* tx, void* page) {
	/* 1.变量的定义 */
	int rc = 0;

	/* 2.判断参数是否为空 */
	if (tx == NULL) {   /* 这里page可以为NULL，因为btreeTable中的锁被添加进来的时候是以NULL为标记的 */
		return GNCDB_PARAMNULL;;
	}

	/* 3.添加到latchedPageSet中 */
	rc = varArrayListAddPointer(tx->latchedPageSet, page);
	return rc;
}

/// <summary>
/// 返回latchedPageSet
/// </summary>
/// <param name="tx"></param>
/// <returns></returns>
varArrayList* getLatchedPageSet(struct Transaction* tx) {
	/* 1.判断参数是否为空 */
	if (tx == NULL) {
		return NULL;
	}

	/* 2.返回latchedPageSet */
	return tx->latchedPageSet;
}
int addIntoNewCreatedPageSet(struct Transaction* tx, struct Page* page) {
	/* 1.变量的定义 */
	int rc = 0;

	/* 2.判断参数是否为空 */
	if (tx == NULL || page == NULL) {
		return GNCDB_PARAMNULL;
	}

	/* 3.添加到newCreatedPageSet中 */
	rc = varArrayListAdd(tx->newCreatedPageSet, &(page->id));
	return rc;
}

varArrayList* getNewCreatedPageSet(struct Transaction* tx) {
	/* 1.判断参数是否为空 */
	if (tx == NULL) {
		return NULL;
	}

	/* 2.返回newCreatedPageSet */
	return tx->newCreatedPageSet;
}
/// <summary>
/// 记录需要删除的页
/// </summary>
/// <param name="tx"></param>
/// <param name="page"></param>
/// <returns></returns>
int addIntoDeletedPageSet(struct Transaction* tx, struct Page* page) {
	/* 1.变量的定义 */
	int rc = 0;

	/* 2.判断参数是否为空 */
	if (tx == NULL || page == NULL) {
		return GNCDB_PARAMNULL;
	}

	/* 3.添加到deletedPageSet中 */
	rc = varArrayListAddPointer(tx->deletedPageSet, page);
	return rc;
}

/// <summary>
/// 返回deletedPageSet
/// </summary>
/// <param name="tx"></param>
/// <returns></returns>
varArrayList* getDeletedPageSet(struct Transaction* tx) {
	/* 1.判断参数是否为空 */
	if (tx == NULL) {
		return NULL;
	}

	/* 2.返回deletedPageSet */
	return tx->deletedPageSet;
}

/// @brief 添加到溢出页集合中
/// @param tx 
/// @param page 
/// @return 
int addIntoOverflowPageSet(struct Transaction* tx, struct Page* page){
	/* 1.变量的定义 */
	int rc = 0;

	/* 2.判断参数是否为空 */
	if (tx == NULL || page == NULL) {
		return GNCDB_PARAMNULL;
	}

	/* 3.添加到overflowPageSet中 */
	rc = varArrayListAddPointer(tx->overflowPageSet, page);
	return rc;
}

/// @brief 返回溢出页集合
/// @param tx 
/// @return 
varArrayList* getOverflowPageSet(struct Transaction* tx){
	/* 1.判断参数是否为空 */
	if (tx == NULL) {
		return NULL;
	}

	/* 2.返回overflowPageSet */
	return tx->overflowPageSet;
}

/// <summary>
/// 查询该事务操作页面list存不存在pageid，不存在则添加
/// </summary>
/// <param name="txManager"></param>
/// <param name="pageId"></param>
/// <param name="tid"></param>
/// <returns></returns>
int transactionMemTidPid(int pageId, struct Transaction* tx)
{
	// int i = 0;
	// int* tempId = NULL;
	int rc = 0;
	// varArrayList* array = NULL;

	// LOG(LOG_TRACE, "WLOCKing:txManager tid=%d", tx->id);
	// WriteLock(&(tx->transactionManager->latch));
	// LOG(LOG_TRACE, "WLOCKend:txManager tid=%d", tx->id);

	/* array若空，则新创建 */
	if(tx->tidPidMap == NULL)
	{
		tx->tidPidMap = hashMapCreate(INTKEY, 0, NULL);
		if (tx->tidPidMap == NULL) {
			return GNCDB_SPACE_LACK;
		}
	}
	/* 先查询该事务操作页面list存不存在pageid，不存在则添加 */
	if (hashMapExists(tx->tidPidMap, &pageId))
	{
		return GNCDB_SUCCESS;
	}
	/* 事务操作页面list存不存在pageid，则添加 */
	rc = hashMapPut(tx->tidPidMap, &pageId, &pageId);
	return rc;
}
/// <summary>
/// 删除该事务操作页面list存不存在pageid，确保list无这个pageid
/// </summary>
/// <param name="txManager"></param>
/// <param name="pageId"></param>
/// <param name="tid"></param>
/// <returns></returns>
int transcationDelTidPid(int pageId, struct Transaction* tx)
{
	int rc = 0;

	// LOG(LOG_TRACE, "WLOCKing:txManager tid=%d", tx->id);
	// WriteLock(&(txManager->latch));
	// LOG(LOG_TRACE, "WLOCKend:txManager tid=%d", tx->id);

	/* array若空，直接返回成功 */
	if (tx->tidPidMap == NULL)
	{
		// WriteUnLock(&(txManager->latch));
		return GNCDB_SUCCESS;
	}
	rc = hashMapRemove(tx->tidPidMap, &pageId);
	return rc;
	// WriteUnLock(&(txManager->latch));
	// LOG(LOG_TRACE, "WUNLOCKend:txManager tid=%d", tx->id);
}

HashMap* transactionGetTidPidMap(struct Transaction* tx) 
{
	return tx->tidPidMap;
}
// 更新当前事务的状态
int transactionUpdateStatus(struct Transaction* transaction, TransactionStatus status){
	if (transaction == NULL) {
		return GNCDB_PARAMNULL;
	}

	transaction->status = status;
	if(status == ABORTED){
		PRINT("Transaction %d has been aborted\n", transaction->id);
	}
	return GNCDB_SUCCESS;
}

/// <summary>
/// 
/// </summary>
/// <param name="transaction"></param>
/// <returns></returns>
int transactionGetStatus(struct Transaction* transaction) {
	return transaction->status;
}


int transactionAddOldData(struct GNCDB* db, struct Transaction* tx, int pid,  void* old) {

	TidOld* oldData = NULL;
	int rc = 0;
	if (old == NULL) {
		return GNCDB_PARAMNULL;
	}
	if(!transactionExsitOldData(db, tx, pid)){
		return GNCDB_OLD_PAGE_EXIST;
	}

	// * 备份脏页，维护事务的flushMap和缓冲池的flushMap的pageControlNode
	rc = pagePoolAddFlushOld(db, tx, pid, old);
	if (rc != GNCDB_SUCCESS) {
		return rc;
	}

	// * 备份事务的oldData以便回滚
	oldData = my_malloc(sizeof(TidOld));
	if (oldData == NULL)
	{
		return GNCDB_MEM;
	}
	oldData->tid = tx->id;
	oldData->oldPage = old;
	hashMapPut(tx->oldPageData, &pid, oldData);
	
	WriteLock(&(db->pagePool->latch));
	setPageStatusPinUp(db->pagePool, pid, NULL);
	WriteUnLock(&(db->pagePool->latch));

	return GNCDB_SUCCESS;

}
int transactionExsitOldData(struct GNCDB* db, struct Transaction* tx, int pid) {

	TidOld* beforData = NULL;
	beforData = (TidOld*)hashMapGet(tx->oldPageData, &pid);
	/* 在一个事务中 已经有了oldpage，不再进行添加 */
	if (beforData != NULL)
	{
		if (beforData->tid == tx->id)
		{
			return GNCDB_SUCCESS;
		}
	}
	return GNCDB_NOT_FOUND;
}
void swapPointer(void** ptr1, void** ptr2){
	void* temp = *ptr1;
	*ptr1 = *ptr2;
	*ptr2 = temp; 
}

// // 字符串交换内容
// int swapStringContents(char **str1, char **str2) {
//     char *temp = my_malloc(strlen(*str1) + 1);
//     if (temp == NULL) {
//         fprintf(stderr, "Memory allocation failed\n");
//         return GNCDB_MEM;
//     }

//     strcpy(temp, *str1);
//     strcpy(*str1, *str2);
//     strcpy(*str2, temp);

//     my_free(temp);
// }

int setBeforeImage(struct Transaction* tx, struct GNCDB* db)
{
	TidOld* oldData = NULL;
	int* pageId = NULL;
	HashMapIterator* iteratorMap = NULL;
	PageType* type = NULL;
	Page* newPage = NULL;
	Page* oldpage = NULL;
	BtreePage* newBtreePage = NULL;
	BtreePage* oldBtreePage = NULL;
	OverflowPage* newOverflowPage = NULL;
	OverflowPage* oldOverflowPage = NULL;
	FreePage* freePage = NULL;
	MetaPage* newMetaPage = NULL;
	MetaPage* oldMetaPage = NULL;
	BucketPage* newBucketPage = NULL;
	BucketPage* oldBucketPage = NULL;
	HashOverflowPage* newHashOverflowPage = NULL;
	HashOverflowPage* oldHashOverflowPage = NULL;
	int overflowLen = 0;
	int len = 0;
	// PageControlNode * pcnInPCNMap = NULL;

	iteratorMap = createHashMapIterator(tx->oldPageData);
	if (iteratorMap == NULL)
	{
		return GNCDB_MAP_ITERATOR_FALSE;
	}
	if (tx->oldPageData->entryCount != 0)
	{
		/* 循环遍历每个页 */
		while (hasNextHashMapIterator(iteratorMap))
		{
			iteratorMap = nextHashMapIterator(iteratorMap);
			if (iteratorMap == NULL)
			{
				freeHashMapIterator(&iteratorMap);
				return GNCDB_MAP_NEXT_NOT_FOUND;
			}
			pageId = (int*)iteratorMap->entry->key;
			oldData = (TidOld*)iteratorMap->entry->value;
			type = (PageType*)oldData->oldPage;
			if (type == NULL)
			{
				freeHashMapIterator(&iteratorMap);
				return GNCDB_NOT_FOUND;
			}

      LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
      WriteLock(&(db->pagePool->latch));
      LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

			newPage = (Page*)hashMapGet(db->pagePool->pageMap, pageId);

			/* 当前页复制旧页内容，内存和页面指针不变 */
			if(oldData != NULL){
				oldpage = (Page*)oldData->oldPage;

				/* 先复制一些page结构体中通用的结构：pData、pNext、pageType */
				memcpy(newPage->pData, oldpage->pData, db->pageCurrentSize);
				newPage->pNext = oldpage->pNext;
				newPage->pageType = oldpage->pageType;

				/* 再根据不同的类型分别对不同页结构体中的字段进行还原 */
				if(oldpage->pageType == INTERNAL_PAGE || oldpage->pageType == LEAF_PAGE){
					newBtreePage = (BtreePage*)newPage;
					oldBtreePage = (BtreePage*)oldpage;
					newBtreePage->nextPageId = oldBtreePage->nextPageId;
					newBtreePage->entryNum = oldBtreePage->entryNum;
					newBtreePage->keyLength = oldBtreePage->keyLength;
					newBtreePage->prevInsertIndex = oldBtreePage->prevInsertIndex;
					strcpy(newBtreePage->tableName, oldBtreePage->tableName);
				} else if(oldpage->pageType == OVERFLOW_PAGE){
					newOverflowPage = (OverflowPage*)newPage;
					oldOverflowPage = (OverflowPage*)oldpage;
					newOverflowPage->nextPageId = oldOverflowPage->nextPageId;
					readInt(&overflowLen, (BYTE *)oldOverflowPage->overflowData, &len);
					overflowLen += INT_SIZE;
					memcpy(newOverflowPage->overflowData, oldOverflowPage->overflowData, overflowLen);
				} else if(oldpage->pageType == FREE_PAGE){
					freePage = (FreePage*)oldpage;
					WriteLock(&db->latch);
					freePage->nextPageId = db->firstFreePid;
					db->firstFreePid = freePage->page.id;

					hashMapPut(db->pagePool->pageMap, pageId, freePage);
					oldData->oldPage = newPage;
					WriteUnLock(&db->latch);
				} else if (oldpage->pageType == META_PAGE) {
					newMetaPage = (MetaPage*)newPage;
					oldMetaPage = (MetaPage*)oldpage;
					newMetaPage->hashFunctionId = oldMetaPage->hashFunctionId;
					newMetaPage->keyTidPairCount = oldMetaPage->keyTidPairCount;
					newMetaPage->fillFactor = oldMetaPage->fillFactor;
					newMetaPage->highMask = oldMetaPage->highMask;
					newMetaPage->lowMask = oldMetaPage->lowMask;
					newMetaPage->bucketCount = oldMetaPage->bucketCount;
					newMetaPage->maxBucketNumber = oldMetaPage->maxBucketNumber;
					newMetaPage->splitCount = oldMetaPage->splitCount;
				} else if (oldpage->pageType == BUCKET_PAGE) {
					newBucketPage = (BucketPage*)newPage;
					oldBucketPage = (BucketPage*)oldpage;
					newBucketPage->bucketId = oldBucketPage->bucketId;
					newBucketPage->keyTidPairCount = oldBucketPage->keyTidPairCount;
					newBucketPage->firstOverflowPageId = oldBucketPage->firstOverflowPageId;
					newBucketPage->lastOverflowPageId = oldBucketPage->lastOverflowPageId;
					newBucketPage->primaryKeyLenth = oldBucketPage->primaryKeyLenth;
				} else if (oldpage->pageType == HASH_OVERFLOW_PAGE) {
					newHashOverflowPage = (HashOverflowPage*)newPage;
					oldHashOverflowPage = (HashOverflowPage*)oldpage;
					newHashOverflowPage->bucketId = oldHashOverflowPage->bucketId;
					newHashOverflowPage->keyTidPairCount = oldHashOverflowPage->keyTidPairCount;
					newHashOverflowPage->prevPageId = oldHashOverflowPage->prevPageId;
					newHashOverflowPage->nextPageId = oldHashOverflowPage->nextPageId;
					newHashOverflowPage->primaryKeyLenth = oldHashOverflowPage->primaryKeyLenth;
				}

			}
			/* 当前页是分裂出的 */
			if (oldData == NULL)
			{
				WriteLock(&db->latch);
				// freePage = freePageConstructWithOutData(*pageId, db->firstFreePid);
				freePage = freePageMallocConstructWithOutData(db->pageCurrentSize, *pageId, db->firstFreePid);
				WriteUnLock(&db->latch);
				hashMapPut(db->pagePool->pageMap, pageId, freePage);
			}
//			else
//			{
//				hashMapPut(db->pagePool->pageMap, pageId, oldData->oldPage);
//			}
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(db->pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

		}
	}

	freeHashMapIterator(&iteratorMap);
	return GNCDB_SUCCESS;
}
/// <summary>
/// 事务开始
/// </summary>
/// <param name="tx"></param>
/// <param name="db"></param>
/// <param name="level">隔离级别</param>
/// <returns></returns>
int transactionStart(struct Transaction* tx, struct GNCDB *db) {
	
	int rc = 0;
	rc = transrManageMenTransaction(db->transactionManager, tx);
	if (rc != GNCDB_SUCCESS)
	{
		return rc;
	}
	rc = loggerBegin(db->transactionManager->logger, db, tx);
	if (rc != GNCDB_SUCCESS)
	{
		return rc; 
	}
	return GNCDB_SUCCESS;
}

/**
 * @description: 
 * 1.将需要删除的页转换为freePage
 * 2.将删除的overflowPage转换为freePage
 * 3.写commit日志（其中将修改的页的数据写update日志）
 * 4.清理该事务相关资源
 * 注意：commit时，并不进行刷盘，而是只有触发了写检查点日志再统一刷盘
 * @param {Transaction*} tx
 * @param {GNCDB*} db
 * @return {*}
 */
int transactionCommit(struct Transaction* tx, struct GNCDB* db) {

	int rc = 0;
//    HashMapIterator* iterator = NULL;
	// int i = 0, j = 0;
	// Page* page = NULL;
	// int* pid = 0;
	// LockTableEntry* lockTableEntry = NULL;
	// LockEntry* lockEntry = NULL;

	// 如果事务已经提交
	if (tx->status == COMMITTED)
	{
		transcationDestroy(&tx,db);
		return GNCDB_SUCCESS;
	}

//    printf("commit tx-id: %d -| ", (tx)->id);
//    iterator = createHashMapIterator((tx)->txFlushMap);
//    while(hasNextHashMapIterator(iterator))
//    {
//        iterator = nextHashMapIterator(iterator);
//        printf("%d  ", *(int *)(iterator->entry->key));
//    }
//    printf("end\n");
//    freeHashMapIterator(&iterator);

//    printf("lock tx-id: %d -| ", tx->id);
//    for(i = 0; i < tx->tidPidArray->elementCount; ++i)
//    {
//        pid = varArrayListGet(tx->tidPidArray, i);
//        lockTableEntry = getLockTableEntry(db->transactionManager->lockManager, *pid);
//        printf("%d  ", *pid);
//        for(j = 0; j < lockTableEntry->lockEntryArray->elementCount; ++j)
//        {
//            lockEntry = varArrayListGetPointer(lockTableEntry->lockEntryArray, j);
//            printf("%d-%d ", lockEntry->tid, lockEntry->lockType);
//        }
//    }
//    printf("\n");

//    printf("lock tx-id: %d -| ", tx->id);
//    for(i = 0; i < tx->tidPidArray->elementCount; ++i)
//    {
//        pid = varArrayListGet(tx->tidPidArray, i);
//        lockTableEntry = getLockTableEntry(db->transactionManager->lockManager, *pid);
//        printf("%d  ", *pid);
//        for(j = 0; j < lockTableEntry->lockEntryArray->elementCount; ++j)
//        {
//            lockEntry = varArrayListGetPointer(lockTableEntry->lockEntryArray, j);
//            printf("%d-%d ", lockEntry->tid, lockEntry->lockType);
//        }
//    }
//    printf("\n");

    /* 统一删除需要转化为freepage的page */
    rc = deletePages(db->pagePool, tx, db);
    if (rc != GNCDB_SUCCESS) {
        //return rc;
    }
    rc = deleteOverflowPages(db->pagePool, tx, db);
    if(rc != GNCDB_SUCCESS){
        //return rc;
    }

	tx->status = COMMITTED;
	rc = loggerCommit(db->transactionManager->logger, tx, db);
	if (rc )
	{
		transcationDestroy(&tx, db);
		return rc;
	}
//	rc = cancelTargetedPageofTransaction(db, tx);
//	if (rc)
//	{
//		transcationDestroy(&tx, db);
//		return rc;
//	}
	rc = transrManageDelTransaction(db->transactionManager, tx);
	if (rc != GNCDB_SUCCESS)
	{
		transcationDestroy(&tx, db);
		return rc;
	}
	transcationDestroy(&tx, db);
	return GNCDB_SUCCESS;
}

int transactionCommitSelect(struct Transaction* tx, struct GNCDB* db, bool isSelect) {

	int rc = 0;

	// 如果事务已经提交
	if (tx->status == COMMITTED)
	{
		transcationDestroy(&tx,db);
		return GNCDB_SUCCESS;
	}

  /* 统一删除需要转化为freepage的page */
  rc = deletePages(db->pagePool, tx, db);
  if (rc != GNCDB_SUCCESS) {
      //return rc;
  }
  rc = deleteOverflowPages(db->pagePool, tx, db);
  if(rc != GNCDB_SUCCESS){
      //return rc;
  }

	tx->status = COMMITTED;
	if (!isSelect) {
		rc = loggerCommit(db->transactionManager->logger, tx, db);
		if (rc )
		{
			transcationDestroy(&tx, db);
			return rc;
		}
	}

	rc = transrManageDelTransaction(db->transactionManager, tx);
	if (rc != GNCDB_SUCCESS)
	{
		transcationDestroy(&tx, db);
		return rc;
	}
	transcationDestroy(&tx, db);
	return GNCDB_SUCCESS;
}
/// <summary>
/// 
/// </summary>
/// <param name="tx"></param>
/// <returns></returns>
int transactionRollback(struct Transaction* tx, struct GNCDB* db){
    int rc = 0;

	tx->status = ABORTED;
	rc = loggerRollback(db->transactionManager->logger, tx, db);
	if (rc)
	{
		// return rc;
	}
    rc = releaseWLatches(db->pagePool, tx, false, NULL);
    if (rc != GNCDB_SUCCESS)
    {
        // return rc;
    }
//	rc = cancelTargetedPageofTransaction(db, tx);
//	if (rc)
//	{
//		return rc;
//	}
	rc = transrManageDelTransaction(db->transactionManager, tx);
	if (rc != GNCDB_SUCCESS)
	{
		// return rc;
	}
	transcationDestroy(&tx, db);
	return GNCDB_SUCCESS;
}

/**
 * @description: 添加参数判断是否是select事务回滚，若是，不需要写日志
 * @param {Transaction*} tx
 * @param {GNCDB*} db
 * @param {bool} isSelect
 * @return {*}
 */
int transactionRollbackSelect(struct Transaction* tx, struct GNCDB* db, bool isSelect) {
	int rc = 0;

	tx->status = ABORTED;
	if (!isSelect) {
		rc = loggerRollback(db->transactionManager->logger, tx, db);
	}
	rc = releaseWLatches(db->pagePool, tx, false, NULL);
	rc = transrManageDelTransaction(db->transactionManager, tx);
	transcationDestroy(&tx, db);
	return rc;
}

/// 判断当前事务是否修改该页
/// \param tx
/// \param pageId
/// \return
bool transactionExistPage(struct Transaction* tx, int pageId)
{	
	if(tx->oldPageData == NULL){
		return false;
	}
    return hashMapExists(tx->oldPageData, (void*)&pageId);
}
