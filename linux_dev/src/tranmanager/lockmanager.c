#include "lockmanager.h"
#include <errno.h>
#include "transaction.h"
#include "btreepage.h"
#include "typedefine.h"
/**************************************************************************LockEntry******************************************************************/

int lockEntryCompare(struct varArrayList *array, void *data1, void *data2)
{
  LockEntry **pLockEntry1 = (LockEntry **)data1;
  LockEntry **pLockEntry2 = (LockEntry **)data2;
  if ((*pLockEntry1)->tid == (*pLockEntry2)->tid) {
    return 0;
  } else {
    return 1;
  }
}

/**
 * @brief LockEntry的创建
 * @param tid
 * @param lockType 锁的类型
 * @param isWait 当前加锁请求是否被授予
 * @return 返回LockEntry指针
 */
struct LockEntry *lockEntryConstruct(int tid, LockType lockType, bool isWait)
{
  /* 1.变量的定义 */
  LockEntry *lockEntry = NULL;

  /* 2.创建 */
  lockEntry = my_malloc(sizeof(LockEntry));
  if (lockEntry == NULL) {
    return NULL;
  }

  lockEntry->tid      = tid;
  lockEntry->lockType = lockType;
  lockEntry->isWait   = isWait;
  lockEntry->cnt      = 0;

  return lockEntry;
}

/**
 * @brief LockEntry的销毁
 * @param lockEntry
 */
void lockEntryDestroy(struct LockEntry **lockEntry)
{
  /* 1.判断参数是否为空 */
  if (lockEntry == NULL) {
    return;
  }

  /* 2.销毁变量 */
  my_free(*lockEntry);
  *lockEntry = NULL;
}

/****************************************************************************LockTableEntry******************************************************************/

/**
 * @brief LockTableEntry的创建
 */
struct LockTableEntry *lockTableEntryConstruct()
{
  /* 1.变量的定义 */
  LockTableEntry *lockTableEntry = NULL;

  /* 2.创建 */
  lockTableEntry = my_malloc(sizeof(LockTableEntry));
  if (lockTableEntry == NULL) {
    return NULL;
  }

  // UNKNOW仅仅是初始化或者当前锁表项为空是的一个标识符
  lockTableEntry->upgrading = -1;

  lockTableEntry->lockEntryArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, lockEntryCompare, NULL);
  if (lockTableEntry->lockEntryArray == NULL) {
    return NULL;
  }
  // pthread_cond_init(&lockTableEntry->cond, NULL);
  // pthread_mutex_init(&(lockTableEntry->latch), NULL);
  condVariable_Init(&lockTableEntry->semaphore); /* @todo 后续如果有多个条件变量再改name */
  condVariable_MutexInit(&lockTableEntry->semaphore);

  return lockTableEntry;
}

/**
 * @brief LockTableEntry的销毁
 * @param lockTableEntry
 */
void lockTableEntryDestroy(struct LockTableEntry **lockTableEntry)
{
  /* 1.判断参数是否为空 */
  if (lockTableEntry == NULL) {
    return;
  }

  /* 2.销毁变量 */
  // pthread_mutex_destroy(&((*lockTableEntry)->latch));
  // pthread_cond_destroy(&((*lockTableEntry)->cond));
  condVariable_Destroy(&(*lockTableEntry)->semaphore);
  condVariable_MutexDestroy(&(*lockTableEntry)->semaphore);
  varArrayListDestroy(&((*lockTableEntry)->lockEntryArray));
  my_free(*lockTableEntry);
  *lockTableEntry = NULL;
}

/****************************************************************************LockManager******************************************************************/
/**
 * @brief LockManager的创建
 * @return 返回LockManager指针
 */
LockManager *lockManagerConstruct()
{
  /* 1.变量的定义 */
  LockManager *lockManager = NULL;

  /* 2.创建 */
  lockManager = my_malloc(sizeof(LockManager));
  if (lockManager == NULL) {
    return NULL;
  }
  ReadWriteLockInit(&lockManager->latch);

  lockManager->pageTransactionLock = hashMapCreate(INTKEY, 6000, 0);
  if (lockManager->pageTransactionLock == NULL) {
    ReadWriteLockDestroy(&lockManager->latch);
    my_free(lockManager);
    return NULL;
  }

  return lockManager;
}

/**
 * @brief LockManager的销毁
 * @param LockManager
 */
void lockManagerDestroy(struct LockManager **lockManager)
{
  HashMapIterator *pageTransactionLockIterator = NULL;
  LockTableEntry  *lockTableEntry              = NULL;

  /* 1.判断参数是否为空 */
  if (lockManager == NULL) {
    return;
  }

  /* 2.可能存在锁管理器中的某些锁表项是空的，但该结构体还是需要释放内存的 */
  pageTransactionLockIterator = createHashMapIterator((*lockManager)->pageTransactionLock);
  if (pageTransactionLockIterator == NULL) {
    return;
  }
  while (hasNextHashMapIterator(pageTransactionLockIterator)) {
    pageTransactionLockIterator = nextHashMapIterator(pageTransactionLockIterator);
    lockTableEntry              = pageTransactionLockIterator->entry->value;
    if (lockTableEntry != NULL) {
      lockTableEntryDestroy(&lockTableEntry);
    }
  }
  freeHashMapIterator(&pageTransactionLockIterator);

  /* 3.销毁变量 */
  ReadWriteLockDestroy(&((*lockManager)->latch));
  hashMapDestroy(&((*lockManager)->pageTransactionLock));
  my_free(*lockManager);
  *lockManager = NULL;

  return;
}

/**
 * @brief
 * 获取指定的锁（因为实现的事务读已提交这个隔离级别，那么在加锁时：GROWING阶段是可以加读锁和写锁，SHRINKING阶段是可以加读锁的）
 * @param LockManager
 * @param tx
 * @param lockType
 * @return 返回是否获取锁成功
 */

int lockManagerAcquireLock(struct LockManager *lockManager, struct Transaction *tx, int pid, LockType lockType)
{
  /* 1.变量的定义 */
  int             rc             = 0;
  int             index          = 0;
  LockEntry      *lockEntry      = NULL;
  LockTableEntry *lockTableEntry = NULL;
  // struct timespec ts;
  int ret = 0;

  /* 2.判断参数是否为空 */
  if (lockManager == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.根据当前事务的状态，判断是否符合加锁的条件 */
  if (tx->status == COMMITTED || tx->status == ABORTED) {
    return GNCDB_LOCK_FAIL;
  }
  // printf("lockManagerAcquireLock:txid : %d pid=%d\n",tx->id, pid);
  /* 4.事务加锁分为两个阶段，GROWING和SHRINKING两个阶段分开考虑 */
  if (tx->status == GROWING) {

    /* 4.1.获取指定页锁的等待队列并加锁*/
    lockTableEntry = getLockTableEntry(lockManager, pid);
    if (lockTableEntry == NULL) {
      return GNCDB_NOT_FOUND;
    }

    LOG(LOG_TRACE, "SLOCKing:lockTableEntry");
    // pthread_mutex_lock(&lockTableEntry->latch);
    condVariable_MutexLock(&lockTableEntry->semaphore);
    LOG(LOG_TRACE, "SLOCKend:lockTableEntry");

    /* 4.2.先将锁请求添加到对应页的锁等待队列中 */
    if (updateLock(lockManager, tx, lockTableEntry, lockType) != GNCDB_SUCCESS) {
      LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
      // pthread_mutex_unlock(&lockTableEntry->latch);
      condVariable_MutexUnLock(&lockTableEntry->semaphore);
      LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
      return GNCDB_LOCK_FAIL;
    }

    /* 4.2.获取指定页锁的等待队列并加锁*/
    // lockTableEntry = getLockTableEntry(lockManager,pid);
    // if (lockTableEntry == NULL) {
    // 	return GNCDB_NOT_FOUND;
    // }

    /* 4.3.根据等待队列判断锁是否能够被授予，如果不能被授予，则阻塞当前事务的锁请求，直到锁被授予或者是超时被终止 */
    while (grantLock(lockManager, tx, lockTableEntry, lockType, pid) != GNCDB_SUCCESS) {
      /* 4.3.1.阻塞 */
      // clock_gettime(CLOCK_REALTIME, &ts);
      // ts.tv_sec += 5; // 设置5秒的超时时间
      // ret = pthread_cond_timedwait(&lockTableEntry->cond, &lockTableEntry->latch, &ts);
      ret = condVariable_Wait(&lockTableEntry->semaphore);
#if defined(__linux__) || defined(_WIN32)
      if (ret == ETIMEDOUT)
#else
      /* @todo 增加天脉系统的使用方式，平台测试机上添加 */
      if (ret == ETIMEDOUT)
#endif
      {
        for (index = 0; index < lockTableEntry->lockEntryArray->elementCount; ++index) {
          lockEntry = varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
          if (lockEntry == NULL) {
            break;
          }
          if (tx->id == lockEntry->tid) {
            rc = varArrayListRemovePointer(lockTableEntry->lockEntryArray, lockEntry);
            if (rc != GNCDB_SUCCESS) {
              LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
              // pthread_mutex_unlock(&lockTableEntry->latch);
              condVariable_MutexUnLock(&lockTableEntry->semaphore);
              LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
              return rc;
            }
            lockEntryDestroy(&lockEntry);
            break;
          }
        }
        LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
        // pthread_mutex_unlock(&lockTableEntry->latch);
        condVariable_MutexUnLock(&lockTableEntry->semaphore);
        LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
        return GNCDB_LOCK_FAIL;
      }

      /* 4.3.2.当阻塞的事务被唤醒了之后，先要判断自己的状态，如果没有被中止，才能继续获取锁 */
      if (tx->status == ABORTED) { /* 锁申请失败，意味着需要将当前pid上的锁申请从请求队列中踢出，之前已经获取到的锁由事务回滚之后统一释放
                                    */
        for (index = lockTableEntry->lockEntryArray->elementCount - 1; index >= 0; index--) {
          lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
          if (lockEntry == NULL) {
            LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
            // pthread_mutex_unlock(&lockTableEntry->latch);
            condVariable_MutexUnLock(&lockTableEntry->semaphore);
            LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
            return GNCDB_NOT_FOUND;
          }

          /* 找到当前事务在这个请求队列上的锁申请，删除后，通知相应阻塞在当前锁请求队列上的事务，将它们唤醒 */
          if (lockEntry->tid == tx->id) {
            rc = varArrayListRemovePointer(lockTableEntry->lockEntryArray, lockEntry);
            if (rc != GNCDB_SUCCESS) {
              LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
              // pthread_mutex_unlock(&lockTableEntry->latch);
              condVariable_MutexUnLock(&lockTableEntry->semaphore);
              LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
              return rc;
            }
            lockEntryDestroy(&lockEntry);

            /* 唤醒所有的被阻塞的事务 */
            // pthread_cond_broadcast(&lockTableEntry->cond);
            condVariable_Broadcast(&lockTableEntry->semaphore);

            LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
            // pthread_mutex_unlock(&lockTableEntry->latch);
            condVariable_MutexUnLock(&lockTableEntry->semaphore);
            LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
            return GNCDB_LOCK_FAIL;
          }
        }
      }
    }

    /* 4.4.获取锁成功，也需要唤醒其他被阻塞的事务，因为如果当前事务在锁等待队列上获取的是读锁，那么某些被阻塞的事务如果也是读锁，是可以在此刻被唤醒去获取读锁
     */
    // pthread_cond_broadcast(&lockTableEntry->cond);
    condVariable_Broadcast(&lockTableEntry->semaphore);

    // rc = bookKeeping(tx,pid);
    // if (rc != GNCDB_SUCCESS) {
    //     LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
    //     // pthread_mutex_unlock(&lockTableEntry->latch);
    // 	condVariable_MutexUnLock(&lockTableEntry->semaphore);
    //     LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
    // 	return rc;
    // }

    // printf("lockManagerAcquireLock:txid : %d pid=%d success\n",tx->id, pid);

    LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
    // pthread_mutex_unlock(&lockTableEntry->latch);
    condVariable_MutexUnLock(&lockTableEntry->semaphore);
    LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
    return GNCDB_SUCCESS;
  } else if (tx->status == SHRINKING) {
    if (tx->isolationLevel == READ_COMMITTED) {
      if (lockType == SHARD) {
        /* 4.1.获取指定页锁的等待队列并加锁*/
        lockTableEntry = getLockTableEntry(lockManager, pid);
        if (lockTableEntry == NULL) {
          return GNCDB_NOT_FOUND;
        }
        LOG(LOG_TRACE, "SLOCKing:lockTableEntry");
        // pthread_mutex_lock(&lockTableEntry->latch);
        condVariable_MutexLock(&lockTableEntry->semaphore);
        LOG(LOG_TRACE, "SLOCKend:lockTableEntry");

        /* 4.2.先将锁请求添加到对应页的锁等待队列中 */
        if (updateLock(lockManager, tx, lockTableEntry, lockType) != GNCDB_SUCCESS) {
          LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
          // pthread_mutex_unlock(&lockTableEntry->latch);
          condVariable_MutexUnLock(&lockTableEntry->semaphore);
          LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
          return GNCDB_LOCK_FAIL;
        }

        /* 4.2.获取指定页锁的等待队列并加锁*/
        // lockTableEntry = getLockTableEntry(lockManager, pid);
        // if (lockTableEntry == NULL) {
        // 	return GNCDB_NOT_FOUND;
        // }
        // LOG(LOG_TRACE, "SLOCKing:lockTableEntry");
        // // pthread_mutex_lock(&lockTableEntry->latch);
        // condVariable_MutexLock(&lockTableEntry->semaphore);
        // LOG(LOG_TRACE, "SLOCKend:lockTableEntry");

        /* 4.3.根据等待队列判断锁是否能够被授予，如果不能被授予，则阻塞当前事务的锁请求，直到锁被授予或者是超时被终止 */
        while (grantLock(lockManager, tx, lockTableEntry, lockType, pid) != GNCDB_SUCCESS) {
          /* 4.3.1.阻塞 */
          // clock_gettime(CLOCK_REALTIME, &ts);
          // ts.tv_sec += 5; // 设置5秒的超时时间
          // ret = pthread_cond_timedwait(&lockTableEntry->cond, &lockTableEntry->latch, &ts);
          ret = condVariable_Wait(&lockTableEntry->semaphore);
#if defined(__linux__) || defined(_WIN32)
          if (ret == ETIMEDOUT)
#else
          /* @todo 增加天脉系统的使用方式，平台测试机上添加 */
          if (ret == ETIMEDOUT)
#endif
          {
            LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
            // pthread_mutex_unlock(&lockTableEntry->latch);
            condVariable_MutexUnLock(&lockTableEntry->semaphore);
            LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
            return GNCDB_LOCK_FAIL;
          }

          /* 4.3.2.当阻塞的事务被唤醒了之后，先要判断自己的状态，如果没有被中止，才能继续获取锁 */
          if (tx->status == ABORTED) { /* 锁申请失败，意味着需要将当前pid上的锁申请从请求队列中踢出，之前已经获取到的锁由事务回滚之后统一释放
                                        */
            for (index = lockTableEntry->lockEntryArray->elementCount - 1; index >= 0; index--) {
              lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
              if (lockEntry == NULL) {
                LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
                // pthread_mutex_unlock(&lockTableEntry->latch);
                condVariable_MutexUnLock(&lockTableEntry->semaphore);
                LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
                return GNCDB_NOT_FOUND;
              }

              /* 找到当前事务在这个请求队列上的锁申请，删除后，通知相应阻塞在当前锁请求队列上的事务，将它们唤醒 */
              if (lockEntry->tid == tx->id) {
                rc = varArrayListRemove(lockTableEntry->lockEntryArray, lockEntry);
                if (rc != GNCDB_SUCCESS) {
                  LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
                  // pthread_mutex_unlock(&lockTableEntry->latch);
                  condVariable_MutexUnLock(&lockTableEntry->semaphore);
                  LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
                  return rc;
                }
                lockEntryDestroy(&lockEntry);

                /* 唤醒所有的被阻塞的事务 */
                // pthread_cond_broadcast(&lockTableEntry->cond);
                condVariable_Broadcast(&lockTableEntry->semaphore);

                LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
                // pthread_mutex_unlock(&lockTableEntry->latch);
                condVariable_MutexUnLock(&lockTableEntry->semaphore);
                LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
                return GNCDB_LOCK_FAIL;
              }
            }
          }
        }

        /* 4.4.获取锁成功，也需要唤醒其他被阻塞的事务，因为如果当前事务在锁等待队列上获取的是读锁，那么某些被阻塞的事务如果也是读锁，是可以在此刻被唤醒去获取读锁
         */
        // pthread_cond_broadcast(&lockTableEntry->cond);
        condVariable_Broadcast(&lockTableEntry->semaphore);

        // rc = bookKeeping(tx, pid);
        // if (rc != GNCDB_SUCCESS) {
        //     LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
        //     // pthread_mutex_unlock(&lockTableEntry->latch);
        // 	condVariable_MutexUnLock(&lockTableEntry->semaphore);
        //     LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
        // 	return rc;
        // }

        LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
        // pthread_mutex_unlock(&lockTableEntry->latch);
        condVariable_MutexUnLock(&lockTableEntry->semaphore);
        LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
        return GNCDB_SUCCESS;
      } else { /* 如果READ_COMMITTED事务，在缩减阶段申请的锁不是读锁，则需要中止当前事务 */
        rc = transactionUpdateStatus(tx, ABORTED);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        return GNCDB_LOCK_FAIL;
      }
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief
 * 释放指定的锁（因为实现的事务读已提交这个隔离级别，那么在解锁时：GROWING阶段是可以解读锁，SHRINKING阶段是可以解读锁和写锁的，一旦解了写锁就会进入缩减阶段）
 * @param LockManager
 * @param tx
 * @param pid
 * @param lockType
 * @return 返回是否释放锁成功
 */
int lockManagerReleaseLock(struct LockManager *lockManager, struct Transaction *tx, int pid, LockType lockType)
{
  /* 1.变量的定义 */
  int             rc             = 0;
  int             index          = 0;
  LockEntry      *lockEntry      = NULL;
  LockTableEntry *lockTableEntry = NULL;
  bool            flag           = false;

  /* 2.判断参数是否为空 */
  if (lockManager == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  // printf("lockManagerReleaseLock:txid : %d pid=%d\n",tx->id, pid);

  /* 3..获取指定页锁的等待队列并加锁*/
  lockTableEntry = getLockTableEntry(lockManager, pid);
  if (lockTableEntry == NULL) {
    return GNCDB_NOT_FOUND;
  }
  LOG(LOG_TRACE, "SLOCKing:lockTableEntry");
  // pthread_mutex_lock(&lockTableEntry->latch);
  condVariable_MutexLock(&lockTableEntry->semaphore);
  LOG(LOG_TRACE, "SLOCKend:lockTableEntry");

  /* 4.遍历行锁的等待队列，然后将指定的当前事务的锁请求删除掉 */
  for (index = lockTableEntry->lockEntryArray->elementCount - 1; index >= 0; index--) {
    /* 4.1.获取lockEntry */
    lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
    if (lockEntry == NULL) {
      // pthread_mutex_unlock(&lockTableEntry->latch);
      condVariable_MutexUnLock(&lockTableEntry->semaphore);
      LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
      return GNCDB_NOT_FOUND;
    }

    /* 4.2.找到当前事务在这个请求队列上的锁申请，删除后，通知相应阻塞在当前锁请求队列上的事务，将它们唤醒 */
    if (lockEntry->tid == tx->id) {
      if (lockEntry->lockType != lockType && lockType != UNKNOW) {
        flag = true;
        break;
      }
      lockEntry->cnt--;
      if (lockEntry->cnt == 0) {
        /* 4.2.2.唤醒所有的被阻塞的事务 */
        // pthread_cond_broadcast(&lockTableEntry->cond);
        condVariable_Broadcast(&lockTableEntry->semaphore);
        /* 4.2.3.根据释放锁的类型需要修改事务的状态 */
        rc = unLockChangeState(tx, lockEntry->lockType);
        if (rc != GNCDB_SUCCESS) {
          // pthread_mutex_unlock(&lockTableEntry->latch);
          condVariable_MutexUnLock(&lockTableEntry->semaphore);
          LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
          return rc;
        }

        /* 4.2.1. */
        rc = varArrayListRemovePointer(lockTableEntry->lockEntryArray, lockEntry);
        if (rc != GNCDB_SUCCESS) {
          // pthread_mutex_unlock(&lockTableEntry->latch);
          condVariable_MutexUnLock(&lockTableEntry->semaphore);
          LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
          return rc;
        }
        lockEntryDestroy(&lockEntry);

        /* 将之前的锁从事务的集合中删除掉，后续在重新添加上升级之后的锁 */
        rc = bookKeepingRemove(tx, pid);
        if (rc != GNCDB_SUCCESS) {
          LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
          // pthread_mutex_unlock(&lockTableEntry->latch);
          condVariable_MutexUnLock(&lockTableEntry->semaphore);
          LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
          return rc;
        }
      }
      // pthread_mutex_unlock(&lockTableEntry->latch);
      condVariable_MutexUnLock(&lockTableEntry->semaphore);
      LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
      return GNCDB_SUCCESS;
    }
  }

  // pthread_mutex_unlock(&lockTableEntry->latch);
  condVariable_MutexUnLock(&lockTableEntry->semaphore);
  LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");

  if (!flag) {
    /* 5.如果没有要找到需要释放的锁，中止事务 */
    rc = transactionUpdateStatus(tx, ABORTED);
    LOG(LOG_TRACE, "transaction aborted error");
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief
 * 释放指定的锁（因为releaseAllLock是用iterator遍历map删除的，而原函数内部会调用bookkeepingremove来修改map结构，因此这个函数不调用bookkeepingremove，因为已经在事务销毁阶段，不维护map也可以）
 * @param LockManager
 * @param tx
 * @param pid
 * @param lockType
 * @return 返回是否释放锁成功
 */
int lockManagerReleaseLockForDestroyTx(
    struct LockManager *lockManager, struct Transaction *tx, int pid, LockType lockType)
{
  /* 1.变量的定义 */
  int             rc             = 0;
  int             index          = 0;
  LockEntry      *lockEntry      = NULL;
  LockTableEntry *lockTableEntry = NULL;
  bool            flag           = false;

  /* 2.判断参数是否为空 */
  if (lockManager == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  // printf("lockManagerReleaseLock:txid : %d pid=%d\n",tx->id, pid);

  /* 3..获取指定页锁的等待队列并加锁*/
  lockTableEntry = getLockTableEntry(lockManager, pid);
  if (lockTableEntry == NULL) {
    return GNCDB_NOT_FOUND;
  }
  LOG(LOG_TRACE, "SLOCKing:lockTableEntry");
  // pthread_mutex_lock(&lockTableEntry->latch);
  condVariable_MutexLock(&lockTableEntry->semaphore);
  LOG(LOG_TRACE, "SLOCKend:lockTableEntry");

  /* 4.遍历行锁的等待队列，然后将指定的当前事务的锁请求删除掉 */
  for (index = lockTableEntry->lockEntryArray->elementCount - 1; index >= 0; index--) {
    /* 4.1.获取lockEntry */
    lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
    if (lockEntry == NULL) {
      // pthread_mutex_unlock(&lockTableEntry->latch);
      condVariable_MutexUnLock(&lockTableEntry->semaphore);
      LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
      return GNCDB_NOT_FOUND;
    }

    /* 4.2.找到当前事务在这个请求队列上的锁申请，删除后，通知相应阻塞在当前锁请求队列上的事务，将它们唤醒 */
    if (lockEntry->tid == tx->id) {
      if (lockEntry->lockType != lockType && lockType != UNKNOW) {
        flag = true;
        break;
      }
      /* 4.2.2.唤醒所有的被阻塞的事务 */
      // pthread_cond_broadcast(&lockTableEntry->cond);
      condVariable_Broadcast(&lockTableEntry->semaphore);
      /* 4.2.3.根据释放锁的类型需要修改事务的状态 */
      rc = unLockChangeState(tx, lockEntry->lockType);
      if (rc != GNCDB_SUCCESS) {
        // pthread_mutex_unlock(&lockTableEntry->latch);
        condVariable_MutexUnLock(&lockTableEntry->semaphore);
        LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
        return rc;
      }

      /* 4.2.1. */
      rc = varArrayListRemovePointer(lockTableEntry->lockEntryArray, lockEntry);
      if (rc != GNCDB_SUCCESS) {
        // pthread_mutex_unlock(&lockTableEntry->latch);
        condVariable_MutexUnLock(&lockTableEntry->semaphore);
        LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
        return rc;
      }
      lockEntryDestroy(&lockEntry);

      /* 将之前的锁从事务的集合中删除掉，后续在重新添加上升级之后的锁 */
      // rc = bookKeepingRemove(tx, pid);
      // if (rc != GNCDB_SUCCESS) {
      //     LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
      // 	// pthread_mutex_unlock(&lockTableEntry->latch);
      // 	condVariable_MutexUnLock(&lockTableEntry->semaphore);
      //     LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
      // 	return rc;
      // }

      // pthread_mutex_unlock(&lockTableEntry->latch);
      condVariable_MutexUnLock(&lockTableEntry->semaphore);
      LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
      return GNCDB_SUCCESS;
    }
  }

  // pthread_mutex_unlock(&lockTableEntry->latch);
  condVariable_MutexUnLock(&lockTableEntry->semaphore);
  LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");

  if (!flag) {
    /* 5.如果没有要找到需要释放的锁，中止事务 */
    rc = transactionUpdateStatus(tx, ABORTED);
    LOG(LOG_TRACE, "transaction aborted error");
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 释放指定的锁（因为实现的事务读已提交这个隔离级别，那么释放全部的锁必定是在事务处理COMMITTED/ABORTED）
 * @param LockManager
 * @param tx
 * @return 返回是否成功释放事务tx的所有锁
 */
int lockManagerReleaseAllLock(struct LockManager *lockManager, struct Transaction *tx)
{
  /* 1.变量的定义 */
  int rc = 0;
  // int index = 0;
  int             *pid      = NULL;
  int              id       = 0;
  HashMapIterator *iterator = NULL;

  /* 2.判断参数是否为空 */
  if (lockManager == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }
  iterator = createHashMapIterator(tx->tidPidMap);

  /* 3.遍历事务的锁集合 */
  while (hasNextHashMapIterator(iterator)) {
    iterator = nextHashMapIterator(iterator);
    pid      = (int *)iterator->entry->key;
    if (pid == NULL) {
      freeHashMapIterator(&iterator);
      return GNCDB_NOT_FOUND;
    }
    id = *pid;
    LOG(LOG_TRACE, "AcquireLockUNLOCK:PAGEid=%d", id);
    rc = lockManagerReleaseLockForDestroyTx(lockManager, tx, id, UNKNOW);
    if (rc != GNCDB_SUCCESS) {
      LOG(LOG_TRACE, "AcquireLockUNLOCKfail:PAGEid=%d", id);
      freeHashMapIterator(&iterator);
      return rc;
    }
    LOG(LOG_TRACE, "AcquireLockUNLOCKsuccess:PAGEid=%d", id);
  }
  freeHashMapIterator(&iterator);
  return GNCDB_SUCCESS;
}

/**
 * @brief 可能存在某个时刻该锁等待队列中，压根就没有其他的事务要加锁了，那么就可以把这个lockTableEntry给销毁掉
 * @param lockManager
 * @param pid
 */
void lockManagerDestroyTableEntryByPageID(struct LockManager *lockManager, int pid)
{
  LockTableEntry *lockTableEntry = NULL;

  if (lockManager == NULL) {
    return;
  }

  WriteLock(&lockManager->latch);
  LOG(LOG_TRACE, "SLOCKend:lockManager");
  lockTableEntry = hashMapGet(lockManager->pageTransactionLock, &pid);
  if (lockTableEntry) {
    hashMapRemove(lockManager->pageTransactionLock, &pid);
    lockTableEntryDestroy(&lockTableEntry);
  }
  WriteUnLock(&lockManager->latch);
  LOG(LOG_TRACE, "SUNLOCKend:lockManager");
}

/**
 * @brief 判断两个锁类型是否满足锁升级的条件
 * @param lockTypeBefore 升级之前的锁类型
 * @param lockTypeAfter 升级之后的锁类型
 * @return 返回两个锁类型是否满足锁升级的关系
 */
bool isCompatiable(LockType lockTypeBefore, LockType lockTypeAfter)
{
  if (lockTypeBefore == SHARD && lockTypeAfter == EXCLUSIVE) {
    return true;
  }

  return false;
}

/**
 * @brief 判断锁的授予是否满足兼容性
 * @param lockTypeFirst
 * @param lockTypeSecond
 * @return 返回两个锁类型是否满足兼容性
 */
bool grantCompatiable(LockType lockTypeFirst, LockType lockTypeSecond)
{
  /* 只要有一个写锁，就不满足锁的兼容性 */
  if (lockTypeFirst == EXCLUSIVE || lockTypeSecond == EXCLUSIVE) {
    return false;
  }

  return true;
}

/**
 * @brief 事务释放锁了之后，其所处的状态可能需要发生相应的改变
 * @param tx
 * @param lockType
 * @return 返回是否修改状态成功
 */
int unLockChangeState(struct Transaction *tx, LockType lockType)
{
  /* 1.变量的定义 */
  int rc = 0;

  /* 2.判断参数是否为空 */
  if (tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.判断是否状态是否是GROWING，如果不是，则直接返回，不改变事务的状态 */
  if (tx->status != GROWING) {
    return GNCDB_SUCCESS;
  }

  /* 4.根据不同的事务隔离级别修改事务释放锁之后的状态 */
  if (tx->isolationLevel == READ_COMMITTED && lockType == EXCLUSIVE) {
    rc = transactionUpdateStatus(tx, SHRINKING);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 获取某个页的锁表项（锁请求队列）
 * @param lockManager
 * @param pid
 * @return 返回pid对应的锁表项
 */
LockTableEntry *getLockTableEntry(struct LockManager *lockManager, int pid)
{
  /* 1.变量的定义 */
  LockTableEntry *lockTableEntry = NULL;
  int             rc             = 0;

  /* 2.判断参数是否为空 */
  if (lockManager == NULL) {
    return NULL;
  }

  /* 3.获取指定的pid对应的锁表项 */
  LOG(LOG_TRACE, "SLOCKing:lockManager");
  WriteLock(&lockManager->latch);
  LOG(LOG_TRACE, "SLOCKend:lockManager");
  lockTableEntry = hashMapGet(lockManager->pageTransactionLock, &pid);
  if (lockTableEntry == NULL) {
    lockTableEntry = lockTableEntryConstruct();
    rc             = hashMapPut(lockManager->pageTransactionLock, &pid, lockTableEntry);
    if (rc != GNCDB_SUCCESS) {
      LOG(LOG_TRACE, "SUNLOCKing:lockManager");
      WriteUnLock(&lockManager->latch);
      LOG(LOG_TRACE, "SUNLOCKend:lockManager");
      return NULL;
    }
  }
  LOG(LOG_TRACE, "SUNLOCKing:lockManager");
  WriteUnLock(&lockManager->latch);
  LOG(LOG_TRACE, "SUNLOCKend:lockManager");
  return lockTableEntry;
}

/**
 * @brief 在事务的锁请求集合中添加相应的锁请求
 * @param tx
 * @param pid
 * @return 返回是否成功添加到事务的已经获取的锁集合中
 */
int bookKeeping(struct Transaction *tx, int pid)
{
  int rc = 0;

  /* 1.判断参数是否为空 */
  if (tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2.将pid添加在指定的事务的锁集合中 */
  rc = transactionMemTidPid(pid, tx);

  return rc;
}

/**
 * @brief 在事务的锁请求集合中删除相应的锁请求
 * @param tx
 * @param pid
 * @return 返回是否成功从事务的已经获取的锁集合中删除指定的页的锁
 */
int bookKeepingRemove(struct Transaction *tx, int pid)
{
  int rc = 0;

  /* 1.判断参数是否为空 */
  if (tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2.从事务的锁集合中删除指定的pid */
  transcationDelTidPid(pid, tx);

  return rc;
}

/**
 * @brief 在锁请求队列中更新事务申请的锁的状态
 * @param lockManager
 * @param tx
 * @param pid
 * @param lockType
 * @return 返回是否成功将事务申请的锁添加到锁对应pid的锁等待队列中
 */
int updateLock(
    struct LockManager *lockManager, struct Transaction *tx, LockTableEntry *lockTableEntry, LockType lockType)
{
  /* 1.变量的定义 */
  int rc = 0;
  // LockTableEntry* lockTableEntry = NULL;
  LockEntry *lockEntry = NULL;
  int        index     = 0;

  /* 2.判断参数是否为空 */
  if (lockManager == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取pid的锁表项 */
  // lockTableEntry = getLockTableEntry(lockManager, pid);
  // if (lockTableEntry == NULL) {
  // 	return GNCDB_NOT_FOUND;
  // }

  /* 4.在锁表项中寻找该事务是否已经加锁，是否需要进行锁升级（如果需要进行锁升级，那么该事务之前申请的锁一定被授予了，因为没有被授予则该事务必定是否阻塞住的）
   */
  for (index = 0; index < lockTableEntry->lockEntryArray->elementCount; index++) {
    /* 4.1.首先获取对应的lockEntry */
    lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
    if (lockEntry == NULL) {
      return GNCDB_NOT_FOUND;
    }

    /* 4.2.如果找到了事务tx加锁的记录 */
    if (lockEntry->tid == tx->id) {
      /* 判断当前事务目前加的锁是否与之前加的锁是否相同，如果相同的话，不做任何的处理 */
      if (lockEntry->lockType == lockType || lockEntry->lockType == EXCLUSIVE) {
        if (lockType == SHARD)
          lockEntry->cnt++;
        return GNCDB_SUCCESS;
      }

      /* 如果当前等待队列中没有事务进行锁升级的，则该事务可以进行锁升级（避免死锁） */
      if (lockTableEntry->upgrading == -1) {
        if (isCompatiable(lockEntry->lockType, lockType)) {
          /* 更新等待队列的锁升级的事务id */
          lockTableEntry->upgrading = tx->id;

          /* 将之前的锁从事务的集合中删除掉，后续在重新添加上升级之后的锁 */
          // rc = bookKeepingRemove(tx, pid);
          // if (rc != GNCDB_SUCCESS) {
          //     LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
          // 	// pthread_mutex_unlock(&lockTableEntry->latch);
          // 	condVariable_MutexUnLock(&lockTableEntry->semaphore);
          //     LOG(LOG_TRACE, "SUNLOCKend:lockTableEntry");
          // 	return rc;
          // }

          /* 更新表锁的等待队列（直接添加到尾部） */
          rc = varArrayListRemovePointer(lockTableEntry->lockEntryArray, lockEntry);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }
          lockEntryDestroy(&lockEntry);
          lockEntry = lockEntryConstruct(tx->id, lockType, true);
          if (lockEntry == NULL) {
            return GNCDB_SPACE_LACK;
          }
          rc = varArrayListAddPointer(lockTableEntry->lockEntryArray, lockEntry);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }

          LOG(LOG_TRACE, "SUNLOCKing:lockTableEntry");
          return GNCDB_SUCCESS;
        }

        /* 不满足升级条件直接将事务中止 */
        transactionUpdateStatus(tx, ABORTED);
        return GNCDB_LOCK_FAIL;
      }

      /* 多个事务进行锁升级，发生锁升级冲突 */
      transactionUpdateStatus(tx, ABORTED);
      return GNCDB_LOCK_FAIL;
    }
  }

  /* 5.事务tx之前没有对pid进行加锁操作 */
  lockEntry = lockEntryConstruct(tx->id, lockType, true);
  if (lockEntry == NULL) {
    return GNCDB_SPACE_LACK;
  }

  rc = varArrayListAddPointer(lockTableEntry->lockEntryArray, lockEntry);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

/**
 * @brief 判断是否能够授予事务申请的锁
 * @param lockManager
 * @param tx
 * @param pid
 * @param lockType
 * @return 返回是否成功授予事务申请的锁
 */
int grantLock(
    struct LockManager *lockManager, struct Transaction *tx, LockTableEntry *lockTableEntry, LockType lockType, int pid)
{
  /* 1.变量的定义 */
  int rc = 0;
  // LockTableEntry* lockTableEntry = NULL;
  LockEntry    *lockEntry = NULL, *hasGrantLockEntry = NULL;
  int           index = 0, i = 0, j = 0;
  varArrayList *hasGrantedLockRequst = NULL;
  bool          flag = false, isCompatiable = false;
  int           hasGrantedLockRequstCount = 0;

  /* 2.判断参数是否为空 */
  if (lockManager == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取pid的锁表项 */
  // lockTableEntry = getLockTableEntry(lockManager, pid);
  // if (lockTableEntry == NULL) {
  // 	return GNCDB_NOT_FOUND;
  // }

  /* 4.先找到所有目前已经被授予的锁，并判断是否满足兼容性 */
  hasGrantedLockRequst = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (hasGrantedLockRequst == NULL) {
    return GNCDB_SPACE_LACK;
  }
  for (index = 0; index < lockTableEntry->lockEntryArray->elementCount; index++) {
    lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
    if (lockEntry == NULL) {
      varArrayListDestroy(&hasGrantedLockRequst);
      return GNCDB_NOT_FOUND;
    }

    /* 不是当前事务，且该事务已经被授予了锁，那么需要进行判断当前事务的锁类型是否兼容 */
    if (lockEntry->tid != tx->id && !lockEntry->isWait) {
      /* 不兼容直接退出wait */
      if (!grantCompatiable(lockEntry->lockType, lockType)) {
        varArrayListDestroy(&hasGrantedLockRequst);
        return GNCDB_LOCK_FAIL;
      }
      /* 兼容，将这个锁类型添加到已经授予的集合中 */
      rc = varArrayListAddPointer(hasGrantedLockRequst, lockEntry);
      if (rc != GNCDB_SUCCESS) {
        varArrayListDestroy(&hasGrantedLockRequst);
        return rc;
      }
    }
  }

  /* 5.如果当前事务需要进行锁升级（优先处理锁升级） */
  /* （如果当前等待队列中没有锁被授予，则这个升级后的锁直接被授予；如果当前已经有锁被授予，且升级后的锁能够兼容则授予，否则锁授予失败）
   */
  if (lockTableEntry->upgrading == tx->id) {
    /* 获取tx的锁请求lockEntry */
    for (index = 0; index < lockTableEntry->lockEntryArray->elementCount; index++) {
      lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, index);
      if (lockEntry == NULL) {
        varArrayListDestroy(&hasGrantedLockRequst);
        return GNCDB_NOT_FOUND;
      }

      if (lockEntry->tid == tx->id) { /* 第4步中已经检查了锁能够兼容当前已经被授予的锁，可以直接授予 */
        lockTableEntry->upgrading = -1;
        lockEntry->isWait         = false;
        varArrayListDestroy(&hasGrantedLockRequst);
        return GNCDB_SUCCESS;
      }
    }
  }

  /* 6.普通锁的授予，遍历等待队列，查看目前还没有被授予的锁，如果能够兼容则授予锁 */
  for (i = 0; i < lockTableEntry->lockEntryArray->elementCount; i++) {
    lockEntry = (LockEntry *)varArrayListGetPointer(lockTableEntry->lockEntryArray, i);
    if (lockEntry == NULL) {
      varArrayListDestroy(&hasGrantedLockRequst);
      return GNCDB_NOT_FOUND;
    }

    if (lockEntry->isWait) {
      isCompatiable             = true;
      hasGrantedLockRequstCount = hasGrantedLockRequst->elementCount;

      if (hasGrantedLockRequstCount > 0) {
        for (j = 0; j < hasGrantedLockRequstCount; j++) {
          hasGrantLockEntry = (LockEntry *)varArrayListGetPointer(hasGrantedLockRequst, j);
          if (hasGrantLockEntry == NULL) {
            varArrayListDestroy(&hasGrantedLockRequst);
            return GNCDB_NOT_FOUND;
          }

          /* 不兼容，直接退出 */
          if (!grantCompatiable(lockEntry->lockType, hasGrantLockEntry->lockType)) {
            isCompatiable = false;
            break;
          }

          /* 如果兼容 */
          if (isCompatiable) {
            lockEntry->isWait = false;
            if (lockEntry->lockType == SHARD)
              lockEntry->cnt++;
            if (lockEntry->tid == tx->id) {
              flag = true; /* 标记指定的tx所申请的所是否被授予了 */
            }
            rc = varArrayListAddPointer(hasGrantedLockRequst, lockEntry);
            if (rc != GNCDB_SUCCESS) {
              varArrayListDestroy(&hasGrantedLockRequst);
              return rc;
            }
          }
        }
      } else {
        lockEntry->isWait = false;
        if (lockEntry->lockType == SHARD)
          lockEntry->cnt++;
        if (lockEntry->tid == tx->id) {
          flag = true; /* 标记指定的tx所申请的所是否被授予了 */
        }
      }
    } else {
      if (lockEntry->tid == tx->id) {
        flag = true; /* 标记指定的tx所申请的所是否被授予了 */
      }
    }
  }

  /* 7.释放资源 */
  varArrayListDestroy(&hasGrantedLockRequst);
  /* 8.判断指定的事务所申请的所是否被授予了 */
  if (flag) {
    rc = transactionMemTidPid(pid, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    return GNCDB_SUCCESS;
  } else {
    return GNCDB_LOCK_FAIL;
  }
}

/**
 * @brief 在页集合中为已上写锁所的内部页加逻辑写锁
 * @param lockManager
 * @param tx
 * @param pid
 * @param lockType
 * @return 返回是否成功授予事务申请的锁
 */

int lockManagerInternalPageLock(struct LockManager *lockManager, struct Transaction *tx)
{
  int        i    = 0;
  int        rc   = 0;
  BtreePage *page = NULL;
  for (; i < tx->latchedPageSet->elementCount; ++i) {
    page = varArrayListGetPointer(tx->latchedPageSet, i);
    if (page == NULL) {
      ++i;
      continue;
    }
    if (page->page.pageType == INTERNAL_PAGE) {
      LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", page->page.id);
      rc = lockManagerAcquireLock(lockManager, tx, page->page.id, EXCLUSIVE);
      if (rc != GNCDB_SUCCESS) {
        LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", page->page.id);
        return rc;
      }
      LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", page->page.id);
    }
  }
  return GNCDB_SUCCESS;
}
