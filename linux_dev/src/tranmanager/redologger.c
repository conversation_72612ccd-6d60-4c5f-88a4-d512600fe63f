/************************************************
 版    权:
 文 件 名: redologger.c
 创建日期:
 编 写 者:
 内容描述:
 关系文件:
 更改历史:
************************************************/

#include "gncdb.h"
#include "os.h"
#include "redologger.h"
#include "transaction.h"
#include "pagepool.h"
#include "tranmanager.h"
#include "typedefine.h"
#include <string.h>

/// <summary>
/// logger的构造函数
/// </summary>
/// <param name="tid"></param>
/// <param name="db"></param>
/// <returns></returns>
/************************************************
 函 数 名:
 功能描述:
 输入参数:
 输出参数:
 返 回 值:
 创建日期:
 编 写 者:
 更改历史:
************************************************/
Logger *loggerConstruct(GNCDB *db)
{
    int     rc              = GNCDB_SUCCESS;
    Logger *logger          = my_malloc(sizeof(Logger));
    FILE   *file            = NULL;
    char    logFileName[32] = {0};

    if (logger == NULL)
    {
        return NULL;
    }

    logger->tid    = 0;
    logger->offset = 0;
    ReadWriteLockInit(&(logger->latch));
    logger->LSN = 0;

    getFileName(db->fileName, logFileName, true);
    memset(logger->logFileName, 0, 64);

#if defined _WIN32
    sprintf(logger->logFileName, "%s\\log_%s", LOCAL_PATH, logFileName);
#else
    sprintf(logger->logFileName, "%s/log_%s", LOCAL_PATH, logFileName);
#endif
    // 存在日志文件，则进行故障恢复
    if (isFileExist(logger->logFileName))
    {
        int rc = loggerRecover(logger, db);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(logger);
            return NULL;
        }
        remove(logger->logFileName);
    }

    rc = createFile(logger->logFileName, &file);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(logger);
        return NULL;
    }

    logger->logFile = file;
    return logger;
}

void loggerDestroy(Logger **logger)
{
    /* 1.判断参数是否为空 */
    if (logger == NULL || *logger == NULL)
    {
        return;
    }
    /* 2.释放内存空间 */
    fclose((*logger)->logFile);
    remove((*logger)->logFileName);
    my_free(*logger);
    *logger = NULL;
}
/// <summary>
/// 写日志函数
/// </summary>
/// <param name="logger"></param>
/// <param name="buf"></param>
/// <param name="seek"></param>
/// <param name="size"></param>
/// <returns></returns>

int loggerWrite(struct Logger *logger, BYTE *buf, long seek, int size)
{
    /* 写日志 */
    int rc = 0;
    LOG(LOG_TRACE, "WLOCKing:logger ");
    WriteLock(&(logger->latch));
    LOG(LOG_TRACE, "WLOCKend:logger ");

    rc = osWrite(logger->logFile, buf, seek, size);
    if (!rc)
        logger->LSN++;

    WriteUnLock(&(logger->latch));
    LOG(LOG_TRACE, "WUnLOCKend:logger ");

    return rc;
}

/**
 * @description: 判断是否达到写检查点日志阈值，若达到则写检查点日志
 * @param {Logger*} logger 日志管理器
 * @param {GNCDB*} db 数据库
 * @return {*} 执行结果
 */
int loggerCheck(struct Logger *logger, struct GNCDB *db)
{
    int rc = GNCDB_SUCCESS;
    /* 对LSN加写锁 */
    LOG(LOG_TRACE, "WLOCKing:logger ");
    WriteLock(&(logger->latch));
    LOG(LOG_TRACE, "WLOCKend:logger ");

    if (logger->LSN % MAX_LOGGER == 0)
    {
        // status = 0，检查点行为刚刚开始
        rc = loggerCheckpoint(logger, '0', db);
        // 此处解锁，后续事务可以并发执行
        WriteUnLock(&(logger->latch));
        LOG(LOG_TRACE, "WUnLOCKend:logger ");
        if (rc)
        {
            return rc;
        }
        LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
        WriteLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

        // 接下来将pagePool中的flushMap中的页写入磁盘
        rc = FlushCheckPoint(db);
        if (rc)
        {
            LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
            WriteUnLock(&(db->pagePool->latch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
            return rc;
        }
        LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        WriteUnLock(&(db->pagePool->latch));
        LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

        // 再次给logger加锁
        LOG(LOG_TRACE, "WLOCKing:logger ");
        WriteLock(&(logger->latch));
        LOG(LOG_TRACE, "WLOCKend:logger ");
        // status = 1，检查点行为已经结束，所有脏页已经刷新到磁盘中
        rc = loggerCheckpoint(logger, '1', db);

        WriteUnLock(&(logger->latch));
        LOG(LOG_TRACE, "WUnLOCKend:logger ");

        return rc;
    }
    else
    {
        WriteUnLock(&(logger->latch));
        LOG(LOG_TRACE, "WUnLOCKend:logger ");
    }
    return rc;
}

/// <summary>
/// begin日志格式
/// TYPE	1	该条事务类型0
/// LSN		4	该条日志的序列号。
/// PRE_LSN	4	该事务对应的上条日志序列号(Begin - 1)
/// TID		4	该条事务编号。
/// SIZE	4	该条日志的长度
/// </summary>
/// <param name="logger"></param>
/// <param name="tid"></param>
/// <returns></returns>

int loggerBegin(struct Logger *logger, struct GNCDB *db, struct Transaction *tx)
{
    char  type   = TXN_BEGIN;
    int   len    = 0;
    int   rc     = GNCDB_SUCCESS;
    int   preLSN = 0;
    BYTE *buf    = NULL;
    if (logger == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    buf = my_malloc(sizeof(BYTE) * BEGIN_SIZE);
    if (buf == NULL)
    {
        return GNCDB_MEM;
    }

    rc = writeChar(type, &buf[len], &len);
    if (rc)
    {
        my_free(buf);
        return rc;
    }

    /* 将当前事务的LSN记录到事务的所有LSN中 */
    rc = varArrayListAdd(tx->preLSN, &(logger->LSN));
    if (rc)
    {
        my_free(buf);
        return rc;
    }

    ReadLock(&(logger->latch));
    rc = writeInt(logger->LSN, &buf[len], &len);
    ReadUnLock(&(logger->latch));

    if (rc)
    {
        my_free(buf);
        return rc;
    }

    /* begin类型的日志前一条日志为空，所以赋值为0 */
    rc = writeInt(preLSN, &buf[len], &len);
    if (rc)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(tx->id, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }

    rc = writeInt(BEGIN_SIZE, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = loggerWrite(logger, buf, -1, len);

    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    LOG(LOG_INFO, "LOG BEGIN   Tid = %d", tx->id);
    my_free(buf);

    rc = loggerCheck(logger, db);

    return rc;
}
/// <summary>
/// rollback日志格式
/// TYPE	1	该条事务类型2
/// LSN		4	该条日志序列号
/// PRE_LSN	4	该事务对应的上条日志序列号(Begin为 - 1)
/// TID		4	该条事务编号
/// SIZE	4	该条日志的长度
/// </summary>
/// <param name="logger"></param>
/// <param name="tid"></param>
/// <param name="db"></param>
/// <returns></returns>
int loggerRollback(struct Logger *logger, struct Transaction *tx, struct GNCDB *db)
{

    int   rc        = GNCDB_SUCCESS;
    int  *preLogger = NULL;
    BYTE *buf       = NULL;
    char  type      = TXN_ABORT;
    int   len       = 0;

    rc = setBeforeImage(tx, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }

    /* 依照 ABORT类型记录的字段顺序，在空buf数组依次插入相关字段，最后将记录写入日志文件末尾 */
    buf = my_malloc(sizeof(BYTE) * ABORT_SIZE);
    if (buf == NULL)
    {
        return GNCDB_MEM;
    }

    rc = writeChar(type, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    ReadLock(&(logger->latch));
    rc = writeInt(logger->LSN, &buf[len], &len);
    ReadUnLock(&(logger->latch));
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = transrManageGetPreLogger(db->transactionManager, &preLogger, tx);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = varArrayListAdd(tx->preLSN, &logger->LSN);
    if (rc)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(*preLogger, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(tx->id, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }

    rc = writeInt(ABORT_SIZE, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = loggerWrite(logger, buf, -1, len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    /* 释放该事务所占用的全部锁 */
    // rc = lockManagerFreePageLock(db->transactionManager->lockManager, tx->id, db);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    LOG(LOG_INFO, "LOG ROLLBK  Tid = %d", tx->id);

    my_free(buf);
    rc = loggerCheck(logger, db);

    return rc;
}

// * Commit日志格式：
// * TYPE	    1	该条事务类型3
// * LSN		4	该条日志的序列号
// * PRE_LSN	4	该事务对应的上条日志序列号(Begin为 - 1)
// * TID		4	该条事务编号
// * SIZE	    4	该条日志的长度
/**
 * @description: 将事务提交的日志写入日志文件（将修改的页的数据写update日志）
 * @param {Logger*} logger 日志管理器
 * @param {Transaction*} tx 事务
 * @param {GNCDB*} db 数据库
 * @return {*} 执行结果
 */
int loggerCommit(struct Logger *logger, struct Transaction *tx, struct GNCDB *db)
{
    int rc = GNCDB_SUCCESS;
    // LockManager* lockManager = db->transactionManager->lockManager;
    char             type        = TXN_COMMIT;
    int              len         = 0;
    int             *preLogger   = NULL;
    BYTE            *buf         = NULL;
    HashMapIterator *iterator    = NULL;
    BYTE            *buffer      = NULL; /*日志缓冲区，后续传参数修改*/
    int              index       = 0;
    int              buffer_size = 602; /*单位：页*/

    // 根据oldPageData的页（即修改过的页）刷update日志
    if (tx->oldPageData->entryCount != 0)
    {
        iterator = createHashMapIterator(tx->oldPageData);
        if (iterator == NULL)
        {
            return GNCDB_MAP_ITERATOR_FALSE;
        }
        buffer = my_malloc(sizeof(BYTE) * buffer_size * db->pageCurrentSize);
        /* 循环遍历将每个修改过的页都刷入日志 */
        while (hasNextHashMapIterator(iterator))
        {
            iterator = nextHashMapIterator(iterator);
            if (iterator == NULL)
            {
                freeHashMapIterator(&iterator);
                return GNCDB_MAP_NEXT_NOT_FOUND;
            }
            rc = loggerReplace(logger, tx, *((int *)iterator->entry->key), db, buffer, &index);
            if (rc)
            {
                freeHashMapIterator(&iterator);
                return rc;
            }
            /*缓冲池满了刷盘*/
            if (index + 2 * db->pageCurrentSize >= buffer_size * db->pageCurrentSize)
            {
                rc = loggerWrite(logger, buffer, -1, index);
                if (rc != GNCDB_SUCCESS)
                {
                    freeHashMapIterator(&iterator);
                    my_free(buffer);
                    return rc;
                }
                rc = loggerCheck(logger, db);
                if (rc != GNCDB_SUCCESS)
                {
                    freeHashMapIterator(&iterator);
                    my_free(buffer);
                    return rc;
                }
                index = 0; /*重置index*/
            }
        }
        rc = loggerWrite(logger, buffer, -1, index);
        if (rc != GNCDB_SUCCESS)
        {
            freeHashMapIterator(&iterator);
            my_free(buffer);
            return rc;
        }
        rc = loggerCheck(logger, db);
        if (rc != GNCDB_SUCCESS)
        {
            freeHashMapIterator(&iterator);
            my_free(buffer);
            return rc;
        }
        freeHashMapIterator(&iterator);
        my_free(buffer);
    }

    buf = my_malloc(sizeof(BYTE) * COMMIT_SIZE);
    if (buf == NULL)
    {
        return GNCDB_MEM;
    }

    rc = writeChar(type, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(logger->LSN, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);

        return rc;
    }
    rc = transrManageGetPreLogger(db->transactionManager, &preLogger, tx);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(*preLogger, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(tx->id, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }

    rc = writeInt(COMMIT_SIZE, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }

    rc = loggerWrite(logger, buf, -1, len);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(buf);
        return rc;
    }
    LOG(LOG_INFO, "LOG COMMIT  Tid = %d", tx->id);
    my_free(buf);

    rc = loggerCheck(logger, db);

    return rc;
}

// * UPDATE日志格式
// * TYPE	    1	        该条事务类型1
// * PAGE_ID	4	        对那一页进行了修改
// * LSN		4	        该条日志的序列号
// * PRE_LSN	4	        该事务对应的上条日志序列号(Begin为 - 1)
// * TID		4	        该条事务编号
// * NEW_DATA		更新后的数据
// * SIZE	    4	        该条日志的长度
/**
 * @description: 根据修改过的页的pid写update日志
 * @param {Logger*} logger 日志管理器
 * @param {Transaction*} tx 事务
 * @param {int} pageId	页id
 * @param {GNCDB*} db 全局数据库
 * @return {*} 结果
 */
int loggerReplace(struct Logger *logger, struct Transaction *tx, int pageId, struct GNCDB *db, BYTE *buffer, int *index)
{
    // pthread_mutex_lock(&(logger->latch));
    Page *page         = NULL;
    int   rc           = GNCDB_SUCCESS;
    BYTE *buf          = NULL;
    BYTE *currentBytes = NULL;
    char  logType      = LOG_UPDATE;
    int   len          = 0;
    int  *prelogger    = NULL;
    int   updateSize   = 0;
    updateSize         = db->pageCurrentSize + 5 * INT_SIZE + TYPE_SIZE; 

    /* 日志缓冲区 */
    buf = my_malloc(sizeof(BYTE) * updateSize);
    if (buf == NULL)
    {
        return GNCDB_MEM;
    }
    memset(buf, 0, updateSize);

    rc = pagePoolGetPage((Page **)&page, pageId, NULL, db);

    currentBytes = pageToBytes(db, page);

    rc = setPageStatusPinDown(db->pagePool, page->id, NULL);

    rc = writeChar(logType, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }
    rc = writeInt(pageId, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }
    rc = writeInt(logger->LSN, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }

    rc = transrManageGetPreLogger(db->transactionManager, &prelogger, tx);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }
    rc = writeInt(*prelogger, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }
    rc = writeInt(tx->id, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }

    rc = writeBlob(currentBytes, &buf[len], (db->pageCurrentSize), &len);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }
    rc = writeInt(updateSize, &buf[len], &len);
    if (rc != GNCDB_SUCCESS)
    {
        // my_free(currentBytes);
        // my_free(oldBytes);
        my_free(buf);
        return rc;
    }
    /*日志写入缓冲区，最后统一刷盘*/
    memcpy(buffer + *index, buf, len);
    *index += len;

    // rc = loggerWrite(logger, buf, -1, len);
    // if (rc != GNCDB_SUCCESS)
    // {
    // 	// my_free(currentBytes);
    // 	// my_free(oldBytes);
    // 	my_free(buf);
    // 	return rc;
    // }
    LOG(LOG_INFO, "LOG UPDATE  Tid = %d  PageId = %d", tx->id, pageId);

    // my_free(currentBytes);
    // my_free(oldBytes);
    my_free(buf);
    // rc = loggerCheck(logger, db);

    return rc;
}

/**
 * @description: 根据日志执行故障恢复过程
 * @param {Logger*} logger 日志管理器
 * @param {GNCDB*} db 数据库
 * @return {*} 执行结果
 */
int loggerRecover(struct Logger *logger, struct GNCDB *db)
{
    int   rc = GNCDB_SUCCESS, len = 0;
    long  seek = 0, filelength = 0;
    int   logSize         = 0;
    char  checkCount      = 0;
    BYTE *intBuf          = NULL;
    bool  check_found     = false;
    bool  freepage_found  = false;
    int   firstFreePageId = 0;
    int   totalNumPage    = 0;
    int   totalPN_File    = 1;
    int   freePN_File     = 0;
    int   offset          = 0;
    BYTE *buf             = NULL;
    BYTE *bufSize         = NULL;
    int   updateSize      = 0;

    // * 用于存储确认提交的事务
    varArrayList *commitList = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    // * 用于记录需要重做操作的页面ID
    varArrayList *redoPageIdList = varArrayListCreate(ORDER, sizeof(int), 0, intCompare, NULL);
    if (redoPageIdList == NULL || commitList == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    updateSize = db->pageCurrentSize + 5 * INT_SIZE + TYPE_SIZE;

    if (db->dbFile == NULL)
    {
        return GNCDB_FILE_NOT_FOUND;
    }

    logger->logFile = fopen(logger->logFileName, "rb+");

    /* 先恢复日志清理时的崩溃 */
    rc = loggerDeleteRecover(logger, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    rc = getFileLength(&filelength, logger->logFile);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    buf = my_malloc(updateSize);
    if (buf == NULL)
    {
        return GNCDB_MEM;
    }
    bufSize = my_malloc(INT_SIZE);
    if (bufSize == NULL)
    {
        return GNCDB_MEM;
    }
    /* 从后往前找 */
    seek = filelength;
    while (true)
    {
        /* 先读最小单位的完整日志（检查点） */
        rc = osRead(logger->logFile, seek - INT_SIZE, INT_SIZE, &bufSize);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(buf);
            my_free(bufSize);
            return rc;
        }
        rc = readInt(&logSize, &bufSize[0], &len);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(buf);
            my_free(bufSize);
            return rc;
        }

        /* 日志类型为 BEGIN/COMMIT/ABORT */
        if (logSize == LOGGER_MIN_SIZE)
        {
            int  log_tid  = 0;
            char log_type = 0;

            /* 重新读一个完整BEGIN/COMMIT/ABORT日志 */
            rc = osRead(logger->logFile, seek - LOGGER_MIN_SIZE, LOGGER_MIN_SIZE, &buf);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            rc = readChar(&log_type, buf, &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            /* 有可能这条日志不完整，只能从后往前一个字节一个字节找 */
            if (log_type != TXN_COMMIT && log_type != TXN_ABORT && log_type != TXN_BEGIN)
            {
                seek--;
                continue;
            }
            seek -= LOGGER_MIN_SIZE;
            rc = readInt(&log_tid, &buf[TYPE_SIZE + 2 * INT_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }

            /* 找到检查点后的COMMIT则加入commitList */
            if (log_type == TXN_COMMIT && !check_found)
            {
                rc = varArrayListAdd(commitList, &log_tid);
                if (rc != GNCDB_SUCCESS)
                {
                    my_free(buf);
                    my_free(bufSize);
                    return rc;
                }
            }

            else if (log_type == TXN_ABORT && !check_found)
            {
                continue;
            }
            /* 找到BEGIN,若存在于commitList从commitList移除 */
            else if (log_type == TXN_BEGIN)
            {
                if (varArrayListIndexOf(commitList, &log_tid) >= 0)
                {
                    varArrayListRemove(commitList, &log_tid);
                }
            }
        }
        /* 日志类型为UPDATE */
        else if (logSize == updateSize)
        {
            char log_type;
            int  log_pageid = 0;
            int  log_tid    = 0;
            char log_pageType;
            /* 重新读一个完整update日志 */
            rc = osRead(logger->logFile, seek - updateSize, updateSize, &buf);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            rc = readChar(&log_type, buf, &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            /* 有可能这条日志不完整，只能从后往前一个字节一个字节找 */
            if (log_type != LOG_UPDATE)
            {
                seek--;
                continue;
            }
            seek -= updateSize;

            rc = readInt(&log_pageid, &buf[TYPE_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            rc = readInt(&log_tid, &buf[TYPE_SIZE + 3 * INT_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            /* 已存在redoPageIdList代表当前update日志后的update日志已刷过磁盘 */
            if (varArrayListIndexOf(redoPageIdList, &log_pageid) >= 0)
            {
                continue;
            }
            /* commitList中不存在则代表之前此update操作未存入磁盘，不是redo日志，此事务的update会rollback */
            if (varArrayListIndexOf(commitList, &log_tid) < 0)
            {
                continue;
            }

            /* redo */
            // 找出最大的pageid。再跟原数据库文件中的totalNumPage作比较，取最大者
            if (log_pageid > totalNumPage)
            {
                totalNumPage = log_pageid;
            }
            // 从日志中的页面数据读取页面类型
            rc = readChar(&log_pageType, &buf[updateSize - (db->pageCurrentSize) - INT_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            // 找到最新的FREE_PAGE
            if (!freepage_found && log_pageType == FREE_PAGE)
            {
                firstFreePageId = log_pageid;
                freepage_found  = true;
            }
            rc = osWrite(db->dbFile,
                &buf[updateSize - (db->pageCurrentSize) - INT_SIZE],
                (log_pageid - 1) * (db->pageCurrentSize),
                (db->pageCurrentSize));
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            rc = varArrayListAdd(redoPageIdList, &log_pageid);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            LOG(LOG_INFO,
                "RECOVER Tid = %d, PageId = %d, totalNumPage = %d, firstFreePageId = %d",
                log_tid,
                log_pageid,
                totalNumPage,
                firstFreePageId);
        }
        /* 日志类型为检查点 */
        else if (logSize == CHECK_SIZE)
        {
            char log_type;
            char status;
            /* 重新读一个完整检查点日志 */
            rc = osRead(logger->logFile, seek - CHECK_SIZE, CHECK_SIZE, &buf);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            rc = readChar(&log_type, &buf[0], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            if (log_type != CHECK)
            {
                seek--;
                continue;
            }
            seek -= CHECK_SIZE;
            rc = readChar(&status, &buf[INT_SIZE + TYPE_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                my_free(bufSize);
                return rc;
            }
            /* 跳过不配对的开始检查点(即最后一个检查点是开始检查点，之后发生系统故障的情况) */
            if (checkCount == 0 && status == '0')
            {
                continue;
            }
            checkCount++;
            /* 找到配对检查点，check_found为true */
            if (checkCount == 2 && status == '0')
            {
                check_found = true;
            }
        }
        /* 有可能这条日志不完整，只能从后往前一个字节一个字节找 */
        else
        {
            seek--;
        }
        /* 检查点后的redo找完（找到检查点且为每个检查点之后的commit都找到了配对的begin，即每个commit已经redo完了） */
        if (varArrayListGetCount(commitList) == 0 && check_found)
        {
            break;
        }
        /* 日志找完则退出 */
        if (seek <= 0)
        {
            break;
        }
    }
    my_free(buf);
    my_free(bufSize);
    /* 接下来更新totalNumPage和firstFreePageId */
    // 文件中读取totalPN_File和freePN_File
    intBuf = (BYTE *)my_malloc(INT_SIZE * 2);
    if (intBuf == NULL)
    {
        return GNCDB_MEM;
    }
    rc = osRead(db->dbFile, BYTES32 + 3 * INT_SIZE, INT_SIZE * 2, &intBuf);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(intBuf);
        return rc;
    }
    rc = readInt(&totalPN_File, intBuf, &offset);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(intBuf);
        return rc;
    }
    rc = readInt(&freePN_File, &intBuf[INT_SIZE], &offset);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(intBuf);
        return rc;
    }
    // 若totalNumPageg更大，则写入磁盘
    if (totalNumPage > totalPN_File)
    {
        rc = writeInt(totalNumPage, &intBuf[0], &offset);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(intBuf);
            return rc;
        }
    }
    if (freePN_File != firstFreePageId)
    {
        rc = writeInt(firstFreePageId, &intBuf[INT_SIZE], &offset);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(intBuf);
            return rc;
        }
    }
    rc = osWrite(db->dbFile, intBuf, BYTES32 + 3 * INT_SIZE, INT_SIZE * 2);
    if (rc != GNCDB_SUCCESS)
    {
        my_free(intBuf);
        return rc;
    }
    varArrayListDestroy(&commitList);
    varArrayListDestroy(&redoPageIdList);
    my_free(intBuf);
    fflush(db->dbFile);
    return GNCDB_SUCCESS;
}

// * Checkpoint日志格式：
// * TYPE	    1	该条事务类型
// * LSN		4	该条日志的序列号
// * STAT   	1	标记检查点类型 0开始/1结束
// * SIZE	    4	该条日志的长度
/**
 * @description: 写检查点日志
 * @param {Logger*} logger 日志管理器
 * @param {char} sta
 * @return {*} 执行结果
 */
int loggerCheckpoint(struct Logger *logger, char sta, GNCDB *db)
{
    // 此日志不在内层获取写锁，在外层加锁
    /* todo?暂时将文件清理放于此 */
    int   rc  = GNCDB_SUCCESS;
    int   len = 0;
    BYTE *buf = NULL;
    rc        = loggerDelete(logger, db);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    buf = my_malloc(sizeof(BYTE) * CHECK_SIZE);
    if (buf == NULL)
    {
        return GNCDB_MEM;
    }
    rc = writeChar(CHECK, &buf[len], &len);
    if (rc)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(logger->LSN, &buf[len], &len);
    if (rc)
    {
        my_free(buf);
        return rc;
    }
    rc = writeChar(sta, &buf[len], &len);
    if (rc)
    {
        my_free(buf);
        return rc;
    }
    rc = writeInt(CHECK_SIZE, &buf[len], &len);
    if (rc)
    {
        my_free(buf);
        return rc;
    }
    rc = osWrite(logger->logFile, buf, -1, CHECK_SIZE);
    logger->LSN++;
    if (rc)
    {
        my_free(buf);
        return rc;
    }
    my_free(buf);
    LOG(LOG_INFO, "********** LOG CHECKPOINT  Status = %c **********", sta);

    return GNCDB_SUCCESS;
}

/**
 * @description: 日志清理，先写到另一个文件，再把原文件删除，期间其他不影响其他事务写日志
 * @param {Logger*} logger 日志管理器
 * @return {*} 执行结果
 */
int loggerDelete(struct Logger *logger, GNCDB *db)
{

    // 暂定不加锁
    int           rc         = GNCDB_SUCCESS;
    long          filelength = 0, seek = 0;
    int           len = 0, logSize = 0, checkCount = 0;
    FILE         *file           = NULL;
    BYTE         *buf            = NULL;
    bool          check_found    = false;
    varArrayList *commitList     = NULL;
    varArrayList *redoPageIdList = NULL;
    // 临时日志名：logTemp_数据库名.dat
    char logTempFilePath[128] = {0};
    char logTempFileName[64]  = {0};
    int  updateSize           = 0;
    updateSize                = db->pageCurrentSize + 5 * INT_SIZE + TYPE_SIZE; 

    if (logger == NULL)
    {
        return GNCDB_FILEISNULL;
    }

    rc = getFileLength(&filelength, logger->logFile);

    if (filelength < LOGFILE_MAXSIZE)
    {
        return GNCDB_SUCCESS;
    }

    /* 从后往前找 */
    seek           = filelength;
    commitList     = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    redoPageIdList = varArrayListCreate(ORDER, sizeof(int), 0, intCompare, NULL);
    if (redoPageIdList == NULL || commitList == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    buf = my_malloc(updateSize);
    if (buf == NULL)
    {
        varArrayListDestroy(&commitList);
        varArrayListDestroy(&redoPageIdList);
        return GNCDB_MEM;
    }
    while (true)
    {
        /* 先读最小单位的完整日志 */
        rc = osRead(logger->logFile, seek - LOGGER_MIN_SIZE, LOGGER_MIN_SIZE, &buf);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(buf);
            return rc;
        }
        // 读取当前最小日志中的最后一个int数据，即日志文件中最后一个日志的大小
        rc = readInt(&logSize, &buf[LOGGER_MIN_SIZE - INT_SIZE], &len);
        if (rc != GNCDB_SUCCESS)
        {
            my_free(buf);
            return rc;
        }

        /* 日志类型为 BEGIN/COMMIT/ABORT，这三者结构一致，size都是LOGGER_MIN_SIZE */
        if (logSize == LOGGER_MIN_SIZE)
        {
            char log_type = 0;
            int  log_tid  = 0;
            rc            = readChar(&log_type, buf, &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                return rc;
            }
            /* 有可能这条日志不完整，只能从后往前一个字节一个字节找 */
            if (log_type != TXN_COMMIT && log_type != TXN_ABORT && log_type != TXN_BEGIN)
            {
                seek--;
                continue;
            }
            seek -= LOGGER_MIN_SIZE;
            rc = readInt(&log_tid, &buf[TYPE_SIZE + 2 * INT_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                return rc;
            }

            /* 找到检查点后的COMMIT则加入commitList */
            if (log_type == TXN_COMMIT && !check_found)
            {
                rc = varArrayListAdd(commitList, &log_tid);
                if (rc != GNCDB_SUCCESS)
                {
                    my_free(buf);
                    return rc;
                }
            }

            else if (log_type == TXN_ABORT && !check_found)
            {
                continue;
            }
            /* 找到BEGIN,若存在于commitList从commitList移除 */
            else if (log_type == TXN_BEGIN)
            {
                if (varArrayListIndexOf(commitList, &log_tid) >= 0)
                {
                    varArrayListRemove(commitList, &log_tid);
                }
            }
        }
        // 日志类型为UPDATE，跳过
        else if (logSize == updateSize)
        {
            seek -= updateSize;
        }
        /* 日志类型为检查点 */
        else if (logSize == CHECK_SIZE)
        {
            char log_type;
            char status;
            rc = readChar(&log_type, &buf[LOGGER_MIN_SIZE - CHECK_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                return rc;
            }
            /* 有可能这条日志不完整，只能从后往前一个字节一个字节找 */
            if (log_type != CHECK)
            {
                seek--;
                continue;
            }
            seek -= CHECK_SIZE;
            rc = readChar(&status, &buf[LOGGER_MIN_SIZE - INT_SIZE - TYPE_SIZE], &len);
            if (rc != GNCDB_SUCCESS)
            {
                my_free(buf);
                return rc;
            }
            /* 跳过不配对的开始检查点（最后一个检查点是开始检查点，没有配对的结束检查点） */
            if (checkCount == 0 && status == '0')
            {
                continue;
            }
            checkCount++;
            /* 找到检查点，check_found为true */
            if (checkCount == 2 && status == '0')
            {
                check_found = true;
            }
        }
        /* 有可能这条日志不完整，只能从后往前一个字节一个字节找 */
        else
        {
            seek--;
        }
        /* 检查点后的redo找完（找到了配对检查点并且为每个检查点之后的commit找到了配对的begin） */
        if (varArrayListGetCount(commitList) == 0 && check_found)
        {
            break;
        }
        /* 日志找完则退出 */
        if (seek <= 0)
        {
            break;
        }
    }
    varArrayListDestroy(&commitList);
    varArrayListDestroy(&redoPageIdList);
    my_free(buf);

    getFileName(logger->logFileName, logTempFileName, true);
#if defined _WIN32
    sprintf(logTempFilePath, "%s\\temp_%s", LOCAL_PATH, logTempFileName);
#else
    sprintf(logTempFilePath, "%s/temp_%s", LOCAL_PATH, logTempFileName);
#endif
    /* 若存在临时日志，先删除 */
    if (isFileExist(logTempFilePath))
    {
        if (remove(logTempFilePath) != 0)
        {
            return GNCDB_NO_REMOVE;
        }
    }

    /* 复制倒数第一个完整checkpoint(sta = 0)后的所有内容到临时文件 */
    rc = copyFile(logger->logFileName, seek, logTempFilePath, 0);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /* 删除原文件 */
    fclose(logger->logFile);
    if (remove(logger->logFileName) != 0)
    {
        return GNCDB_NO_REMOVE;
    }
    /* 重命名文件 */
    if (rename(logTempFilePath, logger->logFileName) != 0)
    {
        return GNCDB_SPACE_LACK;
    }
    file = fopen(logger->logFileName, "rb+");
    if (file == NULL)
    {
        return GNCDB_NOT_OPEN;
    }
    logger->logFile = file;

    return GNCDB_SUCCESS;
}

/// <summary>
/// 删除日志崩溃的恢复函数
/// </summary>
/// <param name="logger"></param>
/// <returns></returns>v
int loggerDeleteRecover(struct Logger *logger, GNCDB *db)
{
    /* 暂定不给原日志文件加锁 */
    long  filelength           = 0;
    FILE *file                 = NULL;
    char  logFileName_Temp[32] = "logTemp_";

    if (logger == NULL)
    {
        return GNCDB_FILEISNULL;
    }

    getFileLength(&filelength, logger->logFile);

    if (filelength < LOGFILE_MAXSIZE)
    {
        return GNCDB_SUCCESS;
    }
    strcat(logFileName_Temp, logger->logFileName);
    /* 日志复制到临时日志文件并且删除成功，却未能进行重命名 */
    if (isFileExist(logFileName_Temp) && !isFileExist(logger->logFileName))
    {
        /* 重命名文件 */
        if (rename(logFileName_Temp, logger->logFileName) != 0)
        {
            return GNCDB_SPACE_LACK;
        }
        file = fopen(logger->logFileName, "wb+");
        if (file == NULL)
        {
            return GNCDB_NOT_OPEN;
        }
        logger->logFile = file;

        return GNCDB_SUCCESS;
    }
    /*重新进行日志清理*/
    return loggerDelete(logger, db);
}
