#include "condvariable.h"

#ifdef _WIN32
// #include <windows.h>
#endif

#if defined __linux__ || _WIN32

#else
    CondtLockPool* global_CondtLockPool = NULL;

    void CondtInit(Condt* cond_var, int index , RETURN_CODE_TYPE* return_code) {
        char str[20];
        sprintf(str, "cond_%d", index);
        CREATE_SEMAPHORE(str, 0, 1, 0, &cond_var->condtId, &return_code);
        if (*return_code != NO_ERROR) {
            LOG(LOG_ERROR, "SLOCKing:%s", "condt init fail");
        }
        cond_var->flag = 0;
        cond_var->index = index;
    }
    int CondtLockPoolInit() {
        int index = 0;
        Condt* cond_var = NULL;
        RETURN_CODE_TYPE return_code;

        if (global_CondtLockPool != NULL) {
            return GNCDB_SUCCESS;
        }

        if (COND_COUNT <= 0) {
            return GNCDB_PARAM_INVALID;
        }

        global_CondtLockPool = my_malloc(sizeof(CondtLockPool));
        if (CondtLockPool == NULL) {
            return GNCDB_MEM;
        }

        ReadWriteLockInit(&global_CondtLockPool->latch);

        global_CondtLockPool->CondtLockMap = hashMapCreate(INTKEY, 0, NULL);
        if (global_CondtLockPool->CondtLockMap == NULL) {
            my_free(global_CondtLockPool);
            return GNCDB_MAP_CREATE_FALSE;
        }

        for (; index < COND_COUNT; index++) {
            cond_var = my_malloc(sizeof(Condt));
            if (cond_var == NULL) {
                return GNCDB_MEM;
            }
            CondtInit(cond_var, index, &return_code);

            if (return_code != NO_ERROR) {
                return GNCDB_MUTEX_INIT_FAIL;
            }
            hashMapPut(global_CondtLockPool->CondtLockMap, &cond_var->index, cond_var);
        }

        return GNCDB_SUCCESS;
    }

    Condt* CondtLockAcquire() {
        HashMapIterator* condtLockPoolIterator = NULL;
        RETURN_CODE_TYPE* return_code;
        Condt* cond_var = NULL;
        
        WriteLock(&global_CondtLockPool->latch);

        condtLockPoolIterator = createHashMapIterator(global_CondtLockPool->CondtLockMap);
        if (condtLockPoolIterator == NULL) {
            return NULL;
        }
        
        while (hasNextHashMapIterator(condtLockPoolIterator)) {
            condtLockPoolIterator = nextHashMapIterator(condtLockPoolIterator);
            if (condtLockPoolIterator->entry->flag == 0) {
                condtLockPoolIterator->entry->flag = 1;
                cond_var = condtLockPoolIterator->entry;
                break;
            }
        }
        freeHashMapIterator(&condtLockPoolIterator);

        WriteUnLock(&global_CondtLockPool->latch);

        return cond_var;
    }

    int CondtLockRelease(Condt* cond_var) {
        WriteLock(&global_CondtLockPool->latch);

        cond_var->flag = 0;

        WriteUnLock(&global_CondtLockPool->latch);

        return GNCDB_SUCCESS;
    }

    int CondtLockDestroy() {
        HashMapIterator* condtLockPoolIterator = NULL;
        Condt* cond_var = NULL;
        
        WriteLock(&global_CondtLockPool->latch);


        condtLockPoolIterator = createHashMapIterator(global_mutexLockPool->CondtLockMap);
        if (condtLockPoolIterator == NULL) {
            return NULL;
        }
        
        while (hasNextHashMapIterator(condtLockPoolIterator)) {
            condtLockPoolIterator = nextHashMapIterator(condtLockPoolIterator);
            my_free(condtLockPoolIterator->entry);
        }
        freeHashMapIterator(&condtLockPoolIterator);

        WriteUnLock(&global_CondtLockPool->latch);

        return GNCDB_SUCCESS;
    }

#endif

void condVariable_Init(Semaphore* semaphore){
#if defined __linux__ || _WIN32
    pthread_cond_init(&semaphore->lock_cond, NULL);
#else
    int return_code = 0;
    semaphore->lock_cond = CondtLockAcquire();
#endif
}

void condVariable_Destroy(Semaphore* semaphore){
    /* 1.判断参数是否为空 */
	if (semaphore == NULL) {
		return;
	}
#if defined(__linux__) || defined(_WIN32)
    pthread_cond_destroy(&semaphore->lock_cond);
#else
	CondtLockRelease(&semaphore->lock_cond);
#endif
}

int condVariable_Wait(Semaphore* semaphore){  
#if defined(__linux__) || defined(_WIN32)
    struct timespec ts;
    int ret = 0;
    clock_gettime(CLOCK_REALTIME, &ts);

    ts.tv_sec += 2; // 设置5秒的超时时间

    // ts.tv_sec = 0;  // 秒数设置为0
    // ts.tv_nsec += 500000000;  // 增加500毫秒，转换为纳秒

    // // 如果tv_nsec超过了一秒的纳秒数，需要调整tv_sec和tv_nsec
    // if (ts.tv_nsec >= 1000000000) {
    // ts.tv_nsec -= 1000000000;
    // ts.tv_sec += 1;
    // }

    ret = pthread_cond_timedwait(&semaphore->lock_cond, &semaphore->lock_mutex, &ts);
    return ret;

#endif

#if defined(__linux__) || defined(_WIN32)

#else
    int return_code = 0;
       /* todo 时间设置 */
    SYSTEM_TIME_TYPE time_out;
    time_out = 9000000000;
    WAIT_SEMAPHORE(semaphore->lock_cond->condtId, time_out, &return_code);
    return return_code;
#endif
}

void condVariable_Broadcast(Semaphore* semaphore){
#if defined(__linux__) || defined(_WIN32)
    pthread_cond_broadcast(&semaphore->lock_cond);
#else  
    SEMAPHORE_STATUS_TYPE semaphore_status;
    int return_code = 0;
    int waitingNum = 0;
    GET_SEMAPHORE_STATUS(semaphore.semaphore_id, &semaphore_status, &return_code);
    waitingNum = semaphore_status->WAITING_PROCESS;
    while(waitingNum > 0){
        SIGNAL_SEMAPHORE(semaphore.semaphore_id, return_code);
        waitingNum--; 
    }
#endif
}

void condVariable_Signal(Semaphore* semaphore){
#if defined(__linux__) || defined(_WIN32)
    pthread_cond_signal(&semaphore->lock_cond);
#else
    SEMAPHORE_STATUS_TYPE semaphore_status;
    int return_code = 0;
    SIGNAL_SEMAPHORE(semaphore.semaphore_id, &return_code);

#endif
}

void condVariable_MutexInit(Semaphore* semaphore) {
#if defined(__linux__) || defined(_WIN32)
    pthread_mutex_init(&semaphore->lock_mutex, NULL);
#else
    ReadWriteLockInit(&semaphore->lock_mutex);
#endif
}

void condVariable_MutexLock(Semaphore* semaphore) {
#if defined(__linux__) || defined(_WIN32)
    pthread_mutex_lock(&semaphore->lock_mutex);
#else
    WriteLock(&semaphore->lock_mutex);
#endif
}

void condVariable_MutexUnLock(Semaphore* semaphore) {
#if defined(__linux__) || defined(_WIN32)
    pthread_mutex_unlock(&semaphore->lock_mutex);
#else
    WriteUnLock(&semaphore->lock_mutex);
#endif
}

void condVariable_MutexDestroy(Semaphore* semaphore) {
#if defined(__linux__) || defined(_WIN32)
    pthread_mutex_destroy(&semaphore->lock_mutex);
#else
    ReadWriteLockDestroy(&semaphore->lock_mutex);
#endif
}