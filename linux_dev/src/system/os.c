/*
 * @Descripttion: 
 * @version: 
 * @Author: Alan
 * @Date: 2023-01-30 09:19:38
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-07-10 16:00:30
 */
#include <stdio.h>
#include <string.h>
#include <malloc.h>
#include "gncdbconstant.h"
#include "os.h"
#include <unistd.h>
#include <sys/stat.h> // For fstat, struct stat

#if defined _WIN32
#include <time.h>
#endif

static bool isLittleEndian = true;  //标志系统是否为小端输出输入流，默认是
double time_per_read = 0.0;
double time_per_write = 0.0;

#ifdef _WIN32
ssize_t getline(char** lineptr, size_t* n, FILE* stream) {
    size_t pos = 0;
    int c;
    size_t new_size;
    char* new_ptr;
  if (!lineptr || !n || !stream) return -1;

  *lineptr = NULL;
  *n = 0;

  while ((c = fgetc(stream)) != EOF) {
      if (pos >= *n - 1) {
        new_size = (*n == 0) ? 128 : *n * 2;
        new_ptr = my_realloc(*lineptr, new_size);
          if (!new_ptr) return -1;
          *lineptr = new_ptr;
          *n = new_size;
      }
      (*lineptr)[pos++] = (char)c;
      if (c == '\n') break;
  }

  if (pos == 0 && feof(stream)) return -1;
  (*lineptr)[pos] = '\0';
  return pos;
}
#endif


/**
 * @brief 初始化大小端标识符
 *
 * @return int
 */
int byteModeConstrcut() {
    unsigned short mode = 0x1234;
    char* pmode = (char*)&mode;
    // 如果将低字节放在低位，则是小端字节序
    isLittleEndian = *pmode == 0x34 ? true : false;
    return GNCDB_SUCCESS;
}

int osOpenFile(char* fileName, FILE** fp) {
    *fp = fopen(fileName, "rb+");
    if (*fp == NULL)
    {
        return GNCDB_NOT_OPEN;
    }
    return GNCDB_SUCCESS;
}

/**
 * @brief 从磁盘读取文件内容
 * 
 * @param file 文件句柄
 * @param seek 偏移量
 * @param size 数据大小
 * @param buf 读取的数据
 * @return int 
 */
int osRead(FILE* file, long seek, int size, BYTE** buf)
{
    int rc = 0;
#ifdef TIMING
    struct timespec start, end;
    clock_gettime(CLOCK_MONOTONIC, &start);
#endif
    rc = fseek(file, seek, SEEK_SET);
    if(rc)
    {
        return rc;
    }
    if ((*buf) == 0){
        (*buf) = my_malloc(size);
        if ((*buf) == 0) {
        return GNCDB_MEM;
        }
    }   
    fread(*buf, size, 1, file);
#ifdef TIMING
    clock_gettime(CLOCK_MONOTONIC, &end);
	//单条执行的微秒；
	time_per_read += (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
#endif
    return GNCDB_SUCCESS;
}
/**
 * @brief 向磁盘中写入文件内容，默认是追加
 * 
 * @param file 文件句柄 
 * @param buf 写入的内容
 * @param seek 偏移量
 * @param size 数据大小
 * @return int 
 */
int osWrite(FILE* file, BYTE* buf, long  seek, int size)
{
    int rc = 0;
    //获取文件长度
    long  fileLength = 0;
#ifdef TIMING
    struct timespec start, end;
    clock_gettime(CLOCK_MONOTONIC, &start);
#endif

//    if(size == 1024 && buf[123] != 0 && buf[27] == buf[12] && buf[88] == buf[77] && buf[453] == buf[57])
//    {
//        sleep(2);
//    }
    if(strlen((char*)buf) == 0 && buf[0] != 0){
        rc = 0;
    }
    rc = getFileLength(&fileLength, file);
    if (rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    //扩容 如果偏移量大于文件大小，先扩容。
    if (fileLength < seek) {
        rc = expandFileSize(file, (int)(seek - fileLength));
        if (rc != GNCDB_SUCCESS) 
            return rc;
    }
    if(seek == -1){
        rc = fseek(file, 0, SEEK_END);
    }
    else {
        rc = fseek(file, seek, SEEK_SET);
    }
    if(rc)
        return GNCDB_WRITE_FILE_FAILED;
    fwrite(buf, 1, size, file);
    fflush(file);
#ifdef TIMING
    clock_gettime(CLOCK_MONOTONIC, &end);
	//单条执行的微秒；
	time_per_write += (double)(end.tv_sec - start.tv_sec) * 1e6 + (double)(end.tv_nsec - start.tv_nsec) / 1e3;
#endif
    return GNCDB_SUCCESS;
}
/**
 * @brief Get the File Length object
 * 
 * @param len 
 * @param file 
 * @return int 
 */
int getFileLength(long* len, FILE* fp)
{
    struct stat statbuf; // 定义一个 stat 结构体变量
    if (fp == NULL)
    {
        return GNCDB_FILEISNULL;
    }


    // 使用 fileno(fp) 获取文件描述符，然后传递给 fstat
    if (fstat(fileno(fp), &statbuf) == -1)
    {
        // fstat 调用失败，返回错误码
        perror("fstat failed"); // 打印错误信息，有助于调试
        return GNCDB_INTERNAL;
    }

    // statbuf.st_size 包含了文件的大小（以字节为单位）
    // 注意：st_size 的类型通常是 off_t，这里将其赋值给 long* len
    // 在 64 位系统上，off_t 通常是 long long，确保 long* 能容纳
    *len = (long)statbuf.st_size;

    return GNCDB_SUCCESS;
}
/**
 * @brief Get the File name from path
 * 
 * @param in 输入字符串
 * @param out 输出字符串
 * @param remainDot 是否保留后缀
 * @return void 
 */
void getFileName(char* in, char* out, bool remainDot) {
    int i = 0, j = 0;
	int index = -1;
    int index_dot = -1;
	int len = strlen(in);
	for (i = len - 1; i >= 0; i--){
        if(remainDot == false && in[i] == '.' && index_dot == -1)
            index_dot = i;
        
		if ((in[i] == '/' || in[i] == '\\') && index == -1) {
			index = i;
			break;
		}
    }
    // 路径符在path末尾
	if (index + 1 == len){
        out = NULL;
		return;
    }
    if(index_dot == -1){
        index_dot = len;
    }
	for (i = index + 1; i < index_dot; i++)
		out[j++] = in[i];
	out[j] = '\0';
}
/**
 * @brief 判断一个文件是否存在
 * 
 * @param filename 
 * @return true 
 * @return false 
 */
bool isFileExist(const char* filename)
{
    //return (access(filename, F_OK) == 0);
    FILE *file = fopen(filename, "r");
    if (file != NULL)
    {
        fclose(file);
        return true;
    }
    return false;
    
}

/**
 * @brief Create a File object
 * 
 * @param filename 
 * @param file 
 * @return int 
 */
int createFile(const char* filename, FILE** file)
{
    *file = fopen(filename, "wb+");//fopen 如果文件不存在，会创建一个空文件
    if (*file == NULL)
    {
        return GNCDB_CREATE_FALSE;
    }
    return GNCDB_SUCCESS;
}
/**
 * @brief 对当前文件进行扩容size大小
 * 
 * @param file 
 * @param size 
 * @return int 
 */
int expandFileSize(FILE* file, int size)
{
    BYTE* buf = NULL;
    int i = 0;
    fseek(file, 0, SEEK_END);
    buf = (BYTE*)my_malloc(sizeof(BYTE));
    if (buf == NULL)
    {
        return GNCDB_MEM;
    }
    memset(buf, 0, sizeof(BYTE));
    for (i = 0; i < size; ++i)
    {
        fwrite(buf, 1, 1, file);
    }
    //fflush(file);
    my_free(buf);
    return GNCDB_SUCCESS;
}

/**
 * @brief 从BYTE数组中读取四字节并转为int型
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
int readInt(int* num, BYTE* buf, int* len)
{
    *len += INT_SIZE;
    *num = (buf[3] << 24) | (buf[2] << 16) | (buf[1] << 8) | buf[0];
    if (!isLittleEndian) {
        *num = ((*num & 0x000000FF) << 24) |
            ((*num & 0x0000FF00) << 8) |
            ((*num & 0x00FF0000) >> 8) |
            ((*num & 0xFF000000) >> 24);
    }

    return GNCDB_SUCCESS;
}
/**
 * @brief 写入一个int值到BYTE数组中
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
int writeInt(int num, BYTE* buf, int* len)
{
    int i = 0;
    *len += INT_SIZE;
    if (!isLittleEndian) {
        num = ((num & 0x000000FF) << 24) |
            ((num & 0x0000FF00) << 8) |
            ((num & 0x00FF0000) >> 8) |
            ((num & 0xFF000000) >> 24);
    }
    for (i = 0; i < INT_SIZE; i++) {
            buf[i] = (BYTE)(num & 0xFF);
            num >>= 8;
    }

    return GNCDB_SUCCESS;
}
/**
 * @brief 从BYTE数组中读取二字节并转为short型
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
int readShort(short* num, BYTE* buf, int* len)
{
    *len += 2;
    *num = ((short)buf[1] << 8) | (short)buf[0];
    if (!isLittleEndian) {
        *num = ((*num & 0x00FF) << 8) | ((*num & 0xFF00) >> 8);
    }
    return GNCDB_SUCCESS;
}
/**
 * @brief 写入一个short值到BYTE数组中
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
int writeShort(short num, BYTE* buf, int* len)
{
    *len += 2;
    if (isLittleEndian) {
        buf[1] = (BYTE)((num >> 8) & 0xFF);
        buf[0] = (BYTE)(num & 0xFF);
    } else {
        buf[0] = (BYTE)((num >> 8) & 0xFF);
        buf[1] = (BYTE)(num & 0xFF);
    }
    return GNCDB_SUCCESS;
}
/**
 * @brief 从BYTE数组中读取二字节并转为char型
 * 
 * @param buf0 
 * @param buf 
 * @param len 
 * @return int 
 */
int readChar(char* buf0, BYTE* buf, int* len)
{
    *len += 1;
    *buf0 = buf[0];
    return GNCDB_SUCCESS;
}
/**
 * @brief 写入一个char值到BYTE数组中
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
int writeChar(char num, BYTE* buf, int* len)
{
    if (buf == NULL || len == NULL) {
        return GNCDB_PARAMNULL;
    }
    *len += 1;
    buf[0] = num;
    return GNCDB_SUCCESS;
}
/**
 * @brief 从BYTE数组中读取四字节并转为char型
 * 这里使用union进行实现，相当于系统帮我们转换了，比较取巧。
 * https://www.thinbug.com/q/4193377
 * @param raw_num 
 * @param buf 
 * @param len 
 * @return int 
 */
union rawDouble {
    //union中的成员共享内存，所占字节为最大成员大小或为所有成员最大的对齐数的倍数
    BYTE buf[8];
    double num;
};

int readDouble(double* raw_num, BYTE* buf, int* len){
    int i = 0;
    union rawDouble raw;
    *len += 8;

    memcpy(&raw.buf, buf, 8);
    //转大端输出
    if (!isLittleEndian) {
        for (i = 0; i < 4; i++) {
            unsigned char ch = raw.buf[i];
            raw.buf[i] = raw.buf[7 - i];
            raw.buf[7 - i] = ch;
        }
    }
    *raw_num = raw.num;
    return GNCDB_SUCCESS;
}
/**
 * @brief 将float写入buf数组
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
int writeDouble(double num, BYTE* buf, int* len){
    int i = 0;
    union rawDouble raw;
    *len += 8;
    raw.num = num;
    //转小端输入
    if (!isLittleEndian) {
        for (i = 0; i < 4; i++) {
            unsigned char ch = raw.buf[i];
            raw.buf[i] = raw.buf[7 - i];
            raw.buf[7 - i] = ch;
        }
    }
    memcpy(buf, &raw.buf, 8);
    return GNCDB_SUCCESS;
}
/**
 * @brief 从buf中获取size个字节并作为string类型
 * 
 * @param str 
 * @param buf 
 * @param size 
 * @param len 
 * @return int 
 */
int readString(char** str, BYTE* buf, int size, int* len)
{
    char* strBuf = NULL;
    *len += size;
    strBuf = my_malloc(size + 1);
    if (strBuf == NULL)
    {
        return GNCDB_MEM;
    }
    memcpy(strBuf, buf, size);
    strBuf[size] = 0; // 注意必须设置结束符
    *str = strBuf;
    return GNCDB_SUCCESS;
}
/**
 * @brief 将str转换为字节数组
 * 
 * @param str 
 * @param buf 
 * @param len 
 * @return int 
 */
int writeString(char* str, BYTE* buf, int* len)
{
    int length = strlen(str);
    *len += length;
    memcpy(buf, str, length);
    return GNCDB_SUCCESS;
}
/**
 * @brief 读取size大小的字节数组 要手动free
 * 
 * @param blob 
 * @param buf 
 * @param size 
 * @param len 
 * @return int 
 */
int readBlob(BYTE* blob, BYTE* buf, int size, int* len)
{
    if (buf == NULL || len == NULL) {
        return GNCDB_PARAMNULL;
    }
    *len += size;
    memcpy(blob, buf, size);
    return GNCDB_SUCCESS;
}
/**
 * @brief 将size大小的blob复制到buf中
 * 
 * @param blob 
 * @param buf 
 * @param size 
 * @param len 
 * @return int 
 */
int writeBlob(BYTE* blob, BYTE* buf, int size, int* len)
{
    *len += size;
    memcpy(buf, blob, size);
    return GNCDB_SUCCESS;
}
/**
 * @brief 将size大小的blob复制到buf中
 * 
 * @param blob 
 * @param buf 
 * @param size 
 * @return int 
 */
int writeBlob1(BYTE* blob, BYTE* buf, int size)
{
    memcpy(buf, blob, size);
    return GNCDB_SUCCESS;
}
/**
 * @brief 从buf中获取size个字节并作为ull类型
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
//todo？大小端转化
int readULong(unsigned long long* num, BYTE* buf, int* len)
{
    int i = 0;
    *len += 8;
    *num = 0;
    if(!isLittleEndian){
        for (i = 7; i >= 0; i--)
            *num |= ((unsigned long long)buf[i] << (i * 8));
        return GNCDB_SUCCESS;
    }
    for (i = 0; i < 8; i++)
        *num |= ((unsigned long long)buf[i] << ((7 - i) * 8));
    return GNCDB_SUCCESS;
}
/**
 * @brief 将ull转换为字节数组
 * 
 * @param num 
 * @param buf 
 * @param len 
 * @return int 
 */
int writeULong(unsigned long long num, BYTE* buf, int* len)
{
    int i = 0;
    *len += 8;
    if(!isLittleEndian){
        for (i = 7; i >= 0; i--)
            buf[7 - i] = ((BYTE)num >> ((7 - i) * 8)) & 0xFF;
        return GNCDB_SUCCESS;
    }
    for (i = 0; i < 8; i++)
        buf[i] = ((BYTE)num >> ((7 - i) * 8)) & 0xFF;
    return GNCDB_SUCCESS;
}
/**
 * @brief 精确位置复制文件内容
 *
 * @param fileRead
 * @param readSeek
 * @param fileWrite
 * @return writeSeek
 */
int copyFile(char* fileRead, int readSeek, char* fileWrite, int writeSeek) {
    FILE* fpRead;  
    FILE* fpWrite;
    
    int bufferLen = 1024 * 4;  // 缓冲区长度
    char* buffer = (char*)my_malloc(bufferLen);  
    int readCount;  // 实际读取的字节数
    if ((fpRead = fopen(fileRead, "rb")) == NULL || (fpWrite = fopen(fileWrite, "wb+")) == NULL) {
        return GNCDB_PARAMNULL;
    }
    fseek(fpRead, readSeek, SEEK_SET);
    fseek(fpWrite, writeSeek, SEEK_SET);
    // 不断从fileRead读取内容，放在缓冲区，再将缓冲区的内容写入fileWrite
    while ((readCount = fread(buffer, 1, bufferLen, fpRead)) > 0) {
        fwrite(buffer, readCount, 1, fpWrite);
    }
    my_free(buffer);
    fclose(fpRead);
    fclose(fpWrite);
    return GNCDB_SUCCESS;
}
