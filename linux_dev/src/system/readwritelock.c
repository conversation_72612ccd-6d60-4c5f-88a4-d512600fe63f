/*
* @ProjectName: readwriteLock.h
*
* @Author: vera
* @Createtime: 2024-1-8  13:30
* @LastEditor: vera
* @LastEditTime:
*/

#include "readwritelock.h"

#if defined(__linux__) || defined(_WIN32)

#else

    MutexLockPool* global_mutexLockPool = NULL;

    void MutexInit(Mutex* mtx, int index, RETURN_CODE_TYPE* return_code) {
        char str[20];
        sprintf(str, "mutex_%d", index);
        CREATE_SEMAPHORE(str, 1, 1, 0, &mtx->mutex, &return_code);
        if (*return_code != NO_ERROR) {
            LOG(LOG_ERROR, "SLOCKing:%s", "mutex init fail");
        }
        mtx->flag = 0;
        mtx->index = index;
    }

    void MutexLock(Mutex* mtx, RETURN_CODE_TYPE* return_code) {
        WAIT_SEMAPHORE(mtx->mutex, 8000000000, return_code);
    }
    
    void MutexUnLock(Mutex* mtx, RETURN_CODE_TYPE* return_code) {
        SIGNAL_SEMAPHORE(mtx->mutex, return_code);
    }

    int MutexLockPoolInit() {
        int index = 0;
        Mutex* mtx = NULL;
        RETURN_CODE_TYPE return_code;

        if (global_mutexLockPool != NULL) {
            return GNCDB_SUCCESS;
        }

        if (MUTEX_COUNT <= 0) {
            return GNCDB_PARAM_INVALID;
        }

        global_mutexLockPool = my_malloc(sizeof(MutexLockPool));
        if (mutexLockPool == NULL) {
            return GNCDB_MEM;
        }

        global_mutexLockPool->MutexLockMap = hashMapCreate(INTKEY, 0, NULL);
        if (global_mutexLockPool->MutexLockMap == NULL) {
            my_free(global_mutexLockPool);
            return GNCDB_MAP_CREATE_FALSE;
        }

        for (; index < MUTEX_COUNT + 1; index++) {
            mtx = my_malloc(sizeof(Mutex));
            if (mtx == NULL) {
                return GNCDB_MEM;
            }
            MutexInit(mtx, index, &return_code);

            if (return_code != NO_ERROR) {
                return GNCDB_MUTEX_INIT_FAIL;
            }

            if (index == 0) {
                global_mutexLockPool->latch = mtx;
            } else {
                hashMapPut(global_mutexLockPool->MutexLockMap, &mtx->index, mtx);
            }
        }

        return GNCDB_SUCCESS;
    }

    Mutex* MutexLockAcquire() {
        HashMapIterator* mutexLockPoolIterator = NULL;
        RETURN_CODE_TYPE return_code;
        Mutex* mtx = NULL;
        
        MutexLock(global_mutexLockPool->latch, &return_code);
        if (return_code != NO_ERROR) {
            return GNCDB_LOCK_FAIL;
        }

        mutexLockPoolIterator = createHashMapIterator(global_mutexLockPool->MutexLockMap);
        if (mutexLockPoolIterator == NULL) {
            return NULL;
        }
        
        while (hasNextHashMapIterator(mutexLockPoolIterator)) {
            mutexLockPoolIterator = nextHashMapIterator(mutexLockPoolIterator);
            if (mutexLockPoolIterator->entry->flag == 0) {
                mutexLockPoolIterator->entry->flag = 1;
                mtx = mutexLockPoolIterator->entry;
                break;
            }
        }
        freeHashMapIterator(&mutexLockPoolIterator);

        MutexUnLock(global_mutexLockPool->latch);
        if (return_code != NO_ERROR) {
            return GNCDB_LOCK_FAIL;
        }

        return mtx;
    }

    int MutexLockRelease(Mutex* mtx) {
        RETURN_CODE_TYPE return_code;
        MutexLock(global_mutexLockPool->latch, &return_code);
        if (return_code != NO_ERROR) {
            return GNCDB_LOCK_FAIL;
        }

        mtx->flag = 0;

        MutexUnLock(global_mutexLockPool->latch, &return_code);
        if (return_code != NO_ERROR) {
            return GNCDB_LOCK_FAIL;
        }

        return GNCDB_SUCCESS;
    }

    int MutexLockDestroy() {
        HashMapIterator* mutexLockPoolIterator = NULL;
        RETURN_CODE_TYPE return_code;
        Mutex* mtx = NULL;
        
        MutexLock(global_mutexLockPool->latch, &return_code);
        if (return_code != NO_ERROR) {
            return GNCDB_LOCK_FAIL;
        }

        mutexLockPoolIterator = createHashMapIterator(global_mutexLockPool->MutexLockMap);
        if (mutexLockPoolIterator == NULL) {
            return NULL;
        }
        
        while (hasNextHashMapIterator(mutexLockPoolIterator)) {
            mutexLockPoolIterator = nextHashMapIterator(mutexLockPoolIterator);
            my_free(mutexLockPoolIterator->entry);
        }
        freeHashMapIterator(&mutexLockPoolIterator);

        MutexUnLock(global_mutexLockPool->latch);
        if (return_code != NO_ERROR) {
            return GNCDB_LOCK_FAIL;
        }

        return GNCDB_SUCCESS;
    }

#endif

void ReadWriteLockInit(struct ReadWriteLock* read_write_lock) {
#if defined(__linux__) || defined(_WIN32)
    pthread_rwlock_init(&(read_write_lock->lock), NULL);
#else
    int return_code = 0;
    read_write_lock->read_cnt = 0;
    read_write_lock->lock = MutexLockAcquire();
    read_write_lock->lock_read_cnt = MutexLockAcquire();
#endif
}

void ReadLock(struct ReadWriteLock* read_write_lock) {
#if defined(__linux__) || defined(_WIN32)
    pthread_rwlock_rdlock(&read_write_lock->lock);
#else
    int return_code = 0;
    MutexLock(read_write_lock->lock_read_cnt, &return_code);
    if (++read_cnt == 1) {
        MutexLock(read_write_lock->lock, &return_code);
    }
    MutexUnLock(read_write_lock->lock_read_cnt, &return_code);
#endif
}

void ReadUnLock(struct ReadWriteLock* read_write_lock) {
#if defined(__linux__) || defined(_WIN32)
    pthread_rwlock_unlock(&read_write_lock->lock);
#else
    int return_code = 0;
    MutexLock(read_write_lock->lock_read_cnt, &return_code);
    if (--read_cnt == 0) {
        MutexUnLock(read_write_lock->lock, &return_code);
    }
    MutexUnLock(read_write_lock->lock_read_cnt, &return_code);
#endif
}

void WriteLock(struct ReadWriteLock* read_write_lock) {
#if defined(__linux__) || defined(_WIN32)
    pthread_rwlock_wrlock(&read_write_lock->lock);
#else
    int return_code = 0;
    MutexLock(read_write_lock->lock, &return_code);
#endif
}

void WriteUnLock(struct ReadWriteLock* read_write_lock) {
#if defined(__linux__) || defined(_WIN32)
    pthread_rwlock_unlock(&read_write_lock->lock);
#else
    int return_code = 0;
    MutexUnLock(read_write_lock->lock, &return_code);
#endif 
}

void ReadWriteLockDestroy(struct ReadWriteLock* read_write_lock) {
#if defined(__linux__) || defined(_WIN32)
    pthread_rwlock_destroy(&read_write_lock->lock);
#else
    int return_code = 0;
    MutexLockRelease(read_write_lock->lock);
    MutexLockRelease(read_write_lock->lock_read_cnt);
#endif 
}