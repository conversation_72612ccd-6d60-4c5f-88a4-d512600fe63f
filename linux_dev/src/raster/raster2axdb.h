#include "gncdbconstant.h"
#include "gncdb.h"
#include "transaction.h"
#include "os.h"
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdarg.h>
#include <math.h>
#include <sys/stat.h>
#include <dirent.h>
#include <jpeglib.h>

#include <cairo/cairo.h>
#include <cairo/cairo-svg.h>
#include <cairo/cairo-pdf.h>
// #include <cairo/cairo-ft.h>
#define     MIN_LON          -180                   //最小经度
#define     MAX_LON          180                    //最大经度
#define     MIN_LAT          -90                    //最小纬度
#define     MAX_LAT          90                    //最大纬度
#define     SAMPLE_8BIT      8                      //8bit数据
#define     SAMPLE_16BIT     16                     //16bit数据
#define     PIXEL_DEM        17                     //高程数据
#define     PIXEL_RGB        18                     //RGB数据
#define		M_MAX_ROWS       50000					// 最大行数
#define		QUERY_POINT_SIZE       18					// 最大行数
#define		RT_HGT           19					// 最大行数
#define		RT_RGB           20					// 最大行数
#define 	RT_BLOCK_START			0xfa
#define 	RT_LITTLE_ENDIAN			0x01
#define 	RT_BIG_ENDIAN				0x00
#define 	RT_DATA_START				0xc8
#define 	RT_DATA_END				0xc9
#define 	RT_MASK_START				0xb6
#define 	RT_MASK_END				0xb7
#define 	RT_BLOCK_END			0xf0
#define     RT_SURFACE_IMG 			2671
#define     NUM 					16
typedef struct rt_raster_t* rt_raster;
typedef struct rt_priv_raster_t* rt_priv_raster;
typedef struct rt_coverage_t* rt_coverage;
typedef struct rt_tileallocator_t* rt_tileallocator;
typedef struct rt_tile_t* rt_tile;
typedef struct rt_geo_extent_t* rt_geo_extent;
typedef struct rt_geo_type_t* rt_geo_type;
typedef struct rt_resolution_t* rt_resolution;
typedef struct rt_tile_decoder_t* rt_tile_decoder;
typedef struct rt_tile_ref_t* rt_tile_ref;
typedef struct rt_tile_in_t* rt_tile_in;
typedef struct rt_tile_out_t* rt_tile_out;
typedef struct rt_section_pyramid_t* rt_section_pyramid;
typedef struct rt_rgb_bitmap_t* rt_rgb_bitmap;
typedef struct rt_rgb_context_t* rt_rgb_context;
typedef struct rt_section_t* rt_section;

enum QueryType{
    RASTER_QUERY_TYPE_POINT,
    RASTER_QUERY_TYPE_LINE,
    RASTER_QUERY_TYPE_RECT,
    RASTER_QUERY_TYPE_CIRCLE
};

typedef struct Querypoint
{
    double longitude;
    double latitude;
    short result;
	struct Querypoint *next;
}Querypoint;

// 定义参数类型枚举
typedef enum {
    INT_TYPE,
    FLOAT_TYPE,
    CHAR_TYPE,
    BLOB_TYPE
} Partype;

//定义准备执行的sql的上下文
typedef struct {
    char *original_sql;      // 原始SQL的拷贝
    size_t *param_positions; // 原始问号的位置数组
    size_t num_params;       // 总参数个数（问号数量）
    size_t total_offset;     // 累计偏移量（因替换导致的位置变化）
    char *modified_sql;      // 当前修改后的SQL缓冲区
    int last_insert_id;      // 当前sql的目标表中的最新的自增id
	char *tablename;
    const char *coveragename;      // 覆盖层名称
    size_t buffer_size;      // 缓冲区大小（防止溢出）
} QueryContext;

//定义coverage
struct  rt_coverage_t{
	const char *coverageName;
	unsigned char sampleType;
	unsigned char pixelType;
	unsigned char nBands;
	unsigned int tileWidth;
	unsigned int tileHeight;
	int srid;
	double xres;
	double yres;
};

//定义查询结果
typedef struct QueryResult{
    int count;
    int columnum;
    int offset;
    char **results;
}QueryResult;

//定义瓦片分配器
struct rt_tileallocator_t
{
    rt_coverage coverage;
	int srid;
	double maxx;
	double miny;
	unsigned int tile_w;
	unsigned int tile_h;
	double xres;
	double yres;
	unsigned char origin_type;
	int quality;
	// const void *origin;
    rt_priv_raster origin;
	rt_tile first;
	rt_tile last;
};

struct rt_tile_decoder_t
{
	void *opaque_thread_id;
	int tile_id;
	unsigned char *blob_data;
	int blob_data_sz;
	unsigned char *outbuf;
	unsigned char *mask;
	unsigned int width;
	unsigned int height;
	unsigned char sample_type;
	unsigned char num_bands;
	// unsigned char auto_band;
	// unsigned char syntetic_band;
	// unsigned char red_band_index;
	// unsigned char green_band_index;
	// unsigned char blue_band_index;
	// unsigned char nir_band_index;
	double x_res;
	double y_res;
	int scale;
	double minx;
	double maxy;
	double tile_minx;
	double tile_maxy;
	// rl2PrivPixelPtr no_data;
	// rl2PrivRasterSymbolizerPtr style;
	// rl2PrivRasterStatisticsPtr stats;
	rt_raster raster;
	// rl2PrivPalettePtr palette;
	int retcode;
};

//定义瓦片
struct rt_tile_t
{
	struct rt_tileallocator_t *mother;
	void *opaque_thread_id;
	rt_raster raster;
	unsigned int row;
	unsigned int col;
	double minx;
	double miny;
	double maxx;
	double maxy;
	int retcode;
	unsigned char *blob_data;
	// unsigned char *blob_even;
	int blob_data_sz;
	// int blob_even_sz;
	struct rt_tile_t *next;
};

//定义统配raster
struct rt_priv_raster_t
    {
	const char *path;
	FILE *tmp;
	unsigned int width;
	unsigned int height;
	int srid;
	double xres;
	double yres;
	double minX;
	double minY;
	double maxX;
	double maxY;
	double noData;
    unsigned char numBands;
	unsigned char sample_type;
	rt_raster rasterData;
};

struct rt_geo_extent_t
{
	double extent_minx;
    double extent_maxx;
    double extent_miny;
    double extent_maxy;
};

struct rt_geo_type_t
{
	int numbands;
	int sample_type;
	int pixeltype;
};

struct rt_resolution_t
{
	double xres;
	double yres;
	int tile_width;
	int tile_height;
};

/*1.定义raster结构体*/
struct rt_raster_t
{
    // uint16_t numBands;

    // double scaleX;
    // double scaleY;
    // double ipX;
    // double ipY;
    // double skewX;
    // double skewY;

    // int32_t srid;
    // uint16_t width;
    // uint16_t height;
    // int16_t* data;

	unsigned char sampleType;
	unsigned char pixelType;
	unsigned char nBands;
	unsigned int width;
	unsigned int height;
	double minX;
	double minY;
	double maxX;
	double maxY;
	int Srid;
	double hResolution;
	double vResolution;
	unsigned char *rasterBuffer;
	unsigned char *maskBuffer;
	int alpha_mask;
};

struct rt_tile_in_t
{
	int tile_id;
	double cx;
	double cy;
	rt_tile_in next;
};

struct rt_tile_ref_t
{
	rt_tile_in child;
	rt_tile_ref next;
};

struct rt_tile_out_t
{
	unsigned int row;
	unsigned int col;
	double minx;
	double miny;
	double maxx;
	double maxy;
	rt_tile_ref first;
	rt_tile_ref last;
	rt_tile_out next;
};

//in是低层次瓦片链表
//out是由低层次瓦片链表的部分瓦片的集合
struct rt_section_pyramid_t
{
	int section_id;
	int scale;
	unsigned char sample_type;
	unsigned char pixel_type;
	unsigned char num_samples;
	unsigned char compression;
	int quality;
	int srid;
	unsigned int full_width;
	unsigned int full_height;
	double res_x;
	double res_y;
	unsigned int scaled_width;
	unsigned int scaled_height;
	double tile_width;
	double tile_height;
	double minx;
	double miny;
	double maxx;
	double maxy;
	int tile_startid;
	rt_tile_in first_in;
	rt_tile_in last_in;
	rt_tile_out first_out;
	rt_tile_out last_out;
};

typedef struct
{
	int argc;
	char **azColName;
	char **argv;
}callBack;

struct rt_rgb_bitmap_t
{
    int width;
    int height;
    unsigned char *rgba;
    cairo_surface_t *bitmap;
    cairo_pattern_t *pattern;
};

struct rt_rgb_context_t
{
/* a Cairo based painting context */
    int type;
    cairo_surface_t *surface;
    cairo_surface_t *clip_surface;
    cairo_t *cairo;
    cairo_t *clip_cairo;
    // struct rl2_graphics_pen current_pen;
    // struct rl2_graphics_brush current_brush;
    double font_red;
    double font_green;
    double font_blue;
    double font_alpha;
    int with_font_halo;
    double halo_radius;
    double halo_red;
    double halo_green;
    double halo_blue;
    double halo_alpha;
    struct rl2_advanced_labeling *labeling;
};

struct rt_section_t
{
	char *sectionName;
	unsigned char Compression;
	unsigned int tileWidth;
	unsigned int tileHeight;
	rt_raster Raster;
};

/*API接口部分*/
/*创建覆盖层*/
int GNCDB_CreateCoverage(GNCDB *db,const char *coveragename,unsigned char sample,
unsigned char pixel,unsigned char num_bands,unsigned short tile_width,
unsigned short tile_height,int srid,double xres,double yres);

int executorCreateCoverage(GNCDB *db,const char *coveragename,unsigned char sample,
unsigned char pixel,unsigned char num_bands,unsigned short tile_width,
unsigned short tile_height,int srid,double xres,double yres);

bool check_coverage_param(unsigned char sample,
unsigned char pixel,unsigned char num_bands,unsigned short tile_width,
unsigned short tile_height,double xres,double yres);

/*2.导入栅格数据到覆盖层*/
int GNCDB_ImportRaster(GNCDB *db, const char *src_path,const char *coveragename, int srid,bool pyramid);
int executorImportRaster(GNCDB *db, const char *src_path,const char *coveragename, int srid,bool pyramid);
int createPyrmaidPreview(GNCDB *db,rt_coverage coverage,rt_priv_raster origin,const char *coveragename,int section_id);
/*3.查询栅格数据*/
int GNCDB_RasterPointQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double longitude,double latitude);
int GNCDB_RasterLineQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double start_longitude,double start_latitude,double end_longitude,double end_latitude,int step);
// int GNCDB_RasterRectangleQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double start_longitude,double start_latitude,double end_longitude,double end_latitude);
/*圆查询，isContain是判断是否包括边界的变量*/
int GNCDB_RasterCircleleQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double center_longitude,double center_latitude,double radious,bool isContain);
int getPointQueryMemorySize();
int GNCDB_RasterRectangleQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double start_longitude,double start_latitude,double end_longitude,double end_latitude,bool isContain);
int getLineQueryMemorySize(GNCDB *db,const char *coveragename,int *result_size ,double start_longitude,double start_latitude,double end_longitude,double end_latitude,int step);
int getCircleleQueryMemorySize(GNCDB *db,const char *coveragename,int *result_size ,double center_longitude,double center_latitude,double radious,bool isContain);
int getRectangleQueryMemorySize(GNCDB *db,const char *coveragename,int *result_size ,double start_longitude,double start_latitude,double end_longitude,double end_latitude,bool isContain);
/*4.删除栅格数据*/
int GNCDB_DeleteRaster(GNCDB *db,const char *coveragename,const char *filename);
int executorDeleteRaster(GNCDB *db,const char *coveragename,const char *filename);
int executorDeleteCoverage(GNCDB *db,const char *coveragename);

/*5.修改栅格数据*/
int GNCDB_UpdateRaster(GNCDB *db,const char *coveragename,const char *src_path,double longitude,double latitude,int16_t newvalue,int srid);
int executorUpdateRaster(GNCDB *db,const char *coveragename,double longitude,double latitude,int16_t newvalue);
int executorUpdateFile(GNCDB *db,const char *src_path,const char *coveragename,int srid);
/*获取线查询的查询点*/
int Line_query_get_points(char *querypoints,rt_geo_extent tile_bound,double start_longitude,double start_latitude,double end_longitude,double end_latitude,rt_resolution resolution,int *width,int *height,int step);

int Rectangle_query_get_points(char *querypoints,rt_geo_extent tile_bound,double start_longitude,double start_latitude,double end_longitude,double end_latitude,rt_resolution resolution,int *width,int *height,bool isContain);

int Circle_query_get_points(char *querypoints,rt_geo_extent tile_bound,double center_longitude,double center_latitude,double radious,rt_resolution resolution,int *width,int *height,bool isContain);

int updateRasterExtent(GNCDB *db,const char *coveragename);

/*6.预览地图数据*/
int GNCDB_previewRaster(GNCDB *db,const char *coveragename,char *dst_path,int out_width,int out_height,double minx,double maxy,double xres,double yres);
int executorPreviewRaster(GNCDB *db,const char *coveragename,char *dst_path,int out_width,int out_height,double minx,double maxy,double xres,double yres);
int find_data_scale(GNCDB *db,rt_coverage cvg,double *xres,double *yres,int *sec_level,int *scale);
bool is_unstrict_equal(double first,double second);
int raster_to_jpeg (rt_raster raster, unsigned char **jpeg, int *jpeg_size, int quality);
int blob_to_jpeg(const unsigned char *pixels,const unsigned char *mask,
			  unsigned int width, unsigned int height,
			  unsigned char sample_type, unsigned char pixel_type,
			  unsigned char **jpeg, int *jpeg_size, int quality);
static int
compress_jpeg (unsigned short width, unsigned short height,
	       unsigned char sample_type, unsigned char pixel_type,
	       const unsigned char *pixel_buffer,
	       const unsigned char *mask_buffer,
	       unsigned char **jpeg, int *jpeg_size, int quality);
int jpg_to_file (char *dst_path, unsigned char *blob, int blob_size);
int write_to_jgw(char *worldFile,double xres,double yres,double minx,double maxy);
int rebuild_sample(int scale,unsigned int *xwidth, unsigned int *xheight,
		      unsigned char sample_type,unsigned char num_bands,const void *pixels_odd, void **pixels, int *pixels_sz);
int Get_target_tile_id(GNCDB *db,const char *coveragename,double xres,double yres,rt_geo_extent geo_bound,int pyramid_level,varArrayList *tileIdList);
void section_pyramid_init(rt_section_pyramid sec_pyr, int section_id, int sec_width, int sec_height, 
                          unsigned char sampleType, unsigned char pixelType, unsigned char nBands, 
                          int srid, double build_xres, double build_yres, double tileWidth, 
                          double tileHeight, double minx, double miny, double maxx, double maxy, 
                          int scale);
static int
put_tile_into_pyr (rt_section_pyramid pyr,
				  int tile_id, double minx,
				  double miny, double maxx, double maxy);
int create_tile_out (rt_section_pyramid sec_pyr, double out_minx, double out_miny, double out_maxx, double out_maxy, int row, int col);
int insert_higher_level_to_db(GNCDB *db, rt_coverage cvg,rt_priv_raster origin, const char *coveragename,rt_section_pyramid sec_pyr,int pyramid_level, int tileWidth, int tileHeight);
rt_rgb_context rgb_context_construct(int tile_width,int tile_height);
void contextdestory (rt_rgb_context ctx);
int raster_data_to_RGBA (rt_raster raster, unsigned char **buffer,int *buf_size);
unsigned char *load_tile_from_db (GNCDB *db, rt_coverage cvg,rt_priv_raster origin,const char *coveragename,int pyramid_level,int tile_startid,int tile_id);
rt_rgb_bitmap rl2_graph_create_bitmap (unsigned char *buf_in, int tileWidth, int tileHeight);
void rl2_graph_draw_rescaled_bitmap (rt_rgb_context ctx,rt_rgb_bitmap base_tile,double x_scale,double y_scale,int x,int y);
unsigned char *rl2_graph_get_context_rgb_array (rt_rgb_context ctx);
unsigned char *rl2_graph_get_context_alpha_array (rt_rgb_context ctx, int *hald_transparent);
void rl2_graph_destroy_bitmap (rt_rgb_bitmap base_tile);
int insert_high_level_tiles(GNCDB *db,const char *coveragename, unsigned char * blob_data, int blob_data_size,
	       int pyr_level, int section_id, int srid, double minx,
	       double miny, double maxx, double maxy);
/*确定直线从左到右的朝向，1为下降，其他为0*/
int reshape(double start_longitude,double start_latitude,double end_longitude,double end_latitude,
double *leftlon,double *leftlat,double *rightlon,double *rightlat);
/*工具性函数*/
char * strcatetablename(const char *coveragename, const char *suffix);
/*检查文件的参数和覆盖层的参数是否匹配*/
int is_confident(rt_priv_raster raster_origin,rt_coverage cvg,bool pyramid);

void query_context_reset(QueryContext *sqlctx);

/*跟新主键id*/
void update_lastinsertid(QueryContext *ctx);

int load_single_tile_from_db(GNCDB *db,int tile_id,const char *coveragename,BYTE **buffer,int *blob_size);

char* extract_basename(const char *src_path);

int get_lastinsertid(QueryContext *ctx);

char *get_coveragename(QueryContext *ctx);

int get_file_size(rt_priv_raster origin,double *minx,double *maxx,double *miny,double *maxy,
double *xres,double *yres,int *width,int *height);

/*查找coveragename和table_suffix组合构成的表中最大的filed的值*/
int insert_primaryid(GNCDB *db,const char *coveragename,const char *table_suffix,const char *filedname,QueryContext *cxt);

int get_tile_size(rt_coverage cvg,int *tile_width,int *tile_height);

/*数据库与磁盘交互函数*/
int get_coverage_fromdb(GNCDB *db,const char *coveragename,rt_coverage *coverage);

rt_priv_raster read_raster_from_disk(const char *src_path,unsigned char pixelType,char **filename);

rt_priv_raster read_hgt_from_disk(const char *src_path,char **filename);
rt_priv_raster read_jpg_from_disk(const char *src_path,char **filename);
int insert_levels(GNCDB *db,double xres,double yres,int pyramid_level,QueryContext *sqlctx);

int insert_sections(GNCDB *db,const char *filename,int srid,int width,int height,
    double minx,double miny,double maxx,double maxy,int *section_id,QueryContext *sqlctx);

int insert_tiles(GNCDB *db,int section_id,rt_coverage cvg,rt_priv_raster origin_raster,int tile_w,int tile_h,double xres,
double yres,double minx,double maxx,double miny,double maxy,int width,int height,QueryContext *sqlctx);

void encode_tile (rt_tile cur_tile);

int insert_single_tile(GNCDB *db,unsigned char *blob_data,int blob_data_sz,
int section_id,int srid,double tile_minx,double tile_miny,double tile_maxx,
double tile_maxy,QueryContext *sqlctx_tils);

int insert_some_tile(GNCDB *db,rt_tile first_tile,int section_id,QueryContext *sqlctx_tils,int tile_num);

void get_tile (rt_tile cur_tile);

double getRowOffset(double lat,double yres);
double getColOffset(double lon,double xres);

/*lat相对于基准纬度的偏移量，以度为单位*/
double getBaseRowOffset(double baseLat,double lat,double yres);
/*lon相对于基准经度的偏移量，以度为单位*/
double getBaseColOffset(double baseLon,double lon,double xres);


rt_raster get_tile_from_file(rt_coverage cvg,rt_priv_raster priv_raster,unsigned char file_type,int row,int col);

rt_raster rasterConstruct (unsigned int tile_w, unsigned int tile_h,
           unsigned char sample_type, unsigned char pixel_type,
		   unsigned char numbands, unsigned char *bufpix,
		   int bufpix_size,unsigned char *mask, int mask_size);

int read_from_hgt(rt_priv_raster priv_raster,int tile_width,int tile_height, 
unsigned char sample_type,unsigned char pixel_type, unsigned char num_bands,
unsigned int startRow, unsigned int startCol,unsigned char **pixels, int *pixels_sz);

static int read_hgt_scanlines (rt_priv_raster priv_raster, unsigned short width,unsigned short height, 
                    unsigned char sample_type,unsigned char numbands,unsigned int startRow,
                    unsigned int startCol, unsigned char *pixels);

int read_from_rgb(rt_priv_raster priv_raster,int tile_width,int tile_height,
unsigned char sample_type,unsigned char pixel_type, unsigned char num_bands,
unsigned int startRow, unsigned int startCol,unsigned char **pixels, int *pixels_sz);

static int
read_rgb_scanlines (rt_priv_raster priv_raster, unsigned short width,unsigned short height, 
                    unsigned char sample_type,unsigned char numbands,unsigned int startRow,
                    unsigned int startCol, unsigned char *pixels);

void encode_tile (rt_tile cur_tile);

int raster_encode(rt_raster raster,unsigned char **blob_data,int *blob_data_sz,
    int quality,int litle_endian);
//把raster缓冲区的数据取到pixelbuffer
int blob_data_rows(rt_raster raster,int *data_rows,int *row_stride,unsigned char **pixelbuffer,
int *data_size,int little_endian);

void feed_data_uint16(void *rasterbuffer,unsigned int width,
unsigned int height, unsigned int nBands, void *data,int swap);
void feed_data_uint8(void *rasterbuffer,unsigned int width,
unsigned int height, unsigned int nBands, void *data);

int set_section_startid(GNCDB *db,char *tablename,char *targetColumn,
void *value,enum FieldType targeType,char *filterColumn,void *filterValue,enum FieldType filterType);

//将一个坐标点转换到其所在的栅格的中心点
void convertLatLonToGridCenter(double *lon,double *lat,rt_resolution resolution);

//将一个从瓦片从blob解码出来
void decode_tile(rt_tile_decoder decoder);
//将瓦片的数据复制进缓冲区
int rl2_copy_raw_pixels_transparent
	(rt_raster raster, unsigned char *outbuf, unsigned char *mask,
	 unsigned int width, unsigned int height, unsigned char sample_type,
	 unsigned char num_bands, double x_res,double y_res, double minx, double maxy, double tile_minx,
	 double tile_maxy);

//获取一个区域所在的瓦片的集合
void getTileIdSet(GNCDB *db,varArrayList *tileIdList,rt_geo_extent tile_bound,rt_resolution resolution,const char *coverage_sec);
//根据经纬度获取该点所在的瓦片id
int getTileId(GNCDB *db,double lon,double lat,rt_resolution resolution,char *coverage_sec);
//拼接目标区域的数据
void copy_int16_raw_pixels_transparent (const short *buffer,const unsigned char *mask,
short *outbuf,unsigned char *outmask,unsigned short width,unsigned short height,
unsigned char num_bands,double x_res, double y_res,double minx, double maxy,
double tile_minx, double tile_maxy,unsigned short tile_width,unsigned short tile_height);
void copy_uint8_raw_pixels_transparent (const unsigned char *buffer,const unsigned char *mask,
char *outbuf,unsigned char *outmask,unsigned short width,unsigned short height,
unsigned char num_bands,double x_res, double y_res,double minx, double maxy,
double tile_minx, double tile_maxy,unsigned short tile_width,unsigned short tile_height);

//获取coverage的x、y方向的分辨率以及瓦片的宽度和高度
int getCoverageResolution(GNCDB *db,rt_resolution resolution,const char *coveragename);
//获取coverage的数据类型、数据位数、波段数
int getCoverageType(GNCDB *db,rt_geo_type coveragetype,const char *coveragename);


static int check_blob_data (const unsigned char *blob, int blob_sz, unsigned int *xwidth,
unsigned int *xheight, unsigned char *xsample_type,
unsigned char *xpixel_type, unsigned char *xnum_bands);

//取整
int customRound(double maxvalue,double comparevalue,double res);
//获取Blob的大小
int getBlobSize(GNCDB *db,const char *coveragename,int tile_id);

static int do_run_decoder_children(rt_tile_decoder *thread_slots, int thread_count);

static void
start_decoder_thread (rt_tile_decoder decoder);

double getTileBound(GNCDB *db,double lon,double lat,rt_resolution resolution,int index);
double getTileBounds(GNCDB *db,const char *coveragename,int tileId,int index);

int getTileOffset(double lon,double lat,rt_resolution resolution);
//根据swap的值移动位，就是高位和地位互换
static void
do_copy_uint16 (int swap, const unsigned short *p_odd, unsigned short *buf,
		unsigned short width, unsigned short odd_rows, unsigned char num_bands);

static int
build_pixel_buffer (int swap, int scale, unsigned int *xwidth,
		    unsigned int *xheight, unsigned char sample_type,
		    unsigned char num_bands, unsigned short rows,
		    const void *pixels_odd, void **pixels, int *pixels_sz);
//解码，将Blob数据解码成raster数据
rt_raster raster_decode(int scale,const unsigned char *blob_data,int blob_data_sz,int *err_code);
//将tileIdset中的瓦片从数据库拿到内存并提取目标区域部分放到outbuf
int loadTile_from_db(GNCDB *db, varArrayList* tileIdset,unsigned char *outbuf,int buffer_sz,
const char *coveragename,int width,int height,double minx,double maxy,
int num_bands,int sample_type,rt_resolution resolution,int max_threads,int scale);

rt_raster cateTile(GNCDB *db, varArrayList* tileIdset,const char *coveragename,int width,int height,
double minx,double maxy,int num_bands,int sample_type,rt_resolution resolution,int max_threads,int *err_code);

//辅助函数
rt_tileallocator tileallocatorconstruct(rt_coverage cvg,double maxx,double miny,unsigned int tile_w,
unsigned int tile_h,double res_x,double res_y,rt_priv_raster origin,unsigned char type);

void tileallocatordestory(rt_tileallocator tileallocator);

void rasterDestory(rt_raster raster);


void addtileintoallocator(rt_tileallocator tileact, int row, int col, double tile_minx, double tile_maxy);

void tiledestory(rt_tile tile);

void TileCleanup (rt_tile pTile);

int get_pixelsize(unsigned char sampleType);

void start_tile_thread(rt_tile tile);

int getRow(double lat,double yres);

int getCol(double lon,double xres);
/*获取纬度相对于基准的绝对偏移，以格为单位*/
int getBaseRow(double baseLat,double lat,double yres);
/*获取经度相对于基准的绝对偏移，以格为单位*/
int getBaseCol(double baseLon,double lon,double xres);
/*根据偏移计算经度*/
double getBaseLon(int x,double baseLon,double xres);
/*根据偏移计算纬度*/
double getBaseLat(int y,double baseLat,double yres);
/*计算x1、y1、x2、y2构成的直线与斜率为k，截距为b的直线是否相交*/
bool isLineSegmentIntersect(double x1, double y1, double x2, double y2, double k, double b);

/*计算行为x列为y的栅格点与直线是否有交点*/
int isIntersect(int x,int y,double k,double b,double w,double h,double baseLon,double baseLat);

double getLon(int col,double xres,double left_bound_lon);

double getLat(int row,double yres,double top_bound_lat);
/*判断栅格和圆是否有交集*/
bool isTntersectCircle(double x,double y,double w,double h,double cenx,double ceny,double radious);
/*判断栅格是否在圆内*/
bool isInCircle(double x,double y,double w,double h,double cenx,double ceny,double radious);

void getHgtFileName(char *filename,double leftup_lon,double leftup_lat);

int getTileStartId(GNCDB *db,char *filename,int *tileStartId,const char *coverage_sec);

static unsigned short
swapUINT16 (unsigned short value)
{
/* swaps a UINT16 respecting declared endiannes */
    union cvt
    {
	unsigned char byte[4];
	unsigned short value;
    } convert1;
    union cvt convert2;
    convert1.value = value;
    convert2.byte[0] = convert1.byte[1];
    convert2.byte[1] = convert1.byte[0];
    return convert2.value;
};

static int
isLittleEnsian()
{
	int isLittleEndian = 0;
    unsigned short mode = 0x1234;
    char* pmode = (char*)&mode;
    // 如果将低字节放在低位，则是小端字节序
    isLittleEndian = *pmode == 0x34 ? 1 : 0;
	return isLittleEndian;
};

static int
pack_rle_rows (rt_raster raster, unsigned char *in,
	       unsigned char **pixels, int *size)
{
/* creating an RLE encoded 1-BIT pixel buffer */
    int sz = 0;
    unsigned char *p_in = in;
    unsigned char *rle;
    unsigned char *p_out;
    unsigned int row;
    unsigned int col;

    for (row = 0; row < raster->height; row++)
      {
	  int cnt = 0;
	  int pix = *p_in;
	  for (col = 0; col < raster->width; col++)
	    {
		/* computing the required size */
		if (pix == *p_in)
		  {
		      if (cnt == 128)
			{
			    sz++;
			    cnt = 1;
			}
		      else
			  cnt++;
		  }
		else
		  {
		      sz++;
		      pix = *p_in;
		      cnt = 1;
		  }
		p_in++;
	    }
	  sz++;
      }

    rle = malloc (sz);
    p_out = rle;
    p_in = in;
    for (row = 0; row < raster->height; row++)
      {
	  char byte;
	  int cnt = 0;
	  int pix = *p_in;
	  for (col = 0; col < raster->width; col++)
	    {
		/* RLE encoding */
		if (pix == *p_in)
		  {
		      if (cnt == 128)
			{
			    if (pix == 1)
			      {
				  byte = 127;
				  *p_out++ = byte;
			      }
			    else
			      {
				  byte = -128;
				  *p_out++ = byte;
			      }
			    cnt = 1;
			}
		      else
			  cnt++;
		  }
		else
		  {
		      if (pix == 1)
			{
			    byte = cnt - 1;
			    *p_out++ = byte;
			}
		      else
			{
			    byte = cnt * -1;
			    *p_out++ = byte;
			}
		      pix = *p_in;
		      cnt = 1;
		  }
		p_in++;
	    }
	  if (pix == 1)
	    {
		byte = cnt - 1;
		*p_out++ = byte;
	    }
	  else
	    {
		byte = cnt * -1;
		*p_out++ = byte;
	    }
      }
    *pixels = rle;
    *size = sz;
    return 1;
};

static int
unpack_rle (unsigned short width, unsigned short height,
	    const unsigned char *pixels_in, int pixels_in_sz,
	    unsigned char **pixels, int *pixels_sz)
{
/* unpacking an RLE encoded 1-BIT raster */
    unsigned char *buf;
    int buf_size;
    int col;
    int byte;
    int i;
    unsigned char px;
    int row_stride;
    int row_no;
    int cnt;
    unsigned char *p_out;
    const char *p_in;

    p_in = (char *) pixels_in;
    row_stride = 0;
    row_no = 0;
    for (col = 0; col < pixels_in_sz; col++)
      {
	  /* checking the encoded buffer for validity */
	  byte = *p_in++;
	  if (byte < 0)
	      row_stride += byte * -1;
	  else
	      row_stride += byte + 1;
	  if (row_stride == width)
	    {
		row_stride = 0;
		row_no++;
	    }
	  else if (row_stride > width)
	      goto error;
      }

    buf_size = width * height;
    buf = malloc (buf_size);
    if (buf == NULL)
	return 0;

    p_in = (char *) pixels_in;
    p_out = buf;
    row_stride = 0;
    row_no = 0;
    for (col = 0; col < pixels_in_sz; col++)
      {
	  /* decoding the buffer */
	  byte = *p_in++;
	  if (byte < 0)
	    {
		px = 0;
		cnt = byte * -1;
	    }
	  else
	    {
		px = 1;
		cnt = byte + 1;
	    }
	  for (i = 0; i < cnt; i++)
	      *p_out++ = px;
      }

    *pixels = buf;
    *pixels_sz = buf_size;
    return 1;
  error:
    return 0;
}

static int
rescale_mask (int scale, unsigned short *xwidth, unsigned short *xheight,
	      unsigned char *mask_pix, unsigned char **mask, int *mask_sz)
{
/* rescaling the transparency mask */
    unsigned short width = 0;
    unsigned short height = 0;
    unsigned char *buf = NULL;
    unsigned char *p_in = mask_pix;
    unsigned char *p_out;
    int buf_size;

	for(int i=0;i < *xwidth;i += scale)
	{
		width++;
	}
	for(int i=0;i < *xheight;i += scale)
	{
		height++;
	}
    buf_size = width * height;
    buf = malloc (buf_size);
    if (buf == NULL)
	{
		return 0;
	}
	p_out = buf;
	for(int row = 0;row < *xheight;row += scale)
	{
		for(int col = 0;col < *xwidth;col += scale)
		{
			*p_out++ = *p_in++;
			p_in += scale - 1;
		}
		p_in += (scale - 1) * (*xwidth);
	}
    *mask = buf;
    *mask_sz = buf_size;
    return 1;
}

// static unsigned char
// unpremultiply (unsigned char c, unsigned char a)
// {
// /* Cairo has premultiplied alphas */
//     double x = ((double) c * 255.0) / (double) a;
//     if (a == 0)
// 	return 0;
//     return (unsigned char) x;
// }

void write_hex_to_file(const char *data, const char *filename,int len);
int write_blob_tofile(const char *data, const char *filename,int len);
int read_jpg_dimensions(const char *input_filename, int *width, int *height);
int get_jpg_location(const char *worldFilePath,double *lon,double *lat,double *xres,double *yres);
void section_pyramid_destory(rt_section_pyramid sec_pyr);
void tile_in_destory(rt_tile_in tile);
void tile_out_destory(rt_tile_out tile);
void getJgwFilePath(const char *src_path,char **filePath);
QueryResult *queryResultCreate();
void queryResultDestroy(QueryResult *qr);

/*预raster构造函数*/
rt_priv_raster privRasterConstruct();
/*预raster销毁函数*/
void privRasterDestroy(rt_priv_raster priv_raster);
/*将一个点的经纬度坐标加入查询结果*/
char *addNewPointRelative(int row,int col,double baseLon,double baseLat,double xres,double yres,char *p_result);
char *addNewPointAbsolute(double lon,double lat,char *p_result);
/*根据经纬度坐标查询raster缓冲区中的数据，将结果放到p_result的对应位置*/
void queryPoint(double *lon,double *lat,char *p_result,int offset,rt_raster raster,double xres,double yres);



