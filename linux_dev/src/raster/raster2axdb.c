#include "raster2axdb.h"
int myCallBacks(void*data, int argc, char **azColName, char **argv) {
    QueryResult *qr = (QueryResult *) data;
    // 动态增加数组大小
    char **new_results = realloc(qr->results, sizeof(char *) * (qr->count + argc));
    if (!new_results) {
        my_free(new_results);
        return GNCDB_MEM;
    }
    qr->results = new_results;
    // 将结果加入qr中
    for(int i = 0; i < argc; i++) {
        qr->results[qr->count] = argv[i] ? strdup(argv[i]) : strdup("NULL");
        if (!qr->results[qr->count]) {
            my_free(new_results);
            return GNCDB_MEM; 
        }
        qr->count++;
    }
    qr->columnum = argc;
    return 0;
}
//全值callback
int rawCallBacks(void*data, int argc, char **azColName, char **argv)
{
    callBack *cb = (callBack *)data;
    cb->argc = argc;
    cb->azColName = azColName;
    cb->argv = argv;
    return 0;
}

//单值回调函数
int getMaxcallback(void*data, int argc, char **azColName, char **argv)
{
    int *value = (int*)data;
    if(argc != 1)
    {
        printf("aggreate result is not one\n");
        return -1;
    }
    *value = atoi(argv[0]);
    return 0;
}

int getSingleDoublecallback(void*data, int argc, char **azColName, char **argv)
{
    double *value = (double *)data;
    if(argc != 1)
    {
        printf("aggreate result is not one\n");
        return -1;
    }
    *value = atof(argv[0]);
    return 0;
}

int geoExtentCallBacks(void*data, int argc, char **azColName, char **argv) {
    rt_geo_extent geo_extent = (rt_geo_extent)data;
    if(argc != 4)
    {
        printf("The result is invalid\n");
        return 0;
    }
    geo_extent->extent_minx = atof(argv[0]);
    geo_extent->extent_maxx = atof(argv[1]);
    geo_extent->extent_miny = atof(argv[2]);
    geo_extent->extent_maxy = atof(argv[3]);
    return 0;
}

int getResolutionCallBacks(void*data, int argc, char **azColName, char **argv) {
    rt_resolution resolution = (rt_resolution)data;
    if(argc != 4)
    {
        printf("The result is invalid\n");
        return 0;
    }
    resolution->xres = atof(argv[0]);
    resolution->yres = atof(argv[1]);
    resolution->tile_width = atoi(argv[2]);
    resolution->tile_height = atoi(argv[3]);
    return 0;
}

int getTypeCallBacks(void*data, int argc, char **azColName, char **argv) {
    rt_geo_type geo_type = (rt_geo_type)data;
    if(argc != 3)
    {
        printf("The result is invalid\n");
        return 0;
    }
    geo_type->sample_type= atoi(argv[0]);
    geo_type->pixeltype = atoi(argv[1]);
    geo_type->numbands = atoi(argv[2]);
    return 0;
}

int getListCallBacks(void*data, int argc, char **azColName, char **argv) {
    varArrayList *tileIdList = (varArrayList *)data;
    for(int i = 0;i < argc;i++)
    {
        int tile_id = atoi(argv[i]);
        varArrayListAdd(tileIdList,&tile_id);
    }
    return 0;
}

#if defined(_WIN32) && !defined(__MINGW32__)
DWORD WINAPI
doRunImportThread (void *arg)
#else
void *
doRunImportThread (void *arg)
#endif
{
/* threaded function: preparing a compressed Tile to be imported */
    rt_tile tile = (rt_tile) arg;
    encode_tile (tile);
#if defined(_WIN32) && !defined(__MINGW32__)
    return 0;
#else
    pthread_exit (NULL);
    return NULL;
#endif
}

#if defined(_WIN32) && !defined(__MINGW32__)
DWORD WINAPI
doRunDecoderThread (void *arg)
#else
void *
doRunDecoderThread (void *arg)
#endif
{
/* threaded function: decoding a Tile */
    rt_tile_decoder decoder = (rt_tile_decoder) arg;
    decode_tile (decoder);
#if defined(_WIN32) && !defined(__MINGW32__)
    return 0;
#else
    pthread_exit (NULL);
    return NULL;
#endif
}

void query_context_init(QueryContext *ctx, const char *sql, size_t buffer_size, const char *coveragename) {
    
    size_t capacity = 10;
     const char *p = sql;
    
    memset(ctx, 0, sizeof(QueryContext));

    /*保存原始SQL的拷贝*/
    ctx->original_sql = strdup(sql);
    ctx->buffer_size = buffer_size;
    ctx->modified_sql = malloc(buffer_size);
    strcpy(ctx->modified_sql, sql);
    ctx->coveragename = coveragename;

    /*解析原始问号位置*/
    ctx->param_positions = malloc(capacity * sizeof(size_t));
    while (*p) {
        if (*p == '?') {
            if (ctx->num_params >= capacity) {
                capacity *= 2;
                ctx->param_positions = realloc(ctx->param_positions, capacity * sizeof(size_t));
            }
            ctx->param_positions[ctx->num_params++] = p - sql;
        }
        p++;
    }
}

void query_context_destroy(QueryContext *ctx) {
    free(ctx->original_sql);
    free(ctx->param_positions);
    free(ctx->modified_sql);
    memset(ctx, 0, sizeof(QueryContext));
}

// 公共替换逻辑
static void _bind_param(QueryContext *ctx, int position, const char *value_str) {
    size_t original_pos = 0;
    size_t actual_pos = 0;
    size_t value_len = 0;
    size_t required_len = 0;
    if (position < 1 || position > ctx->num_params) {
        printf("Invalid position %d\n", position);
        return;
    }

    // 获取原始位置和当前偏移后的实际位置
    original_pos = ctx->param_positions[position - 1];
    actual_pos = original_pos + ctx->total_offset;

    // 检查缓冲区是否足够
    value_len = strlen(value_str);
    required_len = strlen(ctx->modified_sql) + value_len - 1; // 替换掉一个'?'
    if (required_len >= ctx->buffer_size) {
        printf("Buffer overflow!\n");
        return;
    }
    // 移动内存并替换
    memmove(ctx->modified_sql + actual_pos + value_len,
            ctx->modified_sql + actual_pos + 1,
            strlen(ctx->modified_sql) - actual_pos);

    memcpy(ctx->modified_sql + actual_pos, value_str, value_len);
    // 更新累计偏移量
    ctx->total_offset += (value_len - 1);
}

/*将sql中第position个？替换为value的值*/
void bind_char(QueryContext *ctx, int position, const char *value) {
    char buffer[1024] = {0};
    char *dest = buffer;
    *dest++ = '\'';
    for (const char *c = value; *c && (dest - buffer) < sizeof(buffer)-2; c++) {
        if (*c == '\'') *dest++ = '\'';
        *dest++ = *c;
    }
    *dest++ = '\'';
    *dest = '\0';
    _bind_param(ctx, position, buffer);
}
/*将sql中第position个？替换为value的值*/
void bind_int(QueryContext *ctx, int position, int value) {
    char buffer[32];
    snprintf(buffer, sizeof(buffer), "%d", value);
    _bind_param(ctx, position, buffer);
}
/*将sql中第position个？替换为value的值*/
void bind_float(QueryContext *ctx, int position, double value) {
    char buffer[64];
    snprintf(buffer, sizeof(buffer), "%.15f", value);
    _bind_param(ctx, position, buffer);
}

char* replace_nth_placeholder(const char* sql, const char* s, int nth) {
    const char* pattern = "\"%s\"";  // 需要匹配的转义序列
    const size_t pattern_len = strlen(pattern);
    const size_t replace_len = strlen(s);
    int total = 0;
    char* result = NULL;
    char* output = NULL;
    const char* input = NULL;
    int count = 0;
    const char* cursor = NULL;
    // 有效性校验
    if (nth < 1) return strdup(sql);
    
    // 第一次遍历：计算匹配项总数
    cursor = sql;
    while ((cursor = strstr(cursor, pattern))) {
        total++;
        cursor += pattern_len;
    }
    if (nth > total) return strdup(sql);

    // 第二次遍历：构建新字符串
    result = malloc(strlen(sql) + (replace_len - pattern_len) + 1);
    if (!result) return NULL;
    
    output = result;
    input = sql;
    count = 0;
    
    while (*input) {
        if (strncmp(input, pattern, pattern_len) == 0) {
            count++;
            if (count == nth) {  // 命中目标位置
                memcpy(output, s, replace_len);
                output += replace_len;
                input += pattern_len;
            } else {  // 非目标位置保留原样
                memcpy(output, input, pattern_len);
                output += pattern_len;
                input += pattern_len;
            }
        } else {
            *output++ = *input++;
        }
    }
    *output = '\0';
    
    return result;
}

int GNCDB_CreateCoverage(GNCDB *db,const char *coveragename,unsigned char sample,
unsigned char pixel,unsigned char num_bands,unsigned short tile_width,
unsigned short tile_height,int srid,double xres,double yres)
{
    int rc = 0;
    Transaction* tx = NULL;
    
    /*1.参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    if(!check_coverage_param(sample,pixel,num_bands,tile_width,tile_height,xres,yres))
    {
        return GNCDB_PARAM_INVALID;
    }
    
    /*2.开启一个事务*/
	tx = transcationConstrcut(db);
	if (tx == NULL)
	{
		return GNCDB_MEM;
	}

    /*3.执行创建操作*/
    rc = executorCreateCoverage(db, coveragename, sample,pixel, num_bands,
        tile_width, tile_height, srid, xres, yres);
	
    /*4.若成功则提交失败回滚*/
    if (rc){
		transactionRollback(tx, db);
		return rc;
	}else{
		transactionCommit(tx, db);
	}
	return GNCDB_SUCCESS;
}

int executorCreateCoverage(GNCDB *db,const char *coveragename,unsigned char sample,
unsigned char pixel,unsigned char num_bands,unsigned short tile_width,
unsigned short tile_height,int srid,double xres,double yres)
{
    double min = -2147483647.0;
    double max = 2147483647.0;
    int rc = 0;
    const char *gsample = NULL;
    const char *gpixel = NULL;
    char *coverage_levels = NULL;
    char *coverage_sections = NULL;
    char *coverage_tiles = NULL;
    char *sql = NULL;
    char *temp_sql = NULL;
    QueryContext sqlctx_rtcov;
    char SQL[512];

    /*1.创建raster_coverage表*/
    sql = "CREATE TABLE raster_coverages ("
    "coverage_name CHAR(20) PRIMARY KEY,"
    "sample_type CHAR(20),"
    "pixel_type CHAR(20),"
    "num_bands INT,"
    "tile_width INT,"
    "tile_height INT,"
    "srid INT,"
    "xres FLOAT,"
    "yres FLOAT,"
    "extent_minx FLOAT,"
    "extent_maxx FLOAT,"
    "extent_miny FLOAT,"
    "extent_maxy FLOAT);";
    rc = GNCDB_exec(db, sql, NULL, NULL, NULL);
    if(rc != GNCDB_SUCCESS && rc != GNCDB_TABLE_EXIST)
    {
        return rc;
    }

    /*2.将数据插入raster_coverage*/
    switch(sample)
    {
        case SAMPLE_8BIT:
            gsample = "8_BIT";
            break;
        case SAMPLE_16BIT:
            gsample = "16_BIT";
            break;
        default:
            gsample = "UNKNOWN_SAMPLE";
            break;
    }
    switch(pixel)
    {
        case PIXEL_DEM:
            gpixel = "DEM";
            break;
        case PIXEL_RGB:
            gpixel = "RGB";
            break;
        default:
            gpixel = "UNKNOWN_PIXEL";
            break;
    }
    sql = "INSERT INTO raster_coverages VALUES"
    "(?, ?, ?, ?, ?, ?, ?, ?, ?, NULL, NULL, NULL, NULL)";
    query_context_init(&sqlctx_rtcov,sql,512,coveragename);
    bind_char(&sqlctx_rtcov,1,coveragename);
    bind_char(&sqlctx_rtcov,2,gsample);
    bind_char(&sqlctx_rtcov,3,gpixel);
    bind_int(&sqlctx_rtcov,4,num_bands);
    bind_int(&sqlctx_rtcov,5,tile_width);
    bind_int(&sqlctx_rtcov,6,tile_height);
    bind_int(&sqlctx_rtcov,7,srid);
    bind_float(&sqlctx_rtcov,8,xres);
    bind_float(&sqlctx_rtcov,9,yres);
    rc = GNCDB_exec(db, sqlctx_rtcov.modified_sql, NULL, NULL, NULL);
    query_context_destroy(&sqlctx_rtcov);
    if(rc!=GNCDB_SUCCESS)
    {
        printf("Insert raster_coverage fail\n");
        return rc;
    }

    /*3.创建levels表*/
    coverage_levels = strcatetablename(coveragename,"_levels");
    temp_sql = "CREATE TABLE %s ("
    "pyramid_level INT PRIMARY KEY,"
    "x_resolution_1_1 FLOAT,"
    "y_resolution_1_1 FLOAT,"
    "x_resolution_1_2 FLOAT,"
    "y_resolution_1_2 FLOAT,"
    "x_resolution_1_4 FLOAT,"
    "y_resolution_1_4 FLOAT,"
    "x_resolution_1_8 FLOAT,"
    "y_resolution_1_8 FLOAT);";
    snprintf(SQL,sizeof(SQL),temp_sql,coverage_levels);
    my_free(coverage_levels);
    rc = GNCDB_exec(db, SQL, NULL, NULL, NULL);
    if(rc!=GNCDB_SUCCESS)
    {
        return rc;
    }

    /*4.创建sections表*/
    coverage_sections = strcatetablename(coveragename,"_sections");
    temp_sql = "CREATE TABLE %s ("
    "section_id INT PRIMARY KEY,"
    "section_name CHAR(20),"
    "tile_startid INT,"
    "width INT,"
    "height INT,"
    "x_min FLOAT,"
    "x_max FLOAT,"
    "y_min FLOAT,"
    "y_max FLOAT);";
    snprintf(SQL,sizeof(SQL),temp_sql,coverage_sections);
    my_free(coverage_sections);
    rc = GNCDB_exec(db, SQL, NULL, NULL, NULL);
    if(rc!=GNCDB_SUCCESS)
    {
        return rc;
    }

    /*5.创建tiles表*/
    coverage_tiles = strcatetablename(coveragename,"_tiles");
    rc = GNCDB_createTable(db,coverage_tiles,9,
            "tile_id",FIELDTYPE_INTEGER,0,1,min,max,
            "pyramid_level",FIELDTYPE_INTEGER,0,0,min,max,
            "section_id",FIELDTYPE_INTEGER,0,0,min,max,
            "tile_data",FIELDTYPE_BLOB,1,0,min,max,
            "x_min",FIELDTYPE_REAL,0,0,min,max,
            "x_max",FIELDTYPE_REAL,0,0,min,max,
            "y_min",FIELDTYPE_REAL,0,0,min,max,
            "y_max",FIELDTYPE_REAL,0,0,min,max,
            "blob_size",FIELDTYPE_INTEGER,0,0,min,max,
    M_MAX_ROWS);
    my_free(coverage_tiles);
    if(rc!=GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

int GNCDB_ImportRaster(GNCDB *db, const char *src_path,const char *coveragename, int srid,bool pyramid)
{
    int rc = 0;
    Transaction* tx = NULL;
    struct stat path_stat;
    DIR           *dir;
    struct dirent *entry;
    
    /*1.参数检查*/
    if(db == NULL || coveragename == NULL || src_path == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    if(srid <= 0)
    {
        return GNCDB_PARAM_INVALID;
    }
    
    /*2.开启一个事务*/
	tx = transcationConstrcut(db);
	if (tx == NULL)
	{
		return GNCDB_MEM;
	}
    
    /*3.判断src_path是文件夹还是文件*/
    if (stat(src_path, &path_stat) == 0) {
      if (S_ISDIR(path_stat.st_mode)) {
        if ((dir = opendir(src_path)) == NULL) {
          transactionRollback(tx, db);
          perror("opendir failed\n");
          //todo 缺少返回的状态码
          // return;
        }
        while ((entry = readdir(dir)) != NULL) {
          char fullPath[1024];
          snprintf(fullPath, sizeof(fullPath), "%s/%s", src_path, entry->d_name);
          if (strlen(entry->d_name) > 4 && strcmp(entry->d_name + strlen(entry->d_name) - 4, ".hgt") == 0) {
            rc = executorImportRaster(db, fullPath, coveragename, srid,pyramid);
            if (rc != GNCDB_SUCCESS) {
              transactionRollback(tx, db);
              return rc;
            }
          }
        }
        closedir(dir);
      } else if (S_ISREG(path_stat.st_mode)) {
        rc = executorImportRaster(db, src_path, coveragename, srid,pyramid);
        /*若成功则提交失败回滚*/
        if (rc != GNCDB_SUCCESS) {
          transactionRollback(tx, db);
          return rc;
        }
      } else {
        transactionRollback(tx, db);
        printf("'%s' not a dir or hgtfile\n", src_path);
        return GNCDB_PARAM_INVALID;
      }
    } else {
      transactionRollback(tx, db);
      perror("can not get state of the srcpath\n");
      return GNCDB_PARAM_INVALID;
    }

    transactionCommit(tx, db);
    return GNCDB_SUCCESS;
}

int executorImportRaster(GNCDB *db, const char *src_path,const char *coveragename, int srid,bool pyramid)
{
    int rc = GNCDB_SUCCESS;
    char *filename = NULL;
    char *sql = NULL;
    char *temp_sql = NULL;
    char *coverage_sections = NULL;
    char *coverage_levels = NULL;
    char *coverage_tiles = NULL;
    int section_id = 0;
    double minx = 0.0;
    double maxx = 0.0;
    double maxy = 0.0;
    double miny = 0.0;
    double xres = 0.0;
    double yres = 0.0;
    int tile_width = 0;
    int tile_height = 0;
    int width = 0;
    int height = 0;
    int count_section = 0;
    rt_coverage cvg = NULL;
    rt_priv_raster raster_origin = NULL;
    QueryContext sqlctx_sec;
    QueryContext sqlctx_lev;
    QueryContext sqlctx_tils;
    struct timespec start, end;
    double diff;
    char SQL[512];
    clock_gettime(CLOCK_MONOTONIC, &start);

    /*1.从数据库中获取coverage*/
    rc = get_coverage_fromdb(db,coveragename,&cvg);
    if(rc != GNCDB_SUCCESS)
    {
        printf("get coverage:%s falied\n",coveragename);
        return rc;
    }

    /*2.从本地路径中读取raster数据*/
    raster_origin = read_raster_from_disk(src_path,cvg->pixelType,&filename);
    if(raster_origin == NULL)
    {
        my_free(cvg);
        //todo 缺少返回的状态码
        return 1;
    }

    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> finish load file from disk: %f secs\n", diff);


    /*3.验证数据与覆盖层的正确性*/
    rc = is_confident(raster_origin,cvg,pyramid);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(raster_origin);
        return rc;
    }

    /*4.获取size数据*/
    rc = get_file_size(raster_origin,&minx,&maxx,&miny,&maxy,&xres,&yres,&width,&height);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(raster_origin);
        return rc;
    }
    rc = get_tile_size(cvg,&tile_width,&tile_height);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(raster_origin);
        return rc;
    }

    /*5.插入section表*/
    coverage_sections = strcatetablename(coveragename,"_sections");
    temp_sql = "INSERT INTO %s (section_id, section_name,"
	 "tile_startid,width, height, x_min,x_max,y_min,y_max)"
	 "VALUES (?, ?, NULL, ?, ?, ?, ?, ?, ?)";
    snprintf(SQL,sizeof(SQL),temp_sql,coverage_sections);

    query_context_init(&sqlctx_sec,SQL,512,coveragename);
    rc = insert_sections(db,filename,raster_origin->srid,raster_origin->width,
    raster_origin->height,minx,maxx,miny,maxy,&section_id,&sqlctx_sec);
    query_context_destroy(&sqlctx_sec);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(raster_origin);
        my_free(coverage_sections);
        return rc;
    }

    /*6.若是地图数据则插入levels表*/
    if(cvg->pixelType == PIXEL_RGB)
    {
        coverage_levels = strcatetablename(coveragename,"_levels");
        temp_sql = "select count(*) from %s";
        snprintf(SQL,sizeof(SQL),temp_sql,coverage_levels);
        rc = GNCDB_exec(db,SQL,getMaxcallback,&count_section,NULL);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(cvg);
            my_free(raster_origin);
            my_free(coverage_sections);
            my_free(coverage_levels);
            printf("get num of levels line failed %d\n",rc);
            return rc;
        }
        /*若表中不存在levels表*/
        if(count_section == 0)
        {
            temp_sql = "INSERT INTO %s (pyramid_level, "
	           "x_resolution_1_1, y_resolution_1_1, "
	           "x_resolution_1_2, y_resolution_1_2, x_resolution_1_4, "
	           "y_resolution_1_4, x_resolution_1_8, y_resolution_1_8) "
	           "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
            snprintf(SQL,sizeof(SQL),temp_sql,coverage_levels);

            query_context_init(&sqlctx_lev,SQL,512,coveragename);
            rc = insert_levels(db,xres,yres,0,&sqlctx_lev);
            query_context_destroy(&sqlctx_lev);

            if(rc != GNCDB_SUCCESS)
            {
                my_free(cvg);
                my_free(raster_origin);
                my_free(coverage_sections);
                my_free(coverage_levels);
                printf("levels table insert failed\n");
                return rc;
            }
        }
        my_free(coverage_levels);
    }

    /*7.插入tiles表*/
    coverage_tiles = strcatetablename(coveragename,"_tiles");
    temp_sql = "INSERT INTO %s (tile_id,pyramid_level,"
    "section_id,x_min,x_max,y_min,y_max,blob_size) VALUES "
    "(?,0,?,?,?,?,?,?)";
    snprintf(SQL,sizeof(SQL),temp_sql,coverage_tiles);
    
    /*设置该文件的瓦片开始id*/
    query_context_init(&sqlctx_tils,SQL,512,coveragename);
    /*获取tiles表中的最大的tileid*/
    insert_primaryid(db,coveragename,"_tiles","tile_id",&sqlctx_tils);
    update_lastinsertid(&sqlctx_tils);
    set_section_startid(db,coverage_sections,"tile_startid",&sqlctx_tils.last_insert_id,FIELDTYPE_INTEGER,
    "section_id",&section_id,FIELDTYPE_INTEGER);
    
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> begin insert tile: %f secs\n", diff);

    /*插入tiles表*/
    rc = insert_tiles(db,section_id,cvg,raster_origin,tile_width,tile_height,xres,yres,
    minx,maxx,miny,maxy,width,height,&sqlctx_tils);
    query_context_destroy(&sqlctx_tils);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(raster_origin);
        my_free(coverage_tiles);
        my_free(coverage_sections);
        printf("Insert tiles table failed\n");
        return rc;
    }
    my_free(coverage_tiles);
    my_free(coverage_sections);

    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> finish insert tile: %f secs\n", diff);
    
    /*8.在raster_coverages表中插入顶点值*/
    updateRasterExtent(db,coveragename);

    /*9.根据pyramid的值决定是否要建立预览*/
    if(pyramid)
    {
        rc = createPyrmaidPreview(db,cvg,raster_origin,coveragename,section_id);
    }

    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> finish create pyramid time: %f secs\n", diff);
    
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(raster_origin);
        return rc;
    }

    /*10.释放资源*/
    my_free(cvg);
    privRasterDestroy(raster_origin);
    return GNCDB_SUCCESS;
}

int createPyrmaidPreview(GNCDB *db,rt_coverage coverage,rt_priv_raster origin,const char *coveragename,int section_id)
{
    int rc = 0;
    rt_section_pyramid  sec_pyr = NULL;
    rt_coverage cvg = coverage;
    QueryContext sqlctx_sec;
    QueryContext sqlctx_level;
    QueryContext sqlctx;
    unsigned char sampleType = cvg->sampleType;
	unsigned char pixelType = cvg->pixelType;
	unsigned char nBands = cvg->nBands;
	unsigned int tileWidth = cvg->tileWidth;
	unsigned int tileHeight = cvg->tileHeight;
	int srid = cvg->srid;
    double minx;
    double miny;
    double maxx;
    double maxy;
    double out_maxy;
    double out_miny;
    double out_maxx;
    double out_minx;
    int row = 0;
    int col = 0;

    int sec_width = 0;
    int sec_height = 0;
    int tile_startid = 0;

    int pyramid_level = 0;

    double build_xres = 0.0;
    double build_yres = 0.0;
    int scale = 0;

    struct timespec start, end;
    double diff;

    char *sql = NULL;
    char *temp_sql = NULL;
    char *coverage_sec = NULL;
    char *coverage_level = NULL;
    char *coverage_tile = NULL;
    QueryResult *qrs = queryResultCreate();
    coverage_sec = strcatetablename(coveragename,"_sections");
    coverage_level = strcatetablename(coveragename,"_levels");
    coverage_tile = strcatetablename(coveragename,"_tiles");
    /*1.获取section的边界坐标*/
    temp_sql = "SELECT width,height,tile_startid,x_min,x_max,y_min,y_max FROM \"%s\""
    " WHERE section_id = ?";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    query_context_init(&sqlctx_sec,sql,512,coveragename);
    my_free(sql);
    bind_int(&sqlctx_sec,1,section_id);
    rc = GNCDB_exec(db,sqlctx_sec.modified_sql,myCallBacks,qrs,NULL);
    query_context_destroy(&sqlctx_sec);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tile);
        return rc;
    }
    sec_width = atoi(qrs->results[0]);
    sec_height = atoi(qrs->results[1]);
    tile_startid = atoi(qrs->results[2]);
    minx = atof(qrs->results[3]);
    miny = atof(qrs->results[4]);
    maxx = atof(qrs->results[5]);
    maxy = atof(qrs->results[6]);
    queryResultDestroy(qrs);
    while(1)
    {
        QueryResult *qr = queryResultCreate();

        clock_gettime(CLOCK_MONOTONIC, &start);

        sql = "SELECT \"%s\".tile_id,\"%s\".x_min,\"%s\".x_max,\"%s\".y_min,\"%s\".y_max,"
             "\"%s\".x_resolution_1_1, \"%s\".y_resolution_1_1 "
		     "FROM \"%s\" "
		     "INNER JOIN \"%s\" ON (\"%s\".pyramid_level = \"%s\".pyramid_level) "
		     "WHERE \"%s\".pyramid_level = ? AND \"%s\".section_id = ?";
        temp_sql = replace_nth_placeholder(sql,coverage_tile,1);
        sql = replace_nth_placeholder(temp_sql,coverage_tile,1);
        my_free(temp_sql);
        temp_sql = replace_nth_placeholder(sql,coverage_tile,1);
        my_free(sql);
        sql = replace_nth_placeholder(temp_sql,coverage_tile,1);
        my_free(temp_sql);
        temp_sql = replace_nth_placeholder(sql,coverage_tile,1);
        my_free(sql);
        sql = replace_nth_placeholder(temp_sql,coverage_level,1);
        my_free(temp_sql);
        temp_sql = replace_nth_placeholder(sql,coverage_level,1);
        my_free(sql);
        sql = replace_nth_placeholder(temp_sql,coverage_tile,1);
        my_free(temp_sql);
        temp_sql = replace_nth_placeholder(sql,coverage_level,1);
        my_free(sql);
        sql = replace_nth_placeholder(temp_sql,coverage_level,1);
        my_free(temp_sql);
        temp_sql = replace_nth_placeholder(sql,coverage_tile,1);
        my_free(sql);
        sql = replace_nth_placeholder(temp_sql,coverage_level,1);
        my_free(temp_sql);
        temp_sql = replace_nth_placeholder(sql,coverage_tile,1);
        my_free(sql);

        query_context_init(&sqlctx,temp_sql,512,coveragename);
        my_free(temp_sql);
        bind_int(&sqlctx,1,pyramid_level);
        bind_int(&sqlctx,2,section_id);
        rc = GNCDB_exec(db,sqlctx.modified_sql,myCallBacks,qr,NULL);
        query_context_destroy(&sqlctx);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(coverage_sec);
            my_free(coverage_level);
            my_free(coverage_tile);
            return rc;
        }
        sec_pyr = my_malloc(sizeof(struct rt_section_pyramid_t));
        sec_pyr->first_in = NULL;
        sec_pyr->last_in = NULL;
        sec_pyr->first_out = NULL;
        sec_pyr->last_out = NULL;
        sec_pyr->tile_startid = tile_startid;
        if(sec_pyr == NULL)
        {
            my_free(coverage_sec);
            my_free(coverage_level);
            my_free(coverage_tile);
            return GNCDB_MEM;
        }
        for(int i = 0;i < qr->count/qr->columnum;i++)
        {
            int tile_id  = atoi(qr->results[i * 7]);
		    double tminx = atof(qr->results[i * 7 + 1]);
		    double tmaxx = atof(qr->results[i * 7 + 2]);
		    double tminy = atof(qr->results[i * 7 + 3]);
		    double tmaxy = atof(qr->results[i * 7 + 4]);
		    double res_x = atof(qr->results[i * 7 + 5]);
		    double res_y = atof(qr->results[i * 7 + 6]);
            build_xres = res_x * 8;
            build_yres = res_y * 8;
            scale = 8;
            section_pyramid_init(sec_pyr,section_id,sec_width,sec_height,sampleType,
            pixelType,nBands,srid,build_xres,build_yres,(double) tileWidth *build_xres,
            (double) tileHeight *build_yres,minx, miny,maxx, maxy, scale);
            rc = put_tile_into_pyr(sec_pyr, tile_id, tminx, tminy, tmaxx, tmaxy);
        }
        queryResultDestroy(qr);
        out_maxy = maxy;
	    for (row = 0; row < sec_pyr->scaled_height; row += tileHeight)
	    {
		    out_miny = out_maxy - sec_pyr->tile_height;
		    if (out_miny < miny)
		        out_miny = miny;
		    out_minx = minx;
		    for (col = 0; col < sec_pyr->scaled_width; col += tileWidth)
		    {
		      out_maxx = out_minx + sec_pyr->tile_width;
		      if (out_maxx > maxx)
			  out_maxx = maxx;
		      create_tile_out (sec_pyr, out_minx, out_miny,
						    out_maxx, out_maxy, row,
						    col);
		      out_minx += sec_pyr->tile_width;
		    }
		    out_maxy -= sec_pyr->tile_height;
	    }
	    pyramid_level++;
        //开始插入levels表
        temp_sql = "INSERT INTO \"%s\" (pyramid_level, "
	       "x_resolution_1_1, y_resolution_1_1, "
	       "x_resolution_1_2, y_resolution_1_2, x_resolution_1_4, "
	       "y_resolution_1_4, x_resolution_1_8, y_resolution_1_8) "
	       "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        sql = replace_nth_placeholder(temp_sql,coverage_level,1);
        query_context_init(&sqlctx_level,sql,512,coveragename);
        my_free(sql);
        rc = insert_levels(db,sec_pyr->res_x,sec_pyr->res_y,pyramid_level,&sqlctx_level);
        if(rc != GNCDB_SUCCESS && rc != GNCDB_INSERT_FAILED)
        {
            return rc;
        }
        query_context_destroy(&sqlctx_level);
        //将tile_out聚合成高层次的数据并插入数据库
        rc = insert_higher_level_to_db(db,cvg,origin,coveragename,sec_pyr, pyramid_level,tileWidth, tileHeight);
        if(rc != GNCDB_SUCCESS)
        {
            return rc;
        }
        section_pyramid_destory(sec_pyr);

        clock_gettime(CLOCK_MONOTONIC, &end);
        // 计算时间差
        diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
        printf(">> Finish pyrmid: %f %dsecs\n", diff,pyramid_level);

        if (sec_pyr->scaled_width <= tileWidth
	      && sec_pyr->scaled_height <= tileHeight)
	      break;


    }
    return GNCDB_SUCCESS;
}

int GNCDB_RasterPointQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double longitude,double latitude)
{
    int rc = GNCDB_SUCCESS;
    int querypointcount = 0;
    int resultpointcount = 0;
    int width = 0;
    int height = 0;
    double minx = 0.0;
    double maxy = 0.0;
    int num_bands = 0;
    int sample_type = 0;
    char *coverage_sec = NULL;
    char *p_result = NULL;
    int offset = 0;
    int err_code = 0;
    double xres,yres;
    rt_resolution resolution = NULL;
    rt_geo_extent tile_bound = NULL;
    rt_coverage cvg = NULL;
    rt_raster raster = NULL;
    Transaction* tx = NULL;
    varArrayList* tileIdList = varArrayListCreate(ORDER, sizeof(int), 0, intCompare, NULL);
    p_result = result;

    /*1.参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    /*2.开启事务*/
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }

    /*3.取出覆盖层*/
    rc = get_coverage_fromdb(db,coveragename,&cvg);
    if(cvg == NULL)
    {
        return rc;
    }
    xres = cvg->xres;
    yres = cvg->yres;
    num_bands = cvg->nBands;
    sample_type = cvg->sampleType;
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        my_free(cvg);
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    resolution->xres = xres;
    resolution->yres = yres;
    resolution->tile_height = cvg->tileHeight;
    resolution->tile_width = cvg->tileWidth;

    /*4.获取查询点所在的栅格的中心坐标*/
    querypointcount = 1;
    resultpointcount = result_size/(sizeof(double)*2 + sizeof(int16_t));
    if(querypointcount > resultpointcount)
    {
        my_free(resolution);
        my_free(cvg);
        transactionRollback(tx,db);
        printf("memory not enough\n");
        return GNCDB_MEM;
    }

    /*5,将经纬度写进result*/
    addNewPointAbsolute(longitude,latitude,p_result);
    
    /*6.定义查询点的查询区域*/
    tile_bound = my_malloc(sizeof(struct rt_geo_extent_t));
    tile_bound->extent_maxx = longitude;
    tile_bound->extent_minx = longitude;
    tile_bound->extent_miny = latitude;
    tile_bound->extent_maxy = latitude;
    
    /*7.获取瓦片目标区域瓦片id集合*/
    coverage_sec = strcatetablename(coveragename,"_sections");
    getTileIdSet(db,tileIdList,tile_bound,resolution,coverage_sec);
    my_free(coverage_sec);
    width = 1;
    height = 1;
   
    /*8.初始化目标区域数据*/
    minx = tile_bound->extent_minx;
    maxy = tile_bound->extent_maxy;
    
    /*9.拼接瓦片集合获得目标区域缓冲区*/
    raster = cateTile(db,tileIdList,coveragename,width,height,minx,maxy,num_bands,sample_type,resolution,1,&err_code);
    if(raster == NULL)
    {
        my_free(resolution);
        my_free(tile_bound);
        my_free(cvg);
        transactionRollback(tx,db);
        return err_code;
    }

    /*10.查询数据*/
    for(int point = 0;point < querypointcount;point++)
    {
        double lon = 0.0,lat = 0.0;
        offset = point * (sizeof(double) * 2 + sizeof(uint16_t));
        p_result = result + offset;
        queryPoint(&lon,&lat,p_result,offset,raster,xres,yres);
    }

    /*11.释放资源*/
    my_free(resolution);
    my_free(tile_bound);
    my_free(cvg);
    my_free(raster);
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}

int GNCDB_RasterLineQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double start_longitude,double start_latitude,double end_longitude,double end_latitude,int step)
{
    int rc = GNCDB_SUCCESS;
    int querypointcount = 0;
    int resultpointcount = 0;
    int width = 0;
    int height = 0;
    double minx = 0.0;
    double maxy = 0.0;
    double xres = 0.0;
    double yres = 0.0;
    int num_bands = 0;
    int sample_type = 0;
    char *coverage_sec = NULL;
    char *p_result = NULL;
    int offset = 0;
    int err_code = 0;
    struct timespec start, end;
    double diff;
    rt_geo_extent tile_bound = NULL;
    rt_resolution resolution = NULL;
    rt_raster raster = NULL;
    Transaction* tx = NULL;
    varArrayList* tileIdList = varArrayListCreate(ORDER, sizeof(int), 0, intCompare, NULL);
    rt_coverage cvg = NULL;

    clock_gettime(CLOCK_MONOTONIC, &start);

    /*1.参数检查*/
    if(db == NULL || coveragename == NULL || result == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    resultpointcount = result_size/(sizeof(double)*2 + sizeof(int16_t));
    if(resultpointcount * (sizeof(double)*2 + sizeof(int16_t)) != result_size)
    {
        printf("Mem invalid\n");
        return GNCDB_PARAMNULL;
    }

    /*2.开启事务*/
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }
    p_result = result;

    /*3.取出覆盖层*/
    rc = get_coverage_fromdb(db,coveragename,&cvg);
    if(cvg == NULL)
    {
        return rc;
    }
    xres = cvg->xres;
    yres = cvg->yres;
    num_bands = cvg->nBands;
    sample_type = cvg->sampleType;
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        my_free(cvg);
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    resolution->xres = xres;
    resolution->yres = yres;
    resolution->tile_height = cvg->tileHeight;
    resolution->tile_width = cvg->tileWidth;

    
    /*4.计算查询点和查询区域*/
    tile_bound = my_malloc(sizeof(struct rt_geo_extent_t));
    if(tile_bound == NULL)
    {
        my_free(cvg);
        my_free(resolution);
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    querypointcount = Line_query_get_points(p_result,tile_bound,start_longitude,start_latitude,end_longitude,end_latitude,resolution,&width,&height,step);
    if(querypointcount > resultpointcount)
    {
        my_free(tile_bound);
        my_free(cvg);
        my_free(resolution);
        transactionRollback(tx,db);
        return GNCDB_MEM;
    }

    //记录结束时间
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> get tiled time: %f secs\n", diff);

    /*5.获取瓦片*/
    coverage_sec = strcatetablename(coveragename,"_sections");
    getTileIdSet(db,tileIdList,tile_bound,resolution,coverage_sec);
    my_free(coverage_sec);

    /*6.初始化目标区域数据*/
    minx = tile_bound->extent_minx;
    maxy = tile_bound->extent_maxy;


    //记录结束时间
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> get tiledd time: %f secs\n", diff);


    /*7.拼接瓦片集合*/
    raster = cateTile(db,tileIdList,coveragename,width,height,minx,maxy,num_bands,sample_type,resolution,1,&err_code);
    if(raster == NULL)
    {
        my_free(resolution);
        my_free(tile_bound);
        my_free(cvg);
        transactionRollback(tx,db);
        return err_code;
    }
    
    //记录结束时间
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> get data time: %f secs\n", diff);
    
    /*8.查询数据*/
    for(int point = 0;point < querypointcount;point++)
    {
        double lon = 0.0,lat = 0.0;
        offset = point * (sizeof(double) * 2 + sizeof(uint16_t));
        p_result = result + offset;
        queryPoint(&lon,&lat,p_result,offset,raster,xres,yres);
    }

    /*9.查询数据*/
    my_free(resolution);
    my_free(tile_bound);
    my_free(raster);
    my_free(cvg);
    transactionCommit(tx,db);
    //记录结束时间
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> get finish time: %f secs\n", diff);
    return GNCDB_SUCCESS;
}

int getLineQueryMemorySize(GNCDB *db,const char *coveragename,int *result_size ,double start_longitude,double start_latitude,double end_longitude,double end_latitude,int step)
{
    int rc = 0;
    int row = 0;
    int col = 0;
    int startCol = 0;
    int startRow = 0;
    int endCol = 0;
    int endRow = 0;
    double left_lon = 0.0;
    double left_lat = 0.0;
    double right_lon = 0.0;
    double right_lat = 0.0;
    int direction = 0;
    double k = 0.0,b = 0.0;
    double startrow_offset = 0.0;
    double startcol_offset = 0.0;
    double endrow_offset = 0.0;
    double endcol_offset = 0.0;
    double xres = 0.0;
    double yres = 0.0;
    int count = 0;
    double baseLon = 0.0;
    double baseLat = 0.0;
    rt_resolution resolution = NULL;
    Transaction *tx = NULL;
    /*1.参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    /*2.若是步长采样直接返回*/
    if(step)
    {
        *result_size =  step * QUERY_POINT_SIZE;
        return GNCDB_SUCCESS;
    }

    /*3.构造事务*/
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }

    /*4.获取分辨率*/
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    rc = getCoverageResolution(db,resolution,coveragename);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(resolution);
        transactionRollback(tx,db);
        return rc;
    }
    xres = resolution->xres;
    yres = resolution->yres;

    /*5.统一起点为从左到右，direction为从左到右的变化趋势*/
    direction = reshape(start_longitude,start_latitude,end_longitude,end_latitude,
    &left_lon,&left_lat,&right_lon,&right_lat);
    
    // //计算斜率
    // if ( right_lat - left_lat == 0) {
    //     // 这里可以根据需求做特殊处理，比如设置一个很大的值表示无穷大
    //     k = 1e9;
    //     b = 0;
    //     printf("注意：直线垂直于 x 轴，斜率无穷大。\n");
    // } else {
    //     k = (right_lat - left_lat) / (right_lon - left_lon);
    //     b = right_lat - k * right_lon;
    // }
    
    //将起点和终点转换为对应的栅格中心
    start_longitude = left_lon;
    start_latitude = left_lat;
    end_longitude = right_lon;
    end_latitude = right_lat;
    convertLatLonToGridCenter(&left_lon,&left_lat,resolution);
    convertLatLonToGridCenter(&right_lon,&right_lat,resolution);
    
    /*6.确定基准坐标，以左上角为基准*/
    baseLon = left_lon;
    if(direction == -1)
    {
        baseLat = left_lat;
    }
    else
    {
        baseLat = right_lat;
    }

    /*7.坐标系变化，方便后续判断栅格和直线的相交关系*/
    startrow_offset = getBaseRowOffset(baseLat,start_latitude,yres);
    startcol_offset = getBaseColOffset(baseLon,start_longitude,xres);
    endrow_offset = getBaseRowOffset(baseLat,end_latitude,yres);
    endcol_offset = getBaseColOffset(baseLon,end_longitude,xres);
    if (startcol_offset - endcol_offset == 0) {
        // 这里可以根据需求做特殊处理，比如设置一个很大的值表示无穷大
        k = 1e9;
        b = 0;
        printf("注意：直线垂直于 x 轴，斜率无穷大。\n");
    } else {
        k = (endrow_offset - startrow_offset) / (endcol_offset- startcol_offset);
        b = startrow_offset - k * startcol_offset;
    }

    /*8.开始步进，直到到达终点*/
    startRow = getBaseRow(baseLat,left_lat,yres);
    startCol = getBaseCol(baseLon,left_lon,xres);
    endRow = getBaseRow(baseLat,right_lat,yres);
    endCol = getBaseCol(baseLon,right_lon,xres);
    
    /*首先要加入起点*/
    count++;
    row = startRow;
    col = startCol;
    while(!(row == endRow && col == endCol))
    {
        int temp_row = row;
        int temp_col = col;

        /*往右步进*/
        temp_col++;
        if(isIntersect(temp_col,row,k,b,xres,yres,baseLon,baseLat))
        {
            col = temp_col;
            count++;
            continue;
        }

        /*往右步进失败，上、下步进*/
        if(direction<0)
        {
            /*往下步进*/
            temp_row++;
        }
        else
        {
            /*往上步进*/
            temp_row--;
        }
        if(isIntersect(col,temp_row,k,b,xres,yres,baseLon,baseLat))
        {
            row = temp_row;
            count++;
            continue;
        }

        /*往上下步进失败，斜方向步进*/
        col++;
        if(direction<0)
        {
            /*往下步进*/
            row++;
        }
        else
        {
            /*往下步进*/
            row--;
        }
        count++;
    }

    /*加入终点*/
    count++;
    *result_size = count * QUERY_POINT_SIZE;
    my_free(resolution);
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}

int getPointQueryMemorySize()
{
    return QUERY_POINT_SIZE;
}

int GNCDB_RasterRectangleQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double start_longitude,double start_latitude,double end_longitude,double end_latitude,bool isContain)
{
    int rc = GNCDB_SUCCESS;
    int querypointcount = 0;
    int resultpointcount = 0;
    int width = 0;
    int height = 0;
    double minx = 0.0;
    double maxy = 0.0;
    int num_bands = 0;
    int sample_type = 0;
    char *coverage_sec = NULL;
    char *p_result = NULL;
    int offset = 0;
    int row = 0;
    int col = 0;
    int err_code = 0;
    double xres,yres;
    rt_resolution resolution = NULL;
    rt_geo_extent tile_bound = NULL;
    rt_geo_type tile_type = NULL;
    rt_raster raster = NULL;
    rt_coverage cvg = NULL;
    Transaction* tx = NULL;
    varArrayList* tileIdList = varArrayListCreate(ORDER, sizeof(int), 0, intCompare, NULL);
    p_result = result;

    /*1.参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        my_free(resolution);
        my_free(tile_bound);
        my_free(tile_type);
        return GNCDB_PARAMNULL;
    }
    resultpointcount = result_size/(sizeof(double)*2 + sizeof(int16_t));
    if(resultpointcount * (sizeof(double)*2 + sizeof(int16_t)) != result_size)
    {
        printf("Mem invalid\n");
        return GNCDB_PARAMNULL;
    }

    /*2.开启事务*/
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }

    /*3.取出覆盖层*/
    rc = get_coverage_fromdb(db,coveragename,&cvg);
    if(cvg == NULL)
    {
        return rc;
    }
    xres = cvg->xres;
    yres = cvg->yres;
    num_bands = cvg->nBands;
    sample_type = cvg->sampleType;
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        my_free(cvg);
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    resolution->xres = xres;
    resolution->yres = yres;
    resolution->tile_height = cvg->tileHeight;
    resolution->tile_width = cvg->tileWidth;

    /*4.计算查询区域和查询点*/
    tile_bound = my_malloc(sizeof(struct rt_geo_extent_t));
    if(tile_bound == NULL)
    {
        my_free(cvg);
        my_free(resolution);
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    querypointcount = Rectangle_query_get_points(p_result,tile_bound,start_longitude,start_latitude,end_longitude,end_latitude,resolution,&width,&height,isContain);
    if(querypointcount > resultpointcount)
    {
        my_free(resolution);
        my_free(cvg);
        transactionRollback(tx,db);
        printf("memory not enough\n");
        return GNCDB_MEM;
    }

    /*5.获取瓦片*/
    coverage_sec = strcatetablename(coveragename,"_sections");
    getTileIdSet(db,tileIdList,tile_bound,resolution,coverage_sec);
    my_free(coverage_sec);

    /*6.初始化目标区域数据*/
    minx = tile_bound->extent_minx;
    maxy = tile_bound->extent_maxy;

    /*7.拼接瓦片集合*/
    raster = cateTile(db,tileIdList,coveragename,width,height,minx,maxy,num_bands,sample_type,resolution,1,&err_code);
    if(raster == NULL)
    {
        my_free(resolution);
        my_free(cvg);
        transactionRollback(tx,db);
        return err_code;
    }

    /*8.查询数据*/
    for(int point = 0;point < querypointcount;point++)
    {
        short queryresult = 0.0;
        double lon = 0.0,lat = 0.0;
        offset = point * (sizeof(double) * 2 + sizeof(uint16_t));
        p_result = result + offset;
        readDouble(&lon,(BYTE *)p_result,&offset);
        p_result += sizeof(double);
        readDouble(&lat,(BYTE *)p_result,&offset);
        p_result += sizeof(double);
        row = round((raster->maxY - lat)/resolution->yres);
        col = round((lon - raster->minX)/resolution->xres);
        offset = (row * raster->width + col) * sizeof(int16_t);
        readShort(&queryresult,raster->rasterBuffer + offset,&offset);
        writeShort(queryresult,(BYTE *)p_result,&offset);
    }

    /*9.释放资源*/
    my_free(resolution);
    my_free(tile_bound);
    my_free(raster);
    my_free(cvg);
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}

int getRectangleQueryMemorySize(GNCDB *db,const char *coveragename,int *result_size ,double start_longitude,double start_latitude,double end_longitude,double end_latitude,bool isContain)
{
    int rc = 0;
    int endRow = 0;
    int endCol = 0;
    int startCol = 0;
    int startRow = 0;
    double left_lon = 0.0;
    double left_lat = 0.0;
    double right_lon = 0.0;
    double right_lat = 0.0;
    double xres = 0.0;
    double yres = 0.0;
    int count = 0;
    double baseLon,baseLat;
    rt_resolution resolution = NULL;
    Transaction *tx = NULL;

    /*参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }

    /*获取分辨率*/
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    rc = getCoverageResolution(db,resolution,coveragename);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(resolution);
        transactionRollback(tx,db);
        return rc;
    }
    xres = resolution->xres;
    yres = resolution->yres;

    //统一起点为从左到右，direction为从左到右的变化趋势
    left_lon = start_longitude;
    left_lat = start_latitude;
    right_lon = end_longitude;
    right_lat = end_latitude;

    //确定基准坐标
    baseLon = left_lon;
    baseLat = right_lat;
    
    //将起点和终点转换为对应的栅格中心
    convertLatLonToGridCenter(&left_lon,&left_lat,resolution);
    convertLatLonToGridCenter(&right_lon,&right_lat,resolution);
    // count++;
    startRow = getBaseRow(baseLat,left_lat,yres);
    startCol = getBaseCol(baseLon,left_lon,xres);
    endRow = getBaseRow(baseLat,right_lat,yres);
    endCol = getBaseCol(baseLon,right_lon,xres);

    // for(int i = col;i <= getCol(right_lon,xres);i++)
    // {
    //     for(int j = 0;j < (abs(getRow(right_lat,xres)-getRow(left_lat,yres))+1);j++)
    //     {
    //         if(direction)
    //         {
    //             row++;
    //         }
    //         else
    //         {
    //             row--;
    //         }
    //         count++;
    //     }
    //     row = getRow(left_lat,yres);
    // }

    if(isContain)
    {
        count = ((startRow - endRow) + 1) * (endCol - startCol + 1);
    }
    else
    {
        count = ((startRow-1 - endRow)) * (endCol-1 - startCol);
        if(count < 0)
        {
            count = 0;
        }
    }

    *result_size = count * QUERY_POINT_SIZE;
    my_free(resolution);
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}

int GNCDB_RasterCircleleQuery(GNCDB *db,const char *coveragename,char *result,int result_size,double center_longitude,double center_latitude,double radious,bool isContain)
{
    int rc = GNCDB_SUCCESS;
    int querypointcount = 0;
    int resultpointcount = 0;
    int width = 0;
    int height = 0;
    double minx = 0.0;
    double maxy = 0.0;
    int num_bands = 0;
    int sample_type = 0;
    char *coverage_sec = NULL;
    char *p_result = NULL;
    double xres,yres;
    int offset = 0;
    int row = 0;
    int col = 0;
    int err_code = 0;
    rt_resolution resolution = NULL;
    rt_geo_extent tile_bound = NULL;
    rt_geo_type tile_type = NULL;
    rt_raster raster = NULL;
    rt_coverage cvg = NULL;
    Transaction* tx = NULL;
    varArrayList* tileIdList = varArrayListCreate(ORDER, sizeof(int), 0, intCompare, NULL);
    p_result = result;

    /*1.参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    resultpointcount = result_size/(sizeof(double)*2 + sizeof(int16_t));
    if(resultpointcount * (sizeof(double)*2 + sizeof(int16_t)) != result_size)
    {
        printf("Mem invalid\n");
        return GNCDB_MEM;
    }

    /*2.构造事务*/
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }

    /*3.获取覆盖层*/
    rc = get_coverage_fromdb(db,coveragename,&cvg);
    if(cvg == NULL)
    {
        return rc;
    }
    xres = cvg->xres;
    yres = cvg->yres;
    num_bands = cvg->nBands;
    sample_type = cvg->sampleType;
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        my_free(cvg);
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    resolution->xres = xres;
    resolution->yres = yres;
    resolution->tile_height = cvg->tileHeight;
    resolution->tile_width = cvg->tileWidth;

    /*4.计算查询区域*/
    tile_bound = my_malloc(sizeof(struct rt_geo_extent_t));
    if(tile_bound == NULL)
    {
        my_free(resolution);
        my_free(cvg);
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    querypointcount = Circle_query_get_points(p_result,tile_bound,center_longitude,center_latitude,radious,resolution,&width,&height,isContain);
    if(querypointcount > resultpointcount)
    {
        my_free(resolution);
        my_free(tile_bound);
        my_free(tile_type);
        transactionRollback(tx,db);
        printf("memory not enough\n");
        return GNCDB_MEM;
    }

    /*5.获取瓦片*/
    coverage_sec = strcatetablename(coveragename,"_sections");
    getTileIdSet(db,tileIdList,tile_bound,resolution,coverage_sec);
    my_free(coverage_sec);
    
    /*6.初始化目标区域数据*/
    minx = tile_bound->extent_minx;
    maxy = tile_bound->extent_maxy;
    
    /*7.拼接瓦片集合*/
    raster = cateTile(db,tileIdList,coveragename,width,height,minx,maxy,num_bands,sample_type,resolution,1,&err_code);
    if(raster == NULL)
    {
        my_free(resolution);
        my_free(tile_bound);
        my_free(tile_type);
        transactionRollback(tx,db);
        return err_code;
    }

    /*8.查询数据*/
    for(int point = 0;point < querypointcount;point++)
    {
        short queryresult = 0.0;
        double lon = 0.0,lat = 0.0;
        offset = point * (sizeof(double) * 2 + sizeof(uint16_t));
        p_result = result + offset;
        queryPoint(&lon,&lat,p_result,offset,raster,xres,yres);
        readDouble(&lon,(BYTE *)p_result,&offset);
        p_result += sizeof(double);
        readDouble(&lat,(BYTE *)p_result,&offset);
        p_result += sizeof(double);
        row = round((raster->maxY - lat)/resolution->yres);
        col = round((lon - raster->minX)/resolution->xres);
        offset = (row * raster->width + col) * sizeof(int16_t);
        readShort(&queryresult,raster->rasterBuffer + offset,&offset);
        writeShort(queryresult,(BYTE *)p_result,&offset);
    }

    /*9.释放资源*/
    my_free(resolution);
    my_free(tile_bound);
    my_free(cvg);
    my_free(raster);
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}

int getCircleleQueryMemorySize(GNCDB *db,const char *coveragename,int *result_size ,double center_longitude,double center_latitude,double radious,bool isContain)
{
    int rc= 0;
    int count = 0;
    double top_point_lat = 0.0;
    double bottom_point_lat = 0.0;
    double left_point_lon = 0.0;
    double right_point_lon = 0.0;

    double leftCol = 0.0;
    double rightCol = 0.0;
    double topRow = 0.0;
    double bottomRow = 0.0;
    double xres = 0.0;
    double yres = 0.0;
    double baseLon,baseLat;
    rt_resolution resolution = NULL;
    Transaction *tx = NULL;

    /*1.参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }

    /*2.参数检查*/
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }

    /*3.取出精度数据*/
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        transactionRollback(tx,db);
        return GNCDB_SPACE_LACK;
    }
    rc = getCoverageResolution(db,resolution,coveragename);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(resolution);
        transactionRollback(tx,db);
        return rc;
    }
    xres = resolution->xres;
    yres = resolution->yres;

    /*4.确定外接矩形*/
    left_point_lon = center_longitude - radious;
    right_point_lon = center_longitude + radious;
    top_point_lat = center_latitude + radious;
    bottom_point_lat = center_latitude - radious;
    baseLon = left_point_lon;
    baseLat = top_point_lat;

    /*5.计算查询点个数*/
    leftCol = getBaseCol(baseLon,left_point_lon,xres);
    rightCol = getBaseCol(baseLon,right_point_lon,xres);
    topRow = getBaseRow(baseLat,top_point_lat,yres);
    bottomRow = getBaseRow(baseLat,bottom_point_lat,yres);
    if(isContain)
    {
        for(int row = topRow;row <= bottomRow;row++)
        {
            for(int col = leftCol;col <= rightCol;col++)
            {
                if(isTntersectCircle(getBaseLon(col,baseLon,xres),getBaseLat
                (row,baseLat,yres),xres,yres,center_longitude,center_latitude,radious))
                {
                    count++;
                }
            }
        }
    }
    else
    {
        for(int row = topRow;row <= bottomRow;row++)
        {
            for(int col = leftCol;col <= rightCol;col++)
            {
                if(isInCircle(getBaseLon(col,baseLon,xres),getBaseLat
                (row,baseLat,yres),xres,yres,center_longitude,center_latitude,radious))
                {
                    count++;
                }
            }
        }
    }

    *result_size = count * QUERY_POINT_SIZE;
    my_free(resolution);
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}

int GNCDB_DeleteRaster(GNCDB *db,const char *coveragename,const char *filename)
{
    int rc = 0;
    Transaction *tx = NULL;
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }
    /*1.根据文件名是否为空判断是删除整个覆盖层还是单个栅格文件*/
    if(filename != NULL)
    {
        rc = executorDeleteRaster(db,coveragename,filename);
    }
    else
    {
        rc = executorDeleteCoverage(db,coveragename);
    }
    if(rc != GNCDB_SUCCESS)
    {
        transactionRollback(tx,db);
        return rc;
    }
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}
int executorDeleteRaster(GNCDB *db,const char *coveragename,const char *filename)
{
    int rc = 0;
    char *temp_sql = NULL;
    char *sql = NULL;
    char *coverage_sec = NULL;
    char *coverage_level = NULL;
    char *coverage_tils = NULL;
    int sec_count = 0;
    int section_id = 0;
    char tile_id_tostring[20];
    QueryContext sqlctx_sec;
    QueryContext sqlctx_level;
    QueryContext sqlctx_tils;
    varArrayList *tileIdList = NULL;
    varArrayList *levelList = NULL;
    coverage_sec = strcatetablename(coveragename,"_sections");
    coverage_level = strcatetablename(coveragename,"_levels");
    coverage_tils = strcatetablename(coveragename,"_tiles");
    /*获取section_id*/
    temp_sql = "SELECT section_id from \"%s\" WHERE section_name = '\"%s\"'";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    temp_sql = replace_nth_placeholder(sql,filename,1);
    my_free(sql);
    rc = GNCDB_exec(db,temp_sql,getMaxcallback,&section_id,NULL);
    my_free(temp_sql);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        printf("get tile satrtid failed\n");
        return rc;
    }
    /*删除sections表*/
    temp_sql = "DELETE FROM \"%s\" WHERE section_id = ?";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    query_context_init(&sqlctx_sec,sql,128,coveragename);
    my_free(sql);
    bind_int(&sqlctx_sec,1,section_id);
    rc = GNCDB_exec(db,sqlctx_sec.modified_sql,NULL,NULL,NULL);
    query_context_destroy(&sqlctx_sec);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        printf("Delete %s failed\n",filename);
        return rc;
    }
    /*如果sections表为空则删除level表*/
    temp_sql = "SELECT COUNT(*) FROM \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    rc = GNCDB_exec(db,sql,getMaxcallback,&sec_count,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        printf("get section count failed\n");
        return rc;
    }
    /*清空level表*/
    if(sec_count == 0)
    {
        /*查询level表中的行*/
        levelList = varArrayListCreate(ORDER,sizeof(int),1,intCompare,NULL);
        temp_sql = "SELECT pyramid_level FROM \"%s\"";
        sql = replace_nth_placeholder(temp_sql,coverage_level,1);
        rc = GNCDB_exec(db,sql,getListCallBacks,levelList,NULL);
        my_free(sql);
        if(rc != GNCDB_SUCCESS)
        {
            printf("get level list failed\n");
            my_free(coverage_sec);
            my_free(coverage_level);
            my_free(coverage_tils);
            varArrayListDestroy(&levelList);
            return rc;
        }
        temp_sql = "DELETE FROM \"%s\" WHERE pyramid_level = ?";
        sql = replace_nth_placeholder(temp_sql,coverage_level,1);
        query_context_init(&sqlctx_level,sql,128,coveragename);
        my_free(sql);
        /*将level表中的行删除*/
        for(int level = 0;level < varArrayListGetCount(levelList);level++)
        {
            int pyramid_level = *((int *)varArrayListGet(levelList,level));
            bind_int(&sqlctx_level,1,pyramid_level);
            rc = GNCDB_exec(db,sqlctx_level.modified_sql,NULL,NULL,NULL);
            if(rc != GNCDB_SUCCESS)
            {
                printf("delete level %s failed\n",coverage_level);
                my_free(coverage_sec);
                my_free(coverage_level);
                my_free(coverage_tils);
                varArrayListDestroy(&levelList);
                return rc;
            }
            query_context_reset(&sqlctx_level);
        }
        query_context_destroy(&sqlctx_level);
        varArrayListDestroy(&levelList);
    }
    /*删除该栅格文件拥有的瓦片*/
    /*要先删除Blob*/
    tileIdList = varArrayListCreate(ORDER,sizeof(int),0,intCompare,NULL);
    temp_sql = "SELECT tile_id FROM \"%s\" WHERE section_id = ?";
    sql = replace_nth_placeholder(temp_sql,coverage_tils,1);
    query_context_init(&sqlctx_tils,sql,128,coveragename);
    my_free(sql);
    bind_int(&sqlctx_tils,1,section_id);
    rc = GNCDB_exec(db,sqlctx_tils.modified_sql,getListCallBacks,tileIdList,NULL);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        varArrayListDestroy(&tileIdList);
        printf("Get tile id list failed\n");
        return rc;
    }
    /*删除目标行的Blob*/
    for(int tile = 0;tile < varArrayListGetCount(tileIdList);tile++)
    {
        int deleteTileId = *((int *)varArrayListGet(tileIdList,tile));
        sprintf(tile_id_tostring, "%d", deleteTileId);
        rc = GNCDB_deleteBlob(db,coverage_tils,3,1,tile_id_tostring);
        if(rc != GNCDB_SUCCESS)
        {
            printf("Delete blob failed\n");
            my_free(coverage_sec);
            my_free(coverage_level);
            my_free(coverage_tils);
            varArrayListDestroy(&tileIdList);
            return rc;
        }
    }
    varArrayListDestroy(&tileIdList);
    /*删除目标行*/
    temp_sql = "DELETE FROM \"%s\" WHERE section_id = ?";
    sql = replace_nth_placeholder(temp_sql,coverage_tils,1);
    query_context_init(&sqlctx_tils,sql,128,coveragename);
    my_free(sql);
    bind_int(&sqlctx_tils,1,section_id);
    rc = GNCDB_exec(db,sqlctx_tils.modified_sql,NULL,NULL,NULL);
    query_context_destroy(&sqlctx_tils);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        printf("Delete tile failed\n");
        return rc;
    }
    /*处理raster表中的范围数据*/
    updateRasterExtent(db,coveragename);
    my_free(coverage_sec);
    my_free(coverage_level);
    my_free(coverage_tils);
    return GNCDB_SUCCESS;
}
int executorDeleteCoverage(GNCDB *db,const char *coveragename)
{
    int rc = 0;
    char *temp_sql = NULL;
    char *sql = NULL;
    char *coverage_sec = NULL;
    char *coverage_level = NULL;
    char *coverage_tils = NULL;
    coverage_sec = strcatetablename(coveragename,"_sections");
    coverage_level = strcatetablename(coveragename,"_levels");
    coverage_tils = strcatetablename(coveragename,"_tiles");
    /*删除rastercoverage中的行*/
    temp_sql = "DELETE FROM raster_coverages WHERE coverage_name = '\"%s\"'";
    sql = replace_nth_placeholder(temp_sql,coveragename,1);
    rc = GNCDB_exec(db,sql,NULL,NULL,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        printf("delete %s coverage failed\n",coveragename);
        return rc;
    }
    /*删除sections表*/
    temp_sql = "DROP TABLE \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    rc = GNCDB_exec(db,sql,NULL,NULL,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        printf("delete table %s failed\n",coverage_sec);
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        return rc;
    }
    /*删除level表*/
    temp_sql = "DROP TABLE \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_level,1);
    rc = GNCDB_exec(db,sql,NULL,NULL,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        printf("delete table %s failed\n",coverage_level);
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        return rc;
    }
    /*删除tiles表*/
    temp_sql = "DROP TABLE \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_tils,1);
    rc = GNCDB_exec(db,sql,NULL,NULL,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        printf("delete table %s failed\n",coverage_tils);
        my_free(coverage_sec);
        my_free(coverage_level);
        my_free(coverage_tils);
        return rc;
    }
    my_free(coverage_sec);
    my_free(coverage_level);
    my_free(coverage_tils);
    return GNCDB_SUCCESS;
}

int GNCDB_UpdateRaster(GNCDB *db,const char *coveragename,const char *src_path,double longitude,double latitude,int16_t newvalue,int srid)
{
    int rc = 0;
    Transaction *tx = NULL;
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }
    //根据src_path是否为空判断是更新整个文件还是单个点
    if(src_path == NULL)
    {
        rc = executorUpdateRaster(db,coveragename,longitude,latitude,newvalue);
    }
    else
    {
        rc = executorUpdateFile(db,src_path,coveragename,srid);
    }
    if(rc != GNCDB_SUCCESS)
    {
        transactionRollback(tx,db);
        return rc;
    }
    transactionCommit(tx,db);
    return GNCDB_SUCCESS;
}
int executorUpdateRaster(GNCDB *db,const char *coveragename,double longitude,double latitude,int16_t newvalue)
{
    int rc = GNCDB_SUCCESS;
    char *coverage_sec = NULL;
    char *tablename = NULL;
    int offset = 0;
    int length = 0;
    int row = 0;
    int col = 0;
    double tile_upleft_lon = 0.0;
    double tile_upleft_lat = 0.0;
    char tile_id_tostring[20];
    int blob_data_sz = 0;
    BYTE *buffer = NULL;
    BYTE *p_test = NULL;
    rt_resolution resolution = NULL;
    rt_raster raster = NULL;
    int tileid = 0;
    FILE *file = fopen("/tmp/output2.txt", "w");
    if (file == NULL) {
        printf("无法打开文件！\n");
        return 1;
    }
    /*参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    /*获取分辨率*/
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    if(resolution == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    rc = getCoverageResolution(db,resolution,coveragename);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(resolution);
        return rc;
    }
    /*获取查询点所在的栅格的中心坐标*/
    convertLatLonToGridCenter(&longitude,&latitude,resolution);
    /*获取瓦片*/
    coverage_sec = strcatetablename(coveragename,"_sections");
    tileid = getTileId(db,longitude,latitude,resolution,coverage_sec);
    my_free(coverage_sec);
    /*拼接瓦片集合*/
    rc = load_single_tile_from_db(db,tileid,coveragename,&buffer,&blob_data_sz);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(resolution);
        return rc;
    }
    /*测试Blob*/
    p_test = buffer + 30;
    assert(*p_test == RT_DATA_START);
    p_test++;
    for(int i =0 ;i<256*256;i++)
    {
        short num = 0;
        readShort(&num,p_test,&length);
        p_test += 2;
        // 将 num 的值写入文件
        fprintf(file, "%d\n", num);
        // printf("%d\n",num);
    }
    // 关闭文件
    fclose(file);
    /*获取目标位置的偏移,以栅格中心为边界*/
    tile_upleft_lon = getTileBounds(db,coveragename,tileid,0);
    tile_upleft_lat = getTileBounds(db,coveragename,tileid,3);
    row = round((tile_upleft_lat - latitude)/resolution->yres);
    col = round((longitude - tile_upleft_lon)/resolution->xres);
    offset = (row*resolution->tile_width + col) * sizeof(short);
    /*解码Blob*/
    raster = raster_decode(0,buffer,blob_data_sz,&rc);
    my_free(buffer);
    buffer = NULL;
    if(raster == NULL)
    {
        my_free(resolution);
        return rc;
    }
    writeShort(newvalue,raster->rasterBuffer + offset,&length);
    /*编码Blob*/
    raster_encode(raster,&buffer,&blob_data_sz,100,isLittleEnsian());
    /*写回,要先删除对应的Blob*/
    tablename = strcatetablename(coveragename,"_tiles");
    sprintf(tile_id_tostring, "%d", tileid);
    rc = GNCDB_deleteBlob(db,tablename,3,1,tile_id_tostring);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(resolution);
        return rc;
    }
    /*写回Blob*/
    rc = GNCDB_setBlob(db,tablename,3,buffer,blob_data_sz,1,tile_id_tostring);
    my_free(tablename);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(resolution);
        my_free(buffer);
        return rc;
    }
    rasterDestory(raster);
    my_free(resolution);
    my_free(buffer);
    return GNCDB_SUCCESS;
}
int executorUpdateFile(GNCDB *db,const char *src_path,const char *coveragename,int srid)
{
    int rc = 0;
    char *filename = NULL;
    /*1.删除对应的文件*/
    filename = extract_basename(src_path);
    rc = executorDeleteRaster(db,coveragename,filename);
    my_free(filename);
    filename = NULL;
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    /*2.重新导入*/
    rc = GNCDB_ImportRaster(db,src_path,coveragename,srid,false);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

int GNCDB_previewRaster(GNCDB *db,const char *coveragename,char *dst_path,int out_width,int out_height,double minx,double maxy,double xres,double yres)
{
    int rc = 0;
    Transaction *tx = NULL;
    if(db == NULL || coveragename == NULL || dst_path == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    tx = transcationConstrcut(db);
    if(tx == NULL)
    {
        return GNCDB_MEM;
    }
    rc = executorPreviewRaster(db,coveragename,dst_path,out_width,out_height,minx,maxy,xres,yres);
    if(rc != GNCDB_SUCCESS)
    {
        transactionRollback(tx,db);
    }
    else
    {
        transactionCommit(tx,db);
    }
    return GNCDB_SUCCESS;
}

int executorPreviewRaster(GNCDB *db,const char *coveragename,char *dst_path,int out_width,int out_height,double minx,double maxy,double xres,double yres)
{
    int rc = 0;
    rt_coverage cvg = NULL;
    int sec_level = 0;
    int scale = 0;
    unsigned char *bufpix = NULL;
    int buf_size = 0;
    varArrayList *tileIdList = NULL;
    rt_geo_extent target_bound = NULL;
    rt_resolution resolution = NULL;
    rt_raster raster = NULL;
    char *worldFile = NULL;
    int blob_size;
    unsigned char *blob;
    clock_t start,end;
    start = clock();
    // rt_section section = NULL;
    /*1.取出相应的覆盖层*/
    rc = get_coverage_fromdb(db,coveragename,&cvg);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    end = clock();
    printf("%f\n",((double) (end - start)) / CLOCKS_PER_SEC);
    /*2.找到对应的数据版本和缩放因子*/
    rc = find_data_scale(db,cvg,&xres,&yres,&sec_level,&scale);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        return rc;
    }
    /*3.准备数据缓冲区*/
    bufpix = my_malloc(out_height * out_width * 3);
    if(bufpix == NULL)
    {
        my_free(cvg);
        return GNCDB_SPACE_LACK;
    }
    memset(bufpix,255,buf_size);
    end = clock();
    printf("%f\n",((double) (end - start)) / CLOCKS_PER_SEC);
    /*4.将数据取到缓冲区*/
    tileIdList = varArrayListCreate(ORDER,sizeof(int),0,intCompare,NULL);
    target_bound = my_malloc(sizeof(struct rt_geo_extent_t));
    target_bound->extent_minx = minx;
    target_bound->extent_maxy = maxy;
    target_bound->extent_maxx = minx + xres * out_width;
    target_bound->extent_miny = maxy - yres * out_height;
    resolution = my_malloc(sizeof(struct rt_resolution_t));
    resolution->xres = xres;
    resolution->yres = yres;
    resolution->tile_width = cvg->tileWidth;
    resolution->tile_height = cvg->tileHeight;
    rc = Get_target_tile_id(db,coveragename,xres,yres,target_bound,sec_level,tileIdList);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(bufpix);
        my_free(target_bound);
        my_free(resolution);
        varArrayListDestroy(&tileIdList);
        return rc;
    }
    end = clock();
    printf("%f\n",((double) (end - start)) / CLOCKS_PER_SEC);
    rc = loadTile_from_db(db,tileIdList,bufpix,buf_size,coveragename,out_width,out_height,
    target_bound->extent_minx,target_bound->extent_maxy,3,cvg->sampleType,resolution,1,scale);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(bufpix);
        my_free(target_bound);
        my_free(resolution);
        varArrayListDestroy(&tileIdList);
    }
    end = clock();
    printf("loadtile %f\n",((double) (end - start)) / CLOCKS_PER_SEC);
    /*5.将数据打包成jpg图片并输出*/
    raster = rasterConstruct(out_width,out_height,cvg->sampleType,cvg->sampleType,
    3,bufpix,buf_size,NULL,0);
    if(raster == NULL)
    {
        my_free(cvg);
        my_free(bufpix);
        my_free(target_bound);
        my_free(resolution);
        varArrayListDestroy(&tileIdList);
        return GNCDB_MEM;
    }

    rc = raster_to_jpeg (raster, &blob, &blob_size, 85);
    rc = jpg_to_file (dst_path, blob, blob_size);
    my_free (blob);

    /*6.生成jgw文件并输出到文件夹*/
    getJgwFilePath(dst_path,&worldFile);
    rc = write_to_jgw(worldFile,xres,yres,minx,maxy);
    my_free(worldFile);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(cvg);
        my_free(target_bound);
        my_free(resolution);
        rasterDestory(raster);
        remove(dst_path);
        varArrayListDestroy(&tileIdList);
        return rc;
    }
    end = clock();
    printf("%f\n",((double) (end - start)) / CLOCKS_PER_SEC);
    my_free(cvg);
    my_free(target_bound);
    my_free(resolution);
    rasterDestory(raster);
    raster = NULL;
    varArrayListDestroy(&tileIdList);
    return GNCDB_SUCCESS;
}

int find_data_scale(GNCDB *db,rt_coverage cvg,double *xres,double *yres,int *sec_level,int *scale)
{
   int rc = 0;
   QueryResult *qr = queryResultCreate();
   char *sql = NULL;
   char *temp_sql = NULL;
   char *coverage_level = NULL;
   int pyramid_level = 0;
   double x_resolution = 0.0;
   double y_resolution = 0.0;
   bool found = false;
   int totalNum = 0;

   coverage_level = strcatetablename(cvg->coverageName,"_levels");
   temp_sql = "SELECT pyramid_level,x_resolution_1_1,y_resolution_1_1,x_resolution_1_2,"
   "y_resolution_1_2,x_resolution_1_4,y_resolution_1_4,x_resolution_1_8,y_resolution_1_8"
   " FROM \"%s\"";
   sql = replace_nth_placeholder(temp_sql,coverage_level,1);
   rc = GNCDB_exec(db,sql,myCallBacks,qr,NULL);
   my_free(coverage_level);
   my_free(sql);
   if(rc != GNCDB_SUCCESS)
   {
      return rc;
   }
   totalNum = qr->count/qr->columnum;
   for(int i = 0;i < totalNum;i++)
   {
        pyramid_level = atoi(qr->results[i * 9]);
        for(int j = 0;j < 4;j++)
        {
            x_resolution = atof(qr->results[i * 9 + 1 + 2 * j]);
            y_resolution = atof(qr->results[i * 9 + 1 + 2 * j +1]);
            if(is_unstrict_equal(*xres,x_resolution) && is_unstrict_equal(*xres,x_resolution))
            {
                found = true;
                *xres = x_resolution;
                *yres = y_resolution;
                *sec_level = pyramid_level;
                *scale = pow(2,j);
                if(j == 3 && i != totalNum-1)
                {
                    *sec_level = pyramid_level + 1;
                    *scale = pow(2,0);
                }
                queryResultDestroy(qr);
                return GNCDB_SUCCESS;
            }
        }
   }
   queryResultDestroy(qr);
   if(!found)
   {
    printf("The Resolution is valid\n");
    return GNCDB_NOT_FOUND;
   }
   return GNCDB_SUCCESS;
}

int Get_target_tile_id(GNCDB *db,const char *coveragename,double xres,double yres,rt_geo_extent geo_bound,int pyramid_level,varArrayList *tileIdList)
{
    int rc = 0;
    double maxx = geo_bound->extent_maxx;
    double maxy = geo_bound->extent_maxy;
    double miny = geo_bound->extent_miny;
    double minx = geo_bound->extent_minx;
    char *temp_sql = NULL;
    char *sql = NULL;
    char *coverage_tiles = NULL;
    QueryContext sqlctx;
    QueryResult *qr = queryResultCreate();
    coverage_tiles = strcatetablename(coveragename,"_tiles");
    temp_sql = "SELECT tile_id FROM \"%s\" WHERE x_min < ? AND x_max > ? AND"
    " y_min < ? AND y_max > ? AND pyramid_level = ?";
    sql = replace_nth_placeholder(temp_sql,coverage_tiles,1);
    my_free(coverage_tiles);
    query_context_init(&sqlctx,sql,512,coveragename);
    my_free(sql);
    bind_float(&sqlctx,1,maxx);
    bind_float(&sqlctx,2,minx);
    bind_float(&sqlctx,3,maxy);
    bind_float(&sqlctx,4,miny);
    bind_int(&sqlctx,5,pyramid_level);
    rc = GNCDB_exec(db,sqlctx.modified_sql,myCallBacks,qr,NULL);
    query_context_destroy(&sqlctx);
    if(rc != GNCDB_SUCCESS)
    {
        queryResultDestroy(qr);
        return rc;
    }
    for(int i = 0;i < qr->count;i++)
    {
        int tile_id = atoi(qr->results[i]);
        varArrayListAdd(tileIdList,&tile_id);
    }
    queryResultDestroy(qr);
    return GNCDB_SUCCESS;
}

int load_single_tile_from_db(GNCDB *db,int tile_id,const char *coveragename,BYTE **buffer,int *blob_size)
{
    int rc = 0;
    char strbuffer[20];
    BYTE *blob_data = NULL;
	int blob_data_sz = 0;
    char *coverage_tiles = NULL;
    //获取瓦片的blob的size
    blob_data_sz = getBlobSize(db,coveragename,tile_id);
    // blob_data_sz = 197155;
    blob_data = my_malloc(sizeof(char)*blob_data_sz);
    if(blob_data == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    sprintf(strbuffer, "%d", tile_id);
    coverage_tiles = strcatetablename(coveragename,"_tiles");
    rc = GNCDB_getBlob(db,coverage_tiles,3,blob_data,blob_data_sz,1,strbuffer);
    my_free(coverage_tiles);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(blob_data);
        return rc;
    }
    *buffer = blob_data;
    *blob_size = blob_data_sz;
    return GNCDB_SUCCESS;
}

rt_raster cateTile(GNCDB *db, varArrayList* tileIdset,const char *coveragename,int width,int height,
double minx,double maxy,int num_bands,int sample_type,rt_resolution resolution,int max_threads,int *err_code)
{
    int rc = 0;
    rt_raster raster = NULL;
    unsigned char *bufpix = NULL;
    int bufpix_sz = 0;
    bufpix_sz = num_bands * get_pixelsize(sample_type) * width * height;
    bufpix = my_malloc(bufpix_sz);
    if(bufpix == NULL)
    {
        *err_code = GNCDB_SPACE_LACK;
        return NULL;
    }
    /*1.首先导出tile*/
    rc = loadTile_from_db(db, tileIdset,bufpix,bufpix_sz,coveragename,
    width,height,minx,maxy,num_bands,sample_type,resolution,max_threads,1);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(bufpix);
        *err_code = rc;
        return NULL;
    }
    /*2.此时数据都存在bufpix中*/
    raster = rasterConstruct(width,height,sample_type,PIXEL_DEM,num_bands,bufpix,bufpix_sz,NULL,0);
    if(raster == NULL)
    {
        //失败只可能是分配内存失败
        printf("create raster failed\n");
        *err_code = GNCDB_SPACE_LACK;
        return NULL;
    }
    raster->minX = minx;
    raster->maxY = maxy;
    raster->hResolution = resolution->yres;
    raster->vResolution = resolution->xres;
    /*3.raster的数据缓冲区就是目标区域数据*/
    *err_code = GNCDB_SUCCESS;
    return raster;
}

int loadTile_from_db(GNCDB *db, varArrayList* tileIdset,unsigned char *outbuf,int buffer_sz,
const char *coveragename,int width,int height,double minx,double maxy,
int num_bands,int sample_type,rt_resolution resolution,int max_threads,int scale)
{
    /*1.将所有瓦片从DBMS取出*/
    int rc;
    rt_tile_decoder tile_decoder = NULL;
    rt_tile_decoder decoder = NULL;
    rt_tile_decoder *thread_slots = NULL;
    int thread_count;
    int iaux;
    char *temp_sql = NULL;
    char *sql = NULL;
    char *coverage_tiles = NULL;
    QueryContext sqlctx_tils;
    rt_geo_extent tile_geo_extent = NULL;
    coverage_tiles = strcatetablename(coveragename,"_tiles");
    
    /*2.变量初始化*/
    temp_sql = "SELECT x_min,x_max,y_min,y_max FROM "
    "\"%s\" WHERE tile_id = ?";
    sql = replace_nth_placeholder(temp_sql,coverage_tiles,1);
    query_context_init(&sqlctx_tils,sql,512,coveragename);
    my_free(sql);
    if (max_threads < 1)
	max_threads = 1;
    if (max_threads > 64)
	max_threads = 64;
    /*3.初始化解码器*/
    tile_decoder = my_malloc (sizeof (struct rt_tile_decoder_t) * max_threads);
    if (tile_decoder == NULL)
    {
        my_free(coverage_tiles);
        return GNCDB_SPACE_LACK;
    }
    for (iaux = 0; iaux < max_threads; iaux++)
    {
	  decoder = tile_decoder + iaux;
	  decoder->opaque_thread_id = NULL;
	  decoder->blob_data = NULL;
	  decoder->blob_data_sz = 0;
	  decoder->outbuf = outbuf;
	  decoder->mask = NULL;
	  decoder->width = width;
	  decoder->height = height;
	  decoder->sample_type = sample_type;
	  decoder->num_bands = num_bands;
	  decoder->x_res = resolution->xres;
	  decoder->y_res = resolution->yres;
	  decoder->minx = minx;
	  decoder->maxy = maxy;
	  decoder->scale = scale;
	  decoder->raster = NULL;
    }

    /*4.定义线程槽*/
    thread_slots = my_malloc (sizeof (struct rt_tile_decoder_t) * max_threads);
    if(thread_slots == NULL)
    {
        my_free(coverage_tiles);
        my_free(tile_decoder);
        return GNCDB_SPACE_LACK;
    }
    for (thread_count = 0; thread_count < max_threads; thread_count++)
    {
        *(thread_slots + thread_count) = NULL;
    }
    thread_count = 0;
    tile_geo_extent = my_malloc(sizeof(struct rt_geo_extent_t));
    if(thread_slots == NULL)
    {
        my_free(coverage_tiles);
        my_free(tile_decoder);
        my_free(thread_slots);
        return GNCDB_SPACE_LACK;
    }
    for(int tile = 0;tile < varArrayListGetCount(tileIdset);tile++)
    {
        char strbuffer[20];
        const unsigned char *blob_data = NULL;
		int blob_data_sz = 0;
        int tile_id = *((int *)varArrayListGet(tileIdset,tile));
        //获取瓦片的边界
        bind_int(&sqlctx_tils,1,tile_id);
        rc = GNCDB_exec(db,sqlctx_tils.modified_sql,geoExtentCallBacks,tile_geo_extent,NULL);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(coverage_tiles);
            my_free(tile_geo_extent);
            my_free(tile_decoder);
            my_free(thread_slots);
            return rc;
        }
        decoder = tile_decoder + thread_count;
		decoder->tile_id = tile_id;
		decoder->tile_minx = tile_geo_extent->extent_minx;
		decoder->tile_maxy = tile_geo_extent->extent_maxy;
        //获取瓦片的blob的size
        blob_data_sz = getBlobSize(db,sqlctx_tils.coveragename,tile_id);
        // blob_data_sz = 197155;
        if(blob_data_sz == -1)
        {
            my_free(coverage_tiles);
            my_free(tile_geo_extent);
            my_free(tile_decoder);
            my_free(thread_slots);
            return rc;
        }
        blob_data = my_malloc(sizeof(char)*blob_data_sz);
        if(blob_data == NULL)
        {
            my_free(coverage_tiles);
            my_free(tile_geo_extent);
            my_free(tile_decoder);
            my_free(thread_slots);
            return GNCDB_SPACE_LACK;
        }
        sprintf(strbuffer, "%d", tile_id);
        rc = GNCDB_getBlob(db,coverage_tiles,3,(BYTE *)blob_data,blob_data_sz,1,strbuffer);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(tile_geo_extent);
            my_free(tile_decoder);
            my_free(thread_slots);
            my_free(coverage_tiles);
            printf("Get Blob failed\n");
            return rc;
        }
        decoder->blob_data = my_malloc(blob_data_sz);
        if (decoder->blob_data == NULL)
        {
            my_free(tile_geo_extent);
            my_free(tile_decoder);
            my_free(thread_slots);
            my_free(coverage_tiles);
            return GNCDB_SPACE_LACK;
        }
        //数据流向解码器
        memcpy(decoder->blob_data, blob_data, blob_data_sz);
        my_free((void *)blob_data);
        decoder->blob_data_sz = blob_data_sz;
        //解码
        if (max_threads > 1)
        {
          //多线程
          *(thread_slots + thread_count) = decoder;
          thread_count++;
          if (thread_count == max_threads) {
            if (!do_run_decoder_children(thread_slots, thread_count))
            {
                my_free(tile_geo_extent);
                my_free(tile_decoder);
                my_free(thread_slots);
                my_free(coverage_tiles);
                //todo缺个状态码
            }
            thread_count = 0;
          }
        }
        else
        {
          /*单线程*/
          decode_tile(decoder);
          if (decoder->retcode != GNCDB_SUCCESS) {
            printf("decode failed\n");
            my_free(tile_geo_extent);
            my_free(tile_decoder);
            my_free(thread_slots);
            my_free(coverage_tiles);
            //todo失败了怎么清理  失败的时候已经清理
            return decoder->retcode;
          }
        }
        query_context_reset(&sqlctx_tils);
    }
    query_context_destroy(&sqlctx_tils);
    my_free(coverage_tiles);
    my_free(tile_geo_extent);
    my_free(tile_decoder);
    my_free(thread_slots);
    //todo瓦片解码器谁清理的
    return GNCDB_SUCCESS;
}

int getBlobSize(GNCDB *db,const char *coveragename,int tile_id)
{
    int rc = 0;
    char *coverage_tils = strcatetablename(coveragename,"_tiles");
    char *temp_sql = NULL;
    char *sql = NULL;
    int blob_size = 0;
    QueryContext sqlctx_tils;
    temp_sql = "SELECT blob_size FROM \"%s\" WHERE tile_id = ?";
    sql = replace_nth_placeholder(temp_sql,coverage_tils,1);
    my_free(coverage_tils);
    query_context_init(&sqlctx_tils,sql,64,coveragename);
    my_free(sql);
    bind_int(&sqlctx_tils,1,tile_id);
    rc = GNCDB_exec(db,sqlctx_tils.modified_sql,getMaxcallback,&blob_size,NULL);
    query_context_destroy(&sqlctx_tils);
    if(rc != GNCDB_SUCCESS)
    {
        printf("get blob size failed,%d\n",rc);
        return rc;
    }
    return blob_size;
}

static int do_run_decoder_children(rt_tile_decoder *thread_slots, int thread_count)
{
    /* concurrent execution of all decoder children threads */
    rt_tile_decoder decoder;
    int i;
#if defined(_WIN32) && !defined(__MINGW32__)
    HANDLE *handles;
#endif

    for (i = 0; i < thread_count; i++)
      {
	  /* starting all children threads */
	  decoder = *(thread_slots + i);
	  start_decoder_thread (decoder);
      }

/* waiting until all child threads exit */
#if defined(_WIN32) && !defined(__MINGW32__)
    handles = malloc (sizeof (HANDLE) * thread_count);
    for (i = 0; i < thread_count; i++)
      {
	  /* initializing the HANDLEs array */
	  HANDLE *pOpaque;
	  decoder = *(thread_slots + i);
	  pOpaque = (HANDLE *) (decoder->opaque_thread_id);
	  *(handles + i) = *pOpaque;
      }
    WaitForMultipleObjects (thread_count, handles, TRUE, INFINITE);
    free (handles);
#else
    for (i = 0; i < thread_count; i++)
      {
	  pthread_t *pOpaque;
	  decoder = *(thread_slots + i);
	  pOpaque = (pthread_t *) (decoder->opaque_thread_id);
	  pthread_join (*pOpaque, NULL);
      }
#endif

/* all children threads have now finished: resuming the main thread */
    for (i = 0; i < thread_count; i++)
      {
	  /* cleaning up a request slot */
	  decoder = *(thread_slots + i);
	  if (decoder->blob_data != NULL)
	      free (decoder->blob_data);
	  if (decoder->raster != NULL)
	      rasterDestory ((rt_raster) (decoder->raster));
	  if (decoder->opaque_thread_id != NULL)
	      free (decoder->opaque_thread_id);
	  decoder->blob_data = NULL;
	  decoder->blob_data_sz = 0;
	  decoder->raster = NULL;
	  decoder->opaque_thread_id = NULL;
      }
    for (i = 0; i < thread_count; i++)
      {
	  /* checking for eventual errors */
	  decoder = *(thread_slots + i);
	  if (decoder->retcode != GNCDB_SUCCESS)
	    {
		// fprintf (stderr, ERR_FRMT64, decoder->tile_id);
		return 0;
	    }
      }
    return 1;
}

static void
start_decoder_thread (rt_tile_decoder decoder)
{
/* starting a concurrent thread */
#if defined(_WIN32) && !defined(__MINGW32__)
    HANDLE thread_handle;
    HANDLE *p_thread;
    DWORD dwThreadId;
    thread_handle =
	CreateThread (NULL, 0, doRunDecoderThread, decoder, 0, &dwThreadId);
    SetThreadPriority (thread_handle, THREAD_PRIORITY_IDLE);
    p_thread = malloc (sizeof (HANDLE));
    *p_thread = thread_handle;
    decoder->opaque_thread_id = p_thread;
#else
    pthread_t thread_id;
    pthread_t *p_thread;
    int ok_prior = 0;
    int policy;
    int min_prio;
    pthread_attr_t attr;
    struct sched_param sp;
    pthread_attr_init (&attr);
    if (pthread_attr_setschedpolicy (&attr, SCHED_RR) == 0)
      {
	  /* attempting to set the lowest priority */
	  if (pthread_attr_getschedpolicy (&attr, &policy) == 0)
	    {
		min_prio = sched_get_priority_min (policy);
		sp.sched_priority = min_prio;
		if (pthread_attr_setschedparam (&attr, &sp) == 0)
		  {
		      /* ok, setting the lowest priority */
		      ok_prior = 1;
		      pthread_create (&thread_id, &attr, doRunDecoderThread,
				      decoder);
		  }
	    }
      }
    if (!ok_prior)
      {
	  /* failure: using standard priority */
	  pthread_create (&thread_id, NULL, doRunDecoderThread, decoder);
      }
    p_thread = malloc (sizeof (pthread_t));
    *p_thread = thread_id;
    decoder->opaque_thread_id = p_thread;
#endif
}

void decode_tile(rt_tile_decoder decoder)
{
    /* servicing an AuxDecoder Tile request */
    decoder->raster = raster_decode (decoder->scale,
		decoder->blob_data,decoder->blob_data_sz,&decoder->retcode);
    if (decoder->blob_data != NULL)
	my_free (decoder->blob_data);
    decoder->blob_data = NULL;
    if (decoder->raster == NULL)
    {
	  decoder->retcode = -1;
      return;
    }
    if (!rl2_copy_raw_pixels_transparent
	((rt_raster) (decoder->raster), decoder->outbuf, decoder->mask,
	 decoder->width, decoder->height, decoder->sample_type,
	 decoder->num_bands, decoder->x_res,decoder->y_res, decoder->minx, decoder->maxy, decoder->tile_minx,
	 decoder->tile_maxy))
      {
	  decoder->retcode = GNCDB_SUCCESS;
      }
    rasterDestory ((rt_raster) (decoder->raster));
    decoder->raster = NULL;
    decoder->retcode = GNCDB_SUCCESS;
}

int rl2_copy_raw_pixels_transparent
	(rt_raster raster, unsigned char *outbuf, unsigned char *mask,
	 unsigned int width, unsigned int height, unsigned char sample_type,
	 unsigned char num_bands, double x_res,double y_res, double minx, double maxy, double tile_minx,
	 double tile_maxy)
{
    unsigned int tile_width = 0;
    unsigned int tile_height = 0;
    tile_width = raster->width;
    tile_height = raster->height;
    switch(sample_type)
    {
        case SAMPLE_8BIT:
            copy_uint8_raw_pixels_transparent ((const unsigned char *)
				(raster->rasterBuffer),
				(const unsigned char*)(raster->maskBuffer),
				(char *)outbuf, mask, width,
				height,raster->nBands, x_res,
				y_res, minx, maxy,tile_minx,
				tile_maxy,tile_width,tile_height);
            break;
        case SAMPLE_16BIT:
            copy_int16_raw_pixels_transparent ((const short*)
				(raster->rasterBuffer),
				(const unsigned char*)(raster->maskBuffer),
				(short *)outbuf, mask, width,
				height,raster->nBands, x_res,
				y_res, minx, maxy,tile_minx,
				tile_maxy,tile_width,tile_height);
            break;
    }
    return GNCDB_SUCCESS;
}


void copy_int16_raw_pixels_transparent (const short *buffer,const unsigned char *mask,
short *outbuf,unsigned char *outmask,unsigned short width,unsigned short height,
unsigned char num_bands,double x_res, double y_res,double minx, double maxy,
double tile_minx, double tile_maxy,unsigned short tile_width,unsigned short tile_height)
{
/* copying INT16 raw pixels from the DBMS tile into the output image */
    int x;
    int y;
    int out_x;
    int out_y;
    double geo_x;
    double geo_y;
    const short *p_in = buffer;
    const unsigned char *p_msk = mask;
    short *p_out;
    unsigned char *p_outmsk;
    int transparent;

    int ignore_no_data = 0;
    double y_res2 = y_res / 2.0;
    double x_res2 = x_res / 2.0;
    int offset = 0;

    int xx = 0;


    // 打开文件
    FILE *file = fopen("/tmp/output.txt", "w");
    if (file == NULL) {
        perror("无法打开文件");
        // return 1;
    }


// out_y = (maxy - geo_y) / y_res;
    geo_y = tile_maxy + y_res2;
    for (y = 0; y < tile_height; y++)
      {
	  geo_y -= y_res;
	//   out_y = customRound(maxy,geo_y,y_res);
    out_y = round((maxy - geo_y) / y_res);
	  if (out_y < 0 || out_y >= height)
	    {
		p_in += tile_width;
        offset += tile_width;
		if (p_msk != NULL)
		    p_msk += tile_width;
		continue;
	    }
    //   if(xx != out_y)
    //   {
    //     printf("ddddddddddddddddddd\n");
    //   }
	  geo_x = tile_minx - x_res2;
      xx = out_y + 1;
	  for (x = 0; x < tile_width; x++)
	    {
		geo_x += x_res;
		// out_x = customRound(geo_x,minx,x_res);
		out_x = round((geo_x - minx) / x_res);
		if (out_x < 0 || out_x >= width)
		  {
		      p_in++;
              offset++;
		      if (p_msk != NULL)
			  p_msk++;
		      continue;
		  }
        // if(tt != out_x)
        // {
        //     printf("DDDDDDDDDDDDDDDDDDDDDDDDD  %d %d\n",out_y,out_x);
        // }
        // printf("%d\n",offset);
		p_out = outbuf + (out_y * width) + out_x;
        // if(out_x == 0)
        // {
        //     printf("%d\n",*p_in);
        // }
        // readShort(&num,(BYTE *)outbuf,&xx);
        // printf("%d",num);
		p_outmsk = outmask + (out_y * width) + out_x;
		transparent = 0;
        // tt = out_x + 1;
 		if (p_msk != NULL)
		  {
		      if (*p_msk++ == 0)
			  transparent = 1;
		  }
		if (transparent || ignore_no_data)
		  {
		      /* already transparent or missing NO-DATA value */
		      if (transparent)
			{
			    /* skipping a transparent pixel */
			    p_out++;
			    p_in++;
			}
		      else
			  *p_out++ = *p_in++;
		      if (outmask != NULL)
			{
			    if (transparent)
				p_outmsk++;
			    else
				*p_outmsk++ = 0;
			}
		  }
		else
		  {
		    //   /* testing for NO-DATA values */
		    //   int match = 0;
		    //   const short *p_save = p_in;
		    //   short sample = 0;
		    //   rl2_get_pixel_sample_int16 (no_data, &sample);
		    //   if (sample == *p_in++)
			//   match = 1;
		    //   if (!match)
			// {
			//     /* opaque pixel */
			//     p_in = p_save;
                
                // 将结果写入文件
                fprintf(file, "%d\n", *p_in);
                *p_out++ = *p_in++;
			    if (outmask != NULL)
				*p_outmsk++ = 0;
			// }
		    //   else
			// {
			//     /* NO-DATA pixel */
			//     p_out++;
			//     if (outmask != NULL)
			// 	p_outmsk++;
			// }
		  }
	    }
      }
    // 关闭文件
    fclose(file);
}

void copy_uint8_raw_pixels_transparent (const unsigned char *buffer,const unsigned char *mask,
char *outbuf,unsigned char *outmask,unsigned short width,unsigned short height,
unsigned char num_bands,double x_res, double y_res,double minx, double maxy,
double tile_minx, double tile_maxy,unsigned short tile_width,unsigned short tile_height)
{
/* copying INT16 raw pixels from the DBMS tile into the output image */
    int x;
    int y;
    int out_x;
    int out_y;
    double geo_x;
    double geo_y;
    const char *p_in = buffer;
    const unsigned char *p_msk = mask;
    char *p_out;
    int transparent;
    double y_res2 = y_res / 2.0;
    double x_res2 = x_res / 2.0;

    geo_y = tile_maxy + y_res2;
    for (y = 0; y < tile_height; y++)
    {
	  geo_y -= y_res;
	  out_y = customRound(maxy,geo_y,y_res);
	  if (out_y < 0 || out_y >= height)
	    {
		p_in += tile_width * num_bands;
		if (p_msk != NULL)
		    p_msk += tile_width;
		continue;
	    }
	  geo_x = tile_minx - x_res2;
	  for (x = 0; x < tile_width; x++)
	  {
		geo_x += x_res;
		out_x = customRound(geo_x,minx,x_res);
		if (out_x < 0 || out_x >= width)
		{
		      p_in += num_bands;
		      if (p_msk != NULL)
			  p_msk++;
		      continue;
		}
		p_out = outbuf + ((out_y * width) + out_x) * num_bands;
		transparent = 0;
 		if (p_msk != NULL)
		{
		    if (*p_msk++ == 0)
			transparent = 1;
		}
		if (transparent)
		{
            for(int i = 0;i<num_bands;i++)
            {
                p_out ++;
			    p_in ++;
            }
		}
		else
		{
            // 将结果写入文件
            for(int i = 0;i<num_bands;i++)
            {
                *p_out++ = *p_in++;
            }
		}
	  }
    }
}

rt_raster raster_decode(int scale,const unsigned char *blob_data,int blob_data_sz,int *err_code)
{
    unsigned int width = 0;
    unsigned int height = 0;
    unsigned short mask_width = 0;
    unsigned short mask_height = 0;
    unsigned char sample_type = 0;
    unsigned char pixel_type = 0;
    unsigned char num_bands = 0;
    unsigned short row_stride = 0;
    unsigned int rows = 0;
    int uncompressed = 0;
    int compressed = 0;
    int uncompressed_mask = 0;
    int compressed_mask = 0;

    const unsigned char *pixels = NULL;
    const unsigned char *pixels_mask = NULL;
    unsigned char *pixels_data = NULL;
    int pixels_sz = 0;
    unsigned char *mask = NULL;
    int mask_sz = 0;
    const unsigned char *ptr = NULL;
    int swap = 0;
    int endian = 0;
    int endian_arch = isLittleEnsian();
    rt_raster raster = my_malloc(sizeof(struct rt_raster_t));
    int len = 0;
    /*1.首先检验blob数据的正确性*/
    if(!check_blob_data(blob_data,blob_data_sz,&width,&height,&sample_type,&pixel_type,&num_bands))
    {
        my_free(raster);
        //todo 缺乏状态码
        // *err_code = GNCDB_BLOB_ERR;
        return NULL;
    }
    /*2.根据编码的位置解码出相应的数据*/
    endian = *(blob_data + 2);
    num_bands = *(blob_data + 5);
    ptr = blob_data + 10;
    readShort(&row_stride,(BYTE *)ptr,&len);
    ptr += 2;
    readShort(&rows,(BYTE *)ptr,&len);
    ptr += 2;
    readInt(&uncompressed,(BYTE *)ptr,&len);
    ptr += 4;
    readInt(&compressed,(BYTE *)ptr,&len);
    ptr += 4;
    readInt(&uncompressed_mask,(BYTE *)ptr,&len);
    ptr += 4;
    readInt(&compressed_mask,(BYTE *)ptr,&len);
    ptr += 4;
    if (*ptr++ != RT_DATA_START)
	return NULL;
    /*3.真正的数据部分*/
    pixels_data = ptr;
    if (uncompressed_mask > 0)
    {
	  /* retrieving the mask */
	  ptr += compressed;
	  if (*ptr++ != RT_DATA_END)
	      return NULL;
	  if (*ptr++ != RT_MASK_START)
	      return NULL;
	  pixels_mask = ptr;
	  mask_width = width;
	  mask_height = height;
	  ptr += compressed_mask;
	  if (*ptr++ != RT_MASK_END)
	      return NULL;
    }
    /*4.将数据移到pixels缓冲区*/
    if (endian != endian_arch)
      swap = 1;
    else
      swap = 0;
    if (build_pixel_buffer(swap,
        scale,&width,&height,sample_type,
        num_bands,rows,pixels_data,(void **)(&pixels),
        &pixels_sz))
    {
            //失败只会是缺乏内存
            *err_code = GNCDB_SPACE_LACK;
            return NULL;
    }
    /*6.处理掩码*/
    if (pixels_mask != NULL)
      {
	  /* unpacking the mask */
	  unsigned char *mask_pix;
	  int mask_pix_sz;
	  if (uncompressed_mask != (mask_width * mask_height))
	  {
        //todo 缺乏状态码
        *err_code = GNCDB_SPACE_LACK;
        return NULL;
      }
	  if (!unpack_rle(mask_width, mask_height, pixels_mask, compressed_mask,
	       &mask_pix, &mask_pix_sz))
      {

      }
	  if (!rescale_mask(scale, &mask_width, &mask_height, mask_pix, &mask, &mask_sz))
	  {
		// free (mask_pix);
	   }
	  free (mask_pix);
    }

    raster = rasterConstruct (width, height, sample_type, pixel_type, num_bands,
			   pixels, pixels_sz, mask, mask_sz);
    if (raster == NULL)
	{
        //失败只会是缺乏内存
        *err_code = GNCDB_SPACE_LACK;
        printf("raster create failed\n");
        return NULL;
    }
    return raster;
}

static int
check_blob_data (const unsigned char *blob, int blob_sz, unsigned int *xwidth,
		unsigned int *xheight, unsigned char *xsample_type,
		unsigned char *xpixel_type, unsigned char *xnum_bands)
{
/* checking the OddBlock for validity */
    const unsigned char *ptr;
    short width;
    short height;
    unsigned char sample_type;
    unsigned char pixel_type;
    unsigned char num_bands;
    // unsigned char compression;
    int compressed;
    int compressed_mask;
    int endian;
    int len = 0;

    if (blob_sz < 36)
	return 0;
    ptr = blob;
    if (*ptr++ != 0x00)
	return 0;
    if (*ptr++ != RT_BLOCK_START)
	return 0;
    endian = *ptr++;
    if (endian == RT_LITTLE_ENDIAN || endian == RT_BIG_ENDIAN)
	;
    else
	return 0;
    sample_type = *ptr++;
    switch (sample_type)
      {
      case SAMPLE_8BIT:
      case SAMPLE_16BIT:
	  break;
      default:
	  return 0;
      };
    pixel_type = *ptr++;
    switch (pixel_type)
      {
      case PIXEL_DEM:
      case PIXEL_RGB:
	  break;
      default:
      printf("invalid pixel type\n");
	  return 0;
      };
    num_bands = *ptr++;
    readShort(&width,(BYTE *)ptr,&len);
    ptr += 2;
    readShort(&height,(BYTE *)ptr,&len);
    ptr += 2;
    ptr += 4;
    ptr += 4;
    readInt(&compressed,(BYTE *)ptr,&len);
    ptr += 4;
    ptr += 4;
    readInt(&compressed_mask,(BYTE *)ptr,&len);
    ptr += 4;
    if (*ptr++ != RT_DATA_START)
	return 0;
    if (blob_sz < 35 + compressed + compressed_mask)
	return 0;
    ptr += compressed;
    if (*ptr++ != RT_DATA_END)
	return 0;
    if (*ptr++ != RT_MASK_START)
	return 0;
    ptr += compressed_mask;
    if (*ptr++ != RT_MASK_END)
	return 0;
    if (*ptr != RT_BLOCK_END)
	return 0;

    *xwidth = width;
    *xheight = height;
    *xsample_type = sample_type;
    *xpixel_type = pixel_type;
    *xnum_bands = num_bands;
    return 1;
}

static int
build_pixel_buffer (int swap, int scale, unsigned int *xwidth,
		    unsigned int *xheight, unsigned char sample_type,
		    unsigned char num_bands, unsigned short rows,
		    const void *pixels_odd, void **pixels, int *pixels_sz)
{
/* decoding the raster */
    unsigned int width = *xwidth;
    unsigned int height = *xheight;
    void *buf;
    int buf_size;
    unsigned char pixel_size;
    pixel_size = get_pixelsize(sample_type);

    //预览可能会用到,这里修改
    if(scale > 1)
    {
        rebuild_sample(scale, xwidth, xheight, sample_type,
				        num_bands,pixels_odd, pixels, pixels_sz);
        return GNCDB_SUCCESS;
    }
    buf_size = width * height * pixel_size * num_bands;
    buf = my_malloc(buf_size);
    if(buf == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    switch (sample_type)
    {
      case SAMPLE_16BIT:
	  do_copy_uint16 (swap, pixels_odd, buf, width, rows, num_bands);
	  break;
      default:
      memcpy(buf,pixels_odd,buf_size);
    };

    *pixels = buf;
    *pixels_sz = buf_size;
    return GNCDB_SUCCESS;
}

static void
do_copy_uint16 (int swap, const unsigned short *p_odd, unsigned short *buf,
		unsigned short width, unsigned short odd_rows, unsigned char num_bands)
{
/* reassembling a UINT16 raster - scale 1:1 */
    int row;
    int col;
    int band;
    unsigned short *p_out;

    p_out = buf;
    for (row = 0; row < odd_rows; row++)
      {
	  for (col = 0; col < width; col++)
	    {
		for (band = 0; band < num_bands; band++)
		  {
		      if (swap)
			  *p_out++ = swapUINT16 (*p_odd++);
		      else
			  *p_out++ = *p_odd++;
		  }
	    }
    }
}

void getTileIdSet(GNCDB *db,varArrayList *tileIdList,rt_geo_extent tile_bound,rt_resolution resolution,const char *coverage_sec)
{
    double min_lon = 0.0;
    double max_lat = 0.0;
    double max_lon = 0.0;
    double min_lat = 0.0;

    //右边界所在瓦片的最大经度
    double rightMaxLon = 0.0;
    //下边界所在瓦片的最小纬度
    double leftMinLat = 0.0;

    min_lon = tile_bound->extent_minx;
    max_lat = tile_bound->extent_maxy;
    max_lon = tile_bound->extent_maxx;
    min_lat = tile_bound->extent_miny;

    rightMaxLon =  getTileBound(db,max_lon,max_lat,resolution,1);
    leftMinLat =  getTileBound(db,min_lon,min_lat,resolution,2);

    for(double lon = min_lon;lon <= rightMaxLon;lon += resolution->xres*resolution->tile_width)
    {
        for(double lat = max_lat;lat >= leftMinLat;lat -= resolution->yres*resolution->tile_height)
        {
            int tileId = getTileId(db,lon,lat,resolution,coverage_sec);
            varArrayListAdd(tileIdList,&tileId);
        }
    }
}

double getTileBound(GNCDB *db,double lon,double lat,rt_resolution resolution,int index)
{
    double leftup_lon = 0.0;
    double leftup_lat = 0.0;
    double result = 0.0;
    int row = 0,col = 0;
    int tilecol = 0,tilerow = 0;
    //获取左上角坐标
    // leftup_lon = floor(lon) - 0.5*resolution->xres;
    leftup_lon = floor(lon) - 0.5*resolution->xres;
    // leftup_lat = ceil(lat) + 0.5*resolution->xres;
    leftup_lat = ceil(lat) + 0.5*resolution->xres;
    //得到瓦片的相对偏移
    row = getRow(lat,resolution->yres);
    col = getCol(lon,resolution->xres);
    tilecol = ceil(col*1.00/resolution->tile_width);
    tilerow = ceil(row*1.00/resolution->tile_height);
    //0-minx,1-maxx,2-miny,3-maxy
    switch(index)
    {
        case 0:
            result = leftup_lon + (tilecol-1)*resolution->tile_width*resolution->xres;
            break;
        case 1:
            result = leftup_lon + tilecol*resolution->tile_width*resolution->xres;
            break;
        case 2:
            result = leftup_lat - tilerow*resolution->tile_height*resolution->yres;
            break;
        case 3:
            result = leftup_lat - (tilerow-1)*resolution->tile_height*resolution->yres;
            break;
    }
    return result;
}

int getTileId(GNCDB *db,double lon,double lat,rt_resolution resolution,char *coverage_sec)
{
    char *filename = (char *)my_malloc(sizeof(char)*100);
    int tileOffset = 0;
    int rc = 0;
    int tileStartId = 0;
    getHgtFileName(filename,lon,lat);
    tileOffset = getTileOffset(lon,lat,resolution);
    rc = getTileStartId(db,filename,&tileStartId,coverage_sec);
    if(rc != GNCDB_SUCCESS)
    {
        printf("get startid failed\n");
        return rc;
    }
    my_free(filename);
    return tileOffset + tileStartId - 1;
}

int getTileOffset(double lon,double lat,rt_resolution resolution)
{
    int leftup_col = 0;
    int leftup_row = 0;
    int leftup_tilecol = 0;
    int leftup_tilerow = 0;
    int tilerow = 0;
    leftup_col = getCol(lon,resolution->xres);
    leftup_row = getRow(lat,resolution->yres);
    leftup_tilecol = ceil(leftup_col*1.00/resolution->tile_width);
    leftup_tilerow = ceil(leftup_row*1.00/resolution->tile_height);
    tilerow = ceil(1/resolution->xres/resolution->tile_width);
    return leftup_tilecol + (leftup_tilerow-1)* tilerow;
}

void getHgtFileName(char *filename,double leftup_lon,double leftup_lat)
{
    // 对经纬度向下取整
    int lat = (int)floor(leftup_lat);
    int lon = (int)floor(leftup_lon);

    // 判断方向并生成文件名部分
    char lat_dir, lon_dir;
    if (lat >= 0) {
        lat_dir = 'N';
    } else {
        lat_dir = 'S';
        lat = -lat; // 转换为正值以便格式化
    }
    if (lon >= 0) {
        lon_dir = 'E';
    } else {
        lon_dir = 'W';
        lon = -lon; // 转换为正值以便格式化
    }
    // 格式化输出文件名
    // 使用 snprintf 来安全地格式化字符串
    snprintf(filename, 100, "%c%02d%c%03d", lat_dir, lat, lon_dir, lon);
}

int getTileStartId(GNCDB *db,char *filename,int *tileStartId,const char *coverage_sec)
{
    int rc = 0;
    char *sql = NULL;
    char *temp_sql = NULL;
    char *temp_sql1 = NULL;
    temp_sql = "SELECT tile_startid from \"%s\""
    " WHERE section_name = '\"%s\"'";
    temp_sql1 = replace_nth_placeholder(temp_sql,coverage_sec,1);
    sql = replace_nth_placeholder(temp_sql1,filename,1);
    rc = GNCDB_exec(db,sql,getMaxcallback,tileStartId,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return rc;
}

int Line_query_get_points(char *querypoints,rt_geo_extent tile_bound,double start_longitude,double start_latitude,double end_longitude,double end_latitude,rt_resolution resolution,int *width,int *height,int step)
{
    int row = 0;
    int col = 0;
    int startRow = 0;
    int startCol = 0;
    int endRow = 0;
    int endCol = 0;
    double left_lon = 0.0;
    double left_lat = 0.0;
    double right_lon = 0.0;
    double right_lat = 0.0;
    char *p_result = querypoints;
    int direction = 0;
    double k = 0.0,b = 0.0;
    double startrow_offset = 0.0;
    double startcol_offset = 0.0;
    double endrow_offset = 0.0;
    double endcol_offset = 0.0;
    double baseLon = 0.0;
    double baseLat = 0.0;

    int count = 0;
    double xres = resolution->xres;
    double yres = resolution->yres;


    //统一起点为从左到右，direction为从左到右的变化趋势
    direction = reshape(start_longitude,start_latitude,end_longitude,end_latitude,
    &left_lon,&left_lat,&right_lon,&right_lat);
    start_longitude = left_lon;
    start_latitude = left_lat;
    end_longitude = right_lon;
    end_latitude = right_lat;
    //首先加入起点
    p_result = addNewPointAbsolute(left_lon,left_lat,p_result);
    // p_result += QUERY_POINT_SIZE;
    count ++;

    if(step)
    {
        double xOffset = right_lon - left_lon;
        double yOffset = right_lat - left_lat;
        double xStep = xOffset/(step-1);
        double yStep = yOffset/(step-1);
        double lon = left_lon;
        double lat = left_lat;
        for(int i = 0;i < step-2;i++)
        {
            lon += xStep;
            lat += yStep;
            p_result = addNewPointAbsolute(lon,lat,p_result);
        }
        count = step - 1;
    }
    else
    {
        // //计算斜率
        // if ( right_lat - left_lat == 0) {
        //     // 这里可以根据需求做特殊处理，比如设置一个很大的值表示无穷大
        //     k = (-1)*1e9;
        //     b = 0;
        //     printf("注意：直线垂直于 x 轴，斜率无穷大。\n");
        // } else {
        //     k = (right_lat - left_lat) / (right_lon - left_lon);
        //     b = right_lat - k * right_lon;
        // }

        //将起点和终点转换为对应的栅格中心
        convertLatLonToGridCenter(&left_lon,&left_lat,resolution);
        convertLatLonToGridCenter(&right_lon,&right_lat,resolution);

        //4.确定基准坐标，以左上角为基准
        baseLon = left_lon;
        if(direction == -1)
        {
            baseLat = left_lat;
        }
        else
        {
            baseLat = right_lat;
        }

        //坐标系变化，方便后续判断栅格和直线的相交关系
        startrow_offset = getBaseRowOffset(baseLat,start_latitude,yres);
        startcol_offset = getBaseColOffset(baseLon,start_longitude,xres);
        endrow_offset = getBaseRowOffset(baseLat,end_latitude,yres);
        endcol_offset = getBaseColOffset(baseLon,end_longitude,xres);
        
        if (startcol_offset - endcol_offset == 0) {
            // 这里可以根据需求做特殊处理，比如设置一个很大的值表示无穷大
            k = 1e9;
            b = 0;
            printf("注意：直线垂直于 x 轴，斜率无穷大。\n");
        } else {
            k = (endrow_offset - startrow_offset) / (endcol_offset- startcol_offset);
            b = startrow_offset - k * startcol_offset;
        }

        //开始步进，直到到达终点
        startRow = getBaseRow(baseLat,left_lat,yres);
        startCol = getBaseCol(baseLon,left_lon,xres);
        endRow = getBaseRow(baseLat,right_lat,yres);
        endCol = getBaseCol(baseLon,right_lon,xres);
        row = startRow;
        col = startCol;
        while(!(row == endRow && col == endCol))
        {
            int temp_row = row;
            int temp_col = col;

            //往右步进
            temp_col++;
            if(isIntersect(temp_col,row,k,b,xres,yres,baseLon,baseLat))
            {
                col = temp_col;
                //定义新的查询点
                p_result = addNewPointRelative(row,col,baseLon,baseLat,xres,yres,p_result);
                count++;
                continue;
            }

            //右步进失败、上下步进
            if(direction<0)
            {
                //往下步进
                temp_row++;
            }
            else
            {
                //往上步进
                temp_row--;
            }
            if(isIntersect(col,temp_row,k,b,xres,yres,baseLon,baseLat))
            {
                row = temp_row;
                //定义新的查询点
                p_result = addNewPointRelative(row,col,baseLon,baseLat,xres,yres,p_result);
                count++;
                continue;
            }

            //上下步进失败，斜方向步进
            col++;
            if(direction<0)
            {
                //往下步进
                row++;
            }
            else
            {
                //往下步进
                row--;
            }
            p_result = addNewPointRelative(row,col,baseLon,baseLat,xres,yres,p_result);
            count++;
        }
    }

    p_result = addNewPointAbsolute(right_lon,right_lat,p_result);
    count++;
    //计算查询区域
    tile_bound->extent_minx = left_lon;
    tile_bound->extent_maxx = right_lon;
    if(left_lat >= right_lat)
    {
        tile_bound->extent_maxy = left_lat;
        tile_bound->extent_miny = right_lat;
    }
    else
    {
        tile_bound->extent_maxy = right_lat;
        tile_bound->extent_miny = left_lat;
    }
    //计算查询区域的宽高
    *width = round((tile_bound->extent_maxx - tile_bound->extent_minx)/xres)+1;
    *height = round((tile_bound->extent_maxy - tile_bound->extent_miny)/yres)+1;
    return count;
}

int Rectangle_query_get_points(char *querypoints,rt_geo_extent tile_bound,double start_longitude,double start_latitude,double end_longitude,double end_latitude,rt_resolution resolution,int *width,int *height,bool isContain)
{

    int startRow = 0;
    int startCol = 0;
    int endRow = 0;
    int endCol = 0;
    double left_lon = 0.0;
    double left_lat = 0.0;
    double right_lon = 0.0;
    double right_lat = 0.0;

    double lon = 0.0;
    double lat = 0.0;
    char *p_result = querypoints;
    double xres = resolution->xres;
    double yres = resolution->yres;
    int count = 0;
    double baseLon,baseLat;


    //统一起点为从左到右，direction为从左到右的变化趋势
    reshape(start_longitude,start_latitude,end_longitude,end_latitude,
    &left_lon,&left_lat,&right_lon,&right_lat);
    baseLon = start_longitude;
    baseLat = end_latitude;

    //将起点和终点转换为对应的栅格中心
    convertLatLonToGridCenter(&left_lon,&left_lat,resolution);
    convertLatLonToGridCenter(&right_lon,&right_lat,resolution);
    
    //左上角坐标
    startRow = getBaseRow(baseLat,left_lat,yres);
    startCol = getBaseCol(baseLon,left_lon,xres);
    endRow = getBaseRow(baseLat,right_lat,yres);
    endCol = getBaseCol(baseLon,right_lon,xres);

    //生成查询点
    if(isContain)
    {
        lat = right_lat;
        for(int i = endRow;i<=startRow;i++)
        {
            lon = left_lon;
            for(int j = startCol;j<=endCol;j++)
            {
                count++;
                p_result = addNewPointAbsolute(lon,lat,p_result);
                lon = lon + xres;
                if(lon>right_lon)
                {
                    lon = right_lon;
                }
            }
            lat = lat - yres;
            if(lat<left_lat)
            {
                lat = left_lat;
            }
        }
    }
    else
    {
        lat = right_lat - yres;
        for(int i = endRow + 1;i<=startRow - 1;i++)
        {
            lon = left_lon + xres;
            for(int j = startCol + 1;j<=endCol - 1;j++)
            {
                count++;
                p_result = addNewPointAbsolute(lon,lat,p_result);
                lon = lon + xres;
            }
            lat = lat - yres;
        }
    }

    //计算查询的区域以及宽高
    tile_bound->extent_minx = left_lon;
    tile_bound->extent_maxx = right_lon;
    if(left_lat >= right_lat)
    {
        tile_bound->extent_maxy = left_lat;
        tile_bound->extent_miny = right_lat;
    }
    else
    {
        tile_bound->extent_maxy = right_lat;
        tile_bound->extent_miny = left_lat;
    }
    *width = round((tile_bound->extent_maxx - tile_bound->extent_minx)/xres) + 1;
    *height = round((tile_bound->extent_maxy - tile_bound->extent_miny)/yres) + 1;
    return count;
}

int Circle_query_get_points(char *querypoints,rt_geo_extent tile_bound,double center_longitude,double center_latitude,double radious,rt_resolution resolution,int *width,int *height,bool isContain)
{
    int count = 0;
    char *p_result = querypoints;
    double top_point_lat = 0.0;
    double bottom_point_lat = 0.0;
    double left_point_lon = 0.0;
    double right_point_lon = 0.0;
    int leftCol = 0;
    int rightCol = 0;
    int topRow = 0;
    int bottomRow = 0;
    double baseLon,baseLat;
    double xres = resolution->xres;
    double yres = resolution->yres;

    /*1.计算圆的外接矩形*/
    left_point_lon = center_longitude - radious;
    right_point_lon = center_longitude + radious;
    top_point_lat = center_latitude + radious;
    bottom_point_lat = center_latitude - radious;
    baseLon = left_point_lon;
    baseLat = top_point_lat;
    
    /*2.计算相对于左上角的偏移*/
    leftCol = getBaseCol(baseLon,left_point_lon,xres);
    rightCol = getBaseCol(baseLon,right_point_lon,xres);
    topRow = getBaseRow(baseLat,top_point_lat,yres);
    bottomRow = getBaseRow(baseLat,bottom_point_lat,yres);

    /*3.计算查询点*/
    for(int row = topRow;row <= bottomRow;row++)
    {
        for(int col = leftCol;col <= rightCol;col++)
        {
            if(isContain)
            {
                if(isTntersectCircle(getBaseLon(col,baseLon,xres),getBaseLat
                (row,baseLat,yres),xres,yres,center_longitude,center_latitude,radious))
                {
                    p_result = addNewPointRelative(row,col,baseLon,baseLat,xres,yres,p_result);
                    count++;
                }
            }
            else
            {
                if(isInCircle(getBaseLon(col,baseLon,xres),getBaseLat
                (row,baseLat,yres),xres,yres,center_longitude,center_latitude,radious))
                {
                    p_result = addNewPointRelative(row,col,baseLon,baseLat,xres,yres,p_result);
                    count++;
                }
            }
        }
    }

    /*4.计算查询区域*/
    tile_bound->extent_minx = left_point_lon;
    tile_bound->extent_maxx = right_point_lon;
    tile_bound->extent_miny = bottom_point_lat;
    tile_bound->extent_maxy = top_point_lat;
    *width = rightCol - leftCol + 1;
    *height = bottomRow - topRow + 1;
    return count;
}

int reshape(double start_longitude,double start_latitude,double end_longitude,double end_latitude,
double *left_lon,double *left_lat,double *right_lon,double *right_lat)
{
    if(start_longitude < end_longitude)
    {
        *left_lon = start_longitude;
        *left_lat = start_latitude;
        *right_lon = end_longitude;
        *right_lat = end_latitude;
    }
    else if(start_longitude > end_longitude)
    {
        *left_lon = end_longitude;
        *left_lat = end_latitude;
        *right_lon = start_longitude;
        *right_lat = start_latitude;
    }
    else
    {
        //斜率不存在，规定上面的点为左端点
        if(start_latitude > end_latitude)
        {
            *left_lon = start_longitude;
            *left_lat = start_latitude;
            *right_lon = end_longitude;
            *right_lat = end_latitude;
        }
        else if(start_latitude < end_latitude)
        {
            *left_lon = end_longitude;
            *left_lat = end_latitude;
            *right_lon = start_longitude;
            *right_lat = start_latitude;
        }
        else
        {
            printf("This is not a valid line\n");
            return -1;
        }
    }
    //若是起点的纬度大于终点，返回1
    if((*left_lat) > (*right_lat))
    {
        return -1;
    }
    else if((*left_lat) > (*right_lat))
    {
        return 0;
    }
    return 1;
}

int getRow(double lat,double yres)
{
    double lats = 0.0;
    double upperBound = 0.0;
    double rowDouble = 0.0;
    int row = 0;
    // 对 lat 向上取整得到 lats
    lats = ceil(lat);
    // 计算上界
    upperBound = lats + yres * 0.5;
    // upperBound = lats;
    // 计算 row 的浮点数表示
    rowDouble = (upperBound - lat) / yres;
    // 将结果转换为整数类型，并向上取整
    row = (int)ceil(rowDouble);
    // row = round(rowDouble);
    return row;
}

int getCol(double lon,double xres)
{
    double lats = 0.0;        // 对 lat 向上取整后的值
    double upperBound = 0.0;  // 上界值
    double rowDouble = 0.0;   // 行号的浮点数表示
    int row = 0;              // 最终的行号

    // 对 lat 向上取整得到 lats
    lats = floor(lon);

    // 计算上界
    upperBound = lats - xres * 0.5;
    // upperBound = lats;

    // 计算 row 的浮点数表示
    rowDouble = (lon - upperBound) / xres;

    // 将结果转换为整数类型，并向上取整
    row = (int)ceil(rowDouble);
    // row = round(rowDouble);

    return row;
}

int getBaseRow(double baseLat,double lat,double yres)
{
    return ceil((fabs(baseLat+0.5*yres - lat))/yres);
}

int getBaseCol(double baseLon,double lon,double xres)
{
    return ceil((fabs(baseLon-0.5*xres - lon))/xres);
}

double getBaseLon(int x,double baseLon,double xres)
{
    return baseLon + (x - 1) * xres;
}

double getBaseLat(int y,double baseLat,double yres)
{
    return baseLat - (y - 1) * yres;
}


int isIntersect(int x,int y,double k,double b,double w,double h,double baseLon,double baseLat)
{
    // 计算矩形的四个顶点坐标
    // double cenx = getBaseLon(x,baseLon,w);
    // double ceny = getBaseLat(y,baseLat,h);
    // double left = cenx - 0.5*w;
    // double right = cenx + 0.5*w;
    // double top =  ceny + 0.5*h;
    // double bottom = ceny - 0.5*h;

    double left = (x-1)*w;
    double right = x*w;
    double top =  (y-1)*h;
    double bottom = y*h;

    
    // 判断直线与矩形的四条边是否相交
    // 上边界
    if (isLineSegmentIntersect(left, top, right, top, k, b)) {
        return 1;
    }
    // 下边界
    if (isLineSegmentIntersect(left, bottom, right, bottom, k, b)) {
        return 1;
    }
    // 左边界
    if (isLineSegmentIntersect(left, bottom, left, top, k, b)) {
        return 1;
    }
    // 右边界
    if (isLineSegmentIntersect(right, bottom, right, top, k, b)) {
        return 1;
    }
    // 如果直线与四条边都不相交，则不相交
    return 0;
}

bool isLineSegmentIntersect(double x1, double y1, double x2, double y2, double k, double b) {
    // 计算线段两个端点在直线上的函数值
    double f1 = y1 - (k * x1 + b);
    double f2 = y2 - (k * x2 + b);
    // 如果两个端点的函数值异号，则直线与线段相交
    return (f1 * f2 < 0);
}

bool isTntersectCircle(double x,double y,double w,double h,double cenx,double ceny,double radious)
{
    // 先定义所有需要的变量并初始化为 0
    double left = 0;
    double right = 0;
    double top = 0;
    double bottom = 0;
    double nearestX = 0;
    double nearestY = 0;
    double distance = 0;

    // 计算矩形的边界
    left = x - w / 2;
    right = x + w / 2;
    top = y + h / 2;
    bottom = y - h / 2;

    // 找到矩形上离圆心最近的点
    nearestX = (cenx < left) ? left : ((cenx > right) ? right : cenx);
    nearestY = (ceny < bottom) ? bottom : ((ceny > top) ? top : ceny);

    // 计算最近点到圆心的距离
    distance = sqrt((nearestX - cenx) * (nearestX - cenx) + (nearestY - ceny) * (nearestY - ceny));
    // 判断距离是否小于等于半径
    return distance <= radious;
}

bool isInCircle(double x,double y,double w,double h,double cenx,double ceny,double radious)
{
    // 先定义所有需要的变量并初始化为 0
    double left = 0;
    double right = 0;
    double top = 0;
    double bottom = 0;
    double nearestX = 0;
    double nearestY = 0;
    double distance = 0;

    // 计算矩形的边界
    left = x - w / 2;
    right = x + w / 2;
    top = y + h / 2;
    bottom = y - h / 2;

    // 找到矩形上离圆心最近的点
    nearestX = (cenx > right) ? left : ((cenx < left) ? right : cenx);
    nearestY = (ceny < bottom) ? top : ((ceny > top) ? bottom : ceny);

    // 计算最近点到圆心的距离
    distance = sqrt((nearestX - cenx) * (nearestX - cenx) + (nearestY - ceny) * (nearestY - ceny));
    // 判断距离是否小于等于半径
    return distance <= radious;
}

double getLon(int col,double xres,double left_bound_lon)
{
    return left_bound_lon + (col-1) * xres;
}

double getLat(int row,double yres,double top_bound_lat)
{
    return top_bound_lat - (row-1) * yres;
}

int set_section_startid(GNCDB *db,char *tablename,char *targetColumn,void *value,enum FieldType targeType,char *filterColumn,void *filterValue,enum FieldType filterType)
{
    int rc = 0;
    char *textValue = NULL;
    int *intValue = NULL;
    double *doubleValue = NULL;
    char *sql = NULL;
    char *sql1 = NULL;
    char *sql2 = NULL;
    char *sql3 = NULL;
    QueryContext sqlctx;
    /*1.参数检查*/
    if(db == NULL || tablename == NULL || targetColumn == NULL || filterColumn == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    sql1 = "UPDATE \"%s\" "
          "SET \"%s\" = ? "
          "WHERE \"%s\" = ?;";
    sql2 = replace_nth_placeholder(sql1,tablename,1);
    sql3 = replace_nth_placeholder(sql2,targetColumn,1);
    my_free(sql2);
    sql = replace_nth_placeholder(sql3,filterColumn,1);
    my_free(sql3);
    query_context_init(&sqlctx,sql,512,NULL);
    my_free(sql);
    switch(targeType)
    {
        case FIELDTYPE_INTEGER:
            intValue = (int *)value;
            bind_int(&sqlctx,1,*intValue);
            break;
        case FIELDTYPE_REAL:
            doubleValue = (double *)value;
            bind_float(&sqlctx,1,*doubleValue);
            break;
        case FIELDTYPE_VARCHAR:
            textValue = (char *)value;
            bind_char(&sqlctx,1,textValue);
            break;
        default:
            printf("Invalid type\n");
    }
    switch(filterType)
    {
        case FIELDTYPE_INTEGER:
            intValue = (int *)filterValue;
            bind_int(&sqlctx,2,*intValue);
            break;
        case FIELDTYPE_REAL:
            doubleValue = (double *)filterValue;
            bind_float(&sqlctx,2,*doubleValue);
            break;
        case FIELDTYPE_VARCHAR:
            textValue = (char *)filterValue;
            bind_char(&sqlctx,2,textValue);
            break;
        default:
            printf("Invalid type\n");
    }
    rc  = GNCDB_exec(db,sqlctx.modified_sql,NULL,NULL,NULL);
    query_context_destroy(&sqlctx);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

int insert_tiles(GNCDB *db,int section_id,rt_coverage cvg,rt_priv_raster origin_raster,int tile_w,int tile_h,double xres,
double yres,double minx,double maxx,double miny,double maxy,int width,int height,QueryContext *sqlctx)
{
    int rc = GNCDB_SUCCESS;
    double tile_maxy = 0;
    double tile_minx = 0;
    int max_threads = 1;
    rt_tile *thread_slots = NULL;
    rt_tile cur_tile = NULL;
    int thread_count = 0;
    int tile_num = 0;
    rt_tile first_tile = NULL;
    rt_tileallocator tileallocator = NULL;
    
    /*1.创建导入请求*/
    switch(cvg->pixelType)
    {
        case PIXEL_DEM:
            tileallocator = tileallocatorconstruct(cvg,maxx,miny,tile_w,tile_h,xres,yres,origin_raster,RT_HGT);
            break;
            default:
            tileallocator = tileallocatorconstruct(cvg,maxx,miny,tile_w,tile_h,xres,yres,origin_raster,RT_RGB);
    }
    if(tileallocator == NULL)
    {
        return GNCDB_MEM;
    }

    /*2.将原来的raster分成瓦片，并放进瓦片分配器*/
    tile_maxy = maxy;
    for(int row = 0; row < height; row += tile_h)
    {
        tile_minx = minx;
        for(int col = 0; col < width; col += tile_w)
        {
            addtileintoallocator(tileallocator,row,col,tile_minx,tile_maxy);
            tile_minx += (double) tile_w *xres;
        }
        tile_maxy -= (double) tile_h *yres;
    }
    
    /*3.分配线程槽*/
    thread_slots = my_malloc(sizeof(rt_tile)*max_threads);
    if(thread_slots == NULL)
    {
        my_free(tileallocator);
        return GNCDB_MEM;
    }
    for(thread_count=0; thread_count<max_threads; thread_count++)
    {
        *(thread_slots + thread_count) = NULL;
    }
    thread_count = 0;
    cur_tile = tileallocator->first;
    while(cur_tile != NULL)
    {
        if(tile_num == 0)
        {
            first_tile = cur_tile;
        }
        tile_num++;
        if(max_threads > 1)
        {
            get_tile (cur_tile);
		    *(thread_slots + thread_count) = cur_tile;
		    thread_count++;
            start_tile_thread (cur_tile);
		    if (thread_count == max_threads || cur_tile->next == NULL)
		    {
		      for (thread_count = 0; thread_count < max_threads;
			   thread_count++)
			    {
			    pthread_t *pOpaque;
			    rt_tile pTile =
				*(thread_slots + thread_count);
			    if (pTile == NULL)
				continue;
			    pOpaque = (pthread_t *) (pTile->opaque_thread_id);
			    pthread_join (*pOpaque, NULL);
			    }
		      /* all children threads have now finished: resuming the main thread */
		      for (thread_count = 0; thread_count < max_threads;
			   thread_count++)
			  {
			    rt_tile pTile =
				*(thread_slots + thread_count);
			    if (pTile == NULL)
				continue;
			    if (pTile->retcode != GNCDB_SUCCESS)
                {

                }
			  }
		      thread_count = 0;
		   }
		   else
		   {
		      cur_tile = cur_tile->next;
		      continue;
		   }
        }
        else
        {
            /* single thread execution */
		    get_tile (cur_tile);
		    *(thread_slots + 0) = cur_tile;
		    encode_tile (cur_tile);
		    if (cur_tile->retcode != GNCDB_SUCCESS)
            {
                printf("encode failed\n");
                return 1;
            }
        }

        if(tile_num == NUM)
        {
            if (!insert_some_tile
		        (db,first_tile,section_id,sqlctx,tile_num))
		    {
		        //   pTile->blob_data = NULL;
		    }
            tile_num = 0;
        }
        if(cur_tile->next == NULL && tile_num!=0)
        {
            if (!insert_some_tile
		        (db,first_tile,section_id,sqlctx,tile_num))
		    {
		        //   pTile->blob_data = NULL;
		    }
            tile_num = 0;
        }
        // for (thread_count = 0; thread_count < max_threads; thread_count++)
	    // {
		//     rt_tile pTile = *(thread_slots + thread_count);
		//     if (pTile == NULL)
		//         continue;
		//     if (!insert_single_tile
		//         (db, pTile->blob_data, pTile->blob_data_sz, section_id,origin_raster->srid,
		//          pTile->minx, pTile->miny, pTile->maxx, pTile->maxy,sqlctx))
		//       {
		//           pTile->blob_data = NULL;
		//       }
		//     TileCleanup (pTile);
        //     query_context_reset(sqlctx);
	    // }
	    for (thread_count = 0; thread_count < max_threads; thread_count++)
	      *(thread_slots + thread_count) = NULL;
	    thread_count = 0;
	    cur_tile = cur_tile->next;
    }
    tileallocatordestory (tileallocator);
    my_free (thread_slots);
    thread_slots = NULL;
    return rc;
}

void start_tile_thread(rt_tile tile)
{
    pthread_t thread_id;
    pthread_t *p_thread;
    int ok_prior = 0;
    int policy;
    int min_prio;
    pthread_attr_t attr;
    struct sched_param sp;
    pthread_attr_init (&attr);
    if (pthread_attr_setschedpolicy (&attr, SCHED_RR) == 0)
      {
	  /* attempting to set the lowest priority */
	  if (pthread_attr_getschedpolicy (&attr, &policy) == 0)
	    {
		min_prio = sched_get_priority_min (policy);
		sp.sched_priority = min_prio;
		if (pthread_attr_setschedparam (&attr, &sp) == 0)
		  {
		      /* ok, setting the lowest priority */
		      ok_prior = 1;
		      pthread_create (&thread_id, &attr, doRunImportThread,
				      tile);
		  }
	    }
      }
    if (!ok_prior)
      {
	  /* failure: using standard priority */
	  pthread_create (&thread_id, NULL, doRunImportThread, tile);
      }
    p_thread = malloc (sizeof (pthread_t));
    *p_thread = thread_id;
    tile->opaque_thread_id = p_thread;
}

int insert_single_tile(GNCDB *db,unsigned char *blob_data,int blob_data_sz,
int section_id,int srid,double tile_minx,double tile_miny,double tile_maxx,
double tile_maxy,QueryContext *sqlctx_tils)
{
    int rc = GNCDB_SUCCESS;
    char *tablename = NULL;
    char buffer[20];
    int rows = 0;
    tablename = strcatetablename(get_coveragename(sqlctx_tils),"_tiles");
    query_context_reset(sqlctx_tils);
    rc = GNCDB_insert(db, &rows, tablename,
    sqlctx_tils->last_insert_id,rows,section_id,
    blob_data_sz,tile_minx,tile_maxx,
    tile_miny,tile_maxy,blob_data_sz);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(tablename);
        return rc;
    }
    sprintf(buffer, "%d", sqlctx_tils->last_insert_id);
    rc = GNCDB_setBlob(db,tablename,3,blob_data,blob_data_sz,1,buffer);
    my_free(tablename);
    if(rc != GNCDB_SUCCESS)
    {
        printf("%d\n",rc);
        return rc;
    }
    update_lastinsertid(sqlctx_tils);
    return GNCDB_SUCCESS;
}

int insert_some_tile(GNCDB *db,rt_tile first_tile,int section_id,QueryContext *sqlctx_tils,int tile_num)
{
    int rc = GNCDB_SUCCESS;
    char *tablename = NULL;
    char buffer[tile_num][20];
    int rows = 0;
    rt_tile pTile = first_tile;
    tablename = strcatetablename(get_coveragename(sqlctx_tils),"_tiles");
    query_context_reset(sqlctx_tils);
    for(int i=0;i<tile_num;i++)
    {
        rc = GNCDB_insert(db, &rows, tablename,
        sqlctx_tils->last_insert_id,0,section_id,
        pTile->blob_data_sz,pTile->minx,pTile->maxx,
        pTile->miny,pTile->maxy,pTile->blob_data_sz);
        sprintf(buffer[i], "%d", sqlctx_tils->last_insert_id);
        if(rc != GNCDB_SUCCESS)
        {
            my_free(tablename);
            return rc;
        }
        update_lastinsertid(sqlctx_tils);
        pTile = pTile->next;
    }
    pTile = first_tile;
    for(int i=0;i<tile_num;i++)
    {
        rc = GNCDB_setBlob(db,tablename,3,pTile->blob_data,pTile->blob_data_sz,1,buffer[i]);
        if(rc != GNCDB_SUCCESS)
        {
            printf("%d\n",rc);
            return rc;
        }
        TileCleanup(pTile);
        pTile = pTile->next;
    }
    my_free(tablename);
    return GNCDB_SUCCESS;
}

void encode_tile (rt_tile cur_tile)
{
    int rc = 0;
    rt_tileallocator tileallocator = NULL;
    tileallocator = cur_tile->mother;
    if(cur_tile->raster == NULL)
    {
        printf("The raster of the tile is NULL\n");
    }
    rc = raster_encode(cur_tile->raster,&cur_tile->blob_data,&cur_tile->blob_data_sz,
    tileallocator->quality,1);
    if(rc != GNCDB_SUCCESS)
    {
        printf("the raster encode failed\n");
        cur_tile->retcode = rc;
        return;
    }
    cur_tile->retcode = GNCDB_SUCCESS;
    return;
}

void get_tile (rt_tile cur_tile)
{
    rt_tileallocator tileallocator = NULL;
    tileallocator = cur_tile->mother;
    switch(tileallocator->origin_type)
    {
        case RT_HGT:
            cur_tile->raster = get_tile_from_file(tileallocator->coverage,(rt_priv_raster)tileallocator->origin,tileallocator->origin_type,cur_tile->row,cur_tile->col);
            break;
        case RT_RGB:
            cur_tile->raster = get_tile_from_file(tileallocator->coverage,(rt_priv_raster)tileallocator->origin,tileallocator->origin_type,cur_tile->row,cur_tile->col);
            break;
    }
}

void addtileintoallocator(rt_tileallocator tileact, int row, int col, double tile_minx, double tile_maxy)
{
    rt_tile tile = NULL;
    if(tileact == NULL)
    {
        return;
    }
    tile = (rt_tile)malloc(sizeof(struct rt_tile_t));
    tile->row = row;
    tile->col = col;
    tile->raster = NULL;
    tile->mother = tileact;
    tile->minx = tile_minx;
    tile->maxy = tile_maxy;
    tile->miny = tile_maxy - ((double)tileact->tile_h*tileact->yres);
    tile->maxx = tile_minx + ((double)tileact->tile_w*tileact->xres);
    //去掉超出边界的值
    tile->maxx = tile->maxx > tileact->maxx? tileact->maxx : tile->maxx;
    tile->miny = tile->miny < tileact->miny? tileact->miny : tile->miny;
    tile->next = NULL;
    tile->blob_data = NULL;
    tile->blob_data_sz = 0;
    //将瓦片连接起来
    if (tileact->first == NULL)
	tileact->first = tile;
    if (tileact->last != NULL)
	tileact->last->next = tile;
    tileact->last = tile;
}

rt_tileallocator tileallocatorconstruct(rt_coverage cvg,double maxx,double miny,unsigned int tile_w,
unsigned int tile_h,double res_x,double res_y,rt_priv_raster origin,unsigned char type)
{
    rt_tileallocator tileallocator = NULL;
    tileallocator = (rt_tileallocator)my_malloc(sizeof(struct rt_tileallocator_t));
    if(tileallocator == NULL)
    {
        return NULL;
    }
    tileallocator->coverage = cvg;
    tileallocator->maxx = maxx;
    tileallocator->miny = miny;
    tileallocator->tile_w = tile_w;
    tileallocator->tile_h = tile_h;
    tileallocator->xres = res_x;
    tileallocator->yres = res_y;
    tileallocator->origin = origin;
    tileallocator->origin_type = type;
    tileallocator->first = NULL;
    tileallocator->last = NULL;
    return tileallocator;
}

void tileallocatordestory(rt_tileallocator tileallocator)
{
    rt_tile tile = NULL;
    rt_tile tile_next = NULL;
    if(tileallocator == NULL)
    {
        return;
    }
    tile = tileallocator->first;
    while(tile != NULL)
    {
        tile_next = tile->next;
        tiledestory(tile);
        tile = tile_next;
    }
    my_free(tileallocator);
}

void tiledestory(rt_tile tile)
{
    if(tile == NULL)
    {
        return;
    }
    if(tile->raster != NULL)
    {
        rasterDestory(tile->raster);
    }
    my_free(tile);
}

void rasterDestory(rt_raster raster)
{
    if(raster == NULL)
    {
        return;
    }
    if(raster->rasterBuffer != NULL)
    {
        my_free(raster->rasterBuffer);
        raster->rasterBuffer = NULL;
    }
    if(raster->maskBuffer != NULL)
    {
        my_free(raster->maskBuffer);
        raster->maskBuffer = NULL;
    }
    my_free(raster);
}

void TileCleanup (rt_tile pTile)
{
    if(pTile == NULL)
    {
        return;
    }
    if(pTile->blob_data != NULL)
    {
        my_free(pTile->blob_data);
    }
    rasterDestory(pTile->raster);
    pTile->raster = NULL;
}

int insert_levels(GNCDB *db,double xres,double yres,int pyramid_level,QueryContext *sqlctx)
{
    int rc = 0;
    double x_resolution_1_1 = 0.0;
    double x_resolution_1_2 = 0.0;
    double x_resolution_1_4 = 0.0;
    double x_resolution_1_8 = 0.0;
    double y_resolution_1_1 = 0.0;
    double y_resolution_1_2 = 0.0;
    double y_resolution_1_4 = 0.0;
    double y_resolution_1_8 = 0.0;
    x_resolution_1_1 = xres;
    x_resolution_1_2 = xres * 2.0;
    x_resolution_1_4 = xres * 4.0;
    x_resolution_1_8 = xres * 8.0;
    y_resolution_1_1 = yres;
    y_resolution_1_2 = yres * 2.0;
    y_resolution_1_4 = yres * 4.0;
    y_resolution_1_8 = yres * 8.0;
    query_context_reset(sqlctx);
    bind_int(sqlctx,1,pyramid_level);
    bind_float(sqlctx,2,x_resolution_1_1);
    bind_float(sqlctx,3,y_resolution_1_1);
    bind_float(sqlctx,4,x_resolution_1_2);
    bind_float(sqlctx,5,y_resolution_1_2);
    bind_float(sqlctx,6,x_resolution_1_4);
    bind_float(sqlctx,7,y_resolution_1_4);
    bind_float(sqlctx,8,x_resolution_1_8);
    bind_float(sqlctx,9,y_resolution_1_8);
    rc = GNCDB_exec(db,sqlctx->modified_sql,NULL,NULL,NULL);
    if(rc!=GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

int insert_sections(GNCDB *db,const char *filename,int srid,int width,int height,
    double minx,double miny,double maxx,double maxy,int *section_id,QueryContext *sqlctx)
{
    int rc = 0;

    /*查找section表中最大的sectionid*/
    insert_primaryid(db,get_coveragename(sqlctx),"_sections","section_id",sqlctx);
    query_context_reset(sqlctx);
    *section_id = get_lastinsertid(sqlctx) + 1;
    
    /*绑定参数*/
    bind_int(sqlctx,1,*section_id);
    bind_char(sqlctx,2,filename);
    bind_int(sqlctx,3,width);
    bind_int(sqlctx,4,height);
    bind_float(sqlctx,5,minx);
    bind_float(sqlctx,6,maxx);
    bind_float(sqlctx,7,miny);
    bind_float(sqlctx,8,maxy);
    rc = GNCDB_exec(db,sqlctx->modified_sql,NULL,NULL,NULL);
    if(rc!=GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

char * strcatetablename(const char *coveragename, const char *suffix)
{
    char *tablename = (char *)malloc(strlen(coveragename)+strlen(suffix)+1);
    if(tablename==NULL)
    {
        return NULL;
    }
    strcpy(tablename,coveragename);
    strcat(tablename, suffix);
    return tablename;
}

int get_coverage_fromdb(GNCDB *db,const char *coveragename,rt_coverage *coverage)
{
    char *sql = NULL;
    char *tmp_sql = NULL;
    int rc = GNCDB_SUCCESS;
    QueryResult qr;
    int sample_type = 0;
    int pixel_type = 0;
    int num_bands = 0;
    int tile_width = 0;
    int tile_height = 0;
    double xres = 0.0;
    double yres = 0.0;
    int srid = 0;
    rt_coverage cvg = NULL;
    qr.columnum = 0;
    qr.count = 0;
    qr.offset = 0;
    qr.results = NULL;
    /**/
    tmp_sql= "SELECT sample_type, pixel_type, num_bands,"
    "tile_width, tile_height, srid, xres, yres FROM raster_coverages"
    " WHERE coverage_name = '\"%s\"'";
    sql = replace_nth_placeholder(tmp_sql,coveragename,1);
    rc = GNCDB_exec(db, sql, myCallBacks, &qr, NULL);
    my_free(sql);
    if(rc!=GNCDB_SUCCESS)
    {
        my_free(cvg);
        return rc;
    }
    if(qr.count != qr.columnum)
    {
        printf("ERROR:HAVE NOT ONE COVERAGE");
        my_free(cvg);
        return rc;
    }
    else
    {
        if(!strcmp(qr.results[0],"8_BIT"))
        {
            sample_type = SAMPLE_8BIT;
        }
        else if(!strcmp(qr.results[0],"16_BIT"))
        {
            sample_type = SAMPLE_16BIT;
        }
        if(!strcmp(qr.results[1],"DEM"))
        {
            pixel_type = PIXEL_DEM;
        }
        else if(!strcmp(qr.results[1],"RGB"))
        {
            pixel_type = PIXEL_RGB;
        }
        num_bands = atoi(qr.results[2]);
        tile_width = atoi(qr.results[3]);
        tile_height = atoi(qr.results[4]);
        srid = atoi(qr.results[5]);
        xres = atof(qr.results[6]);
        yres = atof(qr.results[7]);
    }
    /*2.构造并返回coverage*/
    cvg = (rt_coverage)malloc(sizeof(struct rt_coverage_t));
    cvg->coverageName = coveragename;
    cvg->sampleType = sample_type;
    cvg->pixelType = pixel_type;
    cvg->nBands = num_bands;
    cvg->tileWidth = tile_width;
    cvg->tileHeight = tile_height;
    cvg->srid = srid;
    cvg->xres = xres;
    cvg->yres = yres;
    *coverage = cvg;
    return GNCDB_SUCCESS;
}

rt_priv_raster read_raster_from_disk(const char *src_path,unsigned char pixelType,char **filename)
{
    rt_priv_raster raster = NULL;
    switch(pixelType)
    {
        case PIXEL_DEM:
            raster = read_hgt_from_disk(src_path,filename);
            break;
        default:
            raster = read_jpg_from_disk(src_path,filename);
    }
    if(raster == NULL)
    {
        return NULL;
    }
    return raster;
}

rt_priv_raster read_hgt_from_disk(const char *src_path,char **filename)
{
    FILE *file = NULL;
    long file_size = 0;
    long data_points = 0;
    uint16_t width = 0;
    const char *basename = NULL;
    char name[8] = {0};
    char *dot = NULL;
    double sw_lat = 0.0, ipY = 0.0, sw_lon = 0.0, ipX = 0.0;
    rt_priv_raster raster = NULL;
    
    // 打开文件
    file = fopen(src_path, "rb");
    if (!file) return NULL;

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 计算尺寸并验证有效性
    if (file_size % 2 != 0) { // 必须为偶数字节
        fclose(file);
        return NULL;
    }
    data_points = file_size / 2;
    width = (uint16_t)sqrt(data_points);
    if (width * width != data_points) { // 仅支持正方形
        fclose(file);
        return NULL;
    }

    // 解析文件名获取地理信息
    basename = strrchr(src_path, '/');
    if (!basename) basename = strrchr(src_path, '\\');
    basename = basename ? basename + 1 : src_path;

    strncpy(name, basename, 7);
    *filename = malloc(strlen(name) + 1);
    strcpy(*filename, name);
    dot = strchr(name, '.'); // 移除扩展名
    if (dot) *dot = 0;

    if (strlen(name) != 7) { // 验证文件名格式
        fclose(file);
        return NULL;
    }

    // 解析纬度信息
    sw_lat = atoi((char[]){name[1], name[2], 0});
    if (name[0] == 'S') sw_lat = -sw_lat;
    ipY = sw_lat;

    // 解析经度信息
    sw_lon = atoi((char[]){name[4], name[5], name[6], 0});
    if (name[3] == 'W') sw_lon = -sw_lon;
    ipX = sw_lon;

    // 初始化栅格结构
    raster = (rt_priv_raster)malloc(sizeof(struct rt_priv_raster_t));
    if (!raster) {
        fclose(file);
        return NULL;
    }

    // 填充元数据
    raster->numBands = 1;
    raster->xres = 1.0 / (width - 1);
    raster->yres = 1.0 / (width - 1); // 负号表示南向递减
    raster->minX = ipX - (1.0 / (width - 1)) * 0.5;
    raster->maxY = ipY + 1 + (1.0 / (width - 1)) * 0.5;
    raster->minY = ipY - (1.0 / (width - 1)) * 0.5;
    raster->maxX = ipX + 1 + (1.0 / (width - 1)) * 0.5;
    raster->srid = 4326;  // WGS84坐标系
    raster->width = width;
    raster->height = width;
    raster->path = src_path;
    raster->rasterData = NULL;
    fclose(file);
    return raster;
}

rt_priv_raster read_jpg_from_disk(const char *src_path,char **filename)
{
    int width = 0;
    int height = 0;
    double lon = 0.0;
    double lat = 0.0;
    double xres = 0.0;
    double yres = 0.0;
    const char *basename = NULL;
    char name[8] = {0};
    char *dot = NULL;
    rt_priv_raster raster = NULL;
    char *worldFile = NULL;
    struct jpeg_decompress_struct cinfo;
    struct jpeg_error_mgr jerr;
    // JSAMPROW row_pointer[1] = { NULL };
    JSAMPARRAY buffer;
    FILE *fp = NULL;
    unsigned char *data = NULL;
    unsigned char *p_out = NULL;
    int data_size = 0;
      struct timespec start, end;
  double diff;
  clock_gettime(CLOCK_MONOTONIC, &start);
    /* 初始化JPEG对象 */
    cinfo.err = jpeg_std_error(&jerr);
    jpeg_create_decompress(&cinfo);

    /* 打开文件 */
    if ((fp = fopen(src_path, "rb")) == NULL) {
        printf("无法打开文件: %s\n", src_path);
        jpeg_destroy_decompress(&cinfo);
        return NULL;
    }
    jpeg_stdio_src(&cinfo, fp);

    /* 读取头信息 */
    if (jpeg_read_header(&cinfo, TRUE) != JPEG_HEADER_OK) {
        printf("无效的JPEG头\n");
        fclose(fp);
        jpeg_destroy_decompress(&cinfo);
        return NULL;
    }

    /* 设置输出参数为RGB（强制要求） */
    cinfo.out_color_space = JCS_RGB; // 明确指定输出颜色空间
    cinfo.do_fancy_upsampling = FALSE; // 禁用高级采样优化（提速）
    cinfo.do_block_smoothing = FALSE; // 禁用块平滑（提速）

    /* 启动解压缩 */
    if (!jpeg_start_decompress(&cinfo)) {
        printf("启动解压失败\n");
        fclose(fp);
        jpeg_destroy_decompress(&cinfo);
        return NULL;
    }

    width = cinfo.output_width;
    height = cinfo.output_height;

    /* 仅支持RGB */
    if (cinfo.output_components != 3) {
        printf("仅支持RGB三通道图像\n");
        jpeg_finish_decompress(&cinfo);
        jpeg_destroy_decompress(&cinfo);
        fclose(fp);
        return NULL;
    }

    /* 分配目标内存 */
    data_size = height * width * 3;
    data = my_malloc(data_size);
    if (!data) {
        jpeg_finish_decompress(&cinfo);
        jpeg_destroy_decompress(&cinfo);
        fclose(fp);
        return NULL;
    }

    /* 使用libjpeg内部函数分配行缓冲区（关键优化！） */
    buffer = (*cinfo.mem->alloc_sarray)((j_common_ptr)&cinfo, JPOOL_IMAGE, width * 3, 1);
    p_out = data;
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> finish get data: %f secs\n", diff);
    /* 一次性读取所有行（减少错误检查次数） */
    while (cinfo.output_scanline < cinfo.output_height) {
        if (jpeg_read_scanlines(&cinfo, buffer, 1) != 1) {
            break; // 统一在循环外处理错误
        }
        memcpy(p_out, buffer[0], width * 3); // 直接复制到目标内存
        p_out += width * 3;
    }
    clock_gettime(CLOCK_MONOTONIC, &end);
    // 计算时间差
    diff = (end.tv_sec - start.tv_sec) + (end.tv_nsec - start.tv_nsec) / 1e9;
    printf(">> finish get data: %f secs\n", diff);
    /* 统一错误处理 */
    if (cinfo.output_scanline != cinfo.output_height) {
        printf("读取数据不完整\n");
        my_free(data);
        data = NULL;
    }

    /* 清理资源 */
    jpeg_finish_decompress(&cinfo);
    jpeg_destroy_decompress(&cinfo);
    fclose(fp);
    


    /*7.解析文件名获取地理信息*/
    basename = strrchr(src_path, '/');
    if (!basename) basename = strrchr(src_path, '\\');
    basename = basename ? basename + 1 : src_path;
    strncpy(name, basename, 7);
    *filename = malloc(strlen(name) + 1);
    strcpy(*filename, name);
    dot = strchr(name, '.');
    if (dot) *dot = 0;
    
    /*8.获取坐标和分辨率信息*/
    getJgwFilePath(src_path,&worldFile);
    if(!isFileExist(worldFile))
    {
        printf("Can not get the image world file\n");
        my_free(filename);
        return NULL;
    }
    if(get_jpg_location(worldFile,&lon,&lat,&xres,&yres))
    {
        printf("Read world file failed\n");
        my_free(filename);
        my_free(worldFile);
        return NULL;
    }
    my_free(worldFile);

    /*9.构建priv_raster*/
    raster = privRasterConstruct();
    if (!raster) {
        my_free(filename);
        return NULL;
    }
    raster->numBands = 3;
    raster->xres = xres;
    raster->yres = yres * (-1); // 负号表示南向递减
    raster->minX = lon;
    raster->maxY = lat;
    raster->minY = lat + height * yres;
    raster->maxX = lon + width * xres;
    raster->srid = 4326;
    raster->width = width;
    raster->height = height;
    raster->path = src_path;

    /*10.读取数据放到raster中*/
    raster->rasterData = rasterConstruct (width, height, SAMPLE_8BIT, RT_RGB, 3,
			   data, data_size, NULL, 0);
    if(raster->rasterData == NULL)
    {
        my_free(filename);
        my_free(raster);
        return NULL;
    }
    return raster;
}

// rt_priv_raster read_jpg_from_disk(const char *src_path,char **filename)
// {

// /* attempting to create a raster section from a JPEG file */
//     int blob_size;
//     unsigned char *blob;
//     rt_raster rst;
// /* attempting to create a raster */
//     if (rl2_blob_from_file (src_path, &blob, &blob_size) != 0)
// 	return NULL;
//     rst = rl2_raster_from_jpeg (blob, blob_size);
//     free (blob);
//     if (rst == NULL)
// 	return NULL;
// }

int is_confident(rt_priv_raster raster_origin,rt_coverage cvg,bool pyramid)
{
    if(raster_origin->srid != cvg->srid )
    {
        printf("The file's srid is not valid\n");
        return GNCDB_PARAM_INVALID;
    }

    if(raster_origin->numBands != cvg->nBands)
    {
        printf("The number of bands in the file is not valid\n");
        return GNCDB_PARAM_INVALID;
    }

    if(!((raster_origin->xres < 1.01 * cvg->xres)&&(raster_origin->xres > 0.99 * cvg->xres)))
    {
        printf("The xres in the file is not valid\n");
        return GNCDB_PARAM_INVALID;
    }

    if(!((raster_origin->yres < 1.01 * cvg->yres)&&(raster_origin->yres > 0.99 * cvg->yres)))
    {
        printf("The yres in the file is not valid\n");
        return GNCDB_PARAM_INVALID;
    }

    if(pyramid && cvg->pixelType == PIXEL_DEM)
    {
        printf("Terraindata can not build pyramid\n");
        return GNCDB_PARAM_INVALID;
    }

    return GNCDB_SUCCESS;
}

int get_file_size(rt_priv_raster origin,double *minx,double *maxx,double *miny,double *maxy,
double *xres,double *yres,int *width,int *height)
{
    if(origin == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    *minx = origin->minX;
    *maxx = origin->maxX;
    *miny = origin->minY;
    *maxy = origin->maxY;
    *xres = origin->yres;
    *yres = origin->yres;
    *width = origin->width;
    *height = origin->height;
    return GNCDB_SUCCESS;
}

void query_context_reset(QueryContext *sqlctx)
{
    strcpy(sqlctx->modified_sql, sqlctx->original_sql);
    sqlctx->total_offset = 0;
}



int insert_primaryid(GNCDB *db,const char *coveragename,const char *table_suffix,const char *filedname,QueryContext *cxt)
{
    int rc = 0;
    char *temp_sql = NULL;
    char *tablename = NULL;
    int max_primaryid = 0;
    char SQL[512];

    tablename = strcatetablename(coveragename,table_suffix);
    temp_sql = "SELECT max(%s) from %s;";
    snprintf(SQL,sizeof(SQL),temp_sql,filedname,tablename);
    my_free(tablename);

    rc = GNCDB_exec(db,SQL,getMaxcallback,&max_primaryid,NULL);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    cxt->last_insert_id = max_primaryid;
    return GNCDB_SUCCESS;
}

void update_lastinsertid(QueryContext *ctx)
{
    if(ctx == NULL)
    {
        printf("QueryContext pointer is NULL");
        return;
    }
    ctx->last_insert_id++;
}

int get_lastinsertid(QueryContext *ctx)
{
    if(ctx == NULL)
    {
        printf("QueryContext pointer is NULL");
        return -1;
    }
    return ctx->last_insert_id;
}

char *get_coveragename(QueryContext *ctx)
{
    if(ctx == NULL)
    {
        printf("QueryContext pointer is NULL\n");
        return NULL;
    }
    if(ctx->coveragename == NULL)
    {
        printf("tablename is null\n");
        return NULL;
    }
    return ctx->coveragename;
}

rt_raster get_tile_from_file(rt_coverage cvg,rt_priv_raster priv_raster,unsigned char file_type,int row,int col)
{
    rt_raster raster = NULL;
    unsigned char *pixels = NULL;
    int pixels_sz = 0;
    unsigned char *mask = NULL;
    int mask_size = 0;
    unsigned int unused_width = 0;
    unsigned int unused_height = 0;
    raster = my_malloc(sizeof(struct rt_raster_t));
    if(cvg == NULL || priv_raster == NULL)
    {
        my_free(raster);
        return NULL;
    }
    if(row > priv_raster->height || col>priv_raster->width)
    {
        my_free(raster);
        return NULL;
    }
    switch(file_type)
    {
        case RT_HGT:
                    read_from_hgt(priv_raster,cvg->tileWidth,cvg->tileHeight,cvg->sampleType,
                                  cvg->pixelType,cvg->nBands,row,col,&pixels,&pixels_sz);
                    break;
        case RT_RGB:
                    read_from_rgb(priv_raster,cvg->tileWidth,cvg->tileHeight,cvg->sampleType,
                                  cvg->pixelType,cvg->nBands,row,col,&pixels,&pixels_sz);
                    break;

    }

    if (col + cvg->tileWidth > priv_raster->width)
	unused_width = (col + cvg->tileWidth) - priv_raster->width;
    if (row + cvg->tileHeight > priv_raster->height)
	unused_height = (row + cvg->tileHeight) - priv_raster->height;
    if (unused_width || unused_height)
    {
	  unsigned int shadow_x = cvg->tileWidth - unused_width;
	  unsigned int shadow_y = cvg->tileHeight - unused_height;
	  unsigned int row;
	  mask_size = cvg->tileWidth * cvg->tileHeight;
	  mask = malloc (mask_size);
	  if (mask == NULL)
      {
        return NULL;
      }
	  /* full Transparent mask */
	  memset (mask, 0, cvg->tileWidth * cvg->tileHeight);
	  for (row = 0; row < cvg->tileHeight; row++)
	    {
		unsigned char *p = mask + (row * cvg->tileWidth);
		if (row < shadow_y)
		  {
		      /* setting opaque pixels */
		      memset (p, 1, shadow_x);
		  }
	    }
    }
    raster = rasterConstruct (cvg->tileWidth, cvg->tileHeight,
			   cvg->sampleType, cvg->pixelType,
			   cvg->nBands, pixels, pixels_sz, mask,
			   mask_size);
    if (raster == NULL)
    {
        printf("create tile raster failed\n");
        return NULL;
    }
    return raster;
}

int read_from_hgt(rt_priv_raster priv_raster,int tile_width,int tile_height,
unsigned char sample_type,unsigned char pixel_type, unsigned char num_bands,
unsigned int startRow, unsigned int startCol,unsigned char **pixels, int *pixels_sz)
{
    int rc = 0;
    unsigned char *pixel_buffer = NULL;
    int pixelbuffersize = 0;
    int pixel_size = 0;
    //根据数据的类型，确定像素点占的位数
    switch(sample_type)
    {
        case SAMPLE_16BIT:
            pixel_size = 2;
            break;
        case SAMPLE_8BIT:
            pixel_size = 1;
            break;
    }
    pixelbuffersize = tile_width*tile_height*pixel_size*num_bands;
    pixel_buffer = my_malloc(pixelbuffersize);
    if(pixel_buffer == NULL)
    {
        return GNCDB_MEM;
    }
    if ((startRow + tile_height) > priv_raster->height
	|| (startCol + tile_width) > priv_raster->width)
    {
        // init_bound_tile(bufPixels, width, height, sample_type, num_bands,NULL);
    }
    // if (origin->isTiled)
	// 	ret =
	// 	read_raw_tiles (origin, width, height, sample_type,
	// 				num_bands, startRow, startCol,
	// 				bufPixels);
	// else
		rc =
		read_hgt_scanlines (priv_raster, tile_width, tile_height,sample_type, num_bands, startRow,
					    startCol, pixel_buffer);
    //数据在pixelbuffer
    if(rc != GNCDB_SUCCESS)
    {
        my_free(pixel_buffer);
    }
    *pixels = pixel_buffer;
    *pixels_sz = pixelbuffersize;
    return GNCDB_SUCCESS;
}
static int
read_hgt_scanlines (rt_priv_raster priv_raster, unsigned short width,unsigned short height, 
                    unsigned char sample_type,unsigned char numbands,unsigned int startRow,
                    unsigned int startCol, unsigned char *pixels)
{
// 定义所有需要使用的变量
    FILE* fp = NULL;
    uint32_t x, y;
    int16_t *scanline = NULL;
    size_t bytes_read;
    struct stat st;
    int resolution = priv_raster->width;
    const char *path = priv_raster->path;
    int num = 0;

    // 初始化文件指针和扫描线缓冲区
    fp = fopen(path, "rb");
    if (!fp)
    {
        printf("file not valid\n");
        return 1;
    }

    if (stat(path, &st) != 0)
    {
        return 0;
    }

    scanline = malloc(resolution * sizeof(int16_t));
    if (!scanline)
    {
        return GNCDB_SUCCESS;
    }

    // 处理每一行数据
    for (y = 0; y < height; y++) {
        // 计算当前处理的行号，并检查是否超出范围
        uint32_t current_row = startRow + y;
        int16_t *dst = NULL; // 假设pixels指向的是int16_t类型的数据
        if(y==224)
        {
            num++;
        }
        if (current_row >= resolution) break; // 如果超出高度，则退出循环

        // 定位到当前行的起始位置
        if (fseek(fp, current_row * resolution * sizeof(int16_t), SEEK_SET) != 0)
        {
            my_free(scanline);
            return 1;
        }

        // 读取一行数据
        bytes_read = fread(scanline, sizeof(int16_t), resolution, fp);
        if (bytes_read != resolution) break; // 如果读取的数据量不对，则退出循环

        // 字节序转换 (big-endian to native)
        for (x = 0; x < resolution; x++) {
            scanline[x] = be16toh(scanline[x]);
        }

        // 复制目标列数据
        dst = (int16_t *)(pixels + y * width * sizeof(int16_t)); // 假设pixels指向的是int16_t类型的数据
        for (x = startCol; x < startCol + width && x < resolution; x++) { // 确保不越界
            *dst++ = scanline[x];
        }
    }
    free(scanline);
    fclose(fp);
    return GNCDB_SUCCESS;
}

int read_from_rgb(rt_priv_raster priv_raster,int tile_width,int tile_height,
unsigned char sample_type,unsigned char pixel_type, unsigned char num_bands,
unsigned int startRow, unsigned int startCol,unsigned char **pixels, int *pixels_sz)
{
    int rc = 0;
    unsigned char *pixel_buffer = NULL;
    int pixelBufferSize = 0;
    int pixel_size = 0;

    /*1.申请缓冲区*/
    switch(sample_type)
    {
        case SAMPLE_16BIT:
            pixel_size = 2;
            break;
        case SAMPLE_8BIT:
            pixel_size = 1;
            break;
    }
    pixelBufferSize = tile_width * tile_height * pixel_size * num_bands;
    pixel_buffer = my_malloc(pixelBufferSize);
    if(pixel_buffer == NULL)
    {
        return GNCDB_MEM;
    }

    /*2.从源数据中读对应瓦片的数据*/
    if ((startRow + tile_height) > priv_raster->height
	|| (startCol + tile_width) > priv_raster->width)
    {
        // init_bound_tile(bufPixels, width, height, sample_type, num_bands,NULL);
    }
    // if (origin->isTiled)
	// 	ret =
	// 	read_raw_tiles (origin, width, height, sample_type,
	// 				num_bands, startRow, startCol,
	// 				bufPixels);
	// else
		rc =
		read_rgb_scanlines (priv_raster, tile_width, tile_height,sample_type, num_bands, startRow,
					    startCol, pixel_buffer);
    //数据在pixelbuffer
    if(rc != GNCDB_SUCCESS)
    {
        my_free(pixel_buffer);
    }
    *pixels = pixel_buffer;
    *pixels_sz = pixelBufferSize;
    return GNCDB_SUCCESS;
}

static int 
read_rgb_scanlines(rt_priv_raster priv_raster, unsigned short width, unsigned short height,
                   unsigned char sample_type, unsigned char numbands, unsigned int startRow,
                   unsigned int startCol, unsigned char *pixels)
{
    rt_raster raster = priv_raster->rasterData;
    int dataWidth = priv_raster->width;
    int dataHeight = priv_raster->height;
    unsigned char *p_in = NULL;
    unsigned char *p_out = NULL;

    /*1.根据瓦片的位置从raster中读相应的数据到瓦片缓冲区*/
    for (int y = startRow ; y < startRow + height && y < dataHeight; y++) {
        for (int x = startCol; x < startCol + width && x < dataWidth;x++) 
        {
            p_in = raster->rasterBuffer + (y * dataWidth + x) * 3;
            p_out = pixels + ((y - startRow) * width + (x - startCol)) * 3;
            memcpy(p_out, p_in, 3);
        }
    }
    return GNCDB_SUCCESS;
}



rt_raster rasterConstruct (unsigned int tile_w, unsigned int tile_h,
           unsigned char sample_type, unsigned char pixel_type,
		   unsigned char numbands, unsigned char *bufpix,
		   int bufpix_size,unsigned char *mask, int mask_size)
{
    rt_raster raster = NULL;
    raster = my_malloc(sizeof(struct rt_raster_t));
    if (raster == NULL)
    {
        return NULL;
    }
    raster->sampleType = sample_type;
    raster->pixelType = pixel_type;
    raster->nBands = numbands;
    raster->width = tile_w;
    raster->height = tile_h;
    raster->Srid = -1;
    raster->minX = 0.0;
    raster->minY = 0.0;
    raster->maxX = tile_w;
    raster->maxY = tile_h;
    raster->rasterBuffer = bufpix;
    raster->maskBuffer = mask;
    raster->alpha_mask = 0;
    return raster;
}

int raster_encode(rt_raster raster,unsigned char **blob_data,int *blob_data_sz,
    int quality,int little_endian)
{
    int rc = 0;
    unsigned char *pixels = NULL;
    unsigned char *save_mask = NULL;
    unsigned char *mask_pix = NULL;
    unsigned char *compr_data = NULL;
    unsigned char *compr_mask = NULL;
    unsigned char *block_data = NULL;
    unsigned char *ptr = NULL;
    int uncompressed_mask = 0;
    int compressed_mask = 0;
    int uncompressed = 0;
    int compressed;
    int mask_pix_size = 0;
    int row_stride = 0;
    int data_size = 0;
    int data_rows = 0;
    int block_data_size = 0;
    int len = 0;
    if(raster == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    //数据在pixels
    rc = blob_data_rows(raster, &data_rows, &row_stride, &pixels, &data_size, little_endian);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    if (raster->maskBuffer != NULL)
    {
	  /* preparing the mask buffer */
	  save_mask = raster->maskBuffer;
	  raster->maskBuffer = NULL;
	  /* packing RLE data */
	  if (!pack_rle_rows (raster, save_mask, &mask_pix, &mask_pix_size))
	      return 1;
    }
	uncompressed = data_size;
	compressed = data_size;
	compr_data = pixels;
	if (mask_pix == NULL)
	    uncompressed_mask = 0;
	else
	    uncompressed_mask = raster->width * raster->height;
	compressed_mask = mask_pix_size;
	compr_mask = mask_pix;
    block_data_size = 35 + compressed + compressed_mask;
    block_data = malloc (block_data_size);
    if (block_data == NULL)
    {
        return GNCDB_MEM;
    };
    ptr = block_data;
    *ptr++ = 0x00;
    *ptr++ = RT_BLOCK_START;
    if (little_endian)
	*ptr++ = RT_LITTLE_ENDIAN;
    else
	*ptr++ = RT_BIG_ENDIAN;
    *ptr++ = raster->sampleType;
    *ptr++ = raster->pixelType;
    *ptr++ = raster->nBands;
    writeShort(raster->width,(BYTE *)ptr,&len);
    ptr += 2;
    writeShort(raster->height,(BYTE *)ptr,&len);
    ptr += 2;
    writeShort(row_stride,(BYTE *)ptr,&len);
    ptr += 2;
    writeShort(data_rows,(BYTE *)ptr,&len);
    ptr += 2;
    writeInt(uncompressed,(BYTE *)ptr,&len);
    ptr += 4;
    writeInt(compressed,(BYTE *)ptr,&len);
    ptr += 4;
    writeInt(uncompressed_mask,(BYTE *)ptr,&len);
    ptr += 4;
    writeInt(compressed_mask,(BYTE *)ptr,&len);
    ptr += 4;
    *ptr++ = RT_DATA_START;
    memcpy (ptr, compr_data, compressed);
    ptr += compressed;
    *ptr++ = RT_DATA_END;
    *ptr++ = RT_MASK_START;
    if (compr_mask != NULL)
      {
	  memcpy (ptr, compr_mask, compressed_mask);	/* the mask */
	  ptr += compressed_mask;
      }
    *ptr++ = RT_MASK_END;
    *ptr = RT_BLOCK_END;

    if (pixels != NULL)
	free (pixels);

    if (mask_pix != NULL)
	free (mask_pix);

    raster->maskBuffer = save_mask;
    *blob_data = block_data;
    *blob_data_sz = block_data_size;
    return GNCDB_SUCCESS;
}
int blob_data_rows(rt_raster raster,int *data_rows,int *row_stride,unsigned char **pixelbuffer,
int *data_size,int little_endian)
{
    int pixel_size = 0;
    int rows = 0;
    int size = 0;
    int stride = 0;
    int swap = 0;
    u_int16_t *buffer = NULL;
    if(little_endian != isLittleEnsian())
    {
        swap = 1;
    }
    rows = raster->height;
    pixel_size = get_pixelsize(raster->sampleType);
    size = rows * raster->width * pixel_size * raster->nBands;
    stride = raster->width * pixel_size * raster->nBands;
    buffer = my_malloc(size);
    if(buffer == NULL)
    {
        return GNCDB_MEM;
    }
    memset(buffer,0,size);
    switch(raster->sampleType)
    {
        case SAMPLE_8BIT:
            feed_data_uint8(raster->rasterBuffer,raster->width,
            raster->height,raster->nBands,buffer);
            break;
        case SAMPLE_16BIT:
            feed_data_uint16(raster->rasterBuffer,raster->width,
            raster->height,raster->nBands,buffer,swap);
    }
    *data_rows = rows;
    *row_stride = stride;
    *pixelbuffer = buffer;
    *data_size = size;
    return GNCDB_SUCCESS;
}

int get_pixelsize(unsigned char sampleType)
{
    switch(sampleType)
    {
        case SAMPLE_8BIT:
            return 1;
        case SAMPLE_16BIT:
            return 2;
    }
    return 0;
}

void feed_data_uint16(void *rasterbuffer,unsigned int width,
unsigned int height, unsigned int nBands, void *data,int swap)
{
    const short *in = NULL;
    short *out = NULL;
    int row = 0;
    int col = 0;
    in = rasterbuffer;
    out = data;
    for(row = 0; row < height; row++)
    {
        for(col = 0; col < width*nBands; col++)
        {
            if (swap)
		        *out++ = swapUINT16 (*in++);
		    else
		        *out++ = *in++;
        }
    }
}

void feed_data_uint8(void *rasterbuffer,unsigned int width,
unsigned int height, unsigned int nBands, void *data)
{
    char *in = NULL;
    char *out = NULL;
    in = rasterbuffer;
    out = data;
    memcpy(out,in,height * width * nBands);
}

int get_tile_size(rt_coverage cvg,int *tile_width,int *tile_height)
{   
    if(cvg == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    *tile_width = cvg->tileWidth;
    *tile_height = cvg->tileHeight;
    return GNCDB_SUCCESS;
}

void convertLatLonToGridCenter(double *lon,double *lat,rt_resolution resolution)
{
    // 计算基础经纬度（向下取整）
    double base_latitude = (int)(*lat) + 1 + 0.5 * resolution->yres;
    double base_longitude = (int)(*lon) - 0.5 * resolution->xres;

    // 计算偏移量（秒为单位）
    int row = ceil((base_latitude - *lat)/resolution->yres);
    int col = ceil((*lon - base_longitude)/resolution->xres);

    // 根据行列号反推栅格中心点的经纬度
    double center_lat = base_latitude - (row-0.5) * resolution->yres;
    double center_lon = base_longitude + (col-0.5) * resolution->xres;

    // 更新输入参数指向的经纬度值为中心点的经纬度
    *lat = center_lat;
    *lon = center_lon;
}

int customRound(double maxvalue,double comparevalue,double res)
{
    double diff = maxvalue - comparevalue;

    if (fabs(diff) < 0.5 * res)
    {
        return 0;
    }
    else if (diff < 0)
    {
        return (int)floor(diff / res);
    }
    else
    {
        return (int)(diff / res);
    }
}

void write_hex_to_file(const char *data, const char *filename,int len) {
    FILE *file = fopen(filename, "w");
    size_t length = 0;
    if (file == NULL) {
        perror("Error opening file");
        return;
    }

    // 遍历数据，每次处理2个字节
    length = len;
    for (size_t i = 0; i < length; i += 2) {
        // 打印前导零以确保两位数的显示
        fprintf(file, "%02X%02X\n", 
                (unsigned char)data[i], 
                (i + 1 < length) ? (unsigned char)data[i + 1] : 0);
    }

    fclose(file);
}

int write_blob_tofile(const char *data, const char *filename,int len)
{
    // 检查输入参数是否有效
    FILE *file = NULL;
    size_t written = 0;
    if (data == NULL || filename == NULL || len <= 0) {
        fprintf(stderr, "Invalid input parameters.\n");
        return -1;
    }

    // 打开文件以二进制写入模式
    file = fopen(filename, "wb");
    if (file == NULL) {
        perror("Failed to open file");
        return -1;
    }

    // 写入数据到文件
    written = fwrite(data, sizeof(char), len, file);
    if (written != len) {
        perror("Failed to write data to file");
        fclose(file);
        return -1;
    }

    // 关闭文件
    if (fclose(file) != 0) {
        perror("Failed to close file");
        return -1;
    }

    return 0; // 成功完成
}

int getCoverageResolution(GNCDB *db,rt_resolution resolution,const char *coveragename)
{
    int rc = GNCDB_SUCCESS;
    char *sql = NULL;
    char *temp_sql = NULL;
    /*1.参数检查*/
    if(db == NULL || coveragename == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    /*2.构造sql*/
    temp_sql = "SELECT xres,yres,tile_width,tile_height FROM raster_coverages WHERE "
    "coverage_name = '\"%s\"';";
    sql = replace_nth_placeholder(temp_sql,coveragename,1);
    /*3.执行查询，结果在resolution中*/
    rc = GNCDB_exec(db,sql,getResolutionCallBacks,resolution,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

int getCoverageType(GNCDB *db,rt_geo_type coveragetype,const char *coveragename)
{
    int rc = 0;
    char *temp_sql = NULL;
    char *sql = NULL;
    temp_sql = "SELECT sample_type,pixel_type,num_bands "
    "FROM raster_coverages WHERE coverage_name = '\"%s\"';";
    sql = replace_nth_placeholder(temp_sql,coveragename,1);
    rc = GNCDB_exec(db,sql,getTypeCallBacks,coveragetype,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

double getRowOffset(double lat,double yres)
{
    double lats = 0.0;
    double upperBound = 0.0;
    // 对 lat 向上取整得到 lats
    lats = ceil(lat);
    // 计算上界
    upperBound = lats + yres * 0.5;
    return upperBound - lat;
}

double getColOffset(double lon,double xres)
{
    double lons = 0.0;
    double bottomBound = 0.0;
    // 对 lat 向上取整得到 lats
    lons = floor(lon);
    // 计算上界
    bottomBound = lons - xres * 0.5;
    return lon - bottomBound;
}

double getBaseRowOffset(double baseLat,double lat,double yres)
{
    // return ceil((fabs(baseLat - lat))/yres);
    return fabs(baseLat - lat);
}

double getBaseColOffset(double baseLon,double lon,double xres)
{
    // return ceil((fabs(baseLon - lon))/xres);
    return fabs(baseLon - lon);
}

char* extract_basename(const char *src_path) {
    const char *basename = NULL;
    char *filename = NULL;
    char name[8] = {0}; // 用于存储截取后的文件名，假定长度不超过7
    char *dot = NULL;

    // 解析文件名，忽略路径
    basename = strrchr(src_path, '/');
    if (!basename) basename = strrchr(src_path, '\\');
    basename = basename ? basename + 1 : src_path;

    // 截取前7个字符
    strncpy(name, basename, 7);

    // 分配内存并复制name到filename
    filename = (char*)malloc(strlen(name) + 1);
    if (filename != NULL) {
        strcpy(filename, name);

        // 移除可能存在的扩展名
        dot = strchr(filename, '.');
        if (dot) *dot = '\0';
    }

    // 检查文件名格式是否符合预期
    if (strlen(filename) != 7) {
        free(filename);
        return NULL;
    }

    return filename;
}

int updateRasterExtent(GNCDB *db,const char *coveragename)
{
    int rc = 0;
    char *coverage_sec = NULL;
    double extent_minx = 0.0;
    double extent_maxx = 0.0;
    double extent_miny = 0.0;
    double extent_maxy = 0.0;
    char *sql = NULL;
    char *temp_sql = NULL;
    QueryContext sqlctx_rc;
    coverage_sec = strcatetablename(coveragename,"_sections");
    temp_sql = "SELECT MIN(x_min) FROM \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    rc = GNCDB_exec(db,sql,getSingleDoublecallback,&extent_minx,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        return rc;
    }
    temp_sql = "SELECT MIN(y_min) FROM \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    rc = GNCDB_exec(db,sql,getSingleDoublecallback,&extent_miny,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        return rc;
    }
    temp_sql = "SELECT MAX(x_max) FROM \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    rc = GNCDB_exec(db,sql,getSingleDoublecallback,&extent_maxx,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        return rc;
    }
    temp_sql = "SELECT MAX(y_max) FROM \"%s\"";
    sql = replace_nth_placeholder(temp_sql,coverage_sec,1);
    rc = GNCDB_exec(db,sql,getSingleDoublecallback,&extent_maxy,NULL);
    my_free(sql);
    if(rc != GNCDB_SUCCESS)
    {
        my_free(coverage_sec);
        return rc;
    }
    my_free(coverage_sec);
    /*更新写回*/
    sql = "UPDATE raster_coverages SET extent_minx = ?,"
    "extent_maxx = ?, extent_miny = ?, extent_maxy = ? "
    "where \"%s\" = '\"%s\"';";
    temp_sql = replace_nth_placeholder(sql,"coverage_name",1);
    sql = replace_nth_placeholder(temp_sql,coveragename,1);
    my_free(temp_sql);
    query_context_init(&sqlctx_rc,sql,512,coveragename);
    my_free(sql);
    bind_float(&sqlctx_rc,1,extent_minx);
    bind_float(&sqlctx_rc,2,extent_maxx);
    bind_float(&sqlctx_rc,3,extent_miny);
    bind_float(&sqlctx_rc,4,extent_maxy);
    rc = GNCDB_exec(db,sqlctx_rc.modified_sql,NULL,NULL,NULL);
    query_context_destroy(&sqlctx_rc);
    if(rc != GNCDB_SUCCESS)
    {
        printf("Update raster_coverage geo_extent failed %d\n",rc);
        return rc;
    }
    return GNCDB_SUCCESS;
}

void convert_jpg_to_rgb(const char *input_filename, unsigned char **buffer, int *buffer_size)
{
    /* 变量定义部分 */
    struct jpeg_decompress_struct cinfo;
    struct jpeg_error_mgr jerr;
    FILE *infile = NULL;
    int width, height, row_stride;
    JSAMPARRAY temp_buffer;
    unsigned char *output_ptr;

    /* 打开输入文件 */
    infile = fopen(input_filename, "rb");
    if (infile == NULL) {
        fprintf(stderr, "无法打开输入文件: %s\n", input_filename);
        return;
    }

    /* 设置错误处理 */
    cinfo.err = jpeg_std_error(&jerr);
    jpeg_create_decompress(&cinfo);

    /* 指定输入文件 */
    jpeg_stdio_src(&cinfo, infile);

    /* 读取JPEG文件头 */
    (void)jpeg_read_header(&cinfo, TRUE);

    /* 开始解压缩 */
    (void)jpeg_start_decompress(&cinfo);

    /* 获取图像的宽度和高度 */
    width = cinfo.output_width;
    height = cinfo.output_height;
    row_stride = width * cinfo.output_components;

    /* 计算缓冲区大小并分配内存 */
    *buffer_size = row_stride * height;
    *buffer = (unsigned char *)malloc(*buffer_size);
    if (*buffer == NULL) {
        fprintf(stderr, "内存分配失败\n");
        fclose(infile);
        jpeg_destroy_decompress(&cinfo);
        return;
    }

    /* 分配内存来存储一行像素数据 */
    temp_buffer = (*cinfo.mem->alloc_sarray)((j_common_ptr)&cinfo, JPOOL_IMAGE, row_stride, 1);

    /* 逐行读取并写入缓冲区 */
    output_ptr = *buffer;
    while (cinfo.output_scanline < cinfo.output_height) {
        (void)jpeg_read_scanlines(&cinfo, temp_buffer, 1);
        memcpy(output_ptr, temp_buffer[0], row_stride);
        output_ptr += row_stride;
    }

    /* 完成解压缩 */
    (void)jpeg_finish_decompress(&cinfo);

    /* 关闭文件和释放资源 */
    fclose(infile);
    jpeg_destroy_decompress(&cinfo);
}

int read_jpg_dimensions(const char *input_filename, int *width, int *height) 
{
    /* 初始化JPEG解压缩结构体 */
    struct jpeg_decompress_struct cinfo;
    struct jpeg_error_mgr jerr;

    /* 打开输入文件 */
    FILE *infile = fopen(input_filename, "rb");
    if (infile == NULL) {
        fprintf(stderr, "无法打开输入文件: %s\n", input_filename);
        return -1; /* 返回错误码 */
    }

    /* 设置错误处理 */
    cinfo.err = jpeg_std_error(&jerr);
    jpeg_create_decompress(&cinfo);

    /* 指定输入文件 */
    jpeg_stdio_src(&cinfo, infile);

    /* 读取JPEG文件头 */
    jpeg_read_header(&cinfo, TRUE);

    /* 获取图像的宽度和高度 */
    *width = cinfo.image_width;
    *height = cinfo.image_height;

    /* 完成解压缩 */
    jpeg_destroy_decompress(&cinfo);

    /* 关闭文件 */
    fclose(infile);

    return 0; /* 成功返回 */
}

int get_jpg_location(const char *worldFilePath,double *lon,double *lat,double *xres,double *yres)
{
    FILE *file;
    double x_rotation, y_rotation;

    // 尝试打开 JGW 文件
    file = fopen(worldFilePath, "r");
    if (file == NULL) {
        // 如果文件打开失败，输出错误信息并返回 -1
        perror("无法打开 JGW 文件");
        return GNCDB_READ_FAILED;
    }

    // 从文件中读取 6 个参数
    if (fscanf(file, "%lf %lf %lf %lf %lf %lf", xres, &x_rotation, &y_rotation, yres, lon, lat) != 6) {
        // 如果读取参数数量不足 6 个，输出错误信息并返回 -1
        fprintf(stderr, "读取 JGW 文件时出错，参数数量不足\n");
        fclose(file);
        return -1;
    }

    // 关闭文件
    fclose(file);
    return GNCDB_SUCCESS;
}

void section_pyramid_init(rt_section_pyramid sec_pyr, int section_id, int sec_width, int sec_height,
                          unsigned char sampleType, unsigned char pixelType, unsigned char nBands,
                          int srid, double build_xres, double build_yres, double tileWidth,
                          double tileHeight, double minx, double miny, double maxx, double maxy,
                          int scale)
{
    double ext_x = maxx - minx;
    double ext_y = maxy - miny;
    sec_pyr->section_id = section_id;
    sec_pyr->scale = scale;
    sec_pyr->full_width = sec_width;
    sec_pyr->full_height = sec_height;
    sec_pyr->sample_type = sampleType;
    sec_pyr->pixel_type = pixelType;
    sec_pyr->num_samples = nBands;
    sec_pyr->srid = srid;
    sec_pyr->res_x = build_xres;
    sec_pyr->res_y = build_yres;
    sec_pyr->scaled_width = ext_x / build_xres;
    sec_pyr->scaled_height = ext_y / build_yres;
    sec_pyr->tile_width = tileWidth;
    sec_pyr->tile_height = tileHeight;
    sec_pyr->minx = minx;
    sec_pyr->miny = miny;
    sec_pyr->maxx = maxx;
    sec_pyr->maxy = maxy;

}

static int
put_tile_into_pyr (rt_section_pyramid pyr,
                  int tile_id, double minx,
                  double miny, double maxx, double maxy)
{
    /* inserting a base tile into the Pyramid level */
    rt_tile_in tile;
    if (pyr == NULL)
        return 0;

    // 合并 alloc_section_pyramid_tile 函数的实现
    tile = malloc (sizeof (struct rt_tile_in_t));
    if (tile == NULL)
        return 0;
    tile->tile_id = tile_id;
    tile->cx = minx + ((maxx - minx) / 2.0);
    tile->cy = miny + ((maxy - miny) / 2.0);
    tile->next = NULL;

    if (pyr->first_in == NULL)
        pyr->first_in = tile;
    if (pyr->last_in != NULL)
        pyr->last_in->next = tile;
    pyr->last_in = tile;
    return 0;
}

int create_tile_out (rt_section_pyramid sec_pyr, double out_minx, double out_miny, double out_maxx, double out_maxy, int row, int col)
{
    //构建一个tile_out,此时存的是这个区域的瓦片集合
    rt_tile_out tile_out = my_malloc(sizeof(struct rt_tile_out_t));
    rt_tile_in tile_in = sec_pyr->first_in;
    if(tile_out == NULL)
    {
        //todo:mem leak;
        return GNCDB_MEM;
    }
    tile_out->first = NULL;
    tile_out->last = NULL;
    tile_out->row = row;
    tile_out->col = col;
    tile_out->miny = out_miny;
    tile_out->maxy = out_maxy;
    tile_out->minx = out_minx;
    tile_out->maxx = out_maxx;
    tile_out->next = NULL;
    /*遍历in瓦片列表*/
    while(tile_in != NULL)
    {
        //如果瓦片中心在聚合区域内
        if(tile_in->cx > out_minx && tile_in->cx < out_maxx &&
        tile_in->cy > out_miny && tile_in->cy < out_maxy)
        {
            rt_tile_ref tile_ref = my_malloc(sizeof(struct rt_tile_ref_t));
            tile_ref->child = tile_in;
            tile_ref->next = NULL;
            if(tile_out->first == NULL)
            {
                tile_out->first = tile_ref;
            }
            if(tile_out->last != NULL)
            {
                tile_out->last->next = tile_ref;
            }
            tile_out->last = tile_ref;
        }
        tile_in = tile_in->next;
    }
    //放入sec_pyr的tile_out链表中
    if(sec_pyr->first_out == NULL)
    {
        sec_pyr->first_out = tile_out;
    }
    if(sec_pyr->last_out != NULL)
    {
        sec_pyr->last_out->next = tile_out;
    }
    sec_pyr->last_out = tile_out;
    return 0;
}

int insert_higher_level_to_db(GNCDB *db, rt_coverage cvg,rt_priv_raster origin, const char *coveragename,rt_section_pyramid sec_pyr,int pyramid_level, int tileWidth, int tileHeight)
{
    int rc;
    rt_tile_out tile_out = sec_pyr->first_out;
    rt_tile_ref tile_in = NULL;
    rt_rgb_context ctx = NULL;
    unsigned char *buf_in;
    rt_rgb_bitmap base_tile;

    unsigned int x;
    unsigned int y;
    unsigned int row;
    unsigned int col;
    unsigned int tic_x;
    unsigned int tic_y;
    double pos_y;
    double pos_x;
    double geo_x;
    double geo_y;
    unsigned char *rgb = NULL;
    unsigned char *alpha = NULL;

    rt_raster raster = NULL;
    unsigned char *blob_odd;
    int blob_odd_sz;
    unsigned char *p;
    int hald_transparent;

    if (sec_pyr == NULL)
    {
        return GNCDB_PARAMNULL;
    }
    tic_x = tileWidth / sec_pyr->scale;
    tic_y = tileHeight / sec_pyr->scale;
    geo_x = (double) tic_x *sec_pyr->res_x;
    geo_y = (double) tic_y *sec_pyr->res_y;
    while(tile_out != NULL)
    {
        tile_in = tile_out->first;
        ctx = rgb_context_construct(tileWidth,tileHeight);
        if(ctx == NULL)
        {
            return GNCDB_MEM;
        }
        while(tile_in != NULL)
        {
            /* loading and rescaling the base tiles */
		    buf_in =
		    load_tile_from_db (db,cvg,origin,coveragename,pyramid_level,sec_pyr->tile_startid,tile_in->child->tile_id);
		    if (buf_in == NULL)
            {
                printf("Get tile failed\n");
                contextdestory (ctx);
                //todo
                return GNCDB_SUCCESS;
            }
		    base_tile =
		        rl2_graph_create_bitmap (buf_in, tileWidth, tileHeight);
		    if (base_tile == NULL)
		    {
              contextdestory (ctx);
		      my_free (buf_in);
              //todo
              return GNCDB_SUCCESS;
		    }
		    pos_y = tile_out->maxy;
		    x = 0;
		    y = 0;
		    for (row = 0; row < tileHeight; row += tic_y)
		    {
		        pos_x = tile_out->minx;
		        for (col = 0; col < tileWidth; col += tic_x)
			    {
			        if (tile_in->child->cy < pos_y
				    && tile_in->child->cy > (pos_y - geo_y)
				    && tile_in->child->cx > pos_x
				    && tile_in->child->cx < (pos_x + geo_x))
			        {
				        x = col;
				        y = row;
				        break;
			        }
			        pos_x += geo_x;
			    }
		        pos_y -= geo_y;
		    }
		    rl2_graph_draw_rescaled_bitmap (ctx, base_tile,
						1.0 / sec_pyr->scale,
						1.0 / sec_pyr->scale, x, y);
		    rl2_graph_destroy_bitmap (base_tile);
		    tile_in = tile_in->next;
        }
        rgb = rl2_graph_get_context_rgb_array (ctx);
	    if (rgb == NULL)
	    {
            return GNCDB_MEM;
        }
	    alpha = rl2_graph_get_context_alpha_array (ctx, &hald_transparent);
	    if (alpha == NULL)
	    {
            return GNCDB_MEM;
        }
	    p = alpha;
	    for (row = 0; row < tileHeight; row++)
	    {
		    unsigned int x_row = tile_out->row + row;
		    for (col = 0; col < tileWidth; col++)
		    {
		      unsigned int x_col = tile_out->col + col;
		      if (x_row >= sec_pyr->scaled_height
			  || x_col >= sec_pyr->scaled_width)
			    {
			    /* masking any portion of the tile exceeding the scaled section size */
			    *p++ = 0;
			    }
		        else
			    {
			        if (*p == 0)
				    p++;
			        else
				    *p++ = 1;
			    }
		    }
	    }
		raster =
		    rasterConstruct (tileWidth, tileHeight,
				       SAMPLE_8BIT, PIXEL_RGB, 3,
				       rgb, tileWidth * tileHeight * 3,
				       alpha, tileWidth * tileHeight);
	    if (raster == NULL)
	    {
		    fprintf (stderr, "ERROR: unable to create a Pyramid Tile\n");
		    
	    }
	    if (raster_encode
	      (raster, &blob_odd, &blob_odd_sz, 80, isLittleEnsian()) != GNCDB_SUCCESS)
	    {
		    fprintf (stderr, "ERROR: unable to encode a Pyramid tile\n");
		    
	    }
	    rasterDestory (raster);
	    raster = NULL;
	    contextdestory (ctx);
	    ctx = NULL;

	    /* INSERTing the tile */
	    rc = insert_high_level_tiles(db,coveragename,blob_odd, blob_odd_sz,
	       pyramid_level, sec_pyr->section_id, sec_pyr->srid, tile_out->minx,
	       tile_out->miny, tile_out->maxx, tile_out->maxy);
	    if(rc != GNCDB_SUCCESS)
        {
            my_free(blob_odd);
            return rc;
        }
        tile_out = tile_out->next;
    }
    return GNCDB_SUCCESS;
}

rt_rgb_context rgb_context_construct(int tile_width,int tile_height)
{
    rt_rgb_context ctx = my_malloc(sizeof(struct rt_rgb_context_t));
    if(ctx == NULL)
    {
        return NULL;
    }
    ctx->type = RT_SURFACE_IMG;
    ctx->clip_surface = NULL;
    ctx->clip_cairo = NULL;
    ctx->surface =
	cairo_image_surface_create (CAIRO_FORMAT_ARGB32, tile_width, tile_height);
    if (cairo_surface_status (ctx->surface) != CAIRO_STATUS_SUCCESS)
    {
        my_free(ctx);
        return NULL;
    }
    ctx->cairo = cairo_create (ctx->surface);
    if (cairo_status (ctx->cairo) == CAIRO_STATUS_NO_MEMORY)
	{
        my_free(ctx);
        return NULL;
    }
    /* common initialization tasks */

/* setting up a default Black Pen */
    // ctx->current_pen.is_solid_color = 1;
    // ctx->current_pen.is_linear_gradient = 0;
    // ctx->current_pen.is_pattern = 0;
    // ctx->current_pen.red = 0.0;
    // ctx->current_pen.green = 0.0;
    // ctx->current_pen.blue = 0.0;
    // ctx->current_pen.alpha = 1.0;
    // ctx->current_pen.width = 1.0;
    // ctx->current_pen.line_cap = RL2_PEN_CAP_BUTT;
    // ctx->current_pen.line_join = RL2_PEN_JOIN_MITER;
    // ctx->current_pen.dash_array = NULL;
    // ctx->current_pen.dash_count = 0;
    // ctx->current_pen.dash_offset = 0.0;
    // ctx->current_pen.pattern = NULL;

/* setting up a default Black Brush */
    // ctx->current_brush.is_solid_color = 1;
    // ctx->current_brush.is_linear_gradient = 0;
    // ctx->current_brush.is_pattern = 0;
    // ctx->current_brush.red = 0.0;
    // ctx->current_brush.green = 0.0;
    // ctx->current_brush.blue = 0.0;
    // ctx->current_brush.alpha = 1.0;
    // ctx->current_brush.pattern = NULL;

/* setting up default Font options */
    ctx->font_red = 0.0;
    ctx->font_green = 0.0;
    ctx->font_blue = 0.0;
    ctx->font_alpha = 1.0;
    ctx->with_font_halo = 0;
    ctx->halo_radius = 0.0;
    ctx->halo_red = 1.0;
    ctx->halo_green = 1.0;
    ctx->halo_blue = 1.0;
    ctx->halo_alpha = 1.0;

    // ctx->labeling = &(cache->labeling);
    // if (ctx->labeling != NULL)
	// do_cleanup_advanced_labeling (ctx->labeling);

/* priming a transparent background */
    cairo_rectangle (ctx->cairo, 0, 0, tile_width, tile_height);
    cairo_set_source_rgba (ctx->cairo, 0.0, 0.0, 0.0, 0.0);
    cairo_fill (ctx->cairo);
    return ctx;
}

void contextdestory (rt_rgb_context ctx)
{
    if (ctx == NULL)
	return;
    // if (ctx->current_pen.dash_array != NULL)
	// free (ctx->current_pen.dash_array);
    cairo_destroy (ctx->cairo);
    cairo_surface_finish (ctx->surface);
    cairo_surface_destroy (ctx->surface);
    free (ctx);
}
unsigned char *load_tile_from_db (GNCDB *db, rt_coverage cvg,rt_priv_raster origin,const char *coveragename,int pyramid_level,int tile_startid,int tile_id)
{
    int rc = 0;
    char tile_id_tostring[20];
    int blob_sz;
    char *coverage_tiles = NULL;
    unsigned char *blob_data = NULL;
    rt_raster raster = NULL;
    unsigned char *rgba_tile = NULL;
    int rgba_sz;
    int row,col;

    if(pyramid_level == 1)
    {
        row = ((tile_id-tile_startid) / (int)ceil((double)origin->width/cvg->tileWidth)) * cvg->tileHeight;
        col = ((tile_id-tile_startid) % (int)ceil((double)origin->width/cvg->tileWidth)) * cvg->tileWidth;
        raster = get_tile_from_file(cvg,origin,RT_RGB,row,col);
    }
    else
    {
        blob_sz = 197155;
        blob_sz = getBlobSize(db,coveragename,tile_id);
        blob_data = my_malloc(blob_sz);
        if(blob_data == NULL)
        {
            printf("mem lack\n");
            return NULL;
        }
        sprintf(tile_id_tostring, "%d", tile_id);
        coverage_tiles = strcatetablename(coveragename,"_tiles");
        rc = GNCDB_getBlob(db,coverage_tiles,3,blob_data,blob_sz,1,tile_id_tostring);
        if(rc != GNCDB_SUCCESS)
        {
            printf("Get blob failed\n");
            my_free(blob_data);
            return NULL;
        }
        raster = raster_decode(1,blob_data,blob_sz,&rc);
        my_free(coverage_tiles);
    }

    if(raster == NULL)
    {
        if(blob_data != NULL)
        {
            my_free(blob_data);
        }
        return NULL;
    }
    rc = raster_data_to_RGBA (raster, &rgba_tile, &rgba_sz);
    rasterDestory(raster);
    if(rc != GNCDB_SUCCESS)
    {
        if(blob_data != NULL)
        {
            my_free(blob_data);
        }
        return NULL;
    }
    return rgba_tile;
}

rt_rgb_bitmap rl2_graph_create_bitmap (unsigned char *buf_in, int tileWidth, int tileHeight)
{
    /* creating a bitmap */
    rt_rgb_bitmap bmp;

    if (buf_in == NULL)
	return NULL;

    // adjust_for_endianness (buf_in, tileWidth, tileHeight);
    bmp = my_malloc (sizeof (struct rt_rgb_bitmap_t));
    if (bmp == NULL)
	return NULL;
    bmp->width = tileWidth;
    bmp->height = tileHeight;
    bmp->rgba = buf_in;
    bmp->bitmap =
	cairo_image_surface_create_for_data (buf_in, CAIRO_FORMAT_ARGB32,
					     tileWidth, tileHeight, tileWidth * 4);
    bmp->pattern = cairo_pattern_create_for_surface (bmp->bitmap);
    return bmp;
}

void rl2_graph_draw_rescaled_bitmap (rt_rgb_context ctx,rt_rgb_bitmap base_tile,double x_scale,double y_scale,int x,int y)
{
    /* drawing a rescaled bitmap */
    cairo_t *cairo;
    cairo_surface_t *surface;
    rt_rgb_bitmap bmp = base_tile;
    // if (ctx == NULL)
	// return 0;
    // if (bmp == NULL)
	// return 0;
	surface = ctx->surface;
	cairo = ctx->cairo;

    cairo_save (cairo);
    cairo_translate (cairo, x, y);
    cairo_scale (cairo, x_scale, y_scale);
    cairo_set_source (cairo, bmp->pattern);
    cairo_paint (cairo);
    cairo_restore (cairo);
    cairo_surface_flush (surface);
    return ;
}

void rl2_graph_destroy_bitmap (rt_rgb_bitmap base_tile)
{
    /* destroying a bitmap */
    rt_rgb_bitmap bmp = base_tile;

    if (bmp == NULL)
	return;

    cairo_pattern_destroy (bmp->pattern);
    cairo_surface_destroy (bmp->bitmap);
    if (bmp->rgba != NULL)
	my_free (bmp->rgba);
    my_free (bmp);
}

unsigned char *rl2_graph_get_context_rgb_array (rt_rgb_context ctx)
{
/* creating an RGB buffer from the given Context */
    int width;
    int height;
    int x;
    int y;
    unsigned char *p_in;
    unsigned char *p_out;
    unsigned char *rgb;
    int little_endian = isLittleEnsian();

    if (ctx == NULL)
	return NULL;

    width = cairo_image_surface_get_width (ctx->surface);
    height = cairo_image_surface_get_height (ctx->surface);
    rgb = malloc (width * height * 3);
    if (rgb == NULL)
	return NULL;

    p_in = cairo_image_surface_get_data (ctx->surface);
    p_out = rgb;
    for (y = 0; y < height; y++)
      {
	  for (x = 0; x < width; x++)
	    {
		unsigned char r;
		unsigned char g;
		unsigned char b;
		unsigned char a;
		if (little_endian)
		  {
              r = *p_in++;
		      g = *p_in++;
		      b = *p_in++;
		      a = *p_in++;
		  }
		else
		  {
              a = *p_in++;
		      b = *p_in++;
		      g = *p_in++;
		      r = *p_in++;
		  }
		*p_out++ = (a == 0 ? 0 : r);
		*p_out++ = (a == 0 ? 0 : g);
		*p_out++ = (a == 0 ? 0 : b);
	    }
      }
    return rgb;
}

unsigned char *rl2_graph_get_context_alpha_array (rt_rgb_context ctx, int *hald_transparent)
{
/* creating an Alpha buffer from the given Context */
    int width;
    int height;
    int x;
    int y;
    unsigned char *p_in;
    unsigned char *p_out;
    unsigned char *alpha;
    int real_alpha = 0;
    int little_endian =  isLittleEnsian();
    *hald_transparent = 0;

    if (ctx == NULL)
	return NULL;

    width = cairo_image_surface_get_width (ctx->surface);
    height = cairo_image_surface_get_height (ctx->surface);
    alpha = malloc (width * height);
    if (alpha == NULL)
	return NULL;

    p_in = cairo_image_surface_get_data (ctx->surface);
    p_out = alpha;
    for (y = 0; y < height; y++)
      {
	  for (x = 0; x < width; x++)
	    {
		if (little_endian)
		  {
		      p_in += 3;	/* skipping RGB */
		      if (*p_in >= 1 && *p_in <= 254)
			  real_alpha = 1;
		      *p_out++ = *p_in++;
		  }
		else
		  {
		      if (*p_in >= 1 && *p_in <= 254)
			  real_alpha = 1;
		      *p_out++ = *p_in++;
		      p_in += 3;	/* skipping RGB */
		  }
	    }
      }
    if (real_alpha)
	*hald_transparent = 1;
    return alpha;
}

int insert_high_level_tiles(GNCDB *db,const char *coveragename,unsigned char * blob_data, int blob_data_size,
	       int pyr_level, int section_id, int srid, double minx,
	       double miny, double maxx, double maxy)
{
    int rc = 0;
    char *sql = NULL;
    char *temp_sql = NULL;
    char buffer[20];
    char *coverage_tile = NULL;
    QueryContext sqlctx;
    coverage_tile = strcatetablename(coveragename,"_tiles");
    //准备sql
    temp_sql = "INSERT INTO \"%s\" (tile_id,"
    "pyramid_level,section_id,x_min,x_max,y_min,y_max,blob_size) "
    "VALUES (?,?,?,?,?,?,?,?)";
    sql = replace_nth_placeholder(temp_sql,coverage_tile,1);
    query_context_init(&sqlctx,sql,512,coveragename);
    insert_primaryid(db,coveragename,"_tiles","tile_id",&sqlctx);
    my_free(sql);
    bind_int(&sqlctx,1,sqlctx.last_insert_id + 1);
    bind_int(&sqlctx,2,pyr_level);
    bind_int(&sqlctx,3,section_id);
    bind_float(&sqlctx,4,minx);
    bind_float(&sqlctx,5,maxx);
    bind_float(&sqlctx,6,miny);
    bind_float(&sqlctx,7,maxy);
    bind_int(&sqlctx,8,blob_data_size);
    //插入大对象以外的列
    rc = GNCDB_exec(db,sqlctx.modified_sql,NULL,NULL,NULL);
    if(rc != GNCDB_SUCCESS)
    {
        query_context_destroy(&sqlctx);
        my_free(coverage_tile);
        return rc;
    }
    //插入大对象
    sprintf(buffer, "%d", sqlctx.last_insert_id+1);
    rc = GNCDB_setBlob(db,coverage_tile,3,blob_data,blob_data_size,1,buffer);
    my_free(coverage_tile);
    my_free(blob_data);
    query_context_destroy(&sqlctx);
    if(rc != GNCDB_SUCCESS)
    {
        return rc;
    }
    return GNCDB_SUCCESS;
}

int raster_data_to_RGBA (rt_raster raster, unsigned char **buffer,
			 int *buf_size)
{
    const unsigned int width = raster->width;
    const unsigned int height = raster->height;
    const size_t pixel_count = (size_t)width * height;
    const size_t buf_size_bytes = pixel_count * 4;
    unsigned char *buf = my_malloc(buf_size_bytes);
    const unsigned char *in = raster->rasterBuffer;
    const unsigned char *mask = raster->maskBuffer;
    unsigned char *out = buf;
    if (!buf)
        return GNCDB_SPACE_LACK;

    if (mask == NULL) {
        for (size_t i = 0; i < pixel_count; ++i) {
            out[0] = in[0];
            out[1] = in[1];
            out[2] = in[2];
            out[3] = 255;
            in += 3;
            out += 4;
        }
    } else {
        for (size_t i = 0; i < pixel_count; ++i) {
            out[0] = in[0];
            out[1] = in[1];
            out[2] = in[2];
            out[3] = *mask++;
            in += 3;
            out += 4;
        }
    }

    *buffer = buf;
    *buf_size = buf_size_bytes;
    return GNCDB_SUCCESS;
}

void section_pyramid_destory(rt_section_pyramid sec_pyr)
{
    if(sec_pyr == NULL)
    {
        return;
    }
    tile_in_destory(sec_pyr->first_in);
    tile_out_destory(sec_pyr->first_out);
    my_free(sec_pyr);
    sec_pyr = NULL;
}

void tile_in_destory(rt_tile_in tile)
{
    rt_tile_in next = NULL;
    while(tile != NULL)
    {
        next = tile->next;
        my_free(tile);
        tile = next;
    }
}

void tile_out_destory(rt_tile_out tile)
{
    rt_tile_out next = NULL;
    while(tile != NULL)
    {
        next = tile->next;
        my_free(tile);
        tile = next;
    }
}

bool is_unstrict_equal(double first,double second)
{
    if(first < second * 1.01 && first > second * 0.99)
    {
        return true;
    }
    return false;
}

int rebuild_sample(int scale,unsigned int *xwidth, unsigned int *xheight,
		      unsigned char sample_type,unsigned char num_bands,const void *pixels_odd, 
              void **pixels, int *pixels_sz)
{
    unsigned int width = 0;
    unsigned int height = 0;
    // unsigned int row;
    // unsigned int col;
    unsigned char *p_in = (unsigned char *)pixels_odd;
    unsigned char *buf = NULL;
    unsigned char *p_out = NULL;
    int buf_size;
    for(int i=0;i<*xwidth;i += scale)
    {
        width++;
    }
    for(int i=0;i<*xheight;i += scale)
    {
        height++;
    }
    buf_size = width * height * 3 * get_pixelsize(sample_type);
    buf = my_malloc(buf_size);
    p_out = buf;
    if(buf == NULL)
    {
        return GNCDB_SPACE_LACK;
    }
    for(int row = 0;row < *xheight;row += scale)
    {
        for(int col = 0;col < *xwidth;col += scale)
        {
            for(int band = 0;band < num_bands;band++)
            {
                *buf++ = *p_in++;
            }
            p_in += num_bands * (scale - 1);
        }
        p_in += num_bands * (*xwidth) * (scale - 1);
    }
    *xwidth = width;
    *xheight = height;
    *pixels = (void *)p_out;
    *pixels_sz = buf_size;
    return GNCDB_SUCCESS;
}

int raster_to_jpeg (rt_raster raster, unsigned char **jpeg, int *jpeg_size, int quality)
{
    int rc = 0;
    unsigned char *blob;
    int blob_size;

    rc = blob_to_jpeg(raster->rasterBuffer, raster->maskBuffer,
	 raster->width, raster->height, raster->sampleType, raster->pixelType, &blob,
	 &blob_size, quality);
    *jpeg = blob;
    *jpeg_size = blob_size;
    return rc;
}
int jpg_to_file (char *dst_path, unsigned char *blob, int blob_size)
{
    int wr;
    FILE *out;
    if (blob == NULL || blob_size < 1)
	return GNCDB_PARAM_INVALID;
    out = fopen (dst_path, "wb");
    if (out == NULL)
	return GNCDB_FIELD_NOT_EXIST;
    wr = fwrite (blob, 1, blob_size, out);
    fclose (out);
    if (wr != blob_size)
      {
	  /* write error */
      //todo 缺少合适状态码
	  return GNCDB_SUCCESS;
      }
    return GNCDB_SUCCESS;
}

int blob_to_jpeg(const unsigned char *pixels,
			  const unsigned char *mask,
			  unsigned int width, unsigned int height,
			  unsigned char sample_type, unsigned char pixel_type,
			  unsigned char **jpeg, int *jpeg_size, int quality)
{
    int rc = 0;
    unsigned char *blob;
    int blob_size;

    rc = compress_jpeg
	(width, height, sample_type, pixel_type, pixels, mask, &blob,
	 &blob_size, quality);

    *jpeg = blob;
    *jpeg_size = blob_size;
    return rc;
}

static int
compress_jpeg (unsigned short width, unsigned short height,
	       unsigned char sample_type, unsigned char pixel_type,
	       const unsigned char *pixel_buffer,
	       const unsigned char *mask_buffer,
	       unsigned char **jpeg, int *jpeg_size, int quality)
{
/* compressing a JPG image */
    struct jpeg_compress_struct cinfo;
    struct jpeg_error_mgr jerr;
    unsigned char *outbuffer = NULL;
    unsigned long outsize = 0;
    volatile JSAMPROW scanline = NULL;
    JSAMPROW rowptr[1];
    JSAMPROW p_row;
    const char *comment;
    const unsigned char *p_data;
    const unsigned char *p_mask;
    unsigned int row;
    unsigned int col;

    cinfo.err = jpeg_std_error (&jerr);
    jpeg_create_compress (&cinfo);
    jpeg_mem_dest (&cinfo, &outbuffer, &outsize);
    cinfo.image_width = width;
    cinfo.image_height = height;
	cinfo.input_components = 3;
	cinfo.in_color_space = JCS_RGB;
      
    jpeg_set_defaults (&cinfo);
    if (quality > 100)
	quality = 100;
    if (quality < 0)
	quality = 75;
    jpeg_set_quality (&cinfo, quality, 1);
    scanline =
	(JSAMPROW) calloc (1,
			   cinfo.image_width * cinfo.input_components *
			   sizeof (JSAMPLE));
    if (scanline == NULL)
	{
        return GNCDB_SPACE_LACK;
    }
    rowptr[0] = scanline;
    jpeg_start_compress (&cinfo, 1);
    comment = "CREATOR: RasterLite2\n";
    jpeg_write_marker (&cinfo, JPEG_COM, (unsigned char *) comment,
		       (unsigned int) strlen (comment));
    p_data = pixel_buffer;
    p_mask = mask_buffer;

    for (row = 0; row < (int) height; row++)
    {
	  p_row = scanline;
	  for (col = 0; col < (int) width; col++)
	  {
		int transparent = 1;
		if (p_mask != NULL)
		    transparent = *p_mask++;
		transparent = !transparent;
		/* RGB */
		if (transparent)
		{
		    /* transparent pixel - defaulting to WHITE */
		    *p_row++ = 255;
		    *p_row++ = 255;
		    *p_row++ = 255;
		    p_data++;
		    p_data++;
		    p_data++;
		}
		  else
		{
		    /* opaque pixel */
		    *p_row++ = *p_data++;
		    *p_row++ = *p_data++;
		    *p_row++ = *p_data++;
		}
	  }
	  jpeg_write_scanlines (&cinfo, rowptr, 1);
    }
    jpeg_finish_compress (&cinfo);
    jpeg_destroy_compress (&cinfo);
    free (scanline);
    *jpeg = outbuffer;
    *jpeg_size = outsize;
    return GNCDB_SUCCESS;
}

void getJgwFilePath(const char *src_path,char **filePath)
{
    char *dot = NULL;
    char *worldFile = NULL;
    dot = strrchr(src_path,'.');
    if(dot == NULL)
    {
        worldFile = my_malloc(strlen(src_path) + 5);
        snprintf(worldFile, strlen(src_path), "%s.jgw", src_path);
        worldFile[strlen(src_path) + 4] = '\0';
    }
    else
    {
        worldFile = my_malloc(dot - src_path + 5);
        strncpy(worldFile, src_path, dot - src_path);
        strcpy(worldFile + (dot - src_path),".jgw");
    }
    *filePath = worldFile;
}

int write_to_jgw(char *worldFile,double xres,double yres,double minx,double maxy)
{
    double skew_x = 0.0;
    double skew_y = 0.0;
    FILE *fp = fopen(worldFile, "w");
    if (fp == NULL) {
        printf("Can not open thr file: %s\n", worldFile);
        return GNCDB_FILE_NOT_FOUND;
    }
    fprintf(fp, "%.10f\n", xres);
    fprintf(fp, "%.10f\n", skew_x);
    fprintf(fp, "%.10f\n", skew_y);
    fprintf(fp, "%.10f\n", yres);
    fprintf(fp, "%.10f\n", minx);
    fprintf(fp, "%.10f\n", maxy);
    fclose(fp);
    return GNCDB_SUCCESS;
}

QueryResult *queryResultCreate()
{
    QueryResult *qr = my_malloc(sizeof(struct QueryResult));
    qr->columnum = 0;
    qr->count = 0;
    qr->offset = 0;
    qr->results = NULL;
    return qr;
}

void queryResultDestroy(QueryResult *qr)
{
    if(qr == NULL)
    {
        return;
    }
    qr->columnum = 0;
    qr->count = 0;
    qr->offset = 0;
    qr->results = NULL;
    my_free(qr);
    qr = NULL;
}

double getTileBounds(GNCDB *db,const char *coveragename,int tileId,int index)
{
    int rc = 0;
    char *sql = NULL;
    char *temp_sql = NULL;
    char *tablename = strcatetablename(coveragename,"_tiles");
    QueryContext sqlctx;
    rt_geo_extent geo_extent = my_malloc(sizeof(struct rt_geo_extent_t));
    temp_sql = "SELECT x_min,x_max,y_min,y_max FROM \"%s\" WHERE tile_id = ?";
    sql = replace_nth_placeholder(temp_sql,tablename,1);
    query_context_init(&sqlctx,sql,128,coveragename);
    bind_float(&sqlctx,1,tileId);
    rc = GNCDB_exec(db,sqlctx.modified_sql,geoExtentCallBacks,geo_extent,NULL);
    my_free(sql);
    my_free(tablename);
    query_context_destroy(&sqlctx);
    switch(index)
    {
        case 0:
            return geo_extent->extent_minx;
            break;
        case 1:
            return geo_extent->extent_maxx;
            break;
        case 2:
            return geo_extent->extent_miny;
            break;
        case 3:
            return geo_extent->extent_maxy;
            break;
    }
    return 0.0;
}

rt_priv_raster privRasterConstruct()
{
    rt_priv_raster raster = my_malloc(sizeof(struct rt_priv_raster_t));
    if(raster == NULL)
    {
        return NULL;
    }
    raster->path = NULL;
    raster->tmp = NULL;
    raster->width = 0;
    raster->height = 0;
    raster->srid = 0;
    raster->xres = 0;
    raster->yres = 0;
    raster->minX = 0;
    raster->minY = 0;
    raster->maxX = 0;
    raster->maxY = 0;
    raster->noData = 0;
    raster->numBands = 0;
    raster->sample_type = 0;
    raster->rasterData = NULL;
    return raster;
}

void privRasterDestroy(rt_priv_raster priv_raster)
{
    if(priv_raster == NULL)
    {
        return;
    }
    if(priv_raster->rasterData != NULL)
    {
        rasterDestory(priv_raster->rasterData);
        priv_raster->rasterData = NULL;
    }
    my_free(priv_raster);
}

char *addNewPointRelative(int row,int col,double baseLon,double baseLat,double xres,double yres,char *p_result)
{
    double lon = getLon(col,xres,baseLon);
    double lat = getLat(row,yres,baseLat);
    return addNewPointAbsolute(lon,lat,p_result);
}

char *addNewPointAbsolute(double lon,double lat,char *p_result)
{
    memcpy(p_result, &lon, sizeof(double));
    p_result += sizeof(double);
    memcpy(p_result, &lat, sizeof(double));
    p_result += sizeof(double);
    memset(p_result, 0, sizeof(int16_t));
    p_result += sizeof(int16_t);
    return p_result;
}

void queryPoint(double *lon,double *lat,char *p_result,int offset,rt_raster raster,double xres,double yres)
{
    int row,col;
    short queryresult = 0.0;
    readDouble(lon,(BYTE *)p_result,&offset);
    p_result += sizeof(double);
    readDouble(lat,(BYTE *)p_result,&offset);
    p_result += sizeof(double);
    row = round((raster->maxY - *lat)/yres);
    col = round((*lon - raster->minX)/xres);
    offset = (row * raster->width + col) * sizeof(int16_t);
    readShort(&queryresult,raster->rasterBuffer + offset,&offset);
    writeShort(queryresult,(BYTE *)p_result,&offset);
}

bool check_coverage_param(unsigned char sample,
unsigned char pixel,unsigned char num_bands,unsigned short tile_width,
unsigned short tile_height,double xres,double yres)
{
    /*1.检查瓦片尺寸：必须 ≥16 且是 16 的整数倍*/
    if (tile_width < 16 || tile_width % 16 != 0) {
        printf("Tile width invalid: must be >=16 and a multiple of 16\n");
        return false;
    }

    if (tile_height < 16 || tile_height % 16 != 0) {
        printf("Tile height invalid: must be >=16 and a multiple of 16\n");
        return false;
    }

    /*2.分辨率必须大于0*/
    if (xres <= 0 || yres <= 0) {
        printf("Resolution invalid: xres and yres must be greater than 0\n");
        return false;
    }

    /*3.波段数量只能是1或3*/
    if (num_bands != 1 && num_bands != 3) {
        printf("Number of bands invalid: must be 1 or 3\n");
        return false;
    }

    /*4.采样类型检查*/
    if (sample != SAMPLE_8BIT && sample != SAMPLE_16BIT) {
        printf("Sample type invalid: must be SAMPLE_8BIT or SAMPLE_16BIT\n");
        return false;
    }

    /*5.像素类型检查*/
    if (pixel != PIXEL_DEM && pixel != PIXEL_RGB) {
        printf("Pixel type invalid: must be PIXEL_DEM or PIXEL_RGB\n");
        return false;
    }

    return true;
}
