/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-14 10:44:19
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-26 21:37:01
 * @FilePath: /gncdbflr/linux_dev/src/optimizer/optimize_stage.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "optimize_stage.h"
#include "btreepage.h"
#include "btreetable.h"
#include "catalog.h"
#include "condvariable.h"
#include "exec_tuple.h"
#include "index_scan_logical_operator.h"
#include "index_scan_physical_operator.h"
#include "select_stmt.h"
#include "expression.h"
#include "hashmap.h"
#include "join_logical_operator.h"
#include "logical_plan_generator.h"
#include "join_optimizer.h"
#include "lookaside_mem.h"
#include "equality_propagation_rule.h"
#include "gncdbconstant.h"
#include "rewriter.h"
#include "logical_operator.h"
#include "logical_plan_generator.h"
#include "physical_plan_generator.h"
#include "select_stmt.h"
#include "table_scan_physical_operator.h"
#include "typedefine.h"
#include "vararraylist.h"
#include <limits.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>

int OptimizeStageHandleRequest_v2(SQLStageEvent *sqlEvent);

/**
 * @description: 清理算子树下的连接算子，但保留底层的表扫描算子
 * @param {LogicalOperator} *logicalOperator 传入的该参数必须是连接算子类型
 * @return {*}
 */
int clearJoinLogiOper(LogicalOperator *logicalOperator)
{
  int                  rc         = GNCDB_SUCCESS;
  JoinLogicalOperator *joinOper   = NULL;
  LogicalOperator     *childOper1 = NULL;
  LogicalOperator     *childOper2 = NULL;

  if (logicalOperator == NULL) {
    return GNCDB_PARAMNULL;
  }

  joinOper   = (JoinLogicalOperator *)logicalOperator;
  childOper1 = varArrayListGetPointer(joinOper->children, 0);
  childOper2 = varArrayListGetPointer(joinOper->children, 1);
  if (childOper1->type == LO_JOIN) {
    rc = clearJoinLogiOper(childOper1);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  if (childOper2->type == LO_JOIN) {
    rc = clearJoinLogiOper(childOper2);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  JoinLogiOperFullDestroy(joinOper);

  return rc;
}

int ruleBasedOptimization(SQLStageEvent *sqlEvent, LogicalOperator *logicalOperator, bool isSingleTable)
{
  int  rc         = GNCDB_SUCCESS;
  bool changeMade = false;

  /*1.此规则优化只需要开始前执行一次即可，不需要放入LogicalOperatorRewriter递归*/
  if (!isSingleTable) {
    rc = EqualityPropagationRewriter(sqlEvent, logicalOperator);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /*2.重写逻辑算子，主要包括表达式重写、谓词重写、谓词下推*/
  do {
    changeMade = false;
    rc         = LogicalOperatorRewriter(logicalOperator, &changeMade);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } while (changeMade);

  return rc;
}

int costBasedOptimization(SQLStageEvent *sqlEvent, LogicalOperator *logicalOperator)
{
  int                      rc           = GNCDB_SUCCESS;
  BtreeTable              *table        = NULL;
  SelectStmt              *selectStmt   = NULL;
  LogicalOperator         *parentOper   = NULL;
  LogicalOperator         *childOper    = logicalOperator;
  LogicalOperator         *newJoinOper  = NULL;
  JoinLogicalOperator     *joinOper     = NULL;
  TableGetLogicalOperator *tableGetOper = NULL;
  JoinTables              *joinTables   = NULL;
  JoinNode                *joinNode     = NULL;
  HashMap                 *joinedMap    = NULL;
  varArrayList            *newOrders    = NULL;  /*新的连接结点List*/

  // todo 非查询语句不进行代价优化，后续也许可以支持其他类型的语句
  if (sqlEvent->stmt->type != ST_SELECT) {
    return GNCDB_SUCCESS;
  }
  selectStmt = (SelectStmt *)sqlEvent->stmt;
  joinTables = selectStmt->joinTables;

  /*单表查询不进行优化*/
  if (joinTables->tables->elementCount == 1) {
    return GNCDB_SUCCESS;
  }

  /*1.找到第一个连接结点*/
  while (childOper != NULL && childOper->type != LO_JOIN) {
    parentOper = childOper;
    childOper  = varArrayListGetPointer(childOper->children, 0);
  }

  /*没找到连接结点，返回*/
  if (childOper == NULL) {
    return GNCDB_SUCCESS;
  }

  /*2.进行代价优化*/
  newOrders = DoJoinOptimization(joinTables, sqlEvent);

  /*3.根据新的连接顺序替换连接结点，否则维持原样不变*/
  if (newOrders != NULL) {
    /*构建新的连接算子链表*/
    JOIN_LIST_DEEPDESTROY(joinTables->joinNodes);
    joinTables->joinNodes = newOrders;
    joinedMap             = hashMapCreate(STRKEY, 0, NULL);
    for (int i = 0; i < newOrders->elementCount; i++) {
      joinNode = varArrayListGetPointer(newOrders, i);
      rc       = JoinLogiConstruct(&newJoinOper, joinNode, joinedMap, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        LogicalPlanDestroy(&newJoinOper);
        hashMapDestroy(&joinedMap);
        return rc;
      }
    }

    /*4.如果存在未连接的表，手动构造笛卡尔积*/
    if (joinTables->tables->elementCount > joinedMap->entryCount) {
      for (int i = 0; i < joinTables->tables->elementCount; i++) {
        table = varArrayListGetPointer(joinTables->tables, i);
        if (!hashMapExists(joinedMap, table->tableName)) {
          tableGetOper = (TableGetLogicalOperator *)hashMapGet(sqlEvent->tabOperMap, table->tableName);
          if (tableGetOper == NULL) {
            TABLE_LOGI_CONSTRUCT(tableGetOper, table);
          }

          joinOper              = (JoinLogicalOperator *)my_malloc0(sizeof(JoinLogicalOperator));
          joinOper->type        = LO_JOIN;
          joinOper->joinType    = JT_NESTED_LOOP;
          joinOper->expressions = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
          joinOper->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
          varArrayListAddPointer(joinOper->children, newJoinOper);
          varArrayListAddPointer(joinOper->children, tableGetOper);
          newJoinOper = (LogicalOperator *)joinOper;
        }
      }
    }
    hashMapDestroy(&joinedMap);
    
    /* 5.清理并替换原有连接算子 */
    rc = clearJoinLogiOper(childOper);
    varArrayListSetByIndexPointer(parentOper->children, 0, newJoinOper);
  }

  hashMapDestroy(&sqlEvent->tabOperMap);

  return GNCDB_SUCCESS;
}

int costBasedOptimization_v2(SQLStageEvent *sqlEvent, bool *optimizationResult)
{
  int           rc         = GNCDB_SUCCESS;
  SelectStmt   *selectStmt = NULL;
  JoinTables   *joinTables = NULL;
  varArrayList *newOrders  = NULL; /* 新的连接结点List */

  // todo 非查询语句不进行代价优化，后续也许可以支持其他类型的语句
  if (sqlEvent->stmt->type != ST_SELECT) {
    return GNCDB_SUCCESS;
  }
  selectStmt = (SelectStmt *)sqlEvent->stmt;
  joinTables = selectStmt->joinTables;

  /* 单表不进行优化，直接退出 */
  if (joinTables->tables->elementCount == 1) {
    return GNCDB_SUCCESS;
  }

  newOrders = DoJoinOptimization_v2(joinTables, sqlEvent);
  if (newOrders == NULL) {
    *optimizationResult = false;
    return GNCDB_SUCCESS;
  }

  if (joinTables->joinNodes->elementCount > 1) {
    JOIN_LIST_DEEPDESTROY(joinTables->joinNodes);
  }
  joinTables->joinNodes = newOrders;

  *optimizationResult = true;
  return rc;
}

/**
 * @description: 通过索引对查询计划进行优化，目前仅支持使用哈希索引算子替换表扫描算子
 * @param {SQLStageEvent} *sqlEvent
 * @param {LogicalOperator} *logicalOperator
 * @return {*}
 */
int indexOptimization(SQLStageEvent *sqlEvent, LogicalOperator **logicalOperator, bool *changed)
{
  int                       rc            = GNCDB_SUCCESS;
  LogicalOperator          *childOper     = NULL;
  TableGetLogicalOperator  *tableGetOper  = NULL;
  IndexScanLogicalOperator *indexLogiOper = NULL;
  Expression               *expr          = NULL;
  ComparisonExpr           *compExpr      = NULL;
  FieldExpr                *fieldExpr     = NULL;
  ValueExpr                *valueExpr     = NULL;
  Column                   *indexColumn   = NULL;
  HashIndex                *hashIndex     = NULL;
  TableSchema              *tableSchema   = NULL;
  varArrayList             *otherExprs    = NULL;
  varArrayList             *indexExprs    = NULL;
  varArrayList             *indexList     = NULL;  // 存储当前表中存在的所有索引
  HashMap                  *indexMap      = NULL;  // 索引列名到索引结构的hashmap<colName, Index*>
  bool                      canIndexScan  = false;

  // todo 非查询语句不进行索引优化，后续可以支持其他类型的语句，比如：delete和update中的表扫描算子
  if (sqlEvent->stmt->type != ST_SELECT) {
    return GNCDB_SUCCESS;
  }

  //* 1.如果已经是索引扫描算子，直接返回
  if ((*logicalOperator)->type == LO_INDEX_SCAN) {
    return GNCDB_SUCCESS;
  }

  //*2.递归处理直到最下层的表算子
  if ((*logicalOperator)->type != LO_TABLE_GET) {
    for (int i = 0; i < (*logicalOperator)->children->elementCount; i++) {
      childOper = varArrayListGetPointer((*logicalOperator)->children, i);
      rc        = indexOptimization(sqlEvent, &childOper, changed);
      if (*changed) {
        varArrayListSetByIndexPointer((*logicalOperator)->children, i, childOper);
      }
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
    return GNCDB_SUCCESS;
  }

  //* 3.检查表上是否存在可用索引（目前仅有哈希索引）
  otherExprs   = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  indexExprs   = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  tableGetOper = (TableGetLogicalOperator *)(*logicalOperator);
  tableSchema  = getTableSchema(sqlEvent->db->catalog, tableGetOper->table->tableName);
  if (tableSchema == NULL) {
    return GNCDB_INTERNAL;
  }

  //* 3.1.没有条件，无法使用哈希索引
  if (tableGetOper->expressions == NULL || tableGetOper->expressions->elementCount == 0) {
    return GNCDB_SUCCESS;
  }

  //* 3.2.获取索引列表并构造索引map<fieldName, hashIndex>
  indexList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  rc        = catalogGetHashIndexList(sqlEvent->db, indexList, tableGetOper->table->tableName, sqlEvent->txn);
  if (rc != GNCDB_SUCCESS) {
    varArrayListDestroy(&indexList);
    return rc;
  }
  if (indexList->elementCount == 0) {
    varArrayListDestroy(&indexList);
    return GNCDB_SUCCESS;
  }
  indexMap = hashMapCreate(STRKEY, 0, NULL);
  for (int i = 0; i < indexList->elementCount; i++) {
    hashIndex   = varArrayListGetPointer(indexList, i);
    indexColumn = varArrayListGetPointer(hashIndex->index_columns, 0);
    hashMapPut(indexMap, indexColumn->fieldName, hashIndex);
  }

  // todo 如果条件中既可以主键查询也可以索引查询，应优先选择主键查询而不是哈希索引
  //* 3.4.检查各个条件
  for (int i = 0; i < tableGetOper->expressions->elementCount; i++) {
    expr = varArrayListGetPointer(tableGetOper->expressions, i);
    if (expr->type != ETG_COMPARISON) {
      varArrayListAddPointer(otherExprs, expr);
      continue;
    }
    compExpr = (ComparisonExpr *)expr;

    // 非相等表达式：a < 5 / 5 > a 的类型：哈希索引只支持相等条件
    if (compExpr->comp != CMPOP_EQUAL_TO) {
      varArrayListAddPointer(otherExprs, compExpr);
      continue;
    }

    // 非比较表达式：a = 5 / 5 = a 的类型
    if (!(compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_VALUE) &&
        !(compExpr->left->type == ETG_VALUE && compExpr->right->type == ETG_FIELD)) {
      varArrayListAddPointer(otherExprs, compExpr);
      continue;
    }
    // 同时反转比较表达式：5 = a -> a = 5
    if (compExpr->left->type == ETG_VALUE && compExpr->right->type == ETG_FIELD) {
      REVERSE_COMPARISON_EXPR(compExpr);
    }
    fieldExpr = (FieldExpr *)compExpr->left;

    // 当前表达式列上无索引
    if (!hashMapExists(indexMap, fieldExpr->fieldName)) {
      varArrayListAddPointer(otherExprs, compExpr);
      continue;
    }

    // todo 多个索引的处理: id = 1 and num = 3, id 和 num 列上都有索引，目前默认是选择第一个匹配的索引
    // todo 按理来说，应该选择一个哈希冲突少的索引性能更佳，目前计划计算每个桶数量的标准差实现
    if (indexExprs->elementCount == 0) {
      canIndexScan = true;
      hashIndex    = hashMapGet(indexMap, fieldExpr->fieldName);
      varArrayListAddPointer(indexExprs, compExpr);
    }
  }

  //* 4.创建并替换表算子为索引扫描算子
  if (canIndexScan) {
    CREATE_INDEX_SCAN_LOGIOPER(indexLogiOper);
    indexLogiOper->index       = hashIndex;
    indexLogiOper->table       = tableGetOper->table;
    indexLogiOper->indexType   = INDEX_TYPE_HASH;
    indexLogiOper->otherExprs  = otherExprs;
    indexLogiOper->expressions = indexExprs;
    compExpr                   = varArrayListGetPointer(indexExprs, 0);
    valueExpr                  = (ValueExpr *)compExpr->right;
    indexLogiOper->indexVal    = valueExpr->value;
    for (int i = 0; i < tableGetOper->children->elementCount; i++) {
      childOper = varArrayListGetPointer(tableGetOper->children, i);
      varArrayListAddPointer(indexLogiOper->children, childOper);
    }
    *logicalOperator = (LogicalOperator *)indexLogiOper;
    *changed         = true;
    TableGetLogiOperDestroy(tableGetOper);
    return GNCDB_SUCCESS;
  }

  //* 5.不能替换则保持原样，清理资源
  varArrayListDestroy(&otherExprs);
  varArrayListDestroy(&indexExprs);

  return rc;
}

int indexOptimization_v2(SQLStageEvent *sqlEvent)
{
  int                        rc            = GNCDB_SUCCESS;
  Expression                *expr          = NULL;
  ComparisonExpr            *compExpr      = NULL;
  FieldExpr                 *fieldExpr     = NULL;
  ValueExpr                 *valueExpr     = NULL;
  Column                    *indexColumn   = NULL;
  HashIndex                 *hashIndex     = NULL;
  TableSchema               *tableSchema   = NULL;
  varArrayList              *otherExprs    = NULL;
  varArrayList              *indexExprs    = NULL;
  varArrayList              *indexList     = NULL;
  HashMap                   *indexMap      = NULL;
  HashMapIterator           *iter          = NULL;
  TableScanPhysicalOperator *tablePhyOper  = NULL;
  IndexScanPhysicalOperator *indexScanOper = NULL;
  bool                       canIndexScan  = false;

  indexList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  indexMap  = hashMapCreate(STRKEY, 0, NULL);

  /* 遍历每个表扫描算子，检查是否能索引扫描 */
  iter = createHashMapIterator(sqlEvent->tabOperMap);
  while (hasNextHashMapIterator(iter)) {
    otherExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    indexExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);

    iter         = nextHashMapIterator(iter);
    tablePhyOper = (TableScanPhysicalOperator *)iter->entry->value;
    tableSchema  = getTableSchema(sqlEvent->db->catalog, tablePhyOper->table->tableName);
    if (tableSchema == NULL) {
      rc = GNCDB_INTERNAL;
      goto cleanup;
    }

    /* 表上无过滤条件无法使用索引，跳过当前表 */
    if (tablePhyOper->predicates == NULL || tablePhyOper->predicates->elementCount == 0) {
      continue;
    }

    /* 获取表上的所有索引 */
    if (indexList->elementCount > 0) {
      varArrayListClear(indexList);
    }
    rc = catalogGetHashIndexList(sqlEvent->db, indexList, tablePhyOper->table->tableName, sqlEvent->txn);
    if (rc != GNCDB_SUCCESS) {
      goto cleanup;
    }

    /* 无索引，跳过当前表 */
    if (indexList->elementCount == 0) {
      continue;
    }

    /* 构造map<fieldName, hashIndex> */
    if (indexMap->entryCount > 0) {
      hashMapClear(indexMap);
    }
    for (int i = 0; i < indexList->elementCount; i++) {
      hashIndex   = varArrayListGetPointer(indexList, i);
      indexColumn = varArrayListGetPointer(hashIndex->index_columns, 0);
      hashMapPut(indexMap, indexColumn->fieldName, hashIndex);
    }

    // todo 如果一个列上同时有哈希索引和主键，应该优先主键
    /* 遍历当前各个条件 */
    for (int i = 0; i < tablePhyOper->predicates->elementCount; i++) {
      expr = varArrayListGetPointer(tablePhyOper->predicates, i);
      if (expr->type != ETG_COMPARISON) {
        varArrayListAddPointer(otherExprs, expr);
        continue;
      }
      compExpr = (ComparisonExpr *)expr;

      /* 非相等表达式：a < 5 / 5 > a 的类型：哈希索引只支持相等条件 */
      if (compExpr->comp != CMPOP_EQUAL_TO) {
        varArrayListAddPointer(otherExprs, compExpr);
        continue;
      }

      /* 非等值表达式a = 5 / 5 = a 的类型 */
      if (!(compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_VALUE) &&
          !(compExpr->left->type == ETG_VALUE && compExpr->right->type == ETG_FIELD)) {
        varArrayListAddPointer(otherExprs, compExpr);
        continue;
      }

      /* 反转等值表达式：5 = a -> a = 5 */
      if (compExpr->left->type == ETG_VALUE && compExpr->right->type == ETG_FIELD) {
        REVERSE_COMPARISON_EXPR(compExpr);
      }

      /* 当前表达式列上无索引 */
      fieldExpr = (FieldExpr *)compExpr->left;
      valueExpr = (ValueExpr *)compExpr->right;
      if (!hashMapExists(indexMap, fieldExpr->fieldName)) {
        varArrayListAddPointer(otherExprs, compExpr);
        continue;
      }

      // todo 默认选择第一个匹配的索引，应该选择一个哈希冲突少的索引性能更佳，后续计划计算每个桶数量的标准差实现
      if (indexExprs->elementCount == 0) {
        canIndexScan = true;
        hashIndex    = hashMapGet(indexMap, fieldExpr->fieldName);
        varArrayListAddPointer(indexExprs, compExpr);
      } else {
        varArrayListAddPointer(otherExprs, compExpr);
        continue;
      }
    }

    /* 如果能使用索引扫描算子，替换表扫描算子 */
    if (canIndexScan) {
      /* 创建新的索引扫描算子 */
      indexScanOper = (IndexScanPhysicalOperator *)my_malloc0(sizeof(IndexScanPhysicalOperator));
      IndexScanPhysOperInit(indexScanOper);
      indexScanOper->table       = tablePhyOper->table;
      indexScanOper->tableSchema = tableSchema;
      indexScanOper->txn         = sqlEvent->txn;
      indexScanOper->indexType   = INDEX_TYPE_HASH;
      indexScanOper->index       = PTR_MOVE((void **)&hashIndex);
      indexScanOper->indexExprs  = PTR_MOVE((void **)&indexExprs);
      indexScanOper->predicates  = PTR_MOVE((void **)&otherExprs);
      indexScanOper->indexVal    = valueExpr->value;
      hashMapPut(sqlEvent->tabOperMap, iter->entry->key, indexScanOper);
      /* 清除旧的表扫描算子的内存 */
      varArrayListDestroy(&(tablePhyOper->predicates));
      tablePhyOper->predicates = NULL;
      TableScanPhysOperDestroy(tablePhyOper);
    }
  }

cleanup:
  freeHashMapIterator(&iter);
  if (indexList != NULL) {
    varArrayListDestroy(&indexList);
  }
  if (indexMap != NULL) {
    hashMapDestroy(&indexMap);
  }
  if (otherExprs != NULL) {
    varArrayListDestroy(&otherExprs);
  }
  if (indexExprs != NULL) {
    varArrayListDestroy(&indexExprs);
  }

  return rc;
}

int OptimizeStageHandleRequest(SQLStageEvent *sqlEvent)
{
  /*1.生成逻辑计划*/
  int               rc               = GNCDB_SUCCESS;
  LogicalOperator  *logicalOperator  = NULL;
  PhysicalOperator *physicalOperator = NULL;
  bool              isSingleTable    = false;
  bool              changed          = false;

#ifdef ENABLE_JOIN_OPTIMIZATION
#ifdef SELECT_PLAN_BYPASS
  if (sqlEvent->stmt->type == ST_SELECT) {
    return OptimizeStageHandleRequest_v2(sqlEvent);
  }
#endif
#endif

  isSingleTable =
      sqlEvent->stmt->type == ST_SELECT && ((SelectStmt *)sqlEvent->stmt)->joinTables->tables->elementCount == 1;

  if (sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*1.根据stmt构建逻辑算子树*/
  rc = logicalPlanConstruct(sqlEvent, sqlEvent->stmt, &logicalOperator);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  sqlEvent->logicalPlan = logicalOperator;
  if (logicalOperator != NULL) {

#ifdef PINTF_OPERATOR_TREE
    LogicalOperatorToString(logicalOperator, 0);
#endif

#ifdef ENABLE_JOIN_OPTIMIZATION
    /*2.重写逻辑计划*/
    /*2.1.基于规则的优化*/
    rc = ruleBasedOptimization(sqlEvent, logicalOperator, isSingleTable);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /*2.2.基于代价的优化*/
    rc = costBasedOptimization(sqlEvent, logicalOperator);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /*2.3.索引优化*/
    rc = indexOptimization(sqlEvent, &logicalOperator, &changed);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
#endif

    /*3.生成物理计划*/
    rc = physicalPlanConstruct(sqlEvent, logicalOperator, &physicalOperator);

    if (rc != GNCDB_SUCCESS || physicalOperator == NULL) {
      return GNCDB_INTERNAL;
    }
    setParentType((physicalOperator), (physicalOperator)->type);

#ifdef PINTF_OPERATOR_TREE
    PhysicalOperatorToString(physicalOperator, 0);
#endif
  }

  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /*4.设置sql_event的物理计划*/
  sqlEvent->plan = physicalOperator;
  return GNCDB_SUCCESS;
}

/**
 * @description: 为了简单考虑，直接对stmt进行优化（规则+代价），后转换为物理算子，跳过逻辑计划
 * @param {SQLStageEvent} *sqlEvent
 * @return {*}
 */
int OptimizeStageHandleRequest_v2(SQLStageEvent *sqlEvent)
{
  int  rc                 = GNCDB_SUCCESS;
  bool optimizationResult = false;

  /* 等值传播规则优化：首先传播出更多条件*/
  if (((SelectStmt *)sqlEvent->stmt)->joinTables->tables->elementCount > 1) {
    /* 对于多表查询，先进行等值传播 */
    rc = EqualityPropagationRewriter_v3(sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /* 表达式优化：表达式简化+下推 */
  rc = ExpressionRewriter_v2(sqlEvent);

  /* 索引优化：检查能否不走表扫描算子而是替换成索引扫描 */
  rc = indexOptimization_v2(sqlEvent);

#ifdef ENABLE_JOIN_OPTIMIZATION
  /* 代价优化：连接顺序+连接算子选择 */
  rc = costBasedOptimization_v2(sqlEvent, &optimizationResult);
#endif

  /* 构造物理算子：直接根据stmt构造 */
  rc = PhysicalOperConstruct_v2(sqlEvent, &optimizationResult);
  setParentType((sqlEvent->plan), (sqlEvent->plan)->type);

  return rc;
}