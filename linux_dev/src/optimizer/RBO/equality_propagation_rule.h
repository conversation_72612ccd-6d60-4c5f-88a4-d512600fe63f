/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-22 15:35:15
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-12 17:25:33
 * @FilePath: /gncdbflr/linux_dev/src/optimizer/RBO/equality_propagation_rule.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef EQUALITY_PROPAGATION_RULE_H
#define EQUALITY_PROPAGATION_RULE_H

#include "rewriter.h"
#include "sql_event.h"
#include "value.h"
#include "vararraylist.h"
#include <limits.h>
#include <stdbool.h>
typedef struct SQLStageEvent SQLStageEvent;
typedef enum
{
  VAR,
  CONST
} UFNodeType;

typedef struct UinionFindNode
{
  UFNodeType             type;        // 节点类型
  Expression            *expression;  // 节点表达式，Field/Value
  char                  *expeStr;     // 节点表达式的字符串表示，列用表名.列名表示，值用数值表示
  struct UinionFindNode *parent;      // 指向父节点的指针
  int                    rank;        // 秩（用于合并优化）
  bool                   isOriginal;  // 是否是原始节点，比如a = 1, b = 2, a和b都是原始节点，避免重复构造条件
} UFNode;

typedef struct UnionFindSet
{
  UFNode       *root;   // 指向并查集的根节点
  varArrayList *nodes;  // 存储并查集节点的列表
} UFSet;

/**
 * @description: 创建一个新的并查集节点，根据表达式类型设置节点类型，初始化父节点指向自己
 * @param {UFNode *} node: 指向新节点的指针
 * @param {Expression *} expr: 表达式
 * @return {*}
 */
#define UFNODE_CREATE(node, expr)                  \
  do {                                             \
    (node) = (UFNode *)my_malloc0(sizeof(UFNode)); \
    if ((node) == NULL) {                          \
      break;                                       \
    }                                              \
    (node)->isOriginal = false;                    \
    if ((expr)->type == ETG_FIELD) {               \
      (node)->type = VAR;                          \
      (node)->rank = 0;                            \
    } else if ((expr)->type == ETG_VALUE) {        \
      (node)->type = CONST;                        \
      (node)->rank = INT_MAX;                      \
    } else {                                       \
      break;                                       \
    }                                              \
    (node)->expression = (expr);                   \
    (node)->parent     = (node);                   \
  } while (0)

/**
 * @description: 销毁并查集节点，释放内存
 * @param {UFNode *} node: 指向要节点的指针
 * @note: 该宏不会释放node中的表达式，因为后续阶段需要继续使用
 * @note: 该宏会释放exprStr字符串
 * @return {*}
 */
#define UFNODE_DESTROY(node)       \
  do {                             \
    if (node != NULL) {            \
      if (node->expeStr != NULL) { \
        my_free(node->expeStr);    \
        node->expeStr = NULL;      \
      }                            \
      my_free(node);               \
      node = NULL;                 \
    }                              \
  } while (0)

/**
 * @description: 创建一个新的并查集，初始化根节点为NULL，节点列表为空
 * @param {UFSet *} set: 指向新并查集的指针
 * @note: 该宏会将set指针指向一个新的UFSet结构体，并初始化其成员变量
 * @note: 该宏会将set中的nodes初始化为一个新的varArrayList，不设置node的销毁函数
 * @return {*}
 */
#define UFSET_CREATE(set)                                                    \
  do {                                                                       \
    set = (UFSet *)my_malloc0(sizeof(UFSet));                                \
    if (set == NULL) {                                                       \
      break;                                                                 \
    }                                                                        \
    set->root  = NULL;                                                       \
    set->nodes = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
  } while (0)

#define UFSET_DESTROY(set)                  \
  do {                                      \
    if (set != NULL) {                      \
      if (set->nodes != NULL) {             \
        varArrayListDestroy(&(set->nodes)); \
      }                                     \
      my_free(set);                         \
      set = NULL;                           \
    }                                       \
  } while (0)

/**
 * @description: 找到当前元素所在集合中的根节点，并进行路径压缩
 * @param {UFNode*} node
 * @return {*}
 */
UFNode *UFSetFind(UFNode *node);

/**
 * @description: 合并两个并查集
 * @param {UFNode} *node1
 * @param {UFNode} *node2
 * @return {*}
 */
void UFSetUnion(UFNode *node1, UFNode *node2);

/**
 * @description: 判断两个节点是否在同一个集合中
 * @param {UFNode} *node1
 * @param {UFNode} *node2
 * @return {*}
 */
#define UFNODE_IS_CONNECTED(node1, node2) (strcmp(UFSetFind(node1)->expeStr, UFSetFind(node2)->expeStr) == 0)

int EqualityPropagationRewriter(SQLStageEvent *sqlEvent, LogicalOperator *oper);
int EqualityPropagationRewriter_v2(SQLStageEvent *sqlEvent);
int EqualityPropagationRewriter_v3(SQLStageEvent *sqlEvent);
#endif