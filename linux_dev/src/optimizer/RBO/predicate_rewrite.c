#include "predicate_rewrite.h"
/**
 * @brief 重写逻辑操作符，以优化查询执行计划。
 *
 * @param oper 指向当前逻辑操作符的指针。
 * @param changeMade 指向布尔值的指针，如果操作符被重写，该布尔值将被设置为true。
 * @return int 返回操作的结果，成功则返回GNCDB_SUCCESS。
 */
int PredicateRewriter(LogicalOperator *oper, bool *changeMade)
{
  varArrayList    *childOpers      = NULL;
  LogicalOperator *childOper       = NULL;
  varArrayList    *expressions     = NULL;
  Expression      *expr            = NULL;
  ValueExpr       *valueExpr       = NULL;
  bool             boolValue       = false;
  int              i               = 0;
  LogicalOperator *grandChildOper  = NULL;
  varArrayList    *grandChildOpers = NULL;

  childOpers = oper->children;
  /*1.如果子操作符不只一个，则不进行重写*/
  if (childOpers->elementCount != 1) {
    return GNCDB_SUCCESS;
  }
  childOper = varArrayListGetPointer(childOpers, 0);

  /*2.如果子操作符不是谓词类型，则不进行重写*/
  if (childOper->type != LO_PREDICATE) {
    return GNCDB_SUCCESS;
  }

  expressions = childOper->expressions;

  /*3.表达式个数等于0，此时谓词操作符无实际作用，可直接移除，实际的操作是把孙子节点提升为儿子节点*/
  if (expressions->elementCount == 0) {
    grandChildOpers = childOper->children;
    varArrayListRemoveByIndexPointer(childOpers, 0);
    for (i = 0; i < grandChildOpers->elementCount; i++) {
      grandChildOper = varArrayListGetPointer(grandChildOpers, i);
      varArrayListAddPointer(oper->children, grandChildOper);
    }
    varArrayListClear(grandChildOpers);
    LogicalPlanDestroy(&childOper);
    *changeMade = true;
    return GNCDB_SUCCESS;
  }

  /*4.表达式个数大于1的则返回*/
  if (expressions->elementCount != 1) {
    return GNCDB_SUCCESS;
  }

  /*5.表达式个数等于1且必须是bool类型的值时才能进行下一步优化*/
  expr = varArrayListGetPointer(expressions, 0);
  if (expr->type != ETG_VALUE) {
    return GNCDB_SUCCESS;
  }
  valueExpr = (ValueExpr *)expr;
  if (valueExpr->value->attrType != BOOLEANS) {
    return GNCDB_SUCCESS;
  }

  /*6.bool类型的谓词的优化规则
   * 如果仅有的一个子节点是predicate
   * 并且这个子节点可以判断为恒为TRUE，那么可以省略这个子节点，并把他的子节点们（就是孙子节点）接管过来
   * 如果可以判断恒为false，那么就可以删除子节点
   */
  boolValue = valueExpr->value->numValue.boolValue;
  if (boolValue == true) {
    /*6.1恒真，则删除儿子节点，把孙子节点提升到儿子节点*/
    grandChildOpers = childOper->children;
    varArrayListClear(childOpers);
    for (i = 0; i < grandChildOpers->elementCount; i++) {
      grandChildOper = varArrayListGetPointer(grandChildOpers, i);
      varArrayListAddPointer(childOpers, grandChildOper);
    }
    varArrayListClear(grandChildOpers);
    LogicalPlanDestroy(&childOper);
  } else {
    /*6.2恒假，则删除所有子算子*/
    for (i = 0; i < childOpers->elementCount; i++) {
      childOper = varArrayListGetPointer(childOpers, i);
      LogicalPlanDestroy(&childOper);
    }
    varArrayListClear(childOpers);
  }
  *changeMade = true;
  return GNCDB_SUCCESS;
}