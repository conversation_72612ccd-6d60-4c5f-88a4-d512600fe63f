#include "conjunction_simplification_rule.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "vararraylist.h"
int tryToGetBoolConstant(Expression **expr, bool *constantValue)
{
  ValueExpr *valueExpr = NULL;
  if ((*expr)->type == ETG_VALUE && ((ValueExpr *)*expr)->value->attrType == BOOLEANS) {
    valueExpr      = (ValueExpr *)*expr;
    *constantValue = valueExpr->value->numValue.boolValue;
    return GNCDB_SUCCESS;
  }
  return GNCDB_INTERNAL;
}
/**
 * @brief 简化逻辑连接表达式。
 *        如果表达式是 AND 类型的连接，并且其中的子表达式为真值常量，则移除该子表达式；
 *        如果表达式是 OR 类型的连接，并且其中的子表达式为假值常量，则移除该子表达式。
 *        如果连接表达式只剩下一个子表达式，则将其替换为该子表达式，changeMade = true。
 *
 * @param expr_ptr 指向原始表达式指针的指针，用于更新表达式。
 * @param expr 指向当前处理的连接表达式指针的指针。
 * @param changeMade 指示表达式是否被修改的布尔标志。
 * @return int 返回操作结果，成功为 GNCDB_SUCCESS。
 */
int ConjunctionSimplificationRewriter(void *exprPtr, Expression **expr, bool *changeMade)
{
  int              rc            = GNCDB_SUCCESS;
  ConjunctionExpr *conjExpr      = NULL;
  varArrayList    *childExprs    = NULL;
  int              i             = 0;
  Expression      *childExpr     = NULL;
  bool             constantValue = false;
  ValueExpr       *childExprCopy = NULL;
  // ComparisonExpr* compExpr = NULL;

  if ((*expr)->type != ETG_CONJUNCTION) {
    return rc;
  }
  *changeMade = false;
  conjExpr    = (ConjunctionExpr *)*expr;
  childExprs  = conjExpr->children;
  for (i = 0; i < childExprs->elementCount; i++) {
    childExpr     = varArrayListGetPointer(childExprs, i);
    constantValue = false;
    rc            = tryToGetBoolConstant(&childExpr, &constantValue);
    if (rc != GNCDB_SUCCESS) {
      rc = GNCDB_SUCCESS;
      continue;
    }
    if (conjExpr->conjunctionType == CJET_AND) {
      if (constantValue == true) {
        varArrayListRemoveByIndex(childExprs, i);
        i--;
      } else {
        // always be false
        childExprCopy        = (ValueExpr *)my_malloc0(sizeof(ValueExpr));
        if(childExprCopy == NULL) {
          return GNCDB_MEM;
        }
        childExprCopy->type  = ETG_VALUE;
        childExprCopy->name  = childExpr->name;
        childExprCopy->value = valueCopy(((ValueExpr *)childExpr)->value);
        varArrayListRemoveByIndex(childExprs, i);
        i--;
        varArrayListDestroy(&childExprs);
        *expr                   = (Expression *)childExprCopy;
        PTR_MOVE((void **)&(childExpr));
        *changeMade             = true;
        return rc;
      }
    } else {
      if (constantValue == true) {
        // always be true
        childExprCopy        = (ValueExpr *)my_malloc0(sizeof(ValueExpr));
        if(childExprCopy == NULL) {
          return GNCDB_MEM;
        }
        childExprCopy->type  = ETG_VALUE;
        childExprCopy->name  = childExpr->name;
        childExprCopy->value = ((ValueExpr *)childExpr)->value;
        varArrayListRemoveByIndex(childExprs, i);
        i--;
        varArrayListDestroy(&childExprs);
        *expr                   = (Expression *)childExprCopy;
        *(Expression **)exprPtr = PTR_MOVE((void **)&(childExpr));
        *changeMade             = true;
        return rc;
      } else {
        varArrayListRemoveByIndex(childExprs, i);
        i--;
      }
    }
  }

  if (childExprs->elementCount == 1) {
    *(Expression **)exprPtr = (Expression *)varArrayListGetPointer(childExprs, 0);
    varArrayListRemoveByIndex(childExprs, 0);
    exprDestroy(*expr);
    *changeMade = true;
  }
  return rc;
}
