
#include "rewriter.h"
#include "btreetable.h"
#include "delete_stmt.h"
#include "expression.h"
#include "expression_rewriter.h"
#include "filter_stmt.h"
#include "gncdb.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "predicate_rewrite.h"
#include "predicate_pushdown_rewriter.h"
#include "select_stmt.h"
#include "sql_event.h"
#include "physical_plan_generator.h"
#include "table_scan_physical_operator.h"
#include "typedefine.h"
#include "update_stmt.h"
#include "value.h"
#include "vararraylist.h"
#include <string.h>

RewriterFunc rewriterFunctions[] = {ExpressionRewriter, PredicateRewriter, PredicatePushdownRewriter};

int ProjectionPushDown(SQLStageEvent *sqlEvent);

int LogicalOperatorRewriter(LogicalOperator *oper, bool *changeMade)
{
  int              rc            = GNCDB_SUCCESS;
  bool             subChangeMade = false;
  LogicalOperator *childOper     = NULL;

  *changeMade = false;

  /*1.依次调用每个重写器函数,就是依次应用三种优化规则*/
  for (size_t i = 0; i < sizeof(rewriterFunctions) / sizeof(rewriterFunctions[0]); i++) {
    subChangeMade = false;
    rc            = rewriterFunctions[i](oper, &subChangeMade);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    if (subChangeMade) {
      *changeMade = true;
    }
  }

  /*2.递归处理子节点*/
  for (int i = 0; i < oper->children->elementCount; i++) {
    subChangeMade = false;
    childOper     = varArrayListGetPointer(oper->children, i);
    rc            = LogicalOperatorRewriter(childOper, &subChangeMade);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    if (subChangeMade) {
      *changeMade = true;
    }
  }

  return rc;
}

int tryToGetBoolValue(Expression **expr, bool *constantValue)
{
  ValueExpr *valueExpr = NULL;
  if ((*expr)->type == ETG_VALUE && ((ValueExpr *)*expr)->value->attrType == BOOLEANS) {
    valueExpr      = (ValueExpr *)*expr;
    *constantValue = valueExpr->value->numValue.boolValue;
    return GNCDB_SUCCESS;
  }
  return GNCDB_INTERNAL;
}

int simplifyOneExpress(Expression **expr)
{
  int              rc            = GNCDB_SUCCESS;
  ComparisonExpr  *compExpr      = NULL;
  ConjunctionExpr *conjExpr      = NULL;
  Expression     **childExpr     = NULL;
  ValueExpr       *valueExpr     = NULL;
  bool             constantValue = false;
  Value            value;

  if (expr == NULL || *expr == NULL) {
    return GNCDB_SUCCESS;
  }

  if ((*expr)->type == ETG_COMPARISON) {
    /* 单个比较条件，判断是否有value op value形式，并将其简化为bool valueExpre */
    compExpr = (ComparisonExpr *)(*expr);
    rc       = exprTryGetValue((Expression *)compExpr, &value);
    if (rc == GNCDB_SUCCESS) {
      valueExpr        = exprCreate(ETG_VALUE);
      valueExpr->value = valueCopy(&value);
      exprDestroy(*expr);
      *expr = (Expression *)valueExpr;
    }
    return GNCDB_SUCCESS;
  } else if ((*expr)->type == ETG_CONJUNCTION) {
    /* 联结条件，对其子比较条件尝试进行简化 */
    conjExpr = (ConjunctionExpr *)(*expr);
    for (int i = 0; i < conjExpr->children->elementCount; i++) {
      childExpr = (Expression **)varArrayListGet(conjExpr->children, i);
      rc        = simplifyOneExpress(childExpr);
      rc        = tryToGetBoolValue(childExpr, &constantValue);
      /* 如果此子条件无法简化，跳过子条件 */
      if (rc != GNCDB_SUCCESS) {
        rc = GNCDB_SUCCESS;
        continue;
      }
      /* 此子条件可以被简化为true或者false */
      if (conjExpr->conjunctionType == CJET_AND) {
        if (constantValue == true) {
          /* and中一个为真，只需要将其删除即可 */
          varArrayListRemoveByIndex(conjExpr->children, i);
          i--;
        } else {
          /* and中一个为假，整个联结条件都为假，使用一个值为false的bool valueExpr替代 */
          valueExpr        = (ValueExpr *)my_malloc0(sizeof(ValueExpr));
          valueExpr->type  = ETG_VALUE;
          valueExpr->name  = NULL;
          valueExpr->value = valueCopy(((ValueExpr *)childExpr)->value);
          exprDestroy(*expr);
          *expr = (Expression *)valueExpr;
        }
      } else if (conjExpr->conjunctionType == CJET_OR) {
        if (constantValue == false) {
          /* or中一个为假，只需要将其删除即可 */
          varArrayListRemoveByIndex(conjExpr->children, i);
          i--;
        } else {
          /* or中一个为真，整个联结条件都为真，使用一个值为true的bool valueExpr替代 */
          valueExpr        = (ValueExpr *)my_malloc0(sizeof(ValueExpr));
          valueExpr->type  = ETG_VALUE;
          valueExpr->name  = NULL;
          valueExpr->value = valueCopy(((ValueExpr *)childExpr)->value);
          exprDestroy(*expr);
          *expr = (Expression *)valueExpr;
        }
      }
    }
    return rc;
  }

  return rc;
}

int ExpressionSimplificationRewriter(FilterStmt **filterStmt)
{
  int rc = GNCDB_SUCCESS;
  rc     = simplifyOneExpress(&((*filterStmt)->condition));
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 如果条件被简化为空，则将filterStmt置为NULL */
  if ((*filterStmt)->condition == NULL) {
    FilterStmtPointerDestroy(&filterStmt);
  }

  return rc;
}

int pushDownOneExpression(Expression **expr, SQLStageEvent *sqlEvent)
{
  int                        rc            = GNCDB_SUCCESS;
  ConjunctionExpr           *conjExpr      = NULL;
  Expression                *childExpr     = NULL;
  ComparisonExpr            *compExpr      = NULL;
  FieldExpr                 *fieldExpr     = NULL;
  TableScanPhysicalOperator *tableScanOper = NULL;
  if (expr == NULL || *expr == NULL || sqlEvent == NULL) {
    return GNCDB_SUCCESS;
  }

  if ((*expr)->type == ETG_COMPARISON) {
    compExpr = (ComparisonExpr *)(*expr);
    if (compExpr->left->type == ETG_FIELD &&
        (compExpr->right->type == ETG_VALUE || compExpr->right->type == ETG_ARITHMETIC)) {
      /* 左边是字段，右边是值，可以下推 */
      fieldExpr     = (FieldExpr *)compExpr->left;
      tableScanOper = hashMapGet(sqlEvent->tabOperMap, fieldExpr->tableName);
      if (tableScanOper != NULL) {
        varArrayListAddPointer(tableScanOper->predicates, compExpr);
        *expr = NULL;
      }
    } else if ((compExpr->left->type == ETG_VALUE || compExpr->left->type == ETG_ARITHMETIC) &&
               compExpr->right->type == ETG_FIELD) {
      /* 左边是值，右边是字段，可以下推 */
      fieldExpr     = (FieldExpr *)compExpr->right;
      tableScanOper = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, fieldExpr->tableName);
      if (tableScanOper != NULL) {
        varArrayListAddPointer(tableScanOper->predicates, compExpr);
        *expr = NULL;
      }
    }
  } else if ((*expr)->type == ETG_CONJUNCTION) {
    conjExpr = (ConjunctionExpr *)(*expr);
    for (int i = 0; i < conjExpr->children->elementCount; i++) {
      childExpr = (Expression *)varArrayListGetPointer(conjExpr->children, i);
      rc        = pushDownOneExpression(&childExpr, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      if (childExpr == NULL) {
        /* 如果子表达式被下推成功，则从联结条件中删除 */
        varArrayListRemoveByIndex(conjExpr->children, i);
        i--;
      }
    }
    if (conjExpr->children->elementCount == 0) {
      /* 如果联结条件中没有子表达式了，则将联结条件置为NULL */
      exprDestroy(*expr);
      *expr = NULL;
    }
  }

  return rc;
}

int ExpressionPushdownRewriter(SQLStageEvent *sqlEvent)
{
  int         rc         = GNCDB_SUCCESS;
  SelectStmt *selectStmt = NULL;

  selectStmt = (SelectStmt *)sqlEvent->stmt; /* 前面步骤已经保证了这里就是select语句 */

  /* 下推阶段：对where中的条件进行遍历，判断是否能够下推到表物理算子中 */
  if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL) {
    rc = pushDownOneExpression(&(selectStmt->filterStmt->condition), sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (selectStmt->filterStmt->condition == NULL) {
      /* 如果where条件被下推成功，则将其置为NULL */
      FilterStmtPointerDestroy(&(selectStmt->filterStmt));
    }
  }

  return rc;
}

int ExpressionRewriter_v2(SQLStageEvent *sqlEvent)
{
  int         rc         = GNCDB_SUCCESS;
  SelectStmt *selectStmt = (SelectStmt *)sqlEvent->stmt; /* 前面步骤已经保证了这里就是select语句 */

  /* 对having子句中的条件进行简化 */
  if (selectStmt->havingStmt != NULL && selectStmt->havingStmt->condition != NULL) {
    rc = ExpressionSimplificationRewriter(&(selectStmt->havingStmt));
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /* 首先对where子句中的条件进行简化 */
  if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL) {
    rc = ExpressionSimplificationRewriter(&(selectStmt->filterStmt));
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  rc = ProjectionPushDown(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 对where子句中的条件进行尝试下推 */
  rc = ExpressionPushdownRewriter(sqlEvent);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return rc;
}

int ProjectionPushDown(SQLStageEvent *sqlEvent)
{
  int                        rc            = GNCDB_SUCCESS;
  SelectStmt                *selectStmt    = NULL;
  JoinTables                *joinTables    = NULL;
  BtreeTable                *table         = NULL;
  TableScanPhysicalOperator *tableScanOper = NULL;

  if (sqlEvent->tabOperMap == NULL) {
    sqlEvent->tabOperMap = hashMapCreate(STRKEY, 0, NULL);
  }

  selectStmt = (SelectStmt *)sqlEvent->stmt;

  /* 准备阶段：根据selectStmt中的表，构造物理算子 */
  joinTables = selectStmt->joinTables;
  for (int i = 0; i < joinTables->tables->elementCount; i++) {
    table = (BtreeTable *)varArrayListGetPointer(joinTables->tables, i);
    TABLE_PHYOPER_CONSTRUCT(tableScanOper, table, sqlEvent);
    hashMapPut(sqlEvent->tabOperMap, table->tableName, tableScanOper);
  }

  /* 下推阶段： */
  // selectStmt->queryFields;
  return rc;
}