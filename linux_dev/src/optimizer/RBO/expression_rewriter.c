#include "expression_rewriter.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "vararraylist.h"
#include "conjunction_simplification_rule.h"
#include "comparison_simplification_rule.h"
int ExpressionRewriter(LogicalOperator *oper, bool *changeMade)
{
  int              rc             = GNCDB_SUCCESS;
  bool             subChangeMade1 = false;
  varArrayList    *childOpers     = NULL;
  Expression      *expr           = NULL;
  void            *exprPtr        = NULL;
  int              i              = 0;
  bool             subChangeMade2 = false;
  varArrayList    *expressions    = oper->expressions;
  LogicalOperator *childOper      = NULL;
  
  /*1.参数检查*/
  if (expressions == NULL || expressions->elementCount == 0) {
    return GNCDB_SUCCESS;
  }

  /*2.重写当前算子的每一个表达式*/
  for (i = 0; i < expressions->elementCount; i++) {
    expr    = varArrayListGetPointer(expressions, i);
    exprPtr = varArrayListGet(expressions, i);
    rc      = RewriteExpression(exprPtr, &expr, &subChangeMade1);
    if (rc != GNCDB_SUCCESS) {
      break;
    }
    if (subChangeMade1 && !(*changeMade)) {
      *changeMade = true;
    }
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /*3.递归处理当前算子的每一个子算子*/
  childOpers = oper->children;
  for (i = 0; i < childOpers->elementCount; i++) {
    subChangeMade2 = false;
    childOper      = varArrayListGetPointer(childOpers, i);
    rc             = ExpressionRewriter(childOper, &subChangeMade2);
    if (subChangeMade2 && !(*changeMade)) {
      *changeMade = true;
    }
    if (rc != GNCDB_SUCCESS) {
      break;
    }
  }
  return rc;
}

int RewriteExpression(void *exprPtr, Expression **expr, bool *changeMade)
{
  int              rc              = GNCDB_SUCCESS;
  bool             subChangeMade1  = false;
  bool             subChangeMade2  = false;
  Expression      *childExpr       = NULL;
  void            *childExprPtr    = NULL;
  ComparisonExpr  *compExpr        = NULL;
  Expression      *leftExpr        = NULL;
  Expression      *rightExpr       = NULL;
  void            *leftExprPtr     = NULL;
  void            *rightExprPtr    = NULL;
  bool             leftChangeMade  = false;
  bool             rightChangeMade = false;
  ConjunctionExpr *conjExpr        = NULL;
  varArrayList    *children        = NULL;
  bool             subChangeMade   = false;
  int              i               = 0;

  *changeMade = false;
  rc          = ComparisonSimplificationRewriter(exprPtr, expr, &subChangeMade1);
  if(rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 这里expr如果发生改变，需要update expr */
  if (subChangeMade1) {
    expr = exprPtr;
  }
  rc = ConjunctionSimplificationRewriter(exprPtr, expr, &subChangeMade2);
  if ((subChangeMade1 || subChangeMade2) && !(*changeMade)) {
    *changeMade = true;
  }

  if (*changeMade || rc != GNCDB_SUCCESS) {
    return rc;
  }

  switch ((*expr)->type) {
    case ETG_FIELD:
    case ETG_VALUE: {

    } break;
    case ETG_CAST: {
      childExpr    = ((CastExpr *)*expr)->child;
      childExprPtr = &((CastExpr *)*expr)->child;
      rc           = RewriteExpression(childExprPtr, &childExpr, changeMade);
    } break;

    case ETG_COMPARISON: {
      compExpr       = (ComparisonExpr *)*expr;
      leftExpr       = compExpr->left;
      rightExpr      = compExpr->right;
      leftExprPtr    = &compExpr->left;
      rightExprPtr   = &compExpr->right;
      leftChangeMade = false;
      rc             = RewriteExpression(leftExprPtr, &leftExpr, &leftChangeMade);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      rightChangeMade = false;
      rc              = RewriteExpression(rightExprPtr, &rightExpr, &rightChangeMade);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      if (subChangeMade1 || subChangeMade2) {
        *changeMade = true;
      }
    } break;
    case ETG_CONJUNCTION: {
      conjExpr = (ConjunctionExpr *)*expr;
      children = conjExpr->children;
      for (i = 0; i < children->elementCount; i++) {
        childExpr     = varArrayListGetPointer(children, i);
        childExprPtr  = varArrayListGet(children, i);
        subChangeMade = false;
        rc            = RewriteExpression(childExprPtr, &childExpr, &subChangeMade);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        if (subChangeMade && !(*changeMade)) {
          *changeMade = true;
        }
      }
    } break;
    default: {
    } break;
  }
  return rc;
}