#include "predicate_pushdown_rewriter.h"
#include "expression.h"
#include "gncdbconstant.h"
#include "join_logical_operator.h"
#include "table_get_logical_operator.h"
#include "typedefine.h"
#include "value.h"
#include <string.h>
extern CompOp swapOp(CompOp op);
/**
 * @brief 对逻辑操作符进行谓词下推优化。
 *        如果操作符是谓词操作符，并且只有一个子操作符，且该子操作符是表获取操作符，
 *        则尝试将谓词表达式下推到表获取操作符中。
 *
 * @param oper 指向当前处理的逻辑操作符的指针。
 * @param changeMade 指示操作符是否被修改的布尔标志。
 * @return int 返回操作结果代码，成功时返回 GNCDB_SUCCESS。
 */
int PredicatePushdownRewriter(LogicalOperator *oper, bool *changeMade)
{
  int              rc                 = GNCDB_SUCCESS;
  LogicalOperator *childOper          = NULL;
  LogicalOperator *leftOper           = NULL;
  LogicalOperator *rightOper          = NULL;
  varArrayList    *predicateOperExprs = NULL;

  /*1.仅仅对predicate和join算子中的条件进行后续的谓词下推检查 */
  if (!(oper->type == LO_PREDICATE || oper->type == LO_JOIN)) {
    return rc;
  }

  if (oper->type == LO_PREDICATE) {
    childOper          = varArrayListGetPointer(oper->children, 0);
    predicateOperExprs = oper->expressions;  /*where后面的条件*/
    if (predicateOperExprs->elementCount != 1) {
      return rc;
    }
    /* 尝试把predicateOperExprs谓词下推给childOper*/
    rc = tryExprsPushdown(predicateOperExprs, childOper, changeMade);
  } else if (oper->type == LO_JOIN) {
    leftOper           = varArrayListGetPointer(oper->children, 0);
    rightOper          = varArrayListGetPointer(oper->children, 1);
    predicateOperExprs = oper->expressions;  /*join节点中on后面的条件*/
    /* 没有条件，直接返回 */
    if (predicateOperExprs == NULL || predicateOperExprs->elementCount == 0) {
      return GNCDB_SUCCESS;
    }
    /* 分别从左右节点遍历尝试下推表达式 */
    rc = tryExprsPushdown(predicateOperExprs, leftOper, changeMade);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = tryExprsPushdown(predicateOperExprs, rightOper, changeMade);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return rc;
}

/**
 * @brief 下推一般Cond，即<col, val>
 *
 * @param cmpExpr lhs为col，rhs为val的比较表达式
 * @param oper
 * @return int 成功返回1
 */
int pushNormalConds(ComparisonExpr *cmpExpr, LogicalOperator *oper)
{
  TableGetLogicalOperator *scan      = NULL;
  JoinLogicalOperator     *join      = NULL;
  Expression              *lhs       = NULL;
  Expression              *rhs       = NULL;
  FieldExpr               *fieldExpr = NULL;
  int                      leftRes   = 0;
  int                      rightRes  = 0;

  lhs = cmpExpr->left;
  rhs = cmpExpr->right;
  if (!((lhs->type == ETG_FIELD && rhs->type == ETG_VALUE) || (lhs->type == ETG_VALUE && rhs->type == ETG_FIELD))) {
    return 0;
  }
  if (oper->type == LO_TABLE_GET) {
    scan = (TableGetLogicalOperator *)oper;
    if (lhs->type == ETG_FIELD || rhs->type == ETG_FIELD) {
      fieldExpr = lhs->type == ETG_FIELD ? (FieldExpr *)lhs : (FieldExpr *)rhs;
      if (strcmp(fieldExpr->tableName, scan->table->tableName) == 0) {
        varArrayListAddPointer(scan->expressions, cmpExpr);
        return 1;
      }
    }
    return 0;
  } else if (oper->type == LO_JOIN) {
    join    = (JoinLogicalOperator *)oper;
    leftRes = pushNormalConds(cmpExpr, varArrayListGetPointer(join->children, 0));
    if (leftRes == 1) {
      return leftRes;
    }
    rightRes = pushNormalConds(cmpExpr, varArrayListGetPointer(join->children, 1));
    return rightRes;
  } else {
    return pushNormalConds(cmpExpr, varArrayListGetPointer(oper->children, 0));
  }
}

/**
 * @brief 下推连接join Cond，即<colA, colB>
 *
 * @param cmpExpr
 * @param oper
 * @return int 成功返回3
 */
int pushJoinConds(ComparisonExpr *cmpExpr, LogicalOperator *oper)
{
  TableGetLogicalOperator *scan     = NULL;
  JoinLogicalOperator     *join     = NULL;
  Expression              *lhs      = NULL;
  Expression              *rhs      = NULL;
  Expression              *tmpExpr  = NULL;
  int                      leftRes  = 0;
  int                      rightRes = 0;

  lhs = cmpExpr->left;
  rhs = cmpExpr->right;
  if (!((lhs->type == ETG_FIELD && rhs->type == ETG_FIELD))) {
    return 0;
  }
  if (oper->type == LO_TABLE_GET) {
    scan = (TableGetLogicalOperator *)oper;
    if (strcmp(scan->table->tableName, ((FieldExpr *)lhs)->tableName) == 0) {
      return 1;
    } else if (strcmp(scan->table->tableName, ((FieldExpr *)rhs)->tableName) == 0) {
      return 2;
    } else {
      return 0;
    }
  } else if (oper->type == LO_JOIN) {
    join = (JoinLogicalOperator *)oper;
    // LogicalOperatorToString(oper, 0);
    leftRes = pushJoinConds(cmpExpr, varArrayListGetPointer(join->children, 0));
    if (leftRes == 3) {
      return 3;
    }
    rightRes = pushJoinConds(cmpExpr, varArrayListGetPointer(join->children, 1));
    if (rightRes == 3) {
      return 3;
    }
    if (leftRes == 0 || rightRes == 0) {
      return leftRes + rightRes;
    }
    if (leftRes == 2) {
      tmpExpr        = cmpExpr->left;
      cmpExpr->left  = cmpExpr->right;
      cmpExpr->right = tmpExpr;
      cmpExpr->comp  = swapOp(cmpExpr->comp);
    }
    varArrayListAddPointer(join->expressions, cmpExpr);
    return 3;
  } else {
    return pushJoinConds(cmpExpr, varArrayListGetPointer(oper->children, 0));
  }
}

int tryCmpExprPushdown(ComparisonExpr *cmpExpr, LogicalOperator *child, bool *changeMade)
{
  int res = 0;
  if ((cmpExpr->left->type == ETG_FIELD && cmpExpr->right->type == ETG_VALUE) ||
      (cmpExpr->left->type == ETG_VALUE && cmpExpr->right->type == ETG_FIELD)) {
    res = pushNormalConds(cmpExpr, child);
    if (res == 1) {
      *changeMade = true;
      return GNCDB_SUCCESS;
    }
  } else if (cmpExpr->left->type == ETG_FIELD && cmpExpr->right->type == ETG_FIELD) {
    res = pushJoinConds(cmpExpr, child);
    if (res == 3) {
      *changeMade = true;
      return GNCDB_SUCCESS;
    }
  }
  return GNCDB_PREDICATE_PUSHDOWN_FAILED;
}

/**
 * @brief Get the Exprs Can Pushdown object
 *
 * @param expr
 * @param pushdown_exprs
 * @return int
 */
int tryExprsPushdown(varArrayList *exprs, LogicalOperator *child, bool *changeMade)
{
  int              rc       = GNCDB_SUCCESS;
  ConjunctionExpr *conjExpr = NULL;
  int              i = 0, j = 0;
  Expression      *subExpr       = NULL;
  Expression      *subExpr2      = NULL;
  bool             subChangeMade = false;

  for (i = 0; i < exprs->elementCount; i++) {
    subExpr = varArrayListGetPointer(exprs, i);
    if (subExpr->type == ETG_CONJUNCTION) {
      conjExpr = (ConjunctionExpr *)subExpr;
      // 或 操作的比较，太复杂，现在不考虑
      if (conjExpr->conjunctionType == CJET_OR) {
        return rc;
      }
      for (j = 0; j < conjExpr->children->elementCount; j++) {
        subExpr2 = varArrayListGetPointer(conjExpr->children, j);
        if (subExpr2->type == ETG_CONJUNCTION) {
          rc = tryExprsPushdown(((ConjunctionExpr *)subExpr2)->children, child, &subChangeMade);
          if (subChangeMade) {
            if (((ConjunctionExpr *)subExpr2)->children->elementCount == 0) {
              varArrayListRemoveByIndex(conjExpr->children, j);
              exprDestroy((Expression *)subExpr2);
              j--;
              subChangeMade = false;
            }
          }
        } else if (subExpr2->type == ETG_COMPARISON) {
          rc = tryCmpExprPushdown((ComparisonExpr *)subExpr2, child, &subChangeMade);
          if (subChangeMade) {
            *changeMade = true;
            varArrayListRemoveByIndex(conjExpr->children, j);
            j--;
            subChangeMade = false;
          }
        }
      }
      //* 如果下推后没有表达式了，则删除该表达式
      if (conjExpr->children->elementCount == 0) {
        exprDestroy((Expression *)conjExpr); /* 删除当前的conjExpr */
        varArrayListRemoveByIndex(exprs, i);
        i--;
      }
    } else if (subExpr->type == ETG_COMPARISON) {
      rc = tryCmpExprPushdown((ComparisonExpr *)subExpr, child, changeMade);
      if (rc == GNCDB_SUCCESS) {
        *changeMade = true;
        varArrayListRemoveByIndex(exprs, i);
        i--;
      }
    }
  }
  return GNCDB_SUCCESS;
}
