#ifndef OPTIMIZER_REWRITER_H_
#define OPTIMIZER_REWRITER_H_
#include <stdbool.h>

typedef struct SQLStageEvent SQLStageEvent;
typedef struct LogicalOperator LogicalOperator;
typedef int (*RewriterFunc)(LogicalOperator *oper, bool *changeMade);

/**
 * @brief 对逻辑计划进行重写
 * @details 如果重写发生，changeMade为true，否则为false。
 * 通常情况下如果改写发生改变，就会继续重写，直到没有改变为止。
 * @param oper 逻辑计划
 * @param changeMade 当前是否有重写发生
 * @return int
 */
int LogicalOperatorRewriter(LogicalOperator *oper, bool *changeMade);

int ExpressionRewriter_v2(SQLStageEvent *sqlEvent);
#endif
