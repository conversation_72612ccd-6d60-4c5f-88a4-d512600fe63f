#include "comparison_simplification_rule.h"
#include "expression.h"
#include "value.h"
/**
 * @brief 对比较表达式进行简化处理。
 *        如果比较表达式可以被简化为一个常量值，那么就用一个值表达式来替换它。
 *
 * @param expr_ptr 指向原始表达式指针的指针，用于在简化后更新表达式。
 * @param expr 指向当前处理的表达式指针的指针。
 * @param changeMade 指示表达式是否被修改的布尔标志。
 * @return int 返回操作结果代码，成功时返回 GNCDB_SUCCESS。
 */
int ComparisonSimplificationRewriter(void *exprPtr, Expression **expr, bool *changeMade)
{
  int             rc       = GNCDB_SUCCESS;
  ComparisonExpr *compExpr = NULL;
  Value           value;
  int             subRc   = 0;
  ValueExpr      *newExpr = NULL;

  *changeMade = false;
  if ((*expr)->type == ETG_COMPARISON) {
    compExpr = (ComparisonExpr *)*expr;
    subRc    = exprTryGetValue((Expression*)compExpr, &value);
    if (subRc == GNCDB_SUCCESS) {
      newExpr = exprCreate(ETG_VALUE);
      newExpr->value = valueCopy(&value);
      *(Expression **)exprPtr = (Expression *)newExpr;
      *changeMade             = true;
      exprDestroy(*expr);
    }
  }
  return rc;
}