/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-22 15:35:20
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-12 17:29:50
 * @FilePath: /gncdbflr/linux_dev/src/optimizer/RBO/equality_propagation_rule.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "equality_propagation_rule.h"
#include "gncdb.h"
#include "join_logical_operator.h"
#include "logical_operator.h"
#include "exec_tuple.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "rewriter.h"
#include "select_stmt.h"
#include "sql_event.h"
#include "typedefine.h"
#include "value.h"
#include "expression.h"
#include "vararraylist.h"
#include "lookaside_mem.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

UFNode *UFSetFind(UFNode *node)
{
    UFNode *parent = node->parent;

    if (strcmp(parent->expeStr, node->expeStr) != 0)
    {
        node->parent = UFSetFind(parent);
    }
    return parent;
}

void UFSetUnion(UFNode *node1, UFNode *node2)
{
    UFNode *root1 = UFSetFind(node1);
    UFNode *root2 = UFSetFind(node2);

    if (root1 == root2)
    {
        return;
    }

    if (root1->rank > root2->rank)
    {
        root2->parent = root1;
    }
    else if (root1->rank < root2->rank)
    {
        root1->parent = root2;
    }
    else
    {
        root2->parent = root1;
        root1->rank++;
    }
}

/**
 * @description: 辅助函数，将表达式转换为字符串，当前主要是针对value和算术表达式
 * @param {Expression} *expr
 * @return {*}
 */
char *exprToString(Expression *expr)
{
    int             len        = 0;
    char           *str        = NULL;
    char           *subStr1    = NULL;
    char           *subStr2    = NULL;
    char           *arithOP[6] = {" INVALID ", " ADD ", " SUB ", " MUV ", " DIV ", " - "};
    ArithmeticExpr *arithExpr  = NULL;
    switch (expr->type)
    {
        case ETG_VALUE: {
            return valueToString(((ValueExpr *)expr)->value);
        }
        case ETG_ARITHMETIC: {
            arithExpr = (ArithmeticExpr *)expr;
            subStr1   = exprToString(arithExpr->left);
            subStr2   = exprToString(arithExpr->right);
            len       = snprintf(NULL, 0, "%s%s%s", subStr1, arithOP[arithExpr->arithmeticType], subStr2);
            str       = (char *)my_malloc0(len + 1);
            sprintf(str, "%s%s%s", subStr1, arithOP[arithExpr->arithmeticType], subStr2);
            str[len] = '\0';
            my_free(subStr1);
            my_free(subStr2);
            return str;
        }
        default:
            return NULL;
    }
    return str;
}

/**
 * @description: 将单个expr中的左右field和value表达式构造并查集节点
 * @param {Expression} *expr 表达式
 * @param {varArrayList} *nEqExprs 收集的非等值条件List
 * @param {HashMap} *ufNodes 并查集节点的哈希表
 * @param {HashMap} *ufSets 并查集的哈希表
 * @return {*}
 */
int exprToUFNode(Expression *expr, varArrayList *nEqExprs, HashMap *ufNodes, HashMap *ufSets)
{
    int              rc        = GNCDB_SUCCESS;
    int              strLen    = 0;
    char            *exprStr   = NULL;
    ComparisonExpr  *compExpr  = NULL;
    ConjunctionExpr *conjExpr  = NULL;
    UFNode          *node1     = NULL;
    UFNode          *node2     = NULL;
    UFNode          *parent1   = NULL;
    UFNode          *parent2   = NULL;
    UFNode          *newParent = NULL;
    UFSet           *ufSet1    = NULL;
    UFSet           *ufSet2    = NULL;
    FieldExpr       *fieldExpr = NULL;

    if (expr == NULL)
    {
        return GNCDB_SUCCESS;
    }

    if (expr->type != ETG_COMPARISON && expr->type != ETG_CONJUNCTION)
    {
        return GNCDB_SUCCESS;
    }

    if (expr->type == ETG_CONJUNCTION)
    {
        conjExpr = (ConjunctionExpr *)expr;
        for (int i = 0; i < conjExpr->children->elementCount; i++)
        {
            expr = (Expression *)varArrayListGetPointer(conjExpr->children, i);
            rc   = exprToUFNode(expr, nEqExprs, ufNodes, ufSets);
            if (rc != GNCDB_SUCCESS)
            {
                return rc;
            }
        }
        return GNCDB_SUCCESS;
    }

    if (expr->type == ETG_COMPARISON)
    {
        //* 如果是比较表达式，需要构造一个含有两个元素的并查集 */
        compExpr = (ComparisonExpr *)expr;

        //* 左边或者右边不是字段或值，直接返回 */
        if (compExpr->left->type != ETG_FIELD && compExpr->left->type != ETG_VALUE)
        {
            return GNCDB_SUCCESS;
        }
        if (compExpr->right->type != ETG_FIELD && compExpr->right->type != ETG_VALUE)
        {
            return GNCDB_SUCCESS;
        }

        //* 非相等条件直接加入List并返回 */
        if (compExpr->comp != CMPOP_EQUAL_TO)
        {
            /* 只有= < > 这些才能在后续进行传播，其余忽略，不加入list */
            if (compExpr->comp >= CMPOP_EQUAL_TO && compExpr->comp <= CMPOP_GREAT_THAN)
            {
                varArrayListAddPointer(nEqExprs, expr);
            }
            return GNCDB_SUCCESS;
        }

        //* 等值条件加入并查集 */
        //* 1.找到或构造左边表达式的UFNode */
        if (compExpr->left->type == ETG_FIELD)
        {
            fieldExpr = (FieldExpr *)compExpr->left;
            strLen    = strlen(fieldExpr->tableName) + strlen(fieldExpr->fieldName) + 2;
            exprStr   = (char *)my_malloc0(strLen);
            snprintf(exprStr, strLen, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
        }
        else if (compExpr->left->type != ETG_FIELD)
        {
            exprStr = exprToString(compExpr->left);
        }
        node1 = (UFNode *)hashMapGet(ufNodes, exprStr);
        if (node1 == NULL)
        {
            //* 如果左边不存在，构造一个UFNode */
            UFNODE_CREATE(node1, compExpr->left);
            node1->expeStr = exprStr;
            hashMapPut(ufNodes, node1->expeStr, node1);
        }
        else
        {
            //* 如果左边已经存在UFNode，直接使用 */
            my_free(exprStr);
        }

        //* 2.找到或构造右边表达式的UFNode */
        if (compExpr->right->type == ETG_FIELD)
        {
            fieldExpr = (FieldExpr *)compExpr->right;
            strLen    = strlen(fieldExpr->tableName) + strlen(fieldExpr->fieldName) + 2;
            exprStr   = (char *)my_malloc0(strLen);
            snprintf(exprStr, strLen, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
        }
        else if (compExpr->right->type == ETG_VALUE)
        {
            exprStr = exprToString(compExpr->right);
        }
        node2 = (UFNode *)hashMapGet(ufNodes, exprStr);
        if (node2 == NULL)
        {
            //* 如果右边不存在，构造一个UFNode */
            UFNODE_CREATE(node2, compExpr->right);
            node2->expeStr = exprStr;
            hashMapPut(ufNodes, node2->expeStr, node2);
        }
        else
        {
            //* 如果右边已经存在UFNode，直接使用 */
            my_free(exprStr);
        }

        //* 2.1.接下来根据节点的根节点找到对应的集合，进行合并操作 */
        parent1 = UFSetFind(node1);
        parent2 = UFSetFind(node2);
        // todo 如果两个parent都是const，此处执行union操作会导致rank溢出
        UFSetUnion(node1, node2);
        //* a = 1 或 1 = a, 标记a避免后续传播重复构造此条件 */
        if (node1->type == VAR && node2->type == CONST)
        {
            node1->isOriginal = true;
        }
        else if (node1->type == CONST && node2->type == VAR)
        {
            node2->isOriginal = true;
        }
        newParent = UFSetFind(node1);

        //* 3.构造或更新并查集，并更新并查集哈希表 */
        ufSet1 = hashMapGet(ufSets, parent1->expeStr);
        ufSet2 = hashMapGet(ufSets, parent2->expeStr);
        if (!ufSet1 && !ufSet2)
        {
            /* 1.两个节点都不在并查集中，需要新构造一个UFSet */
            UFSET_CREATE(ufSet1);
            varArrayListAddPointer(ufSet1->nodes, node1);
            varArrayListAddPointer(ufSet1->nodes, node2);
            ufSet1->root = newParent;
            hashMapPut(ufSets, newParent->expeStr, ufSet1);
        }
        else if (!ufSet1 && ufSet2)
        {
            /* 2.第一个节点不在并查集中，第二个节点在并查集中 */
            varArrayListAddPointer(ufSet2->nodes, node1);
            /* 判断是否需要更新hashmap */
            if (strcmp(ufSet2->root->expeStr, newParent->expeStr) != 0)
            {

                hashMapRemove(ufSets, parent2->expeStr);
                ufSet2->root = newParent;
                hashMapPut(ufSets, newParent->expeStr, ufSet2);
            }
        }
        else if (ufSet1 && !ufSet2)
        {
            /* 3.第一个节点在并查集中，第二个节点不在并查集中 */
            varArrayListAddPointer(ufSet1->nodes, node2);
            /* 判断是否需要更新hashmap */
            if (strcmp(ufSet1->root->expeStr, newParent->expeStr) != 0)
            {
                hashMapRemove(ufSets, parent1->expeStr);
                ufSet1->root = newParent;
                hashMapPut(ufSets, newParent->expeStr, ufSet1);
            }
        }
        else
        {
            /* 4.两个节点都在并查集中，将2合并到1中，销毁2 */
            for (int i = 0; i < ufSet2->nodes->elementCount; i++)
            {
                node2 = (UFNode *)varArrayListGetPointer(ufSet2->nodes, i);
                varArrayListAddPointer(ufSet1->nodes, node2);
            }
            /* 判断是否需要更新hashmap */
            if (strcmp(ufSet1->root->expeStr, newParent->expeStr) == 0)
            {
                hashMapRemove(ufSets, parent2->expeStr);
            }
            else
            {
                hashMapRemove(ufSets, parent1->expeStr);
                hashMapRemove(ufSets, parent2->expeStr);
                ufSet1->root = newParent;
                hashMapPut(ufSets, newParent->expeStr, ufSet1);
            }
            UFSET_DESTROY(ufSet2);
        }
        return GNCDB_SUCCESS;
    }

    return GNCDB_SUCCESS;
}

int exprToUFNode_v3(Expression *expr, varArrayList *nEqExprs, varArrayList *ufNodes, varArrayList *ufSets)
{
    int              rc        = GNCDB_SUCCESS;
    int              strLen    = 0;
    char            *exprStr   = NULL;
    ComparisonExpr  *compExpr  = NULL;
    ConjunctionExpr *conjExpr  = NULL;
    UFNode          *node1     = NULL;
    UFNode          *node2     = NULL;
    UFNode          *iterNode  = NULL;
    UFNode          *parent1   = NULL;
    UFNode          *parent2   = NULL;
    UFNode          *newParent = NULL;
    UFSet           *ufSet1    = NULL;
    UFSet           *ufSet2    = NULL;
    UFSet           *iterSet   = NULL;
    FieldExpr       *fieldExpr = NULL;

    if (expr == NULL)
    {
        return GNCDB_SUCCESS;
    }

    if (expr->type != ETG_COMPARISON && expr->type != ETG_CONJUNCTION)
    {
        return GNCDB_SUCCESS;
    }

    if (expr->type == ETG_CONJUNCTION)
    {
        conjExpr = (ConjunctionExpr *)expr;
        for (int i = 0; i < conjExpr->children->elementCount; i++)
        {
            expr = (Expression *)varArrayListGetPointer(conjExpr->children, i);
            rc   = exprToUFNode_v3(expr, nEqExprs, ufNodes, ufSets);
            if (rc != GNCDB_SUCCESS)
            {
                return rc;
            }
        }
        return GNCDB_SUCCESS;
    }

    if (expr->type == ETG_COMPARISON)
    {
        //* 如果是比较表达式，需要构造一个含有两个元素的并查集 */
        compExpr = (ComparisonExpr *)expr;

        //* 左边或者右边不是字段或值，直接返回 */
        if (compExpr->left->type != ETG_FIELD && compExpr->left->type != ETG_VALUE)
        {
            return GNCDB_SUCCESS;
        }
        if (compExpr->right->type != ETG_FIELD && compExpr->right->type != ETG_VALUE)
        {
            return GNCDB_SUCCESS;
        }

        //* 非相等条件直接加入List并返回 */
        if (compExpr->comp != CMPOP_EQUAL_TO)
        {
            /* 只有= < > 这些才能在后续进行传播，其余忽略，不加入list */
            if (compExpr->comp >= CMPOP_EQUAL_TO && compExpr->comp <= CMPOP_GREAT_THAN)
            {
                varArrayListAddPointer(nEqExprs, expr);
            }
            return GNCDB_SUCCESS;
        }

        //* 等值条件加入并查集 */
        //* 1.找到或构造左边表达式的UFNode */
        if (compExpr->left->type == ETG_FIELD)
        {
            fieldExpr = (FieldExpr *)compExpr->left;
            strLen    = strlen(fieldExpr->tableName) + strlen(fieldExpr->fieldName) + 2;
            exprStr   = (char *)my_malloc0(strLen);
            snprintf(exprStr, strLen, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
        }
        else if (compExpr->left->type != ETG_FIELD)
        {
            exprStr = exprToString(compExpr->left);
        }
        for (int i = 0; i < ufNodes->elementCount; i++)
        {
            iterNode = (UFNode *)varArrayListGetPointer(ufNodes, i);
            if (strcmp(iterNode->expeStr, exprStr) == 0)
            {
                node1 = iterNode;
                break;
            }
        }
        if (node1 == NULL)
        {
            //* 如果左边不存在，构造一个UFNode */
            UFNODE_CREATE(node1, compExpr->left);
            node1->expeStr = exprStr;
            varArrayListAddPointer(ufNodes, node1);
        }
        else
        {
            //* 如果左边已经存在UFNode，直接使用 */
            my_free(exprStr);
        }

        //* 2.找到或构造右边表达式的UFNode */
        if (compExpr->right->type == ETG_FIELD)
        {
            fieldExpr = (FieldExpr *)compExpr->right;
            strLen    = strlen(fieldExpr->tableName) + strlen(fieldExpr->fieldName) + 2;
            exprStr   = (char *)my_malloc0(strLen);
            snprintf(exprStr, strLen, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
        }
        else if (compExpr->right->type == ETG_VALUE)
        {
            exprStr = exprToString(compExpr->right);
        }
        for (int i = 0; i < ufNodes->elementCount; i++)
        {
            iterNode = (UFNode *)varArrayListGetPointer(ufNodes, i);
            if (strcmp(iterNode->expeStr, exprStr) == 0)
            {
                node2 = iterNode;
                break;
            }
        }
        if (node2 == NULL)
        {
            //* 如果右边不存在，构造一个UFNode */
            UFNODE_CREATE(node2, compExpr->right);
            node2->expeStr = exprStr;
            varArrayListAddPointer(ufNodes, node2);
        }
        else
        {
            //* 如果右边已经存在UFNode，直接使用 */
            my_free(exprStr);
        }

        //* 2.1.接下来根据节点的根节点找到对应的集合，进行合并操作 */
        parent1 = UFSetFind(node1);
        parent2 = UFSetFind(node2);
        // todo 如果两个parent都是const，此处执行union操作会导致rank溢出
        UFSetUnion(node1, node2);
        //* a = 1 或 1 = a, 标记a避免后续传播重复构造此条件 */
        if (node1->type == VAR && node2->type == CONST)
        {
            node1->isOriginal = true;
        }
        else if (node1->type == CONST && node2->type == VAR)
        {
            node2->isOriginal = true;
        }
        newParent = UFSetFind(node1);

        //* 3.构造或更新并查集，并更新并查集哈希表 */
        ufSet1 = ufSet2 = NULL;
        for (int i = 0; i < ufSets->elementCount; i++)
        {
            iterSet = (UFSet *)varArrayListGetPointer(ufSets, i);
            if (strcmp(iterSet->root->expeStr, parent1->expeStr) == 0)
            {
                ufSet1 = iterSet;
            }
            else if (strcmp(iterSet->root->expeStr, parent2->expeStr) == 0)
            {
                ufSet2 = iterSet;
            }
            if (ufSet1 && ufSet2)
            {
                break;
            }
        }
        if (!ufSet1 && !ufSet2)
        {
            /* 1.两个节点都不在并查集中，需要新构造一个UFSet */
            UFSET_CREATE(ufSet1);
            varArrayListAddPointer(ufSet1->nodes, node1);
            varArrayListAddPointer(ufSet1->nodes, node2);
            ufSet1->root = newParent;
            varArrayListAddPointer(ufSets, ufSet1);
        }
        else if (!ufSet1 && ufSet2)
        {
            /* 2.第一个节点不在并查集中，第二个节点在并查集中 */
            varArrayListAddPointer(ufSet2->nodes, node1);
        }
        else if (ufSet1 && !ufSet2)
        {
            /* 3.第一个节点在并查集中，第二个节点不在并查集中 */
            varArrayListAddPointer(ufSet1->nodes, node2);
        }
        else
        {
            /* 4.两个节点都在并查集中，将2合并到1中，销毁2 */
            for (int i = 0; i < ufSet2->nodes->elementCount; i++)
            {
                node2 = (UFNode *)varArrayListGetPointer(ufSet2->nodes, i);
                varArrayListAddPointer(ufSet1->nodes, node2);
            }
            for (int i = 0; i < ufSets->elementCount; i++)
            {
                iterSet = (UFSet *)varArrayListGetPointer(ufSets, i);
                if (strcmp(iterSet->root->expeStr, parent2->expeStr) == 0)
                {
                    varArrayListRemoveByIndex(ufSets, i);
                    break;
                }
            }
            UFSET_DESTROY(ufSet2);
        }
        return GNCDB_SUCCESS;
    }

    return GNCDB_SUCCESS;
}

/**
 * @description: 收集所有节点中的等值条件，加入并查集
 * @param {HashMap} *ufNodes
 * @param {HashMap} *ufSets
 * @param {varArrayList} *nEqExprs
 * @param {LogicalOperator} *oper
 * @return {*}
 */
int collectUFSets(HashMap *ufNodes, HashMap *ufSets, varArrayList *nEqExprs, LogicalOperator *oper)
{
    int                  rc        = GNCDB_SUCCESS;
    Expression          *expr      = NULL;
    JoinLogicalOperator *joinOper  = NULL;
    LogicalOperator     *childOper = NULL;

    /* 1.先遍历一遍算子树并收集所有的表达式并加入并查集*/
    if (oper->expressions != NULL && oper->expressions->elementCount != 0)
    {
        for (int i = 0; i < oper->expressions->elementCount; i++)
        {
            expr = (Expression *)varArrayListGetPointer(oper->expressions, i);
            if (expr->type != ETG_COMPARISON && expr->type != ETG_CONJUNCTION)
            {
                continue;
            }
            /*1.1等值条件加入并查集合、非等值条件加入nEqExprs，等值条件整合入ueSets*/
            rc = exprToUFNode(expr, nEqExprs, ufNodes, ufSets);
            if (rc != GNCDB_SUCCESS)
            {
                return rc;
            }
        }
    }

    /* 2.join需要处理多个ExprList*/
    if (oper->type == LO_JOIN)
    {
        joinOper = (JoinLogicalOperator *)oper;
        /*2.1处理join连接的主键等值条件的并查集*/
        if (joinOper->pkExprs != NULL && joinOper->pkExprs->elementCount != 0)
        {
            for (int i = 0; i < joinOper->pkExprs->elementCount; i++)
            {
                expr = (Expression *)varArrayListGetPointer(joinOper->pkExprs, i);
                rc   = exprToUFNode(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
        /*2.2处理join连接的非主键等值条件*/
        if (joinOper->otherExprs != NULL && joinOper->otherExprs->elementCount != 0)
        {
            for (int i = 0; i < joinOper->otherExprs->elementCount; i++)
            {
                expr = (Expression *)varArrayListGetPointer(joinOper->otherExprs, i);
                rc   = exprToUFNode(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
    }

    /*3.处理完当前节点递归处理子节点*/
    for (int i = 0; i < oper->children->elementCount; i++)
    {
        childOper = (LogicalOperator *)varArrayListGetPointer(oper->children, i);
        rc        = collectUFSets(ufNodes, ufSets, nEqExprs, childOper);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }
    return GNCDB_SUCCESS;
}

/**
 * @description: 收集stmt中表达式中的等值条件加入并查集，非相等比较条件加入nEqExprs
 * @param {HashMap} *ufNodes 并查集节点哈希表
 * @param {HashMap} *ufSets 并查集哈希表
 * @param {varArrayList} *nEqExprs 所有非相等表达式集合
 * @param {SelectStmt*} selectStmt
 * @return {*}
 */
int collectUFSets_v2(HashMap *ufNodes, HashMap *ufSets, varArrayList *nEqExprs, SelectStmt *selectStmt)
{
    int         rc       = GNCDB_SUCCESS;
    JoinNode   *joinNode = NULL;
    Expression *expr     = NULL;
    int         i        = 0;
    int         j        = 0;

    /* 1.收集where中的条件 */
    if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL)
    {
        rc = exprToUFNode(selectStmt->filterStmt->condition, nEqExprs, ufNodes, ufSets);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }

    /* 2.收集having中的条件 */
    if (selectStmt->havingStmt != NULL && selectStmt->havingStmt->condition != NULL)
    {
        rc = exprToUFNode(selectStmt->havingStmt->condition, nEqExprs, ufNodes, ufSets);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }

    /* 3.收集join中的条件 */
    for (i = 0; i < selectStmt->joinTables->joinNodes->elementCount; i++)
    {
        joinNode = (JoinNode *)varArrayListGetPointer(selectStmt->joinTables->joinNodes, i);
        if (joinNode->eqExprs != NULL && joinNode->eqExprs->elementCount != 0)
        {
            for (j = 0; j < joinNode->eqExprs->elementCount; j++)
            {
                expr = (Expression *)varArrayListGetPointer(joinNode->eqExprs, j);
                rc   = exprToUFNode(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
        if (joinNode->eqPKExprs != NULL && joinNode->eqPKExprs->elementCount != 0)
        {
            for (j = 0; j < joinNode->eqPKExprs->elementCount; j++)
            {
                expr = (Expression *)varArrayListGetPointer(joinNode->eqPKExprs, j);
                rc   = exprToUFNode(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
        if (joinNode->otherExprs != NULL && joinNode->otherExprs->elementCount != 0)
        {
            for (j = 0; j < joinNode->otherExprs->elementCount; j++)
            {
                expr = (Expression *)varArrayListGetPointer(joinNode->otherExprs, j);
                rc   = exprToUFNode(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
    }

    return rc;
}

int collectUFSets_v3(varArrayList *ufNodes, varArrayList *ufSets, varArrayList *nEqExprs, SelectStmt *selectStmt)
{
    int         rc       = GNCDB_SUCCESS;
    JoinNode   *joinNode = NULL;
    Expression *expr     = NULL;
    int         i        = 0;
    int         j        = 0;

    /* 1.收集where中的条件 */
    if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL)
    {
        rc = exprToUFNode_v3(selectStmt->filterStmt->condition, nEqExprs, ufNodes, ufSets);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }

    /* 2.收集having中的条件 */
    if (selectStmt->havingStmt != NULL && selectStmt->havingStmt->condition != NULL)
    {
        rc = exprToUFNode_v3(selectStmt->havingStmt->condition, nEqExprs, ufNodes, ufSets);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }

    /* 3.收集join中的条件 */
    for (i = 0; i < selectStmt->joinTables->joinNodes->elementCount; i++)
    {
        joinNode = (JoinNode *)varArrayListGetPointer(selectStmt->joinTables->joinNodes, i);
        if (joinNode->eqExprs != NULL && joinNode->eqExprs->elementCount != 0)
        {
            for (j = 0; j < joinNode->eqExprs->elementCount; j++)
            {
                expr = (Expression *)varArrayListGetPointer(joinNode->eqExprs, j);
                rc   = exprToUFNode_v3(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
        if (joinNode->eqPKExprs != NULL && joinNode->eqPKExprs->elementCount != 0)
        {
            for (j = 0; j < joinNode->eqPKExprs->elementCount; j++)
            {
                expr = (Expression *)varArrayListGetPointer(joinNode->eqPKExprs, j);
                rc   = exprToUFNode_v3(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
        if (joinNode->otherExprs != NULL && joinNode->otherExprs->elementCount != 0)
        {
            for (j = 0; j < joinNode->otherExprs->elementCount; j++)
            {
                expr = (Expression *)varArrayListGetPointer(joinNode->otherExprs, j);
                rc   = exprToUFNode_v3(expr, nEqExprs, ufNodes, ufSets);
                if (rc != GNCDB_SUCCESS)
                {
                    return rc;
                }
            }
        }
    }

    return rc;
}

/**
 * @description:  根据并查集进行不等表达式的传播
 * @param {HashMap} *ufNodes 节点map
 * @param {HashMap} *ufSets 并查集map
 * @param {varArrayList} *nEqExprs 不相等条件List
 * @param {varArrayList*} newExprs 传播构造的新的比较表达式
 * @return {*}
 */
int nEqExprPropagation(HashMap *ufNodes, HashMap *ufSets, varArrayList *nEqExprs, varArrayList *newExprs)
{
    // int             rc          = GNCDB_SUCCESS;
    ComparisonExpr *compExpr    = NULL;
    ComparisonExpr *newCompExpr = NULL;
    FieldExpr      *fieldExpr   = NULL;
    FieldExpr      *fieldExpr2  = NULL;
    ValueExpr      *valueExpr   = NULL;
    UFNode         *node        = NULL;
    UFNode         *node2       = NULL;
    UFNode         *propNode    = NULL;
    UFNode         *parent      = NULL;
    UFNode         *parent2     = NULL;
    UFSet          *set         = NULL;
    UFSet          *constSet    = NULL;
    UFSet          *varSet      = NULL;

    char tmpStr1[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};
    char tmpStr2[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};

    /*1.遍历每一个非等值条件，*/
    for (int i = 0; i < nEqExprs->elementCount; i++)
    {
        compExpr = (ComparisonExpr *)varArrayListGetPointer(nEqExprs, i);
        /*1.1如果是filed，即都是列，若某一列是常量另一列是列，则进行常量替换*/
        if (compExpr->comp >= CMPOP_GREAT_THAN || compExpr->comp <= CMPOP_EQUAL_TO)
        {
            continue;
        }
        if (compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_FIELD)
        {
            /*左右都是列，比如：a < b*/
            fieldExpr  = (FieldExpr *)compExpr->left;
            fieldExpr2 = (FieldExpr *)compExpr->right;
            sprintf(tmpStr1, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
            sprintf(tmpStr2, "%s.%s", fieldExpr2->tableName, fieldExpr2->fieldName);
            node  = (UFNode *)hashMapGet(ufNodes, tmpStr1);
            node2 = (UFNode *)hashMapGet(ufNodes, tmpStr2);
            if (!node && !node2)
            {
                continue;
            }
            else if (node && node2)
            {
                parent  = UFSetFind(node);
                parent2 = UFSetFind(node2);

                /* 如果两个节点在同一个并查集中，意味着既存在a < b，也存在a = b，发生了错误 */
                if (strcmp(parent->expeStr, parent2->expeStr) == 0)
                {
                    return GNCDB_INTERNAL;
                }

                /* 如果两个节点都是常量
                   例如: 已知a = 1, b = 2, 当前a < b
                */
                // todo 判断条件是否不合法
                if (parent->type == CONST && parent2->type == CONST)
                {
                    continue;
                }

                /* 如果两个节点都是变量
                   例如： 已知a = c, b = d, 当前a < b，无法传播，跳过
                */
                if (parent->type == VAR && parent2->type == VAR)
                {
                    continue;
                }

                /* 如果一个是变量，一个是常量，
                   比如：已知a = b = c, d = e = 5, 当前a < d
                   需要将变量并查集中节点传播
                */
                if (parent->type == VAR && parent2->type == CONST)
                {
                    varSet   = hashMapGet(ufSets, parent->expeStr);
                    constSet = hashMapGet(ufSets, parent2->expeStr);
                }
                else if (parent->type == CONST && parent2->type == VAR)
                {
                    constSet = hashMapGet(ufSets, parent->expeStr);
                    varSet   = hashMapGet(ufSets, parent2->expeStr);
                }
                for (int i = 0; i < varSet->nodes->elementCount; i++)
                {
                    propNode = (UFNode *)varArrayListGetPointer(varSet->nodes, i);
                    if (strcmp(propNode->expeStr, tmpStr1) == 0 || strcmp(propNode->expeStr, tmpStr2) == 0)
                    {
                        /* 跳过本来就存在的表达式：a < b */
                        continue;
                    }
                    newCompExpr        = exprCreate(ETG_COMPARISON);
                    newCompExpr->comp  = compExpr->comp;
                    newCompExpr->left  = exprDeepCopy(propNode->expression);
                    newCompExpr->right = exprDeepCopy(constSet->root->expression);
                    varArrayListAddPointer(newExprs, newCompExpr);
                }
            }
            else if (node && !node2)
            {
                /* a < b, a在并查集中，b不在，将a的条件传播给b */
                parent = UFSetFind(node);
                if (parent->type == VAR)
                {
                    continue;
                }
                newCompExpr        = exprCreate(ETG_COMPARISON);
                newCompExpr->comp  = REVERSE_COMP(compExpr->comp);
                newCompExpr->left  = exprDeepCopy((Expression *)fieldExpr2);
                newCompExpr->right = exprDeepCopy(parent->expression);
                varArrayListAddPointer(newExprs, newCompExpr);
            }
            else if (!node && node2)
            {
                /* a < b, b在并查集中，a不在，将b的条件传播给a */
                parent2 = UFSetFind(node2);
                if (parent2->type == VAR)
                {
                    continue;
                }
                newCompExpr        = exprCreate(ETG_COMPARISON);
                newCompExpr->comp  = compExpr->comp;
                newCompExpr->left  = exprDeepCopy((Expression *)fieldExpr);
                newCompExpr->right = exprDeepCopy(parent2->expression);
                varArrayListAddPointer(newExprs, newCompExpr);
            }
            continue;
        }

        if (compExpr->left->type == ETG_FIELD && compExpr->right->type != ETG_FIELD)
        {
            /*  左列右值，类似：a < 1 */
            fieldExpr = (FieldExpr *)compExpr->left;
            valueExpr = (ValueExpr *)compExpr->right;
        }
        else if (compExpr->left->type != ETG_FIELD && compExpr->right->type == ETG_FIELD)
        {
            /* 左列右值，类似：1 > a */
            fieldExpr = (FieldExpr *)compExpr->right;
            valueExpr = (ValueExpr *)compExpr->left;
        }
        sprintf(tmpStr1, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
        /* 不存在对应的Node，跳过 */
        node = (UFNode *)hashMapGet(ufNodes, tmpStr1);
        if (!node)
        {
            continue;
        }

        parent = UFSetFind(node);

        /* 不是变量表达式，跳过 */
        // todo 即使是value，也可以进行比较，进行表达式简化
        if (parent->type != VAR)
        {
            continue;
        }

        /* 之前的相等并查集中是变量，比如：a = b = c */
        set = hashMapGet(ufSets, parent->expeStr);
        for (int i = 0; i < set->nodes->elementCount; i++)
        {
            propNode = (UFNode *)varArrayListGetPointer(set->nodes, i);
            if (strcmp(propNode->expeStr, tmpStr1) == 0)
            {
                /* 跳过本来就存在的表达式：a < 1 */
                continue;
            }
            newCompExpr        = exprCreate(ETG_COMPARISON);
            newCompExpr->comp  = compExpr->comp;
            newCompExpr->left  = exprDeepCopy(propNode->expression);
            newCompExpr->right = exprDeepCopy((Expression *)valueExpr);
            varArrayListAddPointer(newExprs, newCompExpr);
        }
    }
    return GNCDB_SUCCESS;
}

/**
 * @description: 根据并查集进行nEqExprs中的不等表达式的传播
 * @param {HashMap} *ufNodes 所有并查集节点的哈希表
 * @param {HashMap} *ufSets 所有并查集的哈希表
 * @param {varArrayList} *nEqExprs 所有不相等的条件List
 * @param {SelectStmt*} selectStmt
 * @return {*}
 */
int nEqExprPropagation_v2(HashMap *ufNodes, HashMap *ufSets, varArrayList *nEqExprs, SelectStmt *selectStmt)
{
    ComparisonExpr  *compExpr    = NULL;
    ComparisonExpr  *newCompExpr = NULL;
    ConjunctionExpr *conjExpr    = NULL;
    FieldExpr       *fieldExpr   = NULL;
    FieldExpr       *fieldExpr2  = NULL;
    ValueExpr       *valueExpr   = NULL;
    UFNode          *node        = NULL;
    UFNode          *node2       = NULL;
    UFNode          *propNode    = NULL;
    UFNode          *parent      = NULL;
    UFNode          *parent2     = NULL;
    UFSet           *set         = NULL;
    UFSet           *constSet    = NULL;
    UFSet           *varSet      = NULL;

    char tmpStr1[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};
    char tmpStr2[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};

    /* 检查selectStmt中的where子句是否是and连接的联结表达式 */
    if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL &&
        selectStmt->filterStmt->condition->type == ETG_CONJUNCTION)
    {
        conjExpr = (ConjunctionExpr *)selectStmt->filterStmt->condition;
    }
    else
    {
        conjExpr                  = (ConjunctionExpr *)my_malloc0(sizeof(ConjunctionExpr));
        conjExpr->type            = ETG_CONJUNCTION;
        conjExpr->conjunctionType = CJET_AND;
        conjExpr->children        = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL)
        {
            /* 如果原有的where子句不是联结条件，则将其添加到新的联结条件中 */
            varArrayListAddPointer(conjExpr->children, selectStmt->filterStmt->condition);
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    /* 遍历每个不相等条件，判断根据并查集能否传播 */
    for (int i = 0; i < nEqExprs->elementCount; i++)
    {
        compExpr = (ComparisonExpr *)varArrayListGetPointer(nEqExprs, i);
        if (compExpr->comp >= CMPOP_GREAT_THAN || compExpr->comp <= CMPOP_EQUAL_TO)
        {
            continue;
        }
        if (compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_FIELD)
        {
            /* 左右都是列，比如：a < b */
            fieldExpr  = (FieldExpr *)compExpr->left;
            fieldExpr2 = (FieldExpr *)compExpr->right;
            sprintf(tmpStr1, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
            sprintf(tmpStr2, "%s.%s", fieldExpr2->tableName, fieldExpr2->fieldName);
            node  = (UFNode *)hashMapGet(ufNodes, tmpStr1);
            node2 = (UFNode *)hashMapGet(ufNodes, tmpStr2);
            if (!node && !node2)
            {
                continue;
            }
            else if (node && node2)
            {
                parent  = UFSetFind(node);
                parent2 = UFSetFind(node2);

                /* 如果两个节点在同一个并查集中，意味着既存在a < b，也存在a = b，发生了错误 */
                if (strcmp(parent->expeStr, parent2->expeStr) == 0)
                {
                    return GNCDB_INTERNAL;
                }

                /* 如果两个节点都是常量
                   例如: 已知a = 1, b = 2, 当前a < b
                */
                // todo 判断条件是否不合法
                if (parent->type == CONST && parent2->type == CONST)
                {
                    continue;
                }

                /* 如果两个节点都是变量
                   例如： 已知a = c, b = d, 当前a < b，无法传播，跳过
                */
                if (parent->type == VAR && parent2->type == VAR)
                {
                    continue;
                }

                /* 如果一个是变量，一个是常量，
                   比如：已知a = b = c, d = e = 5, 当前a < d
                   需要将变量并查集中节点传播
                */
                if (parent->type == VAR && parent2->type == CONST)
                {
                    varSet   = hashMapGet(ufSets, parent->expeStr);
                    constSet = hashMapGet(ufSets, parent2->expeStr);
                }
                else if (parent->type == CONST && parent2->type == VAR)
                {
                    constSet = hashMapGet(ufSets, parent->expeStr);
                    varSet   = hashMapGet(ufSets, parent2->expeStr);
                }
                for (int i = 0; i < varSet->nodes->elementCount; i++)
                {
                    propNode = (UFNode *)varArrayListGetPointer(varSet->nodes, i);
                    if (strcmp(propNode->expeStr, tmpStr1) == 0 || strcmp(propNode->expeStr, tmpStr2) == 0)
                    {
                        /* 跳过本来就存在的表达式：a < b */
                        continue;
                    }
                    newCompExpr        = exprCreate(ETG_COMPARISON);
                    newCompExpr->comp  = compExpr->comp;
                    newCompExpr->left  = exprDeepCopy(propNode->expression);
                    newCompExpr->right = exprDeepCopy(constSet->root->expression);
                    varArrayListAddPointer(conjExpr->children, newCompExpr);
                }
            }
            else if (node && !node2)
            {
                /* a < b, a在并查集中，b不在，将a的条件传播给b */
                parent = UFSetFind(node);
                if (parent->type == VAR)
                {
                    continue;
                }
                newCompExpr        = exprCreate(ETG_COMPARISON);
                newCompExpr->comp  = REVERSE_COMP(compExpr->comp);
                newCompExpr->left  = exprDeepCopy((Expression *)fieldExpr2);
                newCompExpr->right = exprDeepCopy(parent->expression);
                varArrayListAddPointer(conjExpr->children, newCompExpr);
            }
            else if (!node && node2)
            {
                /* a < b, b在并查集中，a不在，将b的条件传播给a */
                parent2 = UFSetFind(node2);
                if (parent2->type == VAR)
                {
                    continue;
                }
                newCompExpr        = exprCreate(ETG_COMPARISON);
                newCompExpr->comp  = compExpr->comp;
                newCompExpr->left  = exprDeepCopy((Expression *)fieldExpr);
                newCompExpr->right = exprDeepCopy(parent2->expression);
                varArrayListAddPointer(conjExpr->children, newCompExpr);
            }
            continue;
        }

        if (compExpr->left->type == ETG_FIELD && compExpr->right->type != ETG_FIELD)
        {
            /*  左列右值，类似：a < 1 */
            fieldExpr = (FieldExpr *)compExpr->left;
            valueExpr = (ValueExpr *)compExpr->right;
        }
        else if (compExpr->left->type != ETG_FIELD && compExpr->right->type == ETG_FIELD)
        {
            /* 左列右值，类似：1 > a */
            fieldExpr = (FieldExpr *)compExpr->right;
            valueExpr = (ValueExpr *)compExpr->left;
        }
        sprintf(tmpStr1, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
        /* 不存在对应的Node，跳过 */
        if (!hashMapExists(ufNodes, tmpStr1))
        {
            continue;
        }

        node   = (UFNode *)hashMapGet(ufNodes, tmpStr1);
        parent = UFSetFind(node);

        /* 不是变量表达式，跳过 */
        // todo 即使是value，也可以进行比较，进行表达式简化
        if (parent->type != VAR)
        {
            continue;
        }

        /* 之前的相等并查集中是变量，比如：a = b = c */
        set = hashMapGet(ufSets, parent->expeStr);
        for (int i = 0; i < set->nodes->elementCount; i++)
        {
            propNode = (UFNode *)varArrayListGetPointer(set->nodes, i);
            if (strcmp(propNode->expeStr, tmpStr1) == 0)
            {
                /* 跳过本来就存在的表达式：a < 1 */
                continue;
            }
            newCompExpr        = exprCreate(ETG_COMPARISON);
            newCompExpr->comp  = compExpr->comp;
            newCompExpr->left  = exprDeepCopy(propNode->expression);
            newCompExpr->right = exprDeepCopy((Expression *)valueExpr);
            varArrayListAddPointer(conjExpr->children, newCompExpr);
        }
    }

    if (conjExpr->children->elementCount == 0)
    {
        /* 如果联结条件中没有任何表达式，则将销毁 */
        exprDestroy((Expression *)conjExpr);
    }
    else
    {
        if (selectStmt->filterStmt == NULL)
        {
            selectStmt->filterStmt            = (FilterStmt *)my_malloc0(sizeof(FilterStmt));
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    return GNCDB_SUCCESS;
}

int nEqExprPropagation_v3(varArrayList *ufNodes, varArrayList *ufSets, varArrayList *nEqExprs, SelectStmt *selectStmt)
{
    ComparisonExpr  *compExpr    = NULL;
    ComparisonExpr  *newCompExpr = NULL;
    ConjunctionExpr *conjExpr    = NULL;
    FieldExpr       *fieldExpr   = NULL;
    FieldExpr       *fieldExpr2  = NULL;
    ValueExpr       *valueExpr   = NULL;
    UFNode          *node        = NULL;
    UFNode          *node2       = NULL;
    UFNode          *iterNode    = NULL;
    UFNode          *propNode    = NULL;
    UFNode          *parent      = NULL;
    UFNode          *parent2     = NULL;
    UFSet           *set         = NULL;
    UFSet           *iterSet     = NULL;
    UFSet           *constSet    = NULL;
    UFSet           *varSet      = NULL;

    char tmpStr1[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};
    char tmpStr2[2 * TABLENAME_FIELD_MAXLEN + 2] = {0};

    /* 检查selectStmt中的where子句是否是and连接的联结表达式 */
    if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL &&
        selectStmt->filterStmt->condition->type == ETG_CONJUNCTION)
    {
        conjExpr = (ConjunctionExpr *)selectStmt->filterStmt->condition;
    }
    else
    {
        conjExpr                  = (ConjunctionExpr *)my_malloc0(sizeof(ConjunctionExpr));
        conjExpr->type            = ETG_CONJUNCTION;
        conjExpr->conjunctionType = CJET_AND;
        conjExpr->children        = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL)
        {
            /* 如果原有的where子句不是联结条件，则将其添加到新的联结条件中 */
            varArrayListAddPointer(conjExpr->children, selectStmt->filterStmt->condition);
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    /* 遍历每个不相等条件，判断根据并查集能否传播 */
    for (int i = 0; i < nEqExprs->elementCount; i++)
    {
        compExpr = (ComparisonExpr *)varArrayListGetPointer(nEqExprs, i);
        if (compExpr->comp >= CMPOP_GREAT_THAN || compExpr->comp <= CMPOP_EQUAL_TO)
        {
            continue;
        }
        if (compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_FIELD)
        {
            /* 左右都是列，比如：a < b */
            fieldExpr  = (FieldExpr *)compExpr->left;
            fieldExpr2 = (FieldExpr *)compExpr->right;
            sprintf(tmpStr1, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
            sprintf(tmpStr2, "%s.%s", fieldExpr2->tableName, fieldExpr2->fieldName);
            node = node2 = NULL;
            for (int i = 0; i < ufNodes->elementCount; i++)
            {
                iterNode = (UFNode *)varArrayListGetPointer(ufNodes, i);
                if (strcmp(iterNode->expeStr, tmpStr1) == 0)
                {
                    node = iterNode;
                }
                if (strcmp(iterNode->expeStr, tmpStr2) == 0)
                {
                    node2 = iterNode;
                }
                if (node && node2)
                {
                    break;
                }
            }
            if (!node && !node2)
            {
                continue;
            }
            else if (node && node2)
            {
                parent  = UFSetFind(node);
                parent2 = UFSetFind(node2);

                /* 如果两个节点在同一个并查集中，意味着既存在a < b，也存在a = b，发生了错误 */
                if (strcmp(parent->expeStr, parent2->expeStr) == 0)
                {
                    return GNCDB_INTERNAL;
                }

                /* 如果两个节点都是常量
                   例如: 已知a = 1, b = 2, 当前a < b
                */
                // todo 判断条件是否不合法
                if (parent->type == CONST && parent2->type == CONST)
                {
                    continue;
                }

                /* 如果两个节点都是变量
                   例如： 已知a = c, b = d, 当前a < b，无法传播，跳过
                */
                if (parent->type == VAR && parent2->type == VAR)
                {
                    continue;
                }

                /* 如果一个是变量，一个是常量，
                   比如：已知a = b = c, d = e = 5, 当前a < d
                   需要将变量并查集中节点传播
                */
                varSet = constSet = NULL;
                if (parent->type == VAR && parent2->type == CONST)
                {
                    for (int i = 0; i < ufSets->elementCount; i++)
                    {
                        iterSet = (UFSet *)varArrayListGetPointer(ufSets, i);
                        if (strcmp(iterSet->root->expeStr, parent->expeStr) == 0)
                        {
                            varSet = iterSet;
                        }
                        if (strcmp(iterSet->root->expeStr, parent2->expeStr) == 0)
                        {
                            constSet = iterSet;
                        }
                        if (varSet && constSet)
                        {
                            break;
                        }
                    }
                }
                else if (parent->type == CONST && parent2->type == VAR)
                {
                    for (int i = 0; i < ufSets->elementCount; i++)
                    {
                        iterSet = (UFSet *)varArrayListGetPointer(ufSets, i);
                        if (strcmp(iterSet->root->expeStr, parent->expeStr) == 0)
                        {
                            constSet = iterSet;
                        }
                        if (strcmp(iterSet->root->expeStr, parent2->expeStr) == 0)
                        {
                            varSet = iterSet;
                        }
                        if (varSet && constSet)
                        {
                            break;
                        }
                    }
                }
                for (int i = 0; i < varSet->nodes->elementCount; i++)
                {
                    propNode = (UFNode *)varArrayListGetPointer(varSet->nodes, i);
                    if (strcmp(propNode->expeStr, tmpStr1) == 0 || strcmp(propNode->expeStr, tmpStr2) == 0)
                    {
                        /* 跳过本来就存在的表达式：a < b */
                        continue;
                    }
                    newCompExpr        = exprCreate(ETG_COMPARISON);
                    newCompExpr->comp  = compExpr->comp;
                    newCompExpr->left  = exprDeepCopy(propNode->expression);
                    newCompExpr->right = exprDeepCopy(constSet->root->expression);
                    varArrayListAddPointer(conjExpr->children, newCompExpr);
                }
            }
            else if (node && !node2)
            {
                /* a < b, a在并查集中，b不在，将a的条件传播给b */
                parent = UFSetFind(node);
                if (parent->type == VAR)
                {
                    continue;
                }
                newCompExpr        = exprCreate(ETG_COMPARISON);
                newCompExpr->comp  = REVERSE_COMP(compExpr->comp);
                newCompExpr->left  = exprDeepCopy((Expression *)fieldExpr2);
                newCompExpr->right = exprDeepCopy(parent->expression);
                varArrayListAddPointer(conjExpr->children, newCompExpr);
            }
            else if (!node && node2)
            {
                /* a < b, b在并查集中，a不在，将b的条件传播给a */
                parent2 = UFSetFind(node2);
                if (parent2->type == VAR)
                {
                    continue;
                }
                newCompExpr        = exprCreate(ETG_COMPARISON);
                newCompExpr->comp  = compExpr->comp;
                newCompExpr->left  = exprDeepCopy((Expression *)fieldExpr);
                newCompExpr->right = exprDeepCopy(parent2->expression);
                varArrayListAddPointer(conjExpr->children, newCompExpr);
            }
            continue;
        }

        if (compExpr->left->type == ETG_FIELD && compExpr->right->type != ETG_FIELD)
        {
            /*  左列右值，类似：a < 1 */
            fieldExpr = (FieldExpr *)compExpr->left;
            valueExpr = (ValueExpr *)compExpr->right;
        }
        else if (compExpr->left->type != ETG_FIELD && compExpr->right->type == ETG_FIELD)
        {
            /* 左列右值，类似：1 > a */
            fieldExpr = (FieldExpr *)compExpr->right;
            valueExpr = (ValueExpr *)compExpr->left;
        }
        sprintf(tmpStr1, "%s.%s", fieldExpr->tableName, fieldExpr->fieldName);
        node = NULL;
        for (int i = 0; i < ufNodes->elementCount; i++)
        {
            iterNode = (UFNode *)varArrayListGetPointer(ufNodes, i);
            if (strcmp(iterNode->expeStr, tmpStr1) == 0)
            {
                node = iterNode;
                break;
            }
        }
        /* 不存在对应的Node，跳过 */
        if (node == NULL)
        {
            continue;
        }
        parent = UFSetFind(node);

        /* 不是变量表达式，跳过 */
        // todo 即使是value，也可以进行比较，进行表达式简化
        if (parent->type != VAR)
        {
            continue;
        }

        /* 之前的相等并查集中是变量，比如：a = b = c */
        for (int i = 0; i < ufSets->elementCount; i++)
        {
            iterSet = (UFSet *)varArrayListGetPointer(ufSets, i);
            if (strcmp(iterSet->root->expeStr, parent->expeStr) != 0)
            {
                continue;
            }
            set = iterSet;
            break;
        }
        for (int i = 0; i < set->nodes->elementCount; i++)
        {
            propNode = (UFNode *)varArrayListGetPointer(set->nodes, i);
            if (strcmp(propNode->expeStr, tmpStr1) == 0)
            {
                /* 跳过本来就存在的表达式：a < 1 */
                continue;
            }
            newCompExpr        = exprCreate(ETG_COMPARISON);
            newCompExpr->comp  = compExpr->comp;
            newCompExpr->left  = exprDeepCopy(propNode->expression);
            newCompExpr->right = exprDeepCopy((Expression *)valueExpr);
            varArrayListAddPointer(conjExpr->children, newCompExpr);
        }
    }

    if (conjExpr->children->elementCount == 0)
    {
        /* 如果联结条件中没有任何表达式，则将销毁 */
        exprDestroy((Expression *)conjExpr);
    }
    else
    {
        if (selectStmt->filterStmt == NULL)
        {
            selectStmt->filterStmt            = (FilterStmt *)my_malloc0(sizeof(FilterStmt));
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    return GNCDB_SUCCESS;
}

/**
 * @description: 遍历各个并查集，传播集合内的各个表达式
 * @param {HashMap} *ufSets ufSet的hashmap
 * @param {varArrayList} *newExprs 播构造的新的比较表达式
 * @return {*}
 */
int eqExprPropagation(HashMap *ufSets, varArrayList *newExprs)
{
    int              rc          = GNCDB_SUCCESS;
    HashMapIterator *iter        = NULL;
    UFSet           *set         = NULL;
    UFNode          *node        = NULL;
    ComparisonExpr  *newCompExpr = NULL;
    iter                         = createHashMapIterator(ufSets);
    while (hasNextHashMapIterator(iter))
    {
        iter = nextHashMapIterator(iter);
        set  = (UFSet *)iter->entry->value;
        if (set->root->type == VAR)
        {
            continue;
        }
        for (int i = 0; i < set->nodes->elementCount; i++)
        {
            node = (UFNode *)varArrayListGetPointer(set->nodes, i);
            /*跳过本来就存在的表达式：a = 1的a*/
            if (node->isOriginal)
            {
                continue;
            }
            /*跳过常量表达式：a = 1的1*/
            if (node->type == CONST)
            {
                continue;
            }
            newCompExpr        = exprCreate(ETG_COMPARISON);
            newCompExpr->comp  = CMPOP_EQUAL_TO;
            newCompExpr->left  = exprDeepCopy(node->expression);
            newCompExpr->right = exprDeepCopy(set->root->expression);
            varArrayListAddPointer(newExprs, newCompExpr);
        }
    }
    freeHashMapIterator(&iter);
    return rc;
}

/**
 * @description: 遍历各个并查集，传播集合内的相等表达式，添加到selectStmt中where子句中
 * @param {HashMap} *ufSets 并查集的哈希表
 * @param {SelectStmt*} selectStmt
 * @return {*}
 */
int eqExprPropagation_v2(HashMap *ufSets, SelectStmt *selectStmt)
{
    int              rc          = GNCDB_SUCCESS;
    HashMapIterator *iter        = NULL;
    UFSet           *set         = NULL;
    UFNode          *node        = NULL;
    ComparisonExpr  *newCompExpr = NULL;
    ConjunctionExpr *conjExpr    = NULL;

    /* 检查selectStmt中的where子句是否是and连接的联结表达式 */
    if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL &&
        selectStmt->filterStmt->condition->type == ETG_CONJUNCTION)
    {
        conjExpr = (ConjunctionExpr *)selectStmt->filterStmt->condition;
    }
    else
    {
        conjExpr                  = (ConjunctionExpr *)my_malloc0(sizeof(ConjunctionExpr));
        conjExpr->type            = ETG_CONJUNCTION;
        conjExpr->conjunctionType = CJET_AND;
        conjExpr->children        = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL)
        {
            /* 如果原有的where子句不是联结条件，则将其添加到新的联结条件中 */
            varArrayListAddPointer(conjExpr->children, selectStmt->filterStmt->condition);
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    /* 遍历每个并查集，构造新的相等条件，并添加到where子句的表达式中 */
    iter = createHashMapIterator(ufSets);
    while (hasNextHashMapIterator(iter))
    {
        iter = nextHashMapIterator(iter);
        set  = (UFSet *)iter->entry->value;
        if (set->root->type == VAR)
        {
            continue;
        }
        for (int i = 0; i < set->nodes->elementCount; i++)
        {
            node = (UFNode *)varArrayListGetPointer(set->nodes, i);
            /* 跳过本来就存在的表达式：a = 1的a */
            if (node->isOriginal)
            {
                continue;
            }
            /* 跳过常量表达式：a = 1的1 */
            if (node->type == CONST)
            {
                continue;
            }
            newCompExpr        = exprCreate(ETG_COMPARISON);
            newCompExpr->comp  = CMPOP_EQUAL_TO;
            newCompExpr->left  = exprDeepCopy(node->expression);
            newCompExpr->right = exprDeepCopy(set->root->expression);
            varArrayListAddPointer(conjExpr->children, newCompExpr);
        }
    }
    freeHashMapIterator(&iter);

    if (conjExpr->children->elementCount == 0)
    {
        /* 如果联结条件中没有任何表达式，则将销毁 */
        exprDestroy((Expression *)conjExpr);
    }
    else
    {
        if (selectStmt->filterStmt == NULL)
        {
            selectStmt->filterStmt            = (FilterStmt *)my_malloc0(sizeof(FilterStmt));
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    return rc;
}

int eqExprPropagation_v3(varArrayList *ufSets, SelectStmt *selectStmt)
{
    int              rc          = GNCDB_SUCCESS;
    UFSet           *set         = NULL;
    UFNode          *node        = NULL;
    ComparisonExpr  *newCompExpr = NULL;
    ConjunctionExpr *conjExpr    = NULL;

    /* 检查selectStmt中的where子句是否是and连接的联结表达式 */
    if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL &&
        selectStmt->filterStmt->condition->type == ETG_CONJUNCTION)
    {
        conjExpr = (ConjunctionExpr *)selectStmt->filterStmt->condition;
    }
    else
    {
        conjExpr                  = (ConjunctionExpr *)my_malloc0(sizeof(ConjunctionExpr));
        conjExpr->type            = ETG_CONJUNCTION;
        conjExpr->conjunctionType = CJET_AND;
        conjExpr->children        = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
        if (selectStmt->filterStmt != NULL && selectStmt->filterStmt->condition != NULL)
        {
            /* 如果原有的where子句不是联结条件，则将其添加到新的联结条件中 */
            varArrayListAddPointer(conjExpr->children, selectStmt->filterStmt->condition);
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    /* 遍历每个并查集，构造新的相等条件，并添加到where子句的表达式中 */
    for (int i = 0; i < ufSets->elementCount; i++)
    {
        set = (UFSet *)varArrayListGetPointer(ufSets, i);
        if (set->root->type == VAR)
        {
            continue;
        }
        for (int i = 0; i < set->nodes->elementCount; i++)
        {
            node = (UFNode *)varArrayListGetPointer(set->nodes, i);
            /* 跳过本来就存在的表达式：a = 1的a */
            if (node->isOriginal)
            {
                continue;
            }
            /* 跳过常量表达式：a = 1的1 */
            if (node->type == CONST)
            {
                continue;
            }
            newCompExpr        = exprCreate(ETG_COMPARISON);
            newCompExpr->comp  = CMPOP_EQUAL_TO;
            newCompExpr->left  = exprDeepCopy(node->expression);
            newCompExpr->right = exprDeepCopy(set->root->expression);
            varArrayListAddPointer(conjExpr->children, newCompExpr);
        }
    }

    if (conjExpr->children->elementCount == 0)
    {
        /* 如果联结条件中没有任何表达式，则将销毁 */
        exprDestroy((Expression *)conjExpr);
    }
    else
    {
        if (selectStmt->filterStmt == NULL)
        {
            selectStmt->filterStmt            = (FilterStmt *)my_malloc0(sizeof(FilterStmt));
            selectStmt->filterStmt->condition = (Expression *)conjExpr;
        }
    }

    return rc;
}

/**
 * @description: 将新的表达式加入predicate算子中
 * @param {LogicalOperator} *oper
 * @param {varArrayList} *newExprs
 * @return {*}
 */
int addFilterOper(LogicalOperator *oper, varArrayList *newExprs)
{
    LogicalOperator *childOper = NULL;
    int              rc        = GNCDB_SUCCESS;
    Expression      *expr      = NULL;
    ConjunctionExpr *conjExpr  = NULL;

    /*1.如果是过滤逻辑算子那么一次将newexprs中的谓词加进去*/
    if (oper->type == LO_PREDICATE)
    {
        expr = varArrayListGetPointer(oper->expressions, 0);
        if (expr->type == ETG_CONJUNCTION)
        {
            /*1.1如果是联结条件，直接添加到联结条件中*/
            conjExpr = (ConjunctionExpr *)expr;
        }
        else
        {
            /*1.2如果是其他条件，创建一个联结条件并替换原有*/
            conjExpr = exprCreate(ETG_CONJUNCTION);
            if (conjExpr == NULL)
            {
                return GNCDB_MEM;
            }
            conjExpr->children = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
            if (conjExpr->children == NULL)
            {
                exprPtrDestroy((void **)&conjExpr);
                return GNCDB_MEM;
            }
            varArrayListAddPointer(conjExpr->children, expr);
            varArrayListSetByIndexPointer(oper->expressions, 0, conjExpr);
        }
        /*1.3一次将新产生的谓词加入过滤谓词中*/
        for (int i = 0; i < newExprs->elementCount; i++)
        {
            expr = varArrayListGetPointer(newExprs, i);
            varArrayListAddPointer(conjExpr->children, expr);
        }
        return GNCDB_SUCCESS;
    }

    /*2.尝试对子算子应用新表达式，就是把新表达式下推给过滤算子*/
    for (int i = 0; i < oper->children->elementCount; i++)
    {
        childOper = (LogicalOperator *)varArrayListGetPointer(oper->children, i);
        rc        = addFilterOper(childOper, newExprs);
        if (rc != GNCDB_SUCCESS)
        {
            return rc;
        }
    }
    return rc;
}

int EqualityPropagationRewriter(SQLStageEvent *sqlEvent, LogicalOperator *oper)
{
    int              rc   = GNCDB_SUCCESS;
    UFSet           *set  = NULL;
    UFNode          *node = NULL;
    HashMapIterator *iter = NULL;

    /* 存储所有并查集结点的哈希表*/
    HashMap *ufNodes = hashMapCreate(STRKEY, 0, NULL);
    /* 存储并查集的根节点的哈希表*/
    HashMap *ufSets = hashMapCreate(STRKEY, 0, NULL);
    /* 存储非等值条件的表达式List*/
    varArrayList *nEqExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    /* 存储传播构造的新的比较表达式*/
    varArrayList *newCompExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);

    /* 1.收集所有的表达式，构造并查集*/
    rc = collectUFSets(ufNodes, ufSets, nEqExprs, oper);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

    /* 2.等值传播，将并查集中相等条件在集合内传播,新产生的表达式放入newCompExprs*/
    rc = eqExprPropagation(ufSets, newCompExprs);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

    /* 3.等值传播，将非等值条件的表达式根据并查集进行扩散*/
    rc = nEqExprPropagation(ufNodes, ufSets, nEqExprs, newCompExprs);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

    /* 4.将新的表达式加入predicate算子中*/
    if (newCompExprs->elementCount != 0)
    {
        rc = addFilterOper(oper, newCompExprs);
        if (rc != GNCDB_SUCCESS)
        {
            goto cleanup;
        }
    }

/* 5.释放内存*/
cleanup:
    if (ufNodes != NULL)
    {
        iter = createHashMapIterator(ufNodes);
        while (hasNextHashMapIterator(iter))
        {
            iter = nextHashMapIterator(iter);
            node = (UFNode *)iter->entry->value;
            UFNODE_DESTROY(node);
        }
        freeHashMapIterator(&iter);
        hashMapDestroy(&ufNodes);
    }
    if (ufSets != NULL)
    {
        iter = createHashMapIterator(ufSets);
        while (hasNextHashMapIterator(iter))
        {
            iter = nextHashMapIterator(iter);
            set  = (UFSet *)iter->entry->value;
            UFSET_DESTROY(set);
        }
        freeHashMapIterator(&iter);
        hashMapDestroy(&ufSets);
    }
    if (nEqExprs != NULL)
    {
        varArrayListDestroy(&nEqExprs);
    }
    if (newCompExprs != NULL)
    {
        varArrayListDestroy(&newCompExprs);
    }
    return rc;
}

/**
 * @description: 相较于原版本：EqualityPropagationRewriter，跳过了逻辑算子直接在selectStmt中进行
 * @param {SQLStageEvent*} sqlEvent
 * @return {*}
 */
int EqualityPropagationRewriter_v2(SQLStageEvent *sqlEvent)
{
    int              rc         = GNCDB_SUCCESS;
    UFSet           *set        = NULL;
    UFNode          *node       = NULL;
    HashMapIterator *iter       = NULL;
    SelectStmt      *selectStmt = NULL;
    HashMap         *ufNodes    = NULL; /* 存储所有并查集结点的哈希表 */
    HashMap         *ufSets     = NULL; /* 存储并查集的根节点的哈希表 */
    varArrayList    *nEqExprs   = NULL; /* 存储非等值条件的表达式List */

    /* 仅处理select语句 */
    if (sqlEvent->stmt->type != ST_SELECT)
    {
        return GNCDB_SUCCESS;
    }

    /* 单表无需进行优化 */
    selectStmt = (SelectStmt *)sqlEvent->stmt;
    if (selectStmt->joinTables->joinedTables->entryCount == 1)
    {
        return GNCDB_SUCCESS;
    }

    ufNodes  = hashMapCreate(STRKEY, 0, NULL);
    ufSets   = hashMapCreate(STRKEY, 0, NULL);
    nEqExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);

    //* 1.收集selectStmt中的所有表达式，构造并查集 */
    rc = collectUFSets_v2(ufNodes, ufSets, nEqExprs, selectStmt);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

    //* 2.等值传播：并查集中相等的条件在集合内部互相传播，并添加到select的where子句中 */
    rc = eqExprPropagation_v2(ufSets, selectStmt);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

    //* 3.等值传播：利用并查集中的相等条件，传播非相等条件，并添加到select的where子句中 */
    rc = nEqExprPropagation_v2(ufNodes, ufSets, nEqExprs, selectStmt);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

cleanup:
    if (ufNodes != NULL)
    {
        iter = createHashMapIterator(ufNodes);
        while (hasNextHashMapIterator(iter))
        {
            iter = nextHashMapIterator(iter);
            node = (UFNode *)iter->entry->value;
            UFNODE_DESTROY(node);
        }
        freeHashMapIterator(&iter);
        hashMapDestroy(&ufNodes);
    }
    if (ufSets != NULL)
    {
        iter = createHashMapIterator(ufSets);
        while (hasNextHashMapIterator(iter))
        {
            iter = nextHashMapIterator(iter);
            set  = (UFSet *)iter->entry->value;
            UFSET_DESTROY(set);
        }
        freeHashMapIterator(&iter);
        hashMapDestroy(&ufSets);
    }
    if (nEqExprs != NULL)
    {
        varArrayListDestroy(&nEqExprs);
    }
    return rc;
}

/**
 * @description: 相较于v2版本，将内部创建的哈希表（ufNodes，ufSets）简化为数组形式
 * @note 其内部调用的各种_v3后缀的函数相较于_v2版本唯一的不同就是替换HashMap为List
 * @param {SQLStageEvent} *sqlEvent
 * @return {*}
 */
int EqualityPropagationRewriter_v3(SQLStageEvent *sqlEvent)
{
    int           rc         = GNCDB_SUCCESS;
    UFSet        *set        = NULL;
    UFNode       *node       = NULL;
    SelectStmt   *selectStmt = NULL;
    varArrayList *ufNodes    = NULL; /* 存储所有并查集结点的哈希表 */
    varArrayList *ufSets     = NULL; /* 存储并查集的根节点的哈希表 */
    varArrayList *nEqExprs   = NULL; /* 存储非等值条件的表达式List */

    /* 仅处理select语句 */
    if (sqlEvent->stmt->type != ST_SELECT)
    {
        return GNCDB_SUCCESS;
    }

    /* 单表无需进行优化 */
    selectStmt = (SelectStmt *)sqlEvent->stmt;
    if (selectStmt->joinTables->joinedTables->entryCount == 1)
    {
        return GNCDB_SUCCESS;
    }

    ufNodes  = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    ufSets   = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    nEqExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);

    //* 1.收集selectStmt中的所有表达式，构造并查集 */
    rc = collectUFSets_v3(ufNodes, ufSets, nEqExprs, selectStmt);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

    //* 2.等值传播：并查集中相等的条件在集合内部互相传播，并添加到select的where子句中 */
    rc = eqExprPropagation_v3(ufSets, selectStmt);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

    //* 3.等值传播：利用并查集中的相等条件，传播非相等条件，并添加到select的where子句中 */
    rc = nEqExprPropagation_v3(ufNodes, ufSets, nEqExprs, selectStmt);
    if (rc != GNCDB_SUCCESS)
    {
        goto cleanup;
    }

cleanup:
    if (ufNodes != NULL)
    {
        for (int i = 0; i < ufNodes->elementCount; i++)
        {
            node = (UFNode *)varArrayListGetPointer(ufNodes, i);
            UFNODE_DESTROY(node);
        }
        varArrayListDestroy(&ufNodes);
    }
    if (ufSets != NULL)
    {
        for (int i = 0; i < ufSets->elementCount; i++)
        {
            set = (UFSet *)varArrayListGetPointer(ufSets, i);
            UFSET_DESTROY(set);
        }
        varArrayListDestroy(&ufSets);
    }
    if (nEqExprs != NULL)
    {
        varArrayListDestroy(&nEqExprs);
    }
    return rc;
}
