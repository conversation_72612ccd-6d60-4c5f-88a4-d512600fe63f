/*
 * @Author: zql <EMAIL>
 * @Date: 2025-01-09 09:32:41
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-06-11 16:17:23
 * @FilePath: /linux_dev/query_processing/sql/optimizer/histogram.c
 * @Description: 统计直方图相关函数接口的实现
 */
#include "histogram.h"
#include "typedefine.h"
#include "math.h"

/**
 * @description: 将DateTime转换为time_t
 * @param {DateTime} dt 日期时间结构体
 * @return {*} 时间戳
 */
time_t datetime_to_timestamp(DateTime dt)
{
  struct tm tm;
  tm.tm_year  = dt.date / 10000 - 1970;
  tm.tm_mon   = (dt.date % 10000) / 100 - 1;
  tm.tm_mday  = dt.date % 100;
  tm.tm_hour  = dt.time / 10000;
  tm.tm_min   = (dt.time % 10000) / 100;
  tm.tm_sec   = dt.time % 100;
  tm.tm_isdst = -1;
  return mktime(&tm);
}

/**
 * @description: 将一个字段的值添加到直方图中
 * @param {NumericalHistogram} *hist 直方图结构体指针
 * @param {Field} *field 字段结构体指针
 * @return {*} 状态码
 */
int add_field_value(NumericalHistogram *hist, Column *column, BYTE *record)
{
  int bucket_index = 0;
  // IntField      *int_field      = NULL;
  // RealField     *real_field     = NULL;
  int    int_value  = 0;
  double real_value = 0;
  // DateTimeField *datetime_field = NULL;
  // DateTime       min_dt, current_dt;
  // time_t         min_ts, current_ts;

  time_t width;
  switch (column->fieldType) {
    case FIELDTYPE_INTEGER: {
      // int_field = (IntField *)field;
      memcpy(&int_value, record + column->offset, sizeof(int));
      if (*((int *)hist->max) == *((int *)hist->min)) {
        bucket_index = 0;
      } else {
        width = *((int *)hist->width);
        // bucket_index = (int)((int_field->value - *((int *)hist->min)) / width);
        bucket_index = (int)((int_value - *((int *)hist->min)) / width);
      }
      if (bucket_index >= hist->bucket_num) {
        bucket_index = hist->bucket_num - 1;  // 确保索引不越界
      }
      hist->buckets[bucket_index]++;
      hist->tuple_sum++;
      break;
    }
    case FIELDTYPE_REAL: {
      // real_field = (RealField *)field;
      memcpy(&real_value, record + column->offset, sizeof(double));
      if (*((double *)hist->max) == *((double *)hist->min)) {
        bucket_index = 0;
      } else {
        double width = *((double *)hist->width);
        // bucket_index = (int)((real_field->value - *((double *)hist->min)) / width);
        bucket_index = (int)((real_value - *((double *)hist->min)) / width);
      }
      if (bucket_index >= hist->bucket_num) {
        bucket_index = hist->bucket_num - 1;  // 确保索引不越界
      }
      hist->buckets[bucket_index]++;
      hist->tuple_sum++;
      break;
    }
    // case FIELDTYPE_DATETIME: {
    //     datetime_field = (DateTimeField *)field;
    //     min_dt = *((DateTime *)hist->min);
    //     current_dt.date = datetime_field->dateValue;
    //     current_dt.time = datetime_field->timeValue;
    //     min_ts = datetime_to_timestamp(min_dt);
    //     current_ts = datetime_to_timestamp(current_dt);
    //     width = *((time_t *)hist->width);
    //     bucket_index = (current_ts - min_ts) / width;
    //     if (bucket_index >= hist->bucket_num) {
    //         bucket_index = hist->bucket_num - 1; // 确保索引不越界
    //     }
    //     hist->buckets[bucket_index]++;
    //     hist->tuple_sum++;
    //     break;
    // }
    case FIELDTYPE_DATE:
    case FIELDTYPE_DATETIME:
    case FIELDTYPE_INVALID:  // 不支持的数据类型，直接返回
    case FIELDTYPE_VARCHAR:
    case FIELDTYPE_BLOB:
    case FIELDTYPE_TEXT: return GNCDB_SUCCESS;
    default: return GNCDB_FIELD_NOT_EXIST;
  }
  return GNCDB_SUCCESS;
}

//* 这里不释放max min，因为这是由外层的table_stats申请分配的
void destroy_numerical_histogram(void *ptr)
{
  NumericalHistogram **hist = (NumericalHistogram **)ptr;
  if (hist == NULL || *hist == NULL) {
    return;
  }
  if ((*hist)->width != NULL) {
    my_free((*hist)->width);
    (*hist)->width = NULL;
  }
  if ((*hist)->buckets != NULL) {
    my_free((*hist)->buckets);
    (*hist)->buckets = NULL;
  }
  my_free(*hist);
  *hist = NULL;
}

int create_numerical_histogram(NumericalHistogram **hist, FieldType col_type, int bucket_num, void *min, void *max)
{
  *hist = (NumericalHistogram *)my_malloc0(sizeof(NumericalHistogram));
  if (*hist == NULL) {
    return GNCDB_MEM;
  }
  (*hist)->col_type   = col_type;
  (*hist)->min        = min;
  (*hist)->max        = max;
  (*hist)->width      = NULL;
  (*hist)->bucket_num = bucket_num;
  (*hist)->tuple_sum  = 0;
  (*hist)->buckets    = my_malloc0(sizeof(int) * bucket_num);
  if ((*hist)->buckets == NULL) {
    destroy_numerical_histogram((void *)hist);
    return GNCDB_MEM;
  }
  memset((*hist)->buckets, 0, sizeof(int) * bucket_num);

  switch (col_type) {
    case FIELDTYPE_INTEGER: {
      (*hist)->width = my_malloc0(sizeof(int));
      if ((*hist)->width == NULL) {
        destroy_numerical_histogram((void *)hist);
        return GNCDB_MEM;
      }
      // 使用浮点数计算并向上取整
      *((int *)(*hist)->width) = (int)ceil(((*((int *)max) - *((int *)min)) + 1.0) / bucket_num);
      break;
    }
    case FIELDTYPE_REAL: {
      (*hist)->width = my_malloc0(sizeof(double));
      if ((*hist)->width == NULL) {
        destroy_numerical_histogram((void *)hist);
        return GNCDB_MEM;
      }
      *((double *)(*hist)->width) = (*((double *)max) - *((double *)min)) / bucket_num;
      break;
    }
    // case FIELDTYPE_DATETIME: {
    //     DateTime *dt_min = (DateTime *)min;
    //     DateTime *dt_max = (DateTime *)max;
    //     time_t ts_min = datetime_to_timestamp(*dt_min);
    //     time_t ts_max = datetime_to_timestamp(*dt_max);
    //     (*hist)->width = my_malloc0(sizeof(time_t));
    //     if ((*hist)->width == NULL) {
    //         destroy_numerical_histogram((void *)hist);
    //         return GNCDB_MEM;
    //     }
    //     // 使用浮点数计算并向上取整
    //     *((time_t *)(*hist)->width) = (time_t)ceil((double)(ts_max - ts_min + 1) / bucket_num);
    //     break;
    // }
    case FIELDTYPE_INVALID:  // 不支持的数据类型，释放直方图内存直接返回，直方图是NULL
    case FIELDTYPE_VARCHAR:
    case FIELDTYPE_BLOB:
    case FIELDTYPE_TEXT:
    case FIELDTYPE_DATE:
    case FIELDTYPE_DATETIME: destroy_numerical_histogram((void *)hist); return GNCDB_SUCCESS;
    default: destroy_numerical_histogram((void *)hist); return GNCDB_FIELD_NOT_EXIST;
  }

  return GNCDB_SUCCESS;
}

double estimate_equal_selectivity(NumericalHistogram *hist, void *value)
{
  int bucket_index = 0;
  // DateTime min_dt;
  // time_t   min_ts, current_ts;

  switch (hist->col_type) {
    /*1.计算值对应的桶下标，就是估计value在（min，max）的位置*/
    case FIELDTYPE_INTEGER: {
      if (*((int *)hist->max) == *((int *)hist->min)) {
        bucket_index = 0;
      } else {
        bucket_index = (int)floor(((*(int *)value - *((int *)hist->min)) / (double)(*((int *)hist->width))));
      }
      break;
    }
    case FIELDTYPE_REAL: {
      if (*((double *)hist->max) == *((double *)hist->min)) {
        bucket_index = 0;
      } else {
        bucket_index = (int)((*(double *)value - *((double *)hist->min)) / *((double *)hist->width));
      }
      break;
    }
    // case FIELDTYPE_DATETIME: {
    //     min_dt = *((DateTime *)hist->min);
    //     min_ts = datetime_to_timestamp(min_dt);
    //     current_ts = datetime_to_timestamp(*(DateTime *)value);
    //     if (*((time_t *)hist->max) == *((time_t *)hist->min)) {
    //         bucket_index = 0;
    //     } else {
    //         bucket_index = (time_t)floor(current_ts - *((time_t *)hist->min) / (long double)(*((time_t
    //         *)hist->width)));
    //     }
    //     break;
    // }
    default: return 0.0;
  }
  /*2.若是比最小值小最大值大则返回0*/
  if (bucket_index < 0 || bucket_index >= hist->bucket_num) {
    return 0.0;
  }
  /*3.buckets[bucket_index]是在value所在的区间的元组的个数，除以总的元组数就是等值条件的选择率*/
  return (double)hist->buckets[bucket_index] / hist->tuple_sum;
}

double estimate_less_selectivity(NumericalHistogram *hist, void *value)
{
  int bucket_index = 0;
  double card         = 0;
  // DateTime min_dt;
  int    int_width = 0, int_min = 0, int_value = 0;
  double real_width = 0.0, real_min = 0.0, real_value = 0.0;
  // time_t   datetime_width = 0, datetime_min = 0, datetime_value = 0;

  /*1.计算value的桶下标*/
  switch (hist->col_type) {
    case FIELDTYPE_INTEGER: {
      int_min   = *((int *)hist->min);
      int_width = *((int *)hist->width);
      int_value = *((int *)value);
      /*1.1如果value大于最大值，返回100%选择率,因为此时是小于条件*/
      if (int_value > *((int *)hist->max)) {
        return 1.0;
      }
      if (*((int *)hist->max) == int_min) {
        bucket_index = 0;
      } else {
        bucket_index = (int)floor(((int_value - int_min) / (double)(int_width)));
      }
      break;
    }
    case FIELDTYPE_REAL: {
      real_min   = *((double *)hist->min);
      real_width = *((double *)hist->width);
      real_value = *((double *)value);
      /*1.2如果value大于最大值，返回100%选择率,因为此时是小于条件*/
      if (real_value > *((double *)hist->max)) {
        return 1.0;
      }
      if (*((double *)hist->max) == real_min) {
        bucket_index = 0;
      } else {
        bucket_index = (int)((real_value - real_min) / real_width);
      }
      break;
    }
    // case FIELDTYPE_DATETIME: {
    //     min_dt = *((DateTime *)hist->min);
    //     datetime_min = datetime_to_timestamp(min_dt);
    //     datetime_value = datetime_to_timestamp(*(DateTime *)value);
    //     datetime_width = *((time_t *)hist->width);
    //     if (datetime_to_timestamp(*((DateTime *)hist->max)) == *((time_t *)hist->min)) {
    //         bucket_index = 0;
    //     } else {
    //         bucket_index = (time_t)floor(datetime_value - datetime_min) / (double)(datetime_width);
    //     }
    //     break;
    // }
    default: return 0.0;
  }

  /*2.处理大于最大值小于最小值的情况*/
  if (bucket_index >= hist->bucket_num) {
    /*2.1确保索引不越界,直接复制为最大下标，保证选择率为100*/
    bucket_index = hist->bucket_num - 1;
  } else if (bucket_index < 0) {
    /*2.2如果小于0，说明没有小于这个值的记录*/
    return 0.0;
  }

  /*3.前bucket_index-1个累加,因为每个bucket中是在该区间的元组的个数*/
  for (int i = 0; i < bucket_index - 1; i++) {
    card += (*hist).buckets[i];
  }

  /*4.估计bucket_index中的个数并相加,这里处理的是value在区间内的情况，比如[1,2],value = 1.5,那么基数应该加该区间的一半*/
  switch ((*hist).col_type) {
    case FIELDTYPE_INTEGER: {
      card +=
          (*hist).buckets[bucket_index] * (((double)int_value - int_min - (bucket_index - 1) * int_width) / int_width);
      break;
    }
    case FIELDTYPE_REAL: {
      card += (*hist).buckets[bucket_index] * ((real_value - real_min - (bucket_index - 1) * real_width) / real_width);
      break;
    }
      // case FIELDTYPE_DATETIME: {
      //     card += (*hist).buckets[bucket_index] * (((long double)datetime_value - datetime_min - bucket_index *
      //     datetime_width) / datetime_width); break;
      // }

    default: return 0.0; break;
  }

  return card / hist->tuple_sum;
}

double estimate_selectivity(NumericalHistogram *hist, CompOp op, void *value)
{
  // int            bucket_index   = 0;
  // IntField      *int_field      = NULL;
  // RealField     *real_field     = NULL;
  // DateTimeField *datetime_field = NULL;
  // DateTime       min_dt, current_dt;
  // time_t         min_ts, current_ts, width;
  // double         selectivity = 0.0;

  /* 排除不支持的数据类型*/
  if (hist->col_type == FIELDTYPE_INVALID || hist->col_type == FIELDTYPE_VARCHAR || hist->col_type == FIELDTYPE_BLOB ||
      hist->col_type == FIELDTYPE_TEXT) {
    return 1.0;
  }

  switch (op) {
    case CMPOP_EQUAL_TO: return estimate_equal_selectivity(hist, value); break;
    case CMPOP_NOT_EQUAL: return 1.0 - estimate_equal_selectivity(hist, value); break;
    case CMPOP_LESS_THAN: return estimate_less_selectivity(hist, value); break;
    case CMPOP_LESS_EQUAL:
      return estimate_less_selectivity(hist, value) + estimate_equal_selectivity(hist, value);
      break;
    case CMPOP_GREAT_THAN:
      return 1.0 - estimate_less_selectivity(hist, value) - estimate_equal_selectivity(hist, value);
      break;
    case CMPOP_GREAT_EQUAL: return 1.0 - estimate_less_selectivity(hist, value); break;
    default: return 0.0;
  }
  return 0.0;
}