/*
 * @Author: zql <EMAIL>
 * @Date: 2025-06-23 20:53:38
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-08-27 16:14:27
 * @FilePath: /gncdbflr/linux_dev/src/optimizer/CBO/join_optimizer.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#include "join_optimizer.h"
#include "btreetable.h"
#include "catalog.h"
#include "condvariable.h"
#include "exec_tuple.h"
#include "expression.h"
#include "filter_stmt.h"
#include "gncdb.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "logical_plan_generator.h"
#include "lookaside_mem.h"
#include "parse_defs.h"
#include "plan_cache.h"
#include "select_stmt.h"
#include "sql_event.h"
#include "table_get_logical_operator.h"
#include "table_scan_physical_operator.h"
#include "table_stats.h"
#include "typedefine.h"
#include "vararraylist.h"
#include <assert.h>
#include <float.h>
#include <limits.h>
#include <math.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>

#define LOG2(x) (log(x) / 0.693147180559945)
#define MIN(a, b) ((a) < (b) ? (a) : (b))

/**
 * @description: 判断一个列是否在JoinNode的相等连接条件中
 * @param {Column *} column 需要判断的列
 * @param {JoinNode *} joinNode 连接结点
 * @param {ComparisonExpr *} compExpr 中间变量
 * @param {int} idx 列位于eqExprs中的下标
 * @param {char} left_right 左右表的标志
 * @param {bool} res 判断结果
 * @return {*}
 */
#define JUDGE_COLUMN_IN_JOINNODE(column, joinNode, compExpr, idx, left_right, res)                  \
  do {                                                                                              \
    res = false;                                                                                    \
    if (idx < 0) {                                                                                  \
      for (idx = 0; idx < (joinNode)->eqExprs->elementCount; idx++) {                               \
        compExpr = varArrayListGetPointer((joinNode)->eqExprs, idx);                                \
        if (strcmp(((FieldExpr *)((compExpr)->left_right))->fieldName, (column)->fieldName) == 0) { \
          res = true;                                                                               \
          break;                                                                                    \
        }                                                                                           \
      }                                                                                             \
    } else {                                                                                        \
      compExpr = varArrayListGetPointer((joinNode)->eqExprs, idx);                                  \
      if (strcmp(((FieldExpr *)((compExpr)->left_right))->fieldName, (column)->fieldName) == 0) {   \
        res = true;                                                                                 \
        break;                                                                                      \
      }                                                                                             \
    }                                                                                               \
  } while (0)

#define ESTIMATE_SORT_COST(card)                                    \
  ((LOG2(card) * (card) +                       /* compare cost */  \
       0.25 * (card) * (card) - 0.25 * (card) + /* mem move cost */ \
       (card))                                  /* insert cost */   \
      * optCostConfig.CPU_OPERATOR_COST)

#define ESTIMATE_HASH_BUILD_COST(card, hash_expand_num)                 \
  ({                                                                    \
    hash_expand_num = (card) > 10 ? 3 * (card) * LOG2((card) - 10) : 0; \
    ((card) +            /* get str cost */                             \
        (card) +         /* cal hashcode cost */                        \
        (card) +         /* mem malloc cost */                          \
        (card) +         /* search cost */                              \
        hash_expand_num) /* hash expand cost */                         \
        * optCostConfig.CPU_OPERATOR_COST;                              \
  })

#define ESTIMATE_HASH_PROBE_COST(card)     \
  ((card) +        /* get str cost */      \
      2 * (card) + /* judge exists cost */ \
      (card) +     /* cal hashcode cost */ \
      (card) +     /* mem malloc cost */   \
      (card))      /* search cost */       \
      * optCostConfig.CPU_OPERATOR_COST

/**
 * @description: 计算哈希连接的成本
 * @param {double} scanCost1 左表扫描成本
 * @param {double} scanCost2 右表扫描成本
 * @param {int} card1 左表基数
 * @param {int} card2 右表基数
 * @param {double} expandNum 哈希扩展成本
 * @return {double} 哈希连接成本
 */
#define CALCULATE_HASH_COST(scanCost1, scanCost2, card1, card2, expandNum)    \
  ((scanCost1) + (scanCost2) +                         /* IO cost */          \
      ((card1) + (card2)) * optCostConfig.TUPLE_COST + /* parse tuple cost */ \
      ESTIMATE_HASH_BUILD_COST(card1, expandNum) +     /* build cost */       \
      ESTIMATE_HASH_PROBE_COST(card2))                 /* probe cost */

/**
 * @description: 计算归并连接的成本
 * @param {double} scanCost1 左表扫描成本
 * @param {double} scanCost2 右表扫描成本
 * @param {int} card1 左表基数
 * @param {int} card2 右表基数
 * @param {bool} canPKSMJ 是否可以通过主键进行归并连接
 * @return {double} 归并连接成本
 */
#define CALCULATE_MERGE_COST(scanCost1, scanCost2, card1, card2, canPKSMJ)       \
  ((scanCost1) + (scanCost2) +                         /* IO cost */             \
      ((card1) + (card2)) * optCostConfig.TUPLE_COST + /* parse tuple cost */    \
      ((MIN(card1, card2) + (card1) + (card2) - 1) / 2.0) *                      \
          optCostConfig.TUPLE_COST + /* join compare cost: (best + worst) / 2 */ \
      ((canPKSMJ) ? 0 : (ESTIMATE_SORT_COST(card1) + ESTIMATE_SORT_COST(card2)))) /* sort cost */

/**
 * @description: 计算块嵌套连接的成本
 * @param {double} scanCost1 左表扫描成本
 * @param {double} scanCost2 右表扫描成本
 * @param {int} card1 左表基数
 * @param {int} card2 右表基数
 * @param {int} numBlks 左表块数
 * @return {double} 块嵌套连接成本
 */
#define CALCULATE_NESTED_COST(scanCost1, scanCost2, card1, card2, numBlks)                \
  ((scanCost1) + (numBlks) * (scanCost2) +                         /* IO cost */          \
      ((card1) + (numBlks) * (card2)) * optCostConfig.TUPLE_COST + /* parse tuple cost */ \
      (card1) * optCostConfig.CPU_OPERATOR_COST +                  /* fill block cost */  \
      (card1) * (card2) * optCostConfig.CPU_OPERATOR_COST)         /* join compare cost */

/**
 * @description: 判断一个列是否在JoinNode的相等连接条件中
 * @param {Column *} column 需要判断的列
 * @param {JoinNode *} joinNode 连接结点
 * @param {ComparisonExpr *} compExpr 中间变量
 * @param {int} idx 列位于eqExprs中的下标
 * @param {char} left_right 左右表的标志
 * @param {bool} res 判断结果
 * @return {*}
 */
#define JUDGE_COLUMN_IN_JOINNODE(column, joinNode, compExpr, idx, left_right, res)                  \
  do {                                                                                              \
    res = false;                                                                                    \
    if (idx < 0) {                                                                                  \
      for (idx = 0; idx < (joinNode)->eqExprs->elementCount; idx++) {                               \
        compExpr = varArrayListGetPointer((joinNode)->eqExprs, idx);                                \
        if (strcmp(((FieldExpr *)((compExpr)->left_right))->fieldName, (column)->fieldName) == 0) { \
          res = true;                                                                               \
          break;                                                                                    \
        }                                                                                           \
      }                                                                                             \
    } else {                                                                                        \
      compExpr = varArrayListGetPointer((joinNode)->eqExprs, idx);                                  \
      if (strcmp(((FieldExpr *)((compExpr)->left_right))->fieldName, (column)->fieldName) == 0) {   \
        res = true;                                                                                 \
        break;                                                                                      \
      }                                                                                             \
    }                                                                                               \
  } while (0)

varArrayList *enumerateSubsets(varArrayList *joinSet, int size)
{
  int           n      = joinSet->elementCount;
  varArrayList *result = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);

  // 使用位掩码生成子集
  for (int mask = 0; mask < (1 << n); mask++) {
    if (__builtin_popcount(mask) == size) {  // 检查子集大小是否等于 size
      varArrayList *subset = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
      for (int i = 0; i < n; i++) {
        if (mask & (1 << i)) {
          varArrayListAddPointer(subset, varArrayListGetPointer(joinSet, i));
        }
      }
      varArrayListAddPointer(result, subset);
    }
  }

  return result;
}

double estimateJoinCost(JoinNode *j, int card1, int card2, double scanCost1, double scanCost2, bool canPKSMJ)
{
  double hashCost   = 0;
  double mergeCost  = 0;
  double nestedCost = 0;
  double expandNum  = 0;
  int    numBlks    = (card1 + JOIN_BLOCK_TNUM - 1) / JOIN_BLOCK_TNUM;  // 计算左表的块数，向上取整

  if (j == NULL) {
    return 0;
  } else {

    // 块嵌套连接的成本
    nestedCost = CALCULATE_NESTED_COST(scanCost1, scanCost2, card1, card2, numBlks);

    // 只有非等值连接，只能考虑块嵌套连接
    if (j->eqExprs->elementCount == 0 && j->eqPKExprs->elementCount == 0) {
      j->joinType = JT_NESTED_LOOP;
      return nestedCost;
    }

    // todo 考虑多字段哈希连接的成本和哈希连接后非等值连接条件过滤的成本
    // 哈希连接的成本
    hashCost = CALCULATE_HASH_COST(scanCost1, scanCost2, card1, card2, expandNum);

    // todo 考虑多字段归并连接的成本和归并连接后非等值连接条件过滤的成本
    // 归并连接的成本
    mergeCost = CALCULATE_MERGE_COST(scanCost1, scanCost2, card1, card2, canPKSMJ);

    // mergeCost = -1;

    if (hashCost < mergeCost && hashCost < nestedCost) {
      j->joinType = JT_HASH;
      return hashCost;
    } else if (mergeCost < hashCost && mergeCost < nestedCost) {
      j->joinType = JT_SORT_MERGE;
      return mergeCost;
    } else {
      j->joinType = JT_NESTED_LOOP;
      return nestedCost;
    }

    return 0;
  }
}

int estimateJoinCard(JoinNode *joinNode, int card1, int card2)
{
  int joinCard = card1 * card2;

  if (joinNode == NULL) {
    return -1;
  }

  // 主键等值连接条件
  if (joinNode->eqPKExprs->elementCount > 0) {
    joinCard = (card1 < card2 ? card1 : card2);  // 应用主键等值连接条件之后的基数
  }

  // 非主键等值连接条件
  for (int i = 0; i < joinNode->eqExprs->elementCount; i++) {
    joinCard *= 0.1;
  }

  // 非等值连接条件
  for (int i = 0; i < joinNode->otherExprs->elementCount; i++) {
    joinCard *= 0.33;
  }
  return joinCard;
}

/** 如果指定的表在连接列表中，则返回 true，否则返回 false */
bool doesJoin(varArrayList *joinList, char *tableName)
{
  JoinNode *j = NULL;
  int       i = 0;

  if (joinList == NULL || tableName == NULL) {
    return false;
  }

  for (i = 0; i < joinList->elementCount; i++) {
    j = varArrayListGetPointer(joinList, i);
    if (strcmp(j->tableName1, tableName) == 0 || (j->tableName2 != NULL && strcmp(j->tableName2, tableName) == 0)) {
      return true;
    }
  }
  return false;
}

/**
 * @description: 探索将joinNode加入{joinSet - joinNode}的最佳路径
 * @param {SpecialJoinInfo*} sji 包含了各种信息
 * @param {varArrayList*} joinSet 长度为d的集合
 * @param {int} idx 当前连接结点在joinSet中的下标
 * @param {JoinNode*} joinNode 连接节点
 * @param {double} bestCostSoFar 长度为d的集合的最小成本
 * @return {*}
 */
PlanInfo *computeSubPlan(PlanCache *planCache, varArrayList *subSet, int idx, JoinNode *joinNode, double bestCostSoFar)
{
  JoinNode        *swapNode         = NULL;
  JoinNode        *tempNode         = NULL;
  PlanInfo        *prevPlanInfo     = NULL;
  PlanInfo        *curPlanInfo      = NULL;
  HashMap         *statsMap         = planCache->stats;
  HashMap         *selectivitiesMap = planCache->selectivities;
  TableStats      *stats1           = NULL;
  TableStats      *stats2           = NULL;
  varArrayList    *bestOrder        = NULL;  // 长度为d-1的集合的最佳顺序
  double          *bestCost         = 0;     // 长度为d-1的集合的最小成本
  int             *bestCard         = 0;     // 长度为d-1的集合的最小基数
  HashMap         *bestPKInfo       = NULL;  // 长度为d-1的集合的主键信息
  HashMapIterator *iter             = NULL;
  int              card1            = 0;
  int              card2            = 0;
  double           cost1            = 0;
  double           cost2            = 0;
  double           joinCost         = 0;
  double           swapJoinCost     = 0;
  double           selectivity1     = 0;
  double           selectivity2     = 0;
  int              pkNum1           = 0;
  int              pkNum2           = 0;
  bool             canPKSMJ         = false;
  bool             swapFlag         = NULL;

  if (subSet->elementCount == 0) {
    /* 两个都是基表 */
    stats1       = hashMapGet(statsMap, joinNode->tableName1);
    stats2       = hashMapGet(statsMap, joinNode->tableName2);
    cost1        = tableStatsEstimateScanCost(stats1);
    cost2        = tableStatsEstimateScanCost(stats2);
    selectivity1 = *(double *)hashMapGet(selectivitiesMap, joinNode->tableName1);
    selectivity2 = *(double *)hashMapGet(selectivitiesMap, joinNode->tableName2);
    card1        = tableStatsEstimateCardinality(stats1, selectivity1);
    card2        = tableStatsEstimateCardinality(stats2, selectivity2);
    canPKSMJ     = joinNode->eqPKExprs->elementCount != 0;
  } else {
    prevPlanInfo = PLAN_CACHE_GET_PLAN(planCache, subSet);
    if (prevPlanInfo) {
      bestOrder  = prevPlanInfo->plan;
      bestCost   = &prevPlanInfo->cost;
      bestCard   = &prevPlanInfo->card;
      bestPKInfo = prevPlanInfo->pkInfo;
    }
    if (doesJoin(bestOrder, joinNode->tableName1)) {
      /* 左表在原先连接结点中 */
      stats2       = hashMapGet(statsMap, joinNode->tableName2);
      cost2        = tableStatsEstimateScanCost(stats2);
      selectivity2 = *(double *)hashMapGet(selectivitiesMap, joinNode->tableName2);
      card2        = tableStatsEstimateCardinality(stats2, selectivity2);
      cost1        = bestCost != NULL ? *bestCost : -1.0;
      card1        = bestCard != NULL ? *bestCard : -1;
      if (bestPKInfo && bestPKInfo->entryCount > 0) {
        /* 左表在之前的连接结点中的最左匹配列数 */
        pkNum1 = *(int *)hashMapGet(bestPKInfo, joinNode->tableName1);
        /*仅当左表之前的结点（如<a, b, c>）个数大于当前的结点（如<a, b>）个数时，才可以使用主键归并连接 */
        if (pkNum1 >= joinNode->leftPKNum) {
          canPKSMJ = true;
        } else {
          canPKSMJ = false;
        }
      }
    } else if (doesJoin(bestOrder, joinNode->tableName2)) {
      // 右表在原先连接结点中
      stats1       = hashMapGet(statsMap, joinNode->tableName1);
      cost1        = tableStatsEstimateScanCost(stats1);
      selectivity1 = *(double *)hashMapGet(selectivitiesMap, joinNode->tableName1);
      card1        = tableStatsEstimateCardinality(stats1, selectivity1);
      cost2        = bestCost != NULL ? *bestCost : DBL_MAX;
      card2        = bestCard != NULL ? *bestCard : INT_MAX;
      if (bestPKInfo) {
        // 右表在之前的连接结点中的最左匹配列数
        pkNum2 = *(int *)hashMapGet(bestPKInfo, joinNode->tableName2);
        // 仅当右表之前的结点（如<a, b, c>）个数大于当前的结点（如<a, b>）个数时，才可以使用主键归并连接
        if (pkNum2 >= joinNode->leftPKNum) {
          canPKSMJ = true;
        } else {
          canPKSMJ = false;
        }
      }
    } else {
      return NULL;
    }
  }

  joinCost = estimateJoinCost(joinNode, card1, card2, cost1, cost2, canPKSMJ);  // 计算连接的成本
  JOIN_NODE_SHALLOWCOPY(tempNode, joinNode);
  swapJoinCost = estimateJoinCost(tempNode, card2, card1, cost2, cost1, canPKSMJ);
  if (swapJoinCost < joinCost) {
    JOIN_NODE_DEEPCOPY(swapNode, joinNode);
    ReverseJoinNode(swapNode);
    swapNode->joinType = tempNode->joinType;
    joinCost           = swapJoinCost;
    joinNode           = swapNode;
    swapNode           = NULL;
    swapFlag           = true;
  }
  JOIN_NODE_SHALLOW_DESTROY(tempNode);
  if (joinCost >= bestCostSoFar) {
    JOIN_NODE_EXPR_DESTROY(swapNode);
    return NULL;  // 如果当前连接的成本大于已知的最佳成本，则提前退出
  }

  PLAN_INFO_CREATE(curPlanInfo);
  curPlanInfo->cost = joinCost;
  curPlanInfo->card = estimateJoinCard(joinNode, card1, card2);

  // 添加order
  if (bestOrder != NULL) {
    JOIN_LIST_DEEPCOPY(curPlanInfo->plan, bestOrder);
  }
  // 添加当前连接结点
  if (swapFlag) {
    tempNode = joinNode;
  } else {
    JOIN_NODE_DEEPCOPY(tempNode, joinNode);
  }
  /* 主键归并连接时，尽量把大表放在前面 */
  if (tempNode->joinType == JT_SORT_MERGE && canPKSMJ && card1 < card2) {
    ReverseJoinNode(tempNode);
  }
  varArrayListAddPointer(curPlanInfo->plan, tempNode);

  // 添加主键信息
  if (canPKSMJ && joinNode->joinType == JT_SORT_MERGE) {
    assert(joinNode->leftPKNum == joinNode->rightPKNum);
    // 能够使用主键连接
    curPlanInfo->pkInfo = hashMapCreate(STRKEY, 0, NULL);
    if (subSet->elementCount == 0 && bestPKInfo == NULL) {
      // 基表连接且hashmap为空，直接加入值
      hashMapPut(curPlanInfo->pkInfo, joinNode->tableName1, &joinNode->leftPKNum);
      hashMapPut(curPlanInfo->pkInfo, joinNode->tableName2, &joinNode->rightPKNum);
    } else {
      // 之前已经存在hashmap，复制并更改值
      iter = createHashMapIterator(bestPKInfo);
      while (hasNextHashMapIterator(iter)) {
        iter = nextHashMapIterator(iter);
        if ((*(int *)iter->entry->value) > joinNode->leftPKNum) {
          hashMapPut(curPlanInfo->pkInfo, iter->entry->key, &joinNode->leftPKNum);
        } else {
          hashMapPut(curPlanInfo->pkInfo, iter->entry->key, iter->entry->value);
        }
      }
      freeHashMapIterator(&iter);
      hashMapPut(curPlanInfo->pkInfo, joinNode->tableName1, &joinNode->leftPKNum);
      hashMapPut(curPlanInfo->pkInfo, joinNode->tableName2, &joinNode->rightPKNum);
    }
  }
  JOIN_NODE_EXPR_DESTROY(swapNode);
  return curPlanInfo;
}

/**
 * @description: 此函数仅仅适用于加速处理只有一个JoinNode的情况，避免复杂的拷贝流程
 * @param {PlanCache} *planCache
 * @param {JoinNode} *joinNode
 * @return {*}
 */
void oneJoinNodeOptimize(HashMap *statsMap, HashMap *selectivityMap, JoinNode *joinNode)
{
  JoinNode   *tempNode     = NULL;
  TableStats *stats1       = NULL;
  TableStats *stats2       = NULL;
  int         card1        = 0;
  int         card2        = 0;
  double      cost1        = 0;
  double      cost2        = 0;
  double      joinCost     = 0;
  double      swapJoinCost = 0;
  double      selectivity1 = 0;
  double      selectivity2 = 0;
  bool        canPKSMJ     = false;

  /* 两个都是基表 */
  stats1       = hashMapGet(statsMap, joinNode->tableName1);
  stats2       = hashMapGet(statsMap, joinNode->tableName2);
  cost1        = tableStatsEstimateScanCost(stats1);
  cost2        = tableStatsEstimateScanCost(stats2);
  selectivity1 = *(double *)hashMapGet(selectivityMap, joinNode->tableName1);
  selectivity2 = *(double *)hashMapGet(selectivityMap, joinNode->tableName2);
  card1        = tableStatsEstimateCardinality(stats1, selectivity1);
  card2        = tableStatsEstimateCardinality(stats2, selectivity2);
  canPKSMJ     = joinNode->eqPKExprs->elementCount != 0;

  /* 比较代价判断是否需要反转 */
  joinCost = estimateJoinCost(joinNode, card1, card2, cost1, cost2, canPKSMJ);  // 计算连接的成本
  JOIN_NODE_SHALLOWCOPY(tempNode, joinNode);
  swapJoinCost = estimateJoinCost(tempNode, card2, card1, cost2, cost1, canPKSMJ);
  if (swapJoinCost < joinCost) {
    ReverseJoinNode(joinNode);
    joinNode->joinType = tempNode->joinType;
    joinCost           = swapJoinCost;
  }
  JOIN_NODE_SHALLOW_DESTROY(tempNode);

  /* 主键归并连接时，尽量把大表放在前面 */
  if (joinNode->joinType == JT_SORT_MERGE && canPKSMJ && card1 < card2) {
    ReverseJoinNode(joinNode);
  }
}

/**
 * @brief 为连接列表中的连接计算最佳连接顺序
 *
 * @param joinList
 * @param sji
 * @param db
 * @return varArrayList*
 */
varArrayList *orderJoins(varArrayList *joinList, PlanCache *planCache, GNCDB *db)
{
  PlanInfo     *currPlan      = NULL;
  PlanInfo     *bestPlan      = NULL;
  int           numJoins      = 0;
  int           i             = 0;
  int           j             = 0;
  int           subsetLen     = 0;
  double        bestCostSoFar = 0;
  varArrayList *currSubsets   = NULL;
  varArrayList *subSet        = NULL;
  // varArrayList *subSetCopy      = NULL;
  varArrayList *optimalOrdering = NULL;
  JoinNode     *joinToRemove    = NULL;

  numJoins = joinList->elementCount;

  /*1.如果 joinList 为 NULL 或为空，则提前退出*/
  if (!joinList || numJoins == 0) {
    return NULL;
  }

  /*3.迭代不同长度的子集*/
  for (subsetLen = 1; subsetLen <= numJoins; subsetLen++) {
    /*3.2currSubsets是joinList长度为subsetLen组合，即C(joinList->elementCount, subsetLen)*/
    /*生成大小为subsetlen的集合，也就是在joinlist中任选subsetLen个*/
    currSubsets = enumerateSubsets(joinList, subsetLen);

    /*3.3迭代每个子集，即在size = 2的时候迭代{{a,b}{a,c}{a,d}{b,c}{b,d}{c,d}}*/
    for (i = 0; i < currSubsets->elementCount; i++) {
      subSet = varArrayListGetPointer(currSubsets, i);
      /* 预先产生哈希映射的字符串 */
      GEN_HASH_KEY(planCache, putStr, subSet);

      /*3.3.2迭代子集内的连接，也就是迭代{a,b}*/
      for (j = 0; j < subSet->elementCount; j++) {

        /* 构造长度为d-1的集合：subSet = {subSet - joinToRemove} */
        joinToRemove = varArrayListGetPointer(subSet, j);
        varArrayListRemoveByIndex(subSet, j);
        bestCostSoFar = (bestPlan == NULL) ? DBL_MAX : bestPlan->cost;

        /* 计算子计划的成本和基数 */
        currPlan = computeSubPlan(planCache, subSet, j, joinToRemove, bestCostSoFar);

        /* 如果找到更好的计划，则更新 bestPlan */
        if (bestPlan == NULL || (currPlan != NULL && currPlan->cost < bestPlan->cost)) {
          PLAN_INFO_DESTROY(bestPlan);
          bestPlan = currPlan;
          currPlan = NULL;
        } else {
          PLAN_INFO_DESTROY(currPlan);
        }

        /* 还原subSet */
        varArrayListInsertPointer(subSet, j, joinToRemove);
      }

      /* 将此子集的最佳计划添加到缓存中 */
      if (bestPlan != NULL) {
        PLAN_CACHE_ADD_PLAN(planCache, subSet, bestPlan);
        bestPlan = NULL;
      }

      varArrayListDestroy(&subSet);
    }
    //* 释放子集集合 */
    varArrayListDestroy(&currSubsets);
  }

  /*从缓存中获取最佳排序*/
  bestPlan        = PLAN_CACHE_REMOVE_PLAN(planCache, joinList);
  optimalOrdering = PTR_MOVE((void **)&(bestPlan->plan));

  /*清理*/
  varArrayListDestroy(&currSubsets);
  PLAN_INFO_DESTROY(bestPlan);

  return optimalOrdering;
}

HashMap *getSelectivitiesMap(HashMap *tabOperMap, GNCDB *db)
{
  HashMap                 *tableStatsMap    = db->catalog->tableStatsMap;
  HashMap                 *selectivitiesMap = hashMapCreate(STRKEY, 0, NULL);
  HashMapIterator         *iter             = createHashMapIterator(tabOperMap);
  TableGetLogicalOperator *tabOper          = NULL;
  TableSchema             *tableSchema      = NULL;
  TableStats              *stats            = NULL;
  ComparisonExpr          *compExpr         = NULL;
  FieldExpr               *fieldExpr        = NULL;
  ValueExpr               *valueExpr        = NULL;
  double                  *selectivity      = NULL;
  int                      index            = 0;

  while (hasNextHashMapIterator(iter)) {
    /*1.遍历每个表算子*/
    iter        = nextHashMapIterator(iter);
    tabOper     = (TableGetLogicalOperator *)iter->entry->value;
    tableSchema = getTableSchema(db->catalog, tabOper->table->tableName);
    stats       = hashMapGet(tableStatsMap, tabOper->table->tableName);

    /*2.遍历表的每个过滤条件，根据直方图计算选择率*/
    selectivity  = my_new0(double);
    *selectivity = 1.0;
    for (int i = 0; i < tabOper->expressions->elementCount; i++) {
      /*2.1若过滤条件的谓词是一列一值得形式则计算选择性*/
      compExpr = varArrayListGetPointer(tabOper->expressions, i);
      if (compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_VALUE) {
        fieldExpr = (FieldExpr *)compExpr->left;
        valueExpr = (ValueExpr *)compExpr->right;
        index     = tableSchemaGetIndex(tableSchema, fieldExpr->fieldName);
      } else if (compExpr->right->type == ETG_FIELD && compExpr->left->type == ETG_VALUE) {
        fieldExpr = (FieldExpr *)compExpr->right;
        valueExpr = (ValueExpr *)compExpr->left;
        index     = tableSchemaGetIndex(tableSchema, fieldExpr->fieldName);
      }
      if (index >= 0) {
        /*2.2根据直方图计算选择性，这个*是乘法号，各列之间的选择性使用的是类乘的形式*/
        (*selectivity) *=
            tableStatsEstimateSelectivity(stats, index, compExpr->comp, valueGetPointer(valueExpr->value));
      }
    }

    /*3.将选择率存入map*/
    if (*selectivity < 0) {
      *selectivity = 0;
    }
    hashMapPut(selectivitiesMap, tabOper->table->tableName, selectivity);
  }
  freeHashMapIterator(&iter);

  return selectivitiesMap;
}

HashMap *getSelectivitiesMap_v2(HashMap *tabOperMap, GNCDB *db)
{
  HashMap                   *tableStatsMap    = db->catalog->tableStatsMap;
  HashMap                   *selectivitiesMap = NULL;
  HashMapIterator           *iter             = NULL;
  TableScanPhysicalOperator *tabOper          = NULL;
  TableSchema               *tableSchema      = NULL;
  TableStats                *stats            = NULL;
  ComparisonExpr            *compExpr         = NULL;
  FieldExpr                 *fieldExpr        = NULL;
  ValueExpr                 *valueExpr        = NULL;
  double                    *selectivity      = NULL;
  int                        index            = 0;

  selectivitiesMap = hashMapCreate(STRKEY, 0, NULL);
  iter             = createHashMapIterator(tabOperMap);

  while (hasNextHashMapIterator(iter)) {
    /* 遍历当前连接中的每个表算子 */
    iter        = nextHashMapIterator(iter);
    tabOper     = (TableScanPhysicalOperator *)iter->entry->value;
    tableSchema = getTableSchema(db->catalog, tabOper->table->tableName);
    stats       = hashMapGet(tableStatsMap, tabOper->table->tableName);

    if (!stats) {
      /* 没有统计信息无法优化直接返回 */
      freeHashMapIterator(&iter);
      hashMapDestroy(&selectivitiesMap);
      return NULL;
    }

    /* 遍历表中的每个条件，计算选择率 */
    selectivity  = my_new0(double);
    *selectivity = 1.0;
    for (int i = 0; i < tabOper->predicates->elementCount; i++) {
      compExpr = varArrayListGetPointer(tabOper->predicates, i);
      if (compExpr->left->type == ETG_FIELD && compExpr->right->type == ETG_VALUE) {
        fieldExpr = (FieldExpr *)compExpr->left;
        valueExpr = (ValueExpr *)compExpr->right;
        index     = tableSchemaGetIndex(tableSchema, fieldExpr->fieldName);
      } else if (compExpr->right->type == ETG_FIELD && compExpr->left->type == ETG_VALUE) {
        fieldExpr = (FieldExpr *)compExpr->right;
        valueExpr = (ValueExpr *)compExpr->left;
        index     = tableSchemaGetIndex(tableSchema, fieldExpr->fieldName);
      }
      if (index >= 0) {
        (*selectivity) *=
            tableStatsEstimateSelectivity(stats, index, compExpr->comp, valueGetPointer(valueExpr->value));
      }
    }

    // 将选择率存入map
    if (*selectivity < 0) {
      *selectivity = 0;
    }
    hashMapPut(selectivitiesMap, tabOper->table->tableName, selectivity);
  }
  freeHashMapIterator(&iter);
  return selectivitiesMap;
}

// 辅助函数：检查是否所有表都有统计信息
static bool allTablesHaveStats(varArrayList *tables, HashMap *tableStatsMap)
{
  int         i     = 0;
  BtreeTable *table = NULL;

  if (!tables || !tableStatsMap) {
    return false;
  }
  for (i = 0; i < varArrayListGetCount(tables); ++i) {
    table = varArrayListGetPointer(tables, i);
    if (!table || !hashMapGet(tableStatsMap, table->tableName)) {
      return false;
    }
  }
  return true;
}

/**
 * @description: 获取每个JoinNode中的相等条件中最左匹配的主键列数
 * @param {JoinTables} *joinTables
 * @param {GNCDB} *db
 * @return {*}
 */
int getPKInfo(JoinTables *joinTables, GNCDB *db)
{
  varArrayList   *pkIdxList1   = NULL;
  varArrayList   *pkIdxList2   = NULL;
  TableSchema    *tableSchema1 = NULL;
  TableSchema    *tableSchema2 = NULL;
  JoinNode       *joinNode     = NULL;
  ComparisonExpr *compExpr     = NULL;
  Column         *column       = NULL;
  int            *colIdx1      = 0; /*主键列下标*/
  int            *colIdx2      = 0; /*主键列下标*/
  int             exprIdx      = 0; /*列在条件中下标*/
  bool            res          = false;

  for (int i = 0; i < joinTables->joinNodes->elementCount; i++) {
    joinNode     = varArrayListGetPointer(joinTables->joinNodes, i);
    tableSchema1 = getTableSchema(db->catalog, joinNode->tableName1);
    tableSchema2 = getTableSchema(db->catalog, joinNode->tableName2);
    pkIdxList1   = getPrimaryIndexArray(db->catalog, joinNode->tableName1);
    pkIdxList2   = getPrimaryIndexArray(db->catalog, joinNode->tableName2);
    if (!tableSchema1 || !pkIdxList1) {
      return GNCDB_INTERNAL;
    }
    for (int j = 0; j < pkIdxList1->elementCount; j++) {
      exprIdx = -1;
      colIdx1 = varArrayListGet(pkIdxList1, j);
      column  = varArrayListGetPointer(tableSchema1->columnList, *colIdx1);
      JUDGE_COLUMN_IN_JOINNODE(column, joinNode, compExpr, exprIdx, left, res);

      /* 左边未找到主键列直接提前结束 */
      if (!res) {
        return GNCDB_SUCCESS;
      }
      /* 左边匹配之后利用idx直接判断右边 */
      colIdx2 = varArrayListGet(pkIdxList2, j);
      column  = varArrayListGetPointer(tableSchema2->columnList, *colIdx2);
      JUDGE_COLUMN_IN_JOINNODE(column, joinNode, compExpr, exprIdx, right, res);
      /* 右边未找到主键列直接提前结束 */
      if (!res) {
        return GNCDB_SUCCESS;
      }
      /* 左右都匹配：将其从相等条件List中删除，并加入到主键条件List中 */
      compExpr = (ComparisonExpr *)varArrayListGetPointer(joinNode->eqExprs, exprIdx);
      varArrayListRemoveByIndexPointer(joinNode->eqExprs, exprIdx);
      varArrayListAddPointer(joinNode->eqPKExprs, compExpr);
      joinNode->leftPKNum++;
      joinNode->rightPKNum++;
      if (joinNode->leftPKNum == joinNode->eqPKExprs->elementCount) {
        /* 如果已经匹配完所有的连接条件，直接结束 */
        break;
      }
    }
  }
  return GNCDB_SUCCESS;
}

varArrayList *DoJoinOptimization(JoinTables *joinTables, SQLStageEvent *sqlEvent)
{
  GNCDB        *db              = sqlEvent->db;
  PlanCache    *planCache       = NULL;
  varArrayList *joinList        = NULL;
  varArrayList *optimalOrdering = NULL;

  /*1. 参数检查*/
  if (!joinTables || !db || !db->catalog || !db->catalog->tableStatsMap) {
    return NULL;
  }

  /*2. 检查所有表是否有统计信息*/
  if (!allTablesHaveStats(joinTables->tables, db->catalog->tableStatsMap)) {
    return NULL;
  }

  /*3. 创建并初始化SpecialJoinInfo*/
  PLAN_CACHE_CREATE(planCache);
  planCache->selectivities = getSelectivitiesMap(sqlEvent->tabOperMap, db);
  planCache->stats         = db->catalog->tableStatsMap;

  /*4.获取主键信息*/
  if (GNCDB_SUCCESS != getPKInfo(joinTables, db)) {
    return NULL;
  }

  /*5. 创建 joinList（用于重新排序）*/
  joinList = joinTables->joinNodes;
  if (!joinList) {
    PLAN_CACHE_DESTROY(planCache);
    return NULL;
  }

  /* 6. 执行连接排序 */
  optimalOrdering = orderJoins(joinList, planCache, db);

  PLAN_CACHE_DESTROY(planCache);
  return optimalOrdering;
}

varArrayList *DoJoinOptimization_v2(JoinTables *joinTables, SQLStageEvent *sqlEvent)
{
  GNCDB           *db               = sqlEvent->db;
  PlanCache       *planCache        = NULL;
  varArrayList    *joinList         = NULL;
  varArrayList    *optimalOrdering  = NULL;
  HashMap         *selectivitiesMap = NULL;
  HashMap         *tableStatsMap    = NULL;
  HashMapIterator *iter             = NULL;

  //* 参数检查和早期退出 */
  if (!joinTables || !db || !db->catalog || !db->catalog->tableStatsMap) {
    return NULL;
  }

  joinList = joinTables->joinNodes;

  /* 当两表连接只有一个连接结点时，此种情况较为简单不需要进行joinNode的拷贝 */
  if (joinList->elementCount == 1) {

    selectivitiesMap = getSelectivitiesMap_v2(sqlEvent->tabOperMap, db);
    /* 无统计信息，直接返回 */
    if (!selectivitiesMap) {
      return NULL;
    }
    tableStatsMap = db->catalog->tableStatsMap;
    //* 为joinNodes添加主键信息 */
    if (GNCDB_SUCCESS != getPKInfo(joinTables, db)) {
      return NULL;
    }
    oneJoinNodeOptimize(tableStatsMap, selectivitiesMap, varArrayListGetPointer(joinList, 0));
    /* 结束销毁选择率map */
    iter = createHashMapIterator(selectivitiesMap);
    while (hasNextHashMapIterator(iter)) {
      iter = nextHashMapIterator(iter);
      my_free(iter->entry->value);
    }
    freeHashMapIterator(&iter);
    hashMapDestroy(&selectivitiesMap);
    return joinList;
  } else {

    //* 创建并初始化SpecialJoinInfo */
    PLAN_CACHE_CREATE(planCache);
    planCache->selectivities = getSelectivitiesMap_v2(sqlEvent->tabOperMap, db);
    if (!planCache->selectivities) {
      PLAN_CACHE_DESTROY(planCache);
      return NULL;
    }
    planCache->stats = db->catalog->tableStatsMap;

    //* 为joinNodes添加主键信息 */
    if (GNCDB_SUCCESS != getPKInfo(joinTables, db)) {
      return NULL;
    }

    //* 执行连接排序 */
    optimalOrdering = orderJoins(joinList, planCache, db);

    PLAN_CACHE_DESTROY(planCache);
    return optimalOrdering;
  }
}
