#include "table_stats.h"
#include "vararraylist.h"
#include "gncdbconstant.h"
#include <stdio.h>
#include <string.h>

void destroy_table_stats(TableStats **stats)
{
  if (stats == NULL || *stats == NULL) {
    return;
  }
  if ((*stats)->histograms != NULL) {
    varArrayListDestroy(&(*stats)->histograms);
  }
  if ((*stats)->field_max != NULL) {
    varArrayListDestroy(&(*stats)->field_max);
  }
  if ((*stats)->field_min != NULL) {
    varArrayListDestroy(&(*stats)->field_min);
  }
  my_free(*stats);
  *stats = NULL;
}

void free_max_min_value(void *value) { my_free(*(void **)value); }

/**
 * @description: 根据叶子节点数和内部节点entry数估计B+树的总节点数
 * @param {int} L 叶子节点数
 * @param {int} C_child 内部节点entry数
 * @return {*} 总节点数
 */
int estimate_total_nodes(int L, int C_child)
{
  int total_nodes         = L;
  int current_level_nodes = L;

  // 计算每一层的节点数，直到树的高度为1
  while (current_level_nodes > 1) {
    /* 使用整数除法实现向上取整: (a + b - 1) / b */
    current_level_nodes = (current_level_nodes + C_child - 1) / C_child;
    total_nodes += current_level_nodes;
  }

  return total_nodes;
}

/**
 * @description: 估计表的大小
 * @param {char} *table_name 表名
 * @param {int} leaf_page_num 叶子节点数
 * @param {GNCDB} *db 数据库实例指针
 * @return {*} 表的大小
 */
int estimate_table_size(char *table_name, int leaf_page_num, GNCDB *db)
{
  BtreeTable *table        = hashMapGet(db->catalog->tableMap, table_name);
  int         i_max        = getInternalEntryMaxCount(db->pageCurrentSize, table);
  int         i_min        = getInternalEntryMinCount(db->pageCurrentSize, table);
  int         total_pages1 = (estimate_total_nodes(leaf_page_num, i_max) + estimate_total_nodes(leaf_page_num, i_min)) /
                     2;  // 取总页面数为最大最小值的平均值
  int total_pages2 =
      estimate_total_nodes(leaf_page_num, (i_max + i_min) / 2);  // l假设每个节点的entry数为最大最小值的平均值
  int table_size = (total_pages1 + total_pages2) / 2 * (db->pageCurrentSize);
  return table_size;
}

/**
 * @description: 估计表的在内存中的比例
 * @param {char} *table_name 表名
 * @param {GNCDB} *db 数据库实例指针
 * @return {*} 在内存中的比例
 */
double estimate_in_mem_rate(char *table_name, GNCDB *db)
{
  double       in_mem_est            = 0.0;
  const double table_in_memory_limit = 0.2;
  TableStats  *stats                 = hashMapGet(db->catalog->tableStatsMap, table_name);
  int          table_size            = estimate_table_size(table_name, stats->page_count, db);
  int          buffer_size           = (db->pagePoolCount) * (db->pageCurrentSize);
  const double percent_of_mem        = (double)table_size / buffer_size;

  if (percent_of_mem < table_in_memory_limit)  // 小于20%
    in_mem_est = 1.0;
  else if (percent_of_mem > 1.0)  // 大于整个buffer
    in_mem_est = 0.0;
  else {
    in_mem_est = 1.0 - (percent_of_mem - table_in_memory_limit) / (1.0 - table_in_memory_limit);
  }
  assert(in_mem_est >= 0.0 && in_mem_est <= 1.0);

  return in_mem_est;
}

int create_table_stats(TableStats **stats, char *table_name, GNCDB *db, Transaction *tx)
{
  int rc           = 0;
  int cur_leaf_pid = 0;
  // int                 page_cnt       = 0;
  // Tuple              *tuple          = NULL;
  // Field              *field          = NULL;
  BYTE *record = NULL;
  // IntField           *int_field      = NULL;
  // RealField          *real_field     = NULL;
  // VarCharField       *varchar_field  = NULL;
  // DateField          *date_field     = NULL;
  // DateTimeField      *datetime_field = NULL;
  // DateTime            cur_dt;
  NumericalHistogram *hist      = NULL;
  BtreeTable         *table     = hashMapGet(db->catalog->tableMap, table_name);
  BtreeCursor        *bt_cursor = NULL;
  TableSchema        *ts        = hashMapGet(db->catalog->tableSchemaMap, table_name);
  int                 col_num   = ts->columnNum;
  int                *pint_max, *pint_min;
  double             *preal_max, *preal_min;
  // DateTime           *pdt_max, *pdt_min;
  Column *column     = NULL;
  int     int_value  = 0;
  double  real_value = 0;

  //* 分配结构体内存
  *stats = (TableStats *)my_malloc0(sizeof(TableStats));
  if (*stats == NULL) {
    return GNCDB_MEM;
  }
  (*stats)->db          = db;
  (*stats)->page_count  = 0;
  (*stats)->tuple_count = 0;
  (*stats)->histograms  = varArrayListCreate(DISORDER, BYTES_POINTER, col_num, NULL, destroy_numerical_histogram);
  (*stats)->field_max   = varArrayListCreate(DISORDER, BYTES_POINTER, col_num, NULL, free_max_min_value);
  (*stats)->field_min   = varArrayListCreate(DISORDER, BYTES_POINTER, col_num, NULL, free_max_min_value);
  (*stats)->histograms->elementCount =
      col_num;  // 手动将这三个arrayList的elementCount设置为col_num，以便后续使用setByIndex填充
  (*stats)->field_max->elementCount = col_num;
  (*stats)->field_min->elementCount = col_num;

  //* 第一次遍历：获取page_cnt, tuple_cnt和各个字段最大最小值
  bt_cursor = btreeCursorConstruct(table->tableName, db, NULL, tx);
  if (bt_cursor == NULL) {
    destroy_table_stats(stats);
    return GNCDB_BTC_CREATE_FALSE;
  }
  if (!btreeTableHasNextTuple(bt_cursor)) {
    btreeCursorDestroy(&bt_cursor);
    destroy_table_stats(stats);
    return GNCDB_SUCCESS;
  }
  while (btreeTableHasNextTuple(bt_cursor)) {
    // tuple = btreeTableGetNextTuple(table, bt_cursor, db);
    record = btreeTableGetNextRecord(table, bt_cursor, db);
    (*stats)->tuple_count++;                             // 更新元组数量
    if (cur_leaf_pid != bt_cursor->currentLeafPageId) {  // 更新页面数量
      cur_leaf_pid = bt_cursor->currentLeafPageId;
      (*stats)->page_count++;
    }
    for (int i = 0; i < col_num; i++) {  // 获取最大最小值
      // field = varArrayListGetPointer(tuple->fieldArray, i);
      column = (Column *)varArrayListGetPointer(ts->columnList, i);
      switch (column->fieldType) {
        case FIELDTYPE_INTEGER: {
          // int_field = (IntField *)field;
          pint_max = (int *)varArrayListGetPointer((*stats)->field_max, i);
          pint_min = (int *)varArrayListGetPointer((*stats)->field_min, i);
          memcpy(&int_value, record + column->offset, sizeof(int));
          if (pint_max == NULL) {
            pint_max = my_malloc(sizeof(int));
            if (pint_max == NULL) {
              destroy_table_stats(stats);
              btreeCursorDestroy(&bt_cursor);
              return GNCDB_MEM;
            }
            // *pint_max = int_field->value;
            *pint_max = int_value;
            varArrayListSetByIndexPointer((*stats)->field_max, i, pint_max);
          } else {
            // if (int_field->value > *pint_max) {
            //   *pint_max = int_field->value;
            // }
            if (int_value > *pint_max) {
              *pint_max = int_value;
            }
          }
          if (pint_min == NULL) {
            pint_min = my_malloc(sizeof(int));
            if (pint_min == NULL) {
              destroy_table_stats(stats);
              btreeCursorDestroy(&bt_cursor);
              return GNCDB_MEM;
            }
            // *pint_min = int_field->value;
            *pint_min = int_value;
            varArrayListSetByIndexPointer((*stats)->field_min, i, pint_min);
          } else {
            // if (int_field->value < *pint_min) {
            //   *pint_min = int_field->value;
            // }
            if (int_value < *pint_min) {
              *pint_min = int_value;
            }
          }
          break;
        }
        case FIELDTYPE_REAL: {
          // real_field = (RealField *)field;
          memcpy(&real_value, record + column->offset, sizeof(double));
          preal_max = (double *)varArrayListGetPointer((*stats)->field_max, i);
          preal_min = (double *)varArrayListGetPointer((*stats)->field_min, i);
          if (preal_max == NULL) {
            preal_max = my_malloc(sizeof(double));
            if (preal_max == NULL) {
              destroy_table_stats(stats);
              btreeCursorDestroy(&bt_cursor);
              return GNCDB_MEM;
            }
            // *preal_max = real_field->value;
            *preal_max = real_value;
            varArrayListSetByIndexPointer((*stats)->field_max, i, preal_max);
          } else {
            // if (real_field->value > *preal_max) {
            //   *preal_max = real_field->value;
            // }
            if (real_value > *preal_max) {
              *preal_max = real_value;
            }
          }
          if (preal_min == NULL) {
            preal_min = my_malloc(sizeof(double));
            if (preal_min == NULL) {
              destroy_table_stats(stats);
              btreeCursorDestroy(&bt_cursor);
              return GNCDB_MEM;
            }
            // *preal_min = real_field->value;
            *preal_min = real_value;
            varArrayListSetByIndexPointer((*stats)->field_min, i, preal_min);
          } else {
            // if (real_field->value < *preal_min) {
            //   *preal_min = real_field->value;
            // }
            if (real_value < *preal_min) {
              *preal_min = real_value;
            }
          }
          break;
        }
        // case FIELDTYPE_DATETIME: {
        //     datetime_field = (DateTimeField *)field;
        //     pdt_max = (DateTime *)varArrayListGetPointer((*stats)->field_max, i);
        //     pdt_min = (DateTime *)varArrayListGetPointer((*stats)->field_min, i);
        //     if (pdt_max == NULL) {
        //         pdt_max = my_malloc(sizeof(DateTime));
        //         if (pdt_max == NULL) {
        //             destroy_table_stats(stats);
        //             btreeCursorDestroy(&bt_cursor);
        //             return GNCDB_MEM;
        //         }
        //         pdt_max->date = datetime_field->dateValue;
        //         pdt_max->time = datetime_field->timeValue;
        //         varArrayListSetByIndexPointer((*stats)->field_max, i, pint_max);
        //     } else {
        //         cur_dt.date = datetime_field->dateValue;
        //         cur_dt.time = datetime_field->timeValue;
        //         if (datetimeCompare(&cur_dt, pdt_max) > 0) {
        //             pdt_max->date = datetime_field->dateValue;
        //             pdt_max->time = datetime_field->timeValue;
        //         }
        //     }
        //     if (pdt_min == NULL) {
        //         pdt_min = my_malloc(sizeof(DateTime));
        //         if (pdt_min == NULL) {
        //             destroy_table_stats(stats);
        //             btreeCursorDestroy(&bt_cursor);
        //             return GNCDB_MEM;
        //         }
        //         pdt_min->date = datetime_field->dateValue;
        //         pdt_min->time = datetime_field->timeValue;
        //         varArrayListSetByIndexPointer((*stats)->field_min, i, pint_min);
        //     } else {
        //         cur_dt.date = datetime_field->dateValue;
        //         cur_dt.time = datetime_field->timeValue;
        //         if (datetimeCompare(&cur_dt, pdt_min) < 0) {
        //             pdt_min->date = datetime_field->dateValue;
        //             pdt_min->time = datetime_field->timeValue;
        //         }
        //     }
        //     break;
        // }
        case FIELDTYPE_DATE:
        case FIELDTYPE_DATETIME:
        case FIELDTYPE_INVALID:  // 不支持的数据类型，min max是NULL
        case FIELDTYPE_VARCHAR:
        case FIELDTYPE_BLOB:
        case FIELDTYPE_TEXT: break;
      }
    }
  }
  btreeCursorDestroy(&bt_cursor);

  //* 初始化各个直方图
  for (int i = 0; i < col_num; i++) {
    // field = varArrayListGetPointer(ts->columnList, i);
    column = (Column *)varArrayListGetPointer(ts->columnList, i);
    rc     = create_numerical_histogram(&hist,
        column->fieldType,
        DEFAULT_NUM_HIST_BINS,
        varArrayListGetPointer((*stats)->field_min, i),
        varArrayListGetPointer((*stats)->field_max, i));
    if (rc != GNCDB_SUCCESS) {
      destroy_table_stats(stats);
      return rc;
    }
    varArrayListSetByIndexPointer((*stats)->histograms, i, hist);
  }

  //* 第二次遍历：将各个tuple字段值填充直方图
  bt_cursor = btreeCursorConstruct(table_name, db, NULL, tx);
  if (rc != GNCDB_SUCCESS) {
    destroy_table_stats(stats);
    return rc;
  }
  while (btreeTableHasNextTuple(bt_cursor)) {
    // tuple = btreeTableGetNextTuple(table, bt_cursor, db);
    record = btreeTableGetNextRecord(table, bt_cursor, db);
    for (int i = 0; i < col_num; i++) {
      // field = varArrayListGetPointer(tuple->fieldArray, i);
      column = (Column *)varArrayListGetPointer(ts->columnList, i);
      rc     = add_field_value(varArrayListGetPointer((*stats)->histograms, i), column, record);
      if (rc != GNCDB_SUCCESS) {
        destroy_table_stats(stats);
        return rc;
      }
    }
  }
  btreeCursorDestroy(&bt_cursor);
  btreeCursorDestroy(&bt_cursor);

  // 将创建的直方图加入到catalog中的tableStatsMap中
  rc = hashMapPut(db->catalog->tableStatsMap, table_name, *stats);

  //* 计算在内存概率
  (*stats)->in_mem_rate = estimate_in_mem_rate(table_name, db);

  // printf("\ncreate table_stats: %s\n", table_name);
  return rc;
}

inline int tableStatsEstimateScanCost(TableStats *stats)
{
  int mem_pages  = stats->page_count * stats->in_mem_rate;
  int disk_pages = stats->page_count - mem_pages;
  return mem_pages * optCostConfig.MEM_PAGE_COST + disk_pages * optCostConfig.RANDOM_PAGE_COST;
  // return 20 * stats->page_count;
}

inline int tableStatsEstimateCardinality(TableStats *stats, double filterSelectivities)
{
  return stats->tuple_count * filterSelectivities;
}

inline double tableStatsEstimateSelectivity(TableStats *stats, int col_index, CompOp op, void *value)
{
  NumericalHistogram *hist = varArrayListGetPointer(stats->histograms, col_index);
  if (hist == NULL) {
    return GNCDB_FIELD_NOT_EXIST;
  }
  return estimate_selectivity(hist, op, value);
}
