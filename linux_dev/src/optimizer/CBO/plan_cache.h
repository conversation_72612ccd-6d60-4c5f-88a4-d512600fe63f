/*
 * @Author: zql <EMAIL>
 * @Date: 2025-03-20 15:22:17
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-07-25 19:33:36
 * @FilePath: /gncdbflr/linux_dev/src/optimizer/plan_cache.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef PLAN_CACHE_H
#define PLAN_CACHE_H
#include "hashmap.h"
#include "vararraylist.h"

/* 当前计划的代价和JoinNodeList等信息 */
typedef struct PlanInfo
{
  double        cost;    // 代价
  int           card;    // 基数
  varArrayList *plan;    // 连接计划(为list分配了内存，需要free)
  HashMap      *pkInfo;  // HashMap<tableName, pkNum>，当前计划中各个表的主键信息
} PlanInfo;

typedef struct PlanCache
{
  HashMap *stats;          // 用于存储表统计信息的哈希表(来自catalog，不需要free)
  HashMap *selectivities;  // 用于存储选择性的哈希表(需要free)
  HashMap *planCache;      // 用于存储计划缓存的列表(由create创建，需要free)<planStr, CostCard *>
  char    *putStr;         // 用于添加计划缓存的key，避免重复分配内存
  char    *getStr;         // 用于查询计划缓存的key，避免重复分配内存
  int      planSize;       // keyStr的长度
} PlanCache;

/**
 * @description: 创建 PlanInfo
 * @return PlanInfo* 新创建的 PlanInfo 指针
 */
#define PLAN_INFO_CREATE(planInfo)                                                   \
  do {                                                                               \
    (planInfo) = (PlanInfo *)my_malloc0(sizeof(PlanInfo));                           \
    if ((planInfo) != NULL) {                                                        \
      (planInfo)->plan = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL); \
    }                                                                                \
  } while (0)

/**
 * @description: 销毁 PlanInfo
 * @param {PlanInfo**} planInfo PlanInfo 的二级指针
 */
#define PLAN_INFO_DESTROY(planInfo)                \
  do {                                             \
    if ((planInfo) != NULL) {                      \
      if (((planInfo))->plan != NULL) {            \
        JOIN_LIST_DEEPDESTROY(((planInfo))->plan); \
      }                                            \
      if (((planInfo))->pkInfo != NULL) {          \
        hashMapDestroy(&(((planInfo))->pkInfo));   \
      }                                            \
      my_free((planInfo));                         \
      (planInfo) = NULL;                           \
    }                                              \
  } while (0)

/**
 * @description: 创建 PlanCache
 * @return PlanCache* 新创建的 PlanCache 指针
 */
#define PLAN_CACHE_CREATE(planCache)                                      \
  do {                                                                    \
    (planCache) = (PlanCache *)my_malloc0(sizeof(PlanCache));             \
    if ((planCache) != NULL) {                                            \
      (planCache)->planSize  = 128;                                       \
      (planCache)->putStr    = (char *)my_malloc0((planCache)->planSize); \
      (planCache)->getStr    = (char *)my_malloc0((planCache)->planSize); \
      (planCache)->planCache = hashMapCreate(STRKEY, 0, NULL);            \
    }                                                                     \
  } while (0)

/**
 * @description: 销毁 PlanCache
 * @param {PlanCache**} cache PlanCache 的二级指针
 */
#define PLAN_CACHE_DESTROY(cache)                               \
  do {                                                          \
    HashMapIterator *iter     = NULL;                           \
    PlanInfo        *planInfo = NULL;                           \
    if ((cache) != NULL) {                                      \
      if (((cache))->selectivities != NULL) {                   \
        iter = createHashMapIterator(((cache))->selectivities); \
        while (hasNextHashMapIterator(iter)) {                  \
          iter = nextHashMapIterator(iter);                     \
          my_free(iter->entry->value);                          \
        }                                                       \
        hashMapDestroy(&(((cache))->selectivities));            \
        freeHashMapIterator(&iter);                             \
      }                                                         \
      if (((cache))->planCache != NULL) {                       \
        iter = createHashMapIterator(((cache))->planCache);     \
        while (hasNextHashMapIterator(iter)) {                  \
          iter     = nextHashMapIterator(iter);                 \
          planInfo = (PlanInfo *)iter->entry->value;            \
          if (planInfo != NULL) {                               \
            PLAN_INFO_DESTROY(planInfo);                        \
          }                                                     \
        }                                                       \
        hashMapDestroy(&(((cache))->planCache));                \
        freeHashMapIterator(&iter);                             \
      }                                                         \
      if (((cache))->putStr != NULL) {                          \
        my_free(((cache))->putStr);                             \
      }                                                         \
      if (((cache))->getStr != NULL) {                          \
        my_free(((cache))->getStr);                             \
      }                                                         \
      my_free((cache));                                         \
      (cache) = NULL;                                           \
    }                                                           \
  } while (0)

/**
 * @description: 生成哈希键字符串，用于计划缓存
 * @param {PlanCache*} planCache 计划缓存结构体
 * @param {char*} str 用于存储生成的哈希键字符串结构体变量
 * @param {varArrayList*} joinNodes JoinNode 列表
 */
#define GEN_HASH_KEY(planCache, str, joinNodes)                                                          \
  do {                                                                                                   \
    JoinNode *node      = NULL;                                                                          \
    int       offset    = 0;                                                                             \
    char     *newKeyStr = NULL;                                                                          \
                                                                                                         \
    if ((planCache)->str == NULL) {                                                                      \
      (planCache)->str = (char *)my_malloc0((planCache)->planSize);                                      \
    }                                                                                                    \
                                                                                                         \
    for (int i = 0; i < (joinNodes)->elementCount; i++) {                                                \
      node = (JoinNode *)varArrayListGetPointer((joinNodes), i);                                         \
      /* 检查 keyStr 的长度是否足够 */                                                                   \
      while (offset + strlen(node->tableName1) + strlen(node->tableName2) + 1 > (planCache)->planSize) { \
        (planCache)->planSize *= 2;                                                                      \
        newKeyStr = (char *)my_realloc((planCache)->str, (planCache)->planSize);                         \
        if (newKeyStr == NULL) {                                                                         \
          my_free((planCache)->str);                                                                     \
          (planCache)->str = NULL;                                                                       \
        }                                                                                                \
        (planCache)->str = newKeyStr;                                                                    \
      }                                                                                                  \
      memset((planCache)->str + offset, 0, (planCache)->planSize - offset);                              \
      offset += sprintf((planCache)->str + offset, "%s%s", node->tableName1, node->tableName2);          \
    }                                                                                                    \
    /* 末尾加 \0 */                                                                                      \
    (planCache)->str[offset] = '\0';                                                                     \
  } while (0)

#define PLAN_CACHE_ADD_PLAN(cache, joinNodes, planInfo)          \
  do {                                                           \
    if ((cache)->putStr[0] == '\0')                              \
      GEN_HASH_KEY((cache), putStr, (joinNodes));                \
    hashMapPut((cache)->planCache, (cache)->putStr, (planInfo)); \
  } while (0)

#define PLAN_CACHE_GET_PLAN(cache, joinNodes)                    \
  ({                                                             \
    GEN_HASH_KEY((cache), getStr, (joinNodes));                  \
    (PlanInfo *)hashMapGet((cache)->planCache, (cache)->getStr); \
  })

#define PLAN_CACHE_REMOVE_PLAN(cache, joinNodes)                \
  ({                                                            \
    PlanInfo *planInfo = NULL;                                  \
    GEN_HASH_KEY((cache), getStr, (joinNodes));                 \
    planInfo = hashMapGet((cache)->planCache, (cache)->getStr); \
    hashMapRemove((cache)->planCache, (cache)->getStr);         \
    planInfo;                                                   \
  })

#endif  // PLAN_CACHE_H
