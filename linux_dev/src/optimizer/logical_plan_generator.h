/*
 * @Author: zql <EMAIL>
 * @Date: 2025-04-14 10:44:19
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-06-19 17:16:50
 * @FilePath: /gncdbflr/linux_dev/src/optimizer/logical_plan_generator.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
#ifndef LOGICAL_PLAN_GENERATOR_H
#define LOGICAL_PLAN_GENERATOR_H
#include "calc_stmt.h"
#include "select_stmt.h"
#include "filter_stmt.h"
#include "insert_stmt.h"
#include "delete_stmt.h"
#include "explain_stmt.h"
#include "logical_operator.h"
#include "update_stmt.h"
typedef struct TableGetLogicalOperator TableGetLogicalOperator;
typedef struct JoinNode                JoinNode;

#define TABLE_LOGI_CONSTRUCT(tableGetOper, btable)                                          \
  do {                                                                                     \
    tableGetOper = (TableGetLogicalOperator *)my_malloc0(sizeof(TableGetLogicalOperator)); \
    TableGetLogicalOperatorInit(&tableGetOper);                                            \
    tableGetOper->type   = LO_TABLE_GET;                                                  \
    tableGetOper->table  = btable;                                                         \
    tableGetOper->fields = NULL;                                                          \
  } while (0)

int  logicalPlanConstruct(SQLStageEvent *sqlEvent, Stmt *stmt, LogicalOperator **logicalOperator);
int  calcLogicalPlanConstruct(SQLStageEvent *sqlEvent, CalcStmt *calcStmt, LogicalOperator **logicalOperator);
int  selectLogicalPlanConstruct(SQLStageEvent *sqlEvent, SelectStmt *selectStmt, LogicalOperator **logicalOperator);
int  JoinLogiConstruct(LogicalOperator **prevOper, JoinNode *joinNode, HashMap *joinedMap, SQLStageEvent *sqlEvent);
void TableGetLogicalOperatorInit(TableGetLogicalOperator **tableGetLogiOper);
int  filterLogicalPlanConstruct(SQLStageEvent *sqlEvent, FilterStmt *filterStmt, LogicalOperator **logicalOperator);
int  insertLogicalPlanConstruct(SQLStageEvent *sqlEvent, InsertStmt *insertStmt, LogicalOperator **logicalOperator);
int  deleteLogicalPlanConstruct(SQLStageEvent *sqlEvent, DeleteStmt *deleteStmt, LogicalOperator **logicalOperator);
int  explainLogicalPlanConstruct(SQLStageEvent *sqlEvent, ExplainStmt *explainStmt, LogicalOperator **logicalOperator);
int  updateLogicalPlanConstruct(SQLStageEvent *sqlEvent, UpdateStmt *updateStmt, LogicalOperator **logicalOperator);
int  groupbyLogicalPlanConstruct(SQLStageEvent *sqlEvent, GroupByStmt *groupbyStmt, LogicalOperator **logicalOperator);
int  createTableLogicalPlanConstruct(
     SQLStageEvent *sqlEvent, CreateTableStmt *createTableStmt, LogicalOperator **logicalOperator);
void LogicalPlanInit(LogicalOperator **logicalOperator);
#endif