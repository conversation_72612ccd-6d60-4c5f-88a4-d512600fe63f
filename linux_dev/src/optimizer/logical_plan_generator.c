#include "logical_plan_generator.h"
#include "btreetable.h"
#include "calc_logical_operator.h"
#include "catalog.h"
#include "execute_stage.h"
#include "expression.h"
#include "filter_stmt.h"
#include "gncdbconstant.h"
#include "hashmap.h"
#include "logical_operator.h"
#include "select_stmt.h"
#include "sql_event.h"
#include "table_get_logical_operator.h"
#include "join_logical_operator.h"
#include "predicate_logical_operator.h"
#include "project_logical_operator.h"
#include "insert_logical_operator.h"
#include "delete_logical_operator.h"
#include "explain_logical_operator.h"
#include "update_logical_operator.h"
#include "groupby_logical_operator.h"
#include "orderby_logical_operator.h"
#include "limit_logical_operator.h"
#include "create_table_logical_operator.h"
#include "create_table_stmt.h"
#include <assert.h>
#include <stdbool.h>
#include <string.h>
#include "utils.h"
#include "value.h"
#include "vararraylist.h"

/**
 * @description: 将JoinNode转换为JoinLogicalOperator
 * @param {JoinNode} *joinNode 需要转换的JoinNode
 * @param {JoinLogicalOperator} *joinOper 转换后的JoinLogicalOperator指针
 * @note: 该宏会分配内存给joinOper，调用者需要负责释放
 * @note: joinOper会浅拷贝pkExprs、expressions、otherExprs
 * @return {*}
 */
#define JOINNODE_TO_LOGIOPER(joinNode, joinOper)                               \
  do {                                                                         \
    joinOper = (JoinLogicalOperator *)my_malloc0(sizeof(JoinLogicalOperator)); \
    JoinLogicalOperatorInit(&joinOper);                                        \
    joinOper->joinType    = joinNode->joinType;                                \
    joinOper->pkExprs     = varArrayListCopy(joinNode->eqPKExprs);             \
    joinOper->expressions = varArrayListCopy(joinNode->eqExprs);               \
    joinOper->otherExprs  = varArrayListCopy(joinNode->otherExprs);            \
  } while (0)

extern int ruleBasedOptimization(SQLStageEvent *sqlEvent, LogicalOperator *logicalOperator, bool isSingleTable);
void       UpdateLogicalOperatorInit(UpdateLogicalOperator **logicalOperator)
{
  (*logicalOperator)->type             = LO_UPDATE;
  (*logicalOperator)->expressions      = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*logicalOperator)->children         = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*logicalOperator)->updateValues     = NULL;
  (*logicalOperator)->updateFieldNames = NULL;
}
void CalcLogicalOperatorInit(CalcLogicalOperator **calcLogiOper)
{
  (*calcLogiOper)->type        = LO_CALC;
  (*calcLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*calcLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
}
void TableGetLogicalOperatorInit(TableGetLogicalOperator **tableGetLogiOper)
{
  (*tableGetLogiOper)->type        = LO_TABLE_GET;
  (*tableGetLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, NULL);
  (*tableGetLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*tableGetLogiOper)->fields      = NULL;
  (*tableGetLogiOper)->table       = NULL;
  (*tableGetLogiOper)->readOnly    = false;
}

void JoinLogicalOperatorInit(JoinLogicalOperator **joinLogiOper)
{
  (*joinLogiOper)->type     = LO_JOIN;
  (*joinLogiOper)->joinType = JT_NESTED_LOOP;
  // (*joinLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*joinLogiOper)->children = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
}

void PredicateLogicalOperatorInit(PredicateLogicalOperator **predicateLogiOper)
{
  (*predicateLogiOper)->type        = LO_PREDICATE;
  (*predicateLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*predicateLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
}

void ProjectLogicalOperatorInit(ProjectLogicalOperator **projectLogiOper)
{
  (*projectLogiOper)->type        = LO_PROJECTION;
  (*projectLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*projectLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
}

void InsertLogicalOperatorInit(InsertLogicalOperator **insertLogiOper)
{
  (*insertLogiOper)->type        = LO_INSERT;
  (*insertLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*insertLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*insertLogiOper)->table       = NULL;
  (*insertLogiOper)->valuelists  = NULL;
}

void DeleteLogicalOperatorInit(DeleteLogicalOperator **deleteLogiOper)
{
  (*deleteLogiOper)->type        = LO_DELETE;
  (*deleteLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*deleteLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*deleteLogiOper)->table       = NULL;
}

void ExplainLogicalOperatorInit(ExplainLogicalOperator **explainLogiOper)
{
  (*explainLogiOper)->type        = LO_EXPLAIN;
  (*explainLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*explainLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
}

void GroupByLogicalOperatorInit(GroupByLogicalOperator **groupbyLogiOper)
{
  (*groupbyLogiOper)->type          = LO_GROUPBY;
  (*groupbyLogiOper)->expressions   = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*groupbyLogiOper)->children      = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*groupbyLogiOper)->groupbyFields = NULL;
  (*groupbyLogiOper)->aggExprs      = NULL;
  (*groupbyLogiOper)->fieldExprs    = NULL;
}

void OrderByLogicalOperatorInit(OrderByLogicalOperator **orderbyLogiOper)
{
  (*orderbyLogiOper)->type         = LO_ORDERBY;
  (*orderbyLogiOper)->expressions  = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*orderbyLogiOper)->children     = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*orderbyLogiOper)->orderbyUnits = NULL;
  (*orderbyLogiOper)->exprs        = NULL;
}

void CreateTableLogicalOperatorInit(CreateTableLogicalOperator **createTableLogiOper)
{
  (*createTableLogiOper)->type        = LO_CREATE_TABLE;
  (*createTableLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*createTableLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*createTableLogiOper)->attrInfos   = NULL;
  (*createTableLogiOper)->tableName   = NULL;
  (*createTableLogiOper)->db          = NULL;
}

void LimitLogicalOperatorInit(LimitLogicalOperator **limitLogiOper)
{
  (*limitLogiOper)->type        = LO_LIMIT;
  (*limitLogiOper)->expressions = varArrayListCreate(DISORDER, sizeof(Expression *), 0, NULL, exprPtrDestroy);
  (*limitLogiOper)->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
  (*limitLogiOper)->limit       = 0;
  (*limitLogiOper)->offset      = 0;
}
extern void FillField(Field **field, Column *column);
int         logicalPlanConstruct(SQLStageEvent *sqlEvent, Stmt *stmt, LogicalOperator **logicalOperator)
{
  switch (stmt->type) {
    case ST_SELECT: {
      return selectLogicalPlanConstruct(sqlEvent, (SelectStmt *)stmt, logicalOperator);
    }
    // case ST_CALC: {
    //     return calcLogicalPlanConstruct(sqlEvent, (CalcStmt *)stmt, logicalOperator);
    // }
    case ST_INSERT: {
      return insertLogicalPlanConstruct(sqlEvent, (InsertStmt *)stmt, logicalOperator);
    }
    case ST_DELETE: {
      return deleteLogicalPlanConstruct(sqlEvent, (DeleteStmt *)stmt, logicalOperator);
    }
    case ST_EXPLAIN: {
      return explainLogicalPlanConstruct(sqlEvent, (ExplainStmt *)stmt, logicalOperator);
    }
    case ST_UPDATE: {
      return updateLogicalPlanConstruct(sqlEvent, (UpdateStmt *)stmt, logicalOperator);
    }
    case ST_GROUP_BY: {
      return groupbyLogicalPlanConstruct(sqlEvent, (GroupByStmt *)stmt, logicalOperator);
    }
    case ST_CREATE_TABLE: {
      return createTableLogicalPlanConstruct(sqlEvent, (CreateTableStmt *)stmt, logicalOperator);
    }
    default: return GNCDB_SUCCESS;
  }
}

/**
 * @brief 创建create table的逻辑计划
 *
 * @param db
 * @param create_table_stmt
 * @param logicalOperator
 * @return int
 */
int createTableLogicalPlanConstruct(
    SQLStageEvent *sqlEvent, CreateTableStmt *createTableStmt, LogicalOperator **logicalOperator)
{
  int                         rc               = GNCDB_SUCCESS;
  LogicalOperator            *selectOper       = NULL;
  CreateTableLogicalOperator *createTableOper  = NULL;
  Stmt                       *createSelectStmt = NULL;
  GNCDB                      *db               = NULL;
  /*1.参数检查*/
  if (sqlEvent == NULL || sqlEvent->db == NULL || createTableStmt == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.若建表stmt的selectstmt不为空则构建select逻辑算子*/
  createSelectStmt = createTableStmt->selectStmt;
  db               = sqlEvent->db;
  if (createSelectStmt != NULL) {
    rc = logicalPlanConstruct(sqlEvent, createSelectStmt, &selectOper);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to create select logical plan. rc=%d", rc);
      return rc;
    }
  }

  /*3.构造建表逻辑算子*/
  createTableOper = (CreateTableLogicalOperator *)my_malloc0(sizeof(CreateTableLogicalOperator));
  if (createTableOper == NULL) {
    LogicalOperatorDestroy(&selectOper);
    return GNCDB_MEM;
  }
  CreateTableLogicalOperatorInit(&createTableOper);
  createTableOper->attrInfos = PTR_MOVE((void **)&createTableStmt->attrInfos);
  createTableOper->tableName = PTR_MOVE((void **)&createTableStmt->tableName);
  createTableOper->db        = db;
  if (selectOper != NULL) {
    varArrayListAddPointer(createTableOper->children, selectOper);
  }
  *logicalOperator = (LogicalOperator *)createTableOper;

  return rc;
}

/**
 * @brief   创建group by的逻辑计划
 *
 * @param sqlEvent
 * @param groupbyStmt   group by stmt(所有权转移)
 * @param logicalOperator
 * @return int
 */
int groupbyLogicalPlanConstruct(SQLStageEvent *sqlEvent, GroupByStmt *groupbyStmt, LogicalOperator **logicalOperator)
{
  GroupByLogicalOperator *groupbyOper = NULL;
  /*1.参数检查*/
  if (sqlEvent == NULL || groupbyStmt == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构造分组聚合算子*/
  groupbyOper = (GroupByLogicalOperator *)my_malloc0(sizeof(GroupByLogicalOperator));
  if (groupbyOper == NULL) {
    return GNCDB_MEM;
  }
  GroupByLogicalOperatorInit(&groupbyOper);
  groupbyOper->groupbyFields = PTR_MOVE((void **)&groupbyStmt->groupbyFields);
  groupbyOper->aggExprs      = PTR_MOVE((void **)&groupbyStmt->aggExprs);
  groupbyOper->fieldExprs    = PTR_MOVE((void **)&groupbyStmt->fieldExprs);
  (*logicalOperator)         = (LogicalOperator *)groupbyOper;

  return GNCDB_SUCCESS;
}

int updateLogicalPlanConstruct(SQLStageEvent *sqlEvent, UpdateStmt *updateStmt, LogicalOperator **logicalOperator)
{
  int                      rc            = GNCDB_SUCCESS;
  LogicalOperator         *tableOper     = NULL;
  BtreeTable              *table         = NULL;
  TableGetLogicalOperator *tableGetOper  = NULL;
  LogicalOperator         *predicateOper = NULL;
  UpdateLogicalOperator   *updateOper    = NULL;

  /*1.参数检查*/
  if (sqlEvent == NULL || updateStmt == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构造表扫描算子*/
  table = updateStmt->table;
  TABLE_LOGI_CONSTRUCT(tableGetOper, table);

  /*3.构造过滤算子*/
  tableOper = (LogicalOperator *)tableGetOper;
  if (updateStmt->filterStmt != NULL) {
    rc = filterLogicalPlanConstruct(sqlEvent, updateStmt->filterStmt, &predicateOper);
    if (rc != GNCDB_SUCCESS) {
      TableGetLogiOperDestroy(tableGetOper);
      printf("failed to create predicate logical plan. rc=%d", rc);
      return rc;
    }
  }

  /*4.构造更新算子*/
  updateOper = (UpdateLogicalOperator *)my_malloc0(sizeof(UpdateLogicalOperator));
  if (updateOper == NULL) {
    TableGetLogiOperDestroy(tableGetOper);
    LogicalOperatorDestroy(&predicateOper);
    return GNCDB_MEM;
  }
  UpdateLogicalOperatorInit(&updateOper);

  updateOper->tableName = updateStmt->table->tableName ? my_strdup(updateStmt->table->tableName) : NULL;

  /*5.若存在过滤算子则加入update的children中*/
  if (predicateOper != NULL && predicateOper->expressions != NULL) {
    if (tableOper != NULL) {
      varArrayListAddPointer(predicateOper->children, tableOper);
    }
    varArrayListAddPointer(updateOper->children, predicateOper);
  } else {
    PhysicalPlanDestroy((PhysicalOperator *)predicateOper);
    if (tableOper != NULL) {
      varArrayListAddPointer(updateOper->children, tableOper);
    }
  }
  updateOper->type             = LO_UPDATE;
  updateOper->updateValues     = PTR_MOVE((void **)&updateStmt->updateValues);
  updateOper->updateFieldNames = PTR_MOVE((void **)&updateStmt->updateFieldNames);
  *logicalOperator             = (LogicalOperator *)updateOper;

  return rc;
}

int calcLogicalPlanConstruct(SQLStageEvent *sqlEvent, CalcStmt *calcStmt, LogicalOperator **logicalOperator)
{
  CalcLogicalOperator *calcOper = NULL;

  calcOper = (CalcLogicalOperator *)my_malloc0(sizeof(CalcLogicalOperator));
  if (calcOper == NULL) {
    return GNCDB_MEM;
  }
  CalcLogicalOperatorInit(&calcOper);
  calcOper->type = LO_CALC;
  if (calcOper->expressions != NULL) {  // 这里是为了释放init时候分配的内存
    varArrayListDestroy(&calcOper->expressions);
  }
  calcOper->expressions = PTR_MOVE((void **)&calcStmt->expressions);
  *logicalOperator      = (LogicalOperator *)calcOper;

  return GNCDB_SUCCESS;
}

/**
 * @description: 根据JoinNode构建JoinLogicalOperator
 * @param {LogicalOperator} *prevOper 之前的逻辑算子
 * @param {JoinNode} *joinNode 连接结点
 * @param {HashMap} *joinedMap 在本次连接中已经构造过的表 key：tableName， value：tableGetOper
 * @param {SQLStageEvent} *sqlEvent sqlEvent->tabGetLogiOperMap用以缓存之前构造过的表算子
 * @return {*}
 */
int JoinLogiConstruct(LogicalOperator **prevOper, JoinNode *joinNode, HashMap *joinedMap, SQLStageEvent *sqlEvent)
{
  TableGetLogicalOperator *tableGetOper1 = NULL;
  TableGetLogicalOperator *tableGetOper2 = NULL;
  JoinLogicalOperator     *joinOper      = NULL;
  BtreeTable              *leftTable     = NULL;
  BtreeTable              *rightTable    = NULL;

  /*1.是不是连接条件的第一个joinnode，比如连接条件是A join B join C，AB连接走true，连接C时走false*/
  if (*prevOper == NULL) {
    /*1.1找到或构造左表的扫描算子*/
    if (sqlEvent->tabOperMap != NULL && hashMapExists(sqlEvent->tabOperMap, joinNode->tableName1)) {
      tableGetOper1 = (TableGetLogicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName1);
    } else {
      catalogGetTable(sqlEvent->db->catalog, &leftTable, joinNode->tableName1);
      if (leftTable == NULL) {
        return GNCDB_TABLE_NOT_FOUND;
      }
      TABLE_LOGI_CONSTRUCT(tableGetOper1, leftTable);
    }

    /*1.2找到或构造右表的扫描算子*/
    if (sqlEvent->tabOperMap != NULL && hashMapExists(sqlEvent->tabOperMap, joinNode->tableName2)) {
      tableGetOper2 = (TableGetLogicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName2);
    } else {
      catalogGetTable(sqlEvent->db->catalog, &rightTable, joinNode->tableName2);
      if (rightTable == NULL) {
        return GNCDB_TABLE_NOT_FOUND;
      }
      TABLE_LOGI_CONSTRUCT(tableGetOper2, rightTable);
    }

    /*1.3根据joinode中的值以及两个表的表扫描算子构造join逻辑算子*/
    JOINNODE_TO_LOGIOPER(joinNode, joinOper);
    varArrayListAddPointer(joinOper->children, tableGetOper1);
    varArrayListAddPointer(joinOper->children, tableGetOper2);
    hashMapPut(joinedMap, joinNode->tableName1, tableGetOper1);
    hashMapPut(joinedMap, joinNode->tableName2, tableGetOper2);
    hashMapPut(sqlEvent->tabOperMap, joinNode->tableName1, tableGetOper1);
    hashMapPut(sqlEvent->tabOperMap, joinNode->tableName2, tableGetOper2);
    *prevOper = (LogicalOperator *)joinOper;
  } else {
    /*2.左表在之前连接过*/
    if (hashMapExists(joinedMap, joinNode->tableName1)) {
      /*2.1左表存在，则找到或构造右表的扫描算子*/
      if (hashMapExists(sqlEvent->tabOperMap, joinNode->tableName2)) {
        tableGetOper2 = (TableGetLogicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName2);
      } else {
        catalogGetTable(sqlEvent->db->catalog, &rightTable, joinNode->tableName2);
        if (rightTable == NULL) {
          return GNCDB_TABLE_NOT_FOUND;
        }
        TABLE_LOGI_CONSTRUCT(tableGetOper2, rightTable);
      }
      /*2.1.1构造连接算子*/
      JOINNODE_TO_LOGIOPER(joinNode, joinOper);
      varArrayListAddPointer(joinOper->children, *prevOper);
      varArrayListAddPointer(joinOper->children, (LogicalOperator *)tableGetOper2);
      hashMapPut(sqlEvent->tabOperMap, joinNode->tableName2, tableGetOper2);
      hashMapPut(joinedMap, joinNode->tableName2, tableGetOper2);
      *prevOper = (LogicalOperator *)joinOper;
    } else if (hashMapExists(joinedMap, joinNode->tableName2)) {
      /*2.2右表存在，则找到或构造左表的扫描算子*/
      if (hashMapExists(sqlEvent->tabOperMap, joinNode->tableName1)) {
        tableGetOper1 = (TableGetLogicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName1);
      } else {
        catalogGetTable(sqlEvent->db->catalog, &leftTable, joinNode->tableName1);
        if (leftTable == NULL) {
          return GNCDB_TABLE_NOT_FOUND;
        }
        TABLE_LOGI_CONSTRUCT(tableGetOper1, leftTable);
      }

      /*2.2.2构造连接算子*/
      JOINNODE_TO_LOGIOPER(joinNode, joinOper);
      varArrayListAddPointer(joinOper->children, (LogicalOperator *)tableGetOper1);
      varArrayListAddPointer(joinOper->children, *prevOper);
      hashMapPut(sqlEvent->tabOperMap, joinNode->tableName1, tableGetOper1);
      hashMapPut(joinedMap, joinNode->tableName1, tableGetOper1);
      *prevOper = (LogicalOperator *)joinOper;
    } else {
      /*3.处理特殊情况*/
      LogicalPlanDestroy(prevOper);
      return GNCDB_INTERNAL;
    }
  }

  return GNCDB_SUCCESS;
}

int selectLogicalPlanConstruct(SQLStageEvent *sqlEvent, SelectStmt *selectStmt, LogicalOperator **logicalOperator)
{
  int                      i                   = 0;
  int                      j                   = 0;
  int                      rc                  = GNCDB_SUCCESS;
  JoinTables              *joinTables          = NULL;
  SubJoin                 *subJoin             = NULL;
  SubJoin                 *leftSubJoin         = NULL;
  SubJoin                 *rightSubJoin        = NULL;
  JoinNode                *joinNode            = NULL;
  HashMap                 *subJoinedMap        = NULL;
  varArrayList            *joinLogiOperList    = NULL;
  LogicalOperator         *outsidePrevOper     = NULL;
  LogicalOperator         *topOper             = NULL;
  LogicalOperator         *predicateOper       = NULL;
  varArrayList            *groupbyFields       = NULL;
  varArrayList            *orderUnits          = NULL;
  Expression              *expr                = NULL;
  OrderByUnit             *orderUnit           = NULL;
  varArrayList            *fieldExprs          = NULL;
  varArrayList            *field               = NULL;
  varArrayList            *tmp                 = NULL;
  OrderByLogicalOperator  *orderbyOper1        = NULL;
  LogicalOperator         *groupbyOper         = NULL;
  LogicalOperator         *havingPredicateOper = NULL;
  OrderByLogicalOperator  *orderbyOper2        = NULL;
  LimitLogicalOperator    *limitOper           = NULL;
  ProjectLogicalOperator  *projectOper         = NULL;
  BtreeTable              *table               = NULL;
  TableGetLogicalOperator *tableGetOper        = NULL;
  JoinLogicalOperator     *joinOper            = NULL;
  HashMapIterator         *iter                = NULL;
  int                      leftIdx             = -1;
  int                      rightIdx            = -1;

  if (sqlEvent == NULL || selectStmt == NULL) {
    return GNCDB_PARAMNULL;
  }
  joinTables = selectStmt->joinTables;
  if (joinTables->tables->elementCount == 1) {
    /*1.只有一张表的情况*/
    table = varArrayListGetPointer(joinTables->tables, 0);
    TABLE_LOGI_CONSTRUCT(tableGetOper, table);
    outsidePrevOper = (LogicalOperator *)tableGetOper;
    subJoin         = varArrayListGetPointer(joinTables->subJoins, 0);
  } else {
    /* 2.多表连接查询的情况 */
    sqlEvent->tabOperMap = hashMapCreate(STRKEY, 0, NULL);
    subJoinedMap         = hashMapCreate(STRKEY, 0, NULL);
    joinLogiOperList     = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);  // <LogicalOperator*>
    /* 2.1.处理各个subJoin：构造每个subJoin内部的Join算子或表扫描算子 */
    for (i = 0; i < joinTables->subJoins->elementCount; i++) {
      subJoin = varArrayListGetPointer(joinTables->subJoins, i);
      /*2.1.1如果该subJoin只有基表，也要构建表扫描算子*/
      if (subJoin->tabMap->entryCount == 1) {
        iter  = createHashMapIterator(subJoin->tabMap);
        iter  = nextHashMapIterator(iter);
        table = (BtreeTable *)iter->entry->value;
        if (table == NULL) {
          return GNCDB_TABLE_NOT_FOUND;
        }
        TABLE_LOGI_CONSTRUCT(tableGetOper, table);
        varArrayListAddPointer(joinLogiOperList, tableGetOper);
        hashMapPut(sqlEvent->tabOperMap, table->tableName, tableGetOper);
        freeHashMapIterator(&iter);
        /*单表一定没有连接条件直接返回*/
        continue;
      }

      /*2.1.2处理subJoin的joinNode*/
      for (j = 0; j < subJoin->joinNodes->elementCount; j++) {
        joinNode = varArrayListGetPointer(subJoin->joinNodes, j);
        rc       = JoinLogiConstruct(&outsidePrevOper, joinNode, subJoinedMap, sqlEvent);
        if (rc != GNCDB_SUCCESS) {
          LogicalPlanDestroy(&topOper);
          return rc;
        }
      }

      /*2.1.3如果还存在表没有连接条件，需要找出这些表并手动构造笛卡尔积*/
      if (subJoin->tabMap->entryCount > subJoinedMap->entryCount) {
        iter = createHashMapIterator(subJoin->tabMap);
        while (hasNextHashMapIterator(iter)) {
          iter = nextHashMapIterator(iter);
          /*该表已经连接过，跳过*/
          if (hashMapExists(subJoinedMap, iter->entry->key)) {
            continue;  // next table
          }
          /*未连接过，构造表扫描算子*/
          table = (BtreeTable *)iter->entry->value;
          TABLE_LOGI_CONSTRUCT(tableGetOper, table);
          hashMapPut(sqlEvent->tabOperMap, table->tableName, tableGetOper);

          /*前面是否有过其它连接*/
          if (!outsidePrevOper) {
            outsidePrevOper = (LogicalOperator *)tableGetOper;
          } else {
            /*相当于把没有连接条件的和已经连接过的做一次连接*/
            joinOper              = (JoinLogicalOperator *)my_malloc0(sizeof(JoinLogicalOperator));
            joinOper->type        = LO_JOIN;
            joinOper->joinType    = JT_NESTED_LOOP;
            joinOper->expressions = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
            joinOper->children    = varArrayListCreate(DISORDER, sizeof(LogicalOperator *), 0, NULL, NULL);
            varArrayListAddPointer(joinOper->children, outsidePrevOper);
            varArrayListAddPointer(joinOper->children, tableGetOper);
            outsidePrevOper = (LogicalOperator *)joinOper;
          }
        }
        freeHashMapIterator(&iter);
      }
      if (outsidePrevOper) {
        varArrayListAddPointer(joinLogiOperList, outsidePrevOper);
        outsidePrevOper = NULL;
        hashMapClear(subJoinedMap);
      }
    }
    hashMapDestroy(&subJoinedMap);

    /* 2.2.处理topJoinNode：顶层joinNode连接各个subJoin*/
    for (i = 0; i < joinTables->topJoinNode->elementCount; i++) {
      joinNode = varArrayListGetPointer(joinTables->topJoinNode, i);
      // todo 这里目前通过遍历subJoins来找到左右子join，应该可以进行优化
      for (j = 0; j < joinTables->subJoins->elementCount; j++) {
        /*2.2.1遍历每一个subjoin，找到该topjoin对应的leftjoin和rightjoin*/
        subJoin = varArrayListGetPointer(joinTables->subJoins, j);
        if (leftIdx < 0) {
          if (hashMapExists(subJoin->tabMap, joinNode->tableName1)) {
            leftIdx     = j;
            leftSubJoin = subJoin;
          }
        }
        if (rightIdx < 0) {
          if (hashMapExists(subJoin->tabMap, joinNode->tableName2)) {
            rightIdx     = j;
            rightSubJoin = subJoin;
          }
        }
        if (leftIdx >= 0 && rightIdx >= 0) {
          break;
        }
      }
      assert(leftIdx >= 0 && rightIdx >= 0);

      /*2.2.2构造连接算子*/
      /* 删除右算子，用新构造父join算子替换左算子 */
      JOINNODE_TO_LOGIOPER(joinNode, joinOper);
      varArrayListAddPointer(joinOper->children, varArrayListGetPointer(joinLogiOperList, leftIdx));
      varArrayListAddPointer(joinOper->children, varArrayListGetPointer(joinLogiOperList, rightIdx));
      /*将原左右孩子算子从队列删除并加入新构造的父join算子，父join算子是由左右两个join算子构造*/
      varArrayListRemoveByIndexPointer(joinLogiOperList, rightIdx);
      /* 这一步的操作是删除右算子时修正左索引 */
      if (rightIdx < leftIdx) {
        leftIdx--;
      }
      varArrayListSetByIndexPointer(joinLogiOperList, leftIdx, joinOper);
      /* 将右subJoin合并到左subJoin中，仅需要删除右subJoin */
      iter = createHashMapIterator(rightSubJoin->tabMap);
      while (hasNextHashMapIterator(iter)) {
        iter = nextHashMapIterator(iter);
        hashMapPut(leftSubJoin->tabMap, iter->entry->key, iter->entry->value);
      }
      freeHashMapIterator(&iter);
      varArrayListAddPointer(leftSubJoin->joinNodes, joinNode);
      for (j = 0; j < rightSubJoin->joinNodes->elementCount; j++) {
        joinNode = varArrayListGetPointer(rightSubJoin->joinNodes, j);
        varArrayListAddPointer(leftSubJoin->joinNodes, joinNode);
      }
      varArrayListRemoveByIndexPointer(joinTables->subJoins, rightIdx);
      SUB_JOIN_DESTROY(rightSubJoin);
    }
    outsidePrevOper = varArrayListGetPointer(joinLogiOperList, 0);
    assert(outsidePrevOper);

    /* 2.3.剩余没有连接条件的表构造笛卡尔积，将其中随机连接在一起*/
    /*Q:这里和subjoin那里是否重复了？*/
    if (joinTables->subJoins->elementCount > 1) {
      for (i = 1; i < joinTables->subJoins->elementCount; i++) {
        joinOper = (JoinLogicalOperator *)my_malloc0(sizeof(JoinLogicalOperator));
        JoinLogicalOperatorInit(&joinOper);
        varArrayListAddPointer(joinOper->children, outsidePrevOper);
        varArrayListAddPointer(joinOper->children, varArrayListGetPointer(joinLogiOperList, i));
        outsidePrevOper = (LogicalOperator *)joinOper;
      }
    }
    varArrayListDestroy(&joinLogiOperList);
  }
  assert(outsidePrevOper);
  topOper         = outsidePrevOper;
  outsidePrevOper = NULL;  /*赋值为NULL，避免重复释放*/

  /* 3.处理where从句 */
  if (selectStmt->filterStmt != NULL) {
    rc = filterLogicalPlanConstruct(sqlEvent, selectStmt->filterStmt, &predicateOper);
    if (rc != GNCDB_SUCCESS) {
      LogicalPlanDestroy(&topOper);
      return rc;
    }
    if (predicateOper) {
      if (topOper) {
        varArrayListAddPointer(predicateOper->children, topOper);
      }
      topOper       = predicateOper;
      predicateOper = NULL;
    }
  }

  /* 4.处理orderby从句 */
  if (selectStmt->orderbyStmt != NULL) {
    orderbyOper2 = (OrderByLogicalOperator *)my_malloc0(sizeof(OrderByLogicalOperator));
    if (orderbyOper2 == NULL) {
      LogicalPlanDestroy(&topOper);
      return GNCDB_MEM;
    }
    OrderByLogicalOperatorInit(&orderbyOper2);
    orderbyOper2->orderbyUnits = PTR_MOVE((void **)&selectStmt->orderbyStmt->orderByUnits);
    orderbyOper2->exprs        = PTR_MOVE((void **)&selectStmt->orderbyStmt->exprs);
    if (topOper) {
      varArrayListAddPointer(orderbyOper2->children, topOper);
      topOper = NULL;
    }
    topOper      = (LogicalOperator *)orderbyOper2;
    orderbyOper2 = NULL;
  }

  /* 5.处理groupby从句 */
  if (selectStmt->groupbyStmt != NULL) {
    /*5.1 OrderByUnit*/
    groupbyFields = selectStmt->groupbyStmt->groupbyFields;

    if (selectStmt->groupbyStmt->groupbyFields->elementCount != 0) {
      orderUnits = varArrayListCreate(
          DISORDER, sizeof(OrderByUnit *), 0, NULL, OrderByUnitPointerDestroy);  // element type:<OrderByUnit*>
      for (i = 0; i < groupbyFields->elementCount; i++) {
        expr      = varArrayListGetPointer(groupbyFields, i);
        orderUnit = (OrderByUnit *)my_malloc0(sizeof(OrderByUnit));
        if (orderUnit == NULL) {
          LogicalPlanDestroy(&topOper);
          varArrayListDestroy(&orderUnits);
          return GNCDB_MEM;
        }
        // TODO: 改进代码，exprDeepCopy冗余
        orderUnit->expr  = exprDeepCopy(expr);
        orderUnit->isAsc = true;
        varArrayListAddPointer(orderUnits, orderUnit);
      }

      /* 5.2 .需要将 groupy_oper 中的 field_expr ,和 groupby  后的expr  复制一份传递给 orderby 算子 */
      fieldExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      field      = selectStmt->groupbyStmt->fieldExprs;
      for (i = 0; i < field->elementCount; i++) {
        expr = varArrayListGetPointer(field, i);
        varArrayListAddPointer(fieldExprs, exprDeepCopy(expr));
      }

      tmp = selectStmt->groupbyStmt->groupbyFields;
      for (i = 0; i < tmp->elementCount; i++) {
        expr = varArrayListGetPointer(tmp, i);
        varArrayListAddPointer(fieldExprs, exprDeepCopy(expr));
      }
      orderbyOper1 = (OrderByLogicalOperator *)my_malloc0(sizeof(OrderByLogicalOperator));
      if (orderbyOper1 == NULL) {
        varArrayListDestroy(&orderUnits);
        varArrayListDestroy(&fieldExprs);
        LogicalPlanDestroy(&topOper);
        return GNCDB_MEM;
      }
      OrderByLogicalOperatorInit(&orderbyOper1);
      orderbyOper1->orderbyUnits = PTR_MOVE((void **)&orderUnits);
      orderbyOper1->exprs        = PTR_MOVE((void **)&fieldExprs);  /*Q: 作用是什么？*/
      if (topOper) {
        varArrayListAddPointer(orderbyOper1->children, topOper);
        topOper = NULL;
      }
      topOper      = (LogicalOperator *)orderbyOper1;
      orderbyOper1 = NULL;
    }

    rc = groupbyLogicalPlanConstruct(sqlEvent, (selectStmt->groupbyStmt), &groupbyOper);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to create groupby logical plan. rc=%d\n", rc);
      varArrayListDestroy(&orderUnits);
      varArrayListDestroy(&fieldExprs);
      LogicalPlanDestroy(&topOper);
      return rc;
    }
    varArrayListAddPointer(groupbyOper->children, topOper);
    topOper     = groupbyOper;
    groupbyOper = NULL;
  }

  /* 6.处理having从句 */
  if (selectStmt->havingStmt != NULL) {
    rc = filterLogicalPlanConstruct(sqlEvent, selectStmt->havingStmt, &havingPredicateOper);
    if (rc != GNCDB_SUCCESS) {
      // printf("failed to create having predicate logical plan. rc=%d", rc);
      LogicalPlanDestroy(&topOper);
      return rc;
    }
    if (havingPredicateOper) {
      if (topOper) {
        varArrayListAddPointer(havingPredicateOper->children, topOper);
        topOper = NULL;
      }
      topOper             = havingPredicateOper;
      havingPredicateOper = NULL;
    }
  }

  /* 7.处理limit从句 */
  if (selectStmt->limitStmt != NULL) {
    limitOper = (LimitLogicalOperator *)my_malloc0(sizeof(LimitLogicalOperator));
    if (limitOper == NULL) {
      LogicalPlanDestroy(&topOper);
      return GNCDB_MEM;
    }
    LimitLogicalOperatorInit(&limitOper);
    limitOper->limit  = selectStmt->limitStmt->limit;
    limitOper->offset = selectStmt->limitStmt->offset;
    if (topOper) {
      varArrayListAddPointer(limitOper->children, topOper);
      topOper = NULL;
    }
    topOper   = (LogicalOperator *)limitOper;
    limitOper = NULL;
  }

  /* 8.处理投影 */
  {
    projectOper = (ProjectLogicalOperator *)my_malloc0(sizeof(ProjectLogicalOperator));
    if (projectOper == NULL) {
      LogicalPlanDestroy(&topOper);
      return GNCDB_MEM;
    }
    ProjectLogicalOperatorInit(&projectOper);
    if (projectOper->expressions != NULL) {
      varArrayListDestroy(&projectOper->expressions);
    }
    projectOper->expressions = PTR_MOVE((void **)&selectStmt->queryFields);
    projectOper->isDistinct  = selectStmt->isDistinct;
    if (topOper) {
      varArrayListAddPointer(projectOper->children, topOper);
      topOper = NULL;
    }
    topOper     = (LogicalOperator *)projectOper;
    projectOper = NULL;
  }
  *logicalOperator = (LogicalOperator *)topOper;
  return GNCDB_SUCCESS;
}

/**
 * @brief 如果是子查询，则生成子查询的逻辑计划，并进行logicalPlanRewrite
 *
 * @param expr
 * @param db
 * @return int
 */
int processSubQueryLogical(Expression *expr, varArrayList *list, int addtionalParmNum, va_list args)
{
  int              rc                  = GNCDB_SUCCESS;
  SQLStageEvent   *sqlEvent            = NULL;
  SubQueryExpr    *subQueryExpr        = NULL;
  LogicalOperator *subQueryLogicalOper = NULL;
  bool             isSingleTable       = false;

  if (expr->type == ETG_SUBQUERY) {
    assert(addtionalParmNum == 1);
    sqlEvent     = va_arg(args, SQLStageEvent *);
    subQueryExpr = (SubQueryExpr *)expr;

    // 生成logical plan
    if (GNCDB_SUCCESS != (rc = logicalPlanConstruct(sqlEvent, (Stmt *)subQueryExpr->stmt, &subQueryLogicalOper))) {
      return rc;
    }
    // 进行逻辑计划重写
    isSingleTable = subQueryExpr->stmt->type == ST_SELECT &&
                    ((SelectStmt *)subQueryExpr->stmt)->joinTables->tables->elementCount == 1;
    rc = ruleBasedOptimization(sqlEvent, subQueryLogicalOper, isSingleTable);
#ifdef PINTF_OPERATOR_TREE
    if (subQueryLogicalOper != NULL) {
      LogicalOperatorToString(subQueryLogicalOper, 0);
    }
#endif
    if (rc != GNCDB_SUCCESS) {
      printf("failed to rewrite sub query plan. rc=%d", rc);
      LogicalOperatorDestroy(&subQueryLogicalOper);
      return rc;
    }
    subQueryExpr->logicalOper           = subQueryLogicalOper;
    subQueryExpr->subEvent->logicalPlan = subQueryLogicalOper;
  }
  return rc;
}

int filterLogicalPlanConstruct(SQLStageEvent *sqlEvent, FilterStmt *filterStmt, LogicalOperator **logicalOperator)
{
  int                       rc            = GNCDB_SUCCESS;
  PredicateLogicalOperator *predicateOper = NULL;

  if (filterStmt == NULL || filterStmt->condition == NULL) {
    return GNCDB_PARAM_INVALID;
  }
  predicateOper = (PredicateLogicalOperator *)my_malloc0(sizeof(PredicateLogicalOperator));
  if (predicateOper == NULL) {
    return GNCDB_MEM;
  }
  PredicateLogicalOperatorInit(&predicateOper);
  varArrayListAddPointer(predicateOper->expressions, PTR_MOVE(&filterStmt->condition));
  *logicalOperator = (LogicalOperator *)predicateOper;
  return rc;
}

int insertLogicalPlanConstruct(SQLStageEvent *sqlEvent, InsertStmt *insertStmt, LogicalOperator **logicalOperator)
{
  InsertLogicalOperator *insertOperator = NULL;
  if (sqlEvent == NULL || insertStmt == NULL) {
    return GNCDB_PARAMNULL;
  }
  insertOperator = (InsertLogicalOperator *)my_malloc0(sizeof(InsertLogicalOperator));
  if (insertOperator == NULL) {
    return GNCDB_MEM;
  }
  InsertLogicalOperatorInit(&insertOperator);
  insertOperator->type       = LO_INSERT;
  insertOperator->table      = insertStmt->table;
  insertOperator->valuelists = PTR_MOVE((void **)&insertStmt->valuelists);
  *logicalOperator           = (LogicalOperator *)insertOperator;

  return GNCDB_SUCCESS;
}

int deleteLogicalPlanConstruct(SQLStageEvent *sqlEvent, DeleteStmt *deleteStmt, LogicalOperator **logicalOperator)
{
  int                      rc            = GNCDB_SUCCESS;
  BtreeTable              *table         = NULL;
  TableGetLogicalOperator *tableGetOper  = NULL;
  LogicalOperator         *predicateOper = NULL;
  DeleteLogicalOperator   *deleteOper    = NULL;
  
  /*1.参数检查*/
  if (sqlEvent == NULL || deleteStmt == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构造表扫描算子*/
  table = deleteStmt->table;
  TABLE_LOGI_CONSTRUCT(tableGetOper, table);

  /*3.构造过滤算子*/
  if (deleteStmt->filterStmt != NULL) {
    rc = filterLogicalPlanConstruct(sqlEvent, deleteStmt->filterStmt, &predicateOper);
    if (rc != GNCDB_SUCCESS) {
      TableGetLogiOperDestroy(tableGetOper);
      return rc;
    }
  }

  /*4.构造删除算子*/
  deleteOper = (DeleteLogicalOperator *)my_malloc0(sizeof(DeleteLogicalOperator));
  if (deleteOper == NULL) {
    TableGetLogiOperDestroy(tableGetOper);
    LogicalOperatorDestroy(&predicateOper);
    return GNCDB_MEM;
  }
  DeleteLogicalOperatorInit(&deleteOper);

  /*5.根据是否有过滤算子初始化删除算子*/
  deleteOper->table = table;
  if (predicateOper != NULL && predicateOper->expressions->elementCount != 0) {
    /*5.1有则把表扫描算子是过滤的子算子，过滤算子是删除的子算子*/
    varArrayListAddPointer(predicateOper->children, tableGetOper);
    varArrayListAddPointer(deleteOper->children, predicateOper);
  } else {
    /*5.2没有则表扫描是删除的子算子*/
    PhysicalPlanDestroy((PhysicalOperator *)predicateOper);
    varArrayListAddPointer(deleteOper->children, tableGetOper);
  }
  *logicalOperator = (LogicalOperator *)deleteOper;

  return GNCDB_SUCCESS;
}

int explainLogicalPlanConstruct(SQLStageEvent *sqlEvent, ExplainStmt *explainStmt, LogicalOperator **logicalOperator)
{
  // TODO
  int                     rc          = GNCDB_SUCCESS;
  Stmt                   *childStmt   = explainStmt->childStmt;
  LogicalOperator        *childOper   = NULL;
  ExplainLogicalOperator *explainOper = NULL;
  /*1.参数检查*/
  if (sqlEvent == NULL || explainStmt == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构造要explain的sql的算子树*/
  rc = logicalPlanConstruct(sqlEvent, childStmt, &childOper);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /*3.构造explain算子*/
  explainOper = (ExplainLogicalOperator *)my_malloc0(sizeof(ExplainLogicalOperator));
  if (explainOper == NULL) {
    LogicalOperatorDestroy(&childOper);
    return GNCDB_MEM;
  }
  ExplainLogicalOperatorInit(&explainOper);
  varArrayListAddPointer(explainOper->children, childOper);

  *logicalOperator = (LogicalOperator *)explainOper;

  return GNCDB_SUCCESS;
}
