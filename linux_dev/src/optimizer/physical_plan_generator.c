#include "physical_plan_generator.h"
#include "btreetable.h"
#include "catalog.h"
#include "expression.h"
#include "hashmap.h"
#include "exec_tuple.h"
#include "gncdbconstant.h"
#include "index_scan_logical_operator.h"
#include "index_scan_physical_operator.h"
#include "insert_physical_operator.h"
#include "join_logical_operator.h"
#include "orderby_stmt.h"
#include "physical_operator.h"
#include "project_physical_operator.h"
#include "select_stmt.h"
#include "sql_event.h"
#include "table_scan_physical_operator.h"
#include "predicate_physical_operator.h"
#include "delete_physical_operator.h"
#include "typedefine.h"
#include "update_physical_operator.h"
#include "join_physical_operator.h"
#include "groupby_physical_operator.h"
#include "orderby_physical_operator.h"
#include "limit_physical_operator.h"
#include "create_table_physical_operator.h"
#include "gncdb.h"
#include <assert.h>
#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include "utils.h"
#include "execute_stage.h"
#include "vararraylist.h"
int physicalPlanConstruct(SQLStageEvent *sqlEvent, LogicalOperator *logicalOper, PhysicalOperator **physicalPlan)
{
  int rc = GNCDB_SUCCESS;

  if (logicalOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  switch (logicalOper->type) {
    case LO_CALC: rc = GNCDB_SUCCESS; break;
    case LO_TABLE_GET:
      rc = tableGetPhysicalPlanConstruct(sqlEvent, (TableGetLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_INDEX_SCAN:
      rc = indexScanPhysicalPlanConstruct(sqlEvent, (IndexScanLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_PREDICATE:
      rc = PredicatePhysicalPlanConstruct(sqlEvent, (PredicateLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_PROJECTION:
      rc = projectPhysicalPlanConstruct(sqlEvent, (ProjectLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_JOIN: rc = joinPhysicalPlanConstruct(sqlEvent, (JoinLogicalOperator *)logicalOper, physicalPlan); break;
    case LO_INSERT:
      rc = insertPhysicalPlanConstruct(sqlEvent, (InsertLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_DELETE:
      rc = deletePhysicalPlanConstruct(sqlEvent, (DeleteLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_EXPLAIN: rc = GNCDB_SUCCESS; break;
    case LO_GROUPBY:
      rc = groupByPhysicalPlanConstruct(sqlEvent, (GroupByLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_UPDATE:
      rc = updatePhysicalPlanConstruct(sqlEvent, (UpdateLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_ORDERBY:
      rc = orderByPhysicalPlanConstruct(sqlEvent, (OrderByLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_CREATE_TABLE:
      rc = createTablePhysicalPlanConstruct(sqlEvent, (CreateTableLogicalOperator *)logicalOper, physicalPlan);
      break;
    case LO_LIMIT: rc = limitPhysicalPlanConstruct(sqlEvent, (LimitLogicalOperator *)logicalOper, physicalPlan); break;
    default: rc = GNCDB_INTERNAL; break;
  }
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  if (physicalPlan == NULL || *physicalPlan == NULL) {
    return GNCDB_INTERNAL;
  }

  return rc;
}

int joinNodeToJoinPhyOper(JoinNode *joinNode, PhysicalOperator **joinPhyOper, SQLStageEvent *sqlEvent)
{
  NestedLoopJoinPhysicalOperator *nljOper = NULL;
  SortMergeJoinPhysicalOperator  *smjOper = NULL;
  HashJoinPhysicalOperator       *hjOper  = NULL;
  int                             i       = 0;

  /* 根据不同的JoinNode类型转换成对应的连接物理算子 */
  switch (joinNode->joinType) {
    case JT_NESTED_LOOP: {
      nljOper = (NestedLoopJoinPhysicalOperator *)my_malloc0(sizeof(NestedLoopJoinPhysicalOperator));
      if (nljOper == NULL) {
        return GNCDB_MEM;
      }
      NestedLoopJoinPhysOperInit(nljOper, PO_NESTED_LOOP_JOIN);
      /* 把JoinNode所有条件收集到nlj中的predicate中 */
      if (joinNode->otherExprs != NULL) {
        nljOper->predicates = PTR_MOVE((void **)&joinNode->otherExprs);
      }
      if (joinNode->eqExprs != NULL) {
        if (nljOper->predicates == NULL) {
          nljOper->predicates = PTR_MOVE((void **)&joinNode->eqExprs);
        } else {
          for (i = 0; i < joinNode->eqExprs->elementCount; i++) {
            varArrayListAddPointer(nljOper->predicates, varArrayListGetPointer(joinNode->eqExprs, i));
          }
        }
      }
      if (joinNode->eqPKExprs != NULL) {
        if (nljOper->predicates == NULL) {
          nljOper->predicates = PTR_MOVE((void **)&joinNode->eqPKExprs);
        } else {
          for (i = 0; i < joinNode->eqPKExprs->elementCount; i++) {
            varArrayListAddPointer(nljOper->predicates, varArrayListGetPointer(joinNode->eqPKExprs, i));
          }
        }
      }
      nljOper->sqlEvent = sqlEvent;
      *joinPhyOper      = (PhysicalOperator *)nljOper;
      break;
    }
    case JT_SORT_MERGE: {
      smjOper = (SortMergeJoinPhysicalOperator *)my_malloc0(sizeof(SortMergeJoinPhysicalOperator));
      if (smjOper == NULL) {
        return GNCDB_MEM;
      }
      SortMergeJoinPhysOperInit(smjOper, PO_SORT_MERGE_JOIN);
      if (joinNode->eqPKExprs != NULL && joinNode->eqPKExprs->elementCount != 0) {
        smjOper->isPKSorted   = true;
        smjOper->pkPredicates = PTR_MOVE((void **)&joinNode->eqPKExprs);
        // 将非主键等值条件和其他条件统一放到predicates中连接后过滤
        if (joinNode->eqExprs != NULL) {
          smjOper->otherPredicates = PTR_MOVE((void **)&joinNode->eqExprs);
        }
        if (joinNode->otherExprs != NULL) {
          if (smjOper->otherPredicates == NULL) {
            smjOper->otherPredicates = PTR_MOVE((void **)&joinNode->otherExprs);
          } else {
            for (i = 0; i < joinNode->otherExprs->elementCount; i++) {
              varArrayListAddPointer(smjOper->otherPredicates, varArrayListGetPointer(joinNode->otherExprs, i));
            }
          }
        }
      } else {
        /* 这里的等值条件一定不能为空，不然无法使用归并连接 */
        smjOper->eqPredicates = PTR_MOVE((void **)&joinNode->eqExprs);
        /* 将其他条件收集到predicates中 */
        if (joinNode->otherExprs != NULL) {
          smjOper->otherPredicates = PTR_MOVE((void **)&joinNode->otherExprs);
        }
      }
      smjOper->sqlEvent = sqlEvent;
      *joinPhyOper      = (PhysicalOperator *)smjOper;
      break;
    }
    case JT_HASH: {
      hjOper = (HashJoinPhysicalOperator *)my_malloc0(sizeof(HashJoinPhysicalOperator));
      if (hjOper == NULL) {
        return GNCDB_MEM;
      }
      HashJoinPhysOperInit(hjOper, PO_HASH_JOIN);
      // joinOper中的主键条件和等值条件确保了都非空
      if (joinNode->eqExprs != NULL) {
        hjOper->eqPredicates = PTR_MOVE((void **)&joinNode->eqExprs);
      }
      if (joinNode->eqPKExprs != NULL) {
        if (hjOper->eqPredicates == NULL) {
          hjOper->eqPredicates = PTR_MOVE((void **)&joinNode->eqPKExprs);
        } else {
          for (i = 0; i < joinNode->eqPKExprs->elementCount; i++) {
            varArrayListAddPointer(hjOper->eqPredicates, varArrayListGetPointer(joinNode->eqPKExprs, i));
          }
        }
      }
      // 将其他条件收集到predicates中
      if (joinNode->otherExprs != NULL) {
        hjOper->otherPredicates = PTR_MOVE((void **)&joinNode->otherExprs);
      }
      hjOper->sqlEvent = sqlEvent;
      *joinPhyOper     = (PhysicalOperator *)hjOper;
      break;
    }
    default: {
      return GNCDB_INTERNAL;
    }
  }

  return GNCDB_SUCCESS;
}
int JoinPhyOperConstruct(PhysicalOperator **prevOper, JoinNode *joinNode, HashMap *joinedMap, SQLStageEvent *sqlEvent)
{
  int                        rc             = GNCDB_SUCCESS;
  PhysicalOperator          *joinOper       = NULL;
  TableScanPhysicalOperator *tableScanOper1 = NULL;
  TableScanPhysicalOperator *tableScanOper2 = NULL;

  if (joinNode == NULL || joinedMap == NULL || sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }

  if (*prevOper == NULL) {
    /* 这是第一个连接算子，左右子节点都是表算子 */
    tableScanOper1 = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName1);
    tableScanOper2 = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName2);
    rc             = joinNodeToJoinPhyOper(joinNode, &joinOper, sqlEvent);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    varArrayListAddPointer(joinOper->children, tableScanOper1);
    varArrayListAddPointer(joinOper->children, tableScanOper2);
    hashMapPut(joinedMap, joinNode->tableName1, tableScanOper1);
    hashMapPut(joinedMap, joinNode->tableName2, tableScanOper2);
    *prevOper = joinOper;
  } else {
    /* 之前存在过连接算子 */
    if (hashMapExists(joinedMap, joinNode->tableName1)) {
      /* 左表已经连接过了 */
      tableScanOper2 = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName2);
      rc             = joinNodeToJoinPhyOper(joinNode, &joinOper, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      varArrayListAddPointer(joinOper->children, *prevOper);
      varArrayListAddPointer(joinOper->children, tableScanOper2);
      hashMapPut(joinedMap, joinNode->tableName2, tableScanOper2);
      hashMapPut(sqlEvent->tabOperMap, joinNode->tableName2, tableScanOper2);
      *prevOper = joinOper;
    } else if (hashMapExists(joinedMap, joinNode->tableName2)) {
      /* 右表已经连接过了 */
      tableScanOper1 = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, joinNode->tableName1);
      rc             = joinNodeToJoinPhyOper(joinNode, &joinOper, sqlEvent);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      varArrayListAddPointer(joinOper->children, tableScanOper1);
      varArrayListAddPointer(joinOper->children, *prevOper);
      hashMapPut(joinedMap, joinNode->tableName1, tableScanOper1);
      hashMapPut(sqlEvent->tabOperMap, joinNode->tableName1, tableScanOper1);
      *prevOper = joinOper;
    } else {
      /* 这种情况出错 */
      return GNCDB_INTERNAL;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @description: 目前仅支持select语句直接从stmt构造物理算子
 * @param {SQLStageEvent} *sqlEvent
 * @return {*}
 */
int PhysicalOperConstruct_v2(SQLStageEvent *sqlEvent, bool *optimized)
{
  int                        rc              = GNCDB_SUCCESS;
  SelectStmt                *selectStmt      = NULL;
  JoinTables                *joinTables      = NULL;
  JoinNode                  *joinNode        = NULL;
  BtreeTable                *table           = NULL;
  PhysicalOperator          *outsidePrevOper = NULL;
  PhysicalOperator          *topOper         = NULL;
  PhysicalOperator          *joinOper        = NULL;
  PredicatePhysicalOperator *whereOper       = NULL;
  PredicatePhysicalOperator *havingOper      = NULL;
  OrderByPhysicalOperator   *orderByOper1    = NULL;
  OrderByPhysicalOperator   *orderByOper2    = NULL;
  OrderByUnit               *orderUnit       = NULL;
  varArrayList              *orderUnits      = NULL;
  varArrayList              *fieldExprs      = NULL;
  Expression                *expr            = NULL;
  GroupByPhysicalOperator   *groupByOper     = NULL;
  LimitPhysicalOperator     *limitOper       = NULL;
  ProjectPhysicalOperator   *projectOper     = NULL;
  TableScanPhysicalOperator *tableScanOper   = NULL;
  HashMap                   *joinedMap       = NULL;
  HashMap                   *subJoinedMap    = NULL;
  varArrayList              *subJoinOperList = NULL;
  varArrayList              *joinOrders      = NULL;
  SubJoin                   *subJoin         = NULL;
  SubJoin                   *leftSubJoin     = NULL;
  SubJoin                   *rightSubJoin    = NULL;
  HashMapIterator           *iter            = NULL;
  int                        leftIdx         = -1;
  int                        rightIdx        = -1;

  if (sqlEvent == NULL) {
    return GNCDB_PARAMNULL;
  }

  selectStmt = (SelectStmt *)sqlEvent->stmt; /* 只有查询语句才能进入到这个函数中 */
  joinTables = selectStmt->joinTables;
  joinOrders = joinTables->joinNodes;

  /* 1.处理表算子和join算子 */
  if (joinTables->tables->elementCount == 1) {
    /* 单表查询的情况 */
    table         = varArrayListGetPointer(joinTables->tables, 0);
    tableScanOper = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, table->tableName);
    if (tableScanOper == NULL) {
      TABLE_PHYOPER_CONSTRUCT(tableScanOper, table, sqlEvent);
      hashMapPut(sqlEvent->tabOperMap, table->tableName, tableScanOper);
    }
    outsidePrevOper = (PhysicalOperator *)tableScanOper;
    topOper         = outsidePrevOper;
  } else {
    /* 多表连接的情况，此时如果前序过程已经优化过了，直接进行简单的算子构建；否则按照subJoin中的层次结构进行构造 */
    if (*optimized) {
      joinedMap = hashMapCreate(STRKEY, 0, NULL);
      for (int i = 0; i < joinOrders->elementCount; i++) {
        joinNode = varArrayListGetPointer(joinOrders, i);
        rc       = JoinPhyOperConstruct(&outsidePrevOper, joinNode, joinedMap, sqlEvent);
      }
      //* 如果存在未连接的表，手动构造笛卡尔积 */
       if (joinTables->tables->elementCount > joinedMap->entryCount) {
        for (int i = 0; i < joinTables->tables->elementCount; i++) {
          table = varArrayListGetPointer(joinTables->tables, i);
          if (!hashMapExists(joinedMap, table->tableName)) {
            tableScanOper = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, table->tableName);
            if (tableScanOper == NULL) {
              TABLE_PHYOPER_CONSTRUCT(tableScanOper, table, sqlEvent);
              hashMapPut(sqlEvent->tabOperMap, table->tableName, tableScanOper);
            }
            if (!outsidePrevOper) {
              outsidePrevOper = (PhysicalOperator *)tableScanOper;
            } else {
              joinOper = (PhysicalOperator *)my_malloc0(sizeof(NestedLoopJoinPhysicalOperator));
              NestedLoopJoinPhysOperInit((NestedLoopJoinPhysicalOperator *)joinOper, PO_NESTED_LOOP_JOIN);
              varArrayListAddPointer(joinOper->children, outsidePrevOper);
              varArrayListAddPointer(joinOper->children, tableScanOper);
              outsidePrevOper = (PhysicalOperator *)joinOper;
            }
          }
        }
      }
      hashMapDestroy(&joinedMap);
    } else {
      subJoinedMap    = hashMapCreate(STRKEY, 0, NULL);
      subJoinOperList = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
      //* 1.1.处理各个subJoin：构造每个subJoin内部的Join算子或表扫描算子 */
      for (int i = 0; i < joinTables->subJoins->elementCount; i++) {
        subJoin = varArrayListGetPointer(joinTables->subJoins, i);
        /* 如果该subJoin只有基表，也要构建表扫描算子 */
        if (subJoin->tabMap->entryCount == 1) {
          iter          = createHashMapIterator(subJoin->tabMap);
          iter          = nextHashMapIterator(iter);
          table         = (BtreeTable *)iter->entry->value;
          tableScanOper = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, table->tableName);
          if (tableScanOper == NULL) {
            TABLE_PHYOPER_CONSTRUCT(tableScanOper, table, sqlEvent);
            hashMapPut(sqlEvent->tabOperMap, table->tableName, tableScanOper);
          }
          varArrayListAddPointer(subJoinOperList, tableScanOper);
          hashMapPut(sqlEvent->tabOperMap, table->tableName, tableScanOper);
          freeHashMapIterator(&iter);
          continue;  // next subJoin
        }
        /* 处理subJoin中的joinNode */
        for (int j = 0; j < subJoin->joinNodes->elementCount; j++) {
          joinNode = varArrayListGetPointer(subJoin->joinNodes, j);
          rc       = JoinPhyOperConstruct(&outsidePrevOper, joinNode, subJoinedMap, sqlEvent);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }
        }
        /* 如果还存在表没有连接条件，需要找出这些表并手动构造笛卡尔积 */
        if (subJoin->tabMap->entryCount > subJoinedMap->entryCount) {
          iter = createHashMapIterator(subJoin->tabMap);
          while (hasNextHashMapIterator(iter)) {
            iter = nextHashMapIterator(iter);
            /* 该表已经连接过，跳过 */
            if (hashMapExists(subJoinedMap, iter->entry->key)) {
              continue;  // next table
            }
            /* 该表未连接过，构造表算子、join算子（NLJ） */
            table         = (BtreeTable *)iter->entry->value;
            tableScanOper = (TableScanPhysicalOperator *)hashMapGet(sqlEvent->tabOperMap, table->tableName);
            if (tableScanOper == NULL) {
              TABLE_PHYOPER_CONSTRUCT(tableScanOper, table, sqlEvent);
              hashMapPut(sqlEvent->tabOperMap, table->tableName, tableScanOper);
            }
            if (!outsidePrevOper) {
              outsidePrevOper = (PhysicalOperator *)tableScanOper;
            } else {
              joinOper = (PhysicalOperator *)my_malloc0(sizeof(NestedLoopJoinPhysicalOperator));
              NestedLoopJoinPhysOperInit((NestedLoopJoinPhysicalOperator *)joinOper, PO_NESTED_LOOP_JOIN);
              varArrayListAddPointer(joinOper->children, outsidePrevOper);
              varArrayListAddPointer(joinOper->children, tableScanOper);
              joinOper->sqlEvent = sqlEvent;
              outsidePrevOper    = (PhysicalOperator *)joinOper;
            }
          }
          freeHashMapIterator(&iter);
        }
        if (outsidePrevOper) {
          varArrayListAddPointer(subJoinOperList, outsidePrevOper);
          outsidePrevOper = NULL;
          hashMapClear(subJoinedMap);
        }
      }
      hashMapDestroy(&subJoinedMap);

      //* 1.2.处理where中的topJoinNode：顶层joinNode连接各个subJoin */
      for (int i = 0; i < joinTables->topJoinNode->elementCount; i++) {
        joinNode = varArrayListGetPointer(joinTables->topJoinNode, i);
        // todo 这里目前通过遍历subJoins来找到左右子join，应该可以进行优化
        for (int j = 0; j < joinTables->subJoins->elementCount; j++) {
          subJoin = varArrayListGetPointer(joinTables->subJoins, j);
          if (leftIdx < 0) {
            if (hashMapExists(subJoin->tabMap, joinNode->tableName1)) {
              leftIdx     = j;
              leftSubJoin = subJoin;
            }
          }
          if (rightIdx < 0) {
            if (hashMapExists(subJoin->tabMap, joinNode->tableName2)) {
              rightIdx     = j;
              rightSubJoin = subJoin;
            }
          }
          if (leftIdx >= 0 && rightIdx >= 0) {
            break;
          }
        }
        assert(leftIdx >= 0 && rightIdx >= 0);
        /* 删除右算子，用新构造父join算子替换左算子 */
        rc = joinNodeToJoinPhyOper(joinNode, &joinOper, sqlEvent);
        varArrayListAddPointer(joinOper->children, varArrayListGetPointer(subJoinOperList, leftIdx));
        varArrayListAddPointer(joinOper->children, varArrayListGetPointer(subJoinOperList, rightIdx));
        varArrayListRemoveByIndexPointer(subJoinOperList, rightIdx);
        /* 这一步的操作是删除右算子时修正左索引 */
        if (rightIdx < leftIdx) {
          leftIdx--;
        }
        varArrayListSetByIndexPointer(subJoinOperList, leftIdx, joinOper);
        /* 将右subJoin合并到左subJoin中，仅需要删除右subJoin */
        iter = createHashMapIterator(rightSubJoin->tabMap);
        while (hasNextHashMapIterator(iter)) {
          iter = nextHashMapIterator(iter);
          hashMapPut(leftSubJoin->tabMap, iter->entry->key, iter->entry->value);
        }
        freeHashMapIterator(&iter);
        varArrayListAddPointer(leftSubJoin->joinNodes, joinNode);
        for (int j = 0; j < rightSubJoin->joinNodes->elementCount; j++) {
          joinNode = varArrayListGetPointer(rightSubJoin->joinNodes, j);
          varArrayListAddPointer(leftSubJoin->joinNodes, joinNode);
        }
        varArrayListRemoveByIndexPointer(joinTables->subJoins, rightIdx);
        SUB_JOIN_DESTROY(rightSubJoin);
      }
      outsidePrevOper = varArrayListGetPointer(subJoinOperList, 0);
      assert(outsidePrevOper);

      //* 1.3.如果剩余subJoin之间无连接条件，则从左至右连接在一起 */
      if (joinTables->subJoins->elementCount > 1) {
        for (int i = 1; i < joinTables->subJoins->elementCount; i++) {
          joinOper = (PhysicalOperator *)my_malloc0(sizeof(NestedLoopJoinPhysicalOperator));
          NestedLoopJoinPhysOperInit((NestedLoopJoinPhysicalOperator *)joinOper, PO_NESTED_LOOP_JOIN);
          varArrayListAddPointer(joinOper->children, outsidePrevOper);
          varArrayListAddPointer(joinOper->children, varArrayListGetPointer(subJoinOperList, i));
          joinOper->sqlEvent = sqlEvent;
          outsidePrevOper    = (PhysicalOperator *)joinOper;
        }
      }
      varArrayListDestroy(&subJoinOperList);
    }

    assert(outsidePrevOper);
    topOper         = outsidePrevOper;
    outsidePrevOper = NULL;
  }

  /* 2.处理where从句 */
  if (selectStmt->filterStmt != NULL) {
    whereOper = (PredicatePhysicalOperator *)my_malloc0(sizeof(PredicatePhysicalOperator));
    PredicatePhysOperInit(whereOper, PO_PREDICATE);
    whereOper->sqlEvent   = sqlEvent;
    whereOper->expression = PTR_MOVE((void **)&selectStmt->filterStmt->condition);
    if (topOper) {
      varArrayListAddPointer(whereOper->children, topOper);
    }
    topOper   = (PhysicalOperator *)whereOper;
    whereOper = NULL;
  }

  /* 3.处理orderby从句 */
  if (selectStmt->orderbyStmt != NULL) {
    orderByOper1 = (OrderByPhysicalOperator *)my_malloc0(sizeof(OrderByPhysicalOperator));
    if (orderByOper1 == NULL) {
      PhysicalPlanDestroy(topOper);
      return GNCDB_MEM;
    }
    OrderByPhysOperInit(orderByOper1, PO_ORDER_BY);
    orderByOper1->orderbyUnits = PTR_MOVE((void **)&selectStmt->orderbyStmt->orderByUnits);
    orderByOper1->exprs        = PTR_MOVE((void **)&selectStmt->orderbyStmt->exprs);
    if (topOper) {
      varArrayListAddPointer(orderByOper1->children, topOper);
    }
    topOper      = (PhysicalOperator *)orderByOper1;
    orderByOper1 = NULL;
  }

  /* 4.处理groupby从句 */
  if (selectStmt->groupbyStmt != NULL) {

    if (selectStmt->groupbyStmt->groupbyFields != NULL && selectStmt->groupbyStmt->groupbyFields->elementCount != 0) {
      /* 需要新建一个orderBy算子 */
      /* 4.1.构造OrderByUnits */
      orderUnits = varArrayListCreate(DISORDER, sizeof(OrderByUnit *), 0, NULL, OrderByUnitPointerDestroy);
      for (int i = 0; i < selectStmt->groupbyStmt->groupbyFields->elementCount; i++) {
        expr      = varArrayListGetPointer(selectStmt->groupbyStmt->groupbyFields, i);
        orderUnit = (OrderByUnit *)my_malloc0(sizeof(OrderByUnit));
        if (orderUnit == NULL) {
          PhysicalPlanDestroy(topOper);
          varArrayListDestroy(&orderUnits);
          return GNCDB_MEM;
        }
        orderUnit->expr  = exprDeepCopy(expr);
        orderUnit->isAsc = true;
        varArrayListAddPointer(orderUnits, orderUnit);
      }

      /*  4.2.需要将groupy_oper中的field_expr,和groupby后的expr复制一份传递给 orderby 算子 */
      fieldExprs = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, exprPtrDestroy);
      for (int i = 0; i < selectStmt->groupbyStmt->fieldExprs->elementCount; i++) {
        expr = varArrayListGetPointer(selectStmt->groupbyStmt->fieldExprs, i);
        varArrayListAddPointer(fieldExprs, exprDeepCopy(expr));
      }
      for (int i = 0; i < selectStmt->groupbyStmt->groupbyFields->elementCount; i++) {
        expr = varArrayListGetPointer(selectStmt->groupbyStmt->groupbyFields, i);
        varArrayListAddPointer(fieldExprs, exprDeepCopy(expr));
      }

      orderByOper2 = (OrderByPhysicalOperator *)my_malloc0(sizeof(OrderByPhysicalOperator));
      OrderByPhysOperInit(orderByOper2, PO_ORDER_BY);
      orderByOper2->orderbyUnits = PTR_MOVE((void **)&orderUnits);
      orderByOper2->exprs        = PTR_MOVE((void **)&fieldExprs);
      if (topOper) {
        varArrayListAddPointer(orderByOper2->children, topOper);
      }
      topOper      = (PhysicalOperator *)orderByOper2;
      orderByOper2 = NULL;
    }

    groupByOper = (GroupByPhysicalOperator *)my_malloc0(sizeof(GroupByPhysicalOperator));
    if (groupByOper == NULL) {
      PhysicalPlanDestroy(topOper);
      return GNCDB_MEM;
    }
    GroupByPhysOperInit(groupByOper, PO_GROUPBY);
    groupByOper->groupbyFields = PTR_MOVE((void **)&selectStmt->groupbyStmt->groupbyFields);
    groupByOper->fieldExprs    = PTR_MOVE((void **)&selectStmt->groupbyStmt->fieldExprs);
    groupByOper->aggExprs      = PTR_MOVE((void **)&selectStmt->groupbyStmt->aggExprs);
    groupByOper->sqlEvent      = sqlEvent;
    if (topOper) {
      varArrayListAddPointer(groupByOper->children, topOper);
    }
    topOper     = (PhysicalOperator *)groupByOper;
    groupByOper = NULL;
  }

  /* 5.处理having从句 */
  if (selectStmt->havingStmt != NULL) {
    havingOper = (PredicatePhysicalOperator *)my_malloc0(sizeof(PredicatePhysicalOperator));
    if (havingOper == NULL) {
      PhysicalPlanDestroy(topOper);
      return GNCDB_MEM;
    }
    PredicatePhysOperInit(havingOper, PO_PREDICATE);
    havingOper->sqlEvent   = sqlEvent;
    havingOper->expression = PTR_MOVE((void **)&selectStmt->havingStmt->condition);
    if (topOper) {
      varArrayListAddPointer(havingOper->children, topOper);
    }
    topOper    = (PhysicalOperator *)havingOper;
    havingOper = NULL;
  }

  /* 6.处理limit从句 */
  if (selectStmt->limitStmt != NULL) {
    limitOper = (LimitPhysicalOperator *)my_malloc0(sizeof(LimitPhysicalOperator));
    LimitPhysOperInit(limitOper, PO_LIMIT);
    limitOper->limit    = selectStmt->limitStmt->limit;
    limitOper->offset   = selectStmt->limitStmt->offset;
    limitOper->sqlEvent = sqlEvent;
    if (topOper) {
      varArrayListAddPointer(limitOper->children, topOper);
    }
    topOper   = (PhysicalOperator *)limitOper;
    limitOper = NULL;
  }

  /* 7.构造最上层的投影算子 */
  {
    projectOper = (ProjectPhysicalOperator *)my_malloc0(sizeof(ProjectPhysicalOperator));
    ProjectPhysOperInit(projectOper);
    projectOper->isDistinct      = selectStmt->isDistinct;
    projectOper->sqlEvent        = sqlEvent;
    projectOper->projectedFields = PTR_MOVE((void **)&selectStmt->queryFields);
    if (topOper) {
      varArrayListAddPointer(projectOper->children, topOper);
      topOper = NULL;
    }
    topOper     = (PhysicalOperator *)projectOper;
    projectOper = NULL;
  }

  sqlEvent->plan = topOper;

  return rc;
}

int limitPhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, LimitLogicalOperator *limitOper, PhysicalOperator **physicalPlan)
{
  int                    rc                = GNCDB_SUCCESS;
  LimitPhysicalOperator *limitPhysicalOper = NULL;
  varArrayList          *childOpers        = limitOper->children;
  PhysicalOperator      *childPhysicalOper = NULL;
  LogicalOperator       *childOper         = NULL;

  /* 1. 构建limit子节点的物理计划 */
  if (childOpers->elementCount != 0) {
    childOper = varArrayListGetPointer(childOpers, 0);
    rc        = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to create limit logical operator's child physical operator. rc=%d\n", rc);
      return rc;
    }
  }

  /* 2. 构建limit的物理计划 */
  limitPhysicalOper = (LimitPhysicalOperator *)my_malloc0(sizeof(LimitPhysicalOperator));
  if (limitPhysicalOper == NULL) {
    PhysicalPlanDestroy((PhysicalOperator *)childOper);
    return GNCDB_MEM;
  }

  LimitPhysOperInit(limitPhysicalOper, PO_LIMIT);
  limitPhysicalOper->limit    = limitOper->limit;
  limitPhysicalOper->offset   = limitOper->offset;
  limitPhysicalOper->sqlEvent = sqlEvent;
  if (childPhysicalOper) {
    varArrayListAddPointer(limitPhysicalOper->children, childPhysicalOper);
  }
  *physicalPlan = (PhysicalOperator *)limitPhysicalOper;

  return rc;
}

int createTablePhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, CreateTableLogicalOperator *createTableOper, PhysicalOperator **physicalPlan)
{
  int                          rc                      = GNCDB_SUCCESS;
  PhysicalOperator            *selectPhysicalOper      = NULL;
  varArrayList                *childOpers              = NULL;
  CreateTablePhysicalOperator *createTablePhysicalOper = NULL;

  /*1.参数检查*/
  if (sqlEvent == NULL || createTableOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构建建表语句的物理计划*/
  createTablePhysicalOper = (CreateTablePhysicalOperator *)my_malloc0(sizeof(CreateTablePhysicalOperator));
  if (createTablePhysicalOper == NULL) {
    return GNCDB_MEM;
  }
  CreateTablePhysOperInit(createTablePhysicalOper, PO_CREATE_TABLE);
  createTablePhysicalOper->db        = createTableOper->db;
  createTablePhysicalOper->tableName = PTR_MOVE((void **)&createTableOper->tableName);
  createTablePhysicalOper->attrInfos = PTR_MOVE((void **)&createTableOper->attrInfos);

  /*3.创建select的物理计划*/
  childOpers = createTableOper->children;
  if (childOpers != NULL && childOpers->elementCount != 0) {
    rc = physicalPlanConstruct(sqlEvent, varArrayListGetPointer(childOpers, 0), &selectPhysicalOper);
    if (rc != GNCDB_SUCCESS) {
      CreateTablePhysOperDestroy(createTablePhysicalOper);
      return rc;
    }
    varArrayListAddPointer(createTablePhysicalOper->children, selectPhysicalOper);
  }
  *physicalPlan = (PhysicalOperator *)createTablePhysicalOper;

  return rc;
}

int orderByPhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, OrderByLogicalOperator *orderByOper, PhysicalOperator **physicalPlan)
{
  int                      rc                  = GNCDB_SUCCESS;
  varArrayList            *childOpers          = NULL;
  PhysicalOperator        *childPhyOper        = NULL;
  OrderByPhysicalOperator *orderByPhysicalOper = NULL;
  if (sqlEvent == NULL || orderByOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  childOpers = orderByOper->children;
  /* 1. 构建order by子节点的物理计划 */
  if (childOpers->elementCount != 0) {
    rc = physicalPlanConstruct(sqlEvent, varArrayListGetPointer(childOpers, 0), &childPhyOper);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  /* 2. 构建order by的物理计划 */
  orderByPhysicalOper = (OrderByPhysicalOperator *)my_malloc0(sizeof(OrderByPhysicalOperator));
  if (orderByPhysicalOper == NULL) {
    PhysicalPlanDestroy((PhysicalOperator *)childPhyOper);
    return GNCDB_MEM;
  }
  OrderByPhysOperInit(orderByPhysicalOper, PO_ORDER_BY);

  /* 3.赋值 */
  orderByPhysicalOper->orderbyUnits = PTR_MOVE((void **)&orderByOper->orderbyUnits);
  orderByPhysicalOper->exprs        = PTR_MOVE((void **)&orderByOper->exprs);
  orderByPhysicalOper->sqlEvent     = sqlEvent;
  if (childPhyOper) {
    varArrayListAddPointer(orderByPhysicalOper->children, childPhyOper);
  }
  *physicalPlan = (PhysicalOperator *)orderByPhysicalOper;
  return GNCDB_SUCCESS;
}

int groupByPhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, GroupByLogicalOperator *groupByOper, PhysicalOperator **physicalPlan)
{
  int                      rc                  = GNCDB_SUCCESS;
  varArrayList            *childOpers          = groupByOper->children;
  PhysicalOperator        *childPhyOper        = NULL;
  GroupByPhysicalOperator *groupByPhysicalOper = NULL;
  /*1.参数检查，分组聚合的子算子必须是一个，也就是表扫描算子*/
  if (childOpers->elementCount != 1) {
    printf("group by operator should have 1 child, but have %d", childOpers->elementCount);
    return GNCDB_INTERNAL;
  }

  /*2.构建分组聚合物理算子并初始化*/
  rc = physicalPlanConstruct(sqlEvent, varArrayListGetPointer(childOpers, 0), &childPhyOper);
  if (rc != GNCDB_SUCCESS) {
    printf("failed to create group by logical operator's child physical operator. rc=%d\n", rc);
    return rc;
  }
  groupByPhysicalOper = (GroupByPhysicalOperator *)my_malloc0(sizeof(GroupByPhysicalOperator));
  if (groupByPhysicalOper == NULL) {
    PhysicalPlanDestroy((PhysicalOperator *)childPhyOper);
    return GNCDB_MEM;
  }
  GroupByPhysOperInit(groupByPhysicalOper, PO_GROUPBY);
  GroupByGetParmsValues(groupByPhysicalOper,
      PTR_MOVE((void **)&groupByOper->groupbyFields),
      PTR_MOVE((void **)&groupByOper->aggExprs),
      PTR_MOVE((void **)&groupByOper->fieldExprs));
  if (childPhyOper) {
    varArrayListAddPointer(groupByPhysicalOper->children, childPhyOper);
  }
  groupByPhysicalOper->sqlEvent = sqlEvent;
  *physicalPlan                 = (PhysicalOperator *)groupByPhysicalOper;
  return GNCDB_SUCCESS;
}

int joinPhysicalPlanConstruct(SQLStageEvent *sqlEvent, JoinLogicalOperator *joinOper, PhysicalOperator **physicalPlan)
{
  int                             rc                = GNCDB_SUCCESS;
  HashJoinPhysicalOperator       *hashJoin          = NULL;
  NestedLoopJoinPhysicalOperator *nestedLoopJoin    = NULL;
  SortMergeJoinPhysicalOperator  *sortMergeJoin     = NULL;
  varArrayList                   *childOpers        = NULL;
  int                             i                 = 0;
  LogicalOperator                *childOper         = NULL;
  PhysicalOperator               *childPhysicalOper = NULL;
  // ComparisonExpr                 *compExpr          = NULL;
  // FieldExpr                      *fieldExpr1        = NULL;
  // FieldExpr                      *fieldExpr2        = NULL;

  /*1.参数非空检查、算子孩子必须为2检查*/
  if (sqlEvent == NULL || joinOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  childOpers = joinOper->children;
  if (childOpers->elementCount != 2) {
    return GNCDB_INTERNAL;
  }

  /*2.根据连接的类型构建相应的算子*/
  if (joinOper->joinType == JT_NESTED_LOOP) {
    /*2.1嵌套循环连接*/
    nestedLoopJoin = (NestedLoopJoinPhysicalOperator *)my_malloc0(sizeof(NestedLoopJoinPhysicalOperator));
    if (nestedLoopJoin == NULL) {
      return GNCDB_MEM;
    }
    NestedLoopJoinPhysOperInit(nestedLoopJoin, PO_NESTED_LOOP_JOIN);
    /*2.1.1构建子算子的物理计划,首先构建子算子物理计划*/
    for (i = 0; i < childOpers->elementCount; i++) {
      childOper         = varArrayListGetPointer(childOpers, i);
      childPhysicalOper = NULL;
      rc                = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
      if (rc != GNCDB_SUCCESS) {
        PhysicalPlanDestroy((PhysicalOperator *)nestedLoopJoin);
        return rc;
      }
      varArrayListAddPointer(nestedLoopJoin->children, childPhysicalOper);
    }
    /*2.1.2把所有条件收集到expressions中,otherExprs为非等值条件，expression为其它等值条件，pkexprs是主键等值条件*/
    if (joinOper->otherExprs != NULL) {
      nestedLoopJoin->predicates = PTR_MOVE((void **)&joinOper->otherExprs);
    }
    if (joinOper->expressions != NULL) {
      if (nestedLoopJoin->predicates == NULL) {
        nestedLoopJoin->predicates = PTR_MOVE((void **)&joinOper->expressions);
      } else {
        for (i = 0; i < joinOper->expressions->elementCount; i++) {
          varArrayListAddPointer(nestedLoopJoin->predicates, varArrayListGetPointer(joinOper->expressions, i));
        }
        /*varArrayListClear(joinOper->expressions);*/
      }
    }
    if (joinOper->pkExprs != NULL) {
      if (nestedLoopJoin->predicates == NULL) {
        nestedLoopJoin->predicates = PTR_MOVE((void **)&joinOper->pkExprs);
      } else {
        for (i = 0; i < joinOper->pkExprs->elementCount; i++) {
          varArrayListAddPointer(nestedLoopJoin->predicates, varArrayListGetPointer(joinOper->pkExprs, i));
        }
        /*varArrayListClear(joinOper->expressions);*/
      }
    }
    nestedLoopJoin->sqlEvent = sqlEvent;
    *physicalPlan            = (PhysicalOperator *)nestedLoopJoin;
  } else if (joinOper->joinType == JT_HASH) {
    /*2.2哈希连接,首先构建子算子物理计划*/
    hashJoin = (HashJoinPhysicalOperator *)my_malloc0(sizeof(HashJoinPhysicalOperator));
    if (hashJoin == NULL) {
      return GNCDB_MEM;
    }
    HashJoinPhysOperInit(hashJoin, PO_HASH_JOIN);
    for (i = 0; i < childOpers->elementCount; i++) {
      childOper         = varArrayListGetPointer(childOpers, i);
      childPhysicalOper = NULL;
      rc                = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
      if (rc != GNCDB_SUCCESS) {
        PhysicalPlanDestroy((PhysicalOperator *)hashJoin);
        return rc;
      }
      varArrayListAddPointer(hashJoin->children, childPhysicalOper);
    }
    /*2.2.1将连接条件收集起来,与嵌套循环不同的是三种条件分开存储*/
    /*joinOper中的主键条件和等值条件确保了都非空*/
    if (joinOper->expressions != NULL) {
      hashJoin->eqPredicates = PTR_MOVE((void **)&joinOper->expressions);
    }
    if (joinOper->pkExprs != NULL) {
      if (hashJoin->eqPredicates == NULL) {
        hashJoin->eqPredicates = PTR_MOVE((void **)&joinOper->pkExprs);
      } else {
        for (i = 0; i < joinOper->pkExprs->elementCount; i++) {
          varArrayListAddPointer(hashJoin->eqPredicates, varArrayListGetPointer(joinOper->pkExprs, i));
        }
      }
    }
    /*将其他条件收集到predicates中*/
    if (joinOper->otherExprs != NULL) {
      hashJoin->otherPredicates = PTR_MOVE((void **)&joinOper->otherExprs);
    }
    hashJoin->sqlEvent = sqlEvent;
    *physicalPlan      = (PhysicalOperator *)hashJoin;
  } else if (joinOper->joinType == JT_SORT_MERGE) {
    /*2.3归并连接,首先构建子算子物理计划*/
    sortMergeJoin = (SortMergeJoinPhysicalOperator *)my_malloc0(sizeof(SortMergeJoinPhysicalOperator));
    if (sortMergeJoin == NULL) {
      return GNCDB_MEM;
    }
    SortMergeJoinPhysOperInit(sortMergeJoin, PO_SORT_MERGE_JOIN);
    for (i = 0; i < childOpers->elementCount; i++) {
      childOper         = varArrayListGetPointer(childOpers, i);
      childPhysicalOper = NULL;
      rc                = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
      if (rc != GNCDB_SUCCESS) {
        PhysicalPlanDestroy((PhysicalOperator *)sortMergeJoin);
        return rc;
      }
      varArrayListAddPointer(sortMergeJoin->children, childPhysicalOper);
    }
    /*2.3.2将非主键等值条件和其他条件统一放到predicates中连接后过滤*/
    if (joinOper->pkExprs != NULL && joinOper->pkExprs->elementCount != 0) {
      sortMergeJoin->isPKSorted   = true;
      sortMergeJoin->pkPredicates = PTR_MOVE((void **)&joinOper->pkExprs);
      /*将非主键等值条件和其他条件统一放到predicates中连接后过滤*/
      if (joinOper->expressions != NULL) {
        sortMergeJoin->otherPredicates = PTR_MOVE((void **)&joinOper->expressions);
      }
      if (joinOper->otherExprs != NULL) {
        if (sortMergeJoin->otherPredicates == NULL) {
          sortMergeJoin->otherPredicates = PTR_MOVE((void **)&joinOper->otherExprs);
        } else {
          for (i = 0; i < joinOper->otherExprs->elementCount; i++) {
            varArrayListAddPointer(sortMergeJoin->otherPredicates, varArrayListGetPointer(joinOper->otherExprs, i));
          }
        }
      }
    } else {
      /*2.3.3这里的等值条件一定不能为空*/
      sortMergeJoin->eqPredicates = PTR_MOVE((void **)&joinOper->expressions);
      /*2.3.4将其他条件收集到predicates中*/
      if (joinOper->otherExprs != NULL) {
        sortMergeJoin->otherPredicates = PTR_MOVE((void **)&joinOper->otherExprs);
      }
    }
    sortMergeJoin->sqlEvent = sqlEvent;
    *physicalPlan           = (PhysicalOperator *)sortMergeJoin;
  }

  return rc;
}

int updatePhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, UpdateLogicalOperator *updateOper, PhysicalOperator **physicalPlan)
{
  int                     rc                 = GNCDB_SUCCESS;
  varArrayList           *childOpers         = updateOper->children;
  PhysicalOperator       *childPhysicalOper  = NULL;
  UpdatePhysicalOperator *updatePhysicalOper = NULL;
  LogicalOperator        *childOper          = NULL;

  /* 1. 构建update子节点的物理计划 */
  if (childOpers != NULL && childOpers->elementCount != 0) {
    childOper = varArrayListGetPointer(childOpers, 0);
    rc        = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to create update logical operator's child physical operator. rc=%d\n", rc);
      return rc;
    }
  }
  /* 2. 构建update的物理计划 */
  updatePhysicalOper = (UpdatePhysicalOperator *)my_malloc0(sizeof(UpdatePhysicalOperator));
  if (updatePhysicalOper == NULL) {
    return GNCDB_MEM;
  }
  UpdatePhysOperInit(updatePhysicalOper);
  updatePhysicalOper->type             = PO_UPDATE;
  updatePhysicalOper->updateValues     = PTR_MOVE((void **)&updateOper->updateValues);
  updatePhysicalOper->updateFieldNames = PTR_MOVE((void **)&updateOper->updateFieldNames);
  updatePhysicalOper->tableName        = PTR_MOVE((void **)&updateOper->tableName);
  updatePhysicalOper->sqlEvent         = sqlEvent;
  if (childPhysicalOper) {
    varArrayListAddPointer(updatePhysicalOper->children, childPhysicalOper);
  }
  *physicalPlan = (PhysicalOperator *)updatePhysicalOper;

  return rc;
}
int deletePhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, DeleteLogicalOperator *deleteOper, PhysicalOperator **physicalPlan)
{
  int                     rc                 = GNCDB_SUCCESS;
  varArrayList           *childOpers         = deleteOper->children;
  PhysicalOperator       *childPhysicalOper  = NULL;
  DeletePhysicalOperator *deletePhysicalOper = NULL;
  LogicalOperator        *childOper          = NULL;

  /* 1. 构建delete子节点的物理计划 */
  if (childOpers != NULL && childOpers->elementCount != 0) {
    childOper = varArrayListGetPointer(childOpers, 0);
    rc        = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to create delete logical operator's child physical operator. rc=%d\n", rc);
      return rc;
    }
  }
  /* 2. 构建delete的物理计划 */
  deletePhysicalOper = (DeletePhysicalOperator *)my_malloc0(sizeof(DeletePhysicalOperator));
  if (deletePhysicalOper == NULL) {
    PhysicalPlanDestroy((PhysicalOperator *)childPhysicalOper);
    return GNCDB_MEM;
  }
  DeletePhysOperInit(deletePhysicalOper, PO_DELETE);
  deletePhysicalOper->table = deleteOper->table;
  deletePhysicalOper->type  = PO_DELETE;
  if (childPhysicalOper) {
    varArrayListAddPointer(deletePhysicalOper->children, childPhysicalOper);
  }
  *physicalPlan = (PhysicalOperator *)deletePhysicalOper;

  return rc;
}

int processSubQueryPhysical(Expression *expr, varArrayList *list, int addtionalParamNums, va_list ap)
{
  int               rc                   = GNCDB_SUCCESS;
  SQLStageEvent    *sqlEvent             = NULL;
  SubQueryExpr     *subQueryExpr         = NULL;
  PhysicalOperator *subQueryPhysicalOper = NULL;

  if (expr->type == ETG_SUBQUERY) {
    assert(addtionalParamNums == 1);
    sqlEvent             = va_arg(ap, SQLStageEvent *);
    subQueryExpr         = (SubQueryExpr *)expr;
    subQueryPhysicalOper = NULL;
    if (GNCDB_SUCCESS != (rc = physicalPlanConstruct(sqlEvent, subQueryExpr->logicalOper, &subQueryPhysicalOper))) {
      return rc;
    }
    subQueryExpr->physicalOper   = subQueryPhysicalOper;
    subQueryExpr->subEvent->plan = subQueryPhysicalOper;
  }
  return rc;
}
typedef int (*CheckFunc)(Expression *, varArrayList *, int, va_list args);

int getStartKeyValue(Expression *expr, varArrayList *startKeyValue, int addtionParaNums, va_list args)
{
  ComparisonExpr *compExpr  = NULL;
  Expression     *left      = NULL;
  Expression     *right     = NULL;
  int            *keyValue  = NULL;
  ValueExpr      *valueExpr = NULL;

  if (expr->type == ETG_COMPARISON) {
    compExpr = (ComparisonExpr *)expr;
    left     = compExpr->left;
    right    = compExpr->right;
    keyValue = NULL;
    if (left->type == ETG_FIELD && right->type == ETG_VALUE) {
      valueExpr = (ValueExpr *)right;
      if (valueExpr->value->attrType != INTS) {
        return GNCDB_SUCCESS;
      }
      keyValue  = my_malloc0(sizeof(int));
      *keyValue = valueExpr->value->numValue.intValue;
      varArrayListAddPointer(startKeyValue, keyValue);
    } else if (left->type == ETG_VALUE && right->type == ETG_FIELD) {
      valueExpr = (ValueExpr *)left;
      if (valueExpr->value->attrType != INTS) {
        return GNCDB_SUCCESS;
      }
      keyValue  = my_malloc0(sizeof(int));
      *keyValue = valueExpr->value->numValue.intValue;
      varArrayListAddPointer(startKeyValue, keyValue);
    }
  }
  return GNCDB_SUCCESS;
}

int tableGetPhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, TableGetLogicalOperator *tableGetOper, PhysicalOperator **physicalPlan)
{
  TableScanPhysicalOperator *tableScanOper = NULL;

  /*1.参数检查*/
  if (sqlEvent == NULL || tableGetOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构建并初始化表扫描算子*/
  tableScanOper = (TableScanPhysicalOperator *)my_malloc0(sizeof(TableScanPhysicalOperator));
  if (tableScanOper == NULL) {
    return GNCDB_MEM;
  }
  TableScanPhysicalOperatorInit(tableScanOper);
  tableScanOper->predicates    = PTR_MOVE((void **)&tableGetOper->expressions);
  tableScanOper->table         = tableGetOper->table;
  tableScanOper->startKeyValue = NULL;
  tableScanOper->txn           = sqlEvent->txn;
  tableScanOper->tableSchema   = getTableSchema(sqlEvent->db->catalog, tableGetOper->table->tableName);
  tableScanOper->sqlEvent      = sqlEvent;

  *physicalPlan = (PhysicalOperator *)tableScanOper;
  return GNCDB_SUCCESS;
}

int indexScanPhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, IndexScanLogicalOperator *indexScanOper, PhysicalOperator **physicalPlan)
{
  IndexScanPhysicalOperator *indexPhyOper = NULL;

  /*1.参数检查*/
  if (sqlEvent == NULL || indexScanOper == NULL) {
    return GNCDB_PARAMNULL;
  }
  indexPhyOper = (IndexScanPhysicalOperator *)my_malloc0(sizeof(IndexScanPhysicalOperator));
  if (indexPhyOper == NULL) {
    return GNCDB_MEM;
  }
  IndexScanPhysOperInit(indexPhyOper);
  indexPhyOper->table       = indexScanOper->table;
  indexPhyOper->tableSchema = getTableSchema(sqlEvent->db->catalog, indexScanOper->table->tableName);
  indexPhyOper->txn         = sqlEvent->txn;
  indexPhyOper->indexType   = indexScanOper->indexType;
  indexPhyOper->index       = indexScanOper->index;
  indexPhyOper->indexExprs  = indexScanOper->expressions;
  indexPhyOper->predicates  = indexScanOper->otherExprs;
  indexPhyOper->indexVal    = indexScanOper->indexVal;
  *physicalPlan             = (PhysicalOperator *)indexPhyOper;
  return GNCDB_SUCCESS;
}

int subQueryExprCheckFunc(Expression *expr, varArrayList *list, int additionalParmNums, va_list ap)
{
  int               rc                  = GNCDB_SUCCESS;
  SQLStageEvent    *sqlEvent            = NULL;
  PhysicalOperator *subQueryPhyOper     = NULL;
  SubQueryExpr     *subQueryExpr        = NULL;
  LogicalOperator  *subQueryLogicalOper = NULL;

  if (expr->type == ETG_SUBQUERY) {
    assert(additionalParmNums == 1);
    sqlEvent            = va_arg(ap, SQLStageEvent *);
    subQueryPhyOper     = NULL;
    subQueryExpr        = (SubQueryExpr *)expr;
    subQueryLogicalOper = subQueryExpr->logicalOper;
    rc                  = physicalPlanConstruct(sqlEvent, subQueryLogicalOper, &subQueryPhyOper);
    if (rc != GNCDB_SUCCESS) {
      printf("failed to create sub query logical operator's physical operator. rc=%d\n", rc);
      return rc;
    }
#ifdef PINTF_OPERATOR_TREE
    if (subQueryPhyOper != NULL) {
      PhysicalOperatorToString(subQueryPhyOper, 0);
    }
#endif
    subQueryExpr->physicalOper   = subQueryPhyOper;
    subQueryExpr->subEvent->plan = subQueryPhyOper;
  }
  return GNCDB_SUCCESS;
}

int PredicatePhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, PredicateLogicalOperator *predicateOper, PhysicalOperator **physicalPlan)
{
  int                        rc                    = GNCDB_SUCCESS;
  PhysicalOperator          *childPhysicalOper     = NULL;
  LogicalOperator           *childOper             = NULL;
  PredicatePhysicalOperator *predicatePhysicalOper = NULL;
  varArrayList              *childOpers            = NULL;
  
  /*1.参数检查*/
  if (sqlEvent == NULL || predicateOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构造孩子的物理计划*/
  childOpers = predicateOper->children;
  if (childOpers->elementCount != 1) {}
  childOper = varArrayListGetPointer(childOpers, 0);
  rc        = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /*3.构造过滤算子物理计划*/
  predicatePhysicalOper = (PredicatePhysicalOperator *)my_malloc0(sizeof(PredicatePhysicalOperator));
  if (predicatePhysicalOper == NULL) {
    PhysicalPlanDestroy(childPhysicalOper);
    return GNCDB_MEM;
  }
  PredicatePhysOperInit(predicatePhysicalOper, PO_PREDICATE);
  predicatePhysicalOper->sqlEvent = sqlEvent;
  if (predicateOper->expressions->elementCount > 0) {
    predicatePhysicalOper->expression = PTR_MOVE((void **)varArrayListGet(predicateOper->expressions, 0));
  } else {
    predicatePhysicalOper->expression = NULL;
  }

  /*4.若子物理计划不空则加入过滤算子的孩子节点处*/
  if (childPhysicalOper != NULL) {
    varArrayListAddPointer(predicatePhysicalOper->children, childPhysicalOper);
  }
  *physicalPlan = (PhysicalOperator *)predicatePhysicalOper;

  return GNCDB_SUCCESS;
}

int insertPhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, InsertLogicalOperator *insertOper, PhysicalOperator **physicalPlan)
{
  InsertPhysicalOperator *insertPhysicalOper = NULL;
  /*1.参数检查*/
  if (sqlEvent == NULL || insertOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构建插入无算子并初始化*/
  insertPhysicalOper = (InsertPhysicalOperator *)my_malloc0(sizeof(InsertPhysicalOperator));
  if (insertPhysicalOper == NULL) {
    return GNCDB_MEM;
  }
  InsertPhysOperInit(insertPhysicalOper);
  insertPhysicalOper->table      = insertOper->table;
  insertPhysicalOper->valuelists = PTR_MOVE((void **)&insertOper->valuelists);
  insertPhysicalOper->type       = PO_INSERT;
  insertOper->sqlEvent           = sqlEvent;
  *physicalPlan                  = (PhysicalOperator *)insertPhysicalOper;

  return GNCDB_SUCCESS;
}

int projectPhysicalPlanConstruct(
    SQLStageEvent *sqlEvent, ProjectLogicalOperator *projectOper, PhysicalOperator **physicalPlan)
{
  int                      rc                  = GNCDB_SUCCESS;
  varArrayList            *childOpers          = NULL;
  PhysicalOperator        *childPhysicalOper   = NULL;
  ProjectPhysicalOperator *projectPhysicalOper = NULL;
  LogicalOperator         *childOper           = NULL;
  /*1.参数检查*/
  if (sqlEvent == NULL || projectOper == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*2.构造子算子的物理计划*/
  childOpers = projectOper->children;
  if (childOpers->elementCount != 0) {
    childOper = varArrayListGetPointer(childOpers, 0);
    rc        = physicalPlanConstruct(sqlEvent, childOper, &childPhysicalOper);
    if (rc != GNCDB_SUCCESS) {
      PRINT("failed to create project logical operator's child physical operator. rc=%d\n", rc);
      return rc;
    }
  }

  /*3.构建投影算子的物理计划*/
  projectPhysicalOper = (ProjectPhysicalOperator *)my_malloc0(sizeof(ProjectPhysicalOperator));
  if (projectPhysicalOper == NULL) {
    PhysicalPlanDestroy((PhysicalOperator *)childOper);
    return GNCDB_MEM;
  }
  ProjectPhysOperInit(projectPhysicalOper);
  /*3.1.标记是否去重*/
  projectPhysicalOper->isDistinct      = projectOper->isDistinct;
  projectPhysicalOper->sqlEvent        = sqlEvent;
  projectPhysicalOper->projectedFields = PTR_MOVE((void **)&projectOper->expressions);
  /*3.2.若子算子不为空则加进去*/
  if (childPhysicalOper) {
    varArrayListAddPointer(projectPhysicalOper->children, childPhysicalOper);
  }

  *physicalPlan = (PhysicalOperator *)projectPhysicalOper;

  return rc;
}
