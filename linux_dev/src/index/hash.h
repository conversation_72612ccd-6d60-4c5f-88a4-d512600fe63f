/*
 * @Author: zql <EMAIL>
 * @Date: 2025-05-22 17:02:58
 * @LastEditors: zql <EMAIL>
 * @LastEditTime: 2025-05-26 15:54:14
 * @FilePath: /gncdbflr/linux_dev/src/index/hash.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置:
 * https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @file hash.h
 * <AUTHOR>
 * @brief  Hash索引中页的定义以及有关页操作的函数声明
 * @version 0.1
 * @date 2025-02-10
 *
 * @copyright Copyright (c) 2025
 *
 */

#ifndef _GNCDB_HASH_H_
#define _GNCDB_HASH_H_

#include "transaction.h"
#include <stdbool.h>
#include "typedefine.h"
#include "vararraylist.h"
#include "catalog.h"
#include "readwritelock.h"
#include "btreepage.h"

typedef struct HashIndex
{
  int           meta_page_id;     // 索引的元数据页号
  char         *tableName;        // 表名
  char         *indexName;        // 索引名
  varArrayList *pkCols;           // 主键列Column List<Column *>
  varArrayList *index_columns;    // 索引的列信息
  int           primaryKeyLenth;  // 主键长度
  ReadWriteLock rwlock;           // 索引级读写锁
} HashIndex;

/* 向哈希页面中插入一条记录*/
int hashPageInsertRecord(Page *hashPage, BYTE *record, HashIndex *hashIndex);
/* 根据主键类型计算哈希值 */
int getHashValue(void *value, FieldType type);
/*HashIndex的创建*/
HashIndex *hashIndexConstruct(
    int metaPageId, varArrayList *indexColumns, char *tableName, char *indexName, Catalog *catalog);
/*HashIndex的销毁*/
void hashIndexDestroy(HashIndex **hashIndex);
/* 向哈希索引中插入键值对 */
int hashIndexInsert(
    HashIndex *hashIndex, int hashValue, varArrayList *pkValueArray, bool isCreate, GNCDB *db, Transaction *tx);
/* 从哈希索引中删除键值对 */
int hashIndexDelete(HashIndex *hashIndex, int hashValue, varArrayList *pkValueArray, GNCDB *db, Transaction *tx);
/* 从哈希索引中查找对应键值对 */
int hashIndexSelect(HashIndex *hashIndex, int hashValue, varArrayList *primarykeyArray, GNCDB *db, Transaction *tx);
/* 调试输出函数 */
/* 打印完整哈希索引结构（包括元数据页和所有桶） */
void printCompleteHashIndex(HashIndex *hashIndex, GNCDB *db);
int  getHashIndexKVCnt(HashIndex *hashIndex, GNCDB *db);
int  getBucketKVCnt(HashIndex *hashIndex, BucketPage *bucketPage, GNCDB *db);
int  getHashBucketKVList(HashIndex *hashIndex, int **cntList, int *bucketNum, GNCDB *db);

#endif