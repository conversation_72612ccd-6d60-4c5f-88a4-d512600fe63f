/**
 * @file btreetable.c
 * <AUTHOR>
 * @brief  Rtree相关操作实现
 * @version 0.1
 * @date 2023-02-02
 *
 * @copyright Copyright (c) 2023
 *
 */

#include"rtree.h"









int rtreeInsert(struct RtreeTable* rtreetable, struct varArrayList* keyValue, struct varArrayList* primValue, int I_si32_pageid,
	struct TableSchema* tableSchema, struct GNCDB* db, struct Transaction* tx, int v_si32_level){
	return 0;
}
/*删除关键字keyValueArray为被删除的目标key,primKeyArray为被删除的元组的主键*/
int rtreeDelete(struct RtreeTable* rtreetable, struct varArrayList* keyValueArray, struct varArrayList* primkeyArray, struct TableSchema* tableSchema,
	struct GNCDB* db, struct Transaction* tx) {
	return 0;
}
/*在R树中找到keyValueArray应该属于的那个叶子页*/
struct RtreePage* rtreeTableFindTupleInLeafPage(struct RtreeTable* rtreetable, struct varArrayList* keyValueArray, struct TableSchema* tableSchema,
	struct GNCDB* db, int tid, int v_si32_level) {
	return NULL;
}
/*构筑RtreeTable v_si32_dim为该Rtree的维度*/
struct RtreeTable* rtreeTableConstruct(char* tableName, int rootPageId, int v_si32_dim, struct TableSchema* tableSchema) {
	return NULL;
}
/*构筑R树游标*/
struct RtreeCursor* rtreeCursorConstruct(struct GNCDB* db, struct varArrayList* searchvalue, char* tableName, int flag, struct Transaction* tx) {
	return NULL;
}
/*判断游标是否有下一个叶子节点对象*/
int rtreeTableHasNextTuple(struct RtreeCursor* rtreeCursor) {
	return 0;
}
/*游标获取下一个叶子节点*/
// struct RtreeTuple* rtreeTableGetNext(struct RtreeTable* rtreeTable, struct RtreeCursor* rtreeCursor, struct GNCDB* db) {
// 	return NULL;
// }
struct RtreePage* rtreePageConstructWithOutData(PageType pageType, int pageId, char* tableName, int parentPageId, struct GNCDB* db) {
	return NULL;
}
void rtreePageDestroy(struct RtreePage** RtreePage) {
	return;
}
struct RtreePage* freePageToRtreePage(int pageId, PageType pageType, char* tableName, int parentPageId, struct GNCDB* db, struct Transaction* tx){
	return NULL;
}
int pagePoolAddRtreePage(struct GNCDB* db, struct RtreePage* page, int pageId, struct Transaction* tx) {
	return 0;
}
void rtreeCursorDestroy(struct RtreeCursor** btreeCursor) {
	return;
}
struct RtreeTuple* rtreeTableGetNextTuple(struct RtreeTable* rtreeTable, struct RtreeCursor* rtreeCursor, struct GNCDB* db)
{
	return NULL;

}
int rtreeTableDropTable(struct RtreeTable* rtreeTable, struct GNCDB* db, struct Transaction* tx) {
	return 0;
}

int getRtreeDim(char* indexName) {
	return 0;
}