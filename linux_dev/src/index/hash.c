#include "hash.h"
#include "btreepage.h"
#include "btreetable.h"
#include "catalog.h"
#include "gncdb.h"
#include "gncdbconstant.h"
#include "lockmanager.h"
#include "pagepool.h"
#include "readwritelock.h"
#include "transaction.h"
#include "typedefine.h"
#include "value.h"
#include "vararraylist.h"
#include <assert.h>
#include <limits.h>
#include <stdio.h>
#include <string.h>
#include "hashpage.h"

#define FNV_PRIME 16777619
#define FNV_OFFSET_BASIS 2166136261
#define MAX_FILL_FACTOR 0.7

#define HASH_VALUE_TO_BUCKER_ID(metaPage, hashValue, bucketId) \
  do {                                                         \
    (bucketId) = (hashValue) & (metaPage)->highMask;           \
    if ((metaPage)->bucketCount - 1 < (bucketId)) {            \
      (bucketId) = (hashValue) & (metaPage)->lowMask;          \
    }                                                          \
  } while (0)

int hashBucketSplit(HashIndex *hashIndex, int bucketId, MetaPage *metaPage, GNCDB *db, Transaction *tx);
int redistributeHashBucket(HashIndex *hashIndex, MetaPage *metaPage, BucketPage *oldBucketPage,
    BucketPage *newBucketPage, GNCDB *db, Transaction *tx);
int squeezeBucket(HashIndex *hashIndex, BucketPage *obucketPage, int originPages, int obucketKVNum, GNCDB *db);
int newBucketPageInsert(HashIndex *hashIndex, MetaPage *metaPage, BucketPage *bucketPage, int hashValue, BYTE *record,
    GNCDB *db, Transaction *tx);

/**
 * @description: 用最后一个键值对替换要删除的position处的键值对
 * @param {HashIndex*} hashIndex 哈希索引结构体
 * @param {BucketPage*} bucketPage 最后一个溢出页所属的桶页
 * @param {Page} *hashPage 当前要删除键值对的页面：桶页或者溢出页
 * @param {int} position 删除的位置索引
 * @param {GNCDB*} db
 * @return {*}
 */
int coverWithLastPair(
    HashIndex *hashIndex, BucketPage *bucketPage, Page *hashPage, int position, GNCDB *db, Transaction *tx)
{
  int               rc             = GNCDB_SUCCESS;
  Page             *prevPage       = NULL;
  HashOverflowPage *lastOFPage     = NULL;
  HashOverflowPage *prevOFPage     = NULL;
  BucketPage       *prevBucketPage = NULL;
  BYTE             *lastRecord     = NULL;
  int               pairLength     = 0;

  //* 获取最后一个溢出页的最后一个record
  pairLength = INT_SIZE + hashIndex->primaryKeyLenth;
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, bucketPage->lastOverflowPageId, EXCLUSIVE);
  rc = pagePoolGetPage((Page **)&lastOFPage, bucketPage->lastOverflowPageId, hashIndex->tableName, db);
  rc = produceOldPageData(db, (Page *)lastOFPage, HASH_OVERFLOW_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  if (lastOFPage->keyTidPairCount <= 0) {
    return GNCDB_INTERNAL;
  }
  lastRecord = lastOFPage->page.pData + HASH_OVERFLOW_PAGE_HEADER_SIZE + (lastOFPage->keyTidPairCount - 1) * pairLength;

  //* 将其插入到hashPage中
  rc = produceOldPageData(db, hashPage, hashPage->pageType, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = hashPageInsertRecord(hashPage, lastRecord, hashIndex);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  //* 如果最后一个页面没有键值对了，需要释放最后一个溢出页 */
  lastOFPage->keyTidPairCount--;
  setPageStatusDirty(db->pagePool, lastOFPage->page.id, NULL);
  if (lastOFPage->keyTidPairCount == 0) {
    rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, lastOFPage->prevPageId, EXCLUSIVE);
    rc = pagePoolGetPage(&prevPage, lastOFPage->prevPageId, hashIndex->tableName, db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = produceOldPageData(db, (Page *)prevPage, prevPage->pageType, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    if (prevPage->pageType == BUCKET_PAGE) {
      //* 该溢出页是桶页的唯一一个溢出页 */
      prevBucketPage                      = (BucketPage *)prevPage;
      prevBucketPage->firstOverflowPageId = -1;
      prevBucketPage->lastOverflowPageId  = -1;
      setPageStatusDirty(db->pagePool, prevBucketPage->page.id, NULL);
    } else if (prevPage->pageType == HASH_OVERFLOW_PAGE) {
      //* 该溢出页是不是桶的唯一一个溢出页 */
      prevOFPage                     = (HashOverflowPage *)prevPage;
      bucketPage->lastOverflowPageId = lastOFPage->prevPageId;
      prevOFPage->nextPageId         = -1;
      setPageStatusDirty(db->pagePool, prevOFPage->page.id, NULL);
    }
    hashOverflowPageToFreePage(lastOFPage, db);
  }
  return rc;
}

/**
 * @description: 二分查找找到等于哈希值的下标
 * @param {Page} *hashPage 查找的页面：桶页/哈希溢出页
 * @param {int} hashValue 哈希值
 * @param {HashIndex} *hashIndex 哈希索引结构体
 * @param {int} *idx 找到的第一个 >= hashValue的下标
 * @return {*}
 */
int hashPageBinarySearch(Page *hashPage, int hashValue, HashIndex *hashIndex, int *idx)
{
  /* 1.变量定义 */
  BucketPage       *bucketPage      = NULL;
  HashOverflowPage *hofPage         = NULL;
  int               offset          = 0;
  int               recordLength    = 0;
  int               keyTidPairCount = 0;
  int               left            = 0;
  int               right           = 0;
  int               mid             = 0;
  int               curHashValue    = 0;
  BYTE             *curRecord       = NULL;

  /* 2.参数检查 */
  if (hashPage == NULL || hashIndex == NULL) {
    return 0;
  }
  *idx = -1;

  /* 3.根据页面类型确定头部偏移量 */
  if (hashPage->pageType == BUCKET_PAGE) {
    offset     = BUCKET_PAGE_HEADER_SIZE;
    bucketPage = (BucketPage *)hashPage;
    /* 读取桶页的键值对数量 */
    keyTidPairCount = bucketPage->keyTidPairCount; /* 5 = 1(页类型) + 4(bucketId) */
  } else if (hashPage->pageType == HASH_OVERFLOW_PAGE) {
    offset  = HASH_OVERFLOW_PAGE_HEADER_SIZE;
    hofPage = (HashOverflowPage *)hashPage;
    /* 读取溢出页的键值对数量 */
    keyTidPairCount = hofPage->keyTidPairCount; /* 5 = 1(页类型) + 4(bucketId) */
  } else {
    return GNCDB_NOT_FOUND;
  }

  /* 4.如果页面中没有记录，直接返回-1 */
  if (keyTidPairCount == 0) {
    return GNCDB_SUCCESS;
  }

  /* 5.计算记录长度 */
  recordLength = INT_SIZE + hashIndex->primaryKeyLenth; /* 哈希值 */

  /* 6.二分查找 */
  left  = 0;
  right = keyTidPairCount;

  while (left < right) {
    mid          = left + (right - left) / 2;
    curRecord    = hashPage->pData + offset + mid * recordLength;
    curHashValue = *(int *)curRecord;

    if (curHashValue < hashValue) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }

  /* 找到第一个 >= hashValue的下标 */
  *idx = left;

  return GNCDB_SUCCESS;
}

/**
 * @description: 比较hash键值对中的主键和传入的主键值List是否相等
 * @param {HashIndex} *hashIndex
 * @param {void} *key1 hash键值对中的主键值
 * @param {varArrayList} *key2 传入的主键值List
 * @return {*}
 */
bool isPrimaryKeyEqual(HashIndex *hashIndex, void *key1, varArrayList *key2)
{
  Column *pkColumn   = NULL;
  int     offset     = 0;
  void   *fieldValue = NULL;
  int     intVal1 = 0, intVal2 = 0;
  double  doubleVal1 = 0.0, doubleVal2 = 0.0;
  char   *strVal1 = NULL, *strVal2 = NULL;

  if (key1 == NULL || key2 == NULL) {
    return false;
  }

  for (int i = 0; i < hashIndex->pkCols->elementCount; i++) {
    pkColumn = varArrayListGetPointer(hashIndex->pkCols, i);
    if (pkColumn == NULL) {
      return false;
    }
    switch (pkColumn->fieldType) {
      case FIELDTYPE_INTEGER: {
        intVal1    = *(int *)(key1 + offset);
        fieldValue = varArrayListGetPointer(key2, i);
        intVal2    = *(int *)fieldValue;
        if (intVal1 != intVal2) {
          return false;
        }
        offset += INT_SIZE;
        break;
      }
      case FIELDTYPE_REAL: {
        doubleVal1 = *(double *)(key1 + offset);
        fieldValue = varArrayListGetPointer(key2, i);
        doubleVal2 = *(double *)fieldValue;
        if (doubleVal1 != doubleVal2) {
          return false;
        }
        offset += DOUBLE_SIZE;
        break;
      }
      case FIELDTYPE_VARCHAR: {
        strVal1    = (char *)(key1 + offset);
        fieldValue = varArrayListGetPointer(key2, i);
        strVal2    = (char *)fieldValue;
        if (strcmp(strVal1, strVal2) != 0) {
          return false;
        }
        offset += pkColumn->columnConstraint->maxValue;
        break;
      }
      default: {
        return false;
      }
    }
  }
  return true;
}

/**
 * @brief 创建哈希记录
 * @param record 输出参数，存储创建的记录
 * @param hashValue 哈希值
 * @param primaryKey 主键值
 * @param hashIndex 哈希索引信息
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int createHashRecord(BYTE *record, int hashValue, varArrayList *pkValueArray, HashIndex *hashIndex)
{
  /* 1.变量定义 */
  int     offset  = 0;
  void   *pkValue = NULL;
  Column *column  = NULL;

  /* 2.参数检查 */
  if (record == NULL || pkValueArray == NULL || hashIndex == NULL) {
    return GNCDB_PARAMNULL;
  }
  assert(pkValueArray->elementCount == hashIndex->pkCols->elementCount);

  /* 3.写入哈希值 */
  memcpy(record + offset, &hashValue, INT_SIZE);
  offset += INT_SIZE;

  /* 4.根据主键类型写入主键值 */
  for (int i = 0; i < pkValueArray->elementCount; i++) {
    column  = varArrayListGetPointer(hashIndex->pkCols, i);
    pkValue = varArrayListGetPointer(pkValueArray, i);
    switch (column->fieldType) {
      case FIELDTYPE_INTEGER: {
        memcpy(record + offset, pkValue, INT_SIZE);
        offset += INT_SIZE;
        break;
      }
      case FIELDTYPE_REAL: {
        memcpy(record + offset, pkValue, DOUBLE_SIZE);
        offset += DOUBLE_SIZE;
        break;
      }
      case FIELDTYPE_VARCHAR: {
        memcpy(record + offset, pkValue, column->columnConstraint->maxValue);
        offset += column->columnConstraint->maxValue;
        break;
      }
      default: return GNCDB_INVALID_FIELD_TYPE;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 向哈希页面中插入一条记录
 * @param hashPage 哈希页面指针（可能是桶页或溢出页）
 * @param record 要插入的记录
 * @param hashIndex 哈希索引信息
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int hashPageInsertRecord(Page *hashPage, BYTE *record, HashIndex *hashIndex)
{
  /* 1.变量定义 */
  int               offset          = 0;
  int               recordLength    = 0;
  int               keyTidPairCount = 0;
  int               hashValue       = 0;
  int               left            = 0;
  int               right           = 0;
  int               mid             = 0;
  BYTE             *curRecord       = NULL;
  int               curHashValue    = 0;
  BucketPage       *bucketPage      = NULL;
  HashOverflowPage *hofPage         = NULL;

  /* 2.参数检查 */
  if (hashPage == NULL || record == NULL || hashIndex == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.根据页面类型确定头部偏移量 */
  if (hashPage->pageType == BUCKET_PAGE) {
    offset     = BUCKET_PAGE_HEADER_SIZE;
    bucketPage = (BucketPage *)hashPage;
    /* 读取桶页的键值对数量 */
    keyTidPairCount = bucketPage->keyTidPairCount;
  } else if (hashPage->pageType == HASH_OVERFLOW_PAGE) {
    offset  = HASH_OVERFLOW_PAGE_HEADER_SIZE;
    hofPage = (HashOverflowPage *)hashPage;
    /* 读取溢出页的键值对数量 */
    keyTidPairCount = hofPage->keyTidPairCount;
  } else {
    return GNCDB_HASHPAGE_UNSAFE;
  }

  /* 4.计算记录长度 */
  recordLength = INT_SIZE + hashIndex->primaryKeyLenth; /* 哈希值 */

  /* 5.读取要插入记录的哈希值 */
  hashValue = *(int *)record;

  /* 6.遍历页面中的记录，找到插入位置 */
  //* 二分查找找到插入位置，参照了C++ std::upper_bound：返回第一个 > hashValue 的位置
  left  = 0;
  right = keyTidPairCount;
  while (left < right) {
    mid          = left + (right - left) / 2;
    curRecord    = hashPage->pData + offset + mid * recordLength;
    curHashValue = *(int *)curRecord;

    if (curHashValue <= hashValue) {
      left = mid + 1;
    } else {
      right = mid;
    }
  }

  // left 就是待插入位置下标
  curRecord = hashPage->pData + offset + left * recordLength;

  /* 8.将插入位置之后的记录后移 */
  // 只有在插入点不是末尾时才需要后移
  if (left < keyTidPairCount) {
    memmove(curRecord + recordLength, curRecord, (keyTidPairCount - left) * recordLength);
  }
  memcpy(curRecord, record, recordLength);

  /* 10.更新键值对数量 */
  if (hashPage->pageType == BUCKET_PAGE) {
    bucketPage->keyTidPairCount++;
  } else {
    hofPage->keyTidPairCount++;
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 计算整数类型的哈希值
 * @param value 整数值
 * @return 哈希值
 */
int getIntHashValue(int value)
{
  unsigned int   hash  = FNV_OFFSET_BASIS;
  unsigned char *bytes = (unsigned char *)&value;
  for (int i = 0; i < sizeof(int); i++) {
    hash ^= bytes[i];
    hash *= FNV_PRIME;
  }
  // printf("哈希值为%d\n",hash);
  return (int)hash;
}

/**
 * @brief 计算浮点数类型的哈希值
 * @param value 浮点数值
 * @return 哈希值
 */
int getDoubleHashValue(double value)
{
  unsigned int   hash  = FNV_OFFSET_BASIS;
  unsigned char *bytes = (unsigned char *)&value;

  for (int i = 0; i < sizeof(double); i++) {
    hash ^= bytes[i];
    hash *= FNV_PRIME;
  }
  return (int)hash;
}

/**
 * @brief 计算字符串类型的哈希值
 * @param str 字符串
 * @return 哈希值
 */
int getStringHashValue(const char *str)
{
  unsigned int hash = FNV_OFFSET_BASIS;

  if (str == NULL)
    return 0;

  for (; *str != '\0'; str++) {
    hash ^= (unsigned char)*str;
    hash *= FNV_PRIME;
  }
  return (int)hash;
}

/**
 * @brief 计算日期时间类型的哈希值
 * @param dateValue 日期值
 * @param timeValue 时间值
 * @return 哈希值
 */
int getDateTimeHashValue(int dateValue, int timeValue)
{
  unsigned int   hash      = FNV_OFFSET_BASIS;
  unsigned char *dateBytes = (unsigned char *)&dateValue;
  unsigned char *timeBytes = (unsigned char *)&timeValue;

  // 合并日期和时间的哈希值
  for (int i = 0; i < sizeof(int); i++) {
    hash ^= dateBytes[i];
    hash *= FNV_PRIME;
  }
  for (int i = 0; i < sizeof(int); i++) {
    hash ^= timeBytes[i];
    hash *= FNV_PRIME;
  }
  return (int)hash;
}

/**
 * @brief 根据主键类型获取对应的哈希值
 * @param key 主键结构体指针
 * @return 哈希值
 */
int getHashValue(void *value, FieldType type)
{
  // printf("getHashValue成功调用,哈希索引列类型为%d\n",type);
  if (value == NULL) {
    printf("传入的value指针为空\n");
    return 0;
  }

  switch (type) {
    case FIELDTYPE_INTEGER: {
      return getIntHashValue((*(int *)value));
      break;
    }

    case FIELDTYPE_REAL: {
      return getDoubleHashValue(*(double *)value);
      break;
    }
    case FIELDTYPE_VARCHAR: {
      return getStringHashValue((char *)value);
      break;
    }

    default: return 0;
  }
}

/**
 * @brief 哈希桶分裂
 * @param hashIndex 哈希索引结构
 * @param bucketId 需要分裂的桶号
 * @param db 数据库实例
 * @param tx 事务指针
 * @return 操作状态码
 */
int hashBucketSplit(HashIndex *hashIndex, int obucketId, MetaPage *metaPage, GNCDB *db, Transaction *tx)
{
  /* 1.变量定义 */
  int               rc            = 0;
  int               nbucketId     = 0;
  int               obucketPageId = 0;
  int               nbucketPageId = 0;
  BucketPage       *obucketPage   = NULL;
  BucketPage       *nbucketPage   = NULL;

  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, metaPage->page.id, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 3.计算新桶号 */
  nbucketId = metaPage->bucketCount;
  if (db == NULL || hashIndex == NULL || metaPage == NULL) {
    return GNCDB_PARAMNULL;
  }
  if (nbucketId > metaPage->highMask) {
    metaPage->splitCount++;
    metaPage->lowMask  = metaPage->highMask;
    metaPage->highMask = nbucketId | metaPage->lowMask;
  }
  // printf(
  //     "分裂桶：%d, 新桶号：%d, 高掩码：%d, 低掩码：%d\n", obucketId, nbucketId, metaPage->highMask, metaPage->lowMask);

  /* 4.创建新桶页 */
  nbucketPage = CreateHashBucketPage(db, hashIndex, nbucketId, tx);
  WriteLock(&(nbucketPage->page.rwlock_t));
  nbucketPageId = nbucketPage->page.id;
  memcpy(metaPage->page.pData + META_PAGE_HEADER_SIZE + nbucketId * INT_SIZE, &nbucketPageId, INT_SIZE);
  nbucketPage->primaryKeyLenth = hashIndex->primaryKeyLenth;
  metaPage->bucketCount++;

  /* 5.获取旧桶页 */
  memcpy(&obucketPageId, metaPage->page.pData + META_PAGE_HEADER_SIZE + obucketId * INT_SIZE, INT_SIZE);
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, obucketPageId, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = pagePoolGetPage((Page **)&obucketPage, obucketPageId, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_NOT_FOUND;
  }
  WriteLock(&(obucketPage->page.rwlock_t));

  /* 6.重新分配键值对 */
  rc = redistributeHashBucket(hashIndex, metaPage, obucketPage, nbucketPage, db, tx);
  if (rc != GNCDB_SUCCESS)
    return rc;

  /* 9.设置旧桶的所有页面为脏页，新桶中的页面都是新建的，默认会在事务提交时设置脏页面 */
  rc = setPageStatusDirty(db->pagePool, metaPage->page.id, NULL);
  rc = setPageStatusDirty(db->pagePool, obucketPage->page.id, NULL);
  WriteUnLock(&(obucketPage->page.rwlock_t));
  WriteUnLock(&(nbucketPage->page.rwlock_t));

  return GNCDB_SUCCESS;
}

/**
 * @brief 重新分配哈希桶中的键值对
 * @param hashIndex 哈希索引结构
 * @param oldBucketPage 原桶页
 * @param newBucketPage 新桶页
 * @return 操作状态码
 */
int redistributeHashBucket(HashIndex *hashIndex, MetaPage *metaPage, BucketPage *obucketPage, BucketPage *newBucketPage,
    GNCDB *db, Transaction *tx)
{
  /* 1.变量定义 */
  int               rc            = GNCDB_SUCCESS;
  int               obucketKVNum  = 0;  // 重分配后旧桶的总键值对数量
  int               oldBucketId   = obucketPage->bucketId;
  int               newBucketId   = newBucketPage->bucketId;
  int               currentPageId = obucketPage->page.id;
  int               nextPageId    = 0;
  int               recordSize    = 0;
  int               hashValue     = 0;
  int               bucketNumber  = 0;
  int               recordCount   = 0;
  int               originPages   = 0;  // 分裂之前，旧桶的页面总数
  BYTE             *record        = NULL;
  BYTE             *pageData      = NULL;
  Page             *currentPage   = NULL;
  HashOverflowPage *overflowPage  = NULL;
  int               i             = 0;

  recordCount = obucketPage->keyTidPairCount;
  nextPageId  = obucketPage->firstOverflowPageId;
  if (db == NULL || obucketPage == NULL || newBucketPage == NULL || metaPage == NULL) {
    return GNCDB_PARAMNULL;
  }

  recordSize    = sizeof(int) + obucketPage->primaryKeyLenth;
  currentPageId = obucketPage->page.id;

  while (currentPageId >= 0) {
    rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, currentPageId, EXCLUSIVE);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = setPageStatusDirty(db->pagePool, currentPageId, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    /* 获取当前页面 */
    if (currentPageId == obucketPage->page.id) {
      currentPage = (Page *)obucketPage;
      recordCount = obucketPage->keyTidPairCount;
      nextPageId  = obucketPage->firstOverflowPageId;
      rc          = produceOldPageData(db, (Page *)obucketPage, BUCKET_PAGE, tx);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    } else {
      rc = pagePoolGetPage((Page **)&currentPage, currentPageId, hashIndex->tableName, db);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      rc = produceOldPageData(db, currentPage, HASH_OVERFLOW_PAGE, tx);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      overflowPage = (HashOverflowPage *)currentPage;
      nextPageId   = overflowPage->nextPageId;
      recordCount  = overflowPage->keyTidPairCount;
    }
    /* 处理当前页面中的记录 */
    pageData = currentPage->pData;
    /* 遍历页面中的所有记录 */
    i = 0;
    while (i < recordCount) {
      /* 获取记录 */
      if (currentPageId == obucketPage->page.id) {
        record = pageData + BUCKET_PAGE_HEADER_SIZE + i * recordSize;
      } else {
        record = pageData + HASH_OVERFLOW_PAGE_HEADER_SIZE + i * recordSize;
      }

      /* 提取哈希值并计算桶号 */
      hashValue = *(int *)record;
      HASH_VALUE_TO_BUCKER_ID(metaPage, hashValue, bucketNumber);

      /* 如果记录应该移动到新桶 */
      if (bucketNumber == newBucketId) {
        /* 将记录插入到新桶 */
        rc = newBucketPageInsert(hashIndex, metaPage, newBucketPage, hashValue, record, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        /* 从当前页面删除记录（将后面的记录前移） */
        if (currentPageId == obucketPage->page.id) {
          if (i < recordCount - 1) {
            memmove(record, record + recordSize, (recordCount - i - 1) * recordSize);
          }
          obucketPage->keyTidPairCount--;
        } else {
          if (i < recordCount - 1) {
            memmove(record, record + recordSize, (recordCount - i - 1) * recordSize);
          }
          overflowPage->keyTidPairCount--;
        }

        /* 由于删除了一条记录，不需要增加i */
        recordCount--;
      } else if (bucketNumber == oldBucketId) {
        /* 记录保留在旧桶，继续下一条记录 */
        i++;
      } else {
        printf("哈希桶分裂时，记录的桶号不正确！\n");
      }
    }

    if (currentPageId == obucketPage->page.id) {
      obucketKVNum += obucketPage->keyTidPairCount;
    } else {
      obucketKVNum += overflowPage->keyTidPairCount;
    }
    originPages++;
    /* 移动到下一个页面 */
    currentPageId = nextPageId;
  }

  rc = squeezeBucket(hashIndex, obucketPage, originPages, obucketKVNum, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}

/**
 * @description: 压缩一个哈希桶
 * @param {HashIndex} *hashIndex 哈希索引指针
 * @param {BucketPage} *obucketPage 待压缩的桶页
 * @param {int} originPages 压缩之前的页面总数
 * @param {int} obucketKVNum 压缩之前的键值对总数
 * @param {GNCDB} *db
 * @return {*}
 */
int squeezeBucket(HashIndex *hashIndex, BucketPage *obucketPage, int originPages, int obucketKVNum, GNCDB *db)
{
  int               rc              = GNCDB_SUCCESS;
  int               i               = 0;
  int               maxEntryPerPage = GET_BUCKET_PAIR_MAX_COUNT(hashIndex, db->pageCurrentSize);
  int               recordCount     = 0;
  int               neededPages     = 0;  // 压缩完成后，旧桶需要的页面总数
  int               deletePgaeNum   = 0;  // 压缩过程中需要删除的页面数量
  int               deletePageId    = 0;  // 从后向前扫描的待删除页面ID
  int               insertPageId    = 0;  // 从前往后扫描的待插入页面ID
  HashOverflowPage *deleteOFPage    = NULL;
  HashOverflowPage *insertOFPage    = NULL;
  Page             *insertPage      = NULL;
  Page             *prevPage        = NULL;
  BYTE             *pageData        = NULL;
  BYTE             *record          = NULL;
  int               recordSize      = sizeof(int) + hashIndex->primaryKeyLenth;

  // 计算当前桶需要的总页面数量 向上取整
  neededPages = (obucketKVNum + maxEntryPerPage - 1) / maxEntryPerPage;
  if (neededPages <= 0) {
    neededPages = 1;
  }
  deletePgaeNum = originPages - neededPages;
  if (deletePgaeNum <= 0) {
    return GNCDB_SUCCESS;
  }

  //* 获取最后一个待删除的溢出页
  deletePageId = obucketPage->lastOverflowPageId;
  rc           = pagePoolGetPage((Page **)&deleteOFPage, deletePageId, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  //* 获取第一个存在空闲待插入的页面
  insertPageId = obucketPage->page.id;
  if (obucketPage->keyTidPairCount < maxEntryPerPage) {
    insertPageId = obucketPage->page.id;
    insertPage   = (Page *)obucketPage;
  } else {
    insertPageId = obucketPage->firstOverflowPageId;
    while (insertPageId > 0) {
      rc = pagePoolGetPage((Page **)&insertOFPage, insertPageId, hashIndex->tableName, db);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      if (insertOFPage->keyTidPairCount < maxEntryPerPage) {
        insertPage = (Page *)insertOFPage;
        break;
      } else {
        insertPageId = insertOFPage->nextPageId;
      }
    }
  }

  //* 从后往前遍历删除页面
  while (deletePgaeNum > 0) {
    i           = 0;
    recordCount = deleteOFPage->keyTidPairCount;
    pageData    = deleteOFPage->page.pData;
    //* 遍历当前待删除页面中的所有记录
    while (i < recordCount) {
      record = pageData + HASH_OVERFLOW_PAGE_HEADER_SIZE + i * recordSize;
      rc     = hashPageInsertRecord(insertPage, record, hashIndex);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      if (insertPage->pageType == HASH_OVERFLOW_PAGE) {
        insertOFPage = (HashOverflowPage *)insertPage;
        if (insertOFPage->keyTidPairCount == maxEntryPerPage) {
          insertPageId = insertOFPage->nextPageId;
          rc           = pagePoolGetPage((Page **)&insertPage, insertPageId, hashIndex->tableName, db);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }
        }
      } else {
        obucketPage = (BucketPage *)insertPage;
        if (obucketPage->keyTidPairCount == maxEntryPerPage) {
          insertPageId = obucketPage->firstOverflowPageId;
          rc           = pagePoolGetPage((Page **)&insertPage, insertPageId, hashIndex->tableName, db);
          if (rc != GNCDB_SUCCESS) {
            return rc;
          }
        }
      }
      i++;
    }
    //* 释放当前溢出页、获取前一个溢出页并更新指针
    rc = pagePoolGetPage(&prevPage, deleteOFPage->prevPageId, hashIndex->tableName, db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    hashOverflowPageToFreePage(deleteOFPage, db);
    if (prevPage->pageType == HASH_OVERFLOW_PAGE) {
      deleteOFPage                    = (HashOverflowPage *)prevPage;
      deleteOFPage->nextPageId        = -1;
      obucketPage->lastOverflowPageId = prevPage->id;
    } else {
      obucketPage                      = (BucketPage *)prevPage;
      obucketPage->firstOverflowPageId = -1;
      obucketPage->lastOverflowPageId  = -1;
    }
    deletePgaeNum--;
  }
  return GNCDB_SUCCESS;
}

/**
 * @brief 键值对插入到新桶中，由于新桶中的页在创建时会备份和加写锁，所以不需要手动加锁和备份
 * @return 操作状态码
 */
int newBucketPageInsert(HashIndex *hashIndex, MetaPage *metaPage, BucketPage *bucketPage, int hashValue, BYTE *record,
    GNCDB *db, Transaction *tx)
{
  /* 1.变量定义 */
  int               rc              = GNCDB_SUCCESS;
  HashOverflowPage *overflowPage    = NULL;
  HashOverflowPage *newoverflowPage = NULL;
  int               bucketId        = 0;
  int               currentPageId   = -1;
  bool              inserted        = false;
  int               maxEntryPerPage = GET_BUCKET_PAIR_MAX_COUNT(hashIndex, db->pageCurrentSize);

  bucketId = bucketPage->bucketId;
  if (bucketPage->keyTidPairCount < maxEntryPerPage) {
    rc = hashPageInsertRecord((Page *)bucketPage, record, hashIndex);
    if (rc == GNCDB_SUCCESS) {
      inserted = true;
    }
  } else {
    /* 尝试插入到溢出页 */
    currentPageId = bucketPage->lastOverflowPageId;
    while (currentPageId > 0 && !inserted) {
      rc = pagePoolGetPage((Page **)&overflowPage, currentPageId, hashIndex->tableName, db);
      if (rc != GNCDB_SUCCESS) {
        my_free(record);
        return rc;
      }

      if (overflowPage->keyTidPairCount < maxEntryPerPage) {
        rc = hashPageInsertRecord((Page *)overflowPage, record, hashIndex);
        if (rc == GNCDB_SUCCESS) {
          inserted = true;
        }
        break;
      } else {
        /* 溢出页已满，继续查找下一个溢出页 */
        currentPageId = overflowPage->nextPageId;
      }
    }
    if (!inserted) {
      /* 创建新的溢出页 */
      newoverflowPage = CreateHashOverflowPage(db, hashIndex, bucketId, tx);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      if (overflowPage == NULL) {  // 说明新创建的溢出页是第一页
        newoverflowPage->prevPageId     = bucketPage->page.id;
        bucketPage->firstOverflowPageId = newoverflowPage->page.id;
      } else {  // 说明之前存在存在溢出页但是是满的
        overflowPage->nextPageId    = newoverflowPage->page.id;
        newoverflowPage->prevPageId = overflowPage->page.id;
      }
      bucketPage->lastOverflowPageId = newoverflowPage->page.id;
      rc                             = hashPageInsertRecord((Page *)newoverflowPage, record, hashIndex);
      if (rc == GNCDB_SUCCESS) {
        inserted = true;
      }
    }
  }
  if (inserted) {
    return GNCDB_SUCCESS;
  }
  return GNCDB_HASHINDEX_INSERT_FAILED;
}

/**
 * @brief HashIndex的创建
 * @param indexId 索引ID
 * @param metaPageId 元数据页号
 * @param indexColumns 索引列
 * @param tableName 表名
 * @param catalog 目录管理器
 * @return 返回HashIndex指针
 */
HashIndex *hashIndexConstruct(
    int metaPageId, varArrayList *indexColumns, char *tableName, char *indexName, Catalog *catalog)
{
  /* 1.定义变量 */
  HashIndex *hashIndex = NULL;

  /* 2.参数检查 */
  if (metaPageId <= 0 || tableName == NULL || catalog == NULL || indexName == NULL) {
    return NULL;
  }

  /* 4.分配HashIndex指针内存 */
  hashIndex = (HashIndex *)my_malloc(sizeof(HashIndex));
  if (hashIndex == NULL) {
    return NULL;
  }

  /* 5.复制表名与索引名 */
  hashIndex->tableName = strdup(tableName);
  hashIndex->indexName = strdup(indexName);

  /* 6.初始化其他成员 */
  hashIndex->primaryKeyLenth = 0;
  hashIndex->meta_page_id    = metaPageId;
  hashIndex->index_columns   = indexColumns;
  hashIndex->pkCols          = NULL;
  /* 7.初始化读写锁 */
  ReadWriteLockInit(&hashIndex->rwlock);

  return hashIndex;
}

/**
 * @brief HashIndex的销毁
 * @param hashIndex 指向HashIndex指针的指针
 */
void hashIndexDestroy(HashIndex **hashIndex)
{
  /* 1.参数检查 */
  if (hashIndex == NULL || *hashIndex == NULL) {
    return;
  }

  /* 2.释放表名与索引名 */
  if ((*hashIndex)->tableName != NULL) {
    my_free((*hashIndex)->tableName);
  }
  if ((*hashIndex)->indexName != NULL) {
    my_free((*hashIndex)->indexName);
  }
  /* 3.释放pkCols */
  if ((*hashIndex)->pkCols != NULL) {
    varArrayListDestroy(&(*hashIndex)->pkCols);
  }

  /* 4.无需释放索引列数组，引用的是indexSchema中的，由它自身释放 */
  if ((*hashIndex)->index_columns != NULL) {
    (*hashIndex)->index_columns = NULL;
  }

  /* 5.销毁读写锁 */
  ReadWriteLockDestroy(&(*hashIndex)->rwlock);

  /* 6.释放索引结构 */
  my_free(*hashIndex);
  *hashIndex = NULL;
}
/**
 * @brief 向哈希索引中插入键值对
 * @param hashIndex 哈希索引结构指针
 * @param keyValue 键值
 * @param primaryKeyValue 主键值
 * @param primaryKeySize 主键大小
 * @param db 数据库实例指针
 * @return 状态码
 */
int hashIndexInsert(
    HashIndex *hashIndex, int hashValue, varArrayList *pkValueArray, bool isCreate, GNCDB *db, Transaction *tx)
{
  int               rc              = GNCDB_SUCCESS;
  MetaPage         *metaPage        = NULL;
  BucketPage       *bucketPage      = NULL;
  HashOverflowPage *overflowPage    = NULL;
  HashOverflowPage *newoverflowPage = NULL;
  BYTE             *record          = NULL;
  int               bucketId        = 0;
  int               bucketPageId    = 0;
  int               currentPageId   = -1;
  double            fillFactor      = 0.0;
  bool              inserted        = false;
  int               maxEntryPerPage = GET_BUCKET_PAIR_MAX_COUNT(hashIndex, db->pageCurrentSize);

  if (hashIndex == NULL || pkValueArray == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }

  // 获取桶页
  if (!isCreate) {
    rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, hashIndex->meta_page_id, EXCLUSIVE);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  rc = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  HASH_VALUE_TO_BUCKER_ID(metaPage, hashValue, bucketId);
  memcpy(&bucketPageId, metaPage->page.pData + META_PAGE_HEADER_SIZE + bucketId * INT_SIZE, INT_SIZE);
  if (!isCreate) {
    rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, bucketPageId, EXCLUSIVE);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }
  rc = pagePoolGetPage((Page **)&bucketPage, bucketPageId, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return GNCDB_NOT_FOUND;
  }
  WriteLock(&(bucketPage->page.rwlock_t));

  /* 6.创建哈希记录 */
  record = (BYTE *)my_malloc0(INT_SIZE + bucketPage->primaryKeyLenth);
  rc     = createHashRecord(record, hashValue, pkValueArray, hashIndex);
  if (rc != GNCDB_SUCCESS) {
    my_free(record);
    return rc;
  }

  /* 8.尝试插入键值对 */
  if (bucketPage->keyTidPairCount < maxEntryPerPage) {
    rc = produceOldPageData(db, (Page *)bucketPage, BUCKET_PAGE, tx);
    if (rc != GNCDB_SUCCESS) {
      my_free(record);
      return rc;
    }
    rc = hashPageInsertRecord((Page *)bucketPage, record, hashIndex);
    if (rc == GNCDB_SUCCESS) {
      inserted = true;
      setPageStatusDirty(db->pagePool, bucketPage->page.id, NULL);
    }
  } else {
    /* 尝试插入到溢出页 */
    currentPageId = bucketPage->lastOverflowPageId;
    while (currentPageId > 0 && !inserted) {
      if (!isCreate) {
        rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, currentPageId, EXCLUSIVE);
        if (rc != GNCDB_SUCCESS) {
          my_free(record);
          return rc;
        }
      }
      rc = pagePoolGetPage((Page **)&overflowPage, currentPageId, hashIndex->tableName, db);
      if (rc != GNCDB_SUCCESS) {
        my_free(record);
        return rc;
      }
      if (overflowPage->keyTidPairCount < maxEntryPerPage) {
        rc = produceOldPageData(db, (Page *)overflowPage, HASH_OVERFLOW_PAGE, tx);
        if (rc != GNCDB_SUCCESS) {
          my_free(record);
          return rc;
        }
        rc = hashPageInsertRecord((Page *)overflowPage, record, hashIndex);
        if (rc == GNCDB_SUCCESS) {
          inserted = true;
          setPageStatusDirty(db->pagePool, overflowPage->page.id, NULL);
        } else {
          return rc;
        }
        break;
      }
      currentPageId = overflowPage->nextPageId;
    }
    if (!inserted) {
      /* 创建新的溢出页 */
      newoverflowPage = CreateHashOverflowPage(db, hashIndex, bucketId, tx);
      if (overflowPage == NULL) {  // 说明新创建的溢出页是第一页
        newoverflowPage->prevPageId     = bucketPage->page.id;
        bucketPage->firstOverflowPageId = newoverflowPage->page.id;
        setPageStatusDirty(db->pagePool, bucketPage->page.id, NULL);
      } else {  // 说明之前存在存在溢出页但是是满的
        overflowPage->nextPageId    = newoverflowPage->page.id;
        newoverflowPage->prevPageId = overflowPage->page.id;
        setPageStatusDirty(db->pagePool, overflowPage->page.id, NULL);
      }
      bucketPage->lastOverflowPageId = newoverflowPage->page.id;
      if (rc != GNCDB_SUCCESS) {
        my_free(record);
        return rc;
      }
      rc = hashPageInsertRecord((Page *)newoverflowPage, record, hashIndex);
      if (rc == GNCDB_SUCCESS) {
        inserted = true;
      }
    }
  }
  WriteUnLock(&(bucketPage->page.rwlock_t));

  /* 9.更新元数据 */
  if (inserted) {
    metaPage->keyTidPairCount++;
    setPageStatusDirty(db->pagePool, metaPage->page.id, NULL);
    fillFactor = (double)metaPage->keyTidPairCount / (metaPage->bucketCount * GET_BUCKET_PAIR_MAX_COUNT(hashIndex, db->pageCurrentSize));
    if (fillFactor - MAX_FILL_FACTOR > 1e-5) {
      if (metaPage->bucketCount < MAX_BUCKET_COUNT(db->pageCurrentSize)) {
        bucketId = metaPage->bucketCount & metaPage->lowMask;
        rc       = hashBucketSplit(hashIndex, bucketId, metaPage, db, tx);
      }
    }
    setPageStatusPinDown(db->pagePool, metaPage->page.id, NULL);
    my_free(record);
    return GNCDB_SUCCESS;
  }
  return GNCDB_HASHINDEX_INSERT_FAILED;
}
/**
 * @brief 从哈希索引中删除一条指定的键值对
 * @param hashIndex 哈希索引结构指针
 * @param hashValue 哈希值
 * @param primaryKeyValue 主键值
 * @param db 数据库实例指针
 * @return int 状态码
 */
int hashIndexDelete(HashIndex *hashIndex, int hashValue, varArrayList *pkValueArray, GNCDB *db, Transaction *tx)
{
  int               rc                    = 0;
  MetaPage         *metaPage              = NULL;
  BucketPage       *bucketPage            = NULL;
  BucketPage       *prevBucketPage        = NULL;
  HashOverflowPage *overflowPage          = NULL;
  HashOverflowPage *prevOFPage            = NULL;
  Page             *prevPage              = NULL;
  void             *curPKValue            = NULL;
  int               bucketId              = 0;
  int               position              = 0;
  int               offset                = 0;
  int               currentHash           = 0;
  int               currentOverflowPageId = -1;
  int               bucketPageId          = 0;
  int               pairLen               = 0;
  bool              isDeleted             = false;

  /* 1. 参数检查 */
  if (hashIndex == NULL || pkValueArray == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2. 获取元数据页计算桶ID并创建结果索引数组 */
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, hashIndex->meta_page_id, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  HASH_VALUE_TO_BUCKER_ID(metaPage, hashValue, bucketId);

  /* 3. 获取桶页 */
  bucketPageId = *(int *)(metaPage->page.pData + META_PAGE_HEADER_SIZE + bucketId * INT_SIZE);
  rc           = pagePoolGetPage((Page **)&bucketPage, bucketPageId, hashIndex->tableName, db);
  WriteLock(&(bucketPage->page.rwlock_t));
  setPageStatusPinUp(db->pagePool, bucketPage->page.id, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 4. 在桶页中查找并删除记录 */
  pairLen = INT_SIZE + bucketPage->primaryKeyLenth;
  rc      = lockManagerAcquireLock(db->transactionManager->lockManager, tx, bucketPageId, EXCLUSIVE);
  rc      = hashPageBinarySearch(&bucketPage->page, hashValue, hashIndex, &position);
  if (rc != GNCDB_SUCCESS) {
    setPageStatusPinDown(db->pagePool, bucketPage->page.id, NULL);
    return rc;
  }
  while (!isDeleted) {
    if (position < 0 || position >= bucketPage->keyTidPairCount) {
      break;
    }
    currentHash = *(int *)(bucketPage->page.pData + BUCKET_PAGE_HEADER_SIZE + position * pairLen);
    if (currentHash != hashValue) {
      break;
    }
    curPKValue = (bucketPage->page.pData + BUCKET_PAGE_HEADER_SIZE + position * pairLen + INT_SIZE);
    /* 检查主键是否匹配 */
    if (!isPrimaryKeyEqual(hashIndex, curPKValue, pkValueArray)) {
      position++;
      continue;
    }

    rc = produceOldPageData(db, (Page *)bucketPage, BUCKET_PAGE, tx);
    if (rc != GNCDB_SUCCESS) {
      setPageStatusPinDown(db->pagePool, bucketPage->page.id, NULL);
      return rc;
    }
    //* 先删除该元素
    if (position < bucketPage->keyTidPairCount - 1) {
      // 后面前移覆盖
      memmove(bucketPage->page.pData + BUCKET_PAGE_HEADER_SIZE + position * pairLen,
          bucketPage->page.pData + BUCKET_PAGE_HEADER_SIZE + (position + 1) * pairLen,
          (bucketPage->keyTidPairCount - position - 1) * pairLen);
    }
    bucketPage->keyTidPairCount--;
    setPageStatusDirty(db->pagePool, bucketPage->page.id, NULL);
    rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, hashIndex->meta_page_id, EXCLUSIVE);
    rc = produceOldPageData(db, (Page *)metaPage, META_PAGE, tx);
    metaPage->keyTidPairCount--;
    setPageStatusDirty(db->pagePool, metaPage->page.id, NULL);
    // 使用最后一页替换
    if (bucketPage->lastOverflowPageId > 0) {
      rc = coverWithLastPair(hashIndex, bucketPage, (Page *)bucketPage, position, db, tx);
      if (rc != GNCDB_SUCCESS) {
        setPageStatusPinDown(db->pagePool, bucketPage->page.id, NULL);
        return rc;
      }
    }
    isDeleted = true;
  }

  /* 5. 在溢出页中查找并删除记录 */
  if (!isDeleted && bucketPage->firstOverflowPageId > 0) {
    offset                = HASH_OVERFLOW_PAGE_HEADER_SIZE;
    currentOverflowPageId = bucketPage->firstOverflowPageId;
    while (!isDeleted && currentOverflowPageId > 0) {
      rc      = lockManagerAcquireLock(db->transactionManager->lockManager, tx, currentOverflowPageId, EXCLUSIVE);
      rc      = pagePoolGetPage((Page **)&overflowPage, currentOverflowPageId, hashIndex->tableName, db);
      pairLen = INT_SIZE + overflowPage->primaryKeyLenth;
      setPageStatusPinUp(db->pagePool, overflowPage->page.id, NULL);
      if (rc != GNCDB_SUCCESS)
        return rc;
      rc = hashPageBinarySearch(&overflowPage->page, hashValue, hashIndex, &position);
      if (rc != GNCDB_SUCCESS) {
        setPageStatusPinDown(db->pagePool, overflowPage->page.id, NULL);
        return rc;
      }
      while (!isDeleted) {
        if (position < 0 || position >= overflowPage->keyTidPairCount) {
          break;
        }
        currentHash = *(int *)(overflowPage->page.pData + HASH_OVERFLOW_PAGE_HEADER_SIZE + position * pairLen);
        if (currentHash != hashValue) {
          break;
        }
        curPKValue = (overflowPage->page.pData + HASH_OVERFLOW_PAGE_HEADER_SIZE + position * pairLen + INT_SIZE);

        /* 检查主键是否匹配 */
        if (!isPrimaryKeyEqual(hashIndex, curPKValue, pkValueArray)) {
          position++;
          continue;
        }

        rc = produceOldPageData(db, (Page*)overflowPage, HASH_OVERFLOW_PAGE, tx);
        if (rc != GNCDB_SUCCESS) {
          setPageStatusPinDown(db->pagePool, overflowPage->page.id, NULL);
          return rc;
        }
        //* 先删除（直接覆盖）该元素
        if (position < overflowPage->keyTidPairCount - 1) {
          // 后面前移覆盖
          memmove(overflowPage->page.pData + offset + position * pairLen,
              overflowPage->page.pData + offset + (position + 1) * pairLen,
              (overflowPage->keyTidPairCount - position - 1) * pairLen);
        }
        overflowPage->keyTidPairCount--;
        setPageStatusDirty(db->pagePool, overflowPage->page.id, NULL);
        rc = produceOldPageData(db, (Page*)metaPage, META_PAGE, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        metaPage->keyTidPairCount--;
        setPageStatusDirty(db->pagePool, metaPage->page.id, NULL);
        //* 判断是否是最后一页 */
        if (overflowPage->page.id != bucketPage->lastOverflowPageId) {
          //* 需要用最后一页的最后一个元素替换当前元素 */
          rc = coverWithLastPair(hashIndex, bucketPage, (Page *)overflowPage, position, db, tx);
        } else {
          //* 是最后一个溢出页，如果内容为空则直接删除
          if (overflowPage->keyTidPairCount == 0) {
            rc = pagePoolGetPage(&prevPage, overflowPage->prevPageId, hashIndex->tableName, db);
            if (rc != GNCDB_SUCCESS) {
              return rc;
            }
            rc = produceOldPageData(db, prevPage, BUCKET_PAGE, tx);
            if (rc != GNCDB_SUCCESS) {
              return rc;
            }
            if (prevPage->pageType == BUCKET_PAGE) {
              //* 该溢出页是桶页的唯一一个溢出页 */
              prevBucketPage                      = (BucketPage *)prevPage;
              prevBucketPage->firstOverflowPageId = -1;
              prevBucketPage->lastOverflowPageId  = -1;
              setPageStatusDirty(db->pagePool, prevBucketPage->page.id, NULL);
            } else if (prevPage->pageType == HASH_OVERFLOW_PAGE) {
              //* 该溢出页不是桶的唯一一个溢出页 */
              prevOFPage                     = (HashOverflowPage *)prevPage;
              bucketPage->lastOverflowPageId = overflowPage->prevPageId;
              prevOFPage->nextPageId         = -1;
              setPageStatusDirty(db->pagePool, prevOFPage->page.id, NULL);
            }
            rc = hashOverflowPageToFreePage(overflowPage, db);
          }
        }
        isDeleted = true;
      }

      currentOverflowPageId = overflowPage->nextPageId;
      setPageStatusPinDown(db->pagePool, overflowPage->page.id, NULL);
    }
  }
  /* 6.释放资源 */
  setPageStatusDirty(db->pagePool, metaPage->page.id, NULL);
  setPageStatusPinDown(db->pagePool, bucketPage->page.id, NULL);
  WriteUnLock(&(bucketPage->page.rwlock_t));
  return rc;
}

/**
 * @brief 从哈希索引中查找对应键值对
 * @param hashIndex 哈希索引
 * @param hashValue 哈希值
 * @param primarykeyArray 存储找到的主键列表，需要调用者预先分配内存
 * @param db 数据库实例
 * @return 操作状态码
 */
int hashIndexSelect(HashIndex *hashIndex, int hashValue, varArrayList *pkValueArrays, GNCDB *db, Transaction *tx)
{
  /* 1.变量定义 */
  int               rc           = GNCDB_SUCCESS;
  int               bucketId     = 0;
  MetaPage         *metaPage     = NULL;
  BucketPage       *bucketPage   = NULL;
  HashOverflowPage *overflowPage = NULL;
  Page             *page         = NULL;
  varArrayList     *pkValueArray = NULL;
  void             *curPKValue   = NULL;
  Column           *pkCol        = NULL;
  int               offset       = 0;
  int               pkOffset     = 0;
  int               curHashValue = 0;
  int               pairLen      = 0;
  int               position     = 0;
  int               nextPageId   = 0;
  int               bucketPageId = 0;

  /* 2.参数检查 */
  if (hashIndex == NULL || pkValueArrays == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取元数据页 */
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, hashIndex->meta_page_id, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, NULL, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 4.计算桶ID */
  HASH_VALUE_TO_BUCKER_ID(metaPage, hashValue, bucketId);

  /* 5.获取桶页 */
  bucketPageId = *(int *)(metaPage->page.pData + META_PAGE_HEADER_SIZE + bucketId * INT_SIZE);
  rc           = lockManagerAcquireLock(db->transactionManager->lockManager, tx, bucketPageId, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = pagePoolGetPage((Page **)&bucketPage, bucketPageId, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  WriteLock(&(bucketPage->page.rwlock_t));
  setPageStatusPinUp(db->pagePool, bucketPage->page.id, NULL);
  if (rc != GNCDB_SUCCESS) {
    setPageStatusPinDown(db->pagePool, metaPage->page.id, NULL);
    return rc;
  }

  /* 7.在桶页中查找匹配的哈希值 */
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, bucketPageId, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  offset  = BUCKET_PAGE_HEADER_SIZE;
  pairLen = INT_SIZE + bucketPage->primaryKeyLenth;
  rc      = hashPageBinarySearch(&bucketPage->page, hashValue, hashIndex, &position);
  if (rc != GNCDB_SUCCESS) {
    setPageStatusPinDown(db->pagePool, metaPage->page.id, NULL);
    setPageStatusPinDown(db->pagePool, bucketPage->page.id, NULL);
    return rc;
  }

  /* 8.处理桶页：从idx分别向前向后遍历，直到找到不等的hashKey或者遍历完 */
  while (true) {
    if (position < 0 || position >= bucketPage->keyTidPairCount) {
      break;
    }
    curHashValue = *(int *)(bucketPage->page.pData + offset + position * pairLen);
    if (curHashValue != hashValue) {
      break;
    }
    curPKValue   = (bucketPage->page.pData + offset + position * pairLen + INT_SIZE);
    pkValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
    pkOffset     = 0;
    for (int j = 0; j < hashIndex->pkCols->elementCount; j++) {
      pkCol = (Column *)varArrayListGetPointer(hashIndex->pkCols, j);
      switch (pkCol->fieldType) {
        case FIELDTYPE_INTEGER:
          rc = varArrayListAddPointer(pkValueArray, curPKValue + pkOffset);
          pkOffset += INT_SIZE;
          break;
        case FIELDTYPE_REAL:
          rc = varArrayListAddPointer(pkValueArray, curPKValue + pkOffset);
          pkOffset += FLOAT_SIZE;
          break;
        case FIELDTYPE_VARCHAR:
          rc = varArrayListAddPointer(pkValueArray, curPKValue + pkOffset);
          pkOffset += pkCol->columnConstraint->maxValue;
          break;
        default: return GNCDB_INVALID_FIELD_TYPE;
      }
    }
    /* 将主键值添加到结果数组 */
    rc = varArrayListAddPointer(pkValueArrays, pkValueArray);
    position++;
  }

  /* 9.检查溢出页链表 */
  nextPageId = bucketPage->firstOverflowPageId;
  rc         = setPageStatusPinDown(db->pagePool, bucketPage->page.id, NULL);
  rc         = lockManagerAcquireLock(db->transactionManager->lockManager, tx, nextPageId, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  offset = HASH_OVERFLOW_PAGE_HEADER_SIZE;
  while (nextPageId > 0) {
    /* 获取溢出页 */
    rc = pagePoolGetPage(&page, nextPageId, NULL, db);
    if (rc != GNCDB_SUCCESS || page == NULL) {
      setPageStatusPinDown(db->pagePool, metaPage->page.id, NULL);
      return rc;
    }

    overflowPage = (HashOverflowPage *)page;

    /* 在溢出页中查找匹配的哈希值 */
    rc = hashPageBinarySearch(&overflowPage->page, hashValue, hashIndex, &position);
    if (rc != GNCDB_SUCCESS) {
      setPageStatusPinDown(db->pagePool, metaPage->page.id, NULL);
      setPageStatusPinDown(db->pagePool, overflowPage->page.id, NULL);
      return rc;
    }

    /* 处理溢出页中找到的结果 */
    while (true) {
      if (position < 0 || position >= overflowPage->keyTidPairCount) {
        break;
      }
      curHashValue = *(int *)(overflowPage->page.pData + offset + position * pairLen);
      if (curHashValue != hashValue) {
        break;
      }
      curPKValue   = (overflowPage->page.pData + offset + position * pairLen + INT_SIZE);
      pkValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
      pkOffset     = 0;
      for (int j = 0; j < hashIndex->pkCols->elementCount; j++) {
        pkCol = (Column *)varArrayListGetPointer(hashIndex->pkCols, j);
        switch (pkCol->fieldType) {
          case FIELDTYPE_INTEGER:
            rc = varArrayListAddPointer(pkValueArray, curPKValue + pkOffset);
            pkOffset += INT_SIZE;
            break;
          case FIELDTYPE_REAL:
            rc = varArrayListAddPointer(pkValueArray, curPKValue + pkOffset);
            pkOffset += FLOAT_SIZE;
            break;
          case FIELDTYPE_VARCHAR:
            rc = varArrayListAddPointer(pkValueArray, curPKValue + pkOffset);
            pkOffset += pkCol->columnConstraint->maxValue;
            break;
          default: return GNCDB_INVALID_FIELD_TYPE;
        }
      }
      /* 将主键值添加到结果数组 */
      rc = varArrayListAddPointer(pkValueArrays, pkValueArray);
      position++;
    }

    /* 获取下一个溢出页ID */
    nextPageId = overflowPage->nextPageId;
    setPageStatusPinDown(db->pagePool, overflowPage->page.id, NULL);
  }

  /* 11.释放资源 */
  setPageStatusPinDown(db->pagePool, metaPage->page.id, NULL);
  WriteUnLock(&(bucketPage->page.rwlock_t));
  return GNCDB_SUCCESS;
}

/**
 * @description: 计算指定桶的键值对总数
 * @param {HashIndex} *hashIndex
 * @param {BucketPage} *bucketPage
 * @return {*}
 */
int getBucketKVCnt(HashIndex *hashIndex, BucketPage *bucketPage, GNCDB *db)
{
  int               cnt          = 0;
  int               curPageId    = bucketPage->firstOverflowPageId;
  HashOverflowPage *overflowPage = NULL;

  cnt += bucketPage->keyTidPairCount;
  while (curPageId > 0) {
    int rc = pagePoolGetPage((Page **)&overflowPage, curPageId, hashIndex->tableName, db);
    if (rc != GNCDB_SUCCESS) {
      return -1;
    }
    cnt += overflowPage->keyTidPairCount;
    curPageId = overflowPage->nextPageId;
  }
  return cnt;
}

/**
 * @description: 计算HashIndex内的键值对总数
 * @param {HashIndex} *hashIndex
 * @param {GNCDB} *db
 * @return {*}
 */
int getHashIndexKVCnt(HashIndex *hashIndex, GNCDB *db)
{
  int         cnt          = 0;
  MetaPage   *metaPage     = NULL;
  BucketPage *bucketPage   = NULL;
  int         bucketPageId = 0;

  int rc = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return -1;
  }

  for (int i = 0; i < metaPage->bucketCount; i++) {
    bucketPageId = *(int *)(metaPage->page.pData + META_PAGE_HEADER_SIZE + i * INT_SIZE);
    if (bucketPageId == -1) {
      continue;
    }
    rc = pagePoolGetPage((Page **)&bucketPage, bucketPageId, hashIndex->tableName, db);
    if (rc != GNCDB_SUCCESS) {
      return -1;
    }
    cnt += getBucketKVCnt(hashIndex, bucketPage, db);
  }

  return cnt;
}

/**
 * @description: 计算一个HashIndex内的各个桶键值对数量并填入数组中
 * @param {HashIndex} *hashIndex
 * @param {int} *cntList 各个桶的键值对数量
 * @param {int} *bucketNum 桶的数量
 * @param {GNCDB} *db
 * @return {*}
 */
int getHashBucketKVList(HashIndex *hashIndex, int **cntList, int *bucketNum, GNCDB *db)
{
  int         cnt          = 0;
  MetaPage   *metaPage     = NULL;
  BucketPage *bucketPage   = NULL;
  int         bucketPageId = 0;

  int rc = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return -1;
  }
  *bucketNum = metaPage->bucketCount;
  *cntList   = my_malloc(metaPage->bucketCount * sizeof(int));

  for (int i = 0; i < metaPage->bucketCount; i++) {
    bucketPageId = *(int *)(metaPage->page.pData + META_PAGE_HEADER_SIZE + i * INT_SIZE);
    if (bucketPageId == -1) {
      continue;
    }
    rc = pagePoolGetPage((Page **)&bucketPage, bucketPageId, hashIndex->tableName, db);
    if (rc != GNCDB_SUCCESS) {
      return -1;
    }
    cnt           = getBucketKVCnt(hashIndex, bucketPage, db);
    (*cntList)[i] = cnt;
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 打印HashIndex结构体信息
 * @param hashIndex 哈希索引指针
 */
void printHashIndex(HashIndex *hashIndex)
{
  Column *pkCol = NULL;
  /* 1.参数检查 */
  if (hashIndex == NULL) {
    printf("HashIndex为NULL\n");
    return;
  }

  /* 2.打印哈希索引基本信息 */
  printf("=== HashIndex信息 ===\n");
  printf("元数据页ID: %d\n", hashIndex->meta_page_id);
  printf("表名: %s\n", hashIndex->tableName);
  printf("索引名: %s\n", hashIndex->indexName);
  printf("索引列: ");
  for (int i = 0; i < hashIndex->index_columns->elementCount; i++) {
    pkCol = (Column *)varArrayListGetPointer(hashIndex->index_columns, i);
    printf("%s ", pkCol->fieldName);
  }
  printf("\n");
  printf("主键长度: %d\n", hashIndex->primaryKeyLenth);
  printf("主键列: ");
  for (int i = 0; i < hashIndex->pkCols->elementCount; i++) {
    pkCol = (Column *)varArrayListGetPointer(hashIndex->pkCols, i);
    printf("%s ", pkCol->fieldName);
  }
  printf("\n");
  printf("===================\n");
}

/**
 * @brief 打印MetaPage信息
 * @param metaPage 元数据页指针
 */
void printMetaPage(MetaPage *metaPage)
{
  int i            = 0;
  int bucketPageId = 0;
  int offset       = META_PAGE_HEADER_SIZE;

  /* 1.参数检查 */
  if (metaPage == NULL) {
    printf("MetaPage为NULL\n");
    return;
  }

  /* 2.打印元数据页基本信息 */
  printf("=== MetaPage信息 ===\n");
  printf("页ID: %d\n", metaPage->page.id);
  printf("哈希函数ID: %d\n", metaPage->hashFunctionId);
  printf("键值对总数: %d\n", metaPage->keyTidPairCount);
  printf("装填因子: %.2f\n", metaPage->fillFactor);
  printf("高位掩码: 0x%x\n", metaPage->highMask);
  printf("低位掩码: 0x%x\n", metaPage->lowMask);
  printf("桶数量: %d\n", metaPage->bucketCount);
  printf("最大桶编号: %d\n", metaPage->maxBucketNumber);
  printf("分裂次数: %d\n", metaPage->splitCount);

  /* 3.打印桶页地址数组 */
  printf("桶页地址数组:\n");
  for (i = 0; i < metaPage->bucketCount; i++) {
    bucketPageId = *(int *)(metaPage->page.pData + offset + i * INT_SIZE);
    if (bucketPageId != -1) {
      printf("  桶%d -> 页ID: %d\n", i, bucketPageId);
    }
  }
  printf("===================\n");
}

/**
 * @brief 打印BucketPage信息
 * @param bucketPage 桶页指针
 */
void printBucketPage(HashIndex *hashIndex, BucketPage *bucketPage)
{
  int     i            = 0;
  int     offset       = BUCKET_PAGE_HEADER_SIZE;
  int     hashValue    = 0;
  int     intValue     = 0;
  double  doubleValue  = 0.0;
  char   *strValue     = NULL;
  Column *pkCol        = NULL;

  /* 1.参数检查 */
  if (bucketPage == NULL) {
    printf("BucketPage为NULL\n");
    return;
  }

  /* 2.打印桶页基本信息 */
  printf("=== BucketPage信息 ===\n");
  printf("页ID: %d\n", bucketPage->page.id);
  printf("桶ID: %d\n", bucketPage->bucketId);
  printf("键值对数量: %d\n", bucketPage->keyTidPairCount);
  printf("第一个溢出页ID: %d\n", bucketPage->firstOverflowPageId);
  printf("最后一个溢出页ID: %d\n", bucketPage->lastOverflowPageId);
  printf("主键长度: %d\n", bucketPage->primaryKeyLenth);

  /* 4.打印键值对信息 */
  printf("键值对列表:\n");
  for (i = 0; i < bucketPage->keyTidPairCount; i++) {
    /* 读取哈希值 */
    hashValue = *(int *)(bucketPage->page.pData + offset);
    printf("  [%d] 哈希值: %d, 主键值: ", i, hashValue);
    offset += INT_SIZE;
    for (int i = 0; i < hashIndex->pkCols->elementCount; i++) {
      pkCol = (Column *)varArrayListGetPointer(hashIndex->pkCols, i);
      switch (pkCol->fieldType) {
        case FIELDTYPE_INTEGER: {
          intValue = *(int *)(bucketPage->page.pData + offset);
          printf("%d ", intValue);
          offset += INT_SIZE;
          break;
        }
        case FIELDTYPE_REAL: {
          doubleValue = *(double *)(bucketPage->page.pData + offset);
          printf("%.2f ", doubleValue);
          offset += DOUBLE_SIZE;
          break;
        }
        case FIELDTYPE_VARCHAR: {
          strValue = (char *)(bucketPage->page.pData + offset);
          printf("%s ", strValue);
          offset += pkCol->columnConstraint->maxValue;
          break;
        }
        default: printf("未知类型 "); break;
      }
    }
    printf("\n");
  }
  printf("===================\n");
}

/**
 * @brief 打印HashOverflowPage信息
 * @param overflowPage 溢出页指针
 */
void printHashOverflowPage(HashIndex *hashIndex, HashOverflowPage *overflowPage)
{
  int     i            = 0;
  int     offset       = HASH_OVERFLOW_PAGE_HEADER_SIZE;
  int     hashValue    = 0;
  int     intValue     = 0;
  double  doubleValue  = 0.0;
  char   *strValue     = NULL;
  Column *pkCol        = NULL;

  /* 1.参数检查 */
  if (overflowPage == NULL) {
    printf("HashOverflowPage为NULL\n");
    return;
  }

  /* 2.打印溢出页基本信息 */
  printf("=== HashOverflowPage信息 ===\n");
  printf("页ID: %d\n", overflowPage->page.id);
  printf("桶ID: %d\n", overflowPage->bucketId);
  printf("键值对数量: %d\n", overflowPage->keyTidPairCount);
  printf("前一个溢出页ID: %d\n", overflowPage->prevPageId);
  printf("下一个溢出页ID: %d\n", overflowPage->nextPageId);
  printf("主键长度: %d\n", overflowPage->primaryKeyLenth);

  /* 4.打印键值对信息 */
  printf("键值对列表:\n");
  for (i = 0; i < overflowPage->keyTidPairCount; i++) {
    /* 读取哈希值 */
    hashValue = *(int *)(overflowPage->page.pData + offset);
    printf("  [%d] 哈希值: %d, 主键值: ", i, hashValue);
    offset += INT_SIZE;
    for (int i = 0; i < hashIndex->pkCols->elementCount; i++) {
      pkCol = (Column *)varArrayListGetPointer(hashIndex->pkCols, i);
      switch (pkCol->fieldType) {
        case FIELDTYPE_INTEGER: {
          intValue = *(int *)(overflowPage->page.pData + offset);
          printf("%d ", intValue);
          offset += INT_SIZE;
          break;
        }
        case FIELDTYPE_REAL: {
          doubleValue = *(double *)(overflowPage->page.pData + offset);
          printf("%.2f ", doubleValue);
          offset += DOUBLE_SIZE;
          break;
        }
        case FIELDTYPE_VARCHAR: {
          strValue = (char *)(overflowPage->page.pData + offset);
          printf("%s ", strValue);
          offset += pkCol->columnConstraint->maxValue;
          break;
        }
        default: printf("未知类型 "); break;
      }
    }
    printf("\n");
  }
  printf("===================\n");
}

/**
 * @brief 打印指定桶号的桶页及其溢出页信息
 * @param metaPage 元数据页指针
 * @param bucketId 桶ID
 * @param db 数据库实例
 */
int printBucketIdPage(HashIndex *hashIndex, MetaPage *metaPage, int bucketId, GNCDB *db)
{
  int               bucketPageId   = 0;
  int               overflowPageId = 0;
  BucketPage       *bucketPage     = NULL;
  HashOverflowPage *overflowPage   = NULL;
  int               recCnt         = 0;
  int               rc             = GNCDB_SUCCESS;

  /* 1.参数检查 */
  if (metaPage == NULL || db == NULL) {
    printf("参数为NULL\n");
    return -1;
  }

  /* 2.检查桶ID是否有效 */
  if (bucketId < 0 || bucketId > metaPage->bucketCount) {
    printf("无效的桶ID: %d\n", bucketId);
    return -1;
  }

  /* 3.获取桶页ID */
  if (bucketId < MAX_BUCKET_COUNT(db->pageCurrentSize)) {
    bucketPageId = *(int *)(metaPage->page.pData + META_PAGE_HEADER_SIZE + bucketId * INT_SIZE);
  } else {
    printf("桶ID超出元数据页桶页地址数组范围\n");
    return -1;
  }

  /* 4.检查桶页ID是否有效 */
  if (bucketPageId == -1) {
    printf("桶%d没有对应的桶页\n", bucketId);
    return -1;
  }

  /* 5.获取桶页 */
  rc = pagePoolGetPage((Page **)&bucketPage, bucketPageId, NULL, db);
  if (rc != GNCDB_SUCCESS) {
    printf("获取桶页失败，错误码: %d\n", rc);
    return -1;
  }

  /* 6.打印桶页信息 */
  printf("=== 桶%d信息 ===\n", bucketId);
  printBucketPage(hashIndex, bucketPage);
  recCnt += bucketPage->keyTidPairCount;

  /* 7.获取并打印溢出页链表 */
  overflowPageId = bucketPage->firstOverflowPageId;
  while (overflowPageId != -1) {
    /* 获取溢出页 */
    rc = pagePoolGetPage((Page **)&overflowPage, overflowPageId, NULL, db);
    if (rc != GNCDB_SUCCESS) {
      printf("获取溢出页失败，错误码: %d\n", rc);
      break;
    }
    recCnt += overflowPage->keyTidPairCount;

    /* 打印溢出页信息 */
    printf("=== 溢出页%d信息 ===\n", overflowPageId);
    printHashOverflowPage(hashIndex, overflowPage);

    /* 获取下一个溢出页ID */
    overflowPageId = overflowPage->nextPageId;
  }

  return recCnt;
}

/**
 * @brief 打印完整哈希索引结构（包括元数据页和所有桶）
 * @param hashIndex 哈希索引指针
 * @param db 数据库实例
 */
void printCompleteHashIndex(HashIndex *hashIndex, GNCDB *db)
{
  MetaPage *metaPage = NULL;
  int       i        = 0;
  int       recCnt   = 0;
  int       subCnt   = 0;
  int       rc       = GNCDB_SUCCESS;

  /* 1.参数检查 */
  if (hashIndex == NULL || db == NULL) {
    printf("参数为NULL\n");
    return;
  }

  /* 2.获取元数据页 */
  rc = pagePoolGetPage((Page **)&metaPage, hashIndex->meta_page_id, hashIndex->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    printf("获取元数据页失败，错误码: %d\n", rc);
    return;
  }

  /* 3.打印哈希索引基本信息 */
  printf("========== 哈希索引信息 ==========\n");
  printHashIndex(hashIndex);

  /* 4.打印元数据页信息 */
  printf("========== 元数据页信息 ==========\n");
  printMetaPage(metaPage);

  /* 5.打印所有桶信息 */
  printf("========== 桶信息 ==========\n");
  for (i = 0; i < metaPage->bucketCount; i++) {
    subCnt = printBucketIdPage(hashIndex, metaPage, i, db);
    if (subCnt < 0) {
      printf("打印桶%d信息失败，错误码: %d\n", i, subCnt);
      continue;
    }
    recCnt += subCnt;
  }
  printf("========== 总记录数: %d ==========\n", recCnt);
  if (recCnt == metaPage->keyTidPairCount) {
    printf("记录数一致\n");
  } else {
    printf("记录数不一致，元数据页记录数: %d, 实际记录数: %d\n", metaPage->keyTidPairCount, recCnt);
  }

  /* 6.释放资源 */
  // pagePoolReleasePage(db->pagePool, hashIndex->meta_page_id, false);
}