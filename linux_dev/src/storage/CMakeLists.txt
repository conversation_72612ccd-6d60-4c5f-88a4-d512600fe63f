cmake_minimum_required(VERSION 3.10)

# 设置可执行文件输出目录
set(EXECUTABLE_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/bin)
set(CMAKE_MODULE_PATH ${CMAKE_SOURCE_DIR}/build)

aux_source_directory(. GNCDB_STORAGE_SRC)
# 动态库依赖其他库模块的源文件，需要手动添加，不然会报错
# set (STORAGE_SRC ${STORAGE_SRC} 
#     ${CMAKE_SOURCE_DIR}/src/os/os.c
#     ${CMAKE_SOURCE_DIR}/src/cachemanager/pagepool.c
#     ${CMAKE_SOURCE_DIR}/query_processing/sql/parser/value.c
# )
add_library(gncdb_storage ${GNCDB_STORAGE_SRC})
