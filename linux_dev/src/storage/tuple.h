/**
 * @file tuple.h
 * <AUTHOR>
 * @brief	元组的定义及其相关操作函数的声明
 * @version 0.1
 * @date 2023-01-30
 *
 * @copyright Copyright (c) 2023
 *
 */

#ifndef _GNCDB_TUPLE_H_
#define _GNCDB_TUPLE_H_

#include<stdbool.h>
#include "btreetable.h"
#include"gncdbconstant.h"
#include"vararraylist.h"
#include"typedefine.h"
#include"field.h"
#include"catalog.h"
#include "tuple_cell.h"
#include "value.h"

/* 获取元组bitMap字节数组的大小 */ 
#define GET_BITMAP_LENGTH(columnNum) ((columnNum - 1) / 8 + 1)
struct TableSchema;
struct Catalog;
struct BtreeTable;

/* 叶子节点元组的内容 */
typedef struct Tuple {	
	varArrayList* fieldArray;		/* 字段内容 */
	BYTE* bitMap;					/* 记录元组各字段的值是否为空 */
}<PERSON><PERSON>;


/* 内部节点的数据内容 */
typedef struct InternalEntry {
	int childPageId;				/* 孩子页id */
	varArrayList* keyValueArray;	/* Tuple主键的值，用于B + 树中的索引 */
}InternalEntry;

/*叶子节点record的操作*/
int leafRecordAddIntField(BYTE* record, int* offset, int intValue, int fieldIndex);
int leafRecordAddRealField(BYTE* record, int* offset, double* doubleValue, int fieldIndex);
int leafRecordAddVarCharField(BYTE* record, int* offset, char* stringValue, int len, int fieldIndex);
int leafRecordAddBlobField(BYTE* record, int* offset, int fieldIndex);

// inline void leafRecordGetIntField(BYTE* record, int* offset, int* intValue);
// inline void leafRecordGetRealField(BYTE* record, int* offset, double* doubleValue);
// inline void leafRecordGetVarCharField(BYTE* record, int* offset, char* stringValue, int len);
int leafRecordCompareFun(BYTE* recordFirst, BYTE* recordSecond, struct TableSchema* tableSchema);

/* 叶子节点元组的操作 */
// int leafTupleSetBitMap(struct Tuple* tuple, int fieldIndex, int flag);
int leafTupleSetBitMap(BYTE* record, int fieldIndex, int flag,int colNum);
// int leafTupleGetBitMap(struct Tuple* tuple, int fieldIndex);
int leafTupleGetBitMap(BYTE* record, int fieldIndex, int colNum);



int leafRecordSetBitMap(BYTE* record, int fieldIndex, int flag);
Tuple* leafTupleConstruct(int columnNum);
int leafTupleAddField(Tuple* tuple, Field* field);
int leafTupleGetKeyValue(varArrayList* keyValueArray,BYTE* record, struct Catalog* catalog, struct TableSchema* tableSchema, char* tableName);

BYTE* leafTupleDeepCopy(BtreeTable* btreeTable, BYTE* record);
void leafTupleDestroy(void* data);
int leafTupleCompareFun(varArrayList* array, void* dataFirst, void* dataSecond);
/* 内部节点Entry的操作 */
InternalEntry* internalEntryConstruct(int childPageId);
int internalEntryAddEntry(InternalEntry* internalEntry, void* entry);
// InternalEntry* internalEntryDeepCopy(int childPageId, varArrayList* keyValueArray, char* tableName, struct Catalog* catalog);
void internalEntryDestroy(void* internalEntry);
int internalEntryCompareFun(varArrayList* array, void* dataFirst, void* dataSecond);
int internalRecordCompareFun(varArrayList* array, varArrayList* varcharLenArray,BYTE* internalRecordFirst, BYTE* internalRecordSecond);
BYTE* internalRecordDeepCopyFromLeafRecord(int childPageId,char* tableName ,struct BtreeTable* btreeTable, struct TableSchema* tableSchema, BYTE* leafRecord);
BYTE* internalRecordDeepCopy(int childPageId,struct BtreeTable* btreeTable,BYTE* internalRecord);
/* RTree */
int leafTupleGetIndexKeyValue(struct varArrayList* keyValueArray, struct Tuple* tuple, struct TableSchema* indexSchema);

int leafTupleGetValueByIndex(varArrayList* valueArray, Tuple* tuple, int index);

void printkeyValueArray(varArrayList* keyValueArray, struct TableSchema* tableSchema);
/* HashIndex */
int leafTupleGetHashIndexKeyValue(varArrayList *keyValueArray, BYTE *record, varArrayList* indexColList);
#endif /* !_GNCDB_TUPLE_H_ */
