/**
 * @file btreetable.c
 * <AUTHOR>
 * @brief  BtreeTable的相关操作实现
 * @version 0.1
 * @date 2023-02-02
 *
 * @copyright Copyright (c) 2023
 *
 */

#include "btreetable.h"
#include "btreepage.h"
#include "btreecursor.h"
#include "field.h"
#include "hashpage.h"
#include "lockmanager.h"
#include "tuple.h"
#include "typedefine.h"
#include "gncdbconstant.h"
#include <stdio.h>
#include <time.h>
#include "value.h"
#include "btreetable.h"
#include "btreepage.h"
#include "tranmanager.h"
#include "transaction.h"
#include "pagepool.h"
#include "value.h"
#include "vararraylist.h"
extern int stringToDatetime(char *str, DateTime *datetime);

/*********************************************************************************btreeTable*******************************************************************/

/**
 * @brief BtreeTable的创建
 * @param tableName 表名
 * @param rootPageId 树的根节点页号
 * @param tableSchema tableSchema用于计算InternalPage中定长记录的长度和LeafPage中定长记录的长度
 * @return 返回BtreeTable指针
 */
struct BtreeTable *btreeTableConstruct(char *tableName, int rootPageId, struct TableSchema *tableSchema)
{
  /* 1.定义变量 */
  int         tableNameLength = 0, internalRecordLength = 0, leafRecordLength = 0, index = 0;
  Column     *column     = NULL;
  BtreeTable *btreeTable = NULL;

  /* 2.参数检查 */
  if (tableName == NULL || tableSchema == NULL) {
    return NULL;
  }

  /* 3.分配BtreeTable指针内存 */
  btreeTable = (BtreeTable *)my_malloc(sizeof(BtreeTable));
  if (btreeTable == NULL) {
    return NULL;
  }
  /* 初始化锁 */
  ReadWriteLockInit(&(btreeTable->rwlock_t));
  /* 初始化根页页号 */
  btreeTable->rootPageId = rootPageId;
  btreeTable->hasBlob    = false; /* 默认没有blob类型的字段 */

  /* 4.分配表名内存 */
  tableNameLength       = strlen(tableName);
  btreeTable->tableName = (char *)my_malloc(tableNameLength + 1);
  if (btreeTable->tableName == NULL) {
    btreeTableDestroy(&btreeTable);
    return NULL;
  }
  /* 复制表名 */
  memcpy(btreeTable->tableName, tableName, tableNameLength);
  btreeTable->tableName[tableNameLength] = '\0';

  /* 5.计算定长记录的长度 */
  internalRecordLength = 0;
  leafRecordLength     = 0;
  for (index = 0; index < tableSchema->columnNum; index++) {
    column = (Column *)varArrayListGetPointer(tableSchema->columnList, index);
    if (column == NULL) {
      btreeTableDestroy(&btreeTable);
      return NULL;
    }
    switch (column->fieldType) {
      case FIELDTYPE_DATE:
      case FIELDTYPE_INTEGER:
        leafRecordLength += INT_SIZE;
        if (column->columnConstraint->isPrimaryKey) {
          internalRecordLength += INT_SIZE;
        }
        break;
      case FIELDTYPE_REAL:
        leafRecordLength += DOUBLE_SIZE;
        if (column->columnConstraint->isPrimaryKey) {
          internalRecordLength += DOUBLE_SIZE;
        }
        break;
      case FIELDTYPE_VARCHAR:
        /* 定长记录中字符串按照最大长度存放，不够最大长度的在末尾处补'\0' */
        leafRecordLength += (int)column->columnConstraint->maxValue;
        if (column->columnConstraint->isPrimaryKey) {
          internalRecordLength += (int)column->columnConstraint->maxValue;
        }
        break;
      case FIELDTYPE_BLOB:
        /* 定长记录中blob类型的数据记录第一个溢出页的页号和Blob数据内容的字节数 */
        leafRecordLength += INT_SIZE * 2;
        btreeTable->hasBlob = true;
        break;
      case FIELDTYPE_DATETIME:
        leafRecordLength += INT_SIZE * 2;
        if (column->columnConstraint->isPrimaryKey) {
          internalRecordLength += DATETIME_SIZE;
        }
        break;
      default: break;
    }
  }

  /* 6.得到定长记录长度的最终结果 */
  /* 叶子页中一条记录的长度需要加上空位图（bitMap）的长度 */
  btreeTable->leafRecordLength = leafRecordLength + GET_BITMAP_LENGTH(tableSchema->columnNum);
  /* 主键值 + 孩子页页号+ 下一个记录的偏移量*/
  btreeTable->internalRecordLength = internalRecordLength + PAGEID_SIZE;
  return btreeTable;
}

/**
 * @brief 获取btreeTable叶子页最多能存放多少个tuple
 * @param btreeTable
 * @return 返回最多存放的数量
 */
int getLeafTupleMaxCount(int pageSize, struct BtreeTable *btreeTable)
{
  /* 1.变量的定义 */
  int leafTupleMaxCount = 0;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.leafPage中最多应该存放的tuple数量 */
  leafTupleMaxCount = (pageSize - PAGE_HEAD_SIZE) / btreeTable->leafRecordLength;

  return leafTupleMaxCount;
}

/**
 * @brief 获取btreeTable叶子页最少能存放多少个tuple
 * @param btreeTable
 * @return 返回最少存放的数量
 */
int getLeafTupleMinCount(int pageSize, struct BtreeTable *btreeTable)
{
  /* 1.变量的定义 */
  int leafTupleMinCount = 0;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.leafPage中最少应该存放的tuple数量 */
  leafTupleMinCount = ((pageSize - PAGE_HEAD_SIZE) / btreeTable->leafRecordLength) / 2;

  return leafTupleMinCount;
}

/**
 * @brief 获取btreeTable内部页最多能存放多少个entry
 * @param btreeTable
 * @return 返回最多存放的数量
 */
int getInternalEntryMaxCount(int pageSize, struct BtreeTable *btreeTable)
{
  /* 1.变量的定义 */
  int internalEntryMaxCount = 0;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL) {
    return GNCDB_PARAMNULL;
  }

  /*3. internalPage中最多能够存放的entry数量 */
  internalEntryMaxCount = (pageSize - PAGE_HEAD_SIZE) / btreeTable->internalRecordLength;

  /* 4.预留一位当页满时分裂用 */
  return internalEntryMaxCount - 1;
}

/**
 * @brief 获取btreeTable内部页最少能存放多少个entry
 * @param btreeTable
 * @return 返回最少存放的数量
 */
int getInternalEntryMinCount(int pageSize, struct BtreeTable *btreeTable)
{
  /* 1.变量的定义 */
  int internalEntryMinCount = 0;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.internalPage中最少能够存放的entry数量 */
  internalEntryMinCount = ((pageSize - PAGE_HEAD_SIZE) / btreeTable->internalRecordLength) / 2;

  return internalEntryMinCount;
}

/**
 * @brief btreeTable的销毁
 * @param btreeTable
 */
void btreeTableDestroy(struct BtreeTable **btreeTable)
{
  /* 1.判断参数是否为空 */
  if (btreeTable == NULL || *btreeTable == NULL) {
    return;
  }

  /* 2.释放内存空间 */
  ReadWriteLockDestroy(&((*btreeTable)->rwlock_t));
  my_free((*btreeTable)->tableName);
  my_free(*btreeTable);
  *btreeTable = NULL;
}

/*********************************************************************************btreeTable的基本操作*******************************************************************/

/**
 * @brief 判断某个页是否为安全页
 * @param btreeTable B+树表
 * @param btreePage 主键值
 * @param op 操作类型
 * @return 返回bool
 */
bool isPageSafe(struct BtreeTable *btreeTable, struct BtreePage *btreePage, int pageSize, OperationType op)
{
  /* 1.判断是否参数是否为空 */
  if (btreePage == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2.如果是查找操作（对于查询、更新、blob数据的添加、获取、删除） */
  if (op == OP_SEARCH || op == OP_UPDATE || op == OP_BLOB_SET || op == OP_BLOB_GET || op == OP_BLOB_DELETE) {
    return true;
  }

  /* 3.如果是插入操作 */
  if (op == OP_INSERT) {
    if (btreePage->page.pageType == LEAF_PAGE) {
      return btreePage->entryNum < getLeafTupleMaxCount(pageSize, btreeTable);
    }

    return btreePage->entryNum < getInternalEntryMaxCount(pageSize, btreeTable);
  }

  /* 4.如果是删除操作 */
  if (op == OP_DELETE) {
    if (btreePage->page.id == btreeTable->rootPageId) { /* 根节点需要单独处理 */
      if (btreePage->page.pageType == LEAF_PAGE) {
        return btreePage->entryNum > 1;
      }

      return btreePage->entryNum > 2;
    }

    if (btreePage->page.pageType == LEAF_PAGE) {
      return btreePage->entryNum > getLeafTupleMinCount(pageSize, btreeTable);
    }

    return btreePage->entryNum > getInternalEntryMinCount(pageSize, btreeTable);
  }
  return GNCDB_PARAMNULL;
}

/**
 * @brief 获取页，如果latchedPageSet中已经有了，直接获取，否则再从pagePool中获取并加锁
 * @param btreeTable B+树表
 * @param db
 * @param pageId
 * @param tx
 * @return 返回加锁的页
 */
struct BtreePage *getLatchedPage(struct BtreeTable *btreeTable, struct GNCDB *db, int pageId, struct Transaction *tx)
{
  /* 1.变量的定义 */
  varArrayList *latchedPageSet = NULL;
  int           elementCount = 0, index = 0, flag = 0, rc = 0;
  BtreePage    *btreePage = NULL;

  /* 2.获取页锁集合，查找要获取的页是否已经加锁了 */
  latchedPageSet = getLatchedPageSet(tx);
  elementCount   = varArrayListGetCount(latchedPageSet);
  for (index = 0; index < elementCount; index++) {
    btreePage = (BtreePage *)varArrayListGetPointer(latchedPageSet, index);
    if (btreePage == NULL) {
      index++;
      continue;
    }
    if (btreePage->page.id == pageId) {
      flag = 1; /* 找到了该页，之前已经获取过并加过锁了 */
      break;
    }
  }

  /* 3.获取页 */
  if (!flag) {
    /* 3.1.从缓存池中获取页 */
    rc = pagePoolGetPage((Page **)&btreePage, pageId, btreeTable->tableName, db);
    if (rc != GNCDB_SUCCESS || btreePage == NULL) {
      return NULL;
    }

    /* 3.2.如果是叶子页，那么需要先加逻辑锁，再加物理锁 */
    if (btreePage->page.pageType == LEAF_PAGE) {
      LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", pageId);
      rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, btreePage->page.id, EXCLUSIVE);
      if (rc != GNCDB_SUCCESS) {
        LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", pageId);
        rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
        return NULL;
      }
      LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", pageId);
    }
    LOG(LOG_TRACE, "WLOCKing:PAGEid=%d", btreePage->page.id);
    WriteLock(&(btreePage->page.rwlock_t)); /* 加写锁 */
    LOG(LOG_TRACE, "WLOCKend:PAGEid=%d", btreePage->page.id);
    /* 如果是内部页需要先加物理写锁 再加逻辑写锁 */
    if (btreePage->page.pageType == INTERNAL_PAGE) {
      LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", pageId);
      rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, btreePage->page.id, EXCLUSIVE);
      if (rc != GNCDB_SUCCESS) {
        LOG(LOG_TRACE, "WUNLOCKing:PAGEid=%d", btreePage->page.id);
        WriteUnLock(&(btreePage->page.rwlock_t)); /* 加写锁 */
        LOG(LOG_TRACE, "WUNLOCKend:PAGEid=%d", btreePage->page.id);
        rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
        LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", pageId);
        return NULL;
      }
      LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", pageId);
    }

    rc = addIntoLatchedPageSet(tx, (void *)btreePage);
    if (rc != GNCDB_SUCCESS) {
      rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
      return NULL;
    }

    /* 3.3.备份页被修改之前的信息oldData，用于回滚 */
    rc = produceOldPageData(db, (Page *)btreePage, btreePage->page.pageType, tx);
    if (rc != GNCDB_SUCCESS) {
      rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
      return NULL;
    }

    /*rc = setPageStatusPinDown(db->pagePool, btreePage->page.id);
    if (rc != GNCDB_SUCCESS) {
        return NULL;
    }*/
  }

  return btreePage;
}

/**
 * @brief B+树在插入发生分裂的时候需要创建新页，将新创建的页添加到newCreatedPageSet做统一管理，
        因为新创建的页只需要钉住，不需要加锁，不能同latchedPageSet一起管理，防止多次备份和钉住
 * @param db
 * @param btreeTable
 * @param pageType
 * @param tx
 * @return 返回新创建的页
 */
struct BtreePage *createEmptyBtreePage(
    struct GNCDB *db, struct BtreeTable *btreeTable, PageType pageType, struct Transaction *tx)
{
  /* 1.变量的定义 */
  BtreePage *newBtreePage = NULL;
  int        rc           = 0;

  /* 2.判断参数是否为空 */
  if (db == NULL || btreeTable == NULL || tx == NULL) {
    return NULL;
  }

  /* 3.创建一个空的BtreePage */
  rc = pagePoolCreateBtreePage(&newBtreePage, pageType, btreeTable->tableName, db, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 新创建的页需要加写锁 */
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, newBtreePage->page.id, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", newBtreePage->page.id);
    return NULL;
  }
  LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", newBtreePage->page.id);

  LOG(LOG_TRACE, "WLOCKing:PAGEid=%d", newBtreePage->page.id);
  WriteLock(&newBtreePage->page.rwlock_t);
  LOG(LOG_TRACE, "WLOCKend:PAGEid=%d", newBtreePage->page.id);
  rc = addIntoLatchedPageSet(tx, (void *)newBtreePage);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }
  setPageStatusPinUp(db->pagePool, newBtreePage->page.id, NULL);

  // * 新创建的btreePage以freepage的形式加入oldPageMap中，因为相当于从空白页变成了btreePage
  /* 4.备份页被修改之前的信息oldData，用于回滚 */
  rc = produceOldPageData(db, (Page *)newBtreePage, FREE_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 5.将新创建的页添加到newCreatedPageSet中 */
  rc = addIntoNewCreatedPageSet(tx, (Page *)newBtreePage);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /*rc = setPageStatusPinDown(db->pagePool, newBtreePage->page.id);
  if (rc != GNCDB_SUCCESS) {
      return NULL;
  }*/

  return newBtreePage;
}

/**
 * @brief 释放集合中的页锁
 * @param btreeTable B+树表
 * @param pagePool 缓存池
 * @param tx
 * @param is_dirty 标记是否需要将页设置为脏页
 * @param releaseCount 释放的祖先节点的数量
 * @return 返回rc（注意LatchedPageSet集合存放的都是写锁集合，所以释放的时候直接调用WriteUnLock）
 */
int releaseWLatches(struct PagePool *pagePool, struct Transaction *tx, bool isDirty, int *releaseCont)
{
  /* 1.变量的定义 */
  varArrayList *pageSet    = NULL;
  int           size       = 0;
  int           count      = 0;
  BtreeTable   *btreeTable = NULL;
  BtreePage    *btreePage  = NULL;
  // PageStatus *pageStatus = NULL;
  int rc = 0;

  /* 2.判断参数是否为空 */
  if (pagePool == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2.获取事务的页锁集合 */
  pageSet = getLatchedPageSet(tx);

  // LOG(LOG_TRACE, "SLOCKing:%s", "pagePool");
  // WriteLock(&(pagePool->latch));
  // LOG(LOG_TRACE, "SLOCKend:%s", "pagePool");

  /* 3.依次取出page，锁是从上往下获取的，也是从上往下依次释放掉 */
  size = varArrayListGetCount(pageSet);
  while (true && size > 0) {
    /* 获取页 */
    btreePage = varArrayListGetPointer(pageSet, 0);
    if (btreePage == NULL) {
      rc = varArrayListRemoveByIndexPointer(pageSet, 0);
      if (rc != GNCDB_SUCCESS) {
        // LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
        // WriteUnLock(&(pagePool->latch));
        // LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
        return rc;
      }
      btreeTable = varArrayListGetPointer(pageSet, 0);
      count++;
    }

    /* 如果为空，说明需要释放根节点的锁 */
    if (btreePage == NULL) {
      LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
      WriteUnLock(&btreeTable->rwlock_t);
      LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
    } else {
      /* 将页锁释放掉，并取消pin */
      LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreePage->page.id);
      WriteUnLock(&btreePage->page.rwlock_t);
      LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreePage->page.id);

      setPageStatusPinDown(pagePool, btreePage->page.id, NULL);

      // pageStatus = hashMapGet(pagePool->pageStatus, &btreePage->page.id);
      // if (pageStatus == NULL) {
      // 	LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
      // 	WriteUnLock(&(pagePool->latch));
      // 	LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
      // 	return GNCDB_PARAMNULL;
      // }
      // pageStatus->pin--;
      // if(pageStatus->pin == 0){
      // 	adjustLRU_Kunpinset(pagePool, pageStatus->pageID, pageStatus);
      // }
      if (releaseCont != NULL) {
        (*releaseCont)++;
      }

      /* 如果页被修改了，需要将页设置成脏页 */
      if (isDirty) {
        setPageStatusDirty(pagePool, btreePage->page.id, NULL);
      }
    }

    /* 将页从集合中踢出 */
    rc = varArrayListRemoveByIndexPointer(pageSet, 0);
    if (rc != GNCDB_SUCCESS) {
      // LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
      // WriteUnLock(&(pagePool->latch));
      // LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");
      return rc;
    }

    /* 计数 */
    count++;
    if (count == size) {
      break;
    }
  }
  // LOG(LOG_TRACE, "SUNLOCKing:%s", "pagePool");
  // WriteUnLock(&(pagePool->latch));
  // LOG(LOG_TRACE, "SUNLOCKend:%s", "pagePool");

  return GNCDB_SUCCESS;
}

/**
 * @brief 销毁需要删除的页
 * @param pagePool 缓存池
 * @param tx
 * @return 返回rc
 */
int deletePages(struct PagePool *pagePool, struct Transaction *tx, struct GNCDB *db)
{
  /* 1.变量的定义 */
  varArrayList *deletedPageSet = NULL;
  int           rc = 0, size = 0, count = 0;
  BtreePage    *btreePage = NULL;

  /* 2.判断参数是否为空 */
  if (pagePool == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.将遍历，将页销毁 */
  deletedPageSet = getDeletedPageSet(tx);
  size           = varArrayListGetCount(deletedPageSet);
  while (size != 0) {
    /* 获取页 */
    btreePage = varArrayListGetPointer(deletedPageSet, 0);

    /* 将页销毁，即转换为FreePage */
    rc = btreePageToFreePage(btreePage, db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 将页从集合中踢出 */
    rc = varArrayListRemoveByIndexPointer(deletedPageSet, 0);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 计数 */
    count++;
    if (count == size) {
      break;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 释放newCreatedPageSet中的页pin
 * @param pagePool 缓存池
 * @param tx
 * @return 返回rc
 */
int unPinNewCreatePages(struct PagePool *pagePool, struct Transaction *tx)
{
  /* 1.变量的定义 */
  varArrayList *newCreatePageSet = NULL;
  int           count = 0, rc = 0, size = 0;
  // BtreePage* btreePage = NULL;
  int        *pageId     = 0;
  PageStatus *pageStatus = NULL;

  /* 2.判断参数是否为空 */
  if (pagePool == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.将新创建的页都unpin */
  newCreatePageSet = getNewCreatedPageSet(tx);
  size             = varArrayListGetCount(newCreatePageSet);
  while (true) {
    /* 获取页 */
    if (size == 0) {
      break;
    }
    count++;
    pageId = varArrayListGet(newCreatePageSet, 0);
    if (pageId != NULL) {
      pageStatus = getPageStatus(pagePool, *pageId);
      if (pageStatus == NULL) {
        return GNCDB_PARAMNULL;
      }
      /* unpin该页 */
      rc = setPageStatusPinDown(pagePool, *pageId, pageStatus);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }

      /* 如果页被修改了，需要将页设置成脏页 */
      rc = setPageStatusDirty(pagePool, *pageId, pageStatus);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }

    /* 将页从集合中踢出 */
    rc = varArrayListRemoveByIndexPointer(newCreatePageSet, 0);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 计数 */

    if (count == size) {
      break;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief
 * 在B+树中找到keyValueArray应该属于的哪个叶子页（如果keyValueArray为NULL，则直接找到B+树最左边的叶子页）针对操作为OP_SEARCH和OP_BLOB_GET，需要手动释放获取到的目标页
 * @param btreeTable B+树表
 * @param keyValueArray 主键值
 * @param tableSchema 表模式
 * @param db 数据库
 * @param tx 事务
 * @param op 操作类型
 * @param first_pass 标记第几次获取
 * @param depth 从根节点开始进入到的b+树的深度，用于从latchedPageSet获取父页
 * @return 返回找到的叶子页
 */
BtreePage *btreeTableFindTupleInLeafPage(struct BtreeTable *btreeTable, struct varArrayList *keyValueArray,
    struct TableSchema *tableSchema, struct GNCDB *db, struct Transaction *tx, OperationType op, bool first_pass,
    int *depth)
{
  /* 1.变量的定义 */
  BtreePage *btreePage = NULL, *preBtreePage = NULL; /* 当前页、当前页的父页 */
  int        prePageId = -1;                         /* 当前页的父页的页号 */
  int        rc        = 0;
  // InternalEntry* internalEntry = NULL;
  BYTE *internalRecord = NULL;
  int   pageId         = btreeTable->rootPageId;
  int   releaseCount   = 0; /*释放的祖先节点的数量*/

  /* 2.检查输入参数是否为空 */
  if (btreeTable == NULL || tableSchema == NULL || db == NULL || tx == NULL) {
    return NULL;
  }

  /* 3.判断是不是第一次调用，第一次使用的乐观锁的方式，第二使用的是悲观锁的方式 */
  if (!first_pass) {
    LOG(LOG_TRACE, "WLOCKing:TABLENAME=%s", btreeTable->tableName);
    WriteLock(&btreeTable->rwlock_t); /* 第二次调用要将btreeTable加上写锁，因为会涉及到根页页号的修改 */
    LOG(LOG_TRACE, "WLOCKend:TABLENAME=%s", btreeTable->tableName);
    addIntoLatchedPageSet(tx, NULL);
    rc = addIntoLatchedPageSet(tx, (void *)btreeTable);
    if (rc != GNCDB_SUCCESS) {
      return NULL;
    }
  }

  /* 4.从根页开始遍历B+ 树，直到找到叶子页 */
  while (true) {
    /* 4.1.获取页，从根节点开始获取 */
    releaseCount = 0;
    rc           = pagePoolGetPage((Page **)&btreePage, pageId, btreeTable->tableName, db);
    if (btreePage == NULL) {
      return NULL;
    }

    // get preBtreePage
    if (prePageId != -1) {
      rc = pagePoolGetPage((Page **)&preBtreePage, prePageId, btreeTable->tableName, db);
      if (preBtreePage == NULL) {
        return NULL;
      }
    }
    /* 4.2.判断是不是叶子页，如果是叶子页的话，说明已经找到了对应的页（这里需要分两种情况） */
    if (first_pass) { /* 第一种情况：如果是第一次调用，说明是使用的乐观锁的模式 */
      /* 4.2.1.乐观锁模式下，只有是叶子页且不是查找操作是才会加写锁 */
      if (btreePage->page.pageType == LEAF_PAGE && op != OP_SEARCH && op != OP_BLOB_GET) {
        LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", pageId);
        rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, pageId, SHARD);
        if (rc != GNCDB_SUCCESS) {
          LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", pageId);
          if (preBtreePage != NULL) {
            LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", preBtreePage->page.id);
            ReadUnLock(&preBtreePage->page.rwlock_t);
            LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", preBtreePage->page.id);
          } else {
            LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
            ReadUnLock(&btreeTable->rwlock_t);
            LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
          }
          return NULL;
        }
        LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", pageId);
        /* todo ? 如果是叶子页，需要先加逻辑锁，根据OperationType加不同类型的锁，再加物理锁 */
        if (!isPageSafe(btreeTable, btreePage, db->pageCurrentSize, op)) { /* 第一次获取且不是安全页 */
          rc = lockManagerReleaseLock(db->transactionManager->lockManager, tx, pageId, SHARD);
          if (rc != GNCDB_SUCCESS) {
            LOG(LOG_TRACE, "AcquireLockUNLOCKfail:PAGEid=%d", pageId);
          } else {
            LOG(LOG_TRACE, "AcquireLockUNLOCKsuccess:PAGEid=%d", pageId);
          }
          /* todo ? 如果不是安全节点，需要先释放物理锁，然后再释放逻辑锁 */
          if (preBtreePage != NULL) { /* 如果preBtreePage不为空的话，那么直接释放读锁并取消Pin */
                                      // @todo 先释放逻辑读锁
            /* 内部页先解逻辑读锁，再解物理读锁 */
            if (!transactionExistPage(tx, preBtreePage->page.id)) {
              LOG(LOG_TRACE, "AcquireLockUNLOCK:PAGEid=%d", preBtreePage->page.id);
              rc = lockManagerReleaseLock(db->transactionManager->lockManager, tx, preBtreePage->page.id, UNKNOW);
              if (rc != GNCDB_SUCCESS) {
                LOG(LOG_TRACE, "AcquireLockUNLOCKfail:PAGEid=%d", preBtreePage->page.id);
              } else {
                LOG(LOG_TRACE, "AcquireLockUNLOCKsuccess:PAGEid=%d", preBtreePage->page.id);
              }
            }
            LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", preBtreePage->page.id);
            ReadUnLock(&preBtreePage->page.rwlock_t);
            LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", preBtreePage->page.id);

            rc = setPageStatusPinDown(db->pagePool, preBtreePage->page.id, NULL);
            if (rc != GNCDB_SUCCESS) {
              return NULL;
            }
            rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
            if (rc != GNCDB_SUCCESS) {
              return NULL;
            }
          } else { /* 如果preBtreePage为空的话，意味着对btreeTable加锁了，相关信息不可以被修改，这里需要释放btreeTable的锁
                    */
            LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
            ReadUnLock(&btreeTable->rwlock_t);
            LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
            rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
            if (rc != GNCDB_SUCCESS) {
              return NULL;
            }
          }
          if (depth != NULL) {
            (*depth) = 0;
          }
          return btreeTableFindTupleInLeafPage(
              btreeTable, keyValueArray, tableSchema, db, tx, op, false, depth); /* 第二次获取锁 */
        } else {
          LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", pageId);
          rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, pageId, EXCLUSIVE);
          if (rc != GNCDB_SUCCESS) {
            LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", pageId);
            if (preBtreePage != NULL) {
              LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", preBtreePage->page.id);
              ReadUnLock(&preBtreePage->page.rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", preBtreePage->page.id);
            } else {
              LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
              ReadUnLock(&btreeTable->rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
            }
            return NULL;
          }
          LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", pageId);
          LOG(LOG_TRACE, "WLOCKing:PAGEid=%d", btreePage->page.id);
          WriteLock(&btreePage->page.rwlock_t);
          LOG(LOG_TRACE, "WLOCKend:PAGEid=%d", btreePage->page.id);
          rc = addIntoLatchedPageSet(tx, (void *)btreePage);
          if (rc != GNCDB_SUCCESS) {
            return NULL;
          }
        }
      } else { /* 直接获取读锁 */
        if (btreePage->page.pageType == LEAF_PAGE) {
          LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", pageId);
          rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, pageId, SHARD);
          if (rc != GNCDB_SUCCESS) {
            LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", pageId);
            if (preBtreePage != NULL) {
              LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", preBtreePage->page.id);
              ReadUnLock(&preBtreePage->page.rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", preBtreePage->page.id);
            } else {
              LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
              ReadUnLock(&btreeTable->rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
            }
            return NULL;
          }
          LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", pageId);

          if (!isPageSafe(btreeTable, btreePage, db->pageCurrentSize, op)) { /* 第一次获取且不是安全页 */
            LOG(LOG_TRACE, "AcquireLockUNLOCK:PAGEid=%d", pageId);
            rc = lockManagerReleaseLock(db->transactionManager->lockManager, tx, pageId, SHARD);
            if (rc != GNCDB_SUCCESS) {
              LOG(LOG_TRACE, "AcquireLockUNLOCKfail:PAGEid=%d", pageId);
            } else {
              LOG(LOG_TRACE, "AcquireLockUNLOCKsuccess:PAGEid=%d", pageId);
            }
            /* todo ? 如果不是安全节点，需要先释放物理锁，然后再释放逻辑锁 */
            if (preBtreePage != NULL) { /* 如果preBtreePage不为空的话，那么直接释放读锁并取消Pin */
              /* 内部页先解逻辑读锁，再解物理读锁 */
              LOG(LOG_TRACE, "AcquireLockUNLOCK:PAGEid=%d", preBtreePage->page.id);
              rc = lockManagerReleaseLock(db->transactionManager->lockManager, tx, preBtreePage->page.id, UNKNOW);
              if (rc != GNCDB_SUCCESS) {
                LOG(LOG_TRACE, "AcquireLockUNLOCKfail:PAGEid=%d", preBtreePage->page.id);
              } else {
                LOG(LOG_TRACE, "AcquireLockUNLOCKsuccess:PAGEid=%d", preBtreePage->page.id);
              }

              LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", preBtreePage->page.id);
              ReadUnLock(&preBtreePage->page.rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", preBtreePage->page.id);

              rc = setPageStatusPinDown(db->pagePool, preBtreePage->page.id, NULL);
              if (rc != GNCDB_SUCCESS) {
                return NULL;
              }
              rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
              if (rc != GNCDB_SUCCESS) {
                return NULL;
              }
            } else { /* 如果preBtreePage为空的话，意味着对btreeTable加锁了，相关信息不可以被修改，这里需要释放btreeTable的锁
                      */
              LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
              ReadUnLock(&btreeTable->rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
              rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
              if (rc != GNCDB_SUCCESS) {
                return NULL;
              }
            }
            if (depth != NULL) {
              (*depth) = 0;
            }

            return btreeTableFindTupleInLeafPage(
                btreeTable, keyValueArray, tableSchema, db, tx, op, false, depth); /* 第二次获取锁 */
          }
          LOG(LOG_TRACE, "RLOCKing:PAGEid=%d", btreePage->page.id);
          ReadLock(&btreePage->page.rwlock_t);
          LOG(LOG_TRACE, "RLOCKend:PAGEid=%d", btreePage->page.id);
        }
        /* 如果是内部页先上物理读锁，再上逻辑读锁 */
        else if (btreePage->page.pageType == INTERNAL_PAGE) {
          LOG(LOG_TRACE, "RLOCKing:PAGEid=%d", btreePage->page.id);
          ReadLock(&btreePage->page.rwlock_t);
          LOG(LOG_TRACE, "RLOCKend:PAGEid=%d", btreePage->page.id);

          LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", pageId);
          rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, pageId, SHARD);
          if (rc != GNCDB_SUCCESS) {
            LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", pageId);
            LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreePage->page.id);
            ReadUnLock(&btreePage->page.rwlock_t);
            LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreePage->page.id);
            if (preBtreePage != NULL) {
              LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", preBtreePage->page.id);
              ReadUnLock(&preBtreePage->page.rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", preBtreePage->page.id);
              rc = setPageStatusPinDown(db->pagePool, preBtreePage->page.id, NULL);
              if (rc != GNCDB_SUCCESS) {
                return NULL;
              }
            } else {
              LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
              ReadUnLock(&btreeTable->rwlock_t);
              LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
            }
            rc = setPageStatusPinDown(db->pagePool, pageId, NULL);
            if (rc != GNCDB_SUCCESS) {
              return NULL;
            }
            return NULL;
          }
          LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", pageId);
        }
      }

      /* 4.2.2.乐观锁模式下需要释放祖先的锁 */
      if (preBtreePage != NULL) { /* 如果preBtreePage不为空的话，那么直接释放读锁并取消Pin */
        /* 内部页先解逻辑读锁，再解物理读锁 */
        if (!transactionExistPage(tx, preBtreePage->page.id)) {
          LOG(LOG_TRACE, "AcquireLockUNLOCK:PAGEid=%d", preBtreePage->page.id);
          rc = lockManagerReleaseLock(db->transactionManager->lockManager, tx, preBtreePage->page.id, UNKNOW);
          if (rc != GNCDB_SUCCESS) {
            LOG(LOG_TRACE, "AcquireLockUNLOCKfail:PAGEid=%d", preBtreePage->page.id);
          } else {
            LOG(LOG_TRACE, "AcquireLockUNLOCKsuccess:PAGEid=%d", preBtreePage->page.id);
          }
        }
        LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", preBtreePage->page.id);
        ReadUnLock(&preBtreePage->page.rwlock_t);
        LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", preBtreePage->page.id);

        rc = setPageStatusPinDown(db->pagePool, preBtreePage->page.id, NULL);
        if (rc != GNCDB_SUCCESS) {
          return NULL;
        }
      } else { /* 如果preBtreePage为空的话，意味着对btreeTable加锁了，相关信息不可以被修改，这里需要释放btreeTable的锁
                */
        LOG(LOG_TRACE, "RWUNLOCKing:TABLENAME=%s", btreeTable->tableName);
        ReadUnLock(&btreeTable->rwlock_t);
        LOG(LOG_TRACE, "RWUNLOCKend:TABLENAME=%s", btreeTable->tableName);
      }
    } else { /* 第二种情况：如果是第二种调用，使用悲观锁模式 */
      /* 4.2.1.只有插入/删除操作才会进入悲观锁模式 */
      if (op == OP_SEARCH || op == OP_UPDATE || op == OP_BLOB_SET || op == OP_BLOB_GET || op == OP_BLOB_DELETE) {
        return NULL;
      }

      /* 4.2.2.直接从根节点重新开始加锁，会涉及到根页页号的修改 */
      if (btreePage->page.pageType == LEAF_PAGE) {
        LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", pageId);
        rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, pageId, EXCLUSIVE);
        if (rc != GNCDB_SUCCESS) {
          LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", pageId);
          rc = setPageStatusPinDown(db->pagePool, pageId, NULL);
          if (rc != GNCDB_SUCCESS) {
            return NULL;
          }
          return NULL;
        }
        LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", pageId);
      }
      LOG(LOG_TRACE, "WLOCKing:PAGEid=%d", btreePage->page.id);
      WriteLock(&btreePage->page.rwlock_t);
      LOG(LOG_TRACE, "WLOCKend:PAGEid=%d", btreePage->page.id);

      /* 4.2.3.需要判断当前节点是否为安全节点，要先释放祖先节点，然后再将当前节点添加到锁集合中 */
      if (isPageSafe(btreeTable, btreePage, db->pageCurrentSize, op)) {
        rc = releaseWLatches(db->pagePool, tx, false, &releaseCount);
        if (depth != NULL) {
          for (int i = 0; i < releaseCount; i++) {
            (*depth)--;
          }
        }
        if (rc != GNCDB_SUCCESS) {
          return NULL;
        }
      }
      //            else
      //            {
      //                if (btreePage->parentPageId > 0) {
      //                    LOG(LOG_TRACE, "AcquireLockLOCK:PAGEid=%d", btreePage->parentPageId);
      //                    rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx,
      //                    btreePage->parentPageId, EXCLUSIVE); if (rc != GNCDB_SUCCESS) {
      //                        LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", btreePage->parentPageId);
      //                        return NULL;
      //                    }
      //                    LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", btreePage->parentPageId);
      //                }
      //            }

      /* 4.2.4.将页锁添加到集合中 */
      rc = addIntoLatchedPageSet(tx, (void *)btreePage);
      if (rc != GNCDB_SUCCESS) {
        return NULL;
      }
    }

    /* 4.3.判断是不是叶子页，如果是叶子页，需要判断是否安全，不安全则需要重新获取一次 */
    if (btreePage->page.pageType == LEAF_PAGE) {
      return btreePage;
    }

    /* 4.4.如果是内部页，需要根据keyValueArray判断获取哪一个孩子页，更新pageId、preBtreePage */
    if (keyValueArray == NULL) { /* 如果keyValueArray为NULL，则直接找到B+树最左边的叶子节点 */
      internalRecord = btreePage->page.pData + PAGE_HEAD_SIZE;
      memcpy(&pageId, internalRecord, sizeof(int));
    } else {
      /* 在内部节点中找到第一个大于等于keyValueArray值的internalEntry */
      internalRecord = internalPageFindEntryByKeyvalue(
          btreePage, keyValueArray, tableSchema, btreeTable->tableName, db->catalog, btreeTable, GREATER_THAN, NULL);
      if (internalRecord == NULL) {
        pageId = btreePage->nextPageId;
        if (depth != NULL) {
          (*depth)++;
        }
      } else {

        memcpy(&pageId, internalRecord, sizeof(int));
        if (depth != NULL) {
          (*depth)++;
        }
      }
    }

    // preBtreePage = btreePage; // 不可以这样赋值，因为btreePage有可能会被替换出去
    prePageId = btreePage->page.id;
    setPageStatusPinDown(db->pagePool, prePageId, NULL);
    preBtreePage = NULL;
  }
}

/**
 * @brief 用于做page修改之前的处理：保存页修改之前的数据用于事务回滚
 * @param catalog
 * @param page
 * @param pageTypeOfDeepCopy 深拷贝的页类型
 * @param tx 事务
 * @return 返回状态码
 */
int produceOldPageData(struct GNCDB *db, Page *page, PageType pageTypeOfDeepCopy, struct Transaction *tx)
{
  /* 1.变量的定义 */
  BtreePage        *btreePage            = NULL;
  BtreePage        *btreePageCopy        = NULL;
  FreePage         *freePage             = NULL;
  FreePage         *freePageCopy         = NULL;
  OverflowPage     *overflowPage         = NULL;
  OverflowPage     *overflowPageCopy     = NULL;
  MetaPage         *metaPage             = NULL;
  MetaPage         *metaPageCopy         = NULL;
  BucketPage       *bucketPage           = NULL;
  BucketPage       *bucketPageCopy       = NULL;
  HashOverflowPage *hashOverflowPage     = NULL;
  HashOverflowPage *hashOverflowPageCopy = NULL;
  PageType         *pageType             = (PageType *)page;
  int               pageId               = 0;
  int               rc                   = 0;

  /* 2.判断参数是否为空 */
  if (db == NULL || page == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.根据pageType来判断page需要转化成什么类型的页 */
  switch (*pageType) {
    case INTERNAL_PAGE:
    case LEAF_PAGE: {
      btreePage = (BtreePage *)page;
      pageId    = btreePage->page.id;
      break;
    }
    case OVERFLOW_PAGE: {
      overflowPage = (OverflowPage *)page;
      pageId       = overflowPage->page.id;
      break;
    }
    case FREE_PAGE: {
      freePage = (FreePage *)page;
      pageId   = freePage->page.id;
      break;
    }
    case META_PAGE: {
      metaPage = (MetaPage *)page;
      pageId   = metaPage->page.id;
      break;
    }
    case BUCKET_PAGE: {
      bucketPage = (BucketPage *)page;
      pageId     = bucketPage->page.id;
      break;
    }
    case HASH_OVERFLOW_PAGE: {
      hashOverflowPage = (HashOverflowPage *)page;
      pageId           = hashOverflowPage->page.id;
      break;
    }

    default: break;
  }
  /* 只备份最老页 */
  if (!transactionExsitOldData(db, tx, pageId)) {
    return GNCDB_SUCCESS;
  }
  /* 4.记录页在被修改之前的内容，用于回滚（深拷贝） */
  if (pageTypeOfDeepCopy == INTERNAL_PAGE || pageTypeOfDeepCopy == LEAF_PAGE) {
    /* 深拷贝 */
    btreePageCopy = btreePageDeepCopy(db, btreePage, db->catalog);
    if (btreePageCopy == NULL) {
      return GNCDB_DEEPCOPY_FAIL;
    }

    /* 添加到事务中 */
    rc = transactionAddOldData(db, tx, btreePageCopy->page.id, btreePageCopy);
    if (rc != GNCDB_SUCCESS) {
      btreePageDestroyMalloc(&btreePageCopy);
      return rc;
    }
  } else if (pageTypeOfDeepCopy == OVERFLOW_PAGE) {
    /* 深拷贝 */
    overflowPageCopy = overflowPageDeepCopy(db, overflowPage);
    if (overflowPageCopy == NULL) {
      return GNCDB_DEEPCOPY_FAIL;
    }

    /* 添加到事务中 */
    rc = transactionAddOldData(db, tx, overflowPageCopy->page.id, overflowPageCopy);
    if (rc != GNCDB_SUCCESS) {
      overflowPageDestroyMalloc(&overflowPageCopy);
      return rc;
    }
  } else if (pageTypeOfDeepCopy == FREE_PAGE) {
    /* 深拷贝 */
    freePageCopy = freePageDeepCopy(db, pageId, 0);
    if (freePageCopy == NULL) {
      return GNCDB_DEEPCOPY_FAIL;
    }

    /* 添加到事务中 */
    rc = transactionAddOldData(db, tx, freePageCopy->page.id, freePageCopy);
    if (rc != GNCDB_SUCCESS) {
      freePageDestroyMalloc(&freePageCopy);
      return rc;
    }
  } else if (pageTypeOfDeepCopy == META_PAGE) {
    /* 深拷贝 */
    metaPageCopy = MetaPageDeepCopy(db, metaPage);
    if (metaPageCopy == NULL) {
      return GNCDB_DEEPCOPY_FAIL;
    }

    /* 添加到事务中 */
    rc = transactionAddOldData(db, tx, metaPageCopy->page.id, metaPageCopy);
    if (rc != GNCDB_SUCCESS) {
      MetaPageDestroyMalloc(&metaPageCopy);
      return rc;
    }
  } else if (pageTypeOfDeepCopy == BUCKET_PAGE) {
    /* 深拷贝 */
    bucketPageCopy = BucketPageDeepCopy(db, bucketPage);
    if (bucketPageCopy == NULL) {
      return GNCDB_DEEPCOPY_FAIL;
    }

    /* 添加到事务中 */
    rc = transactionAddOldData(db, tx, bucketPageCopy->page.id, bucketPageCopy);
    if (rc != GNCDB_SUCCESS) {
      BucketPageDestroyMalloc(&bucketPageCopy);
      return rc;
    }
  } else if (pageTypeOfDeepCopy == HASH_OVERFLOW_PAGE) {
    /* 深拷贝 */
    hashOverflowPageCopy = HashOverflowPageDeepCopy(db, hashOverflowPage);
    if (hashOverflowPageCopy == NULL) {
      return GNCDB_DEEPCOPY_FAIL;
    }

    /* 添加到事务中 */
    rc = transactionAddOldData(db, tx, hashOverflowPageCopy->page.id, hashOverflowPageCopy);
    if (rc != GNCDB_SUCCESS) {
      HashOverflowPageDestroyMalloc(&hashOverflowPageCopy);
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 统一备份latchedPageSet，避免后续的重复备份和漏备份
 * @param catalog
 * @param tx 事务
 * @return 返回状态码
 */
int backUpLatchedPage(struct GNCDB *db, struct Transaction *tx)
{
  /* 1.变量的定义 */
  varArrayList *latchedPageSet = NULL;
  int           elementCount = 0, index = 0, rc = 0;
  BtreePage    *btreePage = NULL;

  /* 2.判断参数是否为空 */
  if (db == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取页锁集合，查找要获取的页是否已经加锁了 */
  latchedPageSet = getLatchedPageSet(tx);
  elementCount   = varArrayListGetCount(latchedPageSet);
  for (index = 0; index < elementCount; index++) {
    btreePage = varArrayListGetPointer(latchedPageSet, index);
    if (btreePage == NULL) {
      /* 如果latchedPageSetarray中保存一个NULL，则下一个数据则为表名，需要跳过 */
      index++;
      continue;
    }
    rc = produceOldPageData(db, (Page *)btreePage, btreePage->page.pageType, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 根据页在B+树中的depth获取对应的父页
 * @param parentPageId 需要查找的parentPageId
 * @param tx 事务
 * @param depth 叶子页在LatchedPageSet中的深度
 * @return 返回状态码
 */
int getParentPageIdfromLatchedPage(int *parentPageId, struct Transaction *tx, int depth)
{
  /* 1.变量的定义 */
  varArrayList *latchedPageSet = NULL;
  int           elementCount = 0, index = 0;
  BtreePage    *btreePage = NULL;

  /* 2.如果depth为0，说明叶子页就是根页，此时B+树只有一层 */
  if (depth == 0) {
    *parentPageId = 0;
    return GNCDB_SUCCESS;
  }

  /* 3.获取页锁集合，使用depth查找对应的父页 */
  latchedPageSet = getLatchedPageSet(tx);
  elementCount   = varArrayListGetCount(latchedPageSet);
  for (index = 0; index < elementCount; index++) {
    btreePage = (BtreePage *)varArrayListGetPointer(latchedPageSet, index);
    if (btreePage == NULL) {
      index++;
      continue;
    } else {
      // printf("index:%d,pageid:%d\n",index,btreePage->page.id);
      depth--;
    }
    if (depth == 0) {
      *parentPageId = btreePage->page.id;
      // printf("\n");
      return GNCDB_SUCCESS;
    }
  }
  return GNCDB_PARENTPAGEID_NOT_EXIST;
}

int btreeTableGetRowNum(char *tableName, struct GNCDB *db, int *rowNum)
{
  /* 1.变量的定义 */
  int          rc          = 0;
  BtreePage   *nextPage    = NULL;
  int          nextPageId  = 0;
  Transaction *tx          = NULL;
  BtreeCursor *btreeCursor = NULL;

  /* 2.判断输入参数是否为空 */
  if (tableName == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }
  tx          = transcationConstrcut(db);
  btreeCursor = btreeCursorConstruct(tableName, db, NULL, tx);
  if (btreeCursor == NULL) {
    btreeCursorDestroy0(&btreeCursor, false);
    transactionRollback(tx, db);
    return GNCDB_BTC_CREATE_FALSE;
  }
  while (btreeTableHasNextTuple(btreeCursor)) {
    if (btreeCursor->currentTupleIndex < btreeCursor->page->entryNum) {

      btreeCursor->currentTupleIndex++;
    } else if (btreeCursor->currentTupleIndex >= btreeCursor->page->entryNum && btreeCursor->page->nextPageId > 0) {
      /* 该叶子页已经遍历完,获取下一个叶子页 */
      nextPageId = btreeCursor->page->nextPageId;
      rc         = pagePoolGetPage((Page **)&nextPage, nextPageId, btreeCursor->page->tableName, db);
      if (rc != GNCDB_SUCCESS) {
        btreeCursorDestroy0(&btreeCursor, false);
        transactionRollback(tx, db);
        return rc;
      }
      /* todo 可能发生死锁,添加逻辑锁 */
      /* 获取下一个页的读锁并pin住该页 */
      /* 解除当前页的pin和锁 */
      rc = setPageStatusPinDown(db->pagePool, btreeCursor->currentLeafPageId, NULL);
      if (rc != GNCDB_SUCCESS) {
        btreeCursorDestroy0(&btreeCursor, false);
        transactionRollback(tx, db);
        return rc;
      }

      btreeCursor->currentLeafPageId = nextPageId;
      btreeCursor->page              = nextPage;
      btreeCursor->currentTupleIndex = 0;
      btreeCursor->currentTupleIndex++;
    } else

    {
      btreeCursorDestroy0(&btreeCursor, false);
      transactionRollback(tx, db);
      return rc;
    }
    (*rowNum)++;
  }
  btreeCursorDestroy0(&btreeCursor, false);
  transactionCommit(tx, db);
  return GNCDB_SUCCESS;
}

/*********************************************************************************btreeTable_Insert*******************************************************************/

/**
 * @brief B+树中插入tuple对上层的接口，同时判断插入的元组是否存在主键重复的情况出现
 * @param btreeTable B+树表
 * @param tuple
 * @param tableSchema 表模式
 * @param db 数据库
 * @param tx 事务
 * @return 返回处理之后的状态码
 */
int btreeTableInsertTuple(struct BtreeTable *btreeTable, BYTE *record, struct TableSchema *tableSchema,
    struct GNCDB *db, struct Transaction *tx)
{
  /* 1.变量定义 */
  int           rc            = 0;
  int           depth         = 0;
  varArrayList *keyValueArray = NULL;
  BtreePage    *btreePage     = NULL;
  // Tuple* sameTuple = NULL;
  BYTE *sameRecord = NULL;

  /* 2.判断输入参数是否为空 */
  if (btreeTable == NULL || record == NULL || tableSchema == NULL || db == NULL || tx == NULL) {
    printf("错误发生在这个地方\n");
    return GNCDB_PARAMNULL;
  }

  /* 3.在B+树中查询tuple存放在哪个leafPage中 */
  keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, NULL);
  if (keyValueArray == NULL) {
    // leafTupleDestroy(&tuple);
    return GNCDB_ARRAY_CREATE_FALSE;
  }

  /* 4.获取tuple的主键值 */
  rc = leafTupleGetKeyValue(keyValueArray, record, db->catalog, tableSchema, btreeTable->tableName);

  if (rc != GNCDB_SUCCESS) {
    // leafTupleDestroy(&tuple);
    varArrayListDestroy(&keyValueArray);
    return rc;
  }

  /* 5.查找应该插入到B+树的哪个叶子页中 */
  /* 先获取btreeTable的读锁 */
  LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", btreeTable->tableName);
  ReadLock(&btreeTable->rwlock_t);
  LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", btreeTable->tableName);

  /* 再调用查找叶子页的函数 */
  btreePage = btreeTableFindTupleInLeafPage(btreeTable, keyValueArray, tableSchema, db, tx, OP_INSERT, true, &depth);
  if (btreePage == NULL) {
    // leafTupleDestroy(&tuple);
    varArrayListDestroy(&keyValueArray);
    return GNCDB_LEAFPAGE_NOT_FOUND;
  }
  // clock_gettime(CLOCK_MONOTONIC, &start);
  rc = lockManagerInternalPageLock(db->transactionManager->lockManager, tx);
  if (rc != GNCDB_SUCCESS) {
    // leafTupleDestroy(&tuple);
    varArrayListDestroy(&keyValueArray);
    return rc;
  }
  /* 6.判断是否有相同主键值的tuple，如果有则，直接拒绝插入 */
  sameRecord = leafPageFindEntryByKeyvalue(
      btreePage, keyValueArray, tableSchema, btreeTable->tableName, db->catalog, EQUAL, btreeTable);
  if (sameRecord != NULL) {
    varArrayListDestroy(&keyValueArray);
    // rc = releaseWLatches(db->pagePool, tx, true);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = unPinNewCreatePages(db->pagePool, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    return GNCDB_DUPLICATE_PRIMARY_KEY;
  }
  varArrayListDestroy(&keyValueArray); /* 使用完销毁 */

  /* 7.对latchedPageSet做统一备份，防止出现重复备份 */
  // clock_gettime(CLOCK_MONOTONIC, &start);
  rc = backUpLatchedPage(db, tx);
  if (rc != GNCDB_SUCCESS) {
    // leafTupleDestroy(&tuple);
    return rc;
  }

  /* 8.调用元组的插入接口 */
  rc = insertTuple(btreeTable, btreePage, record, tableSchema, db, tx, depth);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 9.做后续的处理工作，释放所有的锁并unpin */
  rc = releaseWLatches(db->pagePool, tx, true, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  // clock_gettime(CLOCK_MONOTONIC, &start);
  rc = unPinNewCreatePages(db->pagePool, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* todo ? 考虑逻辑锁的释放 */

  return GNCDB_SUCCESS;
}

/**
 * @brief B+树插入tuple的具体操作
 * @param btreeTable
 * @param tuple 需要插入的元组
 * @param tableSchema
 * @param db
 * @param tid 事务id
 * @param depth 叶子页所在层数
 */
int insertTuple(struct BtreeTable *btreeTable, struct BtreePage *btreePage, BYTE *leafRecord,
    struct TableSchema *tableSchema, struct GNCDB *db, struct Transaction *tx, int depth)
{
  /* 1.变量的定义 */
  int rc = 0;
  // varArrayList* keyValueArray = NULL;
  int        leafTupleMaxCount   = 0;
  BtreePage *newBtreePage        = NULL;
  int        index               = 0;
  int        prevInsertPageIndex = 0;
  BYTE      *internalRecord      = NULL;
  BYTE      *firstRecord         = NULL;
  BYTE      *prevInsertRecord    = NULL;  // 上一次插入的元组
  BYTE      *newPageFirstRecord  = NULL;  // 新页的第一个元组

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || btreePage == NULL || leafRecord == NULL || tableSchema == NULL || db == NULL ||
      tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.leafPage中最大能够存放的tuple数量 */
  leafTupleMaxCount = getLeafTupleMaxCount(db->pageCurrentSize, btreeTable);
  if (btreePage->entryNum < leafTupleMaxCount) { /* 如果leafPage能放下一个tuple */
    rc = leafPageInsertTuple(btreePage, leafRecord, tableSchema, btreeTable);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } else { /* 如果leafPage放不下，则需要进行页的拆分 */
    /* 4.1.创建一个LeafPage，初始化信息，备份并添加到newCreatedPageSet */
    newBtreePage = createEmptyBtreePage(db, btreeTable, LEAF_PAGE, tx);
    if (newBtreePage == NULL) {
      return GNCDB_BTG_CREATE_FALSE;
    }

    /*4.2.拆分*/
    /*根据是否是增序插入导致的分裂判断分裂方式*/
    prevInsertPageIndex = btreePage->prevInsertIndex;
    prevInsertRecord    = btreePage->page.pData + PAGE_HEAD_SIZE + prevInsertPageIndex * btreeTable->leafRecordLength;
    if (btreePage->nextPageId == 0 && prevInsertPageIndex == btreePage->entryNum - 1 &&
        leafRecordCompareFun(prevInsertRecord, leafRecord, tableSchema) < 0) {
      index = btreePage->entryNum; /* 从插入点处开始拆分 */
    } else {
      index = btreePage->entryNum / 2; /* 平均拆分 */
    }
    rc = btreeTableSplitPage(btreeTable, btreePage, newBtreePage, index, db, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 4.3.将leafRecord插入到btreePage中 */
    /* 如果newBtreePage的entryNum为0，说明是递增插入，新元组插入新页中*/
    newPageFirstRecord = newBtreePage->page.pData + PAGE_HEAD_SIZE;
    if (newBtreePage->entryNum == 0) {
      rc = leafPageInsertTuple(newBtreePage, leafRecord, tableSchema, btreeTable);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
    /*处理一般情况下元组插入*/
    else if (leafRecordCompareFun(leafRecord, newPageFirstRecord, tableSchema) < 0) {
      rc = leafPageInsertTuple(btreePage, leafRecord, tableSchema, btreeTable);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    } else {
      rc = leafPageInsertTuple(newBtreePage, leafRecord, tableSchema, btreeTable);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }

    /* 4.4.获取newBtreePage的第一个tuple的keyValueArray,并深拷贝其keyValueArray构建一个internalEntry插入到父页中 */
    firstRecord = newBtreePage->page.pData + PAGE_HEAD_SIZE;

    internalRecord = internalRecordDeepCopyFromLeafRecord(
        btreePage->page.id, btreeTable->tableName, btreeTable, tableSchema, firstRecord);
    if (internalRecord == NULL) {
      return GNCDB_SPACE_LACK;
    }

    /* 4.5.将internalEntry插入到父节点中，原始叶子页为根页，需要交换根页页号的过程在此 */
    rc = btreeTableInsertInParentPage(btreeTable, btreePage, internalRecord, newBtreePage, tableSchema, db, tx, depth);
    if (rc != GNCDB_SUCCESS) {
      if (internalRecord != NULL) {
        my_free(internalRecord);
      }
      return rc;
    }
    /*插入完成后释放临时构建的internalRecord*/
    if (internalRecord != NULL) {
      my_free(internalRecord);
    }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 对页进行拆分，（从左页拆分entry到右页中）
 * @param btreeTable
 * @param leftBtreePage 被拆分后的左页
 * @param rightBtreePage 被拆分后的右页
 * @param index	从leftBtreePage的index下标开始拆分
 * @param db
 * @param tid 事务id
 */
int btreeTableSplitPage(struct BtreeTable *btreeTable, struct BtreePage *leftBtreePage,
    struct BtreePage *rightBtreePage, int index, struct GNCDB *db, struct Transaction *tx)
{
  /* 1.变量的定义 */
  BYTE *internalRecord = NULL;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || leftBtreePage == NULL || rightBtreePage == NULL || db == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.从leftBtreePage移动一半的Entry到rightBtreePage中 */
  /* 内节点在拆分时，会将leftBtreePage中间（index - 1）的一个internalEntry拆分开来，
      指针放到rightBtreePage中，主键值插入到父页中 */
  /* 使用varArrayListRemoveTail一次性将leftBtreePage的index及其后面的内容移动到rightBtreePage */
  if (leftBtreePage->page.pageType == LEAF_PAGE) {
    memcpy(rightBtreePage->page.pData + PAGE_HEAD_SIZE,
        leftBtreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->leafRecordLength,
        (leftBtreePage->entryNum - index) * btreeTable->leafRecordLength);
  } else {
    memcpy(rightBtreePage->page.pData + PAGE_HEAD_SIZE,
        leftBtreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->internalRecordLength,
        (leftBtreePage->entryNum - index) * btreeTable->internalRecordLength);
  }

  /* 修改leftBtreePage和rightBtreePage中的entryNum */
  rightBtreePage->entryNum += leftBtreePage->entryNum - index;
  leftBtreePage->entryNum = index;

  /* 4.重新设置nextPageId */
  if (leftBtreePage->page.pageType == INTERNAL_PAGE) { /* 内部节点 */
    /* 4.1.需要将leftBtreePage最右边孩子页设置成rightBtreePage的最右边孩子页 */
    rightBtreePage->nextPageId = leftBtreePage->nextPageId;

    /* 4.2.在btreeTableInsertInParentPage中，内部页分裂时会将下标为index - 1位置处的keyValue插入到父节点中,
    这里需要将该keyValue对应的childPageId设为leftBtreePage的最右边的孩子页 */
    internalRecord = leftBtreePage->page.pData + PAGE_HEAD_SIZE + (index - 1) * btreeTable->internalRecordLength;
    if (internalRecord == NULL) {
      return GNCDB_NOT_FOUND;
    }
    memcpy(&leftBtreePage->nextPageId, internalRecord, sizeof(int));
  } else { /* 叶子节点 */
    rightBtreePage->nextPageId = leftBtreePage->nextPageId;
    leftBtreePage->nextPageId  = rightBtreePage->page.id;
  }
  /* 5.重新设置prevInsertIndex */
  if (index != 0) {
    /*因为上一次插入的index只在分裂时有用，所以分裂后的index可以直接初始化，因为分裂后的第一次插入不可能导致分裂*/
    leftBtreePage->prevInsertIndex  = 0;
    rightBtreePage->prevInsertIndex = 0;
  }
  /*index为0不需要更新prevInsertIndex*/

  return GNCDB_SUCCESS;
}

/**
 * @brief 在父页中插入主键
 * @param btreeTable
 * @param leftBtreePage 左边孩子页
 * @param internalEntry	 需要被插入到父页中的internalEntry
 * @param rightBtreePage 右边孩子页(被拆分出来的新页)
 * @param tableSchema
 * @param db
 * @param tx 事务
 * @param depth 叶子页所在层数
 */
int btreeTableInsertInParentPage(struct BtreeTable *btreeTable, struct BtreePage *leftBtreePage, BYTE *internalRecord,
    struct BtreePage *rightBtreePage, struct TableSchema *tableSchema, struct GNCDB *db, struct Transaction *tx,
    int depth)
{
  /* 1.变量的定义 */
  int        rc              = 0;
  BtreePage *parentBtreePage = NULL;
  // InternalEntry* nextInternalEntry = NULL;
  BYTE      *nextInternalRecord    = NULL;
  int        internalEntryMaxCount = 0;
  BtreePage *newBtreePage          = NULL;
  int        index                 = 0;
  int        parentPageId          = -1;
  // InternalEntry* pNewInternalEntry = NULL, * newInternalEntry = NULL;
  BYTE *pNewInternalRecord = NULL;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || leftBtreePage == NULL || rightBtreePage == NULL || internalRecord == NULL ||
      tableSchema == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取父页 */
  /* 如果leftBtreePage为根节点，则需要创建新的节点作为根节点 */
  if (leftBtreePage->page.id == btreeTable->rootPageId) {
    /* 3.1.获取一个空页,转换为内部页，作为树的新根节点*/
    parentBtreePage = createEmptyBtreePage(db, btreeTable, INTERNAL_PAGE, tx);
    if (parentBtreePage == NULL) {
      return GNCDB_BTG_CREATE_FALSE;
    }
    /* 3.2.交换leftBtreePage和parentBtreePage，保持根节点始终不变，同时修改相关内容*/
    rc = btreePageExchange(btreeTable, leftBtreePage, parentBtreePage, db, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    /* 3.3.intenalEntry是即将插入到parentBtreePage中的entry，需要修改它的childPageId为leftBtreePage的页号 */
    memcpy(internalRecord, &leftBtreePage->page.id, INT_SIZE);
    // internalEntry->childPageId = leftBtreePage->page.id;
  } else {
    rc = getParentPageIdfromLatchedPage(&parentPageId, tx, depth);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    parentBtreePage = getLatchedPage(btreeTable, db, parentPageId, tx);
    if (parentBtreePage == NULL) {
      return GNCDB_NOT_FOUND_PAGE;
    }
  }

  /* 4.紧挨着internalEntry后面一个的entry,需要修改该entry孩子页的页号为新拆分出来的rightBtreePage页号 */
  // nextInternalEntry = internalPageFindEntryByKeyvalue(parentBtreePage, internalEntry->keyValueArray, tableSchema,
  // btreeTable->tableName,db->catalog,GREATER_THAN);
  nextInternalRecord = internalPageFindRecordByKeyvalue(
      parentBtreePage, internalRecord, tableSchema, btreeTable->tableName, db->catalog, btreeTable, GREATER_THAN);
  /* 说明internalEntry应该被插入到parentBtreePage的最右边，即被拆分的页为parentBtreePage的最右边孩子页*/
  if (nextInternalRecord == NULL) {
    parentBtreePage->nextPageId = rightBtreePage->page.id;
  } else {
    // nextInternalEntry->childPageId = rightBtreePage->page.id;
    memcpy(nextInternalRecord, &rightBtreePage->page.id, INT_SIZE);
  }

  /* 5.internalPage中最大能够存放的entry数量*/
  internalEntryMaxCount = getInternalEntryMaxCount(db->pageCurrentSize, btreeTable);
  if (parentBtreePage->entryNum < internalEntryMaxCount) {
    // rc = internalPageInsertEntry(parentBtreePage,internalEntry);
    rc = internalPageInsertRecord(parentBtreePage, internalRecord, tableSchema, btreeTable, db->catalog);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } else {
    /* 4.1.创建一个LeafPage，初始化信息，备份并添加到newCreatedPageSet */
    newBtreePage = createEmptyBtreePage(db, btreeTable, INTERNAL_PAGE, tx);
    if (newBtreePage == NULL) {
      return GNCDB_BTG_CREATE_FALSE;
    }

    /* 4.2.先将internalEntry插入到parentBtreePage,然后再将部分entry拆分到newBtreePage中 */
    rc = internalPageInsertRecord(parentBtreePage, internalRecord, tableSchema, btreeTable, db->catalog);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /*for (int i = 0; i < parentBtreePage->entryArray->elementCount; ++i)
    {
        InternalEntry* inter = varArrayListGetPointer(parentBtreePage->entryArray, i);
        char* key = varArrayListGetPointer(inter->keyValueArray, 0);
        // printf()
    }*/

    /* 4.3.拆分 */
    /* 从页中下标为index处开始拆分,下标为index-1的internalEntry要插入到parentBtreePage的父节点中 */
    index = parentBtreePage->entryNum / 2;
    rc    = btreeTableSplitPage(btreeTable, parentBtreePage, newBtreePage, index, db, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 4.4.获取第index-1的internalEntry */
    pNewInternalRecord = parentBtreePage->page.pData + PAGE_HEAD_SIZE + (index - 1) * btreeTable->internalRecordLength;
    // pNewInternalEntry = (InternalEntry*)varArrayListGetPointer(parentBtreePage->entryArray,index - 1);
    // if (pNewInternalEntry == NULL) {
    // 	return GNCDB_NOT_FOUND;
    // }
    // newInternalEntry = pNewInternalEntry;

    /* 4.5.这里必须先删除pNewInternalEntry */
    // rc = varArrayListRemovePointer(parentBtreePage->entryArray, pNewInternalEntry);	/*
    // 将pNewInternalEntry从parentBtreePage中移除，但不释放其内存 */ if (rc != GNCDB_SUCCESS) { 	return rc;
    // }

    // 这里删除仅entryNum减一，不释放内存
    /* 更新parentBtreePage中entryNum */
    parentBtreePage->entryNum--;

    /* 4.6.将newInternalEntry的childPageId设置为parentBtreePage页的id */
    memcpy(pNewInternalRecord, &parentBtreePage->page.id, INT_SIZE);
    // newInternalEntry->childPageId = parentBtreePage->page.id;
    rc = btreeTableInsertInParentPage(
        btreeTable, parentBtreePage, pNewInternalRecord, newBtreePage, tableSchema, db, tx, depth - 1);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    // todo:将parentBtreePage的index-1的record清零
  }

  return GNCDB_SUCCESS;
}

/*********************************************************************************btreeTable_delete*******************************************************************/

/**
 * @brief btreeTable删除元组，提供给上层的接口
 * @param btreeTable
 * @param keyValueArray 需要删除的tuple的主键值
 * @param tableSchema
 * @param db
 * @param tx
 */
int btreeTableDeleteTuple(struct BtreeTable *btreeTable, struct varArrayList *keyValueArray,
    struct TableSchema *tableSchema, struct GNCDB *db, struct Transaction *tx, int *updatedPageId, BtreePage *page,
    int index)
{
  /* 1.变量的定义 */
  int        rc        = 0;
  int        depth     = 0;
  BtreePage *btreePage = NULL;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || keyValueArray == NULL || tableSchema == NULL || db == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.查找应该删除B+树的哪个叶子页 */
  /* 先获取btreeTable的读锁 */
  if (page != NULL) {
    btreePage = page;
    rc        = setPageStatusPinUp(db->pagePool, btreePage->page.id, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, btreePage->page.id, EXCLUSIVE);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    LOG(LOG_TRACE, "WLOCKing:PAGEid=%d", btreePage->page.id);
    WriteLock(&btreePage->page.rwlock_t);
    LOG(LOG_TRACE, "WLOCKend:PAGEid=%d", btreePage->page.id);
    rc = addIntoLatchedPageSet(tx, (void *)btreePage);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } else {
    LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", btreeTable->tableName);
    ReadLock(&btreeTable->rwlock_t);
    LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", btreeTable->tableName);

    /* 再调用查找叶子页的函数 */
    btreePage = btreeTableFindTupleInLeafPage(btreeTable, keyValueArray, tableSchema, db, tx, OP_DELETE, true, &depth);
    if (btreePage == NULL) {
      return GNCDB_LEAFPAGE_NOT_FOUND;
    }
    rc = lockManagerInternalPageLock(db->transactionManager->lockManager, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /* 4.对latchedPageSet做统一备份，防止出现重复备份 */
  rc = backUpLatchedPage(db, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 5.调用删除函数 */
  rc = deleteTuple(btreeTable, btreePage, keyValueArray, tableSchema, db, tx, updatedPageId, depth, index);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 6.做后续的处理工作，释放所有的锁并unpin，销毁需要删除的页 */
  rc = releaseWLatches(db->pagePool, tx, true, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* todo ? 考虑逻辑锁的释放 */

  // printf("\n");
  return GNCDB_SUCCESS;
}

/**
 * @brief btreeTable删除元组
 * @param btreeTable
 * @param btreePage
 * @param keyValueArray 需要删除的tuple的主键值
 * @param tableSchema
 * @param db
 * @param tx 事务
 * @param depth 叶子页所在层数
 */
int deleteTuple(struct BtreeTable *btreeTable, struct BtreePage *btreePage, struct varArrayList *keyValueArray,
    struct TableSchema *tableSchema, struct GNCDB *db, struct Transaction *tx, int *updatedPageId, int depth,
    int sourceIndex)
{
  /* 1.变量的定义 */
  int        rc = 0, leafTupleMinCount = 0, leafTupleMaxCount = 0, parentPageId = -1;
  BtreePage *parentBtreePage = NULL, *leftBtreePage = NULL, *rightBtreePage = NULL;
  BYTE      *curParentInternalRecord = NULL, *leftParentInternalRecord = NULL, *rightParentInternalRecord = NULL;
  int        index = 0, childPageId = 0;
  bool       isRootPageChanged = false;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || btreePage == NULL || keyValueArray == NULL || tableSchema == NULL || db == NULL ||
      tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  leafTupleMinCount = getLeafTupleMinCount(db->pageCurrentSize, btreeTable);
  leafTupleMaxCount = getLeafTupleMaxCount(db->pageCurrentSize, btreeTable);
  /* 5.叶子页的tuple太少，需要与左右兄弟合并或者借 */

  if (btreePage->entryNum - 1 < leafTupleMinCount && btreePage->page.id != btreeTable->rootPageId) {
    /* 5.1.获取btreePage父页 */
    rc = getParentPageIdfromLatchedPage(&parentPageId, tx, depth);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    parentBtreePage = getLatchedPage(btreeTable, db, parentPageId, tx);
    if (parentBtreePage == NULL) {
      return GNCDB_NOT_FOUND_PAGE;
    }

    /* 5.2.获取btreePage对应在父页中的internalEntry，即父页中第一个internalEntry的主键值大于keyvalueArray的 */
    curParentInternalRecord = internalPageFindEntryByKeyvalue(parentBtreePage,
        keyValueArray,
        tableSchema,
        btreeTable->tableName,
        db->catalog,
        btreeTable,
        GREATER_THAN,
        &index);
    if (curParentInternalRecord == NULL) { /* keyValueArray位于父页最右边孩子页中，则只可能和左兄弟合并或者借节点 */
      index = parentBtreePage->entryNum;   /* parentInternalEntry在父页中的下标 */
    }
    /* 将keyValueArray从btreePage中删除掉 */
    rc = leafPageDeleteTuple(
        btreePage, keyValueArray, tableSchema, btreeTable->tableName, db->catalog, btreeTable, sourceIndex);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 5.3.判断是与左兄弟操作还是与右兄弟操作 */
    if (index != 0) { /* 左兄弟不为空*/
      /* 5.3.1.获取internalEntry左边的entry*/
      leftParentInternalRecord =
          parentBtreePage->page.pData + PAGE_HEAD_SIZE + (index - 1) * btreeTable->internalRecordLength;

      /* 5.3.2.获取左兄弟页 */
      memcpy(&childPageId, leftParentInternalRecord, INT_SIZE);
      leftBtreePage = getLatchedPage(btreeTable, db, childPageId, tx);
      if (leftBtreePage == NULL) {
        return GNCDB_NOT_FOUND_PAGE;
      }

      /* 5.3.3.两个页合并(与左兄弟合并)*/
      if ((leftBtreePage->entryNum + btreePage->entryNum) <= leafTupleMaxCount) {
        /* 将当前页合并到左兄弟页上 */
        rc = btreeTableMergePage(btreeTable, parentBtreePage, leftBtreePage, btreePage, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }

        /* 用于更新cursor */
        if (updatedPageId != NULL) {
          *updatedPageId = leftBtreePage->page.id;
        }

        /* 从父页中删除leftParentInternalEntry */
        /*（这里的leftParentInternalEntry的孩子页为parentInternalEntry的，将leftParentInternalEntry的孩子页保留，在btreeTableDeleteInParentPage中做了处理）*/
        rc = btreeTableDeleteInParentPage(btreeTable,
            parentBtreePage,
            leftParentInternalRecord,
            curParentInternalRecord,
            tableSchema,
            db,
            tx,
            depth - 1,
            &isRootPageChanged);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }

        /* 当根节点只剩一个孩子时会将根节点页号交换到该叶子页上，此时要再次更新cursor */
        if (updatedPageId != NULL && isRootPageChanged) {
          *updatedPageId = btreeTable->rootPageId;
        }
      } else { /* 从左兄弟页中借*/
        rc = btreeTableBorrowEntry(
            btreeTable, parentBtreePage, leftBtreePage, leftParentInternalRecord, btreePage, tableSchema, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
    } else if (index == 0) { /* 右兄弟不为空 */
      /* 5.3.1.获取internalEntry右边的entry*/
      if (parentBtreePage->entryNum == 1) {
        rightParentInternalRecord = NULL;
        childPageId               = parentBtreePage->nextPageId;
      } else {
        rightParentInternalRecord =
            parentBtreePage->page.pData + PAGE_HEAD_SIZE + (index + 1) * btreeTable->internalRecordLength;
        if (rightParentInternalRecord == NULL) {
          return GNCDB_NOT_FOUND;
        }
        memcpy(&childPageId, rightParentInternalRecord, INT_SIZE);
      }

      /* 5.3.2.获取右兄弟页 */
      rightBtreePage = getLatchedPage(btreeTable, db, childPageId, tx);
      if (rightBtreePage == NULL) {
        return GNCDB_NOT_FOUND_PAGE;
      }

      /* 5.3.3.两个页合并（与右兄弟合并）*/
      if ((rightBtreePage->entryNum + btreePage->entryNum) <= leafTupleMaxCount) {
        /* 右兄弟合并到当前页 */
        rc = btreeTableMergePage(btreeTable, parentBtreePage, btreePage, rightBtreePage, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }

        rc = btreeTableDeleteInParentPage(btreeTable,
            parentBtreePage,
            curParentInternalRecord,
            rightParentInternalRecord,
            tableSchema,
            db,
            tx,
            depth - 1,
            &isRootPageChanged);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
        /* 当根节点只剩一个孩子时会将根节点页号交换到该叶子页上，此时要再次更新cursor */
        if (updatedPageId != NULL && isRootPageChanged) {
          *updatedPageId = btreeTable->rootPageId;
        }
      } else { /* 从右兄弟页中借*/
        rc = btreeTableBorrowEntry(
            btreeTable, parentBtreePage, btreePage, curParentInternalRecord, rightBtreePage, tableSchema, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
    }
  } else {
    rc = leafPageDeleteTuple(
        btreePage, keyValueArray, tableSchema, btreeTable->tableName, db->catalog, btreeTable, sourceIndex);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/**
  * @brief 在父页中删除对应的internalEtnry
  * @param btreeTable
  * @param btreePage 需要删除internalEntry的页
  * @param firstInternalEntry
  * @param secondInternalEntry
  * @param isFirstToSecond
    0：secondInternalEntry的子页合并到了firstInternalEntry（firstInternalEntry为当前页，secondInternalEntry为兄弟页）
    1：firstInternalEntry的子页合并到了secondInternalEntry（secondInternalEntry为当前页，firstInternalEntry为兄弟页）
    合并过程中都是将兄弟页合并到当前页中。
    第一种情况，如果secondInternalEntry不为NULL，将secondInternalEntry的孩子页修改为firstInternalEntry的孩子页，
    如果secondInternalEntry为NULL，则修改父页的nextPageId指针为firstInternalEntry的孩子页，避免将secondInternalEntry的主键值挪动到firstInternalEntry这一过程；
    第二种情况，直接将firstInternalEntry删除即可。
    这两种情况下，都是将firstInternalEntry删除掉
  * @param tableSchema
  * @param db
  * @param tx 事务
  * @param depth btreePage的深度
  * @param isRootPageChanged 是否出现根页只有一个孩子时需要进一步修改根页的情况
  */
int btreeTableDeleteInParentPage(struct BtreeTable *btreeTable, struct BtreePage *btreePage, BYTE *firstInternalRecord,
    BYTE *secondInternalRecord, struct TableSchema *tableSchema, struct GNCDB *db, struct Transaction *tx, int depth,
    bool *isRootPageChanged)
{
  /* 1.变量的定义 */
  int        rc              = 0;
  BtreePage *parentBtreePage = NULL, *childBtreePage = NULL, *leftBtreePage = NULL, *rightBtreePage = NULL;
  // InternalEntry* curParentInternalEntry = NULL, * leftParentInternalEntry = NULL, * newInternalEntry = NULL, *
  // rightParentInternalEntry = NULL;
  BYTE *curParentInternalRecord = NULL, *leftParentInternalRecord = NULL, *rightParentInternalRecord = NULL,
       *newInternalRecord   = NULL;
  int internalEntryMinCount = 0, internalEntryMaxCount = 0;
  int index = 0, childPageId = 0, parentPageId = -1;

  /* 2.判断参数是否为空 */
  /* 这里不需要判断secondInternalEntry是否为空，为空时说明发生合并的其中一个页是父页的最右边孩子页  */
  if (btreeTable == NULL || btreePage == NULL || tableSchema == NULL || db == NULL || tx == NULL ||
      firstInternalRecord == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.总是将右兄弟合并到左兄弟上，这里的处理是将右兄弟的internalEntry保留下来，删除左兄弟的internalEntry，但是设置右兄弟的internalEntry的孩子页为左兄弟的internalEntry保留的孩子页
   */
  if (secondInternalRecord == NULL) {
    // btreePage->nextPageId = firstInternalEntry->childPageId;
    memcpy(&btreePage->nextPageId, firstInternalRecord, INT_SIZE);
  } else {
    // secondInternalEntry->childPageId = firstInternalEntry->childPageId;
    memcpy(secondInternalRecord, firstInternalRecord, INT_SIZE);
  }

  /* 4.获取父页以及btreePage在父页中的对应的curParentInternalEntry
  （放在这里获取的原因是防止btreePage做了删除操作之后entryNum为0） */
  rc = getParentPageIdfromLatchedPage(&parentPageId, tx, depth);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  if (!isPageSafe(btreeTable, btreePage, db->pageCurrentSize, OP_DELETE) && parentPageId != 0) {
    /* 4.1.获取父页 */
    parentBtreePage = getLatchedPage(btreeTable, db, parentPageId, tx);
    if (parentBtreePage == NULL) {
      return GNCDB_NOT_FOUND_PAGE;
    }

    /* 4.2.此处不需要加判断curParentInternalEntry是否为空，
    因为btreePage可能为parentBtreePage最右边的孩子页*/
    // curParentInternalEntry = internalPageFindEntryByKeyvalue(parentBtreePage, firstInternalEntry->keyValueArray,
    // tableSchema, btreeTable->tableName, db->catalog, GREATER_THAN);
    curParentInternalRecord = internalPageFindRecordByKeyvalue(parentBtreePage,
        firstInternalRecord,
        tableSchema,
        btreeTable->tableName,
        db->catalog,
        btreeTable,
        GREATER_THAN);
  }

  /* 5.btreePage删除internalEntry */
  rc = internalPageDeleteRecord(
      btreePage, firstInternalRecord, tableSchema, btreeTable->tableName, db->catalog, btreeTable);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 6.判断是否需要进一步调整 */
  internalEntryMinCount = getInternalEntryMinCount(db->pageCurrentSize, btreeTable);
  internalEntryMaxCount = getInternalEntryMaxCount(db->pageCurrentSize, btreeTable);
  if (btreePage->page.id == btreeTable->rootPageId && btreePage->entryNum == 0) { /* 如果是根节点且只有一个子节点*/
    /* 6.1.获取孩子页*/
    childBtreePage = getLatchedPage(btreeTable, db, btreePage->nextPageId, tx);
    if (childBtreePage == NULL) {
      return GNCDB_NOT_FOUND_PAGE;
    }

    /* 6.2.交换childBtreePage和btreePage，保持根节点始终不变，同时修改相关内容*/
    rc = btreePageExchange(btreeTable, childBtreePage, btreePage, db, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 6.3.将btreePage页添加到待删除页中 */
    rc = addIntoDeletedPageSet(tx, (Page *)btreePage);
    if (childBtreePage->page.pageType == LEAF_PAGE) {
      *isRootPageChanged = true;
    }
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  } else if (btreePage->entryNum < internalEntryMinCount &&
             btreePage->page.id != btreeTable->rootPageId) { /*如果btreePage中entry太少（根页太少不做处理）*/
    /* 6.1.internalEntry在父页中的下标*/
    if (curParentInternalRecord == NULL) {
      /* 说明internalEntry应该在parentBtreePage的最右边孩子页中，只可能和左兄弟合并或者借节点*/
      index = parentBtreePage->entryNum;
    } else {
      index = internalPageFindRecordIndexByKeyvalue(
          parentBtreePage, curParentInternalRecord, tableSchema, btreeTable->tableName, db->catalog, EQUAL, btreeTable);
      // index = varArrayListIndexOfPointer(parentBtreePage->entryArray, curParentInternalEntry);
    }

    /* 6.2.左兄弟不为空*/
    if (index != 0) {
      /* 6.2.1.获取左兄弟在父页中的internalEntry */
      // leftParentInternalEntry = (InternalEntry*)varArrayListGetPointer(parentBtreePage->entryArray, index - 1);
      // if (leftParentInternalEntry == NULL) {
      // 	return GNCDB_NOT_FOUND;
      // }
      leftParentInternalRecord =
          parentBtreePage->page.pData + PAGE_HEAD_SIZE + (index - 1) * btreeTable->internalRecordLength;
      /* 6.2.2.获取左兄弟页*/
      memcpy(&childPageId, leftParentInternalRecord, INT_SIZE);
      leftBtreePage = getLatchedPage(btreeTable, db, childPageId, tx);
      if (leftBtreePage == NULL) {
        return GNCDB_NOT_FOUND_PAGE;
      }

      /* 6.2.3.两个页合并（与左兄弟合并，合并到左边兄弟）*/
      if ((leftBtreePage->entryNum + btreePage->entryNum) < internalEntryMaxCount) {
        /* 内部页在合并时需要将两个孩子页之间对应的父页的keyValueArray添加到子页中(leftInernalEntry为leftBtreePage与btreePage之间)
         */
        newInternalRecord = internalRecordDeepCopy(leftBtreePage->nextPageId, btreeTable, leftParentInternalRecord);
        // newInternalEntry = internalEntryDeepCopy(leftBtreePage->nextPageId, leftParentInternalEntry->keyValueArray,
        // btreeTable->tableName,db->catalog);
        rc = internalPageInsertRecord(leftBtreePage, newInternalRecord, tableSchema, btreeTable, db->catalog);
        // rc = varArrayListAddPointer(leftBtreePage->entryArray, newInternalEntry);
        if (rc != GNCDB_SUCCESS) {
          if (newInternalRecord != NULL) {
            my_free(newInternalRecord);
          }
          return rc;
        }
        if (newInternalRecord != NULL) {
          my_free(newInternalRecord);
        }
        // leftBtreePage->entryNum++;

        /* 两子页合并，当前页合并到左兄弟 */
        rc = btreeTableMergePage(btreeTable, parentBtreePage, leftBtreePage, btreePage, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }

        /* 从父页中删除leftParentInternalEntry*/
        /*（将curParentInternalEntry的孩子页保留，在btreeTableDeleteInParentPage中做了处理）*/
        rc = btreeTableDeleteInParentPage(btreeTable,
            parentBtreePage,
            leftParentInternalRecord,
            curParentInternalRecord,
            tableSchema,
            db,
            tx,
            depth - 1,
            NULL);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      } else { /* 从左兄弟页中借*/
        rc = btreeTableBorrowEntry(
            btreeTable, parentBtreePage, leftBtreePage, leftParentInternalRecord, btreePage, tableSchema, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
    } else if (index == 0) { /* 只能与右兄弟合并或者借 */
      /* 6.2.1.获取右兄弟在父页中的internalEntry */
      if (parentBtreePage->entryNum == 1) {
        rightParentInternalRecord = NULL;
        childPageId               = parentBtreePage->nextPageId;
      } else {
        rightParentInternalRecord =
            parentBtreePage->page.pData + PAGE_HEAD_SIZE + (index + 1) * btreeTable->internalRecordLength;
        // rightParentInternalEntry = (InternalEntry*)varArrayListGetPointer(parentBtreePage->entryArray, index + 1);
        if (rightParentInternalRecord == NULL) {
          return GNCDB_NOT_FOUND;
        }
        memcpy(&childPageId, rightParentInternalRecord, INT_SIZE);
        // childPageId = rightParentInternalEntry->childPageId;
      }

      /* 6.2.2.获取右兄弟页*/
      rightBtreePage = getLatchedPage(btreeTable, db, childPageId, tx);
      if (rightBtreePage == NULL) {
        return GNCDB_NOT_FOUND_PAGE;
      }

      /* 6.2.3.两个页合并（与右兄弟合并，合并到自己本身） */
      if (rightBtreePage->entryNum + btreePage->entryNum < internalEntryMaxCount) {
        /* 内部页在合并时需要将两个孩子页之间对应的父页的keyValueArray添加到子页中(curParentInternalEntry为btreePage与rightBtreePage之间)*/
        newInternalRecord = internalRecordDeepCopy(btreePage->nextPageId, btreeTable, curParentInternalRecord);
        // newInternalEntry = internalEntryDeepCopy(btreePage->nextPageId, curParentInternalEntry->keyValueArray,
        // btreeTable->tableName,db->catalog);
        rc = internalPageInsertRecord(btreePage, newInternalRecord, tableSchema, btreeTable, db->catalog);
        // rc = varArrayListAddPointer(btreePage->entryArray, newInternalEntry);
        if (rc != GNCDB_SUCCESS) {
          if (newInternalRecord != NULL) {
            my_free(newInternalRecord);
          }
          return rc;
        }
        if (newInternalRecord != NULL) {
          my_free(newInternalRecord);
        }
        // btreePage->entryNum++;

        /* 两子页合并，合并到当前页 */
        rc = btreeTableMergePage(btreeTable, parentBtreePage, btreePage, rightBtreePage, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }

        /* 从父页中删除curParentInternalEntry*/
        /*（将rightParentInternalEntry的孩子页保留，但需要将rightParentInternalEntry的孩子页页号设置为curParentInternalEntry的孩子页页号，
             在btreeTableDeleteInParentPage中做了处理）*/
        rc = btreeTableDeleteInParentPage(btreeTable,
            parentBtreePage,
            curParentInternalRecord,
            rightParentInternalRecord,
            tableSchema,
            db,
            tx,
            depth - 1,
            NULL);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      } else { /* 从右兄弟页中借*/
        rc = btreeTableBorrowEntry(
            btreeTable, parentBtreePage, btreePage, curParentInternalRecord, rightBtreePage, tableSchema, db, tx);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }
      }
    }
  }

  return GNCDB_SUCCESS;
}

// int btreeTableDeleteInParentPage(struct BtreeTable* btreeTable, struct BtreePage* btreePage, struct InternalEntry*
// firstInternalEntry, 	struct InternalEntry* secondInternalEntry,struct TableSchema* tableSchema, struct GNCDB* db,
// struct Transaction* tx, int depth, bool* isRootPageChanged) {
// 	/* 1.变量的定义 */
// 	int rc = 0;
// 	BtreePage* parentBtreePage = NULL, * childBtreePage = NULL, * leftBtreePage = NULL, * rightBtreePage = NULL;
// 	InternalEntry* curParentInternalEntry = NULL, * leftParentInternalEntry = NULL, * newInternalEntry = NULL, *
// rightParentInternalEntry = NULL; 	int internalEntryMinCount = 0, internalEntryMaxCount = 0; 	int index = 0,
// childPageId = 0, parentPageId = -1;

// 	/* 2.判断参数是否为空 */
// 	/* 这里不需要判断secondInternalEntry是否为空，为空时说明发生合并的其中一个页是父页的最右边孩子页  */
// 	if (btreeTable == NULL || btreePage == NULL || tableSchema == NULL || db == NULL || tx == NULL || firstInternalEntry
// == NULL) { 		return GNCDB_PARAMNULL;
// 	}

// 	/* 3.总是将右兄弟合并到左兄弟上，这里的处理是将右兄弟的internalEntry保留下来，删除左兄弟的internalEntry，但是设置右兄弟的internalEntry的孩子页为左兄弟的internalEntry保留的孩子页
// */ 	if (secondInternalEntry == NULL) { 		btreePage->nextPageId = firstInternalEntry->childPageId;
// 	}
// 	else {
// 		secondInternalEntry->childPageId = firstInternalEntry->childPageId;
// 	}

// 	/* 4.获取父页以及btreePage在父页中的对应的curParentInternalEntry
// 	（放在这里获取的原因是防止btreePage做了删除操作之后entryNum为0） */
// 	rc = getParentPageIdfromLatchedPage(&parentPageId,tx,depth);
// 	if (rc != GNCDB_SUCCESS) {
// 		return rc;
// 	}
// 	if (!isPageSafe(btreeTable, btreePage, OP_DELETE) && parentPageId != 0) {
// 		/* 4.1.获取父页 */
// 		parentBtreePage = getLatchedPage(btreeTable, db, parentPageId, tx);
// 		if (parentBtreePage == NULL) {
// 			return GNCDB_NOT_FOUND_PAGE;
// 		}

// 		/* 4.2.此处不需要加判断curParentInternalEntry是否为空，
// 		因为btreePage可能为parentBtreePage最右边的孩子页*/
// 		curParentInternalEntry = internalPageFindEntryByKeyvalue(parentBtreePage, firstInternalEntry->keyValueArray,
// tableSchema, btreeTable->tableName, db->catalog, GREATER_THAN);
// 	}

// 	/* 5.btreePage删除internalEntry */
// 	rc = internalPageDeleteEntry(btreePage,
// firstInternalEntry->keyValueArray,tableSchema,btreeTable->tableName,db->catalog); 	if (rc != GNCDB_SUCCESS) {
// return rc;
// 	}

// 	/* 6.判断是否需要进一步调整 */
// 	internalEntryMinCount = getInternalEntryMinCount(btreeTable);
// 	internalEntryMaxCount = getInternalEntryMaxCount(btreeTable);
// 	if (btreePage->page.id == btreeTable->rootPageId && btreePage->entryNum == 0) { /* 如果是根节点且只有一个子节点*/
// 		/* 6.1.获取孩子页*/
// 		childBtreePage = getLatchedPage(btreeTable, db, btreePage->nextPageId, tx);
// 		if (childBtreePage == NULL) {
// 			return GNCDB_NOT_FOUND_PAGE;
// 		}

// 		/* 6.2.交换childBtreePage和btreePage，保持根节点始终不变，同时修改相关内容*/
// 		rc = btreePageExchange(btreeTable, childBtreePage, btreePage, db, tx);
// 		if (rc != GNCDB_SUCCESS) {
// 			return rc;
// 		}

// 		/* 6.3.将btreePage页添加到待删除页中 */
// 		rc = addIntoDeletedPageSet(tx,(Page*)btreePage);
// 		if(childBtreePage->page.pageType == LEAF_PAGE){
// 			*isRootPageChanged = true;
// 		}
// 		if (rc != GNCDB_SUCCESS) {
// 			return rc;
// 		}
// 	} else if (btreePage->entryNum < internalEntryMinCount && btreePage->page.id != btreeTable->rootPageId) {
// /*如果btreePage中entry太少（根页太少不做处理）*/
// 		/* 6.1.internalEntry在父页中的下标*/
// 		if (curParentInternalEntry == NULL) {
// 			/* 说明internalEntry应该在parentBtreePage的最右边孩子页中，只可能和左兄弟合并或者借节点*/
// 			index = parentBtreePage->entryNum;
// 		}
// 		else {
// 			index = varArrayListIndexOfPointer(parentBtreePage->entryArray, curParentInternalEntry);
// 		}

// 		/* 6.2.左兄弟不为空*/
// 		if (index != 0) {
// 			/* 6.2.1.获取左兄弟在父页中的internalEntry */
// 			leftParentInternalEntry = (InternalEntry*)varArrayListGetPointer(parentBtreePage->entryArray, index - 1);
// 			if (leftParentInternalEntry == NULL) {
// 				return GNCDB_NOT_FOUND;
// 			}

// 			/* 6.2.2.获取左兄弟页*/
// 			leftBtreePage = getLatchedPage(btreeTable, db, leftParentInternalEntry->childPageId, tx);
// 			if (leftBtreePage == NULL) {
// 				return GNCDB_NOT_FOUND_PAGE;
// 			}

// 			/* 6.2.3.两个页合并（与左兄弟合并，合并到左边兄弟）*/
// 			if ((leftBtreePage->entryNum + btreePage->entryNum) < internalEntryMaxCount) {
// 				/*
// 内部页在合并时需要将两个孩子页之间对应的父页的keyValueArray添加到子页中(leftInernalEntry为leftBtreePage与btreePage之间)
// */ 				newInternalEntry = internalEntryDeepCopy(leftBtreePage->nextPageId,
// leftParentInternalEntry->keyValueArray, btreeTable->tableName,db->catalog); 				rc =
// varArrayListAddPointer(leftBtreePage->entryArray, newInternalEntry); 				if (rc
// != GNCDB_SUCCESS) { 					return rc;
// 				}
// 				leftBtreePage->entryNum++;

// 				/* 两子页合并，当前页合并到左兄弟 */
// 				rc = btreeTableMergePage(btreeTable, parentBtreePage,leftBtreePage, btreePage, db, tx);
// 				if (rc != GNCDB_SUCCESS) {
// 					return rc;
// 				}

// 				/* 从父页中删除leftParentInternalEntry*/
// 				/*（将curParentInternalEntry的孩子页保留，在btreeTableDeleteInParentPage中做了处理）*/
// 				rc = btreeTableDeleteInParentPage(btreeTable, parentBtreePage, leftParentInternalEntry,
// curParentInternalEntry, tableSchema, db, tx, depth-1, NULL); 				if (rc != GNCDB_SUCCESS) {
// return rc;
// 				}
// 			}else {  /* 从左兄弟页中借*/
// 				rc = btreeTableBorrowEntry(btreeTable,parentBtreePage, leftBtreePage, leftParentInternalEntry,btreePage,
// tableSchema, db,tx); 				if (rc != GNCDB_SUCCESS) { 					return rc;
// 				}
// 			}
// 		}else if (index == 0) {   /* 只能与右兄弟合并或者借 */
// 			/* 6.2.1.获取右兄弟在父页中的internalEntry */
// 			if (parentBtreePage->entryNum == 1) {
// 				rightParentInternalEntry = NULL;
// 				childPageId = parentBtreePage->nextPageId;
// 			}else {
// 				rightParentInternalEntry = (InternalEntry*)varArrayListGetPointer(parentBtreePage->entryArray, index +
// 1); 				if (rightParentInternalEntry == NULL) { 					return GNCDB_NOT_FOUND;
// 				}
// 				childPageId = rightParentInternalEntry->childPageId;
// 			}

// 			/* 6.2.2.获取右兄弟页*/
// 			rightBtreePage = getLatchedPage(btreeTable, db, childPageId, tx);
// 			if (rightBtreePage == NULL) {
// 				return GNCDB_NOT_FOUND_PAGE;
// 			}

// 			/* 6.2.3.两个页合并（与右兄弟合并，合并到自己本身） */
// 			if (rightBtreePage->entryNum + btreePage->entryNum < internalEntryMaxCount) {
// 				/*
// 内部页在合并时需要将两个孩子页之间对应的父页的keyValueArray添加到子页中(curParentInternalEntry为btreePage与rightBtreePage之间)*/
// 				newInternalEntry = internalEntryDeepCopy(btreePage->nextPageId, curParentInternalEntry->keyValueArray,
// btreeTable->tableName,db->catalog); 				rc = varArrayListAddPointer(btreePage->entryArray,
// newInternalEntry); 				if (rc != GNCDB_SUCCESS)
// 				{
// 					return rc;
// 				}
// 				btreePage->entryNum++;

// 				/* 两子页合并，合并到当前页 */
// 				rc = btreeTableMergePage(btreeTable, parentBtreePage,btreePage, rightBtreePage, db, tx);
// 				if (rc != GNCDB_SUCCESS)
// 				{
// 					return rc;
// 				}

// 				/* 从父页中删除curParentInternalEntry*/
// 				/*（将rightParentInternalEntry的孩子页保留，但需要将rightParentInternalEntry的孩子页页号设置为curParentInternalEntry的孩子页页号，
// 				     在btreeTableDeleteInParentPage中做了处理）*/
// 				rc = btreeTableDeleteInParentPage(btreeTable, parentBtreePage, curParentInternalEntry,
// rightParentInternalEntry, tableSchema, db, tx,depth-1, NULL); 				if (rc != GNCDB_SUCCESS){
// return rc;
// 				}
// 			}else { /* 从右兄弟页中借*/
// 				rc = btreeTableBorrowEntry(btreeTable,parentBtreePage, btreePage, curParentInternalEntry,rightBtreePage,
// tableSchema, db,tx); 				if (rc != GNCDB_SUCCESS){ 					return rc;
// 				}
// 			}
// 		}
// 	}

// 	return GNCDB_SUCCESS;
// }

/**
 * @brief 将leftBtreePage与rightBtreePage进行合并
 * @param btreeTable
 * @param leftBtreePage 左页
 * @param rightBtreePage 右页
 * @param db
 * @param tid 事务id
 */
int btreeTableMergePage(struct BtreeTable *btreeTable, struct BtreePage *parentBtreePage,
    struct BtreePage *leftBtreePage, struct BtreePage *rightBtreePage, struct GNCDB *db, struct Transaction *tx)
{
  /* 1.变量的定义 */
  int rc = 0;
  // InternalEntry* internalEntry = NULL;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || leftBtreePage == NULL || rightBtreePage == NULL || db == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.叶子页的合并*/
  if (leftBtreePage->page.pageType == LEAF_PAGE) {
    // if (isFirstToSecond) { /* 左兄弟合并到右兄弟 */
    //	/* 3.1.更新entryNum，并进行合并 */
    //	rightBtreePage->entryNum += leftBtreePage->entryNum;
    //	rc = varArrayListRemoveTail(leftBtreePage->entryArray, rightBtreePage->entryArray, 0);
    //	if (rc != GNCDB_SUCCESS) {
    //		return rc;
    //	}

    //	/* 维护leftBtreePage的左兄弟指针指向rightBtreePage */
    //	index = getIndexOfChildPage(parentBtreePage, leftBtreePage->page.id);
    //	if (index >= 1) {
    //		leftOfLeftInternalEntry = varArrayListGetPointer(parentBtreePage->entryArray,index - 1);
    //		if (leftOfLeftInternalEntry == NULL) {
    //			return GNCDB_NOT_FOUND;
    //		}

    //		leftOfLeftBtreePage = getLatchedPage(btreeTable,db, leftOfLeftInternalEntry->childPageId,tx);
    //		if (leftOfLeftBtreePage == NULL) {
    //			return GNCDB_NOT_FOUND;
    //		}

    //		leftOfLeftBtreePage->nextPageId = rightBtreePage->page.id;
    //	}
    //	else
    //	{
    //		/* leftBtreePage是parentBtreePage的第一个孩子，那么需要判断parentBtreePage是否还有左兄弟 */
    //		grandParentPage = getLatchedPage(btreeTable, db, parentBtreePage->parentPageId, tx);
    //		if (grandParentPage == NULL) {
    //			return GNCDB_NOT_FOUND;
    //		}
    //		index = getIndexOfChildPage(grandParentPage, parentBtreePage->page.id);
    //		if (index >= 1)
    //		{
    //			/* 说明parentBtreePage有做左兄弟，那么需要拿取左兄弟的最后一个孩子页 */
    //			parentLeftSiblingInternalEntry = varArrayListGetPointer(grandParentPage->entryArray, index - 1);
    //			if (parentLeftSiblingInternalEntry == NULL) {
    //				return GNCDB_NOT_FOUND;
    //			}

    //			parentLeftSiblingPage = getLatchedPage(btreeTable, db, parentLeftSiblingInternalEntry->childPageId, tx);
    //			if (parentLeftSiblingPage == NULL) {
    //				return GNCDB_NOT_FOUND;
    //			}
    //			leftOfLeftBtreePage = getLatchedPage(btreeTable, db, parentLeftSiblingPage->nextPageId, tx);
    //			if (leftOfLeftBtreePage == NULL) {
    //				return GNCDB_NOT_FOUND;
    //			}

    //			leftOfLeftBtreePage->nextPageId = rightBtreePage->page.id;
    //		}
    //	}

    //	/* 3.2.将leftBtreePage添加到deletedPageSet中 */
    //	rc = addIntoDeletedPageSet(tx, (Page*)leftBtreePage);
    //	if (rc != GNCDB_SUCCESS) {
    //		return rc;
    //	}
    //}else {
    /* 右兄弟合并到左兄弟 */
    /* 3.1.更新entryNum，并进行合并 */
    memcpy(leftBtreePage->page.pData + PAGE_HEAD_SIZE + leftBtreePage->entryNum * btreeTable->leafRecordLength,
        rightBtreePage->page.pData + PAGE_HEAD_SIZE,
        rightBtreePage->entryNum * btreeTable->leafRecordLength);
    leftBtreePage->entryNum += rightBtreePage->entryNum;
    // rc = varArrayListRemoveHead(rightBtreePage->entryArray, leftBtreePage->entryArray, rightBtreePage->entryNum-1);
    // if (rc != GNCDB_SUCCESS) {
    // 	return rc;
    // }

    /* 3.2.保留的是leftBtreePage，所以需要将leftBtreePage->nextPageId重置 */
    leftBtreePage->nextPageId = rightBtreePage->nextPageId;

    /* 3.3.将rightBtreePage添加到deletedPageSet中 */
    rc = addIntoDeletedPageSet(tx, (Page *)rightBtreePage);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    // }
  } else { /* 内部页的合并 */
    // if (isFirstToSecond) { /* 左兄弟合并到右兄弟 */
    //	/* 3.1.修改leftBtreePage对应的孩子页parentPageId为rightBtreePage */
    //	for (index = 0; index < leftBtreePage->entryNum; index++) {
    //		internalEntry = varArrayListGetPointer(leftBtreePage->entryArray, index);
    //		rc = updateParentPointer(btreeTable, internalEntry->childPageId, db, tx, rightBtreePage->page.id);
    //		if (rc != GNCDB_SUCCESS) {
    //			return rc;
    //		}
    //	}

    //	/* 3.2.合并 */
    //	rightBtreePage->entryNum += leftBtreePage->entryNum;
    //	rc = varArrayListRemoveTail(leftBtreePage->entryArray, rightBtreePage->entryArray, 0);
    //	if (rc != GNCDB_SUCCESS) {
    //		return rc;
    //	}

    //	/* 3.3.将leftBtreePage添加到deletedPageSet中 */
    //	rc = addIntoDeletedPageSet(tx, (Page*)leftBtreePage);
    //	if (rc != GNCDB_SUCCESS) {
    //		return rc;
    //	}
    //}else { /* 右兄弟合并到左兄弟 */

    /* 3.2.合并 */
    memcpy(leftBtreePage->page.pData + PAGE_HEAD_SIZE + leftBtreePage->entryNum * btreeTable->internalRecordLength,
        rightBtreePage->page.pData + PAGE_HEAD_SIZE,
        rightBtreePage->entryNum * btreeTable->internalRecordLength);
    leftBtreePage->entryNum += rightBtreePage->entryNum;
    // rc = varArrayListRemoveHead(rightBtreePage->entryArray, leftBtreePage->entryArray, rightBtreePage->entryNum - 1);
    // if (rc != GNCDB_SUCCESS) {
    // 	return rc;
    // }

    /* 3.3.保留的是leftBtreePage，所以需要将leftBtreePage->nextPageId重置 */
    leftBtreePage->nextPageId = rightBtreePage->nextPageId;

    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 3.4.将rightBtreePage添加到deletedPageSet中 */
    rc = addIntoDeletedPageSet(tx, (Page *)rightBtreePage);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    // }
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief 在左右兄弟页中借一个tuple或者entry
 * @param btreeTable
 * @param parentBtreePage 父页
 * @param leftBtreePage 左页
 * @param rightBtreePage 右页
 * @param tableSchema
 * @param db
 * @param tx 事务
 */
int btreeTableBorrowEntry(struct BtreeTable *btreeTable, struct BtreePage *parentBtreePage,
    struct BtreePage *leftBtreePage, BYTE *parentInternalRecord, struct BtreePage *rightBtreePage,
    struct TableSchema *tableSchema, struct GNCDB *db, struct Transaction *tx)
{
  /* 1.变量的定义 */
  int   rc                = 0;
  BYTE *leafRecord        = NULL;
  int   childPageId       = 0;
  BYTE *newInternalRecord = NULL;
  BYTE *internalRecord    = NULL;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || parentBtreePage == NULL || leftBtreePage == NULL || parentInternalRecord == NULL ||
      rightBtreePage == NULL || tableSchema == NULL || db == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.如果是叶子节点 */
  if (leftBtreePage->page.pageType == LEAF_PAGE) {
    if (leftBtreePage->entryNum > rightBtreePage->entryNum) { /* 从左边借 */
      /* 3.1.从leftBtreePage中借一个tuple添加到rightBtreePage */
      memmove(rightBtreePage->page.pData + PAGE_HEAD_SIZE + btreeTable->leafRecordLength,
          rightBtreePage->page.pData + PAGE_HEAD_SIZE,
          rightBtreePage->entryNum * btreeTable->leafRecordLength);
      memcpy(rightBtreePage->page.pData + PAGE_HEAD_SIZE,
          leftBtreePage->page.pData + PAGE_HEAD_SIZE + (leftBtreePage->entryNum - 1) * btreeTable->leafRecordLength,
          btreeTable->leafRecordLength);

      /* 3.2.修改leftBtreePage和rightBtreePage中的entryNum */
      leftBtreePage->entryNum--;
      rightBtreePage->entryNum++;
    } else { /* 从右边借 */
      /* 3.1.从rightBtreePage借一个添加到leftBtreePage */
      memcpy(leftBtreePage->page.pData + PAGE_HEAD_SIZE + leftBtreePage->entryNum * btreeTable->leafRecordLength,
          rightBtreePage->page.pData + PAGE_HEAD_SIZE,
          btreeTable->leafRecordLength);
      memmove(rightBtreePage->page.pData + PAGE_HEAD_SIZE,
          rightBtreePage->page.pData + PAGE_HEAD_SIZE + btreeTable->leafRecordLength,
          (rightBtreePage->entryNum - 1) * btreeTable->leafRecordLength);

      /* 3.2.修改leftBtreePage和rightBtreePage中的entryNum */
      leftBtreePage->entryNum++;
      rightBtreePage->entryNum--;
    }

    /* 3.3.用rightBtreePage目前的第一个keyValueArray(即(*tuple)->keyValueArray)更新父页中对应的internalEntry（需要做深拷贝）
     */
    memcpy(&childPageId, parentInternalRecord, INT_SIZE);
    /* 将parentInternalEntry从parentBtreePage中删除，并释放空间 */
    // rc = internalPageDeleteRecord(parentBtreePage, parentInternalRecord, tableSchema, btreeTable->tableName,
    // db->catalog,btreeTable);
    leafRecord = rightBtreePage->page.pData + PAGE_HEAD_SIZE;

    /* 深拷贝 */
    newInternalRecord =
        internalRecordDeepCopyFromLeafRecord(childPageId, btreeTable->tableName, btreeTable, tableSchema, leafRecord);
    if (newInternalRecord == NULL) {
      return GNCDB_SPACE_LACK;
    }

    /* 添加 */
    // rc = internalPageInsertRecord(parentBtreePage, parentInternalRecord, tableSchema, btreeTable, db->catalog);
    rc = internalPageUpdateRecord(
        parentBtreePage, parentInternalRecord, newInternalRecord, tableSchema, btreeTable, db->catalog);
    if (rc != GNCDB_SUCCESS) {
      if (newInternalRecord != NULL) {
        my_free(newInternalRecord);
      }
      return rc;
    }
    if (newInternalRecord != NULL) {
      my_free(newInternalRecord);
    }
  } else {                                                    /* 内部节点 */
    if (leftBtreePage->entryNum > rightBtreePage->entryNum) { /* 从左边借 */
      /* 3.1.用leftBtreePage的最右边孩子页号构造一个新的internalEntry */
      newInternalRecord = internalRecordDeepCopy(leftBtreePage->nextPageId, btreeTable, parentInternalRecord);

      /* 3.2.将newInternalEntry插入到rightBtreePage中 */
      rc = internalPageInsertRecord(rightBtreePage, newInternalRecord, tableSchema, btreeTable, db->catalog);
      if (rc != GNCDB_SUCCESS) {
        if (newInternalRecord != NULL) {
          my_free(newInternalRecord);
        }
        return rc;
      }
      if (newInternalRecord != NULL) {
        my_free(newInternalRecord);
      }

      /* 3.3.将leftBtreePage的最右边孩子页的页号设置为被移除之前的最后一个internalEntry的孩子页号 */
      internalRecord =
          leftBtreePage->page.pData + PAGE_HEAD_SIZE + (leftBtreePage->entryNum - 1) * btreeTable->internalRecordLength;
      memcpy(&leftBtreePage->nextPageId, internalRecord, INT_SIZE);

      /* 3.4.更新父页中的parentInternalEntry */
      memcpy(&childPageId, parentInternalRecord, INT_SIZE);
      /* 将parentInternalEntry从parentBtreePage中删除，并释放空间 */
      /* todo 释放parentInternalEntry的空间 */
      // rc = internalPageDeleteRecord(parentBtreePage, parentInternalRecord, tableSchema, btreeTable->tableName,
      // db->catalog,btreeTable); if (rc != GNCDB_SUCCESS) { 	return rc;
      // }

      /* 3.5.深拷贝 */
      newInternalRecord = internalRecordDeepCopy(childPageId, btreeTable, internalRecord);
      if (newInternalRecord == NULL) {
        return GNCDB_SPACE_LACK;
      }

      /* 3.6.更新 */
      // rc = internalPageInsertRecord(parentBtreePage, parentInternalRecord, tableSchema, btreeTable, db->catalog);
      // if (rc != GNCDB_SUCCESS) {
      // 	if(parentInternalRecord != NULL){
      // 		my_free(parentInternalRecord);
      // 	}
      // 	return rc;
      // }
      rc = internalPageUpdateRecord(
          parentBtreePage, parentInternalRecord, newInternalRecord, tableSchema, btreeTable, db->catalog);
      if (rc != GNCDB_SUCCESS) {
        if (newInternalRecord != NULL) {
          my_free(newInternalRecord);
        }
        return rc;
      }
      if (newInternalRecord != NULL) {
        my_free(newInternalRecord);
      }

      /* 3.7.将leftBtreePage中的最后一个internalEntry删除 */
      leftBtreePage->entryNum--;
      // rc = internalPageDeleteRecord(leftBtreePage, internalRecord, tableSchema, btreeTable->tableName,
      // db->catalog,btreeTable); if (rc != GNCDB_SUCCESS) { 	return rc;
      // }
    } else { /* 从右边借 */
      /* 3.1.获取rightBtreePage中第一个internalEntry */
      internalRecord = rightBtreePage->page.pData + PAGE_HEAD_SIZE;
      if (internalRecord == NULL) {
        return GNCDB_NOT_FOUND;
      }

      /* 3.2.用leftBtreePage最右边的孩子页号构建一个新的internalEntry,并用parentInternalEntry的keyValueArray设置主键值
       */
      newInternalRecord = internalRecordDeepCopy(leftBtreePage->nextPageId, btreeTable, parentInternalRecord);
      if (newInternalRecord == NULL) {
        return GNCDB_SPACE_LACK;
      }

      /* 3.3.将newInternalEntry添加到leftBtreePage中 */
      rc = internalPageInsertRecord(leftBtreePage, newInternalRecord, tableSchema, btreeTable, db->catalog);
      if (rc != GNCDB_SUCCESS) {
        if (newInternalRecord != NULL) {
          my_free(newInternalRecord);
        }
        return rc;
      }
      if (newInternalRecord != NULL) {
        my_free(newInternalRecord);
      }
      memcpy(&leftBtreePage->nextPageId, internalRecord, sizeof(int));

      /* 3.4.用rightInternalEntry的keyValueArray更新parentInternalEntry的keyValueArray */
      memcpy(&childPageId, parentInternalRecord, sizeof(int));
      /* 将parentInternalEntry从parentBtreePage中删除，并释放空间 */
      /* todo 释放parentInternalEntry的空间 */
      // rc = internalPageDeleteRecord(parentBtreePage, parentInternalRecord, tableSchema, btreeTable->tableName,
      // db->catalog,btreeTable); if (rc != GNCDB_SUCCESS) { 	return rc;
      // }

      /* 3.5.深拷贝 */
      newInternalRecord = internalRecordDeepCopy(childPageId, btreeTable, internalRecord);
      if (newInternalRecord == NULL) {
        return GNCDB_SPACE_LACK;
      }

      /* 3.6.添加 */
      // rc = internalPageInsertRecord(parentBtreePage, parentInternalRecord, tableSchema, btreeTable, db->catalog);
      // if (rc != GNCDB_SUCCESS) {
      // 	if(parentInternalRecord != NULL){
      // 		my_free(parentInternalRecord);
      // 	}
      // 	return rc;
      // }
      rc = internalPageUpdateRecord(
          parentBtreePage, parentInternalRecord, newInternalRecord, tableSchema, btreeTable, db->catalog);
      if (rc != GNCDB_SUCCESS) {
        if (newInternalRecord != NULL) {
          my_free(newInternalRecord);
        }
        return rc;
      }
      if (newInternalRecord != NULL) {
        my_free(newInternalRecord);
      }

      /* 3.7.将rightBtreePage中第一个internalEntry删除 */
      /* todo 释放rightInternalEntry的空间 */
      rc = internalPageDeleteRecord(
          rightBtreePage, internalRecord, tableSchema, btreeTable->tableName, db->catalog, btreeTable);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }
  }

  return GNCDB_SUCCESS;
}

/*********************************************************************************btreeTable_Update*******************************************************************/

/**
 * @brief 在B+树中根据判断条件更新元组
 * @param btreeTable
 * @param keyValueArray	主键值
 * @param fieldNameArray	字段名称
 * @param fieldValueArray	字段值
 * @param tableSchema
 * @param tuple
 * @param db
 * @param tx 事务
 */
int btreeTableUpdateTuple(struct BtreeTable *btreeTable, struct varArrayList *fieldNameArray,
    struct varArrayList *fieldValueArray, struct TableSchema *tableSchema, BYTE *record, struct GNCDB *db,
    struct Transaction *tx, struct BtreePage *btreePage)
{
  /* 1.变量的定义 */
  int     rc             = 0;
  int    *intValue       = NULL;
  double *doubleValue    = NULL;
  char   *stringValue    = NULL;
  int    *overflowPageId = NULL;
  int    *blobSize       = NULL;

  char   *fieldName = NULL;
  int     index = 0, i = 0, length = 0;
  Column *column      = NULL;
  BYTE   *updateTuple = NULL;
  bool    fieldIsNull = false;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || fieldNameArray == NULL || fieldValueArray == NULL || tableSchema == NULL ||
      record == NULL || db == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, btreePage->page.id, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 3.获取需要更新的页 */
  rc = setPageStatusPinUp(db->pagePool, btreePage->page.id, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, btreePage->page.id, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  LOG(LOG_TRACE, "WLOCKing:PAGEid=%d", btreePage->page.id);
  WriteLock(&btreePage->page.rwlock_t);
  LOG(LOG_TRACE, "WLOCKend:PAGEid=%d", btreePage->page.id);
  rc = addIntoLatchedPageSet(tx, (void *)btreePage);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 5.对需要更改的页进行备份 */
  rc = backUpLatchedPage(db, tx);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  updateTuple = record;
  if (updateTuple == NULL) {
    return GNCDB_NOT_FOUND;
  }

  /* 6.对指定的字段进行修改 */
  for (i = 0; i < fieldNameArray->elementCount; i++) {
    fieldName = (char *)varArrayListGetPointer(fieldNameArray, i);
    if (fieldName == NULL) {
      return GNCDB_NOT_FOUND;
    }
    fieldIsNull = false;
    index       = columnFindFieldGet(tableSchema->columnList, fieldName);
    if (index < 0) {
      return GNCDB_NOT_FOUND;
    }
    column = (Column *)varArrayListGetPointer(tableSchema->columnList, index);
    if (column == NULL) {
      return GNCDB_NOT_FOUND;
    }

    switch (column->fieldType) {
      case FIELDTYPE_INTEGER: {
        intValue = (int *)varArrayListGetPointer(fieldValueArray, i);
        if (intValue == NULL) {
          fieldIsNull = true;
        }
        memcpy(updateTuple + column->offset, intValue, sizeof(int));
        break;
      }
      case FIELDTYPE_REAL: {
        doubleValue = (double *)varArrayListGetPointer(fieldValueArray, i);
        if (doubleValue == NULL) {
          fieldIsNull = true;
        }
        memcpy(updateTuple + column->offset, doubleValue, sizeof(double));
        break;
      }
      case FIELDTYPE_VARCHAR: {
        stringValue = (char *)varArrayListGetPointer(fieldValueArray, i);
        if (stringValue == NULL) {
          fieldIsNull = true;
        }

        length = strlen(stringValue);

        memcpy(updateTuple + column->offset, stringValue, length);
        memset(updateTuple + column->offset + length, '\0', 1);
        break;
      }
      case FIELDTYPE_BLOB: {
        overflowPageId = (int *)varArrayListGetPointer(fieldValueArray, i);
        blobSize       = (int *)varArrayListGetPointer(fieldValueArray, i + 1);
        if (overflowPageId == NULL) {
          fieldIsNull = true;
        }
        memcpy(updateTuple + column->offset, overflowPageId, sizeof(int));
        memcpy(updateTuple + column->offset + sizeof(int), blobSize, sizeof(int));
        break;
      }
      default: break;
    }

    /* 修改元组的空位图（bitMap） */
    if (fieldIsNull) {
      rc = leafRecordSetBitMap(updateTuple, index, 0);
    } else {
      rc = leafRecordSetBitMap(updateTuple, index, 1);
    }
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  /* 7.unlatched/unpin针对解pin需要在事务提交或者回滚时解pin */
  rc = releaseWLatches(db->pagePool, tx, true, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return GNCDB_SUCCESS;
}

/*********************************************************************************btreeTable_DropTable*******************************************************************/

/**
 * @brief 将B+树所有节点销毁，销毁B+树
 * @param btreeTable
 * @param db
 * @param tx 事务
 */
/* todo ??? 需要在master表中加一列属性关于数据表是否被删除的标记，因为在删除表时如果中途失败了，
不能无法通过内存的oldData进行回滚，因此这里通过加上一个标记来实现数据表的删除，后期通过一个离线的删除接口将被标记删除的表回收，
mysql中的vacuum就是这个类似的功能
这里关于页unpin的操作需要另外考虑
*/
int btreeTableDropTable(struct BtreeTable *btreeTable, struct GNCDB *db, struct Transaction *tx)
{
  /* 1.变量的定义 */
  varArrayList *pageIdArray = NULL;
  BtreePage    *btreePage   = NULL;
  // InternalEntry* internalEntry = NULL;
  BYTE *internalEntry = NULL;
  // Tuple* tuple = NULL;
  BYTE        *record      = NULL;
  TableSchema *tableSchema = NULL;
  int         *pageId      = NULL;
  int          childPageId = 0;
  int          pId         = 0;
  int          rc = 0, i = 0, j = 0;
  int          currentNodeCount = 0; /* 记录当前层节点数 */

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || db == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.如果根节点为空 */
  if (btreeTable->rootPageId == 0) {
    return GNCDB_SUCCESS;
  } else {
    /* 3.1.将根节点的页号添加到pageIdArray中 */
    pageIdArray = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (pageIdArray == NULL) {
      return GNCDB_SPACE_LACK;
    }
    rc = varArrayListAdd(pageIdArray, &btreeTable->rootPageId);
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pageIdArray);
      return rc;
    }

    /* 3.2.获取tableSchema */
    tableSchema = getTableSchema(db->catalog, btreeTable->tableName);
    if (tableSchema == NULL) {
      varArrayListDestroy(&pageIdArray);
      return rc;
    }

    /* 3.3.使用广度优先遍历算法 */
    while (pageIdArray->elementCount != 0) {
      /* 初始化当前层节点数 */
      currentNodeCount = pageIdArray->elementCount;
      for (i = 0; i < currentNodeCount; i++) {
        /* 3.3.1.每一次都从pageIdArray中拿出第一个页id */
        pageId = (int *)varArrayListGet(pageIdArray, 0);
        pId    = *pageId;
        if (pageId == NULL) {
          varArrayListDestroy(&pageIdArray);
          return GNCDB_NOT_FOUND;
        }

        /* 3.3.2.获取页 */
        rc = pagePoolGetPage((Page **)&btreePage, (*pageId), btreeTable->tableName, db);
        if (btreePage == NULL || rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return GNCDB_NOT_FOUND; /* 没有找到pageId对应的btreePage */
        }
        rc = produceOldPageData(db, (Page *)btreePage, btreePage->page.pageType, tx);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return rc;
        }
        setPageStatusPinDown(db->pagePool, (*pageId), NULL);
        /* 3.3.3判断btreePage是否为内部节点，如果是需要将该页所有的孩子页获取出来，并添加到pageIdArray中 */
        if (btreePage->page.pageType == INTERNAL_PAGE) {
          /* 获取btreePage所有孩子页的页号 */
          for (j = 0; j < btreePage->entryNum; j++) {
            // internalEntry = (InternalEntry*)varArrayListGetPointer(btreePage->entryArray, j);
            // if (internalEntry == NULL) {
            // 	varArrayListDestroy(&pageIdArray);
            // 	return GNCDB_NOT_FOUND;
            // }
            internalEntry = btreePage->page.pData + PAGE_HEAD_SIZE + j * btreeTable->internalRecordLength;

            memcpy(&childPageId, internalEntry, sizeof(int));
            // rc = varArrayListAdd(pageIdArray, &(internalEntry->childPageId));
            rc = varArrayListAdd(pageIdArray, &(childPageId));
            if (rc != GNCDB_SUCCESS) {
              varArrayListDestroy(&pageIdArray);
              return rc;
            }
          }

          /* 添加btreePage最右边的孩子页 */
          rc = varArrayListAdd(pageIdArray, &(btreePage->nextPageId));
          if (rc != GNCDB_SUCCESS) {
            varArrayListDestroy(&pageIdArray);
            return rc;
          }
        } else { /* 对于叶子页，需要判断对每个tuple中的Blob字段进行单独的处理 */
          for (j = 0; j < btreePage->entryNum; j++) {
            // tuple = (Tuple*)varArrayListGetPointer(btreePage->entryArray,j);
            record = btreePage->page.pData + PAGE_HEAD_SIZE + j * btreeTable->leafRecordLength;
            if (record == NULL) {
              varArrayListDestroy(&pageIdArray);
              return GNCDB_NOT_FOUND;
            }

            /* 需要对tuple中的Blob内容单独进行删除操作 */
            rc = deleteBlobInTuple(record, tableSchema);
            if (rc != GNCDB_SUCCESS) {
              return rc;
            }
          }
        }
        /* 3.3.4.将pageId从pageIdArray中剔除 */
        rc = varArrayListRemove(pageIdArray, &pId);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return rc;
        }
        /* 3.3.5.将btreePage转化为freePage，然后将freePage添加到pagePool中，再将btreePage从pagePool剔除并销毁 */
        rc = btreePageToFreePage(btreePage, db);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return rc;
        }
      }
    }

    /* 销毁pageIdArray */
    varArrayListDestroy(&pageIdArray);
    /* todo 目前没有采用vacuum策略，保证内存安全先销毁tableshema */
    tableSchemaDestroy(tableSchema);
  }

  return GNCDB_SUCCESS;
}

/*********************************************************************************btreeTable_Load*******************************************************************/

/**
 * @brief 将B+树的所有页加载到内存中
 * @param btreeTable
 * @param pageId
 * @param db
 */
int btreeTableDfsTraversal(struct BtreeTable *btreeTable, struct GNCDB *db)
{
  /* 1.变量的定义 */
  BtreePage *btreePage = NULL;
  // InternalEntry* internalEntry = NULL;
  BYTE         *internalEntry = NULL;
  int          *pageId        = NULL;
  int           pId           = 0;
  int           rc = 0, i = 0, j = 0;
  int           childPageId      = 0;
  int           currentNodeCount = 0; /* 记录当前层节点数 */
  varArrayList *pageIdArray      = NULL;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.如果根节点为空 */
  if (btreeTable->rootPageId == 0) {
    return GNCDB_SUCCESS;
  } else {
    /* 3.1.将根节点的页号添加到pageIdArray中 */
    pageIdArray = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (pageIdArray == NULL) {
      return GNCDB_SPACE_LACK;
    }
    rc = varArrayListAdd(pageIdArray, &(btreeTable->rootPageId));
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pageIdArray);
      return rc;
    }

    /* 3.2.使用广度优先遍历算法加载B+树 */
    while (pageIdArray->elementCount != 0) {
      /* 初始化当前层节点数 */
      currentNodeCount = pageIdArray->elementCount;
      for (i = 0; i < currentNodeCount; i++) {
        /* 3.2.1每一次都从pageIdArray中拿出第一个页id */
        pageId = (int *)varArrayListGet(pageIdArray, 0);
        if (pageId == NULL) {
          varArrayListDestroy(&pageIdArray);
          return GNCDB_NOT_FOUND;
        }
        pId = *pageId;

        /* 3.2.2.获取页 */
        rc = pagePoolGetPage((Page **)&btreePage, (*pageId), btreeTable->tableName, db);
        if (btreePage == NULL || rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return GNCDB_NOT_FOUND; /* 没有找到pageId对应的btreePage */
        }

        /* 3.2.3.判断btreePage是否为内部节点，如果是需要将该页所有的孩子页获取出来，并添加到pageIdArray中 */
        if (btreePage->page.pageType == INTERNAL_PAGE) {
          /* 获取btreePage所有孩子页的页号 */
          for (j = 0; j < btreePage->entryNum; j++) {
            // internalEntry = (InternalEntry*)varArrayListGetPointer(btreePage->entryArray, j);
            internalEntry = btreePage->page.pData + PAGE_HEAD_SIZE + j * btreeTable->internalRecordLength;
            if (internalEntry == NULL) {
              varArrayListDestroy(&pageIdArray);
              return GNCDB_NOT_FOUND;
            }
            memcpy(&childPageId, internalEntry, sizeof(int));
            // rc = varArrayListAdd(pageIdArray, &(internalEntry->childPageId));
            rc = varArrayListAdd(pageIdArray, &(childPageId));
            if (rc != GNCDB_SUCCESS) {
              varArrayListDestroy(&pageIdArray);
              return rc;
            }
          }
          /* 添加btreePage最右边的孩子页 */
          rc = varArrayListAdd(pageIdArray, &(btreePage->nextPageId));
          if (rc != GNCDB_SUCCESS) {
            varArrayListDestroy(&pageIdArray);
            return rc;
          }
        }

        rc = setPageStatusPinDown(db->pagePool, btreePage->page.id, NULL);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }

        /* 将pageId从pageIdArray中剔除 */
        rc = varArrayListRemove(pageIdArray, &pId);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return rc;
        }
      }
    }

    /* 销毁pageIdArray */
    varArrayListDestroy(&pageIdArray);
  }

  return GNCDB_SUCCESS;
}

/*********************************************************************************blob*******************************************************************/
/*
对溢出页做操作时，必定是先通过找到存放blob数据的这条tuple记录，那么对blob数据的访问就是先对存放这条记录的叶子页先加读锁或者写锁，
因此对溢出页的访问是线程安全的，不需要额外对溢出页做加锁的操作。

todo ???
因为Blob数据需要的页数比较多，有可能内存空间是无法一次性将所有的一个Blob数据的所有溢出页都放到内存中，因此如果使用内存回滚的方式可能是无法满足这一要求的。
是否可以考虑对于溢出页的操作统一记录undo日志，如果发生了操作失败，则使用undo日志进行回滚操作。
*/

/// <summary>
/// 需要判断tuple的哪些列为Blob类型，如果为Blob类型，判断有没有对应的溢出页需要删除
/// </summary>
/// <param name="tuple"></param>
/// <param name="tableSchema"></param>
/// <param name="db"></param>
/// <param name="tx"></param>
/// <returns></returns>
// int deleteBlobInTuple(struct Tuple* tuple,struct TableSchema* tableSchema) {
int deleteBlobInTuple(BYTE *record, struct TableSchema *tableSchema)
{
  /*1.变量的定义 */
  int     index  = 0;
  Column *column = NULL;
  // BlobField* blobField = NULL;
  int overflowPageId = 0;

  /* 2.判断参数是否为空 */
  if (record == NULL || tableSchema == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.需要判断该tuple是否包含Blob字段，如果有的话，需要依次找出来进行删除 */
  for (index = 0; index < tableSchema->columnNum; index++) {
    column = (Column *)varArrayListGetPointer(tableSchema->columnList, index);
    if (column == NULL) {
      return GNCDB_NOT_FOUND;
    }

    /* 判断是否是Blob字段 */
    if (column->fieldType == FIELDTYPE_BLOB) {
      // blobField = (BlobField*)varArrayListGetPointer(tuple->fieldArray, index);
      // if (blobField == NULL) {
      // 	return GNCDB_NOT_FOUND;
      // }
      memcpy(&overflowPageId, record + column->offset, INT_SIZE);
      if (overflowPageId != 0) {
        return GNCDB_BLOB_NO_DELETE;
      }
      // if (blobField->overflowPageId != 0)
      // {
      // 	return GNCDB_BLOB_NO_DELETE;
      // }
    }
  }

  return GNCDB_SUCCESS;
}

/// <summary>
/// 在setBlob之前需要检查tuple对应的Blob字段存储的溢出页是否为0，如果不为0的话需要将overflowPage全部转化为freePage
/// </summary>
/// <param name="tuple"></param>
/// <param name="columnNum"></param>
/// <param name="db"></param>
/// <param name="tx"></param>
/// <returns></returns>
// int checkBlobAndDeleteOverflowPage(struct Tuple* tuple, int columnNum, struct GNCDB* db,struct Transaction* tx) {
int checkBlobAndDeleteOverflowPage(BYTE *blobField, int columnNum, struct GNCDB *db, struct Transaction *tx)
{
  /* 1.变量的定义 */
  // BlobField* blobField = NULL;
  int rc                 = 0;
  int overflowPageId     = 0;
  int currOverflowPageId = 0;
  // int index = 0;
  OverflowPage *overflowPage   = NULL;
  int           overFlowPageId = 0;
  // varArrayList* overflowPageSet = NULL;

  /* 2.判断参数是否为空 */
  if (blobField == NULL || db == NULL) {
    // if (tuple == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取blob列 */
  // blobField = (BlobField*)varArrayListGetPointer(tuple->fieldArray, columnNum);
  // if (blobField == NULL) {
  // 	return GNCDB_NOT_FOUND;
  // }

  /* 4.判断溢出页是否为0 */
  memcpy(&overFlowPageId, blobField, INT_SIZE);
  if (overFlowPageId != 0) {
    // if (blobField->overflowPageId != 0) {
    // overflowPageId = blobField->overflowPageId;
    while (overflowPageId != 0) {
      rc = pagePoolGetPage((Page **)&overflowPage, overflowPageId, NULL, db);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }

      /* 页删除之前进行备份，用于后续可能的回滚操作 */
      rc = produceOldPageData(db, (Page *)overflowPage, OVERFLOW_PAGE, tx);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }

      /* 事务记录自己修改过哪些溢出页 */
      rc = addIntoOverflowPageSet(tx, (Page *)overflowPage);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
      currOverflowPageId = overflowPageId;
      /* 记录一下overflowPage->nextPageId */
      overflowPageId = overflowPage->nextPageId;
      rc             = setPageStatusPinDown(db->pagePool, currOverflowPageId, NULL);
      if (rc != GNCDB_SUCCESS) {
        return rc;
      }
    }

    /* 5.获取溢出页集合，并做unpin操作 */
    //		overflowPageSet = getOverflowPageSet(tx);
    //		for(index = 0;index < overflowPageSet->elementCount;index++){
    //			overflowPage = (OverflowPage*)varArrayListGetPointer(overflowPageSet,index);
    //			if(overflowPageSet == NULL){
    //				return GNCDB_NOT_FOUND;
    //			}
    //			rc = overflowPageToFreePage(overflowPage, db);
    //			if (rc != GNCDB_SUCCESS) {
    //				return rc;
    //			}
    //		}
  }

  return GNCDB_SUCCESS;
}

/// <summary>
/// 设置对应tuple中的Blob字段内容（溢出页页头后的前4个字节用于存储当前溢出页存储了多少字节的溢出内容）
/// </summary>
/// <param name="db"></param>
/// <param name="btreeTable"></param>
/// <param name="buf"></param>
/// <param name="size"></param>
/// <param name="tid"></param>
/// <param name="overflowPageIdArray"></param>
/// <param name="tx"></param>
int btreeTableSetBlob(struct GNCDB *db, struct BtreeTable *btreeTable, BYTE *buf, int size,
    struct varArrayList *overflowPageIdArray, struct Transaction *tx)
{
  /// <returns></returns>
  /* 1.变量的定义 */
  int           rc                   = 0;
  int           offset               = 0;
  int           overflowPageId       = 0;
  int          *currOverflowPageId   = NULL;
  int           index                = 0;
  OverflowPage *previousOverflowPage = NULL;
  OverflowPage *overflowPage         = NULL;
  // varArrayList* overflowPageSet = NULL;
  int len      = 0;
  int copySize = 0; /* 一次性复制到溢出页中的最大字节数 */

  /* 2.判断参数是否为空 */
  if (db == NULL || btreeTable == NULL || buf == NULL || overflowPageIdArray == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.创建overflowPage保存数据 */
  while (size != 0) {

    rc = pagePoolCreateOverflowPage(&overflowPage, db, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 设置页修改之前的状态和信息 */
    rc = produceOldPageData(db, (Page *)overflowPage, FREE_PAGE, tx);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    /* 设置前面一个溢出页的nextPageId */
    if (previousOverflowPage != NULL) {
      previousOverflowPage->nextPageId = overflowPage->page.id;
    }

    /* 将buf中的数据复制到对应的溢出页中(溢出页页头后的前4个字节用于存储当前溢出页存储了多少字节的溢出内容) */
    copySize = (db->pageCurrentSize) - OVERFLOWPAGE_HEAD_SIZE - INT_SIZE;
    if (size < copySize) {
      copySize = size;
    }
    overflowPage->overflowData = (BYTE *)my_malloc(copySize + INT_SIZE);
    if (overflowPage->overflowData == NULL) {
      return GNCDB_SPACE_LACK;
    }

    writeInt(copySize, overflowPage->overflowData, &len);
    memcpy(overflowPage->overflowData + INT_SIZE, buf + offset, copySize);
    offset += copySize;
    size -= copySize;

    /* 将溢出页的页号添加到overflowPageIdArray中 */
    rc = varArrayListAdd(overflowPageIdArray, &overflowPage->page.id);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    /* 设置为脏页 */
    rc = setPageStatusDirty(db->pagePool, overflowPage->page.id, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
    previousOverflowPage = overflowPage;

    /* 事务记录自己修改过哪些溢出页 */
    // rc = addIntoOverflowPageSet(tx,(Page*)overflowPage);
    // if (rc != GNCDB_SUCCESS) {
    // 	return rc;
    // }
  }

  /* 5.获取溢出页集合，并做unpin操作 */
  // overflowPageSet = getOverflowPageSet(tx);
  for (index = 0; index < overflowPageIdArray->elementCount; index++) {
    currOverflowPageId = varArrayListGet(overflowPageIdArray, index);
    if (currOverflowPageId == NULL) {
      return GNCDB_NOT_FOUND;
    }
    overflowPageId = *currOverflowPageId;
    /* unpin操作 */
    /* @todo 考虑是否需要挪动到事务提交之前才unpin ？？？*/
    rc = setPageStatusPinDown(db->pagePool, overflowPageId, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/// <summary>
/// 获取对应tuple中Blob字段的内容
/// </summary>
/// <param name="db"></param>
/// <param name="btreeTable"></param>
/// <param name="tuple"></param>
/// <param name="columnNum">列号</param>
/// <param name="size">Blob数据的字节数</param>
/// <param name="tid"></param>
/// <param name="buf">存放Blob数据的缓存区</param>
/// <returns></returns>
int btreeTableGetBlob(
    struct GNCDB *db, struct BtreeTable *btreeTable, BYTE *blobField, int columnNum, int size, BYTE *buf)
{
  /* 1.变量的定义 */
  int rc = 0;
  // BlobField* blobField = NULL;
  int           overflowPageId = 0;
  OverflowPage *overflowPage   = NULL;
  int           offset         = 0;
  int           copySize       = 0;
  int           len            = 0;
  int           blobSize       = 0;

  /* 2.判断参数是否为空 */
  if (db == NULL || btreeTable == NULL || blobField == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取blob数据 */
  // blobField = (BlobField*)varArrayListGetPointer(tuple->fieldArray, columnNum);
  // if (blobField == NULL ) {
  // 	return GNCDB_NOT_FOUND;
  // }
  memcpy(&blobSize, blobField + INT_SIZE, INT_SIZE);
  if (blobSize == 0) {
    return GNCDB_BLOB_NOT_EXIST;
  }
  memcpy(&overflowPageId, blobField, INT_SIZE);
  // overflowPageId = blobField->overflowPageId;
  /* 4.依次获取溢出页，并将数据复制到缓存区buf中 */
  while (overflowPageId != 0) {
    rc = pagePoolGetPage((Page **)&overflowPage, overflowPageId, NULL, db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 读取当前溢出页存放的blob数据的字节数 */
    readInt(&copySize, overflowPage->overflowData, &len);
    if (size < copySize) {
      /* todo? 如果出现了size < copySize的情况说明输入的数据有问题，是否需要加一个这样的判断 */
      copySize = size;
    }

    /* 获取对应的内容 */
    memcpy(buf + offset, overflowPage->overflowData + INT_SIZE, copySize);
    offset += copySize;
    size -= copySize;

    /* 获取下一个溢出页页号 */
    overflowPageId = overflowPage->nextPageId;

    rc = setPageStatusPinDown(db->pagePool, overflowPage->page.id, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

int deleteOverflowPages(struct PagePool *pagePool, struct Transaction *tx, struct GNCDB *db)
{
  /* 1.变量的定义 */
  varArrayList *overflowPageSet = NULL;
  int           rc = 0, size = 0, count = 0;
  OverflowPage *overflowPage = NULL;

  /* 2.判断参数是否为空 */
  if (pagePool == NULL || tx == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 3.将遍历，将页销毁 */
  overflowPageSet = tx->overflowPageSet;

  size = varArrayListGetCount(overflowPageSet);
  while (size != 0) {
    /* 获取页 */
    overflowPage = varArrayListGetPointer(overflowPageSet, 0);

    /* 将页销毁，即转换为FreePage */
    rc = overflowPageToFreePage(overflowPage, db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 将页从集合中踢出 */
    rc = varArrayListRemoveByIndexPointer(overflowPageSet, 0);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 计数 */
    count++;
    if (count == size) {
      break;
    }
  }

  return GNCDB_SUCCESS;
}

int btreeTableGetText(
    struct GNCDB *db, struct BtreeTable *btreeTable, struct Tuple *tuple, int columnNum, int size, BYTE *buf)
{
  /* 1.变量的定义 */
  int           rc             = 0;
  TextField    *textField      = NULL;
  int           overflowPageId = 0;
  OverflowPage *overflowPage   = NULL;
  int           offset         = 0;
  int           copySize       = 0;
  int           len            = 0;

  /* 2.判断参数是否为空 */
  if (db == NULL || btreeTable == NULL || tuple == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取text数据 */
  textField = (TextField *)varArrayListGetPointer(tuple->fieldArray, columnNum);
  if (textField == NULL) {
    return GNCDB_NOT_FOUND;
  }
  overflowPageId = textField->overflowPageId;
  /* 4.依次获取溢出页，并将数据复制到缓存区buf中 */
  while (overflowPageId != 0) {
    rc = pagePoolGetPage((Page **)&overflowPage, overflowPageId, NULL, db);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }

    /* 读取当前溢出页存放的text数据的字节数 */
    readInt(&copySize, overflowPage->overflowData, &len);
    if (size < copySize) {
      /* todo? 如果出现了size < copySize的情况说明输入的数据有问题，是否需要加一个这样的判断 */
      copySize = size;
    }

    /* 获取对应的内容 */
    memcpy(buf + offset, overflowPage->overflowData + INT_SIZE, copySize);
    offset += copySize;
    size -= copySize;

    /* 获取下一个溢出页页号 */
    overflowPageId = overflowPage->nextPageId;

    rc = setPageStatusPinDown(db->pagePool, overflowPage->page.id, NULL);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

/*********************************************************************************printBtree*******************************************************************/

/**
 * @brief 将B+树打印输出到磁盘文件
 * @param btreeTable
 * @param pageId
 * @param db
 */
int printBtreeToFile(struct BtreeTable *btreeTable, struct GNCDB *db)
{
  if (btreeTable == NULL || db == NULL) {
    return GNCDB_PARAMNULL;
  }

  // 如果根节点为空
  if (btreeTable->rootPageId == 0) {
    return GNCDB_SUCCESS;
  } else {
    FILE         *fp          = NULL;
    varArrayList *pageIdArray = NULL;
    BtreePage    *btreePage   = NULL;
    // InternalEntry* internalEntry = NULL;
    // Tuple* tuple = NULL;
    BYTE *internalRecord = NULL;
    BYTE *leafRecord     = NULL;
    int  *pageId         = NULL;
    int   id             = 0;
    int   childPageId    = 0;

    int rc                 = 0;
    int internalPrintCount = 0;
    int currentNodeCount   = 0;  // 记录当前层节点数
    int totalNum           = 0;
    fp                     = fopen("./testfile/result/output.txt", "a+");  // 打开文件
    if (fp == NULL) {
      printf("open file failed!\n");
      return -1;
    }
    fseek(fp, 0, SEEK_END);  // 将文件指针定位到文件末尾

    pageIdArray = varArrayListCreate(DISORDER, sizeof(int), 0, intCompare, NULL);
    if (pageIdArray == NULL) {
      return GNCDB_SPACE_LACK;
    }
    rc = varArrayListAdd(pageIdArray, &btreeTable->rootPageId);  // 将根节点的页号添加到pageIdArray中
    if (rc != GNCDB_SUCCESS) {
      varArrayListDestroy(&pageIdArray);
      return rc;
    }

    fprintf(fp,
        "***************************************%s*************************************************\n",
        btreeTable->tableName);
    fflush(fp);
    while (pageIdArray->elementCount != 0) {
      // 初始化当前层节点数
      currentNodeCount = pageIdArray->elementCount;
      for (int i = 0; i < currentNodeCount; i++) {
        // 每一次都从pageIdArray中拿出第一个页id
        pageId = (int *)varArrayListGet(pageIdArray, 0);
        if (pageId == NULL) {
          varArrayListDestroy(&pageIdArray);
          return GNCDB_NOT_FOUND;
        }
        id = *pageId;
        // 获取页
        rc = pagePoolGetPage((Page **)&btreePage, id, btreeTable->tableName, db);
        if (btreePage == NULL || rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return GNCDB_NOT_FOUND;  // 没有找到pageId对应的btreePage
        }

        if (btreePage->page.pageType == INTERNAL_PAGE) {
          fprintf(fp, "InternalPage");
          fflush(fp);
        } else {
          fprintf(fp, "LeafPage");
          fflush(fp);
        }
        fprintf(fp,
            "[pageId = %d,nextId = %d,entryNum = %d]:\n",
            btreePage->page.id,
            btreePage->nextPageId,
            btreePage->entryNum);
        fflush(fp);
        // 判断btreePage是否为内部节点，如果是需要将该页所有的孩子页获取出来，并添加到pageIdArray中
        if (btreePage->page.pageType == INTERNAL_PAGE) {
          int index = 0;
          int k     = 0;
          // 获取btreePage所有孩子页的页号
          internalPrintCount = 0;
          for (index = 0; index < btreePage->entryNum; index++) {
            varArrayList *primaryTypeArray             = NULL;
            varArrayList *tablePrimaryKeyVarcharLenMap = NULL;
            int           offset                       = INT_SIZE;
            internalPrintCount++;
            if (internalPrintCount == 5) {
              internalPrintCount = 0;
              fprintf(fp, "\n");
              fflush(fp);
            }
            internalRecord = btreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->internalRecordLength;
            if (internalRecord == NULL) {
              varArrayListDestroy(&pageIdArray);
              return GNCDB_NOT_FOUND;
            }

            memcpy(&childPageId, internalRecord, INT_SIZE);
            rc = varArrayListAdd(pageIdArray, &(childPageId));
            if (rc != GNCDB_SUCCESS) {
              varArrayListDestroy(&pageIdArray);
              return rc;
            }

            fprintf(fp, "< childId = %d", childPageId);
            fflush(fp);
            LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyTypeMap");
            WriteLock(&(db->catalog->keyTypeLatch));
            LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyTypeMap");
            primaryTypeArray = hashMapGet(db->catalog->tablePrimaryKeyTypeMap, btreeTable->tableName);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyTypeMap");
            WriteUnLock(&(db->catalog->keyTypeLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyTypeMap");

            LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
            WriteLock(&(db->catalog->keyVarcharLenLatch));
            LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyVarcharLenMap");
            tablePrimaryKeyVarcharLenMap = hashMapGet(db->catalog->tablePrimaryKeyVarcharLenMap, btreeTable->tableName);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
            WriteUnLock(&(db->catalog->keyVarcharLenLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyVarcharLenMap");

            for (k = 0; k < primaryTypeArray->elementCount; k++) {
              FieldType *fieldType = (FieldType *)varArrayListGet(primaryTypeArray, k);
              switch (*fieldType) {
                case FIELDTYPE_INTEGER: {
                  int intValue = 0;
                  memcpy(&intValue, internalRecord + offset, INT_SIZE);
                  offset += INT_SIZE;
                  fprintf(fp, ",key = %d", intValue);
                  fflush(fp);
                  break;
                }
                case FIELDTYPE_REAL: {
                  double realValue = 0.0f;
                  memcpy(&realValue, internalRecord + offset, DOUBLE_SIZE);
                  offset += DOUBLE_SIZE;
                  fprintf(fp, ",key = %f", realValue);
                  fflush(fp);
                  break;
                }
                case FIELDTYPE_VARCHAR: {
                  int   varcharLen   = *(int *)varArrayListGet(tablePrimaryKeyVarcharLenMap, k);
                  char *varCharValue = (char *)my_malloc(varcharLen + 1);
                  if (varCharValue == NULL) {
                    varArrayListDestroy(&pageIdArray);
                    return GNCDB_SPACE_LACK;
                  }
                  memcpy(varCharValue, internalRecord + offset, varcharLen);
                  varCharValue[varcharLen] = '\0';  // 确保字符串以'\0'结尾
                  offset += varcharLen;

                  fprintf(fp, ",key = %s", varCharValue);
                  fflush(fp);
                  my_free(varCharValue);
                  break;
                }
                default: break;
              }
            }
            fprintf(fp, " >\t");
            fflush(fp);
          }
          // 添加btreePage最右边的孩子页
          rc = varArrayListAdd(pageIdArray, &(btreePage->nextPageId));
          if (rc != GNCDB_SUCCESS) {
            varArrayListDestroy(&pageIdArray);
            return rc;
          }
          fprintf(fp, "<childId=%d>\n", btreePage->nextPageId);
          fflush(fp);
        } else {
          int index = 0;
          totalNum += btreePage->entryNum;
          for (index = 0; index < btreePage->entryNum; index++) {
            varArrayList *primaryTypeArray             = NULL;
            varArrayList *tablePrimaryKeyOffsetMap     = NULL;
            varArrayList *tablePrimaryKeyVarcharLenMap = NULL;
            int           k                            = 0;
            leafRecord = btreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->leafRecordLength;
            // tuple = (Tuple*)varArrayListGetPointer(btreePage->entryArray, index);
            // if (tuple == NULL) {
            // 	varArrayListDestroy(&pageIdArray);
            // 	return GNCDB_NOT_FOUND;
            // }

            fprintf(fp, "< ");
            fflush(fp);
            LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
            WriteLock(&(db->catalog->keyIndexLatch));
            LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
            primaryTypeArray = hashMapGet(db->catalog->tablePrimaryKeyTypeMap, btreeTable->tableName);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
            WriteUnLock(&(db->catalog->keyIndexLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");

            LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyOffsetMap");
            WriteLock(&(db->catalog->keyOffsetLatch));
            LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyOffsetMap");
            tablePrimaryKeyOffsetMap = hashMapGet(db->catalog->tablePrimaryKeyOffsetMap, btreeTable->tableName);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyOffsetMap");
            WriteUnLock(&(db->catalog->keyOffsetLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyOffsetMap");

            LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
            WriteLock(&(db->catalog->keyVarcharLenLatch));
            LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyVarcharLenMap");
            tablePrimaryKeyVarcharLenMap = hashMapGet(db->catalog->tablePrimaryKeyVarcharLenMap, btreeTable->tableName);
            LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyVarcharLenMap");
            WriteUnLock(&(db->catalog->keyVarcharLenLatch));
            LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyVarcharLenMap");

            for (k = 0; k < primaryTypeArray->elementCount; k++) {
              FieldType *fieldType = (FieldType *)varArrayListGet(primaryTypeArray, k);
              switch (*fieldType) {
                case FIELDTYPE_INTEGER: {
                  int offset   = *(int *)varArrayListGet(tablePrimaryKeyOffsetMap, k);
                  int intValue = 0;
                  memcpy(&intValue, leafRecord + offset, INT_SIZE);
                  fprintf(fp, ",key = %d", intValue);
                  fflush(fp);
                  break;
                }
                case FIELDTYPE_REAL: {
                  int    offset    = *(int *)varArrayListGet(tablePrimaryKeyOffsetMap, k);
                  double realValue = 0.0f;
                  memcpy(&realValue, leafRecord + offset, DOUBLE_SIZE);
                  fprintf(fp, ",key = %f", realValue);
                  fflush(fp);
                  break;
                }
                case FIELDTYPE_VARCHAR: {
                  int   offset       = *(int *)varArrayListGet(tablePrimaryKeyOffsetMap, k);
                  int   varcharLen   = *(int *)varArrayListGet(tablePrimaryKeyVarcharLenMap, k);
                  char *varCharValue = (char *)my_malloc(varcharLen + 1);
                  if (varCharValue == NULL) {
                    varArrayListDestroy(&pageIdArray);
                    return GNCDB_SPACE_LACK;
                  }
                  memcpy(varCharValue, leafRecord + offset, varcharLen);
                  varCharValue[varcharLen] = '\0';

                  fprintf(fp, ",key = %s", varCharValue);
                  fflush(fp);
                  my_free(varCharValue);
                  break;
                }
                default: break;
              }
            }

            if (strcmp(btreeTable->tableName, SCHEMANAME) == 0) {
              // Field** field = (Field**)varArrayListGetPointer(tuple->fieldArray, 2);

              fprintf(fp, "tableName = %s ", "schema");
              fflush(fp);
            }

            fprintf(fp, ">\t");
            fflush(fp);
          }
          fprintf(fp, "\n");

          fflush(fp);
        }

        fprintf(fp, "\n");
        fflush(fp);
        rc = setPageStatusPinDown(db->pagePool, id, NULL);
        if (rc != GNCDB_SUCCESS) {
          return rc;
        }

        // 将pageId从pageIdArray中剔除
        rc = varArrayListRemove(pageIdArray, &id);
        if (rc != GNCDB_SUCCESS) {
          varArrayListDestroy(&pageIdArray);
          return rc;
        }
      }

      fprintf(fp, "================================================================\n");
      fflush(fp);
    }

    fprintf(fp,
        "******************************************************totalNum = "
        "%d**********************************************\n",
        totalNum);

    // 销毁pageIdArray
    varArrayListDestroy(&pageIdArray);

    fflush(fp);
    fclose(fp);  // 关闭文件
  }

  return GNCDB_SUCCESS;
}