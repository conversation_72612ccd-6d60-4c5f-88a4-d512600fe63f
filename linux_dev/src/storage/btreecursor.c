/**
 * @file btreeCursor.c
 * <AUTHOR>
 * @brief  游标的实现
 * @version 0.1
 * @date 2023-02-16
 *
 * @copyright Copyright (c) 2023
 *
 */

#include "btreecursor.h"
#include "pagepool.h"
#include "btreepage.h"
#include "gncdbconstant.h"
#include <string.h>
#include <time.h>
#include "sql_event.h"
#include "tranmanager.h"
#include "transaction.h"
#include "typedefine.h"

/**
 * @brief btreeCursor的创建
 * @param tableName 表名
 * @param db
 * @param tid
 * @param 返回btreeCursor
 */
struct BtreeCursor *btreeCursorConstruct(
    char *tableName, struct GNCDB *db, varArrayList *startKeyValue, struct Transaction *tx)
{
  int          rc          = 0;
  BtreeCursor *btreeCursor = NULL;
  BtreeTable  *table       = NULL;
  TableSchema *tableSchema = NULL;
  BtreePage   *page        = NULL;
  int          pageIndex   = 0;
  //  struct timespec st, ed;
  //  unsigned long long costTime = 0;
  if (tableName == NULL || db == NULL) {
    return NULL;
  }
  rc = catalogGetTable(db->catalog, &table, tableName);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }
  tableSchema = getTableSchema(db->catalog, tableName);
  if (tableSchema == NULL) {
    return NULL;
  }
  btreeCursor = (BtreeCursor *)my_malloc0(sizeof(BtreeCursor));
  if (btreeCursor == NULL) {
    return NULL;
  }
  btreeCursor->page = NULL;
  btreeCursor->db   = db;
  btreeCursor->tx   = tx;

  LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", table->tableName);
  ReadLock(&table->rwlock_t);
  LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", table->tableName);
  // clock_gettime(CLOCK_REALTIME, &st);
  if (startKeyValue == NULL || startKeyValue->elementCount == 0) {
    /* 没有起始位置, 直接寻找表的第一个叶子页的第一条tuple */
    page = btreeTableFindTupleInLeafPage(table, NULL, tableSchema, db, tx, OP_SEARCH, true, NULL);
    if (page == NULL) {
      my_free(btreeCursor);
      return NULL;
    }
  } else {
    /* 给定起始位置, 调用函数使cursor初始化至起始位置 */
    page = btreeTableFindTupleInLeafPage(table, startKeyValue, tableSchema, db, tx, OP_SEARCH, true, NULL);
    if (page == NULL) {
      my_free(btreeCursor);
      return NULL;
    }
    pageIndex = leafPageFindEntryIndexByKeyvalue(
        page, startKeyValue, tableSchema, tableName, db->catalog, GREATER_THAN_OR_EQUAL, table);
    if (pageIndex < 0) {
      ReadUnLock(&page->page.rwlock_t);
      setPageStatusPinDown(db->pagePool, page->page.id, NULL);
      /* 未找到符合条件的元组 */
      my_free(btreeCursor);
      return NULL;
    }
    btreeCursor->currentTupleIndex = pageIndex;
  }

  /* 调试时暂不使用cursor加锁 因此需要对获取的页进行解锁 */
  LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", page->page.id);
  ReadUnLock(&page->page.rwlock_t);
  LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", page->page.id);

  btreeCursor->page              = page;
  btreeCursor->currentLeafPageId = page->page.id;

  return btreeCursor;
}

int btreeCursorEndConstruct(BtreeCursor *btreeCursor, struct varArrayList *endKeyValue)
{
  int          rc          = 0;
  BtreeTable  *table       = NULL;
  TableSchema *tableSchema = NULL;
  BtreePage   *page        = NULL;
  Transaction *tx          = NULL;
  GNCDB       *db          = btreeCursor->db;
  int          pageIndex   = 0;
  if (btreeCursor == NULL || btreeCursor->db == NULL) {
    return GNCDB_PARAMNULL;
  }
  rc = catalogGetTable(btreeCursor->db->catalog, &table, btreeCursor->page->tableName);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  tableSchema = getTableSchema(btreeCursor->db->catalog, btreeCursor->page->tableName);
  if (tableSchema == NULL) {
    return GNCDB_PARAMNULL;
  }

  tx = btreeCursor->tx;

  LOG(LOG_TRACE, "RLOCKing:TABLENAME=%s", table->tableName);
  ReadLock(&table->rwlock_t);
  LOG(LOG_TRACE, "RLOCKend:TABLENAME=%s", table->tableName);
  // clock_gettime(CLOCK_REALTIME, &st);

  /* 给定起始位置, 调用函数使cursor初始化至起始位置 */
  page = btreeTableFindTupleInLeafPage(table, endKeyValue, tableSchema, db, tx, OP_SEARCH, true, NULL);
  if (page == NULL) {
    return GNCDB_NOT_FOUND;
  }

  pageIndex = leafPageFindEntryIndexByKeyvalue(
      page, endKeyValue, tableSchema, btreeCursor->page->tableName, db->catalog, GREATER_THAN, table);
  if (pageIndex < 0) {
    btreeCursor->endTupleIndex = page->entryNum;
  } else {
    btreeCursor->endTupleIndex = pageIndex;
  }

  LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", page->page.id);
  ReadUnLock(&page->page.rwlock_t);
  LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", page->page.id);
  btreeCursor->endPageId = page->page.id;

  // /* 解除当前页的pin */
  // rc = setPageStatusPinDown(db->pagePool, page->page.id, NULL);
  // if (rc != GNCDB_SUCCESS) {
  //   return GNCDB_INTERNAL;
  // }

  // /* 释放锁 */
  // rc = lockManagerReleaseLock(db->transactionManager->lockManager, tx, page->page.id, SHARD);
  // if (rc != GNCDB_SUCCESS) {
  //   return GNCDB_INTERNAL;
  // }

  return GNCDB_SUCCESS;
}

/// <summary>
/// 销毁函数
/// </summary>
/// <param name="btreeCursor"></param>
void btreeCursorDestroy0(struct BtreeCursor **btreeCursor, bool needUnlock)
{

  if (btreeCursor == NULL || *btreeCursor == NULL) {
    return;
  }
  // LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", (*btreeCursor)->page->page.id);
  // ReadUnLock(&(*btreeCursor)->page->page.rwlock_t);
  // LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", (*btreeCursor)->page->page.id);
  if(needUnlock)
  {
    // 释放锁
    lockManagerReleaseLock((*btreeCursor)->db->transactionManager->lockManager, (*btreeCursor)->tx,
                           (*btreeCursor)->page->page.id, SHARD);
  }
  setPageStatusPinDown((*btreeCursor)->db->pagePool, (*btreeCursor)->currentLeafPageId, NULL);
  my_free(*btreeCursor);
  *btreeCursor = NULL;
}

void btreeCursorDestroy(struct BtreeCursor **btreeCursor)
{
  btreeCursorDestroy0(btreeCursor, true);
}
/// <summary>
/// 更新cursor
/// </summary>
/// <param name="btreeCursor"></param>
/// <param name="db"></param>
/// <param name="tableName"></param>
/// <param name="updatePageId"></param>
/// <returns></returns>
int updateCursor(struct BtreeCursor *btreeCursor, struct GNCDB *db, char *tableName, int updatePageId)
{
  int        rc       = 0;
  BtreePage *nextPage = NULL;

  if (db == NULL || btreeCursor == NULL) {
    return GNCDB_PARAMNULL;
  }
  rc = pagePoolGetPage((Page **)(&nextPage), updatePageId, tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  rc = lockManagerAcquireLock(db->transactionManager->lockManager, btreeCursor->tx, updatePageId, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  // LOG(LOG_TRACE, "RWLOCKing:PAGEid=%d", nextPage->page.id);
  // ReadLock(&nextPage->page.rwlock_t);
  // LOG(LOG_TRACE, "RWLOCKend:PAGEid=%d", nextPage->page.id);

  // LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreeCursor->page->page.id);
  // ReadUnLock(&btreeCursor->page->page.rwlock_t);
  // LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreeCursor->page->page.id);
  rc = lockManagerReleaseLock(
      btreeCursor->db->transactionManager->lockManager, btreeCursor->tx, btreeCursor->page->page.id, SHARD);
  if (rc != GNCDB_SUCCESS) {
    // LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d",nextPage->page.id);
    // ReadUnLock(&nextPage->page.rwlock_t);
    // LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", nextPage->page.id);
    return rc;
  }

  setPageStatusPinDown(btreeCursor->db->pagePool, btreeCursor->currentLeafPageId, NULL);
  btreeCursor->currentLeafPageId = updatePageId;
  btreeCursor->page              = nextPage;
  btreeCursor->currentTupleIndex = 1;

  return GNCDB_SUCCESS;
}

/**
 * @brief 判读B+树有没有满足btreeCursor游标条件的tuple
 * @param btreeTable
 * @param btreeCursor 游标指针
 * @param db
 * @param tid
 */
bool btreeTableHasNextTuple(struct BtreeCursor *btreeCursor)
{
  if (btreeCursor == NULL) {
    return false;
  }

  if (btreeCursor->page->nextPageId <= 0 && btreeCursor->currentTupleIndex == btreeCursor->page->entryNum) {
    /* cursor已经遍历到最后一个页且指向了最后一个元素 */
    return false;
  }
  if (btreeCursor->endPageId != 0) {  // 如果有结束页，判断是否已经到达结束页
    // if(btreeCursor->page->page.id > btreeCursor->endPageId){
    // 	return false;
    // }
    if (btreeCursor->page->page.id == btreeCursor->endPageId &&
        btreeCursor->currentTupleIndex == btreeCursor->endTupleIndex) {
      return false;
    }
  }

  return true;
}

/**
 * @brief 通过btreeCursor游标实现在B+树中叶子页中的遍历，依次获取符合判断条件的tuple
 * @param btreeTable
 * @param btreeCursor 游标指针
 * @param db
 * @param tid
 */
// struct Tuple* btreeTableGetNextTuple(struct BtreeTable* btreeTable, struct BtreeCursor* btreeCursor, struct GNCDB*
// db) {
//     int rc = 0;
//     Tuple* tuple = NULL;
//     BtreePage* nextPage = NULL;
//     int nextPageId = 0;
//     if (btreeTable == NULL || btreeCursor == NULL || db == NULL) {
// 		return NULL;
// 	}

// 	// LOG(LOG_TRACE, "RWLOCKing:PAGEid=%d", btreeCursor->page->page.id);
// 	// ReadLock(&btreeCursor->page->page.rwlock_t);
// 	// LOG(LOG_TRACE, "RWLOCKend:PAGEid=%d", btreeCursor->page->page.id);

// 	if (btreeCursor->currentTupleIndex < btreeCursor->page->entryNum)
// 	{
// 		/* 获取当前位置的tuple */
// 		tuple = (Tuple*)varArrayListGetPointer(btreeCursor->page->entryArray, btreeCursor->currentTupleIndex);
// 		btreeCursor->currentTupleIndex++;

// 		// LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreeCursor->page->page.id);
// 		// ReadUnLock(&btreeCursor->page->page.rwlock_t);
// 		// LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreeCursor->page->page.id);
// 	}
// 	else if (btreeCursor->currentTupleIndex >= btreeCursor->page->entryNum && btreeCursor->page->nextPageId > 0)
// 	{
// 		/* 该叶子页已经遍历完,获取下一个叶子页 */
// 		/* 获取下一个页的读锁并pin住该页 */
// 		nextPageId = btreeCursor->page->nextPageId;
// 		rc = pagePoolGetPage((Page**)&nextPage, nextPageId, btreeCursor->page->tableName, db);
// 		if (rc != GNCDB_SUCCESS)
// 		{
// 			return NULL;
// 		}
// 		/* todo 可能发生死锁,添加逻辑锁 */
// 		rc = lockManagerAcquireLock(db->transactionManager->lockManager, btreeCursor->tx, nextPageId, SHARD);
// 		if(rc != GNCDB_SUCCESS)
// 		{
// 			return NULL;
// 		}
// 		// LOG(LOG_TRACE, "RWLOCKing:PAGEid=%d", nextPage->page.id);
// 		// ReadLock(&nextPage->page.rwlock_t);
// 		// LOG(LOG_TRACE, "RWLOCKend:PAGEid=%d", nextPage->page.id);

// 		// LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreeCursor->page->page.id);
// 		// ReadUnLock(&btreeCursor->page->page.rwlock_t);
// 		// LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreeCursor-> page->page.id);
// 		rc = lockManagerReleaseLock(btreeCursor->db->transactionManager->lockManager, btreeCursor->tx,
// btreeCursor->page->page.id, SHARD); 		if(rc != GNCDB_SUCCESS)
// 		{
// 			// LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d",nextPage->page.id);
// 			// ReadUnLock(&nextPage->page.rwlock_t);
// 			// LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", nextPage->page.id);
// 			return NULL;
// 		}
// 		/* 解除当前页的pin和锁 */
// 		rc = setPageStatusPinDown(db->pagePool, btreeCursor->currentLeafPageId);
// 		if (rc != GNCDB_SUCCESS)
// 		{
// 			return NULL;
// 		}

// 		btreeCursor->currentLeafPageId = nextPageId;
// 		btreeCursor->page = nextPage;
// 		btreeCursor->currentTupleIndex = 0;
// 		/* 获取当前位置的tuple */
// 		tuple = (Tuple*)varArrayListGetPointer(btreeCursor->page->entryArray, btreeCursor->currentTupleIndex);
// 		btreeCursor->currentTupleIndex++;

// 		// LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreeCursor->page->page.id);
// 		// ReadUnLock(&btreeCursor->page->page.rwlock_t);
// 		LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreeCursor-> page->page.id);
// 	}
// 	else
// 	{
// 		// LOG(LOG_TRACE, "RWUNLOCKing:PAGEid=%d", btreeCursor->page->page.id);
// 		// ReadUnLock(&btreeCursor->page->page.rwlock_t);
// 		// LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreeCursor->page->page.id);
// 		return NULL;
// 	}

// 	return tuple;
// }

BYTE *btreeTableGetNextRecord(struct BtreeTable *btreeTable, struct BtreeCursor *btreeCursor, struct GNCDB *db)
{
  int rc = 0;
  BYTE      *record     = NULL;
  BtreePage *nextPage   = NULL;
  int        nextPageId = 0;
  if (btreeTable == NULL || btreeCursor == NULL || db == NULL) {
    return NULL;
  }

  if (btreeCursor->currentTupleIndex < btreeCursor->page->entryNum) {
    /* 获取当前位置的tuple */
    record =
        btreeCursor->page->page.pData + PAGE_HEAD_SIZE + btreeCursor->currentTupleIndex * btreeTable->leafRecordLength;
    btreeCursor->currentTupleIndex++;

  } else if (btreeCursor->currentTupleIndex >= btreeCursor->page->entryNum && btreeCursor->page->nextPageId > 0) {
    /* 该叶子页已经遍历完,获取下一个叶子页 */
    /* 获取下一个页的读锁并pin住该页 */
    nextPageId = btreeCursor->page->nextPageId;
    rc         = pagePoolGetPage((Page **)&nextPage, nextPageId, btreeCursor->page->tableName, db);
    if (rc != GNCDB_SUCCESS) {
      return NULL;
    }
    /* todo 可能发生死锁,添加逻辑锁 */
    rc = lockManagerAcquireLock(db->transactionManager->lockManager, btreeCursor->tx, nextPageId, SHARD);
    if (rc != GNCDB_SUCCESS) {
      setPageStatusPinDown(db->pagePool, nextPageId, NULL);
      return NULL;
    }
    rc = lockManagerReleaseLock(
        btreeCursor->db->transactionManager->lockManager, btreeCursor->tx, btreeCursor->page->page.id, SHARD);
    if (rc != GNCDB_SUCCESS) {
      setPageStatusPinDown(db->pagePool, nextPageId, NULL);
      return NULL;
    }
    /* 解除当前页的pin和锁 */
    setPageStatusPinDown(db->pagePool, btreeCursor->currentLeafPageId, NULL);

    btreeCursor->currentLeafPageId = nextPageId;
    btreeCursor->page              = nextPage;
    btreeCursor->currentTupleIndex = 0;
    /* 获取当前位置的tuple */
    record =
        btreeCursor->page->page.pData + PAGE_HEAD_SIZE + btreeCursor->currentTupleIndex * btreeTable->leafRecordLength;
    btreeCursor->currentTupleIndex++;

    LOG(LOG_TRACE, "RWUNLOCKend:PAGEid=%d", btreeCursor->page->page.id);
  } else {
    return NULL;
  }
  return record;
}

int btreeCursorReset(BtreeCursor *btreeCursor, BtreeCursor *backupCursor, GNCDB *db)
{
  int rc = GNCDB_SUCCESS;

  // 1.先释放当前btreeCursor的锁和pin
  rc = lockManagerReleaseLock(
      btreeCursor->db->transactionManager->lockManager, btreeCursor->tx, btreeCursor->page->page.id, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  rc = setPageStatusPinDown(btreeCursor->db->pagePool, btreeCursor->currentLeafPageId, NULL);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  // 2.将btreeCursor的值赋值为backupCursor的值
  btreeCursor->currentLeafPageId = backupCursor->currentLeafPageId;
  btreeCursor->currentTupleIndex = backupCursor->currentTupleIndex;
  btreeCursor->endPageId         = backupCursor->endPageId;
  btreeCursor->endTupleIndex     = backupCursor->endTupleIndex;
  rc = pagePoolGetPage((Page **)(&btreeCursor->page), btreeCursor->currentLeafPageId, btreeCursor->page->tableName, db);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  // 3.获取备份的btreeCursor的锁
  rc = lockManagerAcquireLock(
      db->transactionManager->lockManager, btreeCursor->tx, btreeCursor->currentLeafPageId, SHARD);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return GNCDB_SUCCESS;
}
