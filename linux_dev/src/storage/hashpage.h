
#ifndef _GNCDB_HASH_PAGE_H_
#define _GNCDB_HASH_PAGE_H_

#include "btreepage.h"
#include "hash.h"

#define META_PAGE_HEADER_SIZE 37          /* 元数据页头部大小: 1(页类型) + 4*7(7个int) + 8(double) */
#define BUCKET_PAGE_HEADER_SIZE 21        /* 桶页头部大小: 1(页类型) + 4*5(5个int) */
#define HASH_OVERFLOW_PAGE_HEADER_SIZE 21 /* 溢出页头部大小: 1(页类型) + 4*5(5个int) */
#define MAX_BUCKET_COUNT(PAGE_SIZE_SELF) ((PAGE_SIZE_SELF - META_PAGE_HEADER_SIZE) / INT_SIZE)  // 元数据页中桶页地址数组的最大大小

#define GET_BUCKET_PAIR_MAX_COUNT(HASH_INDEX, PAGE_SIZE_SELF) \
  (((PAGE_SIZE_SELF - BUCKET_PAGE_HEADER_SIZE) / (sizeof(int) + (HASH_INDEX)->primaryKeyLenth)))
/* Meta Page 相关方法 */
MetaPage *CreateHashMetaPage(GNCDB *db, Transaction *tx);
/* 创建不含数据的MetaPage结构体 */
int HashMetaPageInit(MetaPage *metaPage, int pageId);
/* 从字节流构建MetaPage结构体 */
int MetaPageConstruct(MetaPage *metaPage, int pageId);
/* 深拷贝MetaPage，内存由malloc分配 */
MetaPage* MetaPageDeepCopy(GNCDB* db, MetaPage* metaPage);
/* 销毁MetaPage并释放资源 */
void MetaPageDestroy(MetaPage **metaPage);
void MetaPageDestroyMalloc(MetaPage **metaPage);
/* 将MetaPage转换为字节流 */
BYTE *MetaPageToByte(MetaPage *metaPage);

/* Bucket Page 相关方法 */
BucketPage *CreateHashBucketPage(GNCDB *db, HashIndex *hashIndex, int bucNum, Transaction *tx);
/* 创建BucketPage */
int HashBucketPageInit(BucketPage *bucketPage, int pageId, int bucketNumber, HashIndex *hashIndex);
/* 从字节流构建BucketPage */
int BucketPageConstruct(BucketPage *bucketPage, int pageId);
/* 深拷贝BucketPage，内存由malloc分配 */
BucketPage* BucketPageDeepCopy(GNCDB *db, BucketPage* bucketPage);
/* 销毁BucketPage */
void BucketPageDestroy(BucketPage **bucketPage);
void BucketPageDestroyMalloc(BucketPage **bucketPage);
/* 将BucketPage转换为字节流 */
BYTE *BucketPageToByte(BucketPage *bucketPage);

/* Overflow Page 相关方法 */
/* 创建HashOverflowPage */
HashOverflowPage *CreateHashOverflowPage(GNCDB *db, HashIndex *hashIndex, int bucNum, Transaction *tx);
int HashOverflowPageInit(HashOverflowPage *overflowPage, int pageId, int bucketNumber, HashIndex *hashIndex);
/* 从字节流构建HashOverflowPage */
int HashOverflowPageConstruct(HashOverflowPage *overflowPage, int pageId);
/* 深拷贝HashOverflowPage，内存由malloc分配 */
HashOverflowPage *HashOverflowPageDeepCopy(GNCDB *db, HashOverflowPage *hashOverflowPage);
/* 销毁HashOverflowPage */
void HashoverflowPageDestroy(HashOverflowPage **overflowPage);
void HashOverflowPageDestroyMalloc(HashOverflowPage **overflowPage);
/* 将HashOverflowPage转换为字节流 */
BYTE *HashOverflowPageToByte(HashOverflowPage *overflowPage);

#endif  // _GNCDB_HASH_PAGE_H_