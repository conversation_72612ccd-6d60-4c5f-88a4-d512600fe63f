#include "hashpage.h"
#include "btreepage.h"
#include "gncdbconstant.h"
#include "pagepool.h"
#include "typedefine.h"
#include <string.h>

int metaPageFillHeader(MetaPage *metaPage);
int metaPageReadHeader(MetaPage *metaPage);
int bucketPageFillHeader(BucketPage *bucketPage);
int bucketPageReadHeader(BucketPage *bucketPage);
int hashOverflowPageFillHeader(HashOverflowPage *overflowPage);
int hashOverflowPageReadHeader(HashOverflowPage *overflowPage);

/**
 * @description:
 * @param {GNCDB} *db
 * @param {Transaction} *tx
 * @return {MetaPage *}
 */
MetaPage *CreateHashMetaPage(GNCDB *db, Transaction *tx)
{
  /* 1.变量的定义 */
  MetaPage *hashMetaPage = NULL;
  int       rc           = 0;

  /* 2.判断参数是否为空 */
  if (db == NULL || tx == NULL) {
    return NULL;
  }

  /* 3.创建一个空的HashMetaPage */
  rc = pagePoolCreateMetaPage(&hashMetaPage, db, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 新创建的页需要加写锁 */
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, hashMetaPage->page.id, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", hashMetaPage->page.id);
    return NULL;
  }
  LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", hashMetaPage->page.id);

  // * 新创建的btreePage以freepage的形式加入oldPageMap中，因为相当于从空白页变成了btreePage
  /* 4.备份页被修改之前的信息oldData，用于回滚 */
  rc = produceOldPageData(db, (Page *)hashMetaPage, FREE_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }
  setPageStatusDirty(db->pagePool, hashMetaPage->page.id, NULL);

  return hashMetaPage;
}

/**
 * 创建哈希索引的元数据页(不加锁，不涉及事务，仅仅负责从缓冲池分配)
 * @param pageId 页ID（由页管理器分配）
 * @param tableName 表名
 * @param catalog 系统目录（用于获取表级配置）
 * @return 初始化后的MetaPage指针
 */
int HashMetaPageInit(MetaPage *metaPage, int pageId)
{

  /* 2.参数检查 */
  if (metaPage == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.初始化基础页属性 */
  pageInit(&metaPage->page, pageId, META_PAGE, false);

  /* 4.设置元数据字段 */
  metaPage->hashFunctionId  = 0;   /* 默认为fnv-1a */
  metaPage->keyTidPairCount = 0;   /* 初始键值对数量为0 */
  metaPage->fillFactor      = 0.0; /* 初始装填因子为0 */
  metaPage->highMask        = 0;   /* 初始高位掩码*/
  metaPage->lowMask         = 0;   /* 低位掩码为高位掩码右移1位 */
  metaPage->bucketCount     = 0;   /* 初始桶数量为0 */
  metaPage->maxBucketNumber = 0;   /* 最大桶编号初始为0 */
  metaPage->splitCount      = 0;   /* 分裂次数初始为0 */

  return GNCDB_SUCCESS;
}

/**
 * @brief 从字节流构建MetaPage结构体
 * @param metaPage 元数据页指针
 * @param pageId 页ID
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int MetaPageConstruct(MetaPage *metaPage, int pageId)
{
  int rc = GNCDB_SUCCESS;
  /* 1.参数检查 */
  if (metaPage == NULL || metaPage->page.pData == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2.初始化页面基本信息 */
  pageInit(&metaPage->page, pageId, META_PAGE, false);

  /* 3.读取页面头部信息 */
  rc = metaPageReadHeader(metaPage);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return GNCDB_SUCCESS;
}

MetaPage *MetaPageDeepCopy(GNCDB *db, MetaPage *metaPage)
{
  MetaPage *metaPageCopy = NULL;

  metaPageCopy             = (MetaPage *)my_malloc0(sizeof(MetaPage));
  metaPageCopy->page.pData = my_malloc0(db->pageCurrentSize);
  pageInit(&metaPageCopy->page, metaPage->page.id, META_PAGE, true);

  metaPageCopy->hashFunctionId  = metaPage->hashFunctionId;
  metaPageCopy->keyTidPairCount = metaPage->keyTidPairCount;
  metaPageCopy->fillFactor      = metaPage->fillFactor;
  metaPageCopy->highMask        = metaPage->highMask;
  metaPageCopy->lowMask         = metaPage->lowMask;
  metaPageCopy->bucketCount     = metaPage->bucketCount;
  metaPageCopy->maxBucketNumber = metaPage->maxBucketNumber;
  metaPageCopy->splitCount      = metaPage->splitCount;

  memcpy(metaPageCopy->page.pData, metaPage->page.pData, db->pageCurrentSize);

  return metaPageCopy;
}

/**
 * 销毁缓冲池中申请的元数据页并释放资源
 * @param metaPage 元数据页的双重指针（销毁后置NULL）
 * @param db 数据库实例（用于页释放）
 */
void MetaPageDestroy(MetaPage **metaPage)
{
  if (metaPage == NULL || *metaPage == NULL)
    return;
  // 释放元数据页内存
  ReadWriteLockDestroy(&((*metaPage)->page.rwlock_t));
  *metaPage = NULL;
  // 避免悬空指针
}

/**
 * @description: 销毁独立malloc分配的MetaPage
 * @param {MetaPage} *
 * @return {*}
 */
void MetaPageDestroyMalloc(MetaPage **metaPage)
{
  if (metaPage == NULL || *metaPage == NULL)
    return;
  // 释放元数据页内存
  ReadWriteLockDestroy(&((*metaPage)->page.rwlock_t));
  my_free((*metaPage)->page.pData);
  my_free(*metaPage);
  *metaPage = NULL;
  // 避免悬空指针
}

/**
 * @brief 将MetaPage转换为字节数组
 * @param metaPage MetaPage指针
 * @return 成功返回字节数组指针，失败返回NULL
 */
BYTE *MetaPageToByte(MetaPage *metaPage)
{
  // /* 1.变量定义 */
  int rc = GNCDB_SUCCESS;
  /* 2.参数检查 */
  if (metaPage == NULL || metaPage->page.pageType != META_PAGE) {
    return NULL;
  }

  /* 2.填充页面头部信息 */
  rc = metaPageFillHeader(metaPage);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 3.返回页面数据 */
  return metaPage->page.pData;
}

/**
 * @brief MetaPage头部填充函数
 * @param metaPage MetaPage指针
 * @param buf 缓冲区
 */
int metaPageFillHeader(MetaPage *metaPage)
{
  /* 1.定义偏移量和返回值 */
  int   offset = 0;
  int   rc     = 0;
  BYTE *buf    = metaPage->page.pData;

  /* 2.参数检查 */
  if (metaPage == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.写入页类型标记 */
  rc = writeChar((char)META_PAGE, buf + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  /* 4.写入元数据页信息 */
  writeInt(metaPage->hashFunctionId, buf + offset, &offset);
  writeInt(metaPage->keyTidPairCount, buf + offset, &offset);
  writeDouble(metaPage->fillFactor, buf + offset, &offset);
  writeInt(metaPage->highMask, buf + offset, &offset);
  writeInt(metaPage->lowMask, buf + offset, &offset);
  writeInt(metaPage->bucketCount, buf + offset, &offset);
  writeInt(metaPage->maxBucketNumber, buf + offset, &offset);
  writeInt(metaPage->splitCount, buf + offset, &offset);
  return GNCDB_SUCCESS;
}
/**
 * @brief 读取MetaPage头部信息
 * @param metaPage MetaPage指针
 * @param buf 字节缓冲区
 * @return 操作状态码
 */
int metaPageReadHeader(MetaPage *metaPage)
{
  int   offset = 1; /* 跳过页类型标记 */
  int   temp   = 0;
  BYTE *buf    = metaPage->page.pData;

  /* 2.参数检查 */
  if (metaPage == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.检查页类型标记 */
  if ((int)buf[0] != META_PAGE) { /* 不是元数据页 */
    return GNCDB_HASHPAGE_UNSAFE;
  }

  /* 4.读取元数据页信息 */
  readInt(&temp, buf + offset, &offset);
  metaPage->hashFunctionId = temp;

  readInt(&temp, buf + offset, &offset);
  metaPage->keyTidPairCount = temp;

  readDouble(&metaPage->fillFactor, buf + offset, &offset);

  readInt(&temp, buf + offset, &offset);
  metaPage->highMask = temp;

  readInt(&temp, buf + offset, &offset);
  metaPage->lowMask = temp;

  readInt(&temp, buf + offset, &offset);
  metaPage->bucketCount = temp;

  readInt(&temp, buf + offset, &offset);
  metaPage->maxBucketNumber = temp;

  readInt(&temp, buf + offset, &offset);
  metaPage->splitCount = temp;
  return GNCDB_SUCCESS;
}

// Bucket Page
/**
 * @brief 创建桶页
 * @param pageId 页ID
 * @param bucketNumber 桶号
 * @param hashIndex 哈希索引结构体指针
 * @param catalog 系统目录
 * @return 创建的BucketPage指针
 */
int HashBucketPageInit(BucketPage *bucketPage, int pageId, int bucketNumber, HashIndex *hashIndex)
{
  /* 1.变量定义 */
  int rc = GNCDB_SUCCESS;

  /* 2.参数检查 */
  if (bucketPage == NULL || hashIndex == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 3.分配内存 */
  // bucketPage = (BucketPage *)my_malloc(sizeof(BucketPage));

  /* 4.初始化页基本信息 */
  pageInit(&bucketPage->page, pageId, BUCKET_PAGE, false);

  /* 5.初始化桶页特有字段 */
  bucketPage->bucketId            = bucketNumber;
  bucketPage->keyTidPairCount     = 0;
  bucketPage->firstOverflowPageId = -1;
  bucketPage->lastOverflowPageId  = -1;
  bucketPage->primaryKeyLenth     = hashIndex->primaryKeyLenth;

  return rc;
}

/**
 * @description:
 * @param {GNCDB} *db
 * @param {Transaction} *tx
 * @return {MetaPage *}
 */
BucketPage *CreateHashBucketPage(GNCDB *db, HashIndex *hashIndex, int bucNum, Transaction *tx)
{
  /* 1.变量的定义 */
  BucketPage *hashBucketPage = NULL;
  int         rc             = 0;

  /* 2.判断参数是否为空 */
  if (db == NULL || tx == NULL) {
    return NULL;
  }

  /* 3.创建一个空的HashMetaPage */
  rc = pagePoolCreateBucketPage(&hashBucketPage, hashIndex, bucNum, db, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 新创建的页需要加写锁 */
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, hashBucketPage->page.id, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", hashBucketPage->page.id);
    return NULL;
  }
  LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", hashBucketPage->page.id);

  // * 新创建的btreePage以freepage的形式加入oldPageMap中，因为相当于从空白页变成了btreePage
  /* 4.备份页被修改之前的信息oldData，用于回滚 */
  rc = produceOldPageData(db, (Page *)hashBucketPage, FREE_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }
  setPageStatusDirty(db->pagePool, hashBucketPage->page.id, NULL);

  return hashBucketPage;
}

/**
 * @brief 从字节流构建BucketPage结构体
 * @param bucketPage 桶页指针
 * @param pageId 页ID
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int BucketPageConstruct(BucketPage *bucketPage, int pageId)
{
  int rc = GNCDB_SUCCESS;
  /* 1.参数检查 */
  if (bucketPage == NULL || bucketPage->page.pData == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2.初始化页面基本信息 */
  pageInit(&bucketPage->page, pageId, BUCKET_PAGE, false);

  /* 3.读取页面头部信息 */
  rc = bucketPageReadHeader(bucketPage);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return GNCDB_SUCCESS;
}

BucketPage *BucketPageDeepCopy(GNCDB *db, BucketPage *bucketPage)
{
  BucketPage *bucketPageCopy = NULL;

  bucketPageCopy             = (BucketPage *)my_malloc0(sizeof(BucketPage));
  bucketPageCopy->page.pData = (BYTE *)my_malloc0(db->pageCurrentSize);
  pageInit(&bucketPageCopy->page, bucketPage->page.id, BUCKET_PAGE, true);

  bucketPageCopy->bucketId            = bucketPage->bucketId;
  bucketPageCopy->keyTidPairCount     = bucketPage->keyTidPairCount;
  bucketPageCopy->firstOverflowPageId = bucketPage->firstOverflowPageId;
  bucketPageCopy->lastOverflowPageId  = bucketPage->lastOverflowPageId;
  bucketPageCopy->primaryKeyLenth     = bucketPage->primaryKeyLenth;

  memcpy(bucketPageCopy->page.pData, bucketPage->page.pData, db->pageCurrentSize);

  return bucketPageCopy;
}

/**
 * @brief	销毁缓冲池中申请的BucketPage
 * @param bucketPage 指向BucketPage指针的指针
 * @param db 数据库结构指针
 */
void BucketPageDestroy(BucketPage **bucketPage)
{
  if (*bucketPage == NULL) {
    return;
  }
  ReadWriteLockDestroy(&((*bucketPage)->page.rwlock_t));
  // varArrayListDestroy(&(*bucketPage)->entryArray);
  //  my_free(*bucketPage);
  *bucketPage = NULL;
  // printf("桶页销毁成功\n");
}

/**
 * @brief	销毁独立malloc申请的BucketPage
 * @param bucketPage 指向BucketPage指针的指针
 * @param db 数据库结构指针
 */
void BucketPageDestroyMalloc(BucketPage **bucketPage)
{
  if (*bucketPage == NULL) {
    return;
  }
  ReadWriteLockDestroy(&((*bucketPage)->page.rwlock_t));
  my_free((*bucketPage)->page.pData);
  my_free(*bucketPage);
  *bucketPage = NULL;
}

/**
 * @brief BucketPage头部填充函数
 * @param bucketPage BucketPage指针
 * @param buf 缓冲区
 */
int bucketPageFillHeader(BucketPage *bucketPage)
{
  /* 1.定义偏移量和返回值 */
  int   offset = 0;
  int   rc     = 0;
  BYTE *buf    = bucketPage->page.pData;

  /* 2.参数检查 */
  if (bucketPage == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.写入页类型标记 */
  rc = writeChar((char)BUCKET_PAGE, buf + offset, &offset); /* 0x03表示桶页 */
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  writeInt(bucketPage->bucketId, buf + offset, &offset);
  writeInt(bucketPage->keyTidPairCount, buf + offset, &offset);
  writeInt(bucketPage->firstOverflowPageId, buf + offset, &offset);
  writeInt(bucketPage->lastOverflowPageId, buf + offset, &offset);
  writeInt(bucketPage->primaryKeyLenth, buf + offset, &offset);
  return GNCDB_SUCCESS;
}

/**
 * @brief 读取桶页的头部
 * @param bucketPage 桶页指针
 * @return 返回读取结果，成功返回GNCDB_SUCCESS，否则返回错误码
 */
int bucketPageReadHeader(BucketPage *bucketPage)
{
  /* 1.定义临时变量和偏移量 */
  int   offset = 1; /* 跳过页类型标记 */
  int   temp   = 0;
  BYTE *buf    = bucketPage->page.pData;

  /* 2.参数检查 */
  if (bucketPage == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.检查页类型标记 */
  if ((int)buf[0] != BUCKET_PAGE) { /* 不是桶页 */
    return GNCDB_HASHPAGE_UNSAFE;
  }

  /* 4.读取桶页信息 */
  readInt(&temp, buf + offset, &offset);
  bucketPage->bucketId = temp;

  readInt(&temp, buf + offset, &offset);
  bucketPage->keyTidPairCount = temp;

  readInt(&temp, buf + offset, &offset);
  bucketPage->firstOverflowPageId = temp;

  readInt(&temp, buf + offset, &offset);
  bucketPage->lastOverflowPageId = temp;

  readInt(&temp, buf + offset, &offset);
  bucketPage->primaryKeyLenth = temp;

  return GNCDB_SUCCESS;
}

/**
 * @brief 将BucketPage转换为字节数组
 * @param bucketPage BucketPage指针
 * @return 成功返回字节数组指针，失败返回NULL
 */
BYTE *BucketPageToByte(BucketPage *bucketPage)
{
  int rc = GNCDB_SUCCESS;
  /* 1.参数检查 */
  if (bucketPage == NULL || bucketPage->page.pageType != BUCKET_PAGE) {
    return NULL;
  }

  /* 2.填充页面头部信息 */
  rc = bucketPageFillHeader(bucketPage);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 3.返回页面数据 */
  return bucketPage->page.pData;
}

HashOverflowPage *CreateHashOverflowPage(GNCDB *db, HashIndex *hashIndex, int bucNum, Transaction *tx)
{
  /* 1.变量的定义 */
  HashOverflowPage *hashOverflowPage = NULL;
  int               rc               = 0;

  /* 2.判断参数是否为空 */
  if (db == NULL || tx == NULL) {
    return NULL;
  }

  /* 3.创建一个空的HashMetaPage */
  rc = pagePoolcreateHashOverflowPage(&hashOverflowPage, hashIndex, bucNum, db, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 新创建的页需要加写锁 */
  rc = lockManagerAcquireLock(db->transactionManager->lockManager, tx, hashOverflowPage->page.id, EXCLUSIVE);
  if (rc != GNCDB_SUCCESS) {
    LOG(LOG_TRACE, "AcquireLockLOCKfail:PAGEid=%d", hashOverflowPage->page.id);
    return NULL;
  }
  LOG(LOG_TRACE, "AcquireLockLOCKsuccess:PAGEid=%d", hashOverflowPage->page.id);

  // * 新创建的btreePage以freepage的形式加入oldPageMap中，因为相当于从空白页变成了btreePage
  /* 4.备份页被修改之前的信息oldData，用于回滚 */
  rc = produceOldPageData(db, (Page *)hashOverflowPage, FREE_PAGE, tx);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 5.将新创建的页添加到newCreatedPageSet中 */
  rc = addIntoNewCreatedPageSet(tx, (Page *)hashOverflowPage);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }
  setPageStatusDirty(db->pagePool, hashOverflowPage->page.id, NULL);

  return hashOverflowPage;
}

// HashOverflow Page
/**
 * @brief 创建HashOverflowPage
 * @param pageId 页ID
 * @param bucketNumber 桶号
 * @param hashIndex 哈希索引结构体指针
 * @return 创建的HashOverflowPage指针
 */
int HashOverflowPageInit(HashOverflowPage *overflowPage, int pageId, int bucketNumber, HashIndex *hashIndex)
{
  /* 1.变量定义 */

  /* 2.参数检查 */
  if (hashIndex == NULL) {
    return GNCDB_INTERNAL;
  }

  /* 4.初始化页基本信息 */
  pageInit(&overflowPage->page, pageId, HASH_OVERFLOW_PAGE, false);

  /* 5.初始化溢出页特有字段 */
  overflowPage->bucketId        = bucketNumber;
  overflowPage->keyTidPairCount = 0;
  overflowPage->prevPageId      = -1;
  overflowPage->nextPageId      = -1;
  overflowPage->primaryKeyLenth = hashIndex->primaryKeyLenth;

  return GNCDB_SUCCESS;
}

/**
 * @brief 从字节流构建HashOverflowPage结构体
 * @param overflowPage 溢出页指针
 * @param pageId 页ID
 * @return 成功返回GNCDB_SUCCESS，失败返回错误码
 */
int HashOverflowPageConstruct(HashOverflowPage *overflowPage, int pageId)
{
  int rc = GNCDB_SUCCESS;
  /* 1.参数检查 */
  if (overflowPage == NULL || overflowPage->page.pData == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 2.初始化页面基本信息 */
  pageInit(&overflowPage->page, pageId, HASH_OVERFLOW_PAGE, false);

  /* 3.读取页面头部信息 */
  rc = hashOverflowPageReadHeader(overflowPage);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return GNCDB_SUCCESS;
}

HashOverflowPage *HashOverflowPageDeepCopy(GNCDB *db, HashOverflowPage *hashOverflowPage)
{
  HashOverflowPage *hashOverflowPageCopy = NULL;

  hashOverflowPageCopy             = (HashOverflowPage *)my_malloc0(sizeof(HashOverflowPage));
  hashOverflowPageCopy->page.pData = (BYTE *)my_malloc0(db->pageCurrentSize);
  pageInit(&hashOverflowPageCopy->page, hashOverflowPage->page.id, BUCKET_PAGE, true);

  hashOverflowPageCopy->bucketId        = hashOverflowPage->bucketId;
  hashOverflowPageCopy->keyTidPairCount = hashOverflowPage->keyTidPairCount;
  hashOverflowPageCopy->prevPageId      = hashOverflowPage->prevPageId;
  hashOverflowPageCopy->nextPageId      = hashOverflowPage->nextPageId;
  hashOverflowPageCopy->primaryKeyLenth = hashOverflowPage->primaryKeyLenth;

  memcpy(hashOverflowPageCopy->page.pData, hashOverflowPage->page.pData, db->pageCurrentSize);

  return hashOverflowPageCopy;
}

/**
 * @brief	销毁缓冲池中分配的HashOverflowPage
 * @param overflowPage 指向HashOverflowPage指针的指针
 * @param db 数据库结构指针
 */
void HashoverflowPageDestroy(HashOverflowPage **overflowPage)
{
  if (overflowPage == NULL || *overflowPage == NULL) {
    return;
  }
  ReadWriteLockDestroy(&((*overflowPage)->page.rwlock_t));
  // 销毁条目数组（会自动调用HashkeyTidPairDestroy清理每个键值对）
  // varArrayListDestroy(&(*overflowPage)->entryArray);

  // 释放溢出页结构体
  // my_free(*overflowPage);
  *overflowPage = NULL;
}

/**
 * @description: 销毁独立malloc分配的HashOverflowPage
 * @param {HashOverflowPage} *
 * @return {*}
 */
void HashOverflowPageDestroyMalloc(HashOverflowPage **overflowPage)
{
  if (overflowPage == NULL || *overflowPage == NULL) {
    return;
  }
  ReadWriteLockDestroy(&((*overflowPage)->page.rwlock_t));
  my_free((*overflowPage)->page.pData);
  my_free(*overflowPage);
  *overflowPage = NULL;
}

/**
 * @brief 将HashOverflowPage转换为字节数组
 * @param overflowPage HashOverflowPage指针
 * @return 成功返回字节数组指针，失败返回NULL
 */
BYTE *HashOverflowPageToByte(HashOverflowPage *overflowPage)
{
  int rc = GNCDB_SUCCESS;
  /* 1.参数检查 */
  if (overflowPage == NULL || overflowPage->page.pageType != HASH_OVERFLOW_PAGE) {
    return NULL;
  }

  /* 2.填充页面头部信息 */
  rc = hashOverflowPageFillHeader(overflowPage);
  if (rc != GNCDB_SUCCESS) {
    return NULL;
  }

  /* 3.返回页面数据 */
  return overflowPage->page.pData;
}

/**
 * @brief HashOverflowPage头部填充函数
 * @param overflowPage HashOverflowPage指针
 * @param buf 缓冲区
 */
int hashOverflowPageFillHeader(HashOverflowPage *overflowPage)
{
  /* 1.定义偏移量和返回值 */
  int   offset = 0;
  int   rc     = 0;
  BYTE *buf    = overflowPage->page.pData;

  /* 2.参数检查 */
  if (overflowPage == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.写入页类型标记 */
  rc = writeChar((char)HASH_OVERFLOW_PAGE, buf + offset, &offset);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 4.写入溢出页信息 */
  writeInt(overflowPage->bucketId, buf + offset, &offset);
  writeInt(overflowPage->keyTidPairCount, buf + offset, &offset);
  writeInt(overflowPage->prevPageId, buf + offset, &offset);
  writeInt(overflowPage->nextPageId, buf + offset, &offset);
  writeInt(overflowPage->primaryKeyLenth, buf + offset, &offset);
  return GNCDB_SUCCESS;
}

/**
 * @brief 读取哈希溢出页的头部
 * @param overflowPage 溢出页指针
 * @return 返回读取结果，成功返回GNCDB_SUCCESS，否则返回错误码
 */
int hashOverflowPageReadHeader(HashOverflowPage *overflowPage)
{
  /* 1.定义临时变量和偏移量 */
  int   offset = 1; /* 跳过页类型标记 */
  int   temp   = 0;
  BYTE *buf    = overflowPage->page.pData;

  /* 2.参数检查 */
  if (overflowPage == NULL || buf == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.检查页类型标记 */
  if ((int)buf[0] != HASH_OVERFLOW_PAGE) { /* 不是哈希溢出页 */
    return GNCDB_HASHPAGE_UNSAFE;
  }

  /* 4.读取溢出页信息 */
  readInt(&temp, buf + offset, &offset);
  overflowPage->bucketId = temp;

  readInt(&temp, buf + offset, &offset);
  overflowPage->keyTidPairCount = temp;

  readInt(&temp, buf + offset, &offset);
  overflowPage->prevPageId = temp;

  readInt(&temp, buf + offset, &offset);
  overflowPage->nextPageId = temp;

  readInt(&temp, buf + offset, &offset);
  overflowPage->primaryKeyLenth = temp;

  return GNCDB_SUCCESS;
}