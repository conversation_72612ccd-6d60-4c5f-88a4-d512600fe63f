/**
 * @file btreepage.c
 * <AUTHOR>
 * @brief  页操作的实现
 * @version 0.1
 * @date 2023-02-02
 *
 * @copyright Copyright (c) 2023
 *
 */

 #include"btreepage.h"
 #include "catalog.h"
 #include"field.h"
 #include "os.h"
 #include"gncdbconstant.h"
 #include "typedefine.h"
 #include "vararraylist.h"
 #include "gncdb.h"
 #include "pagepool.h"
 #include <time.h>
 
  /***************************************************************************************page***********************************************************************/
 
  /**
   * @brief page的初始化
   * @param page
   * @param pageId
   * @param pageType
   */
 void pageInit(struct Page* page, int pageId, PageType pageType, bool isMalloc) {
	 page->id = pageId;
	 page->pageType = pageType;
	 page->isMalloc = isMalloc;
	 ReadWriteLockInit(&(page->rwlock_t));
 }
 
 /**
  * @brief page页的销毁
  * @param page
  */
 void pageDestroy(struct Page** page) {
	 /* 1.判断参数是否为空 */
	 if (page == NULL || *page == NULL) {
		 return;
	 }
 
	 /* 2.释放内存空间 */
	 ReadWriteLockDestroy(&((*page)->rwlock_t));
	 my_free(*page);
	 *page = NULL;
 }
 
 /****************************************************************************************btreePage************************************************************/
 
  /**
   * @brief 填充btreePage的头部
   * @param btreePage btreePage指针
   * @param buf	字节缓冲区
   * @return 返回填充结果，成功返回GNCDB_SUCCESS，否则返回错误码
   */
 int btreePageFillInHeader(BtreePage* btreePage) {
	 /* 1.定义偏移量和返回值 */
	 int offset = 0;
	 int rc = 0;
	 BYTE* buf = btreePage->page.pData;
 
	 /* 2.参数检查 */
	 if (btreePage == NULL || buf == NULL) { 
		 return GNCDB_PARAMNULL;
	 }	
 
	 /* 3.根据页类型写入页类型标记 */
	 if (btreePage->page.pageType == LEAF_PAGE) {/* 叶子页 */		
		 rc = writeChar((char)0x00, buf + offset, &offset);
	 }
	 else if (btreePage->page.pageType == INTERNAL_PAGE) {/* 内部页 */		
		 rc = writeChar((char)0x01, buf + offset, &offset);
	 }
	 else{
		 return GNCDB_PARAMNULL;
	 }
	 /* 4.如果写入失败，返回错误码 */
	 if (rc != GNCDB_SUCCESS) {
		 return rc;
	 }
 
	 /* 5.调用底层的OS接口，转化成字节流，存放到buf中 */
	 writeInt(btreePage->keyLength, buf + offset, &offset);
	 writeInt(btreePage->entryNum, buf + offset, &offset);
	 writeInt(btreePage->nextPageId, buf + offset, &offset);
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * @brief 读取btreePage的头部
  * @param btreePage btreePage指针
  * @param buf	  字节缓冲区
  * @return 返回读取结果，成功返回GNCDB_SUCCESS，否则返回错误码
  */
 int btreePageReadHeader(BtreePage* btreePage) {
	 /* 1.定义临时变量和偏移量 */
	 int offset = 1;
	 int temp = 0;
	 BYTE* buf = btreePage->page.pData;
 
	 /* 2.参数检查 */
	 if (btreePage == NULL || buf == NULL) {
		 return GNCDB_PARAMNULL;
	 }	
 
	 /* 3.读取页类型标记并设置B树页的页类型 */
	 if ((int)buf[0] == 0) { /* 叶子页 */		
		 btreePage->page.pageType = LEAF_PAGE;
	 }
	 else { /* 内部页 */		
		 btreePage->page.pageType = INTERNAL_PAGE;
	 }
 
	 /* 4.读取key长度、页项数、下一页ID*/
	 readInt(&temp, buf + offset, &offset);
	 btreePage->keyLength = temp;
	 readInt(&temp, buf + offset, &offset);
	 btreePage->entryNum = temp;
	 //fcd
	 // readShort(&temp, buf + offset, &offset);
	 // btreePage->parentPageId = (int)temp;
	 readInt(&temp, buf + offset, &offset);
	 btreePage->nextPageId = temp;
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * @brief btreePage的创建
  * @param pageData  btreePage的字节数组
  * @param pageId 页的id
  * @param tableName 表的名字
  * @param tableSchema 
  * @param catalog
  * @return btreePage指针
  */
 int btreePageConstruct(BtreePage* btreePage, int pageId, char* tableName) {
	 /* 1.定义变量 */
	 // int offset = 0;
	 int rc = 0;
	 // BtreePage* btreePage = 0;
 
	 /* 2.判断参数是否为空 */
	 if (tableName == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 3.构建一个btreePage */
	 // btreePage = (BtreePage*)my_malloc(sizeof(BtreePage));
	 // if (btreePage == NULL) {
	 // 	return NULL;
	 // }
	 
	 /* 4.初始化page */
	  /*  这里对page页只是随机初始化一下，具体的页信息后续会修改 */
	 pageInit(&btreePage->page,pageId,LEAF_PAGE,false); 
 
	 /* 5.读取页的头部信息 */
	 rc = btreePageReadHeader(btreePage);
	 if (rc != GNCDB_SUCCESS) {
		 btreePageDestroy(&btreePage);
		 return rc;
	 }
	 // offset += PAGE_HEAD_SIZE;	/*  增加页内偏移量 */
	 // btreePage->tableName = (char*)my_malloc(strlen(tableName)+1);
	 // if (btreePage->tableName == NULL) {
	 // 	btreePageDestroy(&btreePage);
	 // 	return NULL;
	 // }
	 memcpy(btreePage->tableName,tableName,strlen(tableName));
	 btreePage->tableName[strlen(tableName)] = '\0';

	 btreePage->prevInsertIndex = 0;
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * 初始化新的B树页，不包含数据，返回状态码
  * @param btreePage 待构造的BtreePage结构体指针
  * @param pageType B树页的类型，是叶子页还是内部页
  * @param pageId B树页的ID
  * @param tableName 页所属的表的名称
  * @param catalog Catalog的指针
  * @return 状态码
  */
 int btreePageConstructWithOutData(struct BtreePage* btreePage, PageType pageType, int pageId, char* tableName, struct Catalog* catalog) {
	 /* 1.变量定义 */
	 Column* column = NULL;
	 FieldType fieldType;
	 int isPrimaryKey = 0;
	 int size = 0;
	 int length = 0;
	 varArrayList* primaryIndexArray = NULL;
	 varArrayList* primaryTypeArray = NULL;
	 TableSchema* tableSchema = getTableSchema(catalog, tableName);
 
	 // if(pageId == 410){
	 // 	printf("\n");
	 // }
 
	 /* 2.检查表名是否为空 */
	 if (tableName == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 4.初始化page */
	 pageInit(&btreePage->page,pageId, pageType,false);
 
	 /* 5.复制tableName */
	 length = strlen(tableName);
	 if(length > TABLENAME_FIELD_MAXLEN) {
		 return GNCDB_PARAMNULL;
	 }
	 memcpy(btreePage->tableName, tableName, length);
	 btreePage->tableName[length] = '\0';
 
	 /* 6.初始化BtreePage结构体的各个成员变量 */
	 btreePage->keyLength = 0;
	 //* 先遍历tableSchema的columnList获取key的长度 */
	 if (pageType == INTERNAL_PAGE) {
		 for (int index = 0; index < tableSchema->columnList->elementCount; index++) {
			 column = (Column *)varArrayListGetPointer(tableSchema->columnList, index);
			 if (column == NULL) {
				 return GNCDB_PARAMNULL;
			 }
			 isPrimaryKey = column->columnConstraint->isPrimaryKey;
			 if (isPrimaryKey) {
				 fieldType = column->fieldType;
				 switch (fieldType) {
				 case FIELDTYPE_INTEGER: {
					 btreePage->keyLength += INT_SIZE;
					 break;
				 }
				 case FIELDTYPE_REAL: {
					 btreePage->keyLength += DOUBLE_SIZE;
					 break;
				 }
				 case FIELDTYPE_VARCHAR: {
					 size = (int)column->columnConstraint->maxValue;
					 btreePage->keyLength += size;
					 break;
				 }
				 default:
					 break;
				 }
			 }
		 }
	 }
	 btreePage->entryNum = 0;
	 /*pointer btreePage->leftSiblingPageId = 0;
	 btreePage->rightSiblingPageId = 0; */
	 btreePage->nextPageId = 0;
	 btreePage->prevInsertIndex = 0;
 
	 /* 7.根据页的类型创建entryArray变量 */
	 if (pageType == LEAF_PAGE) {
		 /* 获取表的主键索引数组 */
		 primaryIndexArray = getPrimaryIndexArray(catalog, tableName);
		 if (primaryIndexArray == NULL) {
			 btreePageDestroy(&btreePage);
			 return GNCDB_NOT_FOUND;
		 }
 
		 /* 创建entryArray变量 */
		 // btreePage->entryArray = varArrayListCreate(ORDER, BYTES_POINTER, 0, leafTupleCompareFun, leafTupleDestroy);
		 // if (btreePage->entryArray == NULL) {
		 // 	btreePageDestroy(&btreePage);
		 // 	return NULL;
		 // }
 
		 /* 设置entryArray的reserve属性 */
		 // btreePage->entryArray->reserve = primaryIndexArray;
	 }
	 else {
		 /* 获取表的主键类型数组 */
		 primaryTypeArray = getPrimaryTypeArray(catalog,tableName);
		 if (primaryTypeArray == NULL) {
			 btreePageDestroy(&btreePage);
			 return GNCDB_NOT_FOUND;
		 }
 
		 /* 创建entryArray变量 */		
		 // btreePage->entryArray = varArrayListCreate(ORDER, BYTES_POINTER, 0, internalEntryCompareFun, internalEntryDestroy);
		 // if (btreePage->entryArray == NULL) {
		 // 	btreePageDestroy(&btreePage);
		 // 	return NULL;
		 // }
 
		 /* 设置entryArray的reserve属性 */
		 // btreePage->entryArray->reserve = primaryTypeArray;
	 }
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * 构造一个新的B树页，不包含数据，返回指向BtreePage结构体的指针
  * @param pageType B树页的类型，是叶子页还是内部页
  * @param pageId B树页的ID
  * @param tableName 页所属的表的名称
  * @param catalog Catalog的指针
  * @return 构造的BtreePage结构体指针，如果失败则返回NULL
  */
 struct BtreePage* btreePageMallocConstructWithOutData(struct GNCDB* db, PageType pageType, int pageId, char* tableName, struct Catalog* catalog) {
	 /* 1.变量定义 */
	 BtreePage* btreePage = NULL;
	 TableSchema* tableSchema = getTableSchema(catalog, tableName);
	 Column* column = NULL;
	 FieldType fieldType;
	 int isPrimaryKey = 0;
	 int size = 0;
	 int length = 0;
 
	 /* 2.d检查表名是否为空 */
	 if (tableName == NULL) {
		 return NULL;
	 }
 
	 /* 3.分配BtreePage结构体空间 */
	 btreePage = (BtreePage*)my_malloc(sizeof(BtreePage));
	 if (btreePage == NULL) {
		 return NULL;
	 }
	 btreePage->page.pData = (BYTE*)my_malloc((db->pageCurrentSize));
 
	 /* 4.初始化page */
	 pageInit(&btreePage->page,pageId, pageType,true);
 
	 /* 5.复制tableName */
	 length = strlen(tableName);
	 // btreePage->tableName = (char*)my_malloc(length + 1);
	 // if (btreePage->tableName == NULL) {
	 // 	btreePageDestroy(&btreePage);
	 // 	return NULL;
	 // }
	 memcpy(btreePage->tableName, tableName, length);
	 btreePage->tableName[length] = '\0';
 	 btreePage->prevInsertIndex = 0;
	 /* 6.初始化BtreePage结构体的各个成员变量 */
	 btreePage->keyLength = 0;
	 //* 先遍历tableSchema的columnList获取key的长度 */
	 if (pageType == INTERNAL_PAGE) {
		 for (int index = 0; index < tableSchema->columnList->elementCount; index++) {
			 column = (Column *)varArrayListGetPointer(tableSchema->columnList, index);
			 if (column == NULL) {
				 return NULL;
			 }
			 isPrimaryKey = column->columnConstraint->isPrimaryKey;
			 if (isPrimaryKey) {
				 fieldType = column->fieldType;
				 switch (fieldType) {
				 case FIELDTYPE_INTEGER: {
					 btreePage->keyLength += INT_SIZE;
					 break;
				 }
				 case FIELDTYPE_REAL: {
					 btreePage->keyLength += DOUBLE_SIZE;
					 break;
				 }
				 case FIELDTYPE_VARCHAR: {
					 size = (int)column->columnConstraint->maxValue;
					 btreePage->keyLength += size;
					 break;
				 }
				 default:
					 break;
				 }
			 }
		 }
	 }
	 btreePage->entryNum = 0;
	 /*pointer btreePage->leftSiblingPageId = 0;
	 btreePage->rightSiblingPageId = 0; */
	 btreePage->nextPageId = 0;
 
	 /* 7.根据页的类型创建entryArray变量 */
	 // if (pageType == LEAF_PAGE) {
	 // 	/* 获取表的主键索引数组 */
	 // 	primaryIndexArray = getPrimaryIndexArray(catalog, tableName);
	 // 	if (primaryIndexArray == NULL) {
	 // 		btreePageDestroy(&btreePage);
	 // 		return NULL;
	 // 	}
 
	 // 	/* 创建entryArray变量 */
	 // 	btreePage->entryArray = varArrayListCreate(ORDER, BYTES_POINTER, 0, leafTupleCompareFun, leafTupleDestroy);
	 // 	if (btreePage->entryArray == NULL) {
	 // 		btreePageDestroy(&btreePage);
	 // 		return NULL;
	 // 	}
 
	 // 	/* 设置entryArray的reserve属性 */
	 // 	btreePage->entryArray->reserve = primaryIndexArray;
	 // }
	 // else {
	 // 	/* 获取表的主键类型数组 */
	 // 	primaryTypeArray = getPrimaryTypeArray(catalog,tableName);
	 // 	if (primaryTypeArray == NULL) {
	 // 		btreePageDestroy(&btreePage);
	 // 		return NULL;
	 // 	}
 
	 // 	/* 创建entryArray变量 */		
	 // 	btreePage->entryArray = varArrayListCreate(ORDER, BYTES_POINTER, 0, internalEntryCompareFun, internalEntryDestroy);
	 // 	if (btreePage->entryArray == NULL) {
	 // 		btreePageDestroy(&btreePage);
	 // 		return NULL;
	 // 	}
 
	 // 	/* 设置entryArray的reserve属性 */
	 // 	btreePage->entryArray->reserve = primaryTypeArray;
	 // }
 
	 return btreePage;
 }
 
 
 /**
  * 对一个B树页进行深度拷贝，并返回新的BtreePage结构体的指针
  * @param btreePage 要拷贝的BtreePage结构体指针
  * @param catalog Catalog的指针
  * @return 拷贝后的BtreePage结构体指针，如果失败则返回NULL
  */
 struct BtreePage* btreePageDeepCopy(struct GNCDB* db, struct BtreePage* btreePage, struct Catalog* catalog) {
	 /* 1.变量定义 */
	 BtreePage* btreePageCopy = NULL;
 
	 /* 2.检查输入参数是否为NULL */
	 if (btreePage == NULL || catalog == NULL) {
		 return NULL;
	 }
 
	 /* 3.创建深拷贝的BtreePage结构体 */
	 btreePageCopy = btreePageMallocConstructWithOutData(db, btreePage->page.pageType, btreePage->page.id, btreePage->tableName, catalog);
	 if (btreePageCopy == NULL) {
		 return NULL;
	 }
 
	 /* 4.复制BtreePage结构体的各个成员变量 */
	 btreePageCopy->keyLength = btreePage->keyLength;
	 btreePageCopy->keyLength = btreePage->keyLength;
	 btreePageCopy->entryNum = btreePage->entryNum;
	 btreePageCopy->nextPageId = btreePage->nextPageId;
 
	 /* 5. 拷贝页数据 */
	 memcpy(btreePageCopy->page.pData,btreePage->page.pData,(db->pageCurrentSize));
 
	 return btreePageCopy;
 }
 
 /**
  * @brief btreePage转化为字节流
  * @param btreePage btreePage指针
  * @param tableSchema tableSchema指针
  * @return 返回字节流数组的指针
  */
 BYTE* btreePageToByte(BtreePage* btreePage) {
	 /* 1.变量的定义 */

	 BYTE* pageData = NULL;
	 int rc = 0;

 
	 /* 2.判断参数是否为空 */
	 if (btreePage == NULL ) {
		 return NULL;
	 }
 
	 pageData = btreePage->page.pData;
 
	 /* 3.填充页的头部 */
	 rc = btreePageFillInHeader(btreePage);
	 if (rc != GNCDB_SUCCESS) {
		 return NULL;
	 }
 
	 return pageData;
 }
 
 /**
  * @brief btreePage的销毁
  * @param btreePage btreePage指针
  */
 void btreePageDestroy(BtreePage** btreePage) {
	 /* 1.判断参数是否为空 */
	 if (btreePage == NULL || *btreePage == NULL) {
		 return;
	 }
 
	 /* 2.释放内存空间 */
	 ReadWriteLockDestroy(&((*btreePage)->page.rwlock_t));
	 *btreePage = NULL;
 }

void btreePageDestroyMalloc(BtreePage** btreePage) {
	/* 1.判断参数是否为空 */
	if (btreePage == NULL || *btreePage == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	ReadWriteLockDestroy(&((*btreePage)->page.rwlock_t));
	my_free((*btreePage)->page.pData);
	my_free(*btreePage);
	*btreePage = NULL;
}


 
 /**
  * @brief 实现B+中两个页的交换，使得B+树在插入和删除的过程中始终保持根节点保持不变（leftBtreePage为最初的根节点，parentBtreePage为新创建的根节点）
  * @param btreeTable
  * @param leftBtreePage
  * @param parentBtreePage
  * @param db
  * @param tx
  */
 int btreePageExchange(struct BtreeTable* btreeTable, struct BtreePage* leftBtreePage, struct BtreePage* parentBtreePage, struct GNCDB* db,struct Transaction* tx) {
	 /* 1.变量的定义 */
	 int rc = 0;
	 //PageStatus* parentPageStatus = NULL, *leftPageStatus = NULL;
	 int id = 0;
	 //BtreePage* childBtreePage = NULL;
 
	 /* 2.判断参数是否为空 */
	 if (btreeTable == NULL || leftBtreePage == NULL || parentBtreePage == NULL || db == NULL || tx == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 4.交换两个页的页号 */
	 id = leftBtreePage->page.id;
	 leftBtreePage->page.id = parentBtreePage->page.id;
	 parentBtreePage->page.id = id;
 
	 /* 5.pageMap需要做交换 */
	 rc = pageExchange(db->pagePool, parentBtreePage->page.id, (Page*)leftBtreePage, leftBtreePage->page.id, (Page*)parentBtreePage);
	 if (rc != GNCDB_SUCCESS) {
		 return rc;
	 }
	 return GNCDB_SUCCESS;
 }
 
 /********************************************************************************************leafPage************************************************************/
 
 /**
  * 向叶子页中插入一个Tuple
  * @param btreePage 要插入Tuple的叶子页的BtreePage结构体指针
  * @param tuple 要插入的Tuple结构体指针
  * @return 成功返回GNCDB_SUCCESS，否则返回错误码
  */
 int leafPageInsertTuple(BtreePage* btreePage, BYTE* record, TableSchema* tableSchema, struct BtreeTable* btreeTable) {
	 /* 1.变量定义 */
	 // int rc = 0;
	 int offset = PAGE_HEAD_SIZE;
	 BYTE* curRecord = NULL;
	 BYTE* toInsertRecord = NULL;
	 int i = 0;
 
	 /* 2.检查输入参数是否为NULL */
	 if (btreePage == NULL || record == NULL) {
		 return GNCDB_PARAMNULL;
	 }
	 curRecord = btreePage->page.pData + offset;
	 //遍历叶子页的entryArray，找到插入位置
	 for(i = 0; i < btreePage -> entryNum; i++){
		 if(leafRecordCompareFun(curRecord, record, tableSchema) < 0){
			 offset += btreeTable->leafRecordLength;
			 curRecord = btreePage->page.pData + offset;
			 continue;
		 }
		 else{
			 
			 break;
		 }
	 }
	 btreePage->prevInsertIndex = i;
	 //找到插入位置
	 toInsertRecord = curRecord;
	 //将插入位置之后的记录后移
	 memmove(toInsertRecord + btreeTable->leafRecordLength, toInsertRecord, (btreePage -> entryNum - i) * btreeTable->leafRecordLength);
	 //将record插入到toInsertRecord
	 memcpy(toInsertRecord, record, btreeTable->leafRecordLength);
	 btreePage->entryNum++;
	 return GNCDB_SUCCESS;
	 
 
	 /* 4.更新叶子页中Tuple的总数 */
	 btreePage->entryNum += 1;
 
	 return GNCDB_SUCCESS;
 }
 /**
  * @brief tuple比较特定字段的值，如果满足判断条件，则返回1，不符合则返回0
  * @param valueFirst
  * @param valueSecond
  * @param fieldType
  * @param predicate 比较符号
  * @return 0：valueFirst不满足判断条件；1：valueFirst满足判断条件
  */
 int compareCertainFieldValue(void* valueFirst,void* valueSecond,FieldType fieldType, Predicate predicate) {
	 int flag = 1;	/* 初始化为1（0：表示该valueFirst不满足当前判断条件，1：表示该valueSecond满足当前判断条件） */
	 int result = 0;
	 int int_temp1 = 0, int_temp2 = 0;
	 double double_temp1 = 0, double_temp2 = 0;
	 char char_temp1[100] = {0}, char_temp2[100]={0};
 
	 /* 2.判断参数是否为空 */
	 if (valueFirst == NULL || valueSecond == NULL) {
		 return GNCDB_PARAMNULL;
	 }	
 
	 /* 3.根据不同的数据类型进行比较 */
	 if (fieldType == FIELDTYPE_INTEGER) {
		 memcpy(&int_temp1, valueFirst, INT_SIZE);
		 memcpy(&int_temp2, valueSecond, INT_SIZE);
		 result = int_temp1 > int_temp2 ? 1 : (int_temp1 < int_temp2 ? -1 : 0);
	 }
	 else if (fieldType == FIELDTYPE_REAL) {
		 memcpy(&double_temp1, valueFirst, DOUBLE_SIZE);
		 memcpy(&double_temp2, valueSecond, DOUBLE_SIZE);
		 result = double_temp1 > double_temp2 ? 1 : (double_temp1 < double_temp2 ? -1 : 0);
	 }
	 else if (fieldType == FIELDTYPE_VARCHAR) {
		 memcpy(char_temp1, valueFirst, strlen((char*)valueFirst));
		 memcpy(char_temp2, valueSecond, strlen((char*)valueSecond));
		 result = strcmp(char_temp1, char_temp2);
	 }
 
	 /* 4.根据判断符号predicate设置flag */
	 if (predicate == EQUAL) {
		 if (result != 0) {
			 flag = 0;
		 }
	 }
	 else if (predicate == GREATER_THAN) {
		 if (!(result > 0)) {
			 flag = 0;
		 }
	 }
	 else if (predicate == LESS_THAN) {
		 if (!(result < 0)) {
			 flag = 0;
		 }
	 }
	 else if (predicate == GREATER_THAN_OR_EQUAL) {
		 if (!(result == 0 || result > 0)) {
			 flag = 0;
		 }
	 }
	 else if (predicate == LESS_THAN_OR_EQUAL) {
		 if (!(result == 0 || result < 0)) {
			 flag = 0;
		 }
	 }
	 return flag;
 }

/**
  * @brief 比较特定字段的值，小于返回-1，等于返回0，大于返回1
  * @param valueFirst
  * @param valueSecond
  * @return 0：valueFirst不满足判断条件；1：valueFirst满足判断条件
  */
 int compareIntValue(void* valueFirst,void* valueSecond){
 	int result = 0;
	if (valueFirst == NULL || valueSecond == NULL) {
		return GNCDB_PARAMNULL;
	}	
	result = *(int*)valueFirst > *(int*)valueSecond ? 1 : (*(int*)valueFirst < *(int*)valueSecond ? -1 : 0);
	return result;
 }
 /**
  * @brief 比较特定字段的值，小于返回-1，等于返回0，大于返回1
  * @param valueFirst
  * @param valueSecond
  * @return 0：valueFirst不满足判断条件；1：valueFirst满足判断条件
  */
 int compareRealValue(void* valueFirst,void* valueSecond){
	int result = 0;
	if (valueFirst == NULL || valueSecond == NULL) {
		return GNCDB_PARAMNULL;
	}
	result = *(float*)valueFirst > *(float*)valueSecond ? 1 : (*(float*)valueFirst < *(float*)valueSecond ? -1 : 0);
	return result;
 }
/**
  * @brief 比较特定字段的值，小于返回-1，等于返回0，大于返回1
  * @param valueFirst
  * @param valueSecond
  * @return 0：valueFirst不满足判断条件；1：valueFirst满足判断条件
  */
 int compareVarcharValue(void* valueFirst,void* valueSecond){
	int result = 0;
	if (valueFirst == NULL || valueSecond == NULL) {
		return GNCDB_PARAMNULL;
	}
	result = strcmp((char*)valueFirst, (char*)valueSecond);
	return result;
 }

 
 int leafTupleCompareCertainFieldValue(void* valueFirst,void* valueSecond,FieldType fieldType, Predicate predicate) {
	 int flag = 1;	/* 初始化为1（0：表示该valueFirst不满足当前判断条件，1：表示该valueSecond满足当前判断条件） */
	 int result = 0;
	 int* int_temp1 = NULL, * int_temp2 = NULL;
	 double* double_temp1 = NULL, * double_temp2 = NULL;
	 char* char_temp1 = NULL, * char_temp2 = NULL;
	 DateTimeField* datetime_temp1 = NULL, * datetime_temp2 = NULL;
	 /* 2.判断参数是否为空 */
	 if (valueFirst == NULL || valueSecond == NULL) {
		 return GNCDB_PARAMNULL;
	 }	
 
	 /* 3.根据不同的数据类型进行比较 */
	 if (fieldType == FIELDTYPE_INTEGER) {
		 memcpy(&int_temp1, (int *)valueFirst, INT_SIZE);
		 memcpy(&int_temp2, (int *)valueSecond, INT_SIZE);
		 result = int_temp1 > int_temp2 ? 1 : (int_temp1 < int_temp2 ? -1 : 0);
	 }
	 else if (fieldType == FIELDTYPE_REAL) {
		 memcpy(&double_temp1, (double *)valueFirst, DOUBLE_SIZE);
		 memcpy(&double_temp2, (double *)valueSecond, DOUBLE_SIZE);
		 result = double_temp1 > double_temp2 ? 1 : (double_temp1 < double_temp2 ? -1 : 0);
	 }
	 else if (fieldType == FIELDTYPE_VARCHAR) {
		 memcpy(char_temp1, valueFirst, strlen((char*)valueFirst));
		 memcpy(char_temp2, valueSecond, strlen((char*)valueSecond));
		 result = strcmp(char_temp1, char_temp2);
	 }
	 else if(fieldType == FIELDTYPE_DATE){
		 int_temp1 = (int*)valueFirst;
		 int_temp2 = (int*)valueSecond;
		 result = *int_temp1 > *int_temp2 ? 1 : (*int_temp1 < *int_temp2 ? -1 : 0);
	 }
	 else if(fieldType == FIELDTYPE_DATETIME){
		 datetime_temp1 = (DateTimeField*)valueFirst;
		 datetime_temp2 = (DateTimeField*)valueSecond;
		 result =  DateTimeFieldCompare(datetime_temp1,datetime_temp2);
	 }
 
	 /* 4.根据判断符号predicate设置flag */
	 if (predicate == EQUAL) {
		 if (result != 0) {
			 flag = 0;
		 }
	 }
	 else if (predicate == GREATER_THAN) {
		 if (!(result > 0)) {
			 flag = 0;
		 }
	 }
	 else if (predicate == LESS_THAN) {
		 if (!(result < 0)) {
			 flag = 0;
		 }
	 }
	 else if (predicate == GREATER_THAN_OR_EQUAL) {
		 if (!(result == 0 || result > 0)) {
			 flag = 0;
		 }
	 }
	 else if (predicate == LESS_THAN_OR_EQUAL) {
		 if (!(result == 0 || result < 0)) {
			 flag = 0;
		 }
	 }
 
	 return flag;
 
 }
 
 
 // Tuple* leafPageFindEntryByKeyvalue(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName,
 // 		struct Catalog* catalog,Predicate predicate) {
 // 	/* 1.变量的定义 */
 // 	//int result = 0;	/* 0:两者相等，正数：比keyValueArray大，负数：比keyValueArray小 */
 // 	int flag = 0;   /* 标志是否找到了与keyValueArray值相等的tuple（1:找到了，0：未找到） */
 // 	Tuple* tuple = NULL;
 // 	int keyValueNum = 0;
 // 	int* fieldIndex = 0;
 // 	//void* valueFirst = NULL;
 // 	void* valueSecond = NULL;
 // 	Field* field = NULL;
 // 	FieldType fieldType = 0;
 // 	int i = 0, j = 0;
 // 	IntField* intField = NULL;
 // 	int* intValue = NULL;
 // 	RealField* realField = NULL;
 // 	double* doubleValue = NULL;
 // 	VarCharField* varCharField = NULL;
 // 	char* varCharValue = NULL;
 //     varArrayList* primaryIndexArray = NULL;
 
 // 	/* 2.判断参数是否为空 */
 // 	if (btreePage == NULL || keyValueArray == NULL || tableSchema == NULL || tableName == NULL || catalog == NULL) {
 // 		return NULL;
 // 	}
 
 // 	/* 3.获取primaryIndexArray */
 
 //     LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
 //     WriteLock(&(catalog->keyIndexLatch));
 //     LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
 //     primaryIndexArray = hashMapGet(catalog->tablePrimaryKeyIndexMap, tableName);
 //     LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
 //     WriteUnLock(&(catalog->keyIndexLatch));
 //     LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");
 // 	if (primaryIndexArray == NULL) {
 // 		return NULL;
 // 	}
 
 // 	/* 4.依次比较主键，找到满足条件的tuple */
 // 	for (i = 0; i < btreePage->entryNum; i++) {
 // 		flag = 1;  /* flag初始化 */
 //  		tuple = (Tuple*)varArrayListGetPointer(btreePage->entryArray, i);
 // 		if (tuple == NULL) {
 // 			return NULL;
 // 		}
 
 // 		keyValueNum = keyValueArray->elementCount;
 // 		for (j = 0; j < keyValueNum; j++) {
 // 			fieldIndex = (int*)varArrayListGet(primaryIndexArray, j);
 // 			if(fieldIndex == NULL) {
 // 				return NULL;
 // 			}
 
 // 			valueSecond = varArrayListGetPointer(keyValueArray,j);
 // 			if (valueSecond == NULL) {
 // 				return NULL;
 // 			}
 
 // 			field = (Field*)varArrayListGetPointer(tuple->fieldArray,*fieldIndex);
 // 			if (field == NULL) {
 // 				return NULL;
 // 			}
 // 			fieldType = field->fieldType;
 // 			switch (fieldType)
 // 			{
 // 			case FIELDTYPE_INTEGER: {
 // 				intField = (IntField*)field;
 // 				intValue = (int*)valueSecond;
 // 				flag = compareCertainFieldValue(&(intField->value), intValue, fieldType, predicate);
 // 				break;
 // 			}
 // 			case FIELDTYPE_REAL: {
 // 				realField = (RealField*)field;
 // 				doubleValue = (double*)valueSecond;
 // 				flag = compareCertainFieldValue(&(realField->value), doubleValue, fieldType, predicate);
 // 				break;
 // 			}
 // 			case FIELDTYPE_VARCHAR: {
 // 				varCharField = (VarCharField*)field;
 // 				varCharValue = (char*)valueSecond;
 // 				flag = compareCertainFieldValue(varCharField->value, varCharValue, fieldType, predicate);
 // 				break;
 // 			}
 // 			default:
 // 				break;
 // 			}
 
 // 			if (flag == 0) {
 // 				break;
 // 			}
 // 		}
		 
 // 		/* 找到了与keyValueArray值符合判断条件的tuple */
 // 		if (flag == 1) {
 // 			return tuple;
 // 		}
 // 	}
 
 // 	return NULL;
 // }

 /**
  * @brief 在leafPage中根据主键值查找对应的tuple索引号
  * @param btreePage btreePage指针
  * @param keyValueArray 主键值
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @param predicate 比较符号，用于区分在叶子页中找到与keyValueArray值相等还是比keyValueArray大还是小的第一个tuple
  * @return tuple指针
  */
 int leafPageFindEntryIndexByKeyvalue(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog, Predicate predicate,BtreeTable *btreeTable){
	 /* 1.变量的定义 */
	 int left = 0, right = 0, mid = 0;
	 int cmpResult = 0;
	 int result = 0;
	 BYTE* record = NULL;
	 int keyValueNum = 0;
	 int* fieldIndex = 0;
	 void* valueSecond = NULL;
	 FieldType fieldType = 0;
	 int j = 0;
	 int intFieldValue = 0;
	 int* intValue = NULL;
	 double realFieldValue = 0;
	 double* doubleValue = NULL;
	 char varCharFieldValue[255]={0};
	 char* varCharValue = NULL;
	 int varCharLen = 0;
	 varArrayList* primaryIndexArray = NULL;
	 varArrayList* keyOffsetArray = NULL;
	 varArrayList* keyTypeArray = NULL;
	 varArrayList* keyVarcharLengthArray = NULL;
	 int offset = 0;
	 int keyVarcharIndex = 0;

	 /* 2.判断参数是否为空 */
	 if (btreePage == NULL || keyValueArray == NULL || tableSchema == NULL || tableName == NULL || catalog == NULL) {
		 return -1;
	 }

	 /* 3.获取相关数组 */
	 LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
	 WriteLock(&(catalog->keyIndexLatch));
	 LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
	 primaryIndexArray = hashMapGet(catalog->tablePrimaryKeyIndexMap, tableName);
	 LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
	 WriteUnLock(&(catalog->keyIndexLatch));
	 LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");
	 if (primaryIndexArray == NULL) {
		 return -1;
	 }
	 keyOffsetArray = getPrimaryOffsetArray(catalog,tableName);
	 if (keyOffsetArray == NULL) {
		 return -1;
	 }
	 keyTypeArray = getPrimaryTypeArray(catalog,tableName);
	 if (keyTypeArray == NULL) {
		 return -1;
	 }
	 keyVarcharLengthArray = getPrimaryVarcharLenArray(catalog,tableName);
	 if (keyVarcharLengthArray == NULL) {
		 return -1;
	 }

	 /* 4.使用二分查找定位目标记录 */
	 left = 0;
	 right = btreePage->entryNum - 1;
	 keyValueNum = keyValueArray->elementCount;
	 
	 while (left <= right) {
		 mid = left + (right - left) / 2;
		 record = btreePage->page.pData + PAGE_HEAD_SIZE + mid * btreeTable->leafRecordLength;
		 if (record == NULL) {
			 return -1;
		 }
		 
		 cmpResult = 0;
		 keyVarcharIndex = 0;
		 
		 /* 遍历主键字段进行比较 */
		 for (j = 0; j < keyValueNum; j++) {
			 fieldIndex = (int*)varArrayListGet(primaryIndexArray, j);
			 offset = *(int*)varArrayListGet(keyOffsetArray,j);
			 if(fieldIndex == NULL) {
				 return -1;
			 }

			 valueSecond = varArrayListGetPointer(keyValueArray,j);
			 if (valueSecond == NULL) {
				 return -1;
			 }

			 fieldType = *(FieldType*)varArrayListGet(keyTypeArray,j);
			 switch (fieldType)
			 {
			 case FIELDTYPE_INTEGER: {
				 memcpy(&intFieldValue,record + offset,INT_SIZE);
				 intValue = (int*)valueSecond;
				 result = compareIntValue(&intFieldValue, intValue);
				 break;
			 }
			 case FIELDTYPE_REAL: {
				 memcpy(&realFieldValue,record + offset,DOUBLE_SIZE);
				 doubleValue = (double*)valueSecond;
				 result = compareRealValue(&realFieldValue, doubleValue);
				 break;
			 }
			 case FIELDTYPE_VARCHAR: {
				 varCharLen = *(int*)varArrayListGet(keyVarcharLengthArray,keyVarcharIndex);
				 keyVarcharIndex++;
				 memcpy(varCharFieldValue,record + offset,varCharLen);
				 varCharValue = (char*)valueSecond;
				 result = compareVarcharValue(varCharFieldValue, varCharValue);
				 break;
			 }
			 default:
				 break;
			 }
			 
			 /* 如果当前字段不相等，则整体比较结果确定 */
			 if (result != 0) {
				 cmpResult = result;
				 break;
			 }
		 }
		 
		 /* 根据比较结果调整搜索范围 */
		 if (cmpResult == 0) {
			 /* 找到相等的记录，根据predicate决定是否继续查找 */
			 if (predicate == EQUAL || predicate == GREATER_THAN_OR_EQUAL || predicate == LESS_THAN_OR_EQUAL) {
				 return mid;
			 }
			 else if (predicate == GREATER_THAN) {
				 /* 需要找第一个大于的记录 */
				 left = mid + 1;
			 }
			 else if (predicate == LESS_THAN) {
				 /* 需要找第一个小于的记录 */
				 right = mid - 1;
			 }
		 }
		 else if (cmpResult < 0) {
			 /* 当前记录小于目标值，在右半部分查找 */
			 left = mid + 1;
		 }
		 else {
			 /* 当前记录大于目标值，在左半部分查找 */
			 right = mid - 1;
		 }
	 }
	 
	 /* 5.处理未找到精确匹配的情况 */
	 if (predicate == GREATER_THAN || predicate == GREATER_THAN_OR_EQUAL) {
		 /* 返回第一个大于等于目标值的记录索引 */
		 if (left < btreePage->entryNum) {
			 return left;
		 }
	 }
	 else if (predicate == LESS_THAN || predicate == LESS_THAN_OR_EQUAL) {
		 /* 返回第一个小于等于目标值的记录索引 */
		 if (right >= 0) {
			 return right;
		 }
	 }

	 return -1;
 }
 /**
  * @brief 在leafPage中根据主键值查找对应的tuple
  * @param btreePage btreePage指针
  * @param keyValueArray 主键值
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @param predicate 比较符号，用于区分在叶子页中找到与keyValueArray值相等还是比keyValueArray大还是小的第一个tuple
  * @return tuple指针
  */
 BYTE* leafPageFindEntryByKeyvalue(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName,
	 struct Catalog* catalog,Predicate predicate,BtreeTable *btreeTable) {
	 /* 1.变量的定义 */
	 int result = 0;	/* 0:两者相等，正数：比keyValueArray大，负数：比keyValueArray小 */
	 int flag = 1;   /* 标志是否找到了与keyValueArray值相等的tuple（1:找到了，0：未找到） */
	 BYTE* record = NULL;
	 int keyValueNum = 0;
	 int* fieldIndex = 0;
	 void* valueSecond = NULL;
	 FieldType fieldType = 0;
	 int i = 0, j = 0;
	 int intFieldValue = 0;
	 int* intValue = NULL;
	 double realFieldValue = 0;
	 double* doubleValue = NULL;
	 char varCharFieldValue[255]={0};
	 char* varCharValue = NULL;
	 int varCharLen = 0;
	 varArrayList* primaryIndexArray = NULL;
	 varArrayList* keyOffsetArray = NULL;
	 varArrayList* keyTypeArray = NULL;
	 varArrayList* keyVarcharLengthArray = NULL;
	 int offset = 0;
	 int keyVarcharIndex=0;
 
 
	 /* 2.判断参数是否为空 */
	 if (btreePage == NULL || keyValueArray == NULL || tableSchema == NULL || tableName == NULL || catalog == NULL) {
		 return NULL;
	 }
 
	 /* 3.获取primaryIndexArray */
 
	 LOG(LOG_TRACE, "SLOCKing:%s", "tablePrimaryKeyIndexMap");
	 WriteLock(&(catalog->keyIndexLatch));
	 LOG(LOG_TRACE, "SLOCKend:%s", "tablePrimaryKeyIndexMap");
	 primaryIndexArray = hashMapGet(catalog->tablePrimaryKeyIndexMap, tableName);
	 LOG(LOG_TRACE, "SUNLOCKing:%s", "tablePrimaryKeyIndexMap");
	 WriteUnLock(&(catalog->keyIndexLatch));
	 LOG(LOG_TRACE, "SUNLOCKend:%s", "tablePrimaryKeyIndexMap");
	 if (primaryIndexArray == NULL) {
		 return NULL;
	 }
	 keyOffsetArray = getPrimaryOffsetArray(catalog,tableName);
	 if (primaryIndexArray == NULL) {
		 return NULL;
	 }
	 keyTypeArray = getPrimaryTypeArray(catalog,tableName);
	 if (primaryIndexArray == NULL) {
		 return NULL;
	 }
	 keyVarcharLengthArray = getPrimaryVarcharLenArray(catalog,tableName);
	 if (primaryIndexArray == NULL) {
		 return NULL;
	 }
 
	 /* 4.依次比较主键，找到满足条件的tuple */
	 for (i = 0; i < btreePage->entryNum; i++) {
		 flag = 1;  /* flag初始化 */
		 record = btreePage->page.pData + PAGE_HEAD_SIZE + i * btreeTable->leafRecordLength;
		 if (record == NULL) {
			 return NULL;
		 }
 
		 keyValueNum = keyValueArray->elementCount;
		 keyVarcharIndex=0;
		 for (j = 0; j < keyValueNum; j++) {
			 fieldIndex = (int*)varArrayListGet(primaryIndexArray, j);
			 offset = *(int*)varArrayListGet(keyOffsetArray,j);
			 if(fieldIndex == NULL) {
				 return NULL;
			 }
 
			 valueSecond = varArrayListGetPointer(keyValueArray,j);
			 if (valueSecond == NULL) {
				 return NULL;
			 }
 
			 fieldType = *(FieldType*)varArrayListGet(keyTypeArray,j);


			  /* 对keyValue值进行比较，找到相等的 */
			 switch (fieldType)
			 {
				case FIELDTYPE_INTEGER: {
					memcpy(&intFieldValue,record + offset,INT_SIZE);
					intValue = (int*)valueSecond;
					result = compareIntValue(&intFieldValue, intValue);
					break;
				}
				case FIELDTYPE_REAL: {
					memcpy(&realFieldValue,record + offset,DOUBLE_SIZE);
					doubleValue = (double*)valueSecond;
					result = compareRealValue(&realFieldValue, doubleValue);
					break;
				}
				case FIELDTYPE_VARCHAR: {
					varCharLen = *(int*)varArrayListGet(keyVarcharLengthArray,keyVarcharIndex);
					keyVarcharIndex++;
					memcpy(varCharFieldValue,record + offset,varCharLen);
					varCharValue = (char*)valueSecond;
					result = compareVarcharValue(varCharFieldValue, varCharValue);
					break;
				}
				default:
					break;
			 }
 
			 /* 4.根据判断符号predicate设置flag */
			if (predicate == EQUAL) {
				if (result != 0) {
					flag = 0;
				}
			}
			else if (predicate == GREATER_THAN) {
				if(result > 0 )
					break;
				else if(result < 0)
					flag = 0;
				else{
					/*最后一个主键还是等于就返回false,前面的主键等于就继续对比后面的主键*/
					if(j == keyValueNum - 1)
						flag = 0;
				}
			}
			else if (predicate == LESS_THAN) {
				if(result < 0 )
					break;
				else if(result > 0)
					flag = 0;
				else{
					/*最后一个主键还是等于就返回false,前面的主键等于就继续对比后面的主键*/
					if(j == keyValueNum - 1)
						flag = 0;
				}
			}
			else if (predicate == GREATER_THAN_OR_EQUAL) {
				if(result > 0 )
					break;
				else if(result < 0)
					flag = 0;
				else
					continue;
			}
			else if (predicate == LESS_THAN_OR_EQUAL) {
				if(result < 0 )
					break;
				else if(result > 0)
					flag = 0;
				else
					continue;
			}
			if (flag == 0) {
				break;
			}
		 }
		 /* 找到了与keyValueArray值符合判断条件的tuple */
		 if (flag == 1) {
			 return record;
		 }
	 }
 
	 return NULL;
 }
 
 /**
  * @brief 在leafPage中根据主键值删除tuple
  * @param btreePage btreePage指针
  * @param keyValueArray 主键值
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @param tid  事务id
  * @return 返回状态码
  */
 int leafPageDeleteTuple(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName,struct Catalog* catalog,BtreeTable* btreeTable, int sourceIndex) {
	/* 1.变量的定义 */
	// Tuple* tuple = NULL;
	BYTE* record = NULL;
	int rc = 0;
	int index = 0;

	/* 2.判断参数是否为空 */
	if (btreePage == NULL || keyValueArray == NULL || tableSchema == NULL) {
		return GNCDB_PARAMNULL;
	}
	
	/* 3.找到与主键值相等的tuple */
	if (sourceIndex != -1) {
		index = sourceIndex;
	} else {
		index = leafPageFindEntryIndexByKeyvalue(btreePage, keyValueArray, tableSchema, tableName,catalog,EQUAL,btreeTable);
		if (index == -1) {
			return GNCDB_NOT_FOUND;
		}
	}
	 
	/* 4.需要对tuple中的Blob内容单独进行删除操作 */
	/* todo? 大对象数据的删除处理 */
	record = btreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->leafRecordLength;

	if(btreeTable->hasBlob){
		rc = deleteBlobInTuple(record, tableSchema);
		if (rc != GNCDB_SUCCESS) {
			return rc;
		}
	}

	/* 5.删除对应的tuple(考虑能不能通过index删除)，释放其内存 */
	/* 释放tuple的内存，需要释放内存 */
	memmove(btreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->leafRecordLength, btreePage->page.pData + PAGE_HEAD_SIZE + (index + 1) * btreeTable->leafRecordLength, (btreePage -> entryNum - index - 1) * btreeTable->leafRecordLength);
	/* tuple总数减1 */
	btreePage->entryNum -= 1;

	return rc;
 }
 
 /***************************************************************************************************internalPage****************************************************/
 
 /// <summary>
 /// 在父亲页中寻找childPageId所在的下标
 /// </summary>
 /// <param name="btreePage"></param>
 /// <param name="childPageId"></param>
 /// <returns></returns>
 //暂未用到，暂未修改，为通过编译注释
 // int getIndexOfChildPage(struct BtreePage* btreePage, int childPageId) {
 // 	/* 1.变量的定义 */
 // 	int index = 0;
 // 	InternalEntry* internalEntry = NULL;
 
 // 	/* 2.判断参数是否为空 */
 // 	if (btreePage == NULL) {
 // 		return GNCDB_PARAMNULL;
 // 	}
 
 // 	/* 3.寻找childPage在父亲页btreePage中的下标 */
 // 	for (index = 0; index < btreePage->entryNum; index++) {
 // 		internalEntry = varArrayListGetPointer(btreePage->entryArray, index);
 // 		if (internalEntry == NULL) {
 // 			return GNCDB_NOT_FOUND;
 // 		}
 
 // 		if (internalEntry->childPageId == childPageId) {
 // 			return index;
 // 		}
 // 	}
 
 // 	return GNCDB_NOT_FOUND;
 // }
 /**
  * @brief 在internalPage中插入record
  * @param btreePage btreePage指针
  * @param internalRecord 待插入record
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @return 成功返回GNCDB_SUCCESS，否则返回错误码
  */
 int internalPageInsertRecord(struct BtreePage* btreePage, BYTE* internalRecord, TableSchema* tableSchema, BtreeTable* btreeTable,Catalog* catalog) {
	 /* 1.变量定义 */
	 // int rc = 0;
	 int offset = PAGE_HEAD_SIZE;
	 BYTE* curRecord = NULL;
	 BYTE* toInsertRecord = NULL;
	 varArrayList* primaryTypeArray = NULL;
	 varArrayList* primaryKeyVarcharLenth = NULL;
	 int i = 0;
 
	 /* 2.检查输入参数是否为NULL */
	 if (btreePage == NULL || internalRecord == NULL  || tableSchema == NULL || btreeTable == NULL) {
		 return GNCDB_PARAMNULL;
	 }
	 primaryTypeArray = getPrimaryTypeArray(catalog,btreeTable->tableName);
	 if (primaryTypeArray == NULL) {
		 return GNCDB_PARAMNULL;
	 }
	 primaryKeyVarcharLenth = getPrimaryVarcharLenArray(catalog,btreeTable->tableName);
	 if (primaryKeyVarcharLenth == NULL) {
		 return GNCDB_PARAMNULL;
	 }
	 curRecord = btreePage->page.pData + offset;
	 //遍历叶子页的entryArray，找到插入位置
	 for(i = 0; i < btreePage -> entryNum; i++){
		 if(internalRecordCompareFun(primaryTypeArray,primaryKeyVarcharLenth, curRecord,internalRecord) < 0){
			 offset += btreeTable->internalRecordLength;
			 curRecord = btreePage->page.pData + offset;
		 }
		 else{
			 break;
		 }
	 }
	 //找到插入位置
	 toInsertRecord = curRecord;
	 //将插入位置之后的记录后移
	 memmove(toInsertRecord + btreeTable->internalRecordLength, toInsertRecord, (btreePage -> entryNum - i) * btreeTable->internalRecordLength);
	 //将record插入到toInsertRecord
	 memcpy(toInsertRecord, internalRecord, btreeTable->internalRecordLength);

	 /* 4.更新叶子页中Tuple的总数 */
	 btreePage->entryNum += 1;
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * @brief 在internalPage中根据主键值查找对应的record索引号
  * @param btreePage btreePage指针
  * @param sourceRecord 主键值
  * @param predicate 比较符号，用于区分在内部页中找到与sourceRecord值相等还是比sourceRecord大还是小的第一个record
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @return 返回record的索引号，-1表示未找到
  */
 int internalPageFindRecordIndexByKeyvalue(struct BtreePage* btreePage, BYTE* sourceRecord, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog, Predicate predicate,BtreeTable *btreeTable){
 /* 1.变量的定义 */
	 int result = 0; // 0:两者相等，正数：比keyValueArray大，负数：比keyValueArray小
	 int flag = 1;   // 标志是否找到了与keyValueArray值相等的tuple（1:找到了，0：未找到）
	 BYTE* internalRecord = NULL;
	 BYTE* keyValueFirst = NULL;
	 BYTE* keyValueSecond = NULL;
	 int i = 0,j = 0;
	 FieldType* fieldType = 0;
	 varArrayList* primaryTypeArray = NULL;
	 varArrayList* primaryVarcharLenArray = NULL;
	 int offsetField = INT_SIZE;
	 int varcharIndex=0;
 
	 /* 2.判断参数是否为空 */
	 if (btreePage == NULL || sourceRecord == NULL || tableSchema == NULL || tableName == NULL ||catalog == NULL) {
		 return -1;
	 }
	 
	 /* 3.获取primaryTypeArray */
	 primaryTypeArray = getPrimaryTypeArray(catalog,tableName);
	 if (primaryTypeArray == NULL) {
		 return -1;
	 }
	 primaryVarcharLenArray = getPrimaryVarcharLenArray(catalog,tableName);
	 if (primaryVarcharLenArray == NULL) {
		 return -1;
	 }
 
	 /* 4.依次比较internalEntry */
	 for (i = 0; i < btreePage->entryNum; i++) {
		 flag = 1;
		 internalRecord = btreePage->page.pData + PAGE_HEAD_SIZE + i * btreeTable->internalRecordLength;
		 offsetField = INT_SIZE;
		 varcharIndex = 0;
 
		 /* 遍历internalEntry存放的主键值 */
		 for (j = 0; j < primaryTypeArray->elementCount; j++) {
			 fieldType = (FieldType*)varArrayListGet(primaryTypeArray, j);		/* 获取tuple主键的数据类型 */
			 if (fieldType == NULL) {
				 return -1;
			 }
			 keyValueFirst = internalRecord + offsetField;
			 keyValueSecond = sourceRecord + offsetField;
 
			 /* 对keyValue值进行比较，找到相等的 */
			 if (*fieldType == FIELDTYPE_INTEGER) {
				 result = compareIntValue(keyValueFirst, keyValueSecond);
				 offsetField += INT_SIZE;
			 }
			 else if (*fieldType == FIELDTYPE_REAL) {
				 result = compareRealValue(keyValueFirst, keyValueSecond);
				 offsetField += DOUBLE_SIZE;
			 }
			 else if (*fieldType == FIELDTYPE_VARCHAR) {
				 result = compareVarcharValue(keyValueFirst, keyValueSecond);
				 offsetField += *(int*)varArrayListGet(primaryVarcharLenArray,varcharIndex);
				 varcharIndex++;
			 }
 
			if (predicate == EQUAL) {
				if (result != 0) {
					flag = 0;
				}
			}
			else if (predicate == GREATER_THAN) {
				if(result > 0 )
					break;
				else if(result < 0)
					flag = 0;
				else{
					/*最后一个主键还是等于就返回false,前面的主键等于就继续对比后面的主键*/
					if(j == primaryTypeArray->elementCount - 1)
						flag = 0;
				}
			}
			else if (predicate == LESS_THAN) {
				if(result < 0 )
					break;
				else if(result > 0)
					flag = 0;
				else{
					/*最后一个主键还是等于就返回false,前面的主键等于就继续对比后面的主键*/
					if(j == primaryTypeArray->elementCount - 1)
						flag = 0;
				}
			}
			else if (predicate == GREATER_THAN_OR_EQUAL) {
				if(result > 0 )
					break;
				else if(result < 0)
					flag = 0;
				else
					continue;
			}
			else if (predicate == LESS_THAN_OR_EQUAL) {
				if(result < 0 )
					break;
				else if(result > 0)
					flag = 0;
				else
					continue;
			}
			if (flag == 0) {
				break;
			}
		 }
 
		 /* 找到了与keyValueArray值符合判断条件的internalEntry */
		 if (flag == 1) {
			 return i;
		 }
	 }
 
	 return -1;
 }
 
 /**
  * @brief 在internalPage中根据主键值查找对应的record
  * @param btreePage btreePage指针
  * @param sourceRecord 主键值
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @param predicate 比较符号，用于区分在内部页中找到与sourceRecord值相等还是比sourceRecord大还是小的第一个record
  * @return internalEntry指针
  */
 BYTE* internalPageFindRecordByKeyvalue(struct BtreePage* btreePage, BYTE* sourceRecord, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog,BtreeTable* btreeTable, Predicate predicate){
	 /* 1.变量的定义 */
	 int result = 0; // 0:两者相等，正数：比keyValueArray大，负数：比keyValueArray小
	 int flag = 1;   // 标志是否找到了与keyValueArray值相等的tuple（1:找到了，0：未找到）
	 BYTE* internalRecord = NULL;
	 BYTE* keyValueFirst = NULL;
	 BYTE* keyValueSecond = NULL;
	 int i = 0,j = 0;
	 FieldType* fieldType = 0;
	 varArrayList* primaryTypeArray = NULL;
	 varArrayList* primaryVarcharLenArray = NULL;
	 int varcharIndex=0;
	 int offset = INT_SIZE;
 
	 /* 2.判断参数是否为空 */
	 if (btreePage == NULL || sourceRecord == NULL || tableSchema == NULL || tableName == NULL ||catalog == NULL) {
		 return NULL;
	 }
	 
	 /* 3.获取primaryTypeArray */
	 primaryTypeArray = getPrimaryTypeArray(catalog,tableName);
	 if (primaryTypeArray == NULL) {
		 return NULL;
	 }
	 primaryVarcharLenArray = getPrimaryVarcharLenArray(catalog,tableName);
	 if (primaryVarcharLenArray == NULL) {
		 return NULL;
	 }
 
	 /* 4.依次比较internalEntry */
	 for (i = 0; i < btreePage->entryNum; i++) {
		flag = 1;
				 offset = INT_SIZE;
		 internalRecord = btreePage->page.pData + PAGE_HEAD_SIZE + i * btreeTable->internalRecordLength;
 
		 varcharIndex = 0;
		 /* 遍历internalEntry存放的主键值 */
		 for (j = 0; j < primaryTypeArray->elementCount; j++) {
			 fieldType = (FieldType*)varArrayListGet(primaryTypeArray, j);		/* 获取tuple主键的数据类型 */
			 if (fieldType == NULL) {
				 return NULL;
			 }
			 keyValueFirst = internalRecord + offset;
			 keyValueSecond = sourceRecord + offset;
 
			 if (*fieldType == FIELDTYPE_INTEGER) {
				 offset +=INT_SIZE;
				 result = compareIntValue(keyValueFirst, keyValueSecond);
			 }
			 else if (*fieldType == FIELDTYPE_REAL) {
				 offset += DOUBLE_SIZE;
				 result = compareRealValue(keyValueFirst, keyValueSecond);
			}
			 else if (*fieldType == FIELDTYPE_VARCHAR) {
				 offset += *(int*)varArrayListGet(primaryVarcharLenArray,varcharIndex);
				 varcharIndex++;
				 result = compareVarcharValue(keyValueFirst, keyValueSecond);
			}

			/* 4.根据判断符号predicate设置flag */
			if (predicate == EQUAL) {
				if (result != 0) {
					flag = 0;
				}
			}
			else if (predicate == GREATER_THAN) {
				if(result > 0 )
					break;
				else if(result < 0)
					flag = 0;
				else{
					/*最后一个主键还是等于就返回false,前面的主键等于就继续对比后面的主键*/
					if(j == primaryTypeArray->elementCount - 1)
						flag = 0;
				}
			}
			else if (predicate == LESS_THAN) {
				if(result < 0 )
					break;
				else if(result > 0)
					flag = 0;
				else{
					/*最后一个主键还是等于就返回false,前面的主键等于就继续对比后面的主键*/
					if(j == primaryTypeArray->elementCount - 1)
						flag = 0;
				}
			}
			else if (predicate == GREATER_THAN_OR_EQUAL) {
				if(result > 0 )
					break;
				else if(result < 0)
					flag = 0;
				else
					continue;
			}
			else if (predicate == LESS_THAN_OR_EQUAL) {
				if(result < 0 )
					break;
				else if(result > 0)
					flag = 0;
				else
					continue;
			}
			if (flag == 0) {
				break;
			}
		 }
 
		 /* 找到了与keyValueArray值符合判断条件predicate的internalEntry */
		 if (flag == 1) {
			 return internalRecord;
		 }
	 }
 
	 return NULL;
 }
 
 /**
  * @brief 在internalPage中根据主键值查找对应的tuple
  * @param btreePage btreePage指针
  * @param keyValueArray 主键值
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @param predicate 比较符号，用于区分在内部页中找到与keyValueArray值相等还是比keyValueArray大还是小的第一个entry
  * @return internalEntry指针
  */
 BYTE* internalPageFindEntryByKeyvalue(BtreePage* btreePage, varArrayList* keyValueArray,struct TableSchema* tableSchema,
	 char* tableName,struct Catalog* catalog,BtreeTable* btreeTable, Predicate predicate,int *index) {
	 /* 1.变量的定义 */
	 int left = 0, right = 0, mid = 0;
	 int cmpResult = 0;
	 int primaryKeyIndex = 0; // 主键索引
	 BYTE* internalRecord = NULL;
	 void* keyValueFirst = NULL;
	 void* keyValueSecond = NULL;
	 int j = 0;
	 FieldType* fieldType = 0;
	 varArrayList* primaryTypeArray = NULL;
	 varArrayList* primaryIndexArray = NULL;
	 Column* col;
	 int offset = INT_SIZE;
	 int result = 0;
	 /* 2.判断参数是否为空 */
	 if (btreePage == NULL || keyValueArray == NULL || tableSchema == NULL || tableName == NULL ||catalog == NULL) {
		 return NULL;
	 }
	 
	 /* 3.获取primaryTypeArray */
	 primaryTypeArray = getPrimaryTypeArray(catalog,tableName);
	 if (primaryTypeArray == NULL) {
		 return NULL;
	 }
	 primaryIndexArray = getPrimaryIndexArray(catalog,tableName);
	 if (primaryIndexArray == NULL) {
		 return NULL;
	 }

	 /* 4.使用二分查找定位目标记录 */
	 left = 0;
	 right = btreePage->entryNum - 1;
	 
	 while (left <= right) {
		 mid = left + (right - left) / 2;
		 offset = INT_SIZE;
		 internalRecord = btreePage->page.pData + PAGE_HEAD_SIZE + mid * btreeTable->internalRecordLength;
		 cmpResult = 0;
		 
		 /* 遍历internalEntry存放的主键值进行比较 */
		 for (j = 0; j < primaryTypeArray->elementCount; j++) { 
			 fieldType = (FieldType*)varArrayListGet(primaryTypeArray, j);
			 if (fieldType == NULL) {
				 return NULL;
			 }
			 primaryKeyIndex = *(int*)varArrayListGet(primaryIndexArray,j);
			 col = (Column*)varArrayListGetPointer(tableSchema->columnList,primaryKeyIndex);
			 keyValueFirst = internalRecord + offset;

			 keyValueSecond = varArrayListGetPointer(keyValueArray, j);
			 if (keyValueSecond == NULL) {
				 return NULL;
			 }

			 /* 对keyValue值进行比较 */
			 if (*fieldType == FIELDTYPE_INTEGER) {
				 offset += INT_SIZE;
				 result = compareIntValue(keyValueFirst, keyValueSecond);
			 }
			 else if (*fieldType == FIELDTYPE_REAL) {
				 offset += DOUBLE_SIZE;
				 result = compareRealValue(keyValueFirst, keyValueSecond);
			 }
			 else if (*fieldType == FIELDTYPE_VARCHAR) {
				 offset += col->columnConstraint->maxValue;
				 result = compareVarcharValue(keyValueFirst, keyValueSecond);
			 }
			 
			 /* 如果当前字段不相等，则整体比较结果确定 */
			 if (result != 0) {
				 cmpResult = result;
				 break;
			 }
		 }
		 
		 /* 根据比较结果调整搜索范围 */
		 if (cmpResult == 0) {
			 /* 找到相等的记录，根据predicate决定是否继续查找 */
			 if (predicate == EQUAL || predicate == GREATER_THAN_OR_EQUAL || predicate == LESS_THAN_OR_EQUAL) {
				 if(index != NULL) {
					 *index = mid;
				 }
				 return internalRecord;
			 }
			 else if (predicate == GREATER_THAN) {
				 /* 需要找第一个大于的记录 */
				 left = mid + 1;
			 }
			 else if (predicate == LESS_THAN) {
				 /* 需要找第一个小于的记录 */
				 right = mid - 1;
			 }
		 }
		 else if (cmpResult < 0) {
			 /* 当前记录小于目标值，在右半部分查找 */
			 left = mid + 1;
		 }
		 else {
			 /* 当前记录大于目标值，在左半部分查找 */
			 right = mid - 1;
		 }
	 }
	 
	 /* 5.处理未找到精确匹配的情况 */
	 if (predicate == GREATER_THAN || predicate == GREATER_THAN_OR_EQUAL) {
		 /* 返回第一个大于等于目标值的记录 */
		 if (left < btreePage->entryNum) {
			 if(index != NULL) {
				 *index = left;
			 }
			 return btreePage->page.pData + PAGE_HEAD_SIZE + left * btreeTable->internalRecordLength;
		 }
	 }
	 else if (predicate == LESS_THAN || predicate == LESS_THAN_OR_EQUAL) {
		 /* 返回第一个小于等于目标值的记录 */
		 if (right >= 0) {
			 if(index != NULL) {
				 *index = right;
			 }
			 return btreePage->page.pData + PAGE_HEAD_SIZE + right * btreeTable->internalRecordLength;
		 }
	 }

	 return NULL;
 }
 
 /**
  * @brief 在internalPage中根据主键值删除internalEntry
  * @param btreePage btreePage指针
  * @param keyValueArray 主键值
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @return 返回状态码
  */
 int internalPageDeleteRecord(struct BtreePage* btreePage, BYTE* internalRecord, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog,BtreeTable* btreeTable) {
	 /* 1.变量的定义 */
	 int rc = 0;
	 int index = 0;
 
	 /* 2.判断参数是否为空 */
	 if (btreePage == NULL || internalRecord == NULL || tableSchema == NULL || catalog == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 3.找到与主键值相等的internalEntry */
	 index = internalPageFindRecordIndexByKeyvalue(btreePage, internalRecord, tableSchema, tableName,catalog, EQUAL,btreeTable);
 
	 /* 4.删除对应的tuple(考虑能不能通过index删除) */
	 /* 释放internalEntry的空间 */
	 memmove(btreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->internalRecordLength, btreePage->page.pData + PAGE_HEAD_SIZE + (index + 1) * btreeTable->internalRecordLength, (btreePage -> entryNum - index - 1) * btreeTable->internalRecordLength);
	 /* tuple总数减1 */
	 btreePage->entryNum -= 1;
 
	 return rc;
 }
 
 /**
  * @brief 在internalPage中根据主键值更新internalEntry
  * @param btreePage btreePage指针
  * @param keyValueArray 主键值
  * @param tableSchema
  * @param tableName
  * @param catalog
  * @return 返回状态码
  */
 int internalPageUpdateRecord(struct BtreePage* btreePage, BYTE* oldInternalRecord, BYTE* newInternalRecord,struct  TableSchema* tableSchema,struct  BtreeTable* btreeTable,struct Catalog* catalog){
	/* 1.变量的定义 */
	int rc = 0;
	int index = 0;

	/* 2.判断参数是否为空 */
	if (btreePage == NULL || oldInternalRecord == NULL  || newInternalRecord == NULL || tableSchema == NULL || catalog == NULL) {
		return GNCDB_PARAMNULL;
	}

	/* 3.找到与主键值相等的internalEntry */
	index = internalPageFindRecordIndexByKeyvalue(btreePage, oldInternalRecord, tableSchema, btreeTable->tableName,catalog, EQUAL,btreeTable);

	/* 4.更新对应的record*/
	memcpy(btreePage->page.pData + PAGE_HEAD_SIZE + index * btreeTable->internalRecordLength, newInternalRecord, btreeTable->internalRecordLength);
	return rc;
 }
 
 /****************************************************************************************************overflowPage**************************************************************************************/
 
 /**
  * 在溢出页的头部填充信息，包括页类型和下一个溢出页的页号
  * @param overflowPage 溢出页
  * @param buf 用于存储溢出页头部信息的缓冲区
  * @return 成功返回GNCDB_SUCCESS，否则返回错误码
  */
 int overflowPageFillInHeader(OverflowPage* overflowPage, BYTE* buf) {
	 /* 1.变量的定义 */
	 int rc = 0;
	 int len = 0;
 
	 /* 2.检查参数是否为空 */
	 if (overflowPage == NULL || buf == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 if(overflowPage->page.pageType != OVERFLOW_PAGE)
	 {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 3.写入页类型（0x02表示溢出页） */
	 rc = writeChar((char)0x02, buf, &len);
	 if (rc != GNCDB_SUCCESS) {
		 return rc;
	 }
 
	 /* 4.写入下一个溢出页的页号 */
	 writeInt(overflowPage->nextPageId, buf + 1, &len);
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * 读取溢出页的头部信息，包括页类型和下一个溢出页的页号
  * @param overflowPage 溢出页
  * @param buf 存放溢出页内容的缓冲区
  * @return 成功返回GNCDB_SUCCESS，否则返回错误码
  */
 int overflowPageReadHeader(OverflowPage* overflowPage) {
	 /* 1.变量的定义 */
	 int len = 0; /* 缓冲区读取的字节数 */
 
	 /* 2.检查参数是否为空 */
	 if (overflowPage == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 3.溢出页的页类型为OVERFLOW_PAGE（4） */
	 overflowPage->page.pageType = OVERFLOW_PAGE;
	 /* 读取下一个溢出页的页号 */
	 readInt(&overflowPage->nextPageId, overflowPage->page.pData+1, &len);
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * 创建一个溢出页，并初始化溢出页的页号、页头和溢出数据
  * @param pageData 存储溢出页内容的缓冲区
  * @param pageId 溢出页号
  * @return 成功返回OverflowPage指针，失败返回NULL
  */
 int overflowPageConstruct(OverflowPage* overflowPage, int pageId) {
	 /* 1.变量的定义 */
	 int rc = 0;
	 int size = 0;
	 int len = 0;
 
 
	 /* 2.初始化page */
	 pageInit(&overflowPage->page,pageId, OVERFLOW_PAGE,false);
 
	 /* 3.初始化溢出页的页号和页头信息 */
	 overflowPage->page.id = pageId;
	 rc = overflowPageReadHeader(overflowPage);
	 if (rc != GNCDB_SUCCESS) {
		 // overflowPageDestroy(&overflowPage);
		 return GNCDB_READ_FAILED;
	 }
 
	 /* 4.读取当前页存放的溢出数据字节数 */	
	 readInt(&size, overflowPage->page.pData + OVERFLOWPAGE_HEAD_SIZE, &len);
	 /* 分配溢出数据内存，将溢出数据复制到溢出页的overflowData中 */
	 overflowPage->overflowData = (BYTE*)my_malloc(size + INT_SIZE);
 
	 if (overflowPage->overflowData == NULL) {
		 overflowPageDestroy(&overflowPage);
		 return GNCDB_MEM;
	 }
	 memcpy(overflowPage->overflowData, overflowPage->page.pData + OVERFLOWPAGE_HEAD_SIZE, size + INT_SIZE);
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * 创建一个不包含溢出数据的溢出页，并初始化页号、页类型、下一个溢出页号和溢出数据指针
  * @param pageId 溢出页号
  * @param nextPageId 下一个溢出页号
  * @return 成功返回OverflowPage指针，失败返回NULL
  */
 int overflowPageConstructWithOutData(struct OverflowPage* overflowPage, int pageId, int nextPageId) {
	 /* 1.初始化page */
	 pageInit(&overflowPage->page,pageId, OVERFLOW_PAGE,false);
 
	 /* 4.初始化页号、页类型、下一个溢出页号和溢出数据指针 */
	 overflowPage->page.pageType = OVERFLOW_PAGE;
	 overflowPage->page.id = pageId;
	 overflowPage->nextPageId = nextPageId;
	 overflowPage->overflowData = NULL;
 
	 return GNCDB_SUCCESS;
 }
 
  /**
  * 在内存中用malloc分配一个溢出页，并初始化页号、页类型、下一个溢出页号和溢出数据指针
  * @param pageId 溢出页号
  * @param nextPageId 下一个溢出页号
  * @return 成功返回OverflowPage指针，失败返回NULL
  */
 struct OverflowPage* overflowPageMallocConstructWithOutData(int pageSize, int pageId, int nextPageId){
	 /* 1.变量的定义 */
	 OverflowPage* overflowPage = NULL;
 
	 /* 2.分配OverflowPage结构体内存 */
	 overflowPage = (OverflowPage*)my_malloc(sizeof(OverflowPage));
	 if (overflowPage == NULL) {
		 return NULL;
	 }
 
	 /* 3.初始化page */
	 pageInit(&overflowPage->page,pageId, OVERFLOW_PAGE,true);
 
	 overflowPage->page.pData = (BYTE*)my_malloc(pageSize);
	 if (overflowPage->page.pData == NULL) {
		 my_free(overflowPage);
		 return NULL;
	 }
 
	 /* 4.初始化页号、页类型、下一个溢出页号和溢出数据指针 */
	 overflowPage->page.pageType = OVERFLOW_PAGE;
	 overflowPage->page.id = pageId;
	 overflowPage->nextPageId = nextPageId;
	 overflowPage->overflowData = NULL;
 
	 return overflowPage;
 }
 
 /**
  * 深度复制一个溢出页，包括页号、页头和溢出数据
  * @param overflowPage 溢出页
  * @return 成功返回OverflowPage指针，失败返回NULL
  */
 struct OverflowPage* overflowPageDeepCopy(struct GNCDB* db, OverflowPage* overflowPage) {
	 /* 1.变量的定义 */
	 int len = 0;
	 int length = 0;
	 OverflowPage* overflowPageCopy = NULL;
 
	 /* 2.检查参数是否为空 */
	 if (overflowPage == NULL) {
		 return NULL;
	 }
 
	 /* 3.创建一个不包含溢出数据的溢出页，并初始化页号和下一个溢出页号 */
	 overflowPageCopy = overflowPageMallocConstructWithOutData((db->pageCurrentSize), overflowPage->page.id, overflowPage->nextPageId);
	 if (overflowPageCopy == NULL) {
		 return NULL;
	 }
 
	 /* 4.读取溢出数据的长度，并为溢出数据分配内存 */
	 readInt(&length, overflowPage->overflowData, &len);
	 length += INT_SIZE;
	 overflowPageCopy->overflowData = (BYTE*)my_malloc(length);
	 if (overflowPageCopy->overflowData == NULL) {
		 overflowPageDestroy(&overflowPageCopy);
		 return NULL;
	 }
	 /* 复制溢出数据到新分配的内存中 */
	 memcpy(overflowPageCopy->overflowData, overflowPage->overflowData, length);
 
	 return overflowPageCopy;
 }
 
 /**
  * 将溢出页转换为字节数组
  * @param overflowPage 溢出页
  * @return 成功返回字节数组指针，失败返回NULL
  */
 BYTE* overflowPageToByte(struct GNCDB* db, struct OverflowPage* overflowPage) {
	 /* 1.变量的定义 */
	 BYTE* pageData = NULL;
	 int rc = 0;
	 int offset = OVERFLOWPAGE_HEAD_SIZE; /* 获取溢出页溢出数据 */
	 int len = 0;
	 int size = 0;
 
	 /* 2.检查参数是否为空 */
	 if (overflowPage == NULL) {
		 return NULL;
	 }
 
	 /* 3.分配缓冲区用于存储溢出页 */

	 pageData = overflowPage->page.pData;
	 /* 将缓冲区清零 */
	 memset(pageData, 0x00, db->pageCurrentSize);
 
	 /* 4.填充溢出页的页头信息 */
	 rc = overflowPageFillInHeader(overflowPage, pageData);
	 if (rc != GNCDB_SUCCESS) {
		 return NULL;
	 }
 
	 /* 5.如果溢出页包含溢出数据，则将溢出数据写入到缓冲区中 */
	 if (overflowPage->overflowData != NULL) {
		 readInt(&size, overflowPage->overflowData, &len);
		 /* 将溢出数据的字节数写入页头后面的四个字节中 */
		 writeInt(size, pageData + offset, &len);
		 offset += INT_SIZE;
		 /* 将溢出数据写入缓冲区中 */
		 writeBlob(overflowPage->overflowData + INT_SIZE, pageData + offset, size, &len);
	 }
 
	 return pageData;
 }
 
 /**
  * @brief overflowPage的销毁
  * @param overflowPage  溢出页的指针
  */
 void overflowPageDestroy(OverflowPage** overflowPage) {
	 /* 1.判断参数是否为空 */
	 if (overflowPage == NULL || *overflowPage == NULL) {
		 return;
	 }
 
	 /* 2.释放内存空间 */
	 ReadWriteLockDestroy(&((*overflowPage)->page.rwlock_t));
	 my_free((*overflowPage)->overflowData);
	//  my_free(*overflowPage);
	 *overflowPage = NULL;
 }

 void overflowPageDestroyMalloc(OverflowPage** overflowPage) {
	/* 1.判断参数是否为空 */
	if (overflowPage == NULL || *overflowPage == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	ReadWriteLockDestroy(&((*overflowPage)->page.rwlock_t));
	my_free((*overflowPage)->overflowData);
	my_free((*overflowPage)->page.pData);
	my_free(*overflowPage);
	*overflowPage = NULL;
}

 
 /**********************************************************************************freePage****************************************************/
 
 /**
  * 将空闲页的页头信息填充到缓冲区中
  * @param freePage 空闲页
  * @param buf 缓冲区
  * @return 成功返回GNCDB_SUCCESS，失败返回错误码
  */
 int freePageFillInHeader(FreePage* freePage, BYTE* buf) {
	 /* 1.变量的定义 */
	 int len = 0;
	 int rc = 0;
 
	 /* 2.检查参数是否为空 */
	 if (freePage == NULL || buf == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 if(freePage->page.pageType != FREE_PAGE)
	 {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 3.将页类型写入到缓冲区中 */
	 rc = writeChar((char)0x03, buf, &len);
	 if (rc != GNCDB_SUCCESS) {
		 return rc;
	 }
	 /* 将下一个空闲页的页号写入到缓冲区中 */
	 writeInt(freePage->nextPageId, buf + 1, &len);
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * @brief 读取freePage的头部
  * @param freePage freePage指针
  * @param buf 存放freePage内容
  * @return 成功返回GNCDB_SUCCESS，失败返回错误码
  */
 int freePageReadHeader(FreePage* freePage) {
	 /* 1.变量的定义 */
	 int len = 0; /* 缓冲区读取的字节数 */
 
	 /* 2.检查参数是否为空 */
	 if (freePage == NULL) {
		 return GNCDB_PARAMNULL;
	 }
 
	 /* 3.读取下一个空闲页的页号 */
	 freePage->page.pageType = FREE_PAGE;
	 readInt(&freePage->nextPageId, freePage->page.pData + sizeof(char), &len);
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * @brief freePage的创建
  * @param pageData  freePage的内容
  * @param pageId  freePage页号
  * @return 成功返回freePage的指针，失败返回NULL
  */
 int freePageConstruct(FreePage* freePage, int pageId) {
	 /* 1.变量的定义 */
	 int rc = 0;
 
	 /* 2.初始化page */
	 pageInit(&freePage->page,pageId, FREE_PAGE,false);
 
	 /* 3.读取页头信息 */
	 rc = freePageReadHeader(freePage);
	 if (rc != GNCDB_SUCCESS) {
		return GNCDB_READ_FAILED;
	 }
 
	 return GNCDB_SUCCESS;
 }
 struct FreePage* freePageMallocConstructWithOutData(int pageSize, int pageId, int nextPageId){
	 /* 1.变量的定义 */
	 FreePage* freePage = NULL; 
 
	 /* 2.分配freePage内存 */
	 freePage = (FreePage*)my_malloc(sizeof(FreePage));
	 if (freePage == NULL) {
		 return NULL;
	 }
	 freePage->page.pData = (BYTE*)my_malloc(pageSize);
	 if (freePage->page.pData == NULL) {
		 my_free(freePage);
		 return NULL;
	 }
 
	 /* 3.初始化page */
	 pageInit(&freePage->page,pageId, FREE_PAGE,true);
 
	 /* 4.设置freePage页的数据 */
	 freePage->page.pageType = FREE_PAGE;
	 freePage->page.id = pageId;
	 freePage->nextPageId = nextPageId;
 
	 return freePage;
 }
 
 /**
  * @brief freePage的创建
  * @param pageId freePage页的id
  * @param nextPageId 下一个freePage页的id
  * @return 成功返回freePage的指针，失败返回NULL
  */
 int freePageConstructWithOutData(struct FreePage* freePage,int pageId,int nextPageId) {
	 /* 1.初始化page */
	 pageInit(&freePage->page,pageId, FREE_PAGE,false);
 
	 /* 2.设置freePage页的数据 */ 
	 freePage->page.pageType = FREE_PAGE;
	 freePage->page.id = pageId;
	 freePage->nextPageId = nextPageId;
 
	 return GNCDB_SUCCESS;
 }
 
 /**
  * @brief freePage的深拷贝
  * @param freePage  freePage的指针
  * @param db
  * @return 返回freePage字节流的指针
  */
 struct FreePage* freePageDeepCopy(struct GNCDB* db, int pageId,int nextPageId) {
	 /* 1.变量的定义 */
	 FreePage* freePageCopy = NULL;
 
	 /* 2.创建一个空闲页 */
	 freePageCopy = freePageMallocConstructWithOutData(db->pageCurrentSize, pageId,nextPageId);
	 if (freePageCopy == NULL) {
		 return NULL;
	 }
 
	 return freePageCopy;
 }
 
 /**
  * @brief freePage转化为字节流
  * @param freePage  freePage的指针
  * @return 返回freePage字节流的指针
  */
 BYTE* freePageToByte(struct GNCDB* db, struct FreePage* freePage) {
	 /* 1.变量的定义 */
	 BYTE* pageData = NULL;
	 int rc = 0;
 
	 /* 2.判断参数是否为空 */
	 if (freePage == NULL) {
		 return NULL;
	 }
 
	 /* 3.分配内存空间 */

	 pageData = freePage->page.pData;
	 memset(pageData, 0x00, db->pageCurrentSize);
 
	 /* 4.填充头部 */
	 rc = freePageFillInHeader(freePage,pageData);
	 if (rc != GNCDB_SUCCESS) {
		 return NULL;
	 }
	 
	 return pageData;
 }
 
 /**
  * @brief freePage的销毁
  * @param freePage  freePage的指针
  * @param tid 事务id
  */
 void freePageDestroy(FreePage** freePage) {
	 /* 1.判断参数是否为空 */
	 if (freePage == NULL || *freePage == NULL) {
		 return;
	 }
 
	 /* 2.释放内存空间 */
	 ReadWriteLockDestroy(&((*freePage)->page.rwlock_t));
	 *freePage = NULL;
 }

 void freePageDestroyMalloc(FreePage** freePage) {
	/* 1.判断参数是否为空 */
	if (freePage == NULL || *freePage == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	ReadWriteLockDestroy(&((*freePage)->page.rwlock_t));
	my_free((*freePage)->page.pData);
	my_free(*freePage);
	*freePage = NULL;
}

 
 /**************************************测试debug工具函数*********************************************/
 
 int printfLeftPageTuplePKey(BtreePage* page)
 {
	 // int i = 0;
	 // Tuple* tuple = NULL;
	 // Field* field = NULL;
	 // VarCharField * varCharField = NULL;
	 // printf("table %s PageId : %d - ", page->tableName, page->page.id);
	 // for(; i < page->entryArray->elementCount; ++i)
	 // {
	 //     tuple = varArrayListGetPointer(page->entryArray, i);
	 //     field = varArrayListGetPointer(tuple->fieldArray, 0);
	 //     varCharField = (VarCharField* )field;
	 //     printf("%s--", varCharField->value);
	 //     fflush(stdout);
	 // }
	 // printf("\n");
	 return 0;

 }