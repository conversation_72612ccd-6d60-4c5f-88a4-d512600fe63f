/**
 * @file tuple.c
 * <AUTHOR>
 * @brief  InternalEntry和Tuple的构建和相关操作的实现
 * @version 0.1
 * @date 2023-02-02
 *
 * @copyright Copyright (c) 2023
 *
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "gncdbconstant.h"
#include "tuple.h"
#include "expression.h"
#include "typedefine.h"
#include "value.h"
#include "vararraylist.h"

// 预计算8个位掩码(一个byte)，用于快速查找每个位的掩码值
static const BYTE bitMask[8] = {
	0x01, 0x02, 0x04, 0x08, 0x10,
	0x20, 0x40, 0x80
};
/******************************************************************************LeafTuple****************************************************************************/
/**
 * @brief	记录tuple下标为fieldIndex的字段是否为空
 * @param tuple
 * @param fieldIndex 字段在tuple中的下标
 * @param flag  1：不为空，0：为空
 */
// int leafTupleSetBitMap(struct Tuple* tuple,int fieldIndex,int flag) {
int leafTupleSetBitMap(BYTE *record, int fieldIndex, int flag, int colNum)
{
  /* 1.变量声明 */
  int   index  = 0;
  BYTE *bitMap = record;
  /* 2.判断参数是否为空 */
  // if (tuple == NULL) {
  // 	return GNCDB_PARAMNULL;
  // }

  // bitMap = my_malloc(GET_BITMAP_LENGTH(colNum));
  // memcpy(bitMap, record, GET_BITMAP_LENGTH(colNum));
  /* 3.field是否为空的标记应该在bitMap的第index字节中 */
  /* bitMap字节元组中，bitMap[0]存元组前8个字段是否为空的标记，bitMap[0]的最低位标记元组的第一个字段，以此类推 */
  index = fieldIndex / 8;
  if (flag == 1) { /* tuple下标为fieldIndex的字段不为空 */
    bitMap[index] |= 0x01 << (fieldIndex % 8);
  } else { /* tuple下标为fieldIndex的字段设为空 */
    bitMap[index] &= (0x01 << (fieldIndex % 8)) ^ 0xff;
  }
  // memcpy(record, bitMap, GET_BITMAP_LENGTH(colNum));
  // my_free(bitMap);
  return GNCDB_SUCCESS;
}

int leafRecordSetBitMap(BYTE *record, int fieldIndex, int flag)
{
  /* 1.变量声明 */
  int   index  = 0;
  BYTE *bitmap = record;

  /* 2.判断参数是否为空 */
  if (record == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.field是否为空的标记应该在bitMap的第index字节中 */
  /* bitMap字节元组中，bitMap[0]存元组前8个字段是否为空的标记，bitMap[0]的最低位标记元组的第一个字段，以此类推 */
  index = fieldIndex / 8;
  if (flag == 1) { /* tuple下标为fieldIndex的字段不为空 */
    // tuple->bitMap[index] |= 0x01 << (fieldIndex % 8);
    bitmap[index] |= 0x01 << (fieldIndex % 8);
  } else { /* tuple下标为fieldIndex的字段设为空 */
    // tuple->bitMap[index] &= (0x01 << (fieldIndex % 8)) ^ 0xff;
    bitmap[index] &= (0x01 << (fieldIndex % 8)) ^ 0xff;
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief	判断tuple中下标为fieldIndex的字段是否为空
 * @param tuple
 * @param fieldIndex 字段在tuple中的下标
 * @return isNULL  1：不为空，0：为空
*/
int leafTupleGetBitMap(BYTE *record, int fieldIndex, int colNum) {
	// return ((record) ? ((!!((record)[(fieldIndex) >> 3] & (1 << ((fieldIndex)&7))))) : GNCDB_PARAMNULL);
	int ret = GNCDB_SUCCESS;
	if(record){
		if(fieldIndex < 0 || fieldIndex >= colNum){
			ret = GNCDB_PARAMNULL;
		}
		else if(fieldIndex < 8){
			ret = (record[0] & bitMask[fieldIndex]) ? 1 : 0;
		}
		else{
			ret = ((!!((record)[(fieldIndex) >> 3] & (1 << ((fieldIndex)&7)))));
		}
	}
	else{
		ret = GNCDB_PARAMNULL;
	}
	return ret;
}

/**
 * @brief	leafTuple的创建
 * @param columnNum 元组的列数
 */
Tuple *leafTupleConstruct(int columnNum)
{
  /* 1.变量声明 */
  Tuple *tuple = NULL;
  int    num   = 0;

  /* 2.分配Tuple的内存空间 */
  tuple = (Tuple *)my_malloc(sizeof(Tuple));
  if (tuple == NULL) {
    return NULL;
  }

  /* 3.创建fieldArray */
  tuple->fieldArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, fieldDestroy);
  if (tuple->fieldArray == NULL) {
    my_free(tuple);
    return NULL;
  }

  /* 4.计算bitMap需要分配的字节数，并分配内存空间、初始化 */
  num           = GET_BITMAP_LENGTH(columnNum);
  tuple->bitMap = (BYTE *)my_malloc(num);
  if (tuple->bitMap == NULL) {
    varArrayListDestroy(&tuple->fieldArray);
    my_free(tuple);
    return NULL;
  }
  memset(tuple->bitMap, 0x00, num);

  return tuple;
}

/**
 * @brief	leafTuple添加字段
 * @param tuple 元组的指针
 * @param field 字段
 */
// 不再使用该函数
int leafTupleAddField(Tuple *tuple, Field *field)
{
  /* 1.变量的定义 */
  int rc = 0;

  /* 2.判断参数是否为空，这里的field可以为空*/
  if (tuple == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.将field添加到fieldArray中 */
  rc = varArrayListAddPointer(tuple->fieldArray, field);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  /* 4.设置bitMap*/
  if (field != NULL) {
    // rc = leafTupleSetBitMap(tuple,tuple->fieldArray->elementCount - 1,1);
    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}

inline int leafRecordAddIntField(BYTE *record, int *offset, int intValue, int fieldIndex)
{
  memcpy(record + *offset, &intValue, INT_SIZE);
  *offset += INT_SIZE;

  return GNCDB_SUCCESS;
}
inline int leafRecordAddRealField(BYTE *record, int *offset, double *doubleValue, int fieldIndex)
{
  memcpy(record + *offset, doubleValue, DOUBLE_SIZE);
  *offset += DOUBLE_SIZE;

  return GNCDB_SUCCESS;
}

inline int leafRecordAddVarCharField(BYTE *record, int *offset, char *stringValue, int len, int fieldIndex)
{
  int strLen = strlen(stringValue);
  memcpy(record + *offset, stringValue, strLen);
  if (strLen < len) {
    memset(record + *offset + strLen, '\0', len - strLen);
  }
  *offset += len;
  return GNCDB_SUCCESS;
}
inline int leafRecordAddBlobField(BYTE *record, int *offset, int fieldIndex)
{
  memset(record + *offset, 0, 2 * INT_SIZE);
  *offset += 2 * INT_SIZE;

  return GNCDB_SUCCESS;
}

void leafRecordGetIntField(BYTE *record, int *offset, int *intValue)
{
  memcpy(intValue, record + *offset, INT_SIZE);
  *offset += INT_SIZE;
}
void leafRecordGetRealField(BYTE *record, int *offset, double *doubleValue)
{
  memcpy(doubleValue, record + *offset, DOUBLE_SIZE);
  *offset += DOUBLE_SIZE;
}
void leafRecordGetVarCharField(BYTE *record, int *offset, char *stringValue, int len)
{
  memcpy(stringValue, record + *offset, len);
  *offset += len;
}

int leafRecordCompareFun(BYTE* recordFirst, BYTE* recordSecond, struct TableSchema* tableSchema){
	/* 1.声明变量 */
	int result = 0;
	int index = 0;
	Column *column = NULL;
	int intFieldFirst;
	int intFieldSecond;
	double floatFieldFirst;
	double floatFieldSecond;
	// char* stringFieldFirst = NULL;
	// char* stringFieldSecond = NULL;
	int offsetFirst = GET_BITMAP_LENGTH(tableSchema->columnNum);
	int offsetSecond = GET_BITMAP_LENGTH(tableSchema->columnNum);

  /* 2.参数检查 */
  if (recordFirst == NULL || recordSecond == NULL || tableSchema == NULL) {
    return GNCDB_PARAMNULL;
  }
  for (index = 0; index < tableSchema->columnNum; index++) {
    /* 3.1.获取第index列的元信息 */
    column = (Column *)varArrayListGetPointer(tableSchema->columnList, index);
    if (column == NULL) {
      return GNCDB_ARRAY_GETPOINTER_FALSE;
    }

    /*3.2.判断第index列是否为主键，不为主键直接跳过 */
    if (column->columnConstraint->isPrimaryKey) {
      /* 3.2.2.根据字段的类型来获取对应的值 */
      switch (column->fieldType) {
        case FIELDTYPE_INTEGER: {
          leafRecordGetIntField(recordFirst, &offsetFirst, &intFieldFirst);
          leafRecordGetIntField(recordSecond, &offsetSecond, &intFieldSecond);
          result = intFieldFirst > intFieldSecond ? 1 : (intFieldFirst < intFieldSecond ? -1 : 0);
          break;
        }
        case FIELDTYPE_REAL: {
          leafRecordGetRealField(recordFirst, &offsetFirst, &floatFieldFirst);
          leafRecordGetRealField(recordSecond, &offsetSecond, &floatFieldSecond);
          result = floatFieldFirst > floatFieldSecond ? 1 : (floatFieldFirst < floatFieldSecond ? -1 : 0);
          break;
        }
        case FIELDTYPE_VARCHAR: {
          // leafRecordGetVarCharField(recordFirst, &offsetFirst, stringFieldFirst, column->columnConstraint->maxValue);
          // leafRecordGetVarCharField(recordSecond, &offsetSecond, stringFieldSecond,
          // column->columnConstraint->maxValue); result = strcmp(stringFieldFirst, stringFieldSecond);
          result = strcmp((char *)recordFirst + offsetFirst, (char *)recordSecond + offsetSecond);
          break;
        }
        default: break;
      }
      /* tupleFirst == tupleSecond :0 | tupleFirst > tupleSecond :1 | tupleFirst < tupleSecond -1 */
      if (result != 0) {
        break;
      }

    } else {
      switch (column->fieldType) {
        case FIELDTYPE_INTEGER: {
          offsetFirst += INT_SIZE;
          offsetSecond += INT_SIZE;
          break;
        }
        case FIELDTYPE_REAL: {
          offsetFirst += DOUBLE_SIZE;
          offsetSecond += DOUBLE_SIZE;
          break;
        }
        case FIELDTYPE_VARCHAR: {
          offsetFirst += column->columnConstraint->maxValue;
          offsetSecond += column->columnConstraint->maxValue;
          break;
        }
        default: break;
      }
    }
  }
  return result;
}

/**
 * @brief	获取leafTuple中的主键值 (浅拷贝keyValue)
 * @param keyValueArray 存放主键值（keyValueArray的内存分配在函数外面完成）
 * @param tuple 元组的指针
 * @param tableSchema
*/
int leafTupleGetKeyValue(varArrayList* keyValueArray, BYTE* record, struct Catalog* catalog, struct TableSchema* tableSchema, char* tableName) {
	/* 1.变量声明 */
	int rc = 0;
	Column* column = NULL;
	int index = 0;
	varArrayList* primaryKeyIndexArray = NULL;
	int primaryKeyCnt = 0;

	/* 2.判断参数是否为空 */
	if (keyValueArray == NULL || record == NULL || catalog == NULL || tableSchema == NULL) {
		return GNCDB_PARAMNULL;
	}

	primaryKeyIndexArray = getPrimaryIndexArray(catalog, tableName);
	if (primaryKeyIndexArray == NULL) {
		return GNCDB_ARRAY_GETPOINTER_FALSE;
	}

	primaryKeyCnt = primaryKeyIndexArray->elementCount;

	/* 3.遍历每一个字段，获取主键 */
	for(index = 0; index < primaryKeyCnt; index++){
		/* 3.1.获取第index列的元信息 */
		column = (Column*)varArrayListGetPointer(tableSchema->columnList, *(int*)varArrayListGet(primaryKeyIndexArray, index));
		if (column == NULL) {
			return GNCDB_ARRAY_GETPOINTER_FALSE;
		}
		switch (column->fieldType)
		{
			case FIELDTYPE_INTEGER: {
				rc = varArrayListAddPointer(keyValueArray, record + column->offset);
				break;
			}				
			case FIELDTYPE_REAL: {
				/* todo ? 是否可以使用real作为主键（mysql, sqlite都不建议使用，因为底层的硬件对于浮点数的精度支持程度不一样，可能会造成精度丢失，无法匹配，建议使用字符串） */
				rc = varArrayListAddPointer(keyValueArray, record + column->offset);
				break;
			}				
			case FIELDTYPE_VARCHAR: {
				rc = varArrayListAddPointer(keyValueArray, record + column->offset);
				break;
			}
			// case FIELDTYPE_DATE:{
			// 	dateField = (DateField*)field;
			// 	dateValue = &dateField->value;
			// 	rc = varArrayListAddPointer(keyValueArray, dateValue);
			// 	break;
			// }
			// case FIELDTYPE_DATETIME:{
			// 	datetimeField = (DateTimeField*)field;
			// 	datetimeStrValue = DateTimeFieldToString(datetimeField);
			// 	rc = varArrayListAddPointer(keyValueArray, datetimeStrValue);
			// 	break;
			// }
							
			default:
				break;
		}

		if (rc != GNCDB_SUCCESS) {
			return rc;
		}
	}

  return GNCDB_SUCCESS;
}

BYTE *leafTupleDeepCopy(BtreeTable *btreeTable, BYTE *record)
{
  int recordLength = btreeTable->leafRecordLength;

  BYTE *copyRecord = (BYTE *)my_malloc0(recordLength);
  if (copyRecord == NULL) {
    return NULL;
  }
  memcpy(copyRecord, record, recordLength);
  return copyRecord;
}

// /**
//  * @brief	tuple的深拷贝
//  * @param fieldArray 元组的字段内容
//  * @param tableSchema
//  * @return 返回深拷贝之后的tuple
// */
// Tuple* leafTupleDeepCopy(Tuple* tuple, struct TableSchema* tableSchema) {
// 	/* 1.变量声明 */
// 	int rc = 0;
// 	Tuple* copyTuple;
// 	int index;
// 	Field* field;
// 	Column* column;
// 	FieldType fieldType;
// 	IntField *intField, *copyIntField;
// 	RealField *realField, *copyRealField;
// 	VarCharField *varCharField, *copyVarCharField;
// 	BlobField *blobField, *copyBlobField;
// 	TextField* textField, *copyTextField;

// 	/* 2.判断参数是否为空 */
// 	if (fieldArray == NULL || tableSchema == NULL) {
// 		return NULL;
// 	}

// 	/* 3.构建一个拷贝的tuple */
// 	copyTuple = leafTupleConstruct(tableSchema->columnNum);
// 	if (copyTuple == NULL) {
// 		return NULL;
// 	}
// 	num = GET_BITMAP_LENGTH(tableSchema->columnNum);
// 	memcpy(copyTuple->bitMap, tuple->bitMap, num);

// 	/* 4.遍历tuple的每个字段，进行深拷贝 */
// 	for (index = 0; index < tableSchema->columnNum; index++){
// 		/* 4.1.获取第index的field */
// 		field = varArrayListGetPointer(fieldArray, index);
// 		// if (field == NULL) {
// 		// 	leafTupleDestroy(&copyTuple);
// 		// 	return NULL;
// 		// }

// 		/* 4.2.获取第index列的元信息 */
// 		column = (Column*)varArrayListGetPointer(tableSchema->columnList, index);
// 		if (column == NULL) {
// 			leafTupleDestroy(&copyTuple);
// 			return NULL;
// 		}

// 		/* 4.3.根据字段的类型，进入不同的分支进行深拷贝 */
// 		fieldType = column->fieldType;
// 		if (fieldType == FIELDTYPE_INTEGER) {
// 			if(field == NULL){
// 				if(column->columnConstraint->canBeNull == false){
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}
// 				rc = leafTupleAddField(copyTuple, NULL);
// 			}
// 			else{
// 				intField = (IntField*)field;
// 				copyIntField = intFieldConstruct(intField->value);
// 				if (copyIntField == NULL) {
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}

// 				rc = leafTupleAddField(copyTuple, (Field*)copyIntField);
// 			}
// 		}
// 		else if (fieldType == FIELDTYPE_REAL) {
// 			if(field == NULL){
// 				if(column->columnConstraint->canBeNull == false){
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}
// 				rc = leafTupleAddField(copyTuple, NULL);
// 			}
// 			else{
// 				realField = (RealField*)field;
// 				copyRealField = realFieldConstruct(realField->value);
// 				if (copyRealField == NULL) {
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}

// 				rc = leafTupleAddField(copyTuple, (Field*)copyRealField);
// 			}
// 		}
// 		else if (fieldType == FIELDTYPE_VARCHAR) {
// 			if(field == NULL){
// 				if(column->columnConstraint->canBeNull == false){
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}
// 				rc = leafTupleAddField(copyTuple, NULL);
// 			}
// 			else{
// 				varCharField = (VarCharField*)field;
// 				copyVarCharField = varCharFieldConstruct(varCharField->value);
// 				if (copyVarCharField == NULL) {
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}

// 				rc = leafTupleAddField(copyTuple, (Field*)copyVarCharField);
// 			}
// 		}
// 		else if (fieldType == FIELDTYPE_BLOB) {
// 			if(field == NULL){
// 				if(column->columnConstraint->canBeNull == false){
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}
// 				rc = leafTupleAddField(copyTuple, NULL);
// 			}
// 			else{
// 				blobField = (BlobField*)field;
// 				copyBlobField = blobFieldConstruct(blobField->overflowPageId,blobField->size);
// 				if (copyBlobField == NULL) {
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}

// 				rc = leafTupleAddField(copyTuple, (Field*)copyBlobField);
// 			}
// 		}
// 		else if(fieldType == FIELDTYPE_DATE){
// 			if(field == NULL){
// 				if(column->columnConstraint->canBeNull == false){
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}
// 				rc = leafTupleAddField(copyTuple, NULL);
// 			}
// 			else{
// 				DateField* dateField = (DateField*)field;
// 				DateField* copyDateField = dateFieldConstruct(dateField->value);
// 				if (copyDateField == NULL) {
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}

// 				rc = leafTupleAddField(copyTuple, (Field*)copyDateField);
// 			}
// 		}
// 		else if(fieldType == FIELDTYPE_DATETIME){
// 			if(field == NULL){
// 				if(column->columnConstraint->canBeNull == false){
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}
// 				rc = leafTupleAddField(copyTuple, NULL);
// 			}
// 			else{
// 				DateTimeField* datetimeField = (DateTimeField*)field;
// 				DateTimeField* copyDatetimeField = datetimeFieldConstruct(datetimeField->dateValue,
// datetimeField->timeValue); 				if (copyDatetimeField == NULL) { leafTupleDestroy(&copyTuple);
// return NULL;
// 				}

// 				rc = leafTupleAddField(copyTuple, (Field*)copyDatetimeField);
// 			}
// 		}
// 		else if(fieldType == FIELDTYPE_TEXT){
// 			if(field == NULL){
// 				if(column->columnConstraint->canBeNull == false){
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}
// 				rc = leafTupleAddField(copyTuple, NULL);
// 			}
// 			else{
// 				textField = (TextField*)field;
// 				copyTextField = textFieldConstruct(textField->overflowPageId, textField->size);
// 				if (copyTextField == NULL) {
// 					leafTupleDestroy(&copyTuple);
// 					return NULL;
// 				}

// 				rc = leafTupleAddField(copyTuple, (Field*)copyTextField);
// 			}
// 		}

// 		if (rc != GNCDB_SUCCESS) {
// 			leafTupleDestroy(&copyTuple);
// 			return NULL;
// 		}
// 	}

// 	return copyTuple;
// }

/**
 * @brief	leafTuple的销毁
 * @param tuple 元组的指针
 */
void leafTupleDestroy(void *data)
{
  Tuple **tuple = (Tuple **)data;
  /* 1.判断参数是否为空 */
  if (tuple == NULL || *tuple == NULL) {
    return;
  }

  /* 2.释放内存空间 */
  my_free((*tuple)->bitMap);
  varArrayListDestroy(&(*tuple)->fieldArray);
  my_free(*tuple);
  *tuple = NULL;
}

/**
 * @brief 叶子节点中元组大小的比较
 * @param tupleFirst 第一个元组
 * @param tupleSecond 第二个元组
 * @param primaryIndexArray 元组中主键的index
 * @return 返回大小比较后的结果 tupleFirst > tupleSecond ：1|tupleFirst < tupleSecond ：-1|tupleFirst = tupleSecond ：0
 */
int leafTupleCompareFun(varArrayList *array, void *dataFirst, void *dataSecond)
{
  /* 1.声明变量 */
  int           result = 0, i = 0;
  varArrayList *primaryIndexArray = NULL;
  int          *index             = NULL;
  Field        *fieldFirst        = NULL;
  Field        *fieldSecond       = NULL;
  FieldType     fieldType;
  IntField     *intFieldFirst     = NULL;
  IntField     *intFieldSecond    = NULL;
  RealField    *floatFieldFirst   = NULL;
  RealField    *floatFieldSecond  = NULL;
  VarCharField *stringFieldFirst  = NULL;
  VarCharField *stringFieldSecond = NULL;

  Tuple **tupleFirst  = (Tuple **)dataFirst;
  Tuple **tupleSecond = (Tuple **)dataSecond;

  /* 2.参数检查 */
  if (array == NULL || tupleFirst == NULL || tupleSecond == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取主键下标的数组 */
  primaryIndexArray = (varArrayList *)array->reserve;

  /* 4.遍历主索引数组 */
  for (i = 0; i < primaryIndexArray->elementCount; i++) {
    /* 4.1.获取索引号 */
    index = (int *)varArrayListGet(primaryIndexArray, i);
    if (index == NULL) {
      return GNCDB_NOT_FOUND;
    }

    /* 4.2.获取tupleFirst和tupleSecond的字段 */
    fieldFirst = (Field *)varArrayListGetPointer((*tupleFirst)->fieldArray, *index);
    if (fieldFirst == NULL) {
      return GNCDB_NOT_FOUND;
    }
    fieldSecond = (Field *)varArrayListGetPointer((*tupleSecond)->fieldArray, *index);
    if (fieldSecond == NULL) {
      return GNCDB_NOT_FOUND;
    }

    /* 4.3.根据字段类型进行比较 */
    fieldType = fieldFirst->fieldType;
    if (fieldType == FIELDTYPE_INTEGER) {
      intFieldFirst  = (IntField *)fieldFirst;
      intFieldSecond = (IntField *)fieldSecond;
      result =
          intFieldFirst->value > intFieldSecond->value ? 1 : (intFieldFirst->value < intFieldSecond->value ? -1 : 0);
    } else if (fieldType == FIELDTYPE_REAL) {
      floatFieldFirst  = (RealField *)fieldFirst;
      floatFieldSecond = (RealField *)fieldSecond;
      result           = floatFieldFirst->value > floatFieldSecond->value
                             ? 1
                             : (floatFieldFirst->value < floatFieldSecond->value ? -1 : 0);
    } else if (fieldType == FIELDTYPE_VARCHAR) {
      stringFieldFirst  = (VarCharField *)fieldFirst;
      stringFieldSecond = (VarCharField *)fieldSecond;
      result            = strcmp(stringFieldFirst->value, stringFieldSecond->value);
    } else if (fieldType == FIELDTYPE_DATE) {
      DateField *dateFieldFirst  = (DateField *)fieldFirst;
      DateField *dateFieldSecond = (DateField *)fieldSecond;
      result                     = dateFieldFirst->value > dateFieldSecond->value
                                       ? 1
                                       : (dateFieldFirst->value < dateFieldSecond->value ? -1 : 0);
    } else if (fieldType == FIELDTYPE_DATETIME) {
      DateTimeField *datetimeFieldFirst  = (DateTimeField *)fieldFirst;
      DateTimeField *datetimeFieldSecond = (DateTimeField *)fieldSecond;
      result                             = DateTimeFieldCompare(datetimeFieldFirst, datetimeFieldSecond);
    }

    /* 4.4.如果result不为0, 说明tupleFirst > tupleSecond | tupleFirst < tupleSecond，若为0则继续判断下一个字段 */
    if (result != 0) {
      break;
    }
  }

  return result;
}

/******************************************************************************InternalEntry****************************************************************************/
/**
 * @brief	internalEntry的创建
 * @param childPageId 孩子页id
 */
InternalEntry *internalEntryConstruct(int childPageId)
{
  /* 1.变量声明 */
  InternalEntry *internalEntry = NULL;

  /* 2.分配内存空间 */
  internalEntry = (InternalEntry *)my_malloc(sizeof(InternalEntry));
  if (internalEntry == NULL) {
    return NULL;
  }

  /* 3.赋值 */
  internalEntry->childPageId   = childPageId;
  internalEntry->keyValueArray = varArrayListCreate(DISORDER, BYTES_POINTER, 0, NULL, keyValueDestroy);
  if (internalEntry->keyValueArray == NULL) {
    my_free(internalEntry);
    return NULL;
  }

  return internalEntry;
}
BYTE *internalRecordDeepCopy(int childPageId, BtreeTable *btreeTable, BYTE *internalRecord)
{
  BYTE *copyInternalRecord = NULL;
  copyInternalRecord       = (BYTE *)my_malloc(btreeTable->internalRecordLength);
  memcpy(copyInternalRecord, &childPageId, INT_SIZE);
  memcpy(copyInternalRecord + INT_SIZE, internalRecord + INT_SIZE, btreeTable->internalRecordLength - INT_SIZE);
  return copyInternalRecord;
}

// 用leafRecord的主键构建internalRecord
BYTE *internalRecordDeepCopyFromLeafRecord(
    int childPageId, char *tableName, BtreeTable *btreeTable, TableSchema *tableSchema, BYTE *leafRecord)
{
  /* 1.变量声明 */
  int     rc                 = 0;
  Column *column             = NULL;
  int     index              = 0;
  BYTE   *copyInternalRecord = NULL;
  int     internalOffset     = INT_SIZE;

  /* 2.判断参数是否为空 */
  if (btreeTable == NULL || tableSchema == NULL) {
    return NULL;
  }

  /* 3.构建一个internalEntry */
  copyInternalRecord = (BYTE *)my_malloc(btreeTable->internalRecordLength);
  memcpy(copyInternalRecord, &childPageId, INT_SIZE);

  /* 3.遍历每一个字段，获取主键 */
  for (index = 0; index < tableSchema->columnNum; index++) {
    /* 3.1.获取第index列的元信息 */
    column = (Column *)varArrayListGetPointer(tableSchema->columnList, index);
    if (column == NULL) {
      return NULL;
    }

    /*3.2.判断第index列是否为主键，不为主键直接跳过 */
    if (column->columnConstraint->isPrimaryKey) {
      /* 3.2.2.根据字段的类型来获取对应的值 */
      switch (column->fieldType) {
        case FIELDTYPE_INTEGER: {
          memcpy(copyInternalRecord + internalOffset, leafRecord + column->offset, INT_SIZE);
          internalOffset += INT_SIZE;
          break;
        }
        case FIELDTYPE_REAL: {
          memcpy(copyInternalRecord + internalOffset, leafRecord + column->offset, DOUBLE_SIZE);
          internalOffset += DOUBLE_SIZE;
          break;
        }
        case FIELDTYPE_VARCHAR: {
          memcpy(copyInternalRecord + internalOffset, leafRecord + column->offset, column->columnConstraint->maxValue);
          internalOffset += column->columnConstraint->maxValue;
          break;
        }
        default: break;
      }

      if (rc != GNCDB_SUCCESS) {
        if (copyInternalRecord != NULL) {
          my_free(copyInternalRecord);
        }
        return NULL;
      }
    }
  }

  return copyInternalRecord;
}

/**
 * @brief	internalEntry的深拷贝
 * @param childPageId 孩子页id
 * @param keyValueArray 主键值
 * @param tableName 根据表名从catalog中获取primaryTypeArray
 * @param db
 * @return 返回深拷贝的internalEntry
 */
// InternalEntry* internalEntryDeepCopy(int childPageId,varArrayList* keyValueArray,char* tableName ,struct Catalog*
// catalog) {
// 	/* 1.变量声明 */
// 	InternalEntry* copyInternalEntry = NULL;
// 	int rc = 0;
// 	int* intValue,*intValueCopy = NULL;
// 	double* doubleValue = NULL, * doubleValueCopy = NULL;
// 	char *string = NULL, *copyString = NULL;
// 	int length = 0;
// 	varArrayList* primaryTypeArray = NULL;
// 	int index = 0;
// 	FieldType* fieldType;

// 	/* 2.判断参数是否为空 */
// 	if (keyValueArray == NULL || catalog == NULL) {
// 		return NULL;
// 	}

// 	/* 3.构建一个internalEntry */
// 	copyInternalEntry = internalEntryConstruct(childPageId);
// 	if (copyInternalEntry == NULL) {
// 		return NULL;
// 	}

// 	/* 4.获取主键的数据类型 */
// 	primaryTypeArray = getPrimaryTypeArray(catalog,tableName);
// 	if (primaryTypeArray == NULL) {
// 		return NULL;
// 	}

// 	/* 5.遍历主键，对每个值进行深拷贝（单主键/联合主键） */
// 	for (index = 0; index < primaryTypeArray->elementCount; index++) {
// 		/* 5.1.获取主键类型 */
// 		fieldType = (FieldType*)varArrayListGet(primaryTypeArray, index);
// 		if (fieldType == NULL) {
// 			internalEntryDestroy(&copyInternalEntry);
// 			return NULL;
// 		}

// 		/* 5.2.不同类型的数据，做不同的处理 */
// 		if (*fieldType == FIELDTYPE_INTEGER) {
// 			intValue = (int*)varArrayListGetPointer(keyValueArray, index);
// 			if (intValue == NULL) {
// 				internalEntryDestroy(&copyInternalEntry);
// 				return NULL;
// 			}

// 			intValueCopy = (int*)my_malloc(sizeof(int));
// 			if (intValueCopy == NULL) {
// 				internalEntryDestroy(&copyInternalEntry);
// 				return NULL;
// 			}
// 			*intValueCopy = *intValue;
// 			rc = internalEntryAddEntry(copyInternalEntry, intValueCopy);
// 		}
// 		else if (*fieldType == FIELDTYPE_REAL) {
// 			doubleValue = (double*)varArrayListGetPointer(keyValueArray, index);
// 			if (doubleValue == NULL) {
// 				internalEntryDestroy(&copyInternalEntry);
// 				return NULL;
// 			}

// 			doubleValueCopy = (double*)my_malloc(sizeof(double));
// 			if (doubleValueCopy == NULL) {
// 				internalEntryDestroy(&copyInternalEntry);
// 				return NULL;
// 			}
// 			*doubleValueCopy = *doubleValue;
// 			rc = internalEntryAddEntry(copyInternalEntry, doubleValueCopy);
// 		}
// 		else if (*fieldType == FIELDTYPE_VARCHAR) {
// 			string = (char*)varArrayListGetPointer(keyValueArray,index);
// 			if (string == NULL) {
// 				internalEntryDestroy(&copyInternalEntry);
// 				return NULL;
// 			}

// 			length = strlen(string);
// 			copyString = (char*)my_malloc(length+1);
// 			if (copyString == NULL) {
// 				internalEntryDestroy(&copyInternalEntry);
// 				return NULL;
// 			}
// 			memcpy(copyString, string, length);
// 			copyString[length] = '\0';

// 			rc = internalEntryAddEntry(copyInternalEntry, copyString);
// 		}

// 		if (rc != GNCDB_SUCCESS) {
// 			internalEntryDestroy(&copyInternalEntry);
// 			return NULL;
// 		}
// 	}

// 	return copyInternalEntry;
// }

/**
 * @brief	internalEntry的创建
 * @param internalEntry internalEntry的指针
 * @param entry 字段的值(int/double/string)
 */
int internalEntryAddEntry(InternalEntry *internalEntry, void *entry)
{
  /* 1.变量声明 */
  int rc = 0;

  /* 2.判断参数是否为空 */
  if (internalEntry == NULL || entry == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.添加internalEntry */
  rc = varArrayListAddPointer(internalEntry->keyValueArray, entry);
  if (rc != GNCDB_SUCCESS) {
    return rc;
  }

  return GNCDB_SUCCESS;
}

/**
 * @brief	internalEntry的销毁
 * @param internalEntry internalEntry的指针
 */
void internalEntryDestroy(void *internalEntry)
{
  InternalEntry **pInternalEntry = (InternalEntry **)internalEntry;
  /* 1.判断参数是否为空 */
  if (pInternalEntry == NULL || *pInternalEntry == NULL) {
    return;
  }

  /* 2.释放内存空间 */
  varArrayListDestroy(&(*pInternalEntry)->keyValueArray);
  my_free(*pInternalEntry);
  *pInternalEntry = NULL;
}

int internalRecordCompareFun(
    varArrayList *array, varArrayList *varcharLenArray, BYTE *internalRecordFirst, BYTE *internalRecordSecond)
{
  /* 1.声明变量 */
  int           result = 0, i = 0, varcharIndex = 0;
  varArrayList *primaryTypeArray    = NULL;
  FieldType    *fieldType           = NULL;
  int           keyValueFirst       = 0;
  int           keyValueSecond      = 0;
  double        floatKeyValueFirst  = 0;
  double        floatKeyValueSecond = 0;
  int           offset = INT_SIZE;
  // InternalEntry** internalEntryFirst = (InternalEntry**)dataFirst;
  // InternalEntry** internalEntrySecond = (InternalEntry**)dataSecond;

  /* 2.参数检查 */
  if (array == NULL || internalRecordFirst == NULL || internalRecordSecond == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取主键类型数组 */
  // primaryTypeArray = (varArrayList*)array->reserve;
  primaryTypeArray = (varArrayList *)array;
  /* 4.遍历主键类型数组 */
  for (i = 0; i < primaryTypeArray->elementCount; i++) {
    /* 4.1.获取主键类型 */
    fieldType = (FieldType *)varArrayListGet(primaryTypeArray, i);
    if (fieldType == NULL) {
      return GNCDB_NOT_FOUND;
    }

    /* 4.2.根据主键类型进行比较 */
    if (*fieldType == FIELDTYPE_INTEGER) {
      memcpy(&keyValueFirst, internalRecordFirst + offset, INT_SIZE);
      memcpy(&keyValueSecond, internalRecordSecond + offset, INT_SIZE);
      offset += INT_SIZE;
      // keyValueFirst = (int*)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
      // if (keyValueFirst == NULL) {
      // 	return GNCDB_NOT_FOUND;
      // }

      // keyValueSecond = (int*)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      // if (keyValueSecond == NULL) {
      // 	return GNCDB_NOT_FOUND;
      // }

      result = keyValueFirst > keyValueSecond ? 1 : (keyValueFirst < keyValueSecond ? -1 : 0);
    } else if (*fieldType == FIELDTYPE_REAL) {
      memcpy(&floatKeyValueFirst, internalRecordFirst + offset, DOUBLE_SIZE);
      memcpy(&floatKeyValueSecond, internalRecordSecond + offset, DOUBLE_SIZE);
      offset += DOUBLE_SIZE;
      // floatKeyValueFirst = (double*)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
      // if (floatKeyValueFirst == NULL) {
      // 	return GNCDB_NOT_FOUND;
      // }

      // floatKeyValueSecond = (double*)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      // if (floatKeyValueSecond == NULL) {
      // 	return GNCDB_NOT_FOUND;
      // }

			result = floatKeyValueFirst > floatKeyValueSecond ? 1 : (floatKeyValueFirst < floatKeyValueSecond ? -1 : 0);
		}
		else if (*fieldType == FIELDTYPE_VARCHAR) {
			int len = *(int*)varArrayListGet(varcharLenArray, varcharIndex);
			// memcpy(stringKeyValueFirst, internalRecordFirst + offset, len);
			// memcpy(stringKeyValueSecond, internalRecordSecond + offset, len);
			
			// stringKeyValueFirst = (char*)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
			// if (stringKeyValueFirst == NULL) {
			// 	return GNCDB_NOT_FOUND;
			// }

      // stringKeyValueSecond = (char*)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      // if (stringKeyValueSecond == NULL) {
      // 	return GNCDB_NOT_FOUND;
      // }

			result = strncmp((const char *)(internalRecordFirst + offset), (const char *)(internalRecordSecond + offset), len);
			offset += len;
			varcharIndex++;
		}

    /* 4.3.如果result不为0, 说明internalEntryFirst > internalEntrySecond | internalEntryFirst <
     * internalEntrySecond，若为0则继续判断下一个keyValue值 */
    if (result != 0) {
      break;
    }
  }

  return result;
}

/**
 * @brief 内部节点中entry大小的比较
 * @param internalEntryFirst 第一个entry
 * @param internalEntrySecond 第二个entry
 * @param primaryTypeArray entry的数据类型
 * @return 返回大小比较后的结果 internalEntryFirst > internalEntrySecond ：1|internalEntryFirst < internalEntrySecond
 * ：-1|internalEntryFirst = internalEntrySecond ：0
 */
int internalEntryCompareFun(varArrayList *array, void *dataFirst, void *dataSecond)
{
  /* 1.声明变量 */
  int             result = 0, i = 0;
  varArrayList   *primaryTypeArray       = NULL;
  FieldType      *fieldType              = NULL;
  int            *keyValueFirst          = NULL;
  int            *keyValueSecond         = NULL;
  double         *floatKeyValueFirst     = NULL;
  double         *floatKeyValueSecond    = NULL;
  char           *stringKeyValueFirst    = NULL;
  char           *stringKeyValueSecond   = NULL;
  int            *dateValueFirst         = NULL;
  int            *dateValueSecond        = NULL;
  char           *datetimeStrValueFirst  = NULL;
  char           *datetimeStrValueSecond = NULL;
  InternalEntry **internalEntryFirst     = (InternalEntry **)dataFirst;
  InternalEntry **internalEntrySecond    = (InternalEntry **)dataSecond;

  /* 2.参数检查 */
  if (array == NULL || internalEntryFirst == NULL || internalEntrySecond == NULL) {
    return GNCDB_PARAMNULL;
  }

  /* 3.获取主键类型数组 */
  primaryTypeArray = (varArrayList *)array->reserve;

  /* 4.遍历主键类型数组 */
  for (i = 0; i < primaryTypeArray->elementCount; i++) {
    /* 4.1.获取主键类型 */
    fieldType = (FieldType *)varArrayListGet(primaryTypeArray, i);
    if (fieldType == NULL) {
      return GNCDB_NOT_FOUND;
    }

    /* 4.2.根据主键类型进行比较 */
    if (*fieldType == FIELDTYPE_INTEGER) {
      keyValueFirst = (int *)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
      if (keyValueFirst == NULL) {
        return GNCDB_NOT_FOUND;
      }

      keyValueSecond = (int *)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      if (keyValueSecond == NULL) {
        return GNCDB_NOT_FOUND;
      }

      result = *keyValueFirst > *keyValueSecond ? 1 : (*keyValueFirst < *keyValueSecond ? -1 : 0);
    } else if (*fieldType == FIELDTYPE_REAL) {
      floatKeyValueFirst = (double *)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
      if (floatKeyValueFirst == NULL) {
        return GNCDB_NOT_FOUND;
      }

      floatKeyValueSecond = (double *)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      if (floatKeyValueSecond == NULL) {
        return GNCDB_NOT_FOUND;
      }

      result = *floatKeyValueFirst > *floatKeyValueSecond ? 1 : (*floatKeyValueFirst < *floatKeyValueSecond ? -1 : 0);
    } else if (*fieldType == FIELDTYPE_VARCHAR) {
      stringKeyValueFirst = (char *)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
      if (stringKeyValueFirst == NULL) {
        return GNCDB_NOT_FOUND;
      }

      stringKeyValueSecond = (char *)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      if (stringKeyValueSecond == NULL) {
        return GNCDB_NOT_FOUND;
      }

      result = strcmp(stringKeyValueFirst, stringKeyValueSecond);
    } else if (*fieldType == FIELDTYPE_DATE) {
      dateValueFirst = (int *)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
      if (dateValueFirst == NULL) {
        return GNCDB_NOT_FOUND;
      }

      dateValueSecond = (int *)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      if (dateValueSecond == NULL) {
        return GNCDB_NOT_FOUND;
      }

      result = *dateValueFirst > *dateValueSecond ? 1 : (*dateValueFirst < *dateValueSecond ? -1 : 0);
    } else if (*fieldType == FIELDTYPE_DATETIME) {
      datetimeStrValueFirst = (char *)varArrayListGetPointer((*internalEntryFirst)->keyValueArray, i);
      if (datetimeStrValueFirst == NULL) {
        return GNCDB_NOT_FOUND;
      }

      datetimeStrValueSecond = (char *)varArrayListGetPointer((*internalEntrySecond)->keyValueArray, i);
      if (datetimeStrValueSecond == NULL) {
        return GNCDB_NOT_FOUND;
      }

      result = datetimeStringCompare(datetimeStrValueFirst, datetimeStrValueSecond);
    }

    /* 4.3.如果result不为0, 说明internalEntryFirst > internalEntrySecond | internalEntryFirst <
     * internalEntrySecond，若为0则继续判断下一个keyValue值 */
    if (result != 0) {
      break;
    }
  }

  return result;
}
/**
 * @brief	获取tuple中的索引所需键值(浅拷贝keyValue)
 * @param keyValueArray 存放键值（keyValueArray的内存分配在函数外面完成）
 * @param tuple 元组的指针
 * @param tableSchema
 * @param indexSchema
 */
int leafTupleGetIndexKeyValue(struct varArrayList *keyValueArray, struct Tuple *tuple, struct TableSchema *indexSchema)
{
  int            rc           = 0;
  IntField     **intField     = NULL;
  RealField    **realField    = NULL;
  VarCharField **varCharField = NULL;
  Column       **column       = NULL;
  Field       ***field        = NULL;
  int           *intValue     = NULL;
  double        *doubleValue  = NULL;
  char          *stringValue  = NULL;
  int            i, index = 0;
  char          *filename = NULL;

  if (keyValueArray == NULL || tuple == NULL || indexSchema == NULL) {
    return GNCDB_PARAMNULL;
  }
  /* 不确定是否indexschema需要后三信息列 */
  for (i = 0; i < indexSchema->columnNum - 3; i++) {
    column = (Column **)varArrayListGet(indexSchema->columnList, i);
    if (column == NULL) {
      return GNCDB_NOT_FOUND;
    }
    filename = (*column)->fieldName;
    index    = tableSchemaGetIndex(indexSchema, filename);
    if (index < 0) {
      return GNCDB_FIELD_NOT_EXIST;
    }
    field = (Field ***)varArrayListGet(tuple->fieldArray, index);
    if (field == NULL) {
      return GNCDB_NOT_FOUND;
    }

    switch ((**field)->fieldType) {
      case FIELDTYPE_INTEGER: {
        intField  = (IntField **)field;
        intValue  = (int *)my_malloc(sizeof(int));
        *intValue = (*intField)->value;
        rc        = varArrayListAdd(keyValueArray, &intValue);
        break;
      }
      case FIELDTYPE_REAL: {
        realField    = (RealField **)field;
        doubleValue  = (double *)my_malloc(sizeof(double));
        *doubleValue = (*realField)->value;
        rc           = varArrayListAdd(keyValueArray, &doubleValue);
        break;
      }
      case FIELDTYPE_VARCHAR: {
        varCharField = (VarCharField **)field;
        stringValue  = (char *)my_malloc(strlen((*varCharField)->value) + 1);
        strcpy(stringValue, (*varCharField)->value);
        rc = varArrayListAdd(keyValueArray, &stringValue);
        break;
      }
      default: break;
    }

    if (rc != GNCDB_SUCCESS) {
      if (intValue != NULL) {
        my_free(intValue);
      }
      if (doubleValue != NULL) {
        my_free(doubleValue);
      }
      if (stringValue != NULL) {
        my_free(stringValue);
      }
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}
/**
 * @brief	获取tuple中的哈希索引所需键值(浅拷贝keyValue)
 * @param keyValueArray 存放键值（keyValueArray的内存分配在函数外面完成）
 * @param tuple 元组的指针
 * @param indexColList 索引列信息List
 * @param indexSchema
 */
int leafTupleGetHashIndexKeyValue(varArrayList *keyValueArray, BYTE *record, varArrayList* indexColList)
{
  int      rc          = 0;
  Column **column      = NULL;
  int     *intValue    = NULL;
  double  *doubleValue = NULL;
  char    *stringValue = NULL;
  int      i           = 0;

  if (keyValueArray == NULL || record == NULL || indexColList == NULL) {
    return GNCDB_PARAMNULL;
  }
  for (i = 0; i < indexColList->elementCount; i++) {
    column = (Column **)varArrayListGet(indexColList, i);
    if (column == NULL) {
      return GNCDB_NOT_FOUND;
    }

    switch ((*column)->fieldType) {
      case FIELDTYPE_INTEGER: {
        intValue = (int *)(record + (*column)->offset);
        rc       = varArrayListAddPointer(keyValueArray, intValue);
        break;
      }
      case FIELDTYPE_REAL: {
        doubleValue = (double *)(record + (*column)->offset);
        rc          = varArrayListAddPointer(keyValueArray, doubleValue);
        break;
      }
      case FIELDTYPE_VARCHAR: {
        stringValue  = (char *)(record + (*column)->offset);
        rc           = varArrayListAddPointer(keyValueArray, stringValue);
        break;
      }

      default: break;
    }

    if (rc != GNCDB_SUCCESS) {
      return rc;
    }
  }

  return GNCDB_SUCCESS;
}
/**
 * @brief	获取leafTuple中的下标对应的值，添加到valueArray中
 * @param valueArray 存放值（valueArray的内存分配在函数外面完成）
 * @param tuple 元组的指针
 * @param index
 */
int leafTupleGetValueByIndex(varArrayList *valueArray, Tuple *tuple, int index)
{
  int            rc           = 0;
  IntField     **intField     = NULL;
  RealField    **realField    = NULL;
  VarCharField **varCharField = NULL;
  int           *intValue     = NULL;
  double        *doubleValue  = NULL;
  char          *stringValue  = NULL;
  Field       ***field        = NULL;

  if (tuple == NULL) {
    return GNCDB_PARAMNULL;
  }
  field = (Field ***)varArrayListGet(tuple->fieldArray, index);
  if (field == NULL) {
    return GNCDB_NOT_FOUND;
  }

  switch ((**field)->fieldType) {
    case FIELDTYPE_INTEGER: {
      intField = (IntField **)field;
      intValue = &(*intField)->value;
      rc       = varArrayListAdd(valueArray, &intValue);
      break;
    }
    case FIELDTYPE_REAL: {
      realField   = (RealField **)field;
      doubleValue = &(*realField)->value;
      rc          = varArrayListAdd(valueArray, &doubleValue);
      break;
    }
    case FIELDTYPE_VARCHAR: {
      varCharField = (VarCharField **)field;
      stringValue  = (*varCharField)->value;
      rc           = varArrayListAdd(valueArray, &stringValue);
      break;
    }
    default: break;
  }

  if (rc != GNCDB_SUCCESS) {
    return rc;
  }
  return GNCDB_SUCCESS;
}
