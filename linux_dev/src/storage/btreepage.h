/**
 * @file btreepage.h
 * <AUTHOR>
 * @brief  B+树中页的定义以及有关页操作的函数声明
 * @version 0.1
 * @date 2023-01-30
 *
 * @copyright Copyright (c) 2023
 *
 */

 #ifndef _GNCDB_BTREE_PAGE_H_
 #define _GNCDB_BTREE_PAGE_H_
 
 #include "typedefine.h"
 #include "gncdbconstant.h"
 #include "vararraylist.h"
 #include "tuple.h"
 #include "gncdb.h"
 #include "catalog.h"
 #include "readwritelock.h"


 struct TableSchema;
 struct Catalog;
 struct Transaction;
 struct BtreeTable;
 struct Tuple;
 struct InternalEntry;
 
 typedef struct Page {
	 PageType pageType; 		/* 页的类型 */ 
	 int id;   					/* 页的id */ 
	 ReadWriteLock rwlock_t;	/* 读写锁 */ 
	 /*以下为新增字段*/
	 BYTE* pData;         /*磁盘中的页数据*/
	 struct Page* pNext;      /*指向下一个freePage*/
	 bool isMalloc;   	/*是否是malloc出来的*/
 }Page;
 
 typedef struct BtreePage {
	 struct Page page;
	 // char* tableName; 			/* tableName */ 
	 char tableName[TABLENAME_FIELD_MAXLEN+1]; 			/* tableName */
	 int keyLength;				/* 主键长度 */
	 int entryNum;				/* Tuple的数量 */ 
	 int nextPageId;			/* 内部节点最右边孩子页号 / 叶子节点右边节点页号（维护一条类似从最左边叶子节点到最右边叶子节点的链表）  */ 
	 int prevInsertIndex;       /*上一次元组插入页的位置*/
	 
 }BtreePage;
 
 /* 紧接着溢出页页头后4个字节存放当前页的溢出数据的字节数 */ 
 typedef struct OverflowPage {
	 Page page;
	 int nextPageId;			/* 溢出页是下一溢出页的id */
	 BYTE* overflowData;		/* 溢出数据部分 */ 
 }OverflowPage;
 
 typedef struct FreePage {
	 Page page;
	 int nextPageId;			/* 下一空白页的id  */ 
 }FreePage;
 // Meta Page
typedef struct MetaPage
{
  struct Page page;
  int      hashFunctionId;    /* hash函数类型 */
  int      keyTidPairCount;   /* Hash索引记录的键值对数量 */
  double  fillFactor;        /* 哈希表装填因子 */
  int highMask;          /* 高位掩码 */
  int lowMask;           /* 低位掩码 */
  int      bucketCount;       /* 桶数量 */
  int      maxBucketNumber;   /* 最大桶编号 */
  int      splitCount;        /* 桶分裂的次数 */
  //int      bucketPageId[240]; /* 桶页地址数组,0号元素为桶页地址数组溢出页Id */
} MetaPage;

// Bucket Page
typedef struct BucketPage
{
  struct Page page;              /* 将桶页的页锁作为整个桶的桶锁 */
  int           bucketId;        /* 页面所属的桶号 */
  int           keyTidPairCount; /* 哈希桶（包括桶页和溢出页链表）中存储的键值对数量 */
  int           firstOverflowPageId;  /* 第一个溢出页地址 */
  int           lastOverflowPageId;   /* 最后一个溢出页地址 */
  int           primaryKeyLenth;  //主键长度
} BucketPage;

// HashOverflow Page
typedef struct HashOverflowPage
{
  struct Page page;
  int           bucketId;         /* 页面所属的桶号，当桶号为-1时为储存桶页地址数组溢出页 */
  int           keyTidPairCount; /* 溢出页中存储的键值对数量 */
  int           prevPageId;       /* 前一个页面的地址 */
  int           nextPageId;       /* 下一个页面地址 */
  int           primaryKeyLenth;  //varchar型主键最大长度
} HashOverflowPage;
 typedef union {
	 struct BtreePage btreePage;
	 struct OverflowPage overflowPage;
	 struct FreePage freePage;
	 struct MetaPage metaPage;
	 struct BucketPage bucketPage;
	 struct HashOverflowPage hashOverflowPage;
 }allKindsPage;
 
 /* page */
 void pageInit(struct Page* page,int pageId, PageType pageType, bool isMalloc);
 
 /* btreePage */
 int btreePageFillInHeader(struct BtreePage* btreePage);
 int btreePageReadHeader(struct BtreePage* btreePage);
 int btreePageConstruct(BtreePage* btreepage, int pageId, char* tableName);
 int btreePageConstructWithOutData(struct BtreePage* btreePage, PageType pageType, int pageId, char* tableName, struct Catalog* catalog);
 struct BtreePage* btreePageMallocConstructWithOutData(struct GNCDB* db, PageType pageType, int pageId, char* tableName, struct Catalog* catalog);
 struct BtreePage* btreePageDeepCopy(struct GNCDB* db, struct BtreePage* btreePage, struct Catalog* catalog);
 BYTE* btreePageToByte(struct BtreePage* btreePage);
 void btreePageDestroy(struct BtreePage** btreePage);
 void btreePageDestroyMalloc(BtreePage** btreePage);
 
 int btreePageExchange(struct BtreeTable* btreeTable, struct BtreePage* leftBtreePage, struct BtreePage* parentBtreePage, struct GNCDB* db, struct Transaction* tx);
 
 /*  leafPage */
 int leafPageInsertTuple(struct BtreePage* btreePage, BYTE* record, struct TableSchema* tableSchema, struct BtreeTable* btreeTable);
 int compareCertainFieldValue(void* valueFirst, void* valueSecond, FieldType fieldType, Predicate predicate);
 int compareIntValue(void* valueFirst,void* valueSecond);
 int compareRealValue(void* valueFirst,void* valueSecond);
 int compareVarcharValue(void* valueFirst,void* valueSecond);
 BYTE* leafPageFindEntryByKeyvalue(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog, Predicate predicate,struct BtreeTable *btreeTable);
 int leafPageFindEntryIndexByKeyvalue(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog, Predicate predicate,struct BtreeTable *btreeTable);
 int leafPageDeleteTuple(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog,struct BtreeTable* btreeTable, int sourceIndex);
 
 /* internalPage */
 int internalPageInsertEntry(struct BtreePage* btreePage, struct InternalEntry* internalEntry);
 int internalPageInsertRecord(struct BtreePage* btreePage, BYTE* internalRecord,struct  TableSchema* tableSchema,struct  BtreeTable* btreeTable,struct Catalog* catalog);
 int internalPageUpdateRecord(struct BtreePage* btreePage, BYTE* oldInternalRecord, BYTE* newInternalRecord,struct  TableSchema* tableSchema,struct  BtreeTable* btreeTable,struct Catalog* catalog);
 /*用keyvaluearray查找对应的record*/
 BYTE* internalPageFindEntryByKeyvalue(struct BtreePage* btreePage, struct varArrayList* keyValueArray, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog,struct BtreeTable* btreeTable, Predicate predicate,int *index);
 //用record查找对应的record
 BYTE* internalPageFindRecordByKeyvalue(struct BtreePage* btreePage, BYTE* sourceRecord, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog,struct BtreeTable* btreeTable, Predicate predicate);
 int internalPageFindRecordIndexByKeyvalue(struct BtreePage* btreePage, BYTE* sourceRecord, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog, Predicate predicate,struct BtreeTable *btreeTable);
 int internalPageDeleteRecord(struct BtreePage* btreePage, BYTE* internalRecord, struct TableSchema* tableSchema, char* tableName, struct Catalog* catalog,struct BtreeTable* btreeTable);
 /* overflowPage */
 int overflowPageFillInHeader(struct OverflowPage* overflowPage, BYTE* buf);
 int overflowPageReadHeader(struct OverflowPage* overflowPage);
 int overflowPageConstruct(OverflowPage* overflowPage, int pageId);
 int overflowPageConstructWithOutData(struct OverflowPage* overflowPage,int pageId, int nextPageId);
 struct OverflowPage* overflowPageMallocConstructWithOutData(int pageSize, int pageId, int nextPageId);
 struct OverflowPage* overflowPageDeepCopy(struct GNCDB* db, OverflowPage* overflowPage);
 BYTE* overflowPageToByte(struct GNCDB* db, struct OverflowPage* overflowPage);
 void overflowPageDestroy(OverflowPage** overflowPage);
 void overflowPageDestroyMalloc(OverflowPage** overflowPage);
 
 /* freePage */
 int freePageFillInHeader(struct FreePage* freePage, BYTE* buf);
 int freePageReadHeader(struct FreePage* freePage);
 int freePageConstruct(FreePage* freePage, int pageId);
 int freePageConstructWithOutData(struct FreePage* freePage,int pageId,int nextPageId);
 struct FreePage* freePageMallocConstructWithOutData(int pageSize, int pageId, int nextPageId);
 struct FreePage* freePageDeepCopy(struct GNCDB* db, int pageId, int nextPageId);
 BYTE* freePageToByte(struct GNCDB* db, struct FreePage* freePage);
 void freePageDestroy(struct FreePage** freePage);
 void freePageDestroyMalloc(FreePage** freePage);
 
 /* debug */
 /* 输出指定页中的tuple主键 */
 int printfLeftPageTuplePKey(BtreePage* page);
 
 
 #endif // !_GNCDB_BTREE_PAGE_H_
 