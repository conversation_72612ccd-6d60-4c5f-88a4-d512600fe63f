/**
 * @file field.h
 * <AUTHOR>
 * @brief  元组四种字段类型的构造和相关操作的实现
 * @version 0.1
 * @date 2023-01-30
 *
 * @copyright Copyright (c) 2023
 *
 */
#include<string.h>
#include "field.h"
#include "typedefine.h"

 /**
  * @brief	int类型字段的构造
  * @param value int值
  * @return IntField*
 */
struct  IntField* intFieldConstruct(int value) {
	/* 1.变量声明 */
	IntField* intField = NULL;

	/* 2.分配空间 */
	intField = (IntField*)my_malloc(sizeof(IntField));
	if (intField == NULL) {
		return NULL;
	}

	/* 3.赋值 */
	intField->value = value;
	intField->baseInfo.fieldSize = INT_SIZE;
	intField->baseInfo.fieldType = FIELDTYPE_INTEGER;

	return intField;
}


/**
 * @brief	date类型字段的构造
 * @param value date值
 * @return DateField*
*/
struct DateField* dateFieldConstruct(int value){

	/* 1.变量声明 */
	DateField* dateField = NULL;

	/* 2.分配空间 */
	dateField = (DateField*)my_malloc(sizeof(DateField));
	if (dateField == NULL) {
		return NULL;
	}

	/* 3.赋值 */
	dateField->value = value;
	dateField->baseInfo.fieldSize = INT_SIZE;
	dateField->baseInfo.fieldType = FIELDTYPE_DATE;
	return dateField;
}

/**
 * @brief	float类型字段的构造
 * @param value float值
 * @return FloatField*
*/
struct RealField* realFieldConstruct(double value) {
	/* 1.变量声明 */
	RealField* realField = NULL;

	/* 2.分配空间 */
	realField = (RealField*)my_malloc(sizeof(RealField));
	if (realField == NULL) {
		return NULL;
	}

	/* 3.赋值 */
	realField->value = value;
	realField->baseInfo.fieldSize = DOUBLE_SIZE;
	realField->baseInfo.fieldType = FIELDTYPE_REAL;

	return realField;
}

/**
 * @brief	string类型字段的构造
 * @param value 字符串
 * @return StringField*
*/
struct VarCharField* varCharFieldConstruct(char* value) {
	/* 1.变量声明 */
	VarCharField* varCharField = NULL;
	int size = 0;

	/* 2.判断参数是否为空 */
	if (value == NULL) {
		return NULL;
	}

	/* 3.分配空间 */
	varCharField = (VarCharField*)my_malloc(sizeof(VarCharField));
	if (varCharField == NULL) {
		return NULL;
	}

	/* 4.深拷贝字符串 */
	size = strlen(value);
	varCharField->value = (char*)my_malloc(size + 1);
	if (varCharField->value == NULL) {
		my_free(varCharField);
		return NULL;
	}
	memcpy(varCharField->value, value, size);
	varCharField->value[size] = '\0';

	/* 5.赋值 */
	varCharField->baseInfo.fieldSize = size;
	varCharField->baseInfo.fieldType = FIELDTYPE_VARCHAR;

	return varCharField;
}

/**
 * @brief	blob类型字段的构造
 * @param overflowPageId blob内容所存放的第一个溢出页页号
 * @param size blob内容的字节数
 * @return BlobField*
*/
struct BlobField* blobFieldConstruct(int overflowPageId, int size) {
	/* 1.变量声明 */
	BlobField* blobField = NULL;

	/* 2.分配空间 */
	blobField = (BlobField*)my_malloc(sizeof(BlobField));
	if (blobField == NULL) {
		return NULL;
	}

	/* 3.赋值 */
	blobField->overflowPageId = overflowPageId;
	blobField->size = size;
	blobField->baseInfo.fieldSize = INT_SIZE;
	blobField->baseInfo.fieldType = FIELDTYPE_BLOB;

	return blobField;
}



/**
 * @brief	DateTime类型字段的构造
 * @param dateValue 日期值
 * @param timeValue 时间值
 * @return DateTimeField*
*/
struct DateTimeField* datetimeFieldConstruct(int dateValue,int timeValue){
	/* 1.变量声明 */
	DateTimeField* dateTimeField = NULL;

	/* 2.分配空间 */
	dateTimeField = (DateTimeField*)my_malloc(sizeof(DateTimeField));
	if (dateTimeField == NULL) {
		return NULL;
	}

	/* 3.赋值 */
	dateTimeField->dateValue = dateValue;
	dateTimeField->timeValue = timeValue;
	dateTimeField->baseInfo.fieldSize = INT_SIZE * 2;
	dateTimeField->baseInfo.fieldType = FIELDTYPE_DATETIME;

	return dateTimeField;
}



int DateTimeFieldCompare(void* fieldFirst, void* fieldSecond) {
	DateTimeField* dateTimeField1 = (DateTimeField*)fieldFirst;
	DateTimeField* dateTimeField2 = (DateTimeField*)fieldSecond;
	if(dateTimeField1->dateValue == dateTimeField2->dateValue){
		if(dateTimeField1->timeValue == dateTimeField2->timeValue){
			return 0;
		}
		else if(dateTimeField1->timeValue > dateTimeField2->timeValue){
			return 1;
		}
		else{
			return -1;
		}
	}
	else if(dateTimeField1->dateValue > dateTimeField2->dateValue){
		return 1;
	}
	else{
		return -1;
	}
}

char* DateTimeFieldToString(DateTimeField* dateTimeField){
	char* str = (char*)my_malloc(64);
  	sprintf(str, "%d-%02d-%02d %02d:%02d:%02d", dateTimeField->dateValue / 10000, dateTimeField->dateValue % 10000 / 100, dateTimeField->dateValue % 100, dateTimeField->timeValue / 10000, dateTimeField->timeValue % 10000 / 100, dateTimeField->timeValue % 100);
	return str;

}

struct 	TextField* textFieldConstruct(int overflowPageId,int size){
	TextField* textField = (TextField*)my_malloc(sizeof(TextField));
	if (textField == NULL) {
		return NULL;
	}
	textField->overflowPageId = overflowPageId;
	textField->size = size;
	textField->baseInfo.fieldSize = INT_SIZE;
	textField->baseInfo.fieldType = FIELDTYPE_TEXT;
	return textField;
}

// struct Field* nullFieldConstruct(FieldType fieldType){
// 	/* 1.变量声明 */
// 	Field* field = NULL;

// 	/* 2.分配空间 */
// 	field = (Field*)my_malloc(sizeof(Field));
// 	if(field == NULL){
// 		return NULL;
// 	}

// 	/* 3.赋值 */
// 	field->fieldType = fieldType;
// 	// TODO 类型不同，也应该size不同？
// 	field->fieldSize = 0;
// 	field->isNull = true;

// 	return field;
// }


/**
 * @brief	date字段的销毁
 * @param dateField 字段
*/
void dateFieldDestroy(struct DateField** dateField){
	/* 1.判断参数是否为空 */
	if (dateField == NULL || *dateField == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	my_free(*dateField);
	*dateField = NULL;
}

/**
 * @brief	int类型字段的销毁
 * @param intField 字段
*/
void intFieldDestroy(struct IntField** intField) {
	/* 1.判断参数是否为空 */
	if (intField == NULL || *intField == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	my_free(*intField);
	*intField = NULL;
}

/**
 * @brief	float类型字段的销毁
 * @param floatField 字段
*/
void realFieldDestroy(struct RealField** realField) {
	/* 1.判断参数是否为空 */
	if (realField == NULL || *realField == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	my_free(*realField);
	*realField = NULL;
}

/**
 * @brief	string类型字段的销毁
 * @param stringField 字段
*/
void varCharFieldDestroy(struct VarCharField** varCharField) {
	/* 1.判断参数是否为空 */
	if (varCharField == NULL || *varCharField == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	my_free((*varCharField)->value);
	my_free(*varCharField);
	*varCharField = NULL;
}

/**
 * @brief	blob类型字段的销毁
 * @param blobField 字段
*/
void blobFieldDestroy(struct BlobField** blobField) {
	/* 1.判断参数是否为空 */
	if (blobField == NULL || *blobField == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	my_free(*blobField);
	*blobField = NULL;
}

// /**
//  * @brief	DateTime类型字段的销毁
//  * @param dateTimeField 字段
// */
// void dateTimeFieldDestroy(struct DateTimeField** dateTimeField){
// 	/* 1.判断参数是否为空 */
// 	if (dateTimeField == NULL || *dateTimeField == NULL) {
// 		return;
// 	}

// 	/* 2.释放内存空间 */
// 	my_free(*dateTimeField);
// 	*dateTimeField = NULL;
// }
void datetimeFieldDestroy(struct DateTimeField** datetimeField){
	/* 1.判断参数是否为空 */
	if (datetimeField == NULL || *datetimeField == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	my_free(*datetimeField);
	*datetimeField = NULL;

}


void textFieldDestroy(struct TextField** textField){
	/* 1.判断参数是否为空 */
	if (textField == NULL || *textField == NULL) {
		return;
	}

	/* 2.释放内存空间 */
	my_free(*textField);
	*textField = NULL;

}
/**
 * @brief	字段的销毁
 * @param field 字段
*/
void fieldDestroy(void* data) {
	/* 1.变量声明 */
	struct Field** ppField = NULL;

	/* 2.判断参数是否为空 */
	if (data == NULL) {
		return;
	}

	/* 3.根据field的类型调用不同的销毁函数 */
	ppField = data;
	if(ppField == NULL || *ppField == NULL){
		return;
	}
	switch ((*ppField)->fieldType)
	{
	case FIELDTYPE_INTEGER:
		intFieldDestroy((IntField**)ppField);
		break;
	case FIELDTYPE_REAL:
		realFieldDestroy((RealField**)ppField);
		break;
	case FIELDTYPE_VARCHAR:
		varCharFieldDestroy((VarCharField**)ppField);
		break;
	case FIELDTYPE_BLOB:
		blobFieldDestroy((BlobField**)ppField);
		break;
	case FIELDTYPE_DATE:
		dateFieldDestroy((DateField**)ppField);
		break;
	case FIELDTYPE_DATETIME:
		datetimeFieldDestroy((DateTimeField**)ppField);
		break;
	case FIELDTYPE_TEXT:
		textFieldDestroy((TextField **)ppField);
		break;
	default:
		break;
	}
}

/**
 * @brief	需要销毁的指针
 * @param data
*/
void keyValueDestroy(void* data) {
	void** pData = data;
	if (pData == NULL || *pData == NULL) {
		return ;
	}

	my_free(*pData);
}

/* todo? 这个函数暂时没有使用到 */
/**
 * @brief	字符串转化成对应类型的field
 * @param string 字段
 * @param result 结果
*/
int stringToField(const char* string, void* result) {
	/* 1.变量声明 */
	int num = 0;
	int index = 0;
	IntField* tmp_intfield = NULL;
	RealField* tmp_realfield = NULL;
	VarCharField* tmp_varCharfield = NULL;
    double numDouble = (double)num;
    double base = 0.1;

	/* 2.将字符串转化为对应类型的field */
	while (string[index] >= '0' && string[index] <= '9' && index < (int)strlen(string)) {
		num = num * 10 + string[index++] - '0';
	}

	if (index == strlen(string)) {
		result = my_malloc(sizeof(IntField));
		if (result != NULL) {
			tmp_intfield = (IntField*)result;
			tmp_intfield->baseInfo.fieldType = FIELDTYPE_INTEGER;
			tmp_intfield->baseInfo.fieldSize = INT_SIZE;
			tmp_intfield->value = num;
			return GNCDB_SUCCESS;
		}
		intFieldDestroy((IntField**)&result);
		return GNCDB_MEM;
	}

	if (string[index] != '.') {
		result = my_malloc(sizeof(VarCharField));
		if (result != NULL) {
			tmp_varCharfield = (VarCharField*)result;
			tmp_varCharfield->baseInfo.fieldType = FIELDTYPE_VARCHAR;
			tmp_varCharfield->baseInfo.fieldSize = (int)strlen(string);
			tmp_varCharfield->value = (char*)my_malloc((tmp_varCharfield->baseInfo.fieldSize + 1));
			if (tmp_varCharfield->value == NULL) {
				return GNCDB_MEM;
			}
			memcpy((tmp_varCharfield->value), string, strlen(string) + 1);
			return GNCDB_SUCCESS;
		}
		varCharFieldDestroy((VarCharField**)&result);
		return GNCDB_MEM;
	}

	index++;
	while (string[index] >= '0' && string[index] <= '9' && index < (int)strlen(string)) {
		numDouble = numDouble + base * (string[index++] - '0'), base /= 10;
	}

	if (index != strlen(string)) {
		result = my_malloc(sizeof(VarCharField));
		if (result != NULL) {
			tmp_varCharfield = (VarCharField*)result;
			tmp_varCharfield->baseInfo.fieldType = FIELDTYPE_VARCHAR;
			tmp_varCharfield->baseInfo.fieldSize = (int)strlen(string);
			tmp_varCharfield->value = (char*)my_malloc((tmp_varCharfield->baseInfo.fieldSize + 1));
			if (tmp_varCharfield->value == NULL) {
				return GNCDB_MEM;
			}
			memcpy(tmp_varCharfield->value, string, strlen(string) + 1);
			return GNCDB_SUCCESS;
		}
		varCharFieldDestroy((VarCharField**)&result);
		return GNCDB_MEM;
	}
	else {
		result = my_malloc(sizeof(RealField));
		if (result != NULL) {
			tmp_realfield = (RealField*)result;
			tmp_realfield->baseInfo.fieldType = FIELDTYPE_REAL;
			tmp_realfield->baseInfo.fieldSize = DOUBLE_SIZE;
			tmp_realfield->value = numDouble;
			return GNCDB_SUCCESS;
		}
		realFieldDestroy((RealField**)&result);
		return GNCDB_MEM;
	}
}

/**
 * @brief	判断两个字段是否满足predicate运算符的关系
 * @param fieldFirst 第一个字段
 * @param fieldSecond 第二个字段
 * @param predicate 判断条件
*/
bool fieldIsMatch(void* fieldFirst, void* fieldSecond, Predicate predicate) {
	/* 1.变量声明 */
	Field* field = NULL;
    VarCharField* tmp_varcharfield1, * tmp_varcharfield2;
    char* tmp_str1, * tmp_str2;
    IntField* tmp_intfield1, * tmp_intfield2;
    RealField* tmp_realfield1, * tmp_realfield2;

	/* 2.根据predicate进行比较 */
	field = (Field*)fieldFirst;
	switch (field->fieldType) {
	case FIELDTYPE_INTEGER: {
		tmp_intfield1 = (IntField*)fieldFirst;
		tmp_intfield2 = (IntField*)fieldSecond;
		switch (predicate) {
		case EQUAL: return tmp_intfield1->value == tmp_intfield2->value;
		case NOT_EQUAL: return tmp_intfield1->value != tmp_intfield2->value;
		case GREATER_THAN: return tmp_intfield1->value > tmp_intfield2->value;
		case GREATER_THAN_OR_EQUAL: return tmp_intfield1->value >= tmp_intfield2->value;
		case LESS_THAN: return tmp_intfield1->value < tmp_intfield2->value;
		case LESS_THAN_OR_EQUAL: return tmp_intfield1->value <= tmp_intfield2->value;
		default:return false;
		}
	}
	case FIELDTYPE_REAL: {
		tmp_realfield1 = (RealField*)fieldFirst;
		tmp_realfield2 = (RealField*)fieldSecond;
		switch (predicate) {
		case EQUAL: return tmp_realfield1->value == tmp_realfield2->value;
		case NOT_EQUAL: return tmp_realfield1->value != tmp_realfield2->value;
		case GREATER_THAN: return tmp_realfield1->value > tmp_realfield2->value;
		case GREATER_THAN_OR_EQUAL: return tmp_realfield1->value >= tmp_realfield2->value;
		case LESS_THAN: return tmp_realfield1->value < tmp_realfield2->value;
		case LESS_THAN_OR_EQUAL: return tmp_realfield1->value <= tmp_realfield2->value;
		default:return false;
		}
	}
	case FIELDTYPE_VARCHAR: {
		tmp_varcharfield1 = (VarCharField*)fieldFirst;
		tmp_varcharfield2 = (VarCharField*)fieldSecond;
		tmp_str1 = tmp_varcharfield1->value;
		tmp_str2 = tmp_varcharfield2->value;
		switch (predicate) {
		case EQUAL: return strcmp(tmp_str1, tmp_str2) == 0;
		case NOT_EQUAL: return strcmp(tmp_str1, tmp_str2) != 0;
		case GREATER_THAN: return strcmp(tmp_str1, tmp_str2) > 0;
		case GREATER_THAN_OR_EQUAL: return strcmp(tmp_str1, tmp_str2) >= 0;
		case LESS_THAN: return strcmp(tmp_str1, tmp_str2) < 0;
		case LESS_THAN_OR_EQUAL: return strcmp(tmp_str1, tmp_str2) <= 0;
		default:return false;
		}
	default:
		return false;
	}
	}
}
