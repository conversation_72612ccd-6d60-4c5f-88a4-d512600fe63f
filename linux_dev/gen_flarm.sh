#!/bin/bash

if [ "$#" -lt 1 ]; then
    echo "用法: $0 <程序路径> [程序参数...]"
    exit 1
fi

# 获取程序路径和参数
PROGRAM=$1
shift
PROGRAM_ARGS="$@"

# 定义文件名
PERF_DATA="perf.data"
PERF_SCRIPT="perf_unfolded.txt"
FLAME_GRAPH="flamegraph.svg"

# 清理旧文件
rm -f $PERF_DATA $PERF_SCRIPT $FLAME_GRAPH

# 使用 perf 采样
echo "正在启动程序并采样..."
sudo perf record -e cpu-clock -F 8000 -g -- $PROGRAM $PROGRAM_ARGS

# 转换为文本文件
echo "生成perf脚本文件..."
sudo perf script > $PERF_SCRIPT

# 生成火焰图
echo "生成火焰图..."
if [ ! -d "./Flamegraph" ]; then
    echo "FlameGraph工具未找到，请先克隆仓库"
    exit 1
fi
./Flamegraph/stackcollapse-perf.pl $PERF_SCRIPT | ./Flamegraph/flamegraph.pl > $FLAME_GRAPH

echo "火焰图生成完毕: $FLAME_GRAPH"
