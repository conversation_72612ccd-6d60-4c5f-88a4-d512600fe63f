#!/bin/bash

# 注意使用前需要修改cmake文件，添加asan选项
# 设置 ASAN 环境变量
export ASAN_OPTIONS="halt_on_error=0:detect_leaks=1:malloc_context_size=15:log_path=./asan.log"
# export ASAN_OPTIONS="halt_on_error=0:detect_leaks=1:malloc_context_size=15:log_path=./asan.log:verbosity=2"

# 可选的 ASAN 选项（取消注释以启用）
# export ASAN_OPTIONS="$ASAN_OPTIONS:detect_stack_use_after_return=1" # 检测函数返回后的栈使用
# export ASAN_OPTIONS="$ASAN_OPTIONS:handle_segv=1:handle_sigill=1" # 捕获 SIGSEGV 和 SIGILL 信号
# export ASAN_OPTIONS="$ASAN_OPTIONS:quarantine_size=4194304"       # 设置堆隔离区大小
export ASAN_OPTIONS="$ASAN_OPTIONS:allocator_may_return_null=1:detect_odr_violation=1" # 检测 ODR 违规

# 运行程序（通过环境变量传递 ASAN_OPTIONS）
./bin/gncdb